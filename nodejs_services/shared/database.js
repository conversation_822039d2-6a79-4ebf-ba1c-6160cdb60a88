const { Sequelize, DataTypes } = require('sequelize');
const { google } = require('googleapis');
const _ = require('lodash');
const { read_sheet } = require('./google');

const ENV = process.env.ad_env || 'stag';
const ad_db = JSON.parse(process.env.ad_db);

const sequelize = new Sequelize(
    ENV === 'prod' ? 'ariadirect_prod' : 'ariadirect_stag',
    'aria',
    ad_db.password || 'ariadirect2020',
    {
        host: ad_db.write_host || 'localhost',
        dialect: 'postgres',
    }
);

const ETS = sequelize.define('ETS', {
    id: { type: DataTypes.INTEGER, primaryKey: true },
    name: DataTypes.STRING,
    service_type: DataTypes.STRING,
    country: DataTypes.STRING,
    airport: DataTypes.STRING,
    tasks: DataTypes.STRING,
    attributes: DataTypes.JSONB,
    currency: DataTypes.STRING,
    tag: DataTypes.STRING,
    status: DataTypes.STRING,
    working_times: DataTypes.STRING,
    schema: DataTypes.JSONB,
    order_schema: DataTypes.JSONB,
}, {
    tableName: 'ets',
    timestamps: false,
});
const ETSQueryPod = sequelize.define('ETSQueryPod', {
    // id: { type: DataTypes.INTEGER, primaryKey: true },
    pod_id: DataTypes.STRING,
    service: DataTypes.STRING,
    body: DataTypes.JSONB,
    order: DataTypes.INTEGER,
}, {
    tableName: 'ets_query_pods',
    timestamps: false,
});

const ETSPod = sequelize.define('ETSPod', {
    id: { type: DataTypes.INTEGER, primaryKey: true },
    name: DataTypes.STRING,
    category: DataTypes.STRING,
    sub_category: DataTypes.STRING,
    body: DataTypes.JSONB,
    order: DataTypes.INTEGER,
    title: DataTypes.STRING,
    status: DataTypes.STRING,
    pod_type: DataTypes.STRING,
    services: [DataTypes.STRING],
}, {
    tableName: 'ets_pods',
    timestamps: false,
});

const ETSProvider = sequelize.define('ETSProvider', {
    id: { type: DataTypes.INTEGER, primaryKey: true },
    status: DataTypes.STRING,
    name: DataTypes.STRING,
    country: DataTypes.STRING,
    timezone_name: DataTypes.STRING,
    address: DataTypes.JSONB,
    contact: DataTypes.JSONB,
    secondary_contact: DataTypes.JSONB,
    served_countries: DataTypes.ARRAY(DataTypes.STRING),
    served_area: DataTypes.STRING,
    website: DataTypes.STRING,
    created_at: DataTypes.DATE,
    updated_at: DataTypes.DATE,
    ad_contacts: DataTypes.JSONB,
    org_id: DataTypes.INTEGER,
    served_services: DataTypes.ARRAY(DataTypes.STRING)
}, {
    tableName: 'ets_provider',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
});

const Organization = sequelize.define('Organization', {
    id: { type: DataTypes.INTEGER, primaryKey: true },
    name: DataTypes.TEXT,
}, {
    tableName: 'organization',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
});

const FlightTracker = sequelize.define('FlightTracker', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true
    },
    airport: DataTypes.TEXT,
    airport_fs: DataTypes.TEXT,
    airport_name: DataTypes.TEXT,
    service_type: DataTypes.TEXT,
    service_date_time: DataTypes.DATE,
    flight_id: DataTypes.BIGINT,
    is_code_shared: DataTypes.BOOLEAN,
    code_shared_note: DataTypes.TEXT,
    tracking_url: DataTypes.TEXT,
}, {
    tableName: 'flight_trackers',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
});

const Flight = sequelize.define('Flight', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true
    },
    airline_fs: DataTypes.TEXT,
    airline_name: DataTypes.TEXT,
    flight_number: DataTypes.TEXT,
    aircraft_code: DataTypes.STRING,
    aircraft_name: DataTypes.STRING,
    working_rules: DataTypes.JSONB,
    capacity: {
        type: DataTypes.DECIMAL,
        defaultValue: 0
    }
}, {
    tableName: 'flights',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
});

const FlightAircraft = sequelize.define('FlightAircraft', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true
    },
    aircraft_code: DataTypes.TEXT,
    aircraft_name: DataTypes.TEXT,
    capacity: DataTypes.NUMBER,
}, {
    tableName: 'flight_aircrafts',
    timestamps: false,
});

const ETSPrice = sequelize.define('ETSPrice', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true
    },
    ets_id: DataTypes.NUMBER,
    price: DataTypes.NUMBER,
    currency: DataTypes.STRING,
    additional_fee: DataTypes.JSONB,
}, {
    tableName: 'ets_price',
    timestamps: false,
});

module.exports = {
    sequelize,
    ETS,
    ETSQueryPod,
    ETSPod,
    ETSProvider,
    Organization,
    FlightTracker,
    FlightAircraft,
    Flight,
    ETSPrice,
}