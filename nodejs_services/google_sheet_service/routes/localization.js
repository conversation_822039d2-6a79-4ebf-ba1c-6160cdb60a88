const { read_sheet } = require('../../shared/google');
const AWS = require('aws-sdk');
const fs = require('fs').promises;
const path = require('path');

const ENV = process.env.ENV || 'stag';
const GOOGLE_SHEET_ID = '1Z6dRDPGwFhXk8dH8cUFckigHYl1e0UbU4GON__pKi8c';
const S3_BUCKET_NAME = 'ad-app-version';

const router = {};

const uploadToS3 = async (filePath, bucketName, key) => {
    const s3 = new AWS.S3({
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    });

    const fileContent = await fs.readFile(filePath);

    const params = {
        Bucket: bucketName,
        Key: key,
        Body: fileContent,
        ACL: 'public-read',
        ContentType: 'application/json',
        ContentDisposition: 'inline'
    };

    return s3.upload(params).promise();
};

const createJsonFiles = async (localizationData, appVersionsData) => {
    // Create en.json
    const jsonDataEn = localizationData.reduce((acc, item) => {
        acc[item.key] = item.en;
        return acc;
    }, {});
    await fs.writeFile('en.json', JSON.stringify(jsonDataEn, null, 4));

    // Create vi.json
    const jsonDataVi = localizationData.reduce((acc, item) => {
        acc[item.key] = item.vi;
        return acc;
    }, {});
    await fs.writeFile('vi.json', JSON.stringify(jsonDataVi, null, 4));

    // Create app_versions.json
    await fs.writeFile('app_versions.json', JSON.stringify(appVersionsData, null, 4));
};

router.run_update_localzation = async (req, res) => {
    try {
        console.log('Starting localization update process...');

        // Read sheets
        const sheets = await read_sheet(
            GOOGLE_SHEET_ID,
            0,
            ['localization', 'app_versions']
        );

        const localizationData = sheets.localization;
        const appVersionsData = sheets.app_versions;

        // Create JSON files
        await createJsonFiles(localizationData, appVersionsData);

        // Upload to S3
        await Promise.all([
            uploadToS3(
                'en.json',
                S3_BUCKET_NAME,
                'app-languages/localization_en.json'
            ),
            uploadToS3(
                'vi.json',
                S3_BUCKET_NAME,
                'app-languages/localization_vi.json'
            ),
            uploadToS3(
                'app_versions.json',
                S3_BUCKET_NAME,
                'app-versions/app-versions.json'
            )
        ]);

        // Clean up temporary files
        await Promise.all([
            fs.unlink('en.json'),
            fs.unlink('vi.json'),
            fs.unlink('app_versions.json')
        ]);

        console.log('Localization update completed successfully');
        res.json({ success: true });
    } catch (error) {
        console.error('Error updating localization:', error);
        res.json({
            success: false,
            message: error.message
        });
    }
};

router.run_update_localzation({ query: { service: null } }, { json: console.log })

module.exports = router