const _ = require('lodash');
const { read_sheet } = require('../../shared/google');
const { ETS, ETSPod, ETSQueryPod } = require('../../shared/database');
const { POD_SHEET_ID } = require('./config');

function fill_pod_template(product_sheet_rows, master_pod_list, pod) {
    if (pod.id === 'passport_core_info_issue_date') {
        console.log(pod);
    }

    let pod_text = JSON.stringify(pod);
    const pod_regex = /"{{([a-z0-9_]+)(?:\|(\{[^}]+\}))?\}}"/g;
    const pod_matches = Array.from(pod_text.matchAll(pod_regex)) ?? [];

    if (pod_matches.length > 0) {
        for (const match of pod_matches) {
            const [full_match, id, filter_json] = match;


            let master_pod = master_pod_list.find(v => v.id === id);
            if (!master_pod) {
                throw new Error(`Master pod with id ${id} not found`);
            }

            const product_row = product_sheet_rows.find(v => v.id === master_pod.id);
            if (product_row) {
                master_pod.order = Number(product_row.order);
            }

            if (filter_json) {
                try {
                    const filter = JSON.parse(filter_json.replace(/\\/g, ''));
                    if (filter) {
                        master_pod = {
                            ...master_pod,
                            ...filter,
                        };
                    }
                } catch (e) {
                    console.error(`Failed to parse filter JSON: ${filter_json}`, e);
                }
            }

            pod_text = pod_text.replace(full_match, JSON.stringify(master_pod));
        }
    }

    pod = JSON.parse(pod_text);

    // Check if there are still matches to process
    if (pod_text.match(pod_regex)?.length > 0) {
        return fill_pod_template(product_sheet_rows, master_pod_list, pod);
    }

    return pod;
}

const router = {}
router.run_update_pod_job = async (req, res) => {
    try {
        let sheet_names = req.query.service ? [req.query.service] :
            ['passport', 'new_visa', 'new_visa_urgent', 'fastlane', 'country_tourist', 'global_entry', 'tsa_precheck', 'sentri', 'tsa_precheck', 'nexus', 'airport_entry']
        let master_sheets = await read_sheet(POD_SHEET_ID, 0, ['master_pods', 'query_pods', ...sheet_names]);
        let master_pod_rows = master_sheets.master_pods.map(v => ({
            ..._.pick(v, ['id', 'pod_type', 'category', 'sub_category', 'title', 'name']),
            ...JSON.parse(v.body),
        }));

        for (let row of master_pod_rows) {
            const pod = await ETSPod.findOne({ where: { id: row.id } });
            const services = ['passport', 'new_visa', 'new_visa_urgent', 'fastlane', 'country_tourist', 'global_entry', 'tsa_precheck', 'sentri', 'tsa_precheck', 'nexus', 'airport_entry'].filter(
                v => master_sheets[v]?.find(v => v.id === row)
            ) ?? []
            if (pod) {
                pod.services = services
                await pod.update({ ...row, body: row });
            } else {
                await ETSPod.create({ ...row, services, body: row });
            }
        }
        for (const sheet_name of sheet_names) {
            const product_sheet_rows = master_sheets[sheet_name];
            const product_name_list = Object.keys(product_sheet_rows[0]).filter(v => !['id', 'level', 'order'].includes(v));
            const db_products = await ETS.findAll({ where: { service_type: sheet_name } });
            for (const product_name of product_name_list) {
                const select_products = db_products.filter(v => v.name === product_name);
                for (const db_product of select_products) {
                    const app_schema_list = []
                    const order_schema_list = []
                    for (const row of product_sheet_rows) {
                        if (row[product_name] === 'FALSE') continue;
                        let master_pod_row = master_pod_rows.find(v => v.id === row.id)
                        if (!master_pod_row) {
                            return res.json({ success: false, message: `Master pod ${sheet_name} id: ${row.id} not found` })
                        }
                        master_pod_row.order = Number(row.order)
                        if (row[product_name] !== 'T') {
                            master_pod_row = {
                                ...master_pod_row,
                                ...JSON.parse(row[product_name]),
                                order: Number(row.order),
                                level: row.level,
                            }
                        }
                        master_pod_row = fill_pod_template(product_sheet_rows, master_pod_rows, master_pod_row)
                        if (row.level === 'both' || row.level === 'app') {
                            master_pod_row.level = 'app'
                            app_schema_list.push(master_pod_row)
                        }
                        if (row.level === 'both' || row.level === 'order') {
                            master_pod_row.level = 'order'
                            order_schema_list.push(master_pod_row)
                        }
                    }
                    console.log(product_name)
                    console.log(db_product.toJSON())
                    db_product.schema = app_schema_list
                    db_product.order_schema = order_schema_list
                    await db_product.save()
                }
            }
        }
        // Update query pods
        let query_pods = []
        for (let row of master_sheets.query_pods) {
            let master_pod_row = master_pod_rows.find(v => v.id === row.pod_id)
            if (row.body !== 'T') {
                master_pod_row = {
                    ...master_pod_row,
                    ...JSON.parse(row.body),
                }
            }
            // master_pod_row = fill_pod_template(master_pod_rows, master_pod_row)
            query_pods.push({
                pod_id: row.pod_id,
                service: row.service,
                body: master_pod_row,
                order: row.order,
            })
        }
        await ETSQueryPod.destroy({ where: {} });
        await ETSQueryPod.bulkCreate(query_pods);


    } catch (error) {
        console.log(error)
        res.json({ success: false, message: error.message })
    }

    res.json({ success: true })
}

// router.run_update_pod_job({ query: { service: 'country_tourist' } }, { json: console.log })
module.exports = router