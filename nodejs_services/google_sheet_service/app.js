const express = require('express')
require('express-group-routes');
const cors = require('cors');
const { run_update_pod_job } = require('./routes/pod');
const { run_update_product } = require('./routes/product');
const { } = require('./routes/localization');

const PORT = process.env.PORT || 3000

var app = express()
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cors())

app.group("/v1/google-sheet", (router) => {
    router.get("/status", async (req, res) => { res.json({ success: true }) })
    router.get("/pods", run_update_pod_job)
    router.get("/products", run_update_product)
});


console.log("Listen " + PORT)
app.listen(PORT)