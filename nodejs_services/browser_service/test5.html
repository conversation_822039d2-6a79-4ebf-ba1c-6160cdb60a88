
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>U.S. Passport Application Status</title><script>window['adrum-start-time'] = new Date().getTime(); var appKey='EUM-AAB-AUM';</script><script type="text/javascript" src="https://eum.state.gov/appd.21.7.0.3493.js"></script>
    <link href="https://fonts.googleapis.com/css?family=Oswald:500|Roboto+Condensed:400,700" rel="stylesheet">
    <link href="/Content/font-awesome.min.css" rel="stylesheet" />
    <link href="/Content/bootstrap.min.css" rel="stylesheet" />
    <link href="/Content/Site.css" rel="stylesheet" />
    <script src="/Scripts/modernizr-2.6.2.js"></script>



        <!-- Federated UA Script for Production -->
        <script async type="text/javascript" language="javascript" id="_fed_an_ua_tag"
                src="https://dap.digitalgov.gov/Universal-Federated-Analytics-Min.js?agency=DOS&subagency=CA"></script>

    <!-- OPSS Google Tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-TVB87S3CZT"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());
        gtag('config', 'G-TVB87S3CZT');
    </script>

</head>
<body>
    
    <!--Hamburger Menu-->
    <div class="opssHamburgerMenu">
        <nav class="navbar navbar-toggleable navbar-inverse d-sm-none" style="background-color:#06284C;">
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarTogglerDemo01" aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarTogglerDemo01">
                <ul class="navbar-nav mr-auto mt-2 mt-lg-0">
                    
                    <li class="footerTitle">Travel.State.Gov</li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel.html">Travel.State.Gov</a> </li>

                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/passports.html">U.S. Passports</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/international-travel.html">International Travel</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/us-visas.html">U.S. Visas</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/Intercountry-Adoption.html">Intercountry Adoptions</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/International-Parental-Child-Abduction.html">International Parental Child Abduction</a> </li>

                    <li class="footerTitle">Popular Links</li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel.html">Home</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/traveladvisories/traveladvisories.html">Travel Advisories</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/News/newsroom.html">Newsroom</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/about-us.html">About Us</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/contact-us.html">Contact Us</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://careers.state.gov/">Careers</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://www.usembassy.gov/">Find U.S. Embassies & Consulates</a> </li>
                    
                    <li class="footerTitle">Stay Connected</li>
                    <li class="nav-item footerLink">
                        <a class="socialMeadiaLink" href="https://blogs.state.gov/"> <i class="fa fa-star" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://www.facebook.com/travelgov/"> <i class="fa fa-facebook" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://www.flickr.com/photos/statephotos"> <i class="fa fa-flickr" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://twitter.com/TravelGov"> <i class="fa fa-twitter" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://www.youtube.com/user/statevideo"> <i class="fa fa-youtube" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://travel.state.gov/content/travel/en/rss.html"> <i class="fa fa-rss" aria-hidden="true"></i></a>
                    </li>

                    <li class="footerTitle">Legal Resources</li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/legal.html">Info for Lawyers and Judges</a>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/consularnotification.html">Info for U.S. Law Enforcement</a> </li>
                </ul>
            </div>
        </nav>
    </div><!-- /hamburger menu for portrait phones -->
    <!-- header -->
    <div class="opssHeader">
        <div class="container">
            <div class="opssHeaderBackground">
                <div class="row pl-sm-3 py-3">
                    <div class="stateLogoFrame ml-1 ml-md-2">
                        <a href="https://travel.state.gov/content/travel.html">
                            <img src="/Images/Seal_of_Department_of_state.svg" class="img-fluid" />
                        </a>
                    </div>
                    <a class="titleFrameLink" href="https://travel.state.gov/content/travel/en/passports.html">
                        <div class="titleFrame ml-2 ml-md-3 mt-2 mt-md-3 text-white">
                            <div class="h1 titleFrameLineOne">Travel.State.Gov</div>
                            <div class="titleFrameLineTwo">U.S. DEPARTMENT of STATE — BUREAU of CONSULAR AFFAIRS</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div><!-- /header -->
    
    <!-- Body -->
    <div id="theContainersContainer">
        <div class="container py-4">
            <h1>U.S. Passport Application Status</h1>
            <hr />
            


<div class="row pt-2">
    <div class="col-md-6 col-lg-8">

            <div class="card">
                <div class="card-header text-center">Your Application Status</div>
                <div class="card-block">
                    <div class="card-text">
                        <p class="app-status" style="font-size: 1.313em;text-align: center!important;"><strong>Application Status: In Process</strong></p>
    <p>The U.S. Department of State has received your application for your <span class="passport-type">passport</span> <span class="app-type">book</span> on <span class="receipt-date">05/10/2025</span>. We're now reviewing your application and supporting documents.</p>
    <p class="delivery-info">You requested routine service when you applied. Routine service can take 4-6 weeks. Our processing times begin the day we receive your application at a passport agency or center, not the day you submit it.</p>
    <p>Your application locator number is <span class="app-number">*********</span>.</p>
    <p class="contact-us">If you are traveling within two weeks and have not received your passport, please contact the National Passport Information Center at 1-877-487-2778 (or TDD/TTY 1-888-874-7793) with your application locator number.</p>
                    </div>
                </div>
            </div>
            <br />


        

            <div class="card mb-4 mb-md-0">
        <div class="card-header text-center">Subscribe to Email Updates</div>
        <div class="card-block">
            <div id="registerEmailInfo" class="card-text">
                 <p>Our records show your email address as indicated below. You have already subscribed to receive email notifications each time the status of your passport application changes.  If you want to change your email address, follow these steps:<br /><br />
&nbsp;&nbsp;1. Enter your new email address in the first box.<br />
&nbsp;&nbsp;2. Re-enter your new email address in the second box.<br />
&nbsp;&nbsp;3. Click "Submit."</p>
            </div>

            <div id="targetemailcompare">
<form action="/Email?Length=4" data-ajax="true" data-ajax-method="POST" data-ajax-mode="replace" data-ajax-update="#targetemailcompare" id="emailForm" method="post"><input name="__RequestVerificationToken" type="hidden" value="ksMdMO2vRwc2VeO0oiM9Gsg5Klg_eAUFKDOt41AO4h2JKh26jnzF3zE4LIi2tM-stTOi9_y_GTAjOZ5W802V43HIY9h0AUphY-cGQwOrAN41" />                    <div class="form-group">
                        <div class="col-sm-10 col-lg-8 mx-auto">
                            <label class="col-form-label hidden-xs-down" for="Email">Email Address</label>
                        </div>

                        <div class="col-sm-10 col-lg-8 mx-auto">
                            <input class="form-control" data-val="true" data-val-length="The Email Address must be at least 5 characters long with a maximum of 40 characters." data-val-length-max="40" data-val-length-min="5" data-val-regex="Invalid Email Address" data-val-regex-pattern="^\w+([-+._&#39;]\w+)*@\w+([-._]\w+)*\.\w+([-.]\w+)*$" data-val-required="The Email Address field is required." id="Email" name="Email" placeholder="Enter your email" type="text" value="<EMAIL>" />
                            <span class="field-validation-valid" data-valmsg-for="Email" data-valmsg-replace="true"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-10 col-lg-8 mx-auto">
                            <label class="col-form-label hidden-xs-down" for="Email2">Re-enter Email Address</label>
                        </div>
                        <div class="col-sm-10 col-lg-8 mx-auto">
                            <input class="form-control" data-val="true" data-val-length="The Re-enter Email Address must be at least 5 characters long with a maximum of 40 characters." data-val-length-max="40" data-val-length-min="5" data-val-regex="Invalid Re-enter Email Address" data-val-regex-pattern="^\w+([-+._&#39;]\w+)*@\w+([-._]\w+)*\.\w+([-.]\w+)*$" data-val-required="The Re-enter Email Address field is required." id="Email2" name="Email2" placeholder="Re-enter your email" type="text" value="" />
                            <span class="field-validation-valid" data-valmsg-for="Email2" data-valmsg-replace="true"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-10 col-lg-8 mx-auto">
                                <button type="submit" class="btn btn-block" onclick="handleButtonClick('UpdateEmail');">Submit</button>
                        </div>
                    </div>
</form>            </div>

            <div id="emailInfo" class="card-text"><p><font color=#FF0000>Use of the Internet to access your U.S. passport application information involves the electronic transmission of personal information.  By entering your email address above and clicking on the submit button below, you are consenting to the electronic transmission of your personal information.  Your consent is effective during the entire time you are communicating with the National Passport Information Center.  Passport Services can only release this information to the passport applicant or to the parent or guardian of an applicant under age 18.</font></p><br></div>
                <p class="card-text"><a href="/Unsubscribe">Unsubscribe</a> if you do not want to get more email updates.</p>
        </div>
    </div>
    <!-- Modal -->
    <div class="modal fade" id="staticBackdrop" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="staticBackdropLabel">Changing your email address</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    If you provided an email address on your passport application, we will send status updates to that email address. 
                    You can change or correct that email address now. 
                    If you select Continue, we will only send status updates to the new email address you just entered.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button id="continueButton" type="button" class="btn btn-primary">Continue</button>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        function handleButtonClick(actionName) {
            gtag('event', 'SubmitButton_Click', { 'Action': actionName });

            var isFormValid = false;
            if ($('#emailForm').valid()) {
                isFormValid = true;
            }

            if (isFormValid & !true) {
                event.preventDefault();
                // Show the popup here
                $('#staticBackdrop').modal('show');

                document.getElementById('continueButton').addEventListener('click', function () {
                    $('#staticBackdrop').modal('hide');
                    $('#emailForm').submit();
                });
            }
        }
    </script>



        <hr class="hideMedAndUp" />
    </div>

    

<div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header">Need Help?</div>
            <div class="card-block">
                <div class="card-title"></div>
                <div class="card-text">
                    If you are traveling internationally within 14 days or need a foreign visa within 28 days, go to our <a href="https://travel.state.gov/content/travel/en/contact-us/passports.html" target="_blank">Contact Us webpage</a> for information on how to call us.
                </div>
                <div class="card-title"></div>
                <div class="card-text">If you are reporting technical issues with this status tool, email <a href="mailto:<EMAIL>" target="_blank"><EMAIL>.</a></div>
                <div class="card-title"></div>
                <div class="card-text">If you applied for a special issuance passport, contact your federal travel office or check <a href="https://passportstatus.state.gov/" target="_blank">passportstatus.state.gov</a> on a U.S. government computer or device.</div>
            </div>
        </div>

</div>


</div>

        </div>
    </div>
    <!-- /Body -->
    
    <!-- footer -->
    <div class="opssFooter py-2 hidden-xs-down">
        <!-- displayed in mobile landscape and up -->
        <div class="container">
            <div class="row">
                <div class="col-sm-4">
                    <div class="d-flex flex-column">
                        <div class="footerTitle">Travel.State.Gov</div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel.html">Travel.State.Gov</a> </div>

                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/passports.html">U.S. Passports</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/international-travel.html">International Travel</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/us-visas.html">U.S. Visas</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/Intercountry-Adoption.html">Intercountry Adoptions</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/International-Parental-Child-Abduction.html">International Parental Child Abduction</a> </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="d-flex flex-column">
                        <div class="footerTitle">Popular Links</div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel.html">Home</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/traveladvisories/traveladvisories.html">Travel Advisories</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/News/newsroom.html">Newsroom</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/about-us.html">About Us</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/contact-us.html">Contact Us</a> </div>
                        <div class="footerLink"> <a href="https://careers.state.gov/">Careers</a> </div>
                        <div class="footerLink"> <a href="https://www.usembassy.gov/">Find U.S. Embassies & Consulates</a> </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="d-flex flex-column">
                        <div class="footerTitle">Stay Connected</div>
                        <div class="footerLink">
                            <a class="socialMeadiaLink" href="https://blogs.state.gov/"> <i class="fa fa-star" aria-hidden="true"></i> </a>
                            <a class="socialMeadiaLink" href="https://www.facebook.com/travelgov/"> <i class="fa fa-facebook" aria-hidden="true"></i> </a>
                            <a class="socialMeadiaLink" href="https://www.flickr.com/photos/statephotos"> <i class="fa fa-flickr" aria-hidden="true"></i> </a>
                            <a class="socialMeadiaLink" href="https://twitter.com/TravelGov"><i class="fa fa-twitter" aria-hidden="true"></i></a>
                            <a class="socialMeadiaLink" href="https://www.youtube.com/user/statevideo"> <i class="fa fa-youtube" aria-hidden="true"></i> </a>
                            <a class="socialMeadiaLink" href="https://travel.state.gov/content/travel/en/rss.html"><i class="fa fa-rss" aria-hidden="true"></i></a>
                        </div>
                        <div class="footerTitle legalResourceTitle">Legal Resources</div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/legal.html">Info for Lawyers and Judges</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/consularnotification.html">Info for U.S. Law Enforcement</a> </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!-- /footer -->
    <!-- tsgdisclaimer -->
    <div class="tsgDisclaimer text-white py-2">
        <div class="container">
            <div class="row pl-3">
                <div class="buttonFlagFrame pt-1">
                    <img src="/Images/Flag_of_the_United_States.svg" class="img-fluid" style="width:100px;" />  <!-- bug with IE 11 and svg format - need to use width 100 instead of img-fluid -->
                </div>
                <div class="tsgDisclaimerLinkFrame ml-1 ml-md-3">
                    <div>
                        <a href="https://www.state.gov/privacy-policy/">Privacy</a> | <a href="https://travel.state.gov/content/travel/en/copyright-disclaimer.html">Copyright and Disclaimer</a> 
                            | <a href="https://foia.state.gov/">FOIA</a>  |  <a href="https://www.state.gov/s/ocr/c11528.htm">No Fear Act Data</a> 
                            | <a href="https://www.stateoig.gov/">Office of the Inspector</a> | <a href="https://www.usa.gov/">USA.gov</a> 
                            | <a href="https://gobierno.usa.gov/">GobiernoUSA.gov</a>
                    </div>
                    <div>
                        This site is managed by the <a href="https://www.state.gov/">U.S. Department of State</a>.
                        External links to other Internet sites should not be construed as an endorsement of the views or privacy policies contained therein.
                    </div>
                </div>
            </div>
        </div>
    </div><!-- /tsgdisclaimer -->
    
    <script src="/Scripts/jquery-3.7.1.min.js"></script>
    <script src="/Scripts/jquery.validate.min.js"></script>
    <script src="/Scripts/jquery.validate.unobtrusive.min.js"></script>
    <script src="/Scripts/jquery-ajax-unobtrusive.js"></script>
    <script src="/Scripts/tether.min.js"></script>
    <script src="/Scripts/bootstrap.min.js"></script>

    <script language="javascript" type="text/javascript">
        $(document).ready(function () {
            try {
                $("input[type='text']").each(function () {
                    $(this).attr("autocomplete", "off");
                });
            }
            catch (e)
            { }
        });

    </script>

    <script>
        //Put our input DOM element into a jQuery Object
        var $jqDate = jQuery('input[name="DateOfBirth"]');

        //Bind keyup/keydown to the input
        $jqDate.on('keydown keyup', function (e) {

            //To accomdate for backspacing, we detect which key was pressed - if backspace, do nothing:
            if (e.which !== 8) {
                var numChars = $jqDate.val().length;
                if (numChars === 2 || numChars === 5) {
                    var thisVal = $jqDate.val();
                    thisVal += '/';
                    $jqDate.val(thisVal);
                }
            }
        });
    </script>

    
</body>
</html>
