const fs = require('fs');

const axios = require("axios");
const FormData = require('form-data');
const moment = require('moment')
const helper = require('../../shared/helpers');
const { get_image_captcha_text } = require('../../shared/captcha');
const { create_shipemnt, create_shipment_internaltional } = require('../../shared/fedex');
const PhoneNumber = require('libphonenumber-js');
const { country_iso3_to_iso2, us_state } = require('../../shared/data_mapping');

const stateNameToISO2 = stateName =>
    Object.entries(us_state).find(([code, name]) =>
        name.toLowerCase() === stateName.toLowerCase()
    )?.[0] || null;

const create_form = async (order, data) => {
    try {
        const shipment = order.shipment
        const create_shipment_req = {
            shipper_name: order.provider.name,
            shipper_phone: PhoneNumber(order.provider.contact.phone)?.nationalNumber,
            shipper_address: order.provider.address.address,
            shipper_city: order.provider.address.city,
            shipper_state: order.provider.address.state,
            shipper_postal_code: order.provider.address.zip_code,
            shipper_country: country_iso3_to_iso2[order.provider.address.country],
            recipient_name: shipment.shipping_contact.given_name + ' ' + shipment.shipping_contact.surname,
            recipient_phone: PhoneNumber(shipment.shipping_contact?.phone)?.nationalNumber,
            recipient_address: shipment.shipping_address.address,
            recipient_city: shipment.shipping_address.city,
            recipient_state: stateNameToISO2(shipment.shipping_address.state) ?? '',
            recipient_postal_code: shipment.shipping_address.zip_code,
            recipient_country: country_iso3_to_iso2[shipment.shipping_address.country],
        }

        let file_buffer = null
        if (order.provider.address.country === shipment.shipping_address.country) {
            file_buffer = await create_shipemnt(create_shipment_req)
        } else {
            file_buffer = await create_shipment_internaltional(create_shipment_req)
        }

        const result_file = 'Application_WForm_FedEx_Label_' + moment().format('YYYYMMDDHHmmss') + '.pdf';
        const buckets = JSON.parse(process.env.ad_s3);
        await helper.s3_upload_buff(buckets.ariadirect_prod_applications, `tasks/${result_file}`, file_buffer)
        const file = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/tasks/${result_file}`

        return {
            success: true,
            form_file: file,
            form_callback: {},
        }
    } catch (error) {
        console.log(error.message)
    }
    return null
}

const main = async (order_id) => {
    const { order, pod_data } = await helper.get_order_task_pod_data(order_id)
    const data = await create_form(order, pod_data)
    return data
}

// main(MOCK.order_id, MOCK.task_id)

module.exports = { main, create_form }
