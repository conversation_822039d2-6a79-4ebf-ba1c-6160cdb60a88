const fs = require('fs');
const axios = require('axios');
const moment = require('moment');
const puppeteer = require('puppeteer');
const iso = require('iso-3166-1');
const qs = require('qs');
const us = require('us');
const AWS = require('aws-sdk');
const archiver = require('archiver');
const { LANGUAGE, AIRLINE, SOCIAL_NETWORK } = require('./data_mapping')
const FormData = require('form-data');
const PhoneNumber = require('libphonenumber-js');
const { exec } = require('child_process');
const { get_order_detail, create_zip, s3_upload_buff, create_fastlane_order_from_visa_order } = require('../../shared/helpers');

let DEBUG = false
const isPROD = process.env.ad_env === 'prod'
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const create_form = async (order) => {
    try {
        if (order.tasks?.length === 0) {
            return null
        }
        if (!["Tan Son Nhat International Airport", "Noi Bai International Airport"].includes(order.tasks[0].input_pod_values.travel_enter_flight_enter_airport)) {
            return {}
        }


        const new_order = await create_fastlane_order_from_visa_order(order.id)
        const web_base = JSON.parse(process.env.ad_endpoint).web_base_url
        return {
            success: true,
            form_file: null,
            form_callback: {
                document_url: `${web_base}/dashboard/orders/detail?order_id=${new_order.id}&service=ets`,
            }
        }
    } catch (error) {
        console.log(error)
        console.log(error.message)
        return null
    }

}


const main = async (order_id) => {
    try {
        const order_resp = await get_order_detail(order_id)
        const data = await create_form(order_resp.data)
        return data
    } catch (error) {
        console.log(error)
    }
}

module.exports = { main, create_form }