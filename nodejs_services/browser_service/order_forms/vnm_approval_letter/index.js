const axios = require("axios");
const moment = require('moment-timezone')
const iso = require('iso-3166-1');
const FormData = require('form-data');
const { get_order_detail, fill_docx_form, get_country_name_v2, get_order_task_pod_data, send_zalo_message, ORDER_NOTIF_ZALO_GROUP } = require('../../shared/helpers');
const { AIRPORT } = require('./data_mapping')

const create_form = async ({ pod_data, order, task }) => {
    const entry_date = moment(pod_data.service_core_info_entry_date)
    // const exit_date = moment(pod_data.service_core_info_exit_date)
    const exit_date = moment(pod_data.service_core_info_entry_date).add(30, 'days')
    const approve_date = moment(order.created_at).tz('Asia/Ho_Chi_Minh');
    let data = {
        "day": approve_date.format("DD"),
        "month": approve_date.format("MM"),
        "month_en": approve_date.format("MMM"),
        "year": approve_date.format("YYYY"),
        "total": String(order.tasks.length),
        "entry_date": entry_date.format("DD/MM/YYYY"),
        "exit_date": exit_date.format("DD/MM/YYYY"),
        "airport": AIRPORT[pod_data.travel_enter_flight_enter_airport] ?? "",
        "people": order.tasks.map((v, i) => {
            if (['LNU', 'FNU'].includes(v.input_pod_values.passport_core_info_surname)) {
                v.input_pod_values.passport_core_info_surname = ''
            }
            if (['LNU', 'FNU'].includes(v.input_pod_values.passport_core_info_given_name)) {
                v.input_pod_values.passport_core_info_given_name = ''
            }
            return {
                index: String(i + 1),
                fullName: v.input_pod_values.passport_core_info_surname + " " + v.input_pod_values.passport_core_info_given_name,
                gender: {
                    'M': 'Male',
                    'F': 'Female'
                }[v.input_pod_values.passport_core_info_gender] ?? '',
                gender_en: {
                    'M': 'Ông (Mr)',
                    'F': 'Bà (Ms)'
                }[v.input_pod_values.passport_core_info_gender] ?? '',
                dob: moment(v.input_pod_values.passport_core_info_date_of_birth).format("DD/MM/YYYY"),
                nationality: get_country_name_v2(v.input_pod_values.passport_core_info_nationality),
                passportNo: v.input_pod_values.passport_core_info_passport_number,
            }
        })
    }
    data.filename = `Order_${order.id}_Approval_Letter_${moment().unix()}.pdf`
    const template_name = order.tasks.length > 1 ? 'Invitation_Letter_Multiple.docx' : 'Invitation_Letter_Single.docx'
    const file = await fill_docx_form(__dirname + '/' + template_name, data)

    await send_zalo_message(ORDER_NOTIF_ZALO_GROUP, [
        `Approval Letter for order: ${order_id} will send to user at: ${}`
    ].filter(v => v).join('\n')).catch(console.error);

    return {
        success: true,
        form_file: file,
        form_callback: {
            file
        }
    }
}

const main = async (order_id) => {
    try {
        const { pod_data, order, task } = await get_order_task_pod_data(order_id)
        const data = await create_form({ pod_data, order, task })
        return data
    } catch (error) {
        console.log(error)
    }
}

// main(4073)

module.exports = { main, create_form }