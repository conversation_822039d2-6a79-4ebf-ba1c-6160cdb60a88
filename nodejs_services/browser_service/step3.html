<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head id="Head">
<!--**********************************************************************************-->
<!-- DotNetNuke� - http://www.dotnetnuke.com                                          -->
<!-- Copyright (c) 2002-2007                                                          -->
<!-- by DotNetNuke Corporation   -->
<!--**********************************************************************************-->
<meta id="MetaDescription" name="DESCRIPTION" content="Visa Exemption" /><meta id="MetaKeywords" name="KEYWORDS" content="Visa Exemption, <PERSON><PERSON><PERSON><PERSON><PERSON>, mien thi thuc, mi<PERSON><PERSON>ự<PERSON>, v<PERSON><PERSON><PERSON>, VISA, B<PERSON> Ngoại G<PERSON>o, Bo ngoai giao, MOFA, Ministry of foreign affairs, Lanhsu, lãnh sự, kiều bào, keiubao, dân tộc, dantoc, thithuc, thị thực,DotNetNuke,DNN" /><meta id="MetaCopyright" name="COPYRIGHT" content="Copyright 2015 by MOFA" /><meta id="MetaGenerator" name="GENERATOR" content="DotNetNuke " /><meta id="MetaAuthor" name="AUTHOR" content="Visa Exemption" /><meta name="RESOURCE-TYPE" content="DOCUMENT" /><meta name="DISTRIBUTION" content="GLOBAL" /><meta name="ROBOTS" content="INDEX, FOLLOW" /><meta name="REVISIT-AFTER" content="1 DAYS" /><meta name="RATING" content="GENERAL" /><meta http-equiv="PAGE-ENTER" content="RevealTrans(Duration=0,Transition=1)" /><style id="StylePlaceholder" type="text/css"></style><link id="_DesktopModules_Announcements" rel="stylesheet" type="text/css" href="/DesktopModules/Announcements/module.css" /><link id="_Portals__default_" rel="stylesheet" type="text/css" href="/Portals/_default/default.css" /><link id="_Portals_0_" rel="stylesheet" type="text/css" href="/Portals/0/portal.css" /><link type="text/css" rel="stylesheet" href="/WebResource.axd?d=m7r8BTSOfEq9vef7H5jT1Zgd7JKhebjwkWmL4W_ueMl0KWNbLqqZfiWaZopVxUxtj1JWJFpvcXkwXM17pAaCcI3zkkWMWux8Lf5s8QrBCTWRthACMOT-MHrGDmhux7YoIIgavqqbPv_AoCGrVqlwAvJhrDE1&amp;amp;t=633237171960000000" /><link type="text/css" rel="stylesheet" href="/RadControls/Menu/Skins/CssGrey/styles.css" /><title>
	Khai trực tuyến
</title></head>
<body id="Body" bottommargin="0" leftmargin="0" topmargin="0" rightmargin="0" marginwidth="0" marginheight="0" background="/Portals/0/VE4N_BG_02.gif">
    <noscript></noscript>
    <form name="Form" method="post" action="/Đăngký/Khaitrựctuyến/tabid/104/VE4NCommand/registration/Default.aspx" id="Form" enctype="multipart/form-data" style="height: 100%;">
<div>
<input type="hidden" name="__EVENTTARGET" id="__EVENTTARGET" value="" />
<input type="hidden" name="__EVENTARGUMENT" id="__EVENTARGUMENT" value="" />
<input type="hidden" name="__VSTATE" id="__VSTATE" value="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" />

</div>

<script type="text/javascript">
//<![CDATA[
var theForm = document.forms['Form'];
if (!theForm) {
    theForm = document.Form;
}
function __doPostBack(eventTarget, eventArgument) {
    if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
        theForm.__EVENTTARGET.value = eventTarget;
        theForm.__EVENTARGUMENT.value = eventArgument;
        theForm.submit();
    }
}
//]]>
</script>


<script src="/WebResource.axd?d=ZoP5MEIi95FXYhasIoTJKcnORFpdQgrVO8VSEEv5ewLgIYpkqadIs4SfX97fSNYRdE1xBGkoA4NaKLej0&amp;t=636271923501517547" type="text/javascript"></script>


<script src="/js/dnncore.js" type="text/javascript"></script><script>var imgDir = '/DesktopModules/VE4N/VE4N/Styles/Default/Images/Calendar/';</script><script src='/DesktopModules/VE4N/VE4N/Scripts/PopupCalendar.js'></script><script src="/js/dnn.js"></script>
<script src="/ScriptResource.axd?d=gowRknOR5tNbKFBrp-lsdXpFWEmamRpU_aW5QnSYgA7X0J5v5IO59Vcg9eHYwmca6w12V1zva51Ooymc9ilGuRvgw2jcCT8qVWY1erkU3BfO9JPph2kc1T0dE_I1&amp;t=633202793300000000" type="text/javascript"></script>
<script src="/ScriptResource.axd?d=nMPWbkUF0na8u-GbMVlOwvmr0ey_CXcOqxGfB2rIVNmo1p-XhxKXzN52PttFUT2v4hTjVo4WvJaGi-Gcq4zGENRpX5NEq3N5ZlJwUmOg9kZfJKR_C5ZzAlaISiCowH4qgESIBg2&amp;t=633202793300000000" type="text/javascript"></script>
<script src="/ScriptResource.axd?d=FRGok6d90_3LOo7G5jOkZWCBNyhcKCzkNe6ziH7KWeTQ3J8StxmM5lqDfRCUiImE4puWmYy1J4p8D7QXU9JJcGv5s61qkQ2HK0sA4wSEmkeFztMraTY5OD8eYMxEEwLxFVhu7sW4s41sKKMV0&amp;t=633220825460000000" type="text/javascript"></script>
<script src="/ScriptResource.axd?d=53aDRgHLtqmX4iTeFi2pIzcWvt_mwGhxrwZl7D8VW0E4zdWJKPCM_tJMCaTVnwD2lDmjx-9cDgyHjNDbry3iYT4o0UmwzI4FO1sL27DOavmnSqTJUVfoqSNjZV-k3yzdvZQXwM7MBUwI9HrN0&amp;t=633220825460000000" type="text/javascript"></script>
<script src="/ScriptResource.axd?d=on_cGfl8j4HkPGpOTREmdWJKrZAPIjy27nJYx5bfgFAg3FEpzTcDUFOCTF6Kt7hybJfRCvPVTCK_89SbuH_GcTFo2sRR8JdCkTnc_aFqgkBxwI0a4VkdpXd1S7XulVAy9lMK8q_aa8h_gwAnKTau09mxjLs1&amp;t=633220825460000000" type="text/javascript"></script>
<script src="/ScriptResource.axd?d=vVSLs2D35gbABPbkqmZs0o5EJhoI6rosO0w3qlRfg3vKgz3c9phkFgI0lYum1tcRy5u9ff_XuiIqEhH3URRToPo-krd-58cy7tapQcPp3_l3No_xpwxEZMgCPB3rtOLQJgB8W6OsOdY_zPX7oXUaCJzDxTpoXDyRO7Vuwg2&amp;t=633220825460000000" type="text/javascript"></script>
<script src="/ScriptResource.axd?d=Ruv40YHN44QTGTBQLUe1ZBAd2w2wn3X8baMS1bc-UsEf_NKxHmyxdL-XCeePmcZ4tP89YvYtW8opuqerQvQM0bDezX5YHBD588avZ_JelNX2btYOYEd2bO48VKOy_LNfsPaJFizFvrOcMSsCUc9VMk55G4SV5xOYkPdlzQ2&amp;t=633220825460000000" type="text/javascript"></script>
        <script type="text/javascript">
//<![CDATA[
Sys.WebForms.PageRequestManager._initialize('ScriptManager1', document.getElementById('Form'));
Sys.WebForms.PageRequestManager.getInstance()._updateControls([], [], [], 90);
//]]>
</script>

        
        
<style>
    P, TD, Span div
    {
        font-family: tahoma, arial;
    }
    P
    {
        line-height: 20px;
    }
    .TopBanner
    {
        background-image: url(/Portals/_default/Skins/ve4n/imgs/top1.jpg);
        height: 89;
    }
    .SubBanner
    {
        background-image: url(/Portals/_default/Skins/ve4n/imgs/VE4N_08.gif);
        height: 53px;
        background-repeat: no-repeat;
        padding-left: 15px;
        padding-top: 18px;
        color: white;
        font-size: 11px;
    }
    .VE4NFooter
    {
        background-color: #3E6796;
        border-top: solid 5px #E3E1E7;
        height: 40px;
        color: white;
        font-weight: normal;
        text-align: left;
        padding-left: 10px;
    }
    .VE4NFooter a:link, a:hover, a:visited, .SkinObject
    {
        font-weight: normal;
    }
    .divMenu
    {
        float: left;
        width: 700px;
    }
    .divMenu a:link, a:hover, a:visited, link
    {
    }
    .VE4NMenu
    {
        background-image: url(/Portals/_default/Skins/ve4n/imgs/MenuBG.gif);
        background-repeat: repeat-x;
    }
    .LeftFrame
    {
        /*background-image: url(/Portals/_default/Skins/ve4n/imgs/VDot.gif);
        background-repeat: repeat-y;*/
    }
</style>

<script>
    var hidelp = false;
    function GetDateInfo() {
        var retVal = "";
        var d = new Date();
        switch (d.getDay()) {
            case 0:
                retVal = "Ch&#7911; nh&#7853;t";
                break;
            case 1:
                retVal = "Th&#7913; hai";
                break;
            case 2:
                retVal = "Th&#7913; ba";
                break;
            case 3:
                retVal = "Th&#7913; t&#432;";
                break;
            case 4:
                retVal = "Th&#7913; n&#259;m";
                break;
            case 5:
                retVal = "Th&#7913; sáu";
                break;
            case 6:
                retVal = "Th&#7913; b&#7843;y";
                break;
        }
        retVal += ", ngày " + d.getDate() + " tháng " + (d.getMonth() + 1) + " n&#259;m " + d.getFullYear();
        document.write(retVal);
    }
</script>

<div style="background-color: White">
    <div style="width:1008px; margin:0 auto;">
    <div style="padding:0 26px; background: url(/Portals/0/VE4N_BG_02.gif) repeat-y;">
        <table cellpadding="0" cellspacing="0" border="0" style="/*margin-left: 26px; */ height: 100%;
            width: 900" id="table1" bgcolor="white">
            <tr>
                <td class="Topbanner" colspan="2">
                    <a id="dnn_dnnLOGO_hypLogo" title="Visa Exemption" href="http://mienthithucvk.mofa.gov.vn/Default.aspx?base"></a>
                </td>
            </tr>
            <tr bgcolor="#3D6492">
                <td colspan="2" style='height: 23px; background-color: #3D6492;' class="VE4NMenu">
                    <div class="divMenu">
                        <div id="dnn_dnnMENU_RadMenu1" class="radmenu RadMenu_CssGrey ">
	<!-- 4.2.2 --><script type="text/javascript" src="/WebResource.axd?d=OcCgpK21VhziL1bLeTZC6T7iDms9RXn3lu1PrdAoFqLgY5OuW-inq00X4S6ENt5GNIvCxq078VJUQUlNSl7-e9Y9v_00DeQD8Zdn2XEbU1Izmu8IP6dXt7cYtoxZS_eppYj6a42z3G0bXjrKiCtnNS6hAcs1&amp;t=633237171960000000"></script><span id="dnn_dnnMENU_RadMenu1StyleSheetHolder" style="display:none;"></span><ul class="horizontal rootGroup">
		<li class="item first"><a href="http://mienthithucvk.mofa.gov.vn/Trangch%e1%bb%a7/tabid/54/Default.aspx" id="dnn_dnnMENU_RadMenu1_m0" class="link"><span class="text">Trang chủ</span></a></li><li class="item"><a href="http://mienthithucvk.mofa.gov.vn/%c4%90%c4%83ngk%c3%bd/tabid/55/Default.aspx" id="dnn_dnnMENU_RadMenu1_m1" class="link"><span class="text">Đăng ký</span></a><div class="slide">
			<ul class="vertical group level1">
				<li class="item first"><a href="http://mienthithucvk.mofa.gov.vn/%c4%90%c4%83ngk%c3%bd/Khaitr%e1%bb%b1ctuy%e1%ba%bfn/tabid/104/Default.aspx" id="dnn_dnnMENU_RadMenu1_m1_m0" class="link"><span class="text">Khai trực tuyến</span></a></li><li class="item last"><a href="http://mienthithucvk.mofa.gov.vn/%c4%90%c4%83ngk%c3%bd/Inl%e1%ba%a1it%e1%bb%9dkhai/tabid/105/Default.aspx" id="dnn_dnnMENU_RadMenu1_m1_m1" class="link"><span class="text">In lại tờ khai</span></a></li>
			</ul>
		</div></li><li class="item"><a href="http://mienthithucvk.mofa.gov.vn/C%c6%a1quan%c4%91%e1%ba%a1idi%e1%bb%87n/tabid/56/Default.aspx" id="dnn_dnnMENU_RadMenu1_m2" class="link"><span class="text">Cơ quan đại diện</span></a></li><li class="item"><a href="#" id="dnn_dnnMENU_RadMenu1_m3" class="link"><span class="text">Hướng dẫn</span></a><div class="slide">
			<ul class="vertical group level1">
				<li class="item first"><a href="http://mienthithucvk.mofa.gov.vn/H%c6%b0%e1%bb%9bngd%e1%ba%abn/C%c6%a1s%e1%bb%9fph%c3%a1pl%c3%bd/tabid/112/Default.aspx" id="dnn_dnnMENU_RadMenu1_m3_m0" class="link"><span class="text">Cơ sở pháp lý</span></a></li><li class="item last"><a href="http://mienthithucvk.mofa.gov.vn/Th%c3%b4ngtinv%e1%bb%81mi%e1%bb%85nth%e1%bb%8bth%e1%bb%b1c/tabid/142/Default.aspx" id="dnn_dnnMENU_RadMenu1_m3_m1" class="link"><span class="text">Hồ sơ thủ tục</span></a></li>
			</ul>
		</div></li><li class="item last"><a href="http://mienthithucvk.mofa.gov.vn/%c3%9dki%e1%ba%bfn%c4%91%c3%b3ngg%c3%b3p/tabid/111/Default.aspx" id="dnn_dnnMENU_RadMenu1_m4" class="link"><span class="text">Ý kiến đóng góp</span></a></li>
	</ul><input type="hidden" id="dnn_dnnMENU_RadMenu1_Hidden" name="dnn$dnnMENU$RadMenu1" /><script type="text/javascript">window["dnn_dnnMENU_RadMenu1"] = RadMenu.Create("dnn_dnnMENU_RadMenu1");window["dnn_dnnMENU_RadMenu1"].Initialize({"Skin":"CssGrey","Enabled":true},{"dnn_dnnMENU_RadMenu1_m3":{"NavigateAfterClick":false}});</script>
</div>

                    </div>
                    <div style='display:none; float: right; padding-right: 10px; padding-top: 5px;'>
                        <span style="color: white; font-size: 11px;"><b style="color: white">Tiếng Việt</b>&nbsp;/</span>&nbsp;
                        <a style=" text-decoration: none; color: white; font-size: 11px;" href="http://mienthithucvk.mofa.gov.vn/en">English</a>
                    </div>
                </td>
            </tr>
            <tr>
                <td class="SubBanner" colspan="2">

                    <script>                        GetDateInfo();</script>

                </td>
            </tr>
            <tr>
                
                <td style="width: 750px;" valign="top">
                    <div id="dnn_contentpane" style="padding:10px"> <a name="408"></a><span id="dnn_ctr408_ContentPane" align="left"><!-- Start_Module_408 --><div id="dnn_ctr408_ModuleContent">
	<link href='/DesktopModules/VE4N/VE4N/Styles/Default/Style.css' rel='stylesheet' type='text/css' />

<link href="../../Styles/Default/Style.css" rel="stylesheet" type="text/css" />
<style>
    .divGiayToKhac {
        display: none;
    }

    .divGiayToKhacOn {
        display: block;
    }

    .txtName {
        width: 200px;
        height: 25px;
        font-size: 12px;
    }

    .spDosierItem {
        width: 100%;
        padding-left: 20px;
        color: #666666;
    }

    .Regli {
        margin-top: 5px;
        list-style-type: none;
    }

    span.liNum {
        font-weight: bold;
    }

    label {
        font-family: tahoma, arial;
        font-size: 11px;
    }

    .HelpBox {
        padding: 3px 5px 3px 5px;
        font-size: 11px;
        background-color: #F8F8F8;
        display: none;
        visibility: hidden;
        line-height: 17px;
        color: #587DB3;
        width: 840px;
        border: #bcd8e6 1px solid;
        background-color: aliceblue;
        margin-top: 3px;
    }

    .style1 {
        font-weight: bold;
    }
</style>

<script type="text/javascript">
    function CheckGiayToKhac(checkboxlist, divName)
    {
        if(checkboxlist.checked)
        {
            var divGiayToKhac = document.getElementsByName(divName)[0];
            divGiayToKhac.className=divGiayToKhac.className.replace(' divGiayToKhacOn',""); // first remove the class name if that already exists
            divGiayToKhac.className = divGiayToKhac.className + ' divGiayToKhacOn'; // adding new class name
            
            var txt = divGiayToKhac.getElementsByTagName('input')[0];
            txt.value = '';
            txt.focus();
            
            alert(txtName + '|' + checkboxlist);
        }
        else
        {
            var divGiayToKhac = document.getElementsByName(divName)[0];
            divGiayToKhac.className=divGiayToKhac.className.replace(' divGiayToKhacOn',"");
            
            var txt = divGiayToKhac.getElementsByTagName('input')[0];
            txt.value = '';
            txt.focus();
        }
    }
    

    function DoChangeNationalityAtPresent(idx)
    {
        var dPassportIC = document.all.dnn_ctr408_Desktop_subctr_ddlPassportIssuingCountry;    
        dPassportIC.selectedIndex = idx;
    }
    
    function DoChangeFullName()
    {        
        var obj = document.all.spPrintedName;        
        var tFirstName = document.all.dnn_ctr408_Desktop_subctr_txtFirstName;
        var tMidleName = document.all.dnn_ctr408_Desktop_subctr_txtMiddleName;
        var tLastName = document.all.dnn_ctr408_Desktop_subctr_txtLastName;                        
        
        if(tMidleName.value.trim() != '')    
        {    
            obj.innerText = tFirstName.value + ' ' + tMidleName.value + ' ' + tLastName.value;                        
        }
        else
        {
            obj.innerText = tFirstName.value + ' ' + tLastName.value;            
        }
        obj.innerText = obj.innerText.toUpperCase();   
        if(obj.textContent)
        {
            obj.textContent = obj.innerText;
        }                                
    }
    
    function DoSelectAddressCountry(v)
    {
        var dCountry = document.all.dnn_ctr408_Desktop_subctr_ddlCountry;                    
        var found = false;
        i = 0;
        while((i < dCountry.options.length) && (!found))
        {
            if(dCountry.options[i].value == v)
            {
                dCountry.selectedIndex = i;
                found = true;
            }
            else
                i++;                
        }        
    }

    function CheckPhoneNumber(evt)
    {           
        //        var e = evt ? evt : window.event;
        //        var temp = (e.keyCode ? e.keyCode : e.charCode);                
        //        var str = ",8,9,35,40,41,45,46,48,49,50,51,52,53,54,55,56,57,91,93,";                        
        //        if(str.indexOf(',' + temp + ',') < 0)
        //        {                                  
        //            if(e.keyCode == 0)
        //            {
        //                e.preventDefault();      
        //            }
        //            
        //            e.returnValue = false;   
        //            e.cancel = true;
        //            return false;
        //        }
    }
    
    function CheckPhoneNumberOrEmail(evt)
    {           
        //        var retVal = CheckPhoneNumber(evt);
        //        if(!retVal)
        //        return CheckEmail(evet);
    }
    
    function CheckEmail(evt)
    {
        var email = document.getElementById('txtEmail');
        var filter = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
        return filter.test(email.value);
    }
    
    function CheckInputChar(evt, obj, allowNumeric, allowAlphabet, useUpcase)
    {        
        var e = evt ? evt : window.event;
        var temp = (e.keyCode ? e.keyCode : e.charCode);
        var valid = false;
        if(allowNumeric)
        {
            if(temp >= 48 && temp <= 57)            
                valid = true;            
        }
        if(!valid && allowAlphabet)
        {
            if((temp >= 97 && temp <= 122) || (temp >= 65 && temp <= 90))
                valid = true;
        } 
        if(temp == 8 || temp == 9) valid = true;
        if(!valid)
        {
            if(evt)
                e.preventDefault(); 
            e.cancel = false;
            e.returnValue = false;            
            return false;
        }
        else if(useUpcase)
        {
            obj.value = obj.value.toUpperCase();
        }
    }
    
    function DoUpcase(obj, firstLetterOnly)
    {        
        var str = obj.value.trim();
        while(str.indexOf('  ') >= 0)
        {
            str = str.replace('  ', '');
        }
        if(str == '') return false;
        str = str.substring(0, 1).toUpperCase() + str.substring(1);            
        if(!firstLetterOnly)
        {        
            pos = 0;                 
            while(pos < str.length && pos >= 0)
            {                                
                pos = str.indexOf(' ', pos + 1);                                                
                if((pos < str.length - 1) && (pos >= 0))
                {                       
                    if(pos < str.length - 2)                                        
                        str = str.substring(0, pos + 1) + str.substring(pos + 1, pos + 2).toUpperCase() + str.substring(pos + 2);                                                    
                    else
                        str = str.substring(0, pos + 1) + str.substring(pos + 1, pos + 2).toUpperCase();                                                    
                }
                else
                {
                    pos = -1;                    
                }                
            }            
        }
        obj.value = str;
    }
    
    function DoSelectGender(sender, sex)
    {
        var id = '';
        if(sex == 'Male')        
            id = sender.id.replace('Male', 'Female');        
        else
            id = sender.id.replace('Female', 'Male');            
        var obj = document.getElementById(id);
        obj.checked = !sender.checked;
    }
    
    function DoSelectPrintType(sender, type)
    {
        var id = '';
        if(type == '1')        
            id = sender.id.replace('1', '2');        
        else
            id = sender.id.replace('2', '1');            
        var obj = document.getElementById(id);
        obj.checked = !sender.checked;
    }
    
    function ValidYearOfBirth(dateStr, minage, maxage)
    {
        var d = new Date();
        var arr = dateStr.split('/');
        var age = d.getFullYear() - parseInt(arr[2]);
        return (age >= minage && age <= maxage);        
    }
   
    function ValidExpireValue(dateStr, bMonth)
    {        
        var d = new Date();
        var arr = dateStr.split('/');
        var curDay = d.getDate();
        var curMonth = d.getMonth() + 1;
        var curYear = d.getFullYear();
        if(bMonth < 0) bMonth = 0;                                
        curYear = curYear + parseInt(bMonth / 12);
        bMonth = bMonth % 12;
        curMonth = curMonth + bMonth; 
         
        if(arr[1].indexOf('0') == 0)
        {
            arr[1] = arr[1].substring(1);
        }
        if(arr[0].indexOf('0') == 0)
        {
            arr[0] = arr[0].substring(1);
        }
        if(curMonth > 12)
        {
            curYear = curYear + 1;
            curMonth = curMonth % 12;
        }        
        if(curYear > parseInt(arr[2]))
        {            
            return false;
        }        
        else if(curYear == parseInt(arr[2]))
        {
            if(curMonth < parseInt(arr[1]))
            {
                return true;
            }
            else if(curMonth == parseInt(arr[1]))
            {
                if(curDay < parseInt(arr[0]))
                {
                    return true;
                }
                else
                {         
                    return false;
                }
            }
            else
                return false;
        }
        else 
            return true;
        return false;
    }
    
    function CheckBeforeSaving()
    {        
        var tFirstName = document.all.dnn_ctr408_Desktop_subctr_txtFirstName;
        var tLastName = document.all.dnn_ctr408_Desktop_subctr_txtLastName;
        var tDayOfBirth = document.all.dnn_ctr408_Desktop_subctr_txtDayOfBirth;
        var tMonthOfBirth = document.all.dnn_ctr408_Desktop_subctr_txtMonthOfBirth;
        var tYearOfBirth = document.all.dnn_ctr408_Desktop_subctr_txtYearOfBirth;
        var tPlaceOfBirth = document.all.dnn_ctr408_Desktop_subctr_txtPlaceOfBirth;
        var tPassportNo = document.all.dnn_ctr408_Desktop_subctr_txtPassportNo;
        var tPassportIssuingAuthority =  document.all.dnn_ctr408_Desktop_subctr_txtPassportIssuingAuthority;
        var tPRDNo =  document.all.dnn_ctr408_Desktop_subctr_txtPRDNo;
        var tPRDIssuingAuthority =  document.all.dnn_ctr408_Desktop_subctr_txtPRDIssuingAuthority;        
        var tPassportExpireDate = document.all.dnn_ctr408_Desktop_subctr_diPassportExpireDate_txtDateInput;
        var tPRDExpireDate = document.all.dnn_ctr408_Desktop_subctr_diPRDExpireDate_txtDateInput;
        var tChild1Fullname = document.all.dnn_ctr408_Desktop_subctr_txtChild1Fullname;
        var tChild2Fullname = document.all.dnn_ctr408_Desktop_subctr_txtChild2Fullname;
        var tChild3Fullname = document.all.dnn_ctr408_Desktop_subctr_txtChild3Fullname;
        var tChild1DateOfBirth = document.all.dnn_ctr408_Desktop_subctr_diChild1DateOfBirth_txtDateInput;
        var tChild2DateOfBirth = document.all.dnn_ctr408_Desktop_subctr_diChild2DateOfBirth_txtDateInput;
        var tChild3DateOfBirth = document.all.dnn_ctr408_Desktop_subctr_diChild3DateOfBirth_txtDateInput;
        var tAddressAtPresent = document.all.dnn_ctr408_Desktop_subctr_txtAddressAtPresent;
        var tOccupation = document.all.dnn_ctr408_Desktop_subctr_txtOccupation;
        var tPhoneNumber = document.all.dnn_ctr408_Desktop_subctr_txtPhoneNumber;
        var tContactAddressInVN = document.all.dnn_ctr408_Desktop_subctr_txtContactAddressInVN;
        var tContactPhoneNumber = document.all.dnn_ctr408_Desktop_subctr_txtContactPhoneNumber;
        var dNationalityAtPresent = document.all.dnn_ctr408_Desktop_subctr_ddlNationalityAtPresent;
        var dNationalityAtBirth = document.all.dnn_ctr408_Desktop_subctr_ddlNationalityAtBirth;
        var dPassportIssuingCountry = document.all.dnn_ctr408_Desktop_subctr_ddlPassportIssuingCountry;                            
        var dPRDIssuingCountry = document.all.dnn_ctr408_Desktop_subctr_ddlPRDIssuingCountry;                            
        var dCountry = document.all.dnn_ctr408_Desktop_subctr_ddlCountry;                    
        var dAddressCountry = document.all.dnn_ctr408_Desktop_subctr_ddlAddressCountry;                                    
        var cPrintType = document.all.dnn_ctr408_Desktop_subctr_cblDocuments1;
        var cDocuments = document.all.dnn_ctr408_Desktop_subctr_cblDocuments1;                                       
        var cDocuments2 = document.all.dnn_ctr408_Desktop_subctr_cblDocuments2;                                       
                
        tFirstName.style.backgroundColor = "white";
        tLastName.style.backgroundColor = "white";   
        tDayOfBirth.style.backgroundColor = "white";
        tMonthOfBirth.style.backgroundColor = "white";    
        tYearOfBirth.style.backgroundColor = "white";
        tPlaceOfBirth.style.backgroundColor = "white";
        dNationalityAtPresent.style.backgroundColor = "white";
        dNationalityAtBirth.style.backgroundColor = "white";
        tPassportNo.style.backgroundColor = "white";
        tPassportIssuingAuthority.style.backgroundColor = "white";
        dPassportIssuingCountry.style.backgroundColor = "white";        
        tPRDNo.style.backgroundColor = "white";
        tPRDIssuingAuthority.style.backgroundColor = "white";
        dPRDIssuingCountry.style.backgroundColor = "white";        
        tPassportExpireDate.style.backgroundColor = "white";
        tPRDExpireDate.style.backgroundColor = "white";
        tChild1Fullname.style.backgroundColor = "white";
        tChild2Fullname.style.backgroundColor = "white";
        tChild3Fullname.style.backgroundColor = "white";
        tChild1DateOfBirth.style.backgroundColor = "white";
        tChild2DateOfBirth.style.backgroundColor = "white";
        tChild3DateOfBirth.style.backgroundColor = "white";
        tAddressAtPresent.style.backgroundColor = "white";
        dAddressCountry.style.backgroundColor = "white";
        tOccupation.style.backgroundColor = "white";
        tPhoneNumber.style.backgroundColor = "white";
        tContactAddressInVN.style.backgroundColor = "white";
        tContactPhoneNumber.style.backgroundColor = "white";
        dNationalityAtPresent.style.backgroundColor = "white";
        dCountry.style.backgroundColor = "white";
        
        
        if((tFirstName.value.trim() == ''))
        {
            alert('Hãy nhập tên của bạn!\nInput your first name!');
            tFirstName.focus();
            tFirstName.style.backgroundColor = 'Yellow';
            return false;
        }
        
        if(tLastName.value.trim() == '')
        {
            alert('Hãy nhập họ của bạn!\nInput your last name!');
            tLastName.focus();
            tLastName.style.backgroundColor = 'Yellow';
            return false;
        }
        
        if(tYearOfBirth.value.trim() == '')
        {
            alert('Hãy nhập ngày sinh đầy đủ hoặc năm sinh của bạn!\nPlease fill in your date of birth or just year of birth (in case you forget your date of birth)!');
            tYearOfBirth.focus(); 
            tYearOfBirth.style.backgroundColor = 'Yellow';           
            return false;
        }
        else
        {
            var strDateOfBirth = "01/01/" + tYearOfBirth.value.trim();
            var d = new Date();
            var ngay = -1;
            var thang = -1;
            var nam = parseInt(tYearOfBirth.value);
            if(isNaN(nam))
            {
                alert("Năm sinh yêu cầu nhập số!\nYear of birth must be a number");
                tYearOfBirth.focus();
                tYearOfBirth.style.backgroundColor = 'Yellow';
                return false;
            }
            else if(!ValidYearOfBirth(strDateOfBirth, 0, 150))
            {
                alert('Độ tuổi của bạn không hợp lệ!\nPlease input a valid age!');
                tYearOfBirth.focus();
                tYearOfBirth.style.backgroundColor = 'Yellow';           
                return false;
            }
            else
            {
                if(tDayOfBirth.value != "")
                {
                    ngay = parseInt(tDayOfBirth.value, 10);
                    if(isNaN(ngay))
                    {
                        alert("Ngày sinh yêu cầu nhập số!\nDay of birth must be a number");
                        tDayOfBirth.focus();
                        tDayOfBirth.style.backgroundColor = 'Yellow';           
                        return false;
                    }                    
                }
                else
                {
                    ngay = 1;
                }
                
                if(tMonthOfBirth.value != "")
                {
                    thang = parseInt(tMonthOfBirth.value, 10);
                    if(isNaN(thang))
                    {
                        alert("Tháng sinh yêu cầu nhập số!\nMonth of birth must be a number");
                        tMonthOfBirth.focus();
                        tMonthOfBirth.style.backgroundColor = 'Yellow';           
                        return false;
                    }                                        
                    else
                    {
                        ngay = 1;
                    }
                }
                else
                {
                    thang = 1;
                    ngay = 1;
                    tDayOfBirth.value = "";                            
                }                
                d.setFullYear(nam, thang - 1, ngay);
               
                if((d.getDate() != ngay) || (d.getMonth() + 1 != thang))
                {
                    tDayOfBirth.focus();
                    tDayOfBirth.style.backgroundColor = 'Yellow';
                    tMonthOfBirth.style.backgroundColor = 'Yellow';
                    tYearOfBirth.style.backgroundColor = 'Yellow';
                    return false;
                }             
            }
        }
        if(tPlaceOfBirth .value.trim() == '')
        {
            alert('Hãy nhập nơi sinh!\nPlease input birth place!');
            tPlaceOfBirth .focus();
            tPlaceOfBirth.style.backgroundColor = 'Yellow';           
            return false;
        }
        
        if(dNationalityAtPresent.selectedIndex == 0)
        {
            alert('Hãy nhập quốc tịch hiện tại!\nPlease input nationality at present!');
            dNationalityAtPresent .focus();
            dNationalityAtPresent.style.backgroundColor = 'Yellow';           
            return false;
        }
        
        if(dNationalityAtBirth.selectedIndex == 0)
        {
            alert('Hãy nhập quốc tịch gốc!\nPlease input nationality at birth!');
            dNationalityAtBirth .focus();
            dNationalityAtBirth.style.backgroundColor = 'Yellow';           
            return false;
        }
        
        if(tPassportNo.value.trim() != '')        
        {            
            if(tPassportExpireDate.value.trim() == '')
            {
                alert('Hãy nhập ngày hết hạn hộ chiếu!\nPlease input your passport’s date of expiry!');
                tPassportExpireDate.focus();
                tPassportExpireDate.style.backgroundColor = 'Yellow';           
                return false;
            }
            else
            {                                
                if(!ValidExpireValue(tPassportExpireDate.value.trim(), 12))
                {
                    alert('Hộ chiếu không hợp lệ. Thời hạn hộ chiếu phải có giá trị trong vòng 1 năm tiếp theo.\nYour Passport should be valid for the next 1 year!');
                    tPassportExpireDate.focus();
                    tPassportExpireDate.style.backgroundColor = 'Yellow';           
                    return false;
                }
            }
            if(tPassportIssuingAuthority.value.trim() == '')
            {
                alert('Hãy nhập thông tin cơ quan cấp hộ chiếu!\nPlease input Issuing Authority!');
                tPassportIssuingAuthority.focus();
                tPassportIssuingAuthority.style.backgroundColor = 'Yellow';           
                return false;
            }
            if(dPassportIssuingCountry.selectedIndex == 0)
            {
                alert('Hãy nhập thông tin nước cấp hộ chiếu!\nPlease input Issuing Country!');
                dPassportIssuingCountry.focus();
                dPassportIssuingCountry.style.backgroundColor = 'Yellow';           
                return false;
            }
        }
        else
        {
            if(tPRDNo.value.trim() == '')        
            {
                alert('Hãy nhập thông tin về hộ chiếu hoặc Giấy thường trú do nước ngoài cấp!\nPlease input your foreign Passport/Permanent Residence Document information!');
                tPassportNo.focus();
                tPassportNo.style.backgroundColor = 'Yellow';           
                return false;
            }               
        }
        
        if(tPRDNo.value.trim() != '')        
        {            
            if(tPRDExpireDate.value.trim() == '')
            {
                alert('Hãy nhập thời hạn giấy cư trú.\nPlease input your Permanent Residence Document’s date of expiry!');
                tPRDExpireDate.focus();
                tPRDExpireDate.style.backgroundColor = 'Yellow';           
                return false;
            }
            else
            {
                if(!ValidExpireValue(tPRDExpireDate.value.trim(), 0))
                {
                    alert('Giấy cư trú không hợp lệ.\nYour Permenant Residence Document is invalid.');
                    tPRDExpireDate.focus();
                    tPRDExpireDate.style.backgroundColor = 'Yellow';           
                    return false;
                }
            }
            if(tPRDIssuingAuthority.value.trim() == '')
            {
                alert('Hãy nhập thông tin cơ quan cấp giấy cư trú!\nPlease input Issuing Authority!');
                tPRDIssuingAuthority.focus();
                tPRDIssuingAuthority.style.backgroundColor = 'Yellow';           
                return false;
            }
            if(dPRDIssuingCountry.selectedIndex == 0)
            {
                alert('Hãy nhập thông tin nước cấp Giấy cư trú!\nPlease input Issuing Country!');
                dPRDIssuingCountry.focus();
                dPRDIssuingCountry.style.backgroundColor = 'Yellow';           
                return false;
            }
        }
        else
        {
            if(tPassportNo.value.trim() == '')        
            {
                alert('Hãy nhập thông tin về hộ chiếu hoặc Giấy thường trú do nước ngoài cấp!\nPlease input your foreign Passport/Permanent Residence Document information!');
                tPassportNo.focus();
                tPassportNo.style.backgroundColor = 'Yellow';           
                return false;
            }
        }                        
                            
        if(dNationalityAtPresent.options[dNationalityAtPresent.selectedIndex].value == "VNM")
        {
            if(tPRDNo.value.trim() == '')  
            {
                alert('Yêu cầu nhập thông tin Giấy cư trú do nước ngoài cấp.\nPlease input your Permanent Residence Document information!');
                dNationalityAtPresent.style.backgroundColor = 'Yellow';           
                return false;
            }
        }                
        
        if(tOccupation.value.trim() == '')
        {
            alert('Hãy nhập thông tin nghề nghiệp của bạn!\nPlease input your Occupation!');
            tOccupation.focus();
            tOccupation.style.backgroundColor = 'Yellow';           
            return false;
        }
        
        if(tAddressAtPresent.value.trim() == '')
        {
            alert('Hãy nhập địa chỉ nơi ở hiện tại của bạn!\nPlease input your Address at Present!');
            tAddressAtPresent.focus();
            tAddressAtPresent.style.backgroundColor = 'Yellow';           
            return false;
        }      
        /*
        if(dAddressCountry.selectedIndex == 0)
        {
            alert('Hãy nhập nước cư trú hiện tại!\nPlease input living country at present!');
            dAddressCountry.focus();
            dAddressCountry.style.backgroundColor = 'Yellow';           
            return false;
        }                
        
        if(dAddressCountry.selectedIndex == 0)
        {
            alert('Hãy chọn nước cư trú!\nPlease select your Permanent Residence Country!');
            dAddressCountry.focus();
            dAddressCountry.style.backgroundColor = 'Yellow';           
            return false;
        }
        */
        
        if(tPhoneNumber.value.trim() == '')
        {
            alert('Hãy nhập số điện thoại của bạn!\nPlease input your Telephone Number!');
            tPhoneNumber.focus();
            tPhoneNumber.style.backgroundColor = 'Yellow';           
            return false;
        }
        
        if(tContactAddressInVN.value.trim() == '')
        {
            alert('Hãy nhập địa chỉ liên hệ tại Việt Nam!\nPlease input your Contact Address in Vietnam!');
            tContactAddressInVN.focus();
            tContactAddressInVN.style.backgroundColor = 'Yellow';           
            return false;
        }
        
        if(tContactPhoneNumber.value.trim() == '')
        {
            alert('Hãy nhập số điện thoại liên hệ tại Việt Nam!\nPlease input your Contact Telephone Number in Vietnam!');
            tContactPhoneNumber.focus();
            tContactPhoneNumber.style.backgroundColor = 'Yellow';           
            return false;
        }                               
                                
        if(tChild1Fullname.value == '')
        {
            if(tChild2Fullname.value != '')
            {
                tChild1Fullname.value = tChild2Fullname.value;
                tChild1DateOfBirth.value = tChild2DateOfBirth.value;
                tChild2DateOfBirth.value = '';
                tChild2Fullname.value = '';
            }
            else if(tChild3Fullname.value != '')
            {
                tChild1Fullname.value = tChild3Fullname.value;
                tChild1DateOfBirth.value = tChild3DateOfBirth.value;
                tChild3DateOfBirth.value = '';
                tChild3Fullname.value = '';
            }
        }     
        
        if(tChild2Fullname.value == '')
        {
            if(tChild3Fullname.value != '')
            {
                tChild2Fullname.value = tChild3Fullname.value;
                tChild2DateOfBirth.value = tChild3DateOfBirth.value;
                tChild3DateOfBirth.value = '';
                tChild3Fullname.value = '';
            }
        }                                 
                                
        if(tChild1Fullname.value.trim() != '')
        {            
            if(tChild1DateOfBirth.value.trim() == '')
            {
                alert('Hãy nhập ngày sinh của trẻ em thứ nhất đi cùng!\nPlease input the Date of Birth of your first accompanying child!');
                tChild1DateOfBirth.focus();
                tChild1DateOfBirth.style.backgroundColor = 'Yellow';           
                return false;
            }
            else
            {
                if(!ValidYearOfBirth(tChild1DateOfBirth.value.trim(), 0, 13))
                {
                    alert('Trẻ em đi cùng phải dưới 14 tuổi!\nAccompanying children must be under 14!');
                    tChild1DateOfBirth.focus();
                    tChild1DateOfBirth.style.backgroundColor = 'Yellow';           
                    return false;
                }
            }
        }   
        
        if(tChild2Fullname.value.trim() != '')
        {             
            if(tChild2DateOfBirth.value.trim() == '')
            {
                alert('Hãy nhập ngày sinh của trẻ em thứ hai đi cùng!\nPlease input the Date of Birth of your second accompanying child!');
                tChild2DateOfBirth.focus();
                tChild2DateOfBirth.style.backgroundColor = 'Yellow';           
                return false;
            }
            else
            {
                if(!ValidYearOfBirth(tChild2DateOfBirth.value.trim(), 0, 13))
                {
                    alert('Trẻ em đi cùng phải dưới 14 tuổi!\nAccompanying children must be under 14!');
                    tChild2DateOfBirth.focus();
                    tChild2DateOfBirth.style.backgroundColor = 'Yellow';           
                    return false;
                }
            }
        }   
        
        if(tChild3Fullname.value.trim() != '')
        {             
            if(tChild3DateOfBirth.value.trim() == '')
            {
                alert('Hãy nhập ngày sinh của trẻ em thứ ba đi cùng!\nPlease input the Date of Birth of your third accompanying child!');
                tChild3DateOfBirth.focus();
                tChild3DateOfBirth.style.backgroundColor = 'Yellow';           
                return false;
            }
            else
            {
                if(!ValidYearOfBirth(tChild3DateOfBirth.value.trim(), 0, 13))
                {
                    alert('Trẻ em đi cùng phải dưới 14 tuổi!\nAccompanying children must be under 14!');
                    tChild3DateOfBirth.focus();
                    tChild3DateOfBirth.style.backgroundColor = 'Yellow';           
                    return false;
                }
            }
        }   

        if((tPassportNo.value.trim() == "") && (tChild1Fullname.value.trim() != ""))
        {
            alert('Bạn chỉ có thể đăng ký kèm trẻ em khi có hộ chiếu!\nYou should have a valid passport if you want to register for accompanying children!');
            tChild3DateOfBirth.style.backgroundColor = 'Yellow'; 
            return false;
        }  
        var subId;
        var subCtr;
        var checkedDocs = false;
        i = 0;
        while(i < 15 && (!checkedDocs))
        {
            subId = cDocuments.id + '_' + i;
            subCtr = document.all.item(subId);            
            if(subCtr.checked)
            {
                checkedDocs = true;
            }
            i++;
        }   
        i = 0;
        while(i < 5 && (!checkedDocs))
        {
            subId = cDocuments2.id + '_' + i;
            subCtr = document.all.item(subId);            
            if(subCtr.checked)
            {
                checkedDocs = true;
            }
            i++;
        }        
        if(!checkedDocs)
        {
            alert('Bạn phải cung cấp giấy tờ chứng minh đủ điều kiện xin cấp Giấy miễn thị thực!\nPlease select Documents providing that the applicant meets requirements for Certificate of Visa Exemption!');
            return false;
        }
                
        if(dCountry.selectedIndex == 0)
        {
            alert('Hãy chọn nước đặt cơ quan đại diện để xin cấp giấy miễn thị thực!\nPlease select the Country where you want to apply for Certificate of Visa Exemption!');
            dCountry.focus();
            dCountry.style.backgroundColor = 'Yellow';           
            return false;            
        }
        /*
        else if(dCountry.options[dCountry.selectedIndex].value != dAddressCountry.options[dAddressCountry.selectedIndex].value)
        {
            if(!confirm('Nơi bạn đăng ký xin cấp Giấy miễn thị thực khác nước nơi bạn đang thường trú? Bạn có chắc chắn không?\nThe Country where you want to apply for Certificate of Visa Exemption is different from your Permanent Residence Country. Are you sure?'))
            { 
                dCountry.focus();               
                dCountry.style.backgroundColor = 'Yellow';           
                return false;
            }
        }
        */
        if(!document.all.chkConfirm.checked)
        {
            alert("Hãy xác nhận thông tin trước khi gửi đăng ký!\nPlease confirm that the information you have provided is correct before completing your e-Registration!");
            return false;
        }        
        return true;
    }
    
    function ChangeInfoImage(obj)
    {
        var imgSource;
        if(obj.src.indexOf("Gray") > 0)
        {
            imgSource = document.all.dnn_ctr408_Desktop_subctr_imgS;
        }
        else
        {
            imgSource = document.all.dnn_ctr408_Desktop_subctr_imgSGray;
        }
        obj.src = imgSource.src;
    }
    
    function ChangeHelpBoxDisplay(obj)
    {        
        if(obj.style.display == "block")
        {
            obj.style.display = "none";
            obj.style.visibility = "hidden";   
        }
        else
        {            
            obj.style.display = "block";
            obj.style.visibility = "visible";
        }
    }
    window.onload=function(){
        var vText="-- Quốc gia/Vùng lãnh thổ --";
        document.getElementById("dnn_ctr408_Desktop_subctr_ddlNationalityAtPresent").options[0].text = vText;
        document.getElementById("dnn_ctr408_Desktop_subctr_ddlNationalityAtBirth").options[0].text = vText;
	    document.getElementById("dnn_ctr408_Desktop_subctr_ddlCountry").options[0].text = vText;
	}
</script>

<div style="padding-bottom: 10px; width: 900px; padding-top: 10px; background-color: white;">
    <div style="width: 900px">
        <p align="center">
            &nbsp;
        </p>
        <p align="center">
            <b><font size="4">
            
            TỜ KHAI <br />ĐỀ NGHỊ CẤP/CẤP LẠI GIẤY MIỄN THỊ THỰC (1)
            <br />
            </font></b>APPLICATION FORM FOR ISSUANCE/REISSUANCE OF A VISA EXEMPTION CERTIFICATE 
        </p>
        <br />
        <ol>
            <li class="RegLI"><b>
                <div style="float: right; width: 30px; text-align: right">
                    <img id="dnn_ctr408_Desktop_subctr_imgInfo1" title="Trợ giúp / Help" onclick="ChangeHelpBoxDisplay(document.all.divInfo1);" onmouseover="ChangeInfoImage(this)" onmouseout="ChangeInfoImage(this)" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;cursor: hand" />
                </div>
                <span class='liNum'>1. </span>Họ và tên trong hộ chiếu/giấy tờ thường trú do nước
                ngoài cấp<br />
            </b>Full name, as it appears in your Passport/Permanent Residence Document issued by
                foreign country
                <div id="divInfo1" class="HelpBox">
                    Đề nghị bạn nhập đầy đủ và chính xác họ và tên trong hộ chiếu/Giấy tờ thường trú
                    do nước ngoài cấp vào các ô Tên, Họ đệm, Họ.
                    <br />
                    <br />
                    Sau khi đã điền đầy đủ, đề nghị bạn kiểm tra “Họ và tên sẽ được hiển thị trong Giấy
                    miễn thị thực” trong ô màu vàng phía dưới. Nếu không trùng khớp với họ và tên trong
                    Hộ chiếu/Giấy tờ thường trú, đề nghị bạn nhập lại. Hồ sơ của bạn sẽ bị loại nếu
                    Họ và tên ghi trong tờ khai không khớp với họ và tên ghi trong hộ chiếu/Giấy tờ
                    thường trú do nước ngoài cấp.
                </div>
                <br />
                <table width="100%">
                    <tr>
                        <td style="padding-left: 30px" id="diDateOfBirth">
                            <b>Tên</b><span style="color: #cc3300" class="style1">*</span><b>(First Name) </b>
                        </td>
                        <td style="padding-left: 30px" id="diDateOfBirth">
                            <b>Tên đệm (Middle Name) </b>
                        </td>
                        <td style="padding-left: 30px" id="diDateOfBirth">
                            <b>Họ</b><span style="color: #cc0000" class="style1">*</span><b>(LastName) </b>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input name="dnn$ctr408$Desktop$subctr$txtFirstName" type="text" maxlength="20" id="dnn_ctr408_Desktop_subctr_txtFirstName" class="txtName" onkeyup="DoChangeFullName()" onblur="DoUpcase(this, true);" style="text-transform: uppercase; text-align: center" />
                        </td>
                        <td>
                            <input name="dnn$ctr408$Desktop$subctr$txtMiddleName" type="text" maxlength="20" id="dnn_ctr408_Desktop_subctr_txtMiddleName" class="txtName" onkeyup="DoChangeFullName()" onblur="DoUpcase(this, true);" style="text-transform: uppercase; text-align: center" />
                        </td>
                        <td>
                            <input name="dnn$ctr408$Desktop$subctr$txtLastName" type="text" maxlength="20" id="dnn_ctr408_Desktop_subctr_txtLastName" class="txtName" onkeyup="DoChangeFullName()" onblur="DoUpcase(this, true);" style="text-transform: uppercase; text-align: center" />
                        </td>
                    </tr>
                </table>
                <div style="padding-right: 5px; padding-left: 7px; padding-bottom: 4px; border-left: darkgray 4px solid; padding-top: 4px; background-color: #f8f8f8; border-right: darkgray 1px solid; border-top: darkgray 1px solid; margin-top: 4px; font-size: 11px; width: 840px; border-bottom: darkgray 1px solid;">
                    Họ và tên sẽ được in trong Giấy miễn thị thực. Đề nghị kiểm tra khớp tên trong Hộ
                    chiếu / giấy thường trú: <span id="spPrintedName" style="border-right: 1px; padding-right: 3px; border-top: 1px; padding-left: 5px; padding-bottom: 3px; border-left: 1px; padding-top: 3px; border-bottom: sandybrown 1px solid; background-color: papayawhip;">...</span><br />
                    Full name will be printed in Visa Exemption. Please verify if this matches your
                    full name in your Passport / Permanent Residence Document
                </div>
            </li>
            <li class="RegLI"><b>
                <div style="float: right; width: 30px; text-align: right">
                    <img id="dnn_ctr408_Desktop_subctr_imgInfo2" title="Trợ giúp / Help" onclick="ChangeHelpBoxDisplay(document.all.divInfo2);" onmouseover="ChangeInfoImage(this)" onmouseout="ChangeInfoImage(this)" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;cursor: hand" />
                </div>
                Họ và tên Việt Nam&nbsp;<br />
            </b>Vietnamese full name
                <div id="divInfo2" class="HelpBox">
                    Nhập họ và tên đã từng được sử dụng ở Việt Nam trước đây (nếu có)
                </div>
                <br />
                Tên &nbsp;<input name="dnn$ctr408$Desktop$subctr$txtFirstNameVN" type="text" maxlength="20" id="dnn_ctr408_Desktop_subctr_txtFirstNameVN" onblur="DoUpcase(this, true);" style="width:71px;text-transform: uppercase" />
                &nbsp; &nbsp; Tên đệm
                <input name="dnn$ctr408$Desktop$subctr$txtMiddleNameVN" type="text" maxlength="20" id="dnn_ctr408_Desktop_subctr_txtMiddleNameVN" onblur="DoUpcase(this, true);" style="width:71px;text-transform: uppercase" />
                &nbsp; &nbsp; Họ &nbsp;<input name="dnn$ctr408$Desktop$subctr$txtLastNameVN" type="text" maxlength="20" id="dnn_ctr408_Desktop_subctr_txtLastNameVN" onblur="DoUpcase(this, true);" style="width:71px;text-transform: uppercase" /><br />
                First name &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; Middle name &nbsp; &nbsp;
                &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; Last name </li>
            <li class="RegLI"><b>
                <div style="float: right; width: 30px; text-align: right">
                    <img id="dnn_ctr408_Desktop_subctr_imgInfo4" title="Trợ giúp / Help" onclick="ChangeHelpBoxDisplay(document.all.divInfo4);" onmouseover="ChangeInfoImage(this)" onmouseout="ChangeInfoImage(this)" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;cursor: hand" />
                </div>
                <span class='liNum'>2. </span>Giới tính</b> &nbsp; &nbsp; &nbsp;
                <label for="dnn_ctr408_Desktop_subctr_chkSex_Male">nam</label><input id="dnn_ctr408_Desktop_subctr_chkSex_Male" type="checkbox" name="dnn$ctr408$Desktop$subctr$chkSex_Male" checked="checked" onclick="DoSelectGender(this, 'Male');" />
                &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<label for="dnn_ctr408_Desktop_subctr_chkSex_Female">nữ</label><input id="dnn_ctr408_Desktop_subctr_chkSex_Female" type="checkbox" name="dnn$ctr408$Desktop$subctr$chkSex_Female" onclick="DoSelectGender(this, 'Female');" /><br />
                <span style="width: 83px;">Sex&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                <span style="width: 83px;">&nbsp;&nbsp;&nbsp;&nbsp;Male&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                <span style="width: 80px;">Female</span>
                <div id="divInfo4" class="HelpBox">
                    Nam hoặc nữ
                </div>
            </li>
            <li class="RegLI"><b>
                <div style="float: right; width: 30px; text-align: right">
                    <img id="dnn_ctr408_Desktop_subctr_imgInfo3" title="Trợ giúp / Help" onclick="ChangeHelpBoxDisplay(document.all.divInfo3);" onmouseover="ChangeInfoImage(this)" onmouseout="ChangeInfoImage(this)" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;cursor: hand" />
                </div>
                <span class='liNum'>3. </span>Sinh ngày</b>&nbsp;&nbsp;&nbsp;
                <input name="dnn$ctr408$Desktop$subctr$txtDayOfBirth" type="text" maxlength="2" id="dnn_ctr408_Desktop_subctr_txtDayOfBirth" style="width:30px;" />
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; tháng &nbsp;<input name="dnn$ctr408$Desktop$subctr$txtMonthOfBirth" type="text" maxlength="2" id="dnn_ctr408_Desktop_subctr_txtMonthOfBirth" style="width:30px;" />
                &nbsp;&nbsp;&nbsp;&nbsp; năm
                <input name="dnn$ctr408$Desktop$subctr$txtYearOfBirth" type="text" maxlength="4" id="dnn_ctr408_Desktop_subctr_txtYearOfBirth" style="width:80px;" />
                &nbsp;<span style="color: #cc0000">*</span><br />
                Date of birth&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Day&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <div id="divInfo3" class="HelpBox">
                    Nhập chính xác và đầy đủ ngày tháng năm sinh.
                    <br />
                    Trường hợp nếu trên hộ chiếu/giấy tờ thường trú chỉ ghi năm sinh (không có ngày/tháng),
                    bạn nhập năm sinh và bỏ trống thông tin ngày tháng
                </div>
                Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                Year</li>
            <li class="RegLI"><b>
                <div style="float: right; width: 30px; text-align: right">
                    <img id="dnn_ctr408_Desktop_subctr_imgInfo5" title="Trợ giúp / Help" onclick="ChangeHelpBoxDisplay(document.all.divInfo5);" onmouseover="ChangeInfoImage(this)" onmouseout="ChangeInfoImage(this)" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;cursor: hand" />
                </div>
                <span class='liNum'>4. </span>Nơi sinh<span style="color: #cc0000">*</span>
                <input name="dnn$ctr408$Desktop$subctr$txtPlaceOfBirth" type="text" maxlength="70" id="dnn_ctr408_Desktop_subctr_txtPlaceOfBirth" onblur="DoUpcase(this, false);" style="width:200px;" /></b><br />
                Place of birth
                <div id="divInfo5" class="HelpBox">
                    Nhập nơi sinh trùng với nơi sinh ghi trên hộ chiếu/giấy tờ thường trú
                </div>
            </li>
            <li class="RegLI"><b>
                <div style="float: right; width: 30px; text-align: right">
                    <img id="dnn_ctr408_Desktop_subctr_imgInfo6" title="Trợ giúp / Help" onclick="ChangeHelpBoxDisplay(document.all.divInfo6);" onmouseover="ChangeInfoImage(this)" onmouseout="ChangeInfoImage(this)" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;cursor: hand" />
                </div>
                Quốc tịch </b>
                <div id="divInfo6" style="margin-bottom: 7px;" class="HelpBox">
                    a. Chọn quốc tịch hiện tại của bạn
                    <br />
                    b. Nếu là người Việt Nam hiện đang định cư ở nước ngoài, bạn hãy chọn quốc tịch
                    gốc là Việt Nam
                </div>
                <div>
                    <div style="float: left; width: 350px">
                        <b><span class='liNum'>5. </span>Quốc tịch gốc<span style="color: #cc0000">*</span>&nbsp;<select name="dnn$ctr408$Desktop$subctr$ddlNationalityAtBirth" id="dnn_ctr408_Desktop_subctr_ddlNationalityAtBirth" style="width:170px;">
		<option value="--- Chọn nước ---">--- Chọn nước ---</option>
		<option value="AFG">Afghanistan</option>
		<option value="ALA">&#197;land Islands</option>
		<option value="ALB">Albania</option>
		<option value="DZA">Algeria</option>
		<option value="ASM">American Samoa</option>
		<option value="AND">Andorra</option>
		<option value="AGO">Angola</option>
		<option value="AIA">Anguilla</option>
		<option value="ATA">Antarctica</option>
		<option value="ATG">Antigua and Barbuda</option>
		<option value="ARG">Argentina</option>
		<option value="ARM">Armenia</option>
		<option value="ABW">Aruba</option>
		<option value="AUS">Australia</option>
		<option value="AUT">Austria</option>
		<option value="AZE">Azerbaijan</option>
		<option value="BHS">Bahamas</option>
		<option value="BHR">Bahrain</option>
		<option value="BGD">Bangladesh</option>
		<option value="BRB">Barbados</option>
		<option value="BLR">Belarus</option>
		<option value="BEL">Belgium</option>
		<option value="BLZ">Belize</option>
		<option value="BEN">Benin</option>
		<option value="BMU">Bermuda</option>
		<option value="BTN">Bhutan</option>
		<option value="BOL">Bolivia</option>
		<option value="BIH">Bosnia and Herzegovina</option>
		<option value="BWA">Botswana</option>
		<option value="BVT">Bouvet Island</option>
		<option value="BRA">Brazil</option>
		<option value="IOT">British Indian Ocean Territory</option>
		<option value="BRN">Brunei Darussalam</option>
		<option value="BGR">Bulgaria</option>
		<option value="BFA">Burkina Faso</option>
		<option value="BDI">Burundi</option>
		<option value="KHM">Cambodia</option>
		<option value="CMR">Cameroon</option>
		<option value="CAN">Canada</option>
		<option value="CPV">Cape Verde</option>
		<option value="CYM">Cayman Islands</option>
		<option value="CAF">Central African Republic</option>
		<option value="TCD">Chad</option>
		<option value="CHL">Chile</option>
		<option value="CHN">China</option>
		<option value="CXR">Christmas Island</option>
		<option value="CCK">Cocos (Keeling) Islands</option>
		<option value="COL">Colombia</option>
		<option value="COM">Comoros</option>
		<option value="COG">Congo</option>
		<option value="COD">Congo, the Democratic Republic of the</option>
		<option value="COK">Cook Islands</option>
		<option value="CRI">Costa Rica</option>
		<option value="CIV">C&#244;te d'Ivoire</option>
		<option value="HRV">Croatia</option>
		<option value="CUB">Cuba</option>
		<option value="CYP">Cyprus</option>
		<option value="CZE">Czech Republic</option>
		<option value="DNK">Denmark</option>
		<option value="DJI">Djibouti</option>
		<option value="DMA">Dominica</option>
		<option value="DOM">Dominican Republic</option>
		<option value="ECU">Ecuador</option>
		<option value="EGY">Egypt</option>
		<option value="SLV">El Salvador</option>
		<option value="GNQ">Equatorial Guinea</option>
		<option value="ERI">Eritrea</option>
		<option value="EST">Estonia</option>
		<option value="ETH">Ethiopia</option>
		<option value="FLK">Falkland Islands (Malvinas)</option>
		<option value="FRO">Faroe Islands</option>
		<option value="FJI">Fiji</option>
		<option value="FIN">Finland</option>
		<option value="FRA">France</option>
		<option value="GUF">French Guiana</option>
		<option value="PYF">French Polynesia</option>
		<option value="ATF">French Southern Territories</option>
		<option value="GAB">Gabon</option>
		<option value="GMB">Gambia</option>
		<option value="GEO">Georgia</option>
		<option value="DEU">Germany</option>
		<option value="GHA">Ghana</option>
		<option value="GIB">Gibraltar</option>
		<option value="GRC">Greece</option>
		<option value="GRL">Greenland</option>
		<option value="GRD">Grenada</option>
		<option value="GLP">Guadeloupe</option>
		<option value="GUM">Guam</option>
		<option value="GTM">Guatemala</option>
		<option value="GGY">Guernsey</option>
		<option value="GIN">Guinea</option>
		<option value="GNB">Guinea-Bissau</option>
		<option value="GUY">Guyana</option>
		<option value="HTI">Haiti</option>
		<option value="HMD">Heard Island and McDonald Islands</option>
		<option value="VAT">Holy See (Vatican City State)</option>
		<option value="HND">Honduras</option>
		<option value="HKG">Hong Kong</option>
		<option value="HUN">Hungary</option>
		<option value="ISL">Iceland</option>
		<option value="IND">India</option>
		<option value="IDN">Indonesia</option>
		<option value="IRN">Iran, Islamic Republic of</option>
		<option value="IRQ">Iraq</option>
		<option value="IRL">Ireland</option>
		<option value="IMN">Isle of Man</option>
		<option value="ISR">Israel</option>
		<option value="ITA">Italy</option>
		<option value="JAM">Jamaica</option>
		<option value="JPN">Japan</option>
		<option value="JEY">Jersey</option>
		<option value="JOR">Jordan</option>
		<option value="KAZ">Kazakhstan</option>
		<option value="KEN">Kenya</option>
		<option value="KIR">Kiribati</option>
		<option value="PRK">Korea, Democratic People's Republic of</option>
		<option value="KOR">Korea, Republic of</option>
		<option value="KWT">Kuwait</option>
		<option value="KGZ">Kyrgyzstan</option>
		<option value="LAO">Lao People's Democratic Republic</option>
		<option value="LVA">Latvia</option>
		<option value="LBN">Lebanon</option>
		<option value="LSO">Lesotho</option>
		<option value="LBR">Liberia</option>
		<option value="LBY">Libyan Arab Jamahiriya</option>
		<option value="LIE">Liechtenstein</option>
		<option value="LTU">Lithuania</option>
		<option value="LUX">Luxembourg</option>
		<option value="MAC">Macao</option>
		<option value="MKD">Macedonia, the former Yugoslav Republic of</option>
		<option value="MDG">Madagascar</option>
		<option value="MWI">Malawi</option>
		<option value="MYS">Malaysia</option>
		<option value="MDV">Maldives</option>
		<option value="MLI">Mali</option>
		<option value="MLT">Malta</option>
		<option value="MHL">Marshall Islands</option>
		<option value="MTQ">Martinique</option>
		<option value="MRT">Mauritania</option>
		<option value="MUS">Mauritius</option>
		<option value="MYT">Mayotte</option>
		<option value="MEX">Mexico</option>
		<option value="FSM">Micronesia, Federated States of</option>
		<option value="MDA">Moldova, Republic of</option>
		<option value="MCO">Monaco</option>
		<option value="MNG">Mongolia</option>
		<option value="MNE">Montenegro</option>
		<option value="MSR">Montserrat</option>
		<option value="MAR">Morocco</option>
		<option value="MOZ">Mozambique</option>
		<option value="MMR">Myanmar</option>
		<option value="NAM">Namibia</option>
		<option value="NRU">Nauru</option>
		<option value="NPL">Nepal</option>
		<option value="NLD">Netherlands</option>
		<option value="ANT">Netherlands Antilles</option>
		<option value="NCL">New Caledonia</option>
		<option value="NZL">New Zealand</option>
		<option value="NIC">Nicaragua</option>
		<option value="NER">Niger</option>
		<option value="NGA">Nigeria</option>
		<option value="NIU">Niue</option>
		<option value="NFK">Norfolk Island</option>
		<option value="MNP">Northern Mariana Islands</option>
		<option value="NOR">Norway</option>
		<option value="OMN">Oman</option>
		<option value="PAK">Pakistan</option>
		<option value="PLW">Palau</option>
		<option value="PSE">Palestinian Territory, Occupied</option>
		<option value="PAN">Panama</option>
		<option value="PNG">Papua New Guinea</option>
		<option value="PRY">Paraguay</option>
		<option value="PER">Peru</option>
		<option value="PHL">Philippines</option>
		<option value="PCN">Pitcairn</option>
		<option value="POL">Poland</option>
		<option value="PRT">Portugal</option>
		<option value="PRI">Puerto Rico</option>
		<option value="QAT">Qatar</option>
		<option value="REU">R&#233;union</option>
		<option value="ROU">Romania</option>
		<option value="RUS">Russian Federation</option>
		<option value="RWA">Rwanda</option>
		<option value="SHN">Saint Helena</option>
		<option value="KNA">Saint Kitts and Nevis</option>
		<option value="LCA">Saint Lucia</option>
		<option value="SPM">Saint Pierre and Miquelon</option>
		<option value="VCT">Saint Vincent and the Grenadines</option>
		<option value="WSM">Samoa</option>
		<option value="SMR">San Marino</option>
		<option value="STP">Sao Tome and Principe</option>
		<option value="SAU">Saudi Arabia</option>
		<option value="SEN">Senegal</option>
		<option value="SRB">Serbia</option>
		<option value="SYC">Seychelles</option>
		<option value="SLE">Sierra Leone</option>
		<option value="SGP">Singapore</option>
		<option value="SVK">Slovakia</option>
		<option value="SVN">Slovenia</option>
		<option value="SLB">Solomon Islands</option>
		<option value="SOM">Somalia</option>
		<option value="ZAF">South Africa</option>
		<option value="SGS">South Georgia and the South Sandwich Islands</option>
		<option value="ESP">Spain</option>
		<option value="LKA">Sri Lanka</option>
		<option value="SDN">Sudan</option>
		<option value="SUR">Suriname</option>
		<option value="SJM">Svalbard and Jan Mayen</option>
		<option value="SWZ">Swaziland</option>
		<option value="SWE">Sweden</option>
		<option value="CHE">Switzerland</option>
		<option value="SYR">Syrian Arab Republic</option>
		<option value="TWN">Taiwan</option>
		<option value="TJK">Tajikistan</option>
		<option value="TZA">Tanzania, United Republic of</option>
		<option value="THA">Thailand</option>
		<option value="TLS">Timor-Leste</option>
		<option value="TGO">Togo</option>
		<option value="TKL">Tokelau</option>
		<option value="TON">Tonga</option>
		<option value="TTO">Trinidad and Tobago</option>
		<option value="TUN">Tunisia</option>
		<option value="TUR">Turkey</option>
		<option value="TKM">Turkmenistan</option>
		<option value="TCA">Turks and Caicos Islands</option>
		<option value="TUV">Tuvalu</option>
		<option value="UGA">Uganda</option>
		<option value="UKR">Ukraine</option>
		<option value="ARE">United Arab Emirates</option>
		<option value="GBR">United Kingdom</option>
		<option value="USA">United States</option>
		<option value="UMI">United States Minor Outlying Islands</option>
		<option value="URY">Uruguay</option>
		<option value="UZB">Uzbekistan</option>
		<option value="VUT">Vanuatu</option>
		<option value="VEN">Venezuela</option>
		<option selected="selected" value="VNM">Viet Nam</option>
		<option value="VGB">Virgin Islands, British</option>
		<option value="VIR">Virgin Islands, U.S.</option>
		<option value="WLF">Wallis and Futuna</option>
		<option value="ESH">Western Sahara</option>
		<option value="YEM">Yemen</option>
		<option value="ZMB">Zambia</option>
		<option value="ZWE">Zimbabwe</option>

	</select>
                        </b>
                        <br />
                        Nationality at birth
                    </div>
                    <div style="float: right; width: 400px">
                        <b><span class='liNum'>6. </span>Quốc tịch hiện nay<span style="color: #cc0000">*</span>
                            <select name="dnn$ctr408$Desktop$subctr$ddlNationalityAtPresent" id="dnn_ctr408_Desktop_subctr_ddlNationalityAtPresent" onchange="DoChangeNationalityAtPresent(this.selectedIndex);" style="width:170px;">
		<option value="--- Chọn nước ---">--- Chọn nước ---</option>
		<option value="AFG">Afghanistan</option>
		<option value="ALA">&#197;land Islands</option>
		<option value="ALB">Albania</option>
		<option value="DZA">Algeria</option>
		<option value="ASM">American Samoa</option>
		<option value="AND">Andorra</option>
		<option value="AGO">Angola</option>
		<option value="AIA">Anguilla</option>
		<option value="ATA">Antarctica</option>
		<option value="ATG">Antigua and Barbuda</option>
		<option value="ARG">Argentina</option>
		<option value="ARM">Armenia</option>
		<option value="ABW">Aruba</option>
		<option value="AUS">Australia</option>
		<option value="AUT">Austria</option>
		<option value="AZE">Azerbaijan</option>
		<option value="BHS">Bahamas</option>
		<option value="BHR">Bahrain</option>
		<option value="BGD">Bangladesh</option>
		<option value="BRB">Barbados</option>
		<option value="BLR">Belarus</option>
		<option value="BEL">Belgium</option>
		<option value="BLZ">Belize</option>
		<option value="BEN">Benin</option>
		<option value="BMU">Bermuda</option>
		<option value="BTN">Bhutan</option>
		<option value="BOL">Bolivia</option>
		<option value="BIH">Bosnia and Herzegovina</option>
		<option value="BWA">Botswana</option>
		<option value="BVT">Bouvet Island</option>
		<option value="BRA">Brazil</option>
		<option value="IOT">British Indian Ocean Territory</option>
		<option value="BRN">Brunei Darussalam</option>
		<option value="BGR">Bulgaria</option>
		<option value="BFA">Burkina Faso</option>
		<option value="BDI">Burundi</option>
		<option value="KHM">Cambodia</option>
		<option value="CMR">Cameroon</option>
		<option value="CAN">Canada</option>
		<option value="CPV">Cape Verde</option>
		<option value="CYM">Cayman Islands</option>
		<option value="CAF">Central African Republic</option>
		<option value="TCD">Chad</option>
		<option value="CHL">Chile</option>
		<option value="CHN">China</option>
		<option value="CXR">Christmas Island</option>
		<option value="CCK">Cocos (Keeling) Islands</option>
		<option value="COL">Colombia</option>
		<option value="COM">Comoros</option>
		<option value="COG">Congo</option>
		<option value="COD">Congo, the Democratic Republic of the</option>
		<option value="COK">Cook Islands</option>
		<option value="CRI">Costa Rica</option>
		<option value="CIV">C&#244;te d'Ivoire</option>
		<option value="HRV">Croatia</option>
		<option value="CUB">Cuba</option>
		<option value="CYP">Cyprus</option>
		<option value="CZE">Czech Republic</option>
		<option value="DNK">Denmark</option>
		<option value="DJI">Djibouti</option>
		<option value="DMA">Dominica</option>
		<option value="DOM">Dominican Republic</option>
		<option value="ECU">Ecuador</option>
		<option value="EGY">Egypt</option>
		<option value="SLV">El Salvador</option>
		<option value="GNQ">Equatorial Guinea</option>
		<option value="ERI">Eritrea</option>
		<option value="EST">Estonia</option>
		<option value="ETH">Ethiopia</option>
		<option value="FLK">Falkland Islands (Malvinas)</option>
		<option value="FRO">Faroe Islands</option>
		<option value="FJI">Fiji</option>
		<option value="FIN">Finland</option>
		<option value="FRA">France</option>
		<option value="GUF">French Guiana</option>
		<option value="PYF">French Polynesia</option>
		<option value="ATF">French Southern Territories</option>
		<option value="GAB">Gabon</option>
		<option value="GMB">Gambia</option>
		<option value="GEO">Georgia</option>
		<option value="DEU">Germany</option>
		<option value="GHA">Ghana</option>
		<option value="GIB">Gibraltar</option>
		<option value="GRC">Greece</option>
		<option value="GRL">Greenland</option>
		<option value="GRD">Grenada</option>
		<option value="GLP">Guadeloupe</option>
		<option value="GUM">Guam</option>
		<option value="GTM">Guatemala</option>
		<option value="GGY">Guernsey</option>
		<option value="GIN">Guinea</option>
		<option value="GNB">Guinea-Bissau</option>
		<option value="GUY">Guyana</option>
		<option value="HTI">Haiti</option>
		<option value="HMD">Heard Island and McDonald Islands</option>
		<option value="VAT">Holy See (Vatican City State)</option>
		<option value="HND">Honduras</option>
		<option value="HKG">Hong Kong</option>
		<option value="HUN">Hungary</option>
		<option value="ISL">Iceland</option>
		<option value="IND">India</option>
		<option value="IDN">Indonesia</option>
		<option value="IRN">Iran, Islamic Republic of</option>
		<option value="IRQ">Iraq</option>
		<option value="IRL">Ireland</option>
		<option value="IMN">Isle of Man</option>
		<option value="ISR">Israel</option>
		<option value="ITA">Italy</option>
		<option value="JAM">Jamaica</option>
		<option value="JPN">Japan</option>
		<option value="JEY">Jersey</option>
		<option value="JOR">Jordan</option>
		<option value="KAZ">Kazakhstan</option>
		<option value="KEN">Kenya</option>
		<option value="KIR">Kiribati</option>
		<option value="PRK">Korea, Democratic People's Republic of</option>
		<option value="KOR">Korea, Republic of</option>
		<option value="KWT">Kuwait</option>
		<option value="KGZ">Kyrgyzstan</option>
		<option value="LAO">Lao People's Democratic Republic</option>
		<option value="LVA">Latvia</option>
		<option value="LBN">Lebanon</option>
		<option value="LSO">Lesotho</option>
		<option value="LBR">Liberia</option>
		<option value="LBY">Libyan Arab Jamahiriya</option>
		<option value="LIE">Liechtenstein</option>
		<option value="LTU">Lithuania</option>
		<option value="LUX">Luxembourg</option>
		<option value="MAC">Macao</option>
		<option value="MKD">Macedonia, the former Yugoslav Republic of</option>
		<option value="MDG">Madagascar</option>
		<option value="MWI">Malawi</option>
		<option value="MYS">Malaysia</option>
		<option value="MDV">Maldives</option>
		<option value="MLI">Mali</option>
		<option value="MLT">Malta</option>
		<option value="MHL">Marshall Islands</option>
		<option value="MTQ">Martinique</option>
		<option value="MRT">Mauritania</option>
		<option value="MUS">Mauritius</option>
		<option value="MYT">Mayotte</option>
		<option value="MEX">Mexico</option>
		<option value="FSM">Micronesia, Federated States of</option>
		<option value="MDA">Moldova, Republic of</option>
		<option value="MCO">Monaco</option>
		<option value="MNG">Mongolia</option>
		<option value="MNE">Montenegro</option>
		<option value="MSR">Montserrat</option>
		<option value="MAR">Morocco</option>
		<option value="MOZ">Mozambique</option>
		<option value="MMR">Myanmar</option>
		<option value="NAM">Namibia</option>
		<option value="NRU">Nauru</option>
		<option value="NPL">Nepal</option>
		<option value="NLD">Netherlands</option>
		<option value="ANT">Netherlands Antilles</option>
		<option value="NCL">New Caledonia</option>
		<option value="NZL">New Zealand</option>
		<option value="NIC">Nicaragua</option>
		<option value="NER">Niger</option>
		<option value="NGA">Nigeria</option>
		<option value="NIU">Niue</option>
		<option value="NFK">Norfolk Island</option>
		<option value="MNP">Northern Mariana Islands</option>
		<option value="NOR">Norway</option>
		<option value="OMN">Oman</option>
		<option value="PAK">Pakistan</option>
		<option value="PLW">Palau</option>
		<option value="PSE">Palestinian Territory, Occupied</option>
		<option value="PAN">Panama</option>
		<option value="PNG">Papua New Guinea</option>
		<option value="PRY">Paraguay</option>
		<option value="PER">Peru</option>
		<option value="PHL">Philippines</option>
		<option value="PCN">Pitcairn</option>
		<option value="POL">Poland</option>
		<option value="PRT">Portugal</option>
		<option value="PRI">Puerto Rico</option>
		<option value="QAT">Qatar</option>
		<option value="REU">R&#233;union</option>
		<option value="ROU">Romania</option>
		<option value="RUS">Russian Federation</option>
		<option value="RWA">Rwanda</option>
		<option value="SHN">Saint Helena</option>
		<option value="KNA">Saint Kitts and Nevis</option>
		<option value="LCA">Saint Lucia</option>
		<option value="SPM">Saint Pierre and Miquelon</option>
		<option value="VCT">Saint Vincent and the Grenadines</option>
		<option value="WSM">Samoa</option>
		<option value="SMR">San Marino</option>
		<option value="STP">Sao Tome and Principe</option>
		<option value="SAU">Saudi Arabia</option>
		<option value="SEN">Senegal</option>
		<option value="SRB">Serbia</option>
		<option value="SYC">Seychelles</option>
		<option value="SLE">Sierra Leone</option>
		<option value="SGP">Singapore</option>
		<option value="SVK">Slovakia</option>
		<option value="SVN">Slovenia</option>
		<option value="SLB">Solomon Islands</option>
		<option value="SOM">Somalia</option>
		<option value="ZAF">South Africa</option>
		<option value="SGS">South Georgia and the South Sandwich Islands</option>
		<option value="ESP">Spain</option>
		<option value="LKA">Sri Lanka</option>
		<option value="SDN">Sudan</option>
		<option value="SUR">Suriname</option>
		<option value="SJM">Svalbard and Jan Mayen</option>
		<option value="SWZ">Swaziland</option>
		<option value="SWE">Sweden</option>
		<option value="CHE">Switzerland</option>
		<option value="SYR">Syrian Arab Republic</option>
		<option value="TWN">Taiwan</option>
		<option value="TJK">Tajikistan</option>
		<option value="TZA">Tanzania, United Republic of</option>
		<option value="THA">Thailand</option>
		<option value="TLS">Timor-Leste</option>
		<option value="TGO">Togo</option>
		<option value="TKL">Tokelau</option>
		<option value="TON">Tonga</option>
		<option value="TTO">Trinidad and Tobago</option>
		<option value="TUN">Tunisia</option>
		<option value="TUR">Turkey</option>
		<option value="TKM">Turkmenistan</option>
		<option value="TCA">Turks and Caicos Islands</option>
		<option value="TUV">Tuvalu</option>
		<option value="UGA">Uganda</option>
		<option value="UKR">Ukraine</option>
		<option value="ARE">United Arab Emirates</option>
		<option value="GBR">United Kingdom</option>
		<option value="USA">United States</option>
		<option value="UMI">United States Minor Outlying Islands</option>
		<option value="URY">Uruguay</option>
		<option value="UZB">Uzbekistan</option>
		<option value="VUT">Vanuatu</option>
		<option value="VEN">Venezuela</option>
		<option value="VNM">Viet Nam</option>
		<option value="VGB">Virgin Islands, British</option>
		<option value="VIR">Virgin Islands, U.S.</option>
		<option value="WLF">Wallis and Futuna</option>
		<option value="ESH">Western Sahara</option>
		<option value="YEM">Yemen</option>
		<option value="ZMB">Zambia</option>
		<option value="ZWE">Zimbabwe</option>

	</select>
                        </b>
                        <br />
                        Current nationality&nbsp;
                    </div>
                </div>
            </li>
        </ol>
    </div>
    <div style="width: 900px; float: left">
        <ol start="6">
            <li class="RegLI"><b>
                <div style="float: right; width: 30px; text-align: right">
                    <img id="dnn_ctr408_Desktop_subctr_imgInfo7" title="Trợ giúp / Help" onclick="ChangeHelpBoxDisplay(document.all.divInfo7);" onmouseover="ChangeInfoImage(this)" onmouseout="ChangeInfoImage(this)" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;cursor: hand" />
                </div>
                <div>
                    <span class='liNum'>7. </span>Hộ chiếu/giấy tờ có giá trị đi lại quốc tế số:<span
                        style="color: #cc0000">*</span>
                </div>
            </b>
                <div id="divInfo7" class="HelpBox">
                    Bắt buộc phải khai báo một trong hai loại giấy tờ là Hộ chiếu do nước ngoài cấp
                    hoặc thẻ cư trú.
                    <br />
                    <span style="text-decoration: underline">Chú ý</span>: thời hạn hộ chiếu / giấy
                    tờ cư trú phải có giá trị trong vòng lớn hơn 06 tháng tại thời điểm đăng ký.
                </div>
                <div style="float: left; width: 350px">
                    Passport<br />
                    <div style="width: 100%">
                        <b><span style="width: 80px">Số</span>
                            <input name="dnn$ctr408$Desktop$subctr$txtPassportNo" type="text" maxlength="15" id="dnn_ctr408_Desktop_subctr_txtPassportNo" onkeypress="CheckInputChar(event, this, true, true, true)" style="width:125px;text-transform: uppercase" /></b><br />
                        <span>No</span>
                    </div>
                    <div style="width: 100%">
                        <b><span style="width: 80px">Giá trị đến</span>
                            <input name="dnn$ctr408$Desktop$subctr$diPassportExpireDate$txtDateInput" type="text" id="dnn_ctr408_Desktop_subctr_diPassportExpireDate_txtDateInput" title="dd/MM/yyyy" onblur="if(this.value != '__/__/____') {validateDate(this);}" style="width:85px;" />
<input type="hidden" name="dnn$ctr408$Desktop$subctr$diPassportExpireDate$MaskedEditExtender1_ClientState" id="dnn_ctr408_Desktop_subctr_diPassportExpireDate_MaskedEditExtender1_ClientState" />
                            &nbsp; </b>
                        <br />
                        <span>Date of expiry (dd/MM/yyyy)&nbsp;</span>
                    </div>
                    <div style="width: 100%">
                        <b><span style="width: 80px">Cơ quan cấp</span>
                            <input name="dnn$ctr408$Desktop$subctr$txtPassportIssuingAuthority" type="text" maxlength="75" id="dnn_ctr408_Desktop_subctr_txtPassportIssuingAuthority" onblur="DoUpcase(this, false);" style="width:200px;" /></b><br />
                        <span>Issuing Authority</span>
                    </div>
                    <div style="width: 100%; display: none">
                        <b><span style="width: 80px">Nước cấp</span>
                            <select name="dnn$ctr408$Desktop$subctr$ddlPassportIssuingCountry" id="dnn_ctr408_Desktop_subctr_ddlPassportIssuingCountry" style="width:170px;">

	</select>
                        </b>
                        <br />
                        <span>Issuing Country</span>
                    </div>
                </div>
                <div style="float: right; width: 350px">
                    <strong>or</strong> Permanent Residence Document issued by foreign country
                    <br />
                    <div style="width: 100%">
                        <span style="width: 80px"><b>Số</b></span>
                        <input name="dnn$ctr408$Desktop$subctr$txtPRDNo" type="text" maxlength="15" id="dnn_ctr408_Desktop_subctr_txtPRDNo" onblur="DoUpcase(this, true);" style="width:125px;text-transform: uppercase" /><br />
                        No
                    </div>
                    <div style="width: 100%">
                        <span style="width: 80px"><b>Giá trị đến</b></span>
                        <input name="dnn$ctr408$Desktop$subctr$diPRDExpireDate$txtDateInput" type="text" id="dnn_ctr408_Desktop_subctr_diPRDExpireDate_txtDateInput" title="dd/MM/yyyy" onblur="if(this.value != '__/__/____') {validateDate(this);}" style="width:85px;" />
<input type="hidden" name="dnn$ctr408$Desktop$subctr$diPRDExpireDate$MaskedEditExtender1_ClientState" id="dnn_ctr408_Desktop_subctr_diPRDExpireDate_MaskedEditExtender1_ClientState" />
                        <br />
                        Date of expiry (dd/MM/yyyy)
                    </div>
                    <div style="width: 100%">
                        <span style="width: 80px"><b>Cơ quan cấp</b></span>
                        <input name="dnn$ctr408$Desktop$subctr$txtPRDIssuingAuthority" type="text" maxlength="75" id="dnn_ctr408_Desktop_subctr_txtPRDIssuingAuthority" onblur="DoUpcase(this, false);" style="width:200px;" /><br />
                        Issuing Authority
                    </div>
                    <div style="width: 100%; display: none">
                        <b><span style="width: 80px">Nước cấp</span>
                            <select name="dnn$ctr408$Desktop$subctr$ddlPRDIssuingCountry" id="dnn_ctr408_Desktop_subctr_ddlPRDIssuingCountry" style="width:170px;">

	</select>
                        </b>
                        <br />
                        <span>Issuing Country</span>
                    </div>
                </div>
                <br />
                <div>
                    &nbsp;
                </div>
            </li>
        </ol>
    </div>
    <div style="width: 900px; float: left">
        <ol start="7">
            <li class="RegLI"><b>
                <div style="float: right; width: 30px; text-align: right">
                    <img id="dnn_ctr408_Desktop_subctr_imgInfo8" title="Trợ giúp / Help" onclick="ChangeHelpBoxDisplay(document.all.divInfo8);" onmouseover="ChangeInfoImage(this)" onmouseout="ChangeInfoImage(this)" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;cursor: hand" />
                </div>
                <span class='liNum'>8. </span>Nghề nghiệp<span style="color: #cc0000">*</span>
                <input name="dnn$ctr408$Desktop$subctr$txtOccupation" type="text" maxlength="115" id="dnn_ctr408_Desktop_subctr_txtOccupation" onblur="DoUpcase(this, true);" style="width:200px;" /></b><br />
                Occupation<div id="divInfo8" class="HelpBox">
                    Điền thông tin về nghề nghiệp hiện tại của bạn (tối đa 115 ký tự)
                </div>
            </li>
            <li class="RegLI"><b>
                <div style="float: right; width: 30px; text-align: right">
                    <img id="dnn_ctr408_Desktop_subctr_imgInfo9" title="Trợ giúp / Help" onclick="ChangeHelpBoxDisplay(document.all.divInfo9);" onmouseover="ChangeInfoImage(this)" onmouseout="ChangeInfoImage(this)" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;cursor: hand" />
                </div>
                <span class='liNum'>9. </span>Địa chỉ cư trú, nơi làm việc hiện nay ở nước ngoài<span
                    style="color: #cc0000">*</span></b><br />
                Current residential, business address abroad
                <div id="divInfo9" class="HelpBox" style="margin-bottom: 7px">
                    Chi tiết địa chỉ thường trú hiện nay của bạn, gồm: thông tin địa chỉ, nước đang
                    sinh sống, điện thoại liên lạc.
                </div>
                <br />
                <div style="float: left;">
                    <input name="dnn$ctr408$Desktop$subctr$txtAddressAtPresent" type="text" maxlength="100" id="dnn_ctr408_Desktop_subctr_txtAddressAtPresent" onblur="DoUpcase(this, true);" style="width:400px;" />
                </div>
                <div style="float: right; width: 425px">
                    <div style="float: left; width: 225px; display: none">
                        <b>Nước<span style="color: #cc0000">*</span>
                            <select name="dnn$ctr408$Desktop$subctr$ddlAddressCountry" id="dnn_ctr408_Desktop_subctr_ddlAddressCountry" onchange="DoSelectAddressCountry(this.options[this.selectedIndex].value);" style="width:170px;">

	</select>
                        </b>
                        <br />
                        Country
                    </div>
                    <div style="float: right; width: 350px">
                        <b>Điện thoại/Email<span style="color: #cc0000">*</span>
                            <input name="dnn$ctr408$Desktop$subctr$txtPhoneNumber" type="text" maxlength="50" id="dnn_ctr408_Desktop_subctr_txtPhoneNumber" onkeypress="CheckPhoneNumberOrEmail(event);" style="width:200px;" /></b><br />
                        Telephone/Email
                    </div>
                </div>
                &nbsp;<div>
                    &nbsp;
                </div>
            </li>
        </ol>
    </div>
    <div style="width: 900px; float: left;">
        <ol start="9">
            <li class="RegLI"><b>
                <div style="float: right; width: 30px; text-align: right">
                    <img id="dnn_ctr408_Desktop_subctr_imgInfo10" title="Trợ giúp / Help" onclick="ChangeHelpBoxDisplay(document.all.divInfo10);" onmouseover="ChangeInfoImage(this)" onmouseout="ChangeInfoImage(this)" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;cursor: hand" />
                </div>


                <span class='liNum'>10. </span>Địa chỉ cư trú ở Việt Nam trước khi xuất cảnh định
                    cư ở nước ngoài (nếu có)</b><br />
                Previous residential address in Viet Nam before residing abroad (if
                any)
                
                <div id="divInfo10" class="HelpBox" style="margin-bottom: 7px">
                    Nhập thông tin địa chỉ thường trú ở VN trước khi ra nước ngoài (nếu có) và địa chỉ
                    dùng cho nhà chức trách liên hệ khi có việc khẩn cấp (có thể là địa chỉ thường trú
                    hiện nay của bạn)
                </div>
                <br />

                <input name="dnn$ctr408$Desktop$subctr$txtAddressBeforeResidingAbroad" type="text" maxlength="100" id="dnn_ctr408_Desktop_subctr_txtAddressBeforeResidingAbroad" onblur="DoUpcase(this, true);" style="width:400px;" /><br />
                <br />
                <span class='liNum'>11. </span><b>Địa chỉ liên hệ tại Việt Nam<span style="color: #cc0000">*</span></b><br />
                Contact address in Viet Nam
                <div>
                    <div style="float: left">
                        <input name="dnn$ctr408$Desktop$subctr$txtContactAddressInVN" type="text" maxlength="100" id="dnn_ctr408_Desktop_subctr_txtContactAddressInVN" style="width:400px;" />
                    </div>
                    <div style="float: right; width: 350px">
                        <b>Điện thoại<span style="color: #cc0000">*</span>
                            <input name="dnn$ctr408$Desktop$subctr$txtContactPhoneNumber" type="text" maxlength="15" id="dnn_ctr408_Desktop_subctr_txtContactPhoneNumber" onkeypress="CheckPhoneNumber(event);" style="width:200px;" /></b><br />
                        Telephone
                    </div>
                </div>
                <br />
            </li>
        </ol>
    </div>
    <div style="width: 900px; float: left;">
        <ol start="10">
            <li class="RegLI"><b>
                <div style="float: right; width: 30px; text-align: right">
                    <img id="dnn_ctr408_Desktop_subctr_imgInfo11" title="Trợ giúp / Help" onclick="ChangeHelpBoxDisplay(document.all.divInfo11);" onmouseover="ChangeInfoImage(this)" onmouseout="ChangeInfoImage(this)" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;cursor: hand" />
                </div>
                <span class='liNum'>12. </span>Trẻ em (dưới 14 tuổi) đi cùng hộ chiếu / giấy tờ
                thường trú</b><br />
                Accompanying Children (under 14 years old) included in the applicant's Passport/Permanent
                Residence Document travelling with<div id="divInfo11" class="HelpBox" style="margin-bottom: 7px">
                    Danh sách trẻ em (dưới 14 tuổi) đi kèm (tối đa 3 người) theo hộ chiếu/giấy tờ thường
                    trú của bạn
                </div>
                <div style="float: left;">
                    <div style="width: 100%">
                        <div style="float: left; width: 220px" align="center">
                            <b>Họ tên</b> - Fullname
                        </div>
                        <div style="float: left; width: 150px" align="center">
                            <b>Giới tính - </b>Sex
                        </div>
                        <div style="float: left; width: 200px" align="center">
                            <b>Ngày sinh - </b>Date of birth
                        </div>
                    </div>
                    <div style="width: 100%; float: left">
                        <div style="float: left; width: 220px">
                            <b><span style="width: 15px;">a. </span>
                                <input name="dnn$ctr408$Desktop$subctr$txtChild1Fullname" type="text" maxlength="50" id="dnn_ctr408_Desktop_subctr_txtChild1Fullname" onblur="DoUpcase(this, false);" style="width:200px;" /></b>
                        </div>
                        <div style="float: left; width: 150px" align="center">
                            <label for="dnn_ctr408_Desktop_subctr_chkSex_Child1_Male">nam</label><input id="dnn_ctr408_Desktop_subctr_chkSex_Child1_Male" type="checkbox" name="dnn$ctr408$Desktop$subctr$chkSex_Child1_Male" checked="checked" onclick="DoSelectGender(this, 'Male');" />
                            &nbsp; &nbsp; &nbsp; &nbsp;
                            <label for="dnn_ctr408_Desktop_subctr_chkSex_Child1_Female">nữ</label><input id="dnn_ctr408_Desktop_subctr_chkSex_Child1_Female" type="checkbox" name="dnn$ctr408$Desktop$subctr$chkSex_Child1_Female" onclick="DoSelectGender(this, 'Female');" />
                        </div>
                        <div style="float: left; width: 200px" align="center">
                            <b>
                                <input name="dnn$ctr408$Desktop$subctr$diChild1DateOfBirth$txtDateInput" type="text" id="dnn_ctr408_Desktop_subctr_diChild1DateOfBirth_txtDateInput" title="dd/MM/yyyy" onblur="if(this.value != '__/__/____') {validateDate(this);}" style="width:85px;" />
<input type="hidden" name="dnn$ctr408$Desktop$subctr$diChild1DateOfBirth$MaskedEditExtender1_ClientState" id="dnn_ctr408_Desktop_subctr_diChild1DateOfBirth_MaskedEditExtender1_ClientState" />
                            </b>
                        </div>
                    </div>
                    <div style="width: 100%; float: left;">
                        <div style="float: left; width: 220px">
                            <b><span style="width: 15px;">b. </span>
                                <input name="dnn$ctr408$Desktop$subctr$txtChild2Fullname" type="text" maxlength="50" id="dnn_ctr408_Desktop_subctr_txtChild2Fullname" onblur="DoUpcase(this, false);" style="width:200px;" /></b>
                        </div>
                        <div style="float: left; width: 150px" align="center">
                            <label for="dnn_ctr408_Desktop_subctr_chkSex_Child2_Male">nam</label><input id="dnn_ctr408_Desktop_subctr_chkSex_Child2_Male" type="checkbox" name="dnn$ctr408$Desktop$subctr$chkSex_Child2_Male" checked="checked" onclick="DoSelectGender(this, 'Male');" />
                            &nbsp; &nbsp; &nbsp; &nbsp;
                            <label for="dnn_ctr408_Desktop_subctr_chkSex_Child2_Female">nữ</label><input id="dnn_ctr408_Desktop_subctr_chkSex_Child2_Female" type="checkbox" name="dnn$ctr408$Desktop$subctr$chkSex_Child2_Female" onclick="DoSelectGender(this, 'Female');" />
                        </div>
                        <div style="float: left; width: 200px" align="center">
                            <b>
                                <input name="dnn$ctr408$Desktop$subctr$diChild2DateOfBirth$txtDateInput" type="text" id="dnn_ctr408_Desktop_subctr_diChild2DateOfBirth_txtDateInput" title="dd/MM/yyyy" onblur="if(this.value != '__/__/____') {validateDate(this);}" style="width:85px;" />
<input type="hidden" name="dnn$ctr408$Desktop$subctr$diChild2DateOfBirth$MaskedEditExtender1_ClientState" id="dnn_ctr408_Desktop_subctr_diChild2DateOfBirth_MaskedEditExtender1_ClientState" />
                            </b>
                        </div>
                    </div>
                    <div style="width: 100%; float: left; display: none">
                        <div style="float: left; width: 220px">
                            <b><span style="width: 15px;">c. </span>
                                <input name="dnn$ctr408$Desktop$subctr$txtChild3Fullname" type="text" maxlength="50" id="dnn_ctr408_Desktop_subctr_txtChild3Fullname" onblur="DoUpcase(this, false);" style="width:200px;" /></b>
                        </div>
                        <div style="float: left; width: 150px" align="center">
                            <label for="dnn_ctr408_Desktop_subctr_chkSex_Child3_Male">nam</label><input id="dnn_ctr408_Desktop_subctr_chkSex_Child3_Male" type="checkbox" name="dnn$ctr408$Desktop$subctr$chkSex_Child3_Male" checked="checked" onclick="DoSelectGender(this, 'Male');" />
                            &nbsp; &nbsp; &nbsp; &nbsp;
                            <label for="dnn_ctr408_Desktop_subctr_chkSex_Child3_Female">nữ</label><input id="dnn_ctr408_Desktop_subctr_chkSex_Child3_Female" type="checkbox" name="dnn$ctr408$Desktop$subctr$chkSex_Child3_Female" onclick="DoSelectGender(this, 'Female');" />
                        </div>
                        <div style="float: left; width: 200px" align="center">
                            <b>
                                <input name="dnn$ctr408$Desktop$subctr$diChild3DateOfBirth$txtDateInput" type="text" id="dnn_ctr408_Desktop_subctr_diChild3DateOfBirth_txtDateInput" title="dd/MM/yyyy" onblur="if(this.value != '__/__/____') {validateDate(this);}" style="width:85px;" />
<input type="hidden" name="dnn$ctr408$Desktop$subctr$diChild3DateOfBirth$MaskedEditExtender1_ClientState" id="dnn_ctr408_Desktop_subctr_diChild3DateOfBirth_MaskedEditExtender1_ClientState" />
                            </b>
                        </div>
                    </div>
                </div>
            </li>
        </ol>
    </div>
    <div style="width: 900px; float: left;">
        <ol start="11">
            <li class="RegLI"><b>
                <div style="float: right; width: 30px; text-align: right">
                    <img id="dnn_ctr408_Desktop_subctr_imgInfo12" title="Trợ giúp / Help" onclick="ChangeHelpBoxDisplay(document.all.divInfo12);" onmouseover="ChangeInfoImage(this)" onmouseout="ChangeInfoImage(this)" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;cursor: hand" />
                </div>
                <span class='liNum'>13. </span>Giấy tờ chứng minh thuộc diện cấp giấy MTT<br />
            </b>Supporting documents to prove the eligibility for a Visa Exemption Certificate<div
                id="divInfo12" class="HelpBox">
                Các loại giấy tờ theo qui định tại Qui chế miễn thị thực cho người Việt Nam định
                cư ở nước ngoài. Bạn phải điền vào ít nhất một loại giấy tờ
                </div>
                <strong>
                    <br />
                    <br />
                    Đối với người VN định cư ở nước ngoài</strong><br />
                For the Vietnamese residing abroad<br />
                <div>
                    <table id="dnn_ctr408_Desktop_subctr_cblDocuments1" border="0" style="width:690px;">
		<tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments1_0" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments1$0" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments1_0">Giấy tờ chứng nhận có quốc tịch Việt Nam<span class="spDosierItem">Documents certifying Vietnamese citizenship</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments1_1" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments1$1" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments1_1">Bản sao hoặc bản trích lục Quyết định cho trở lại quốc tịch Việt Nam<span class="spDosierItem">A copy or excerpt of the Decision for Recovery of Vietnamese citizenship</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments1_2" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments1$2" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments1_2">Bản sao hoặc bản trích lục Quyết định cho thôi quốc tịch Việt Nam<span class="spDosierItem">A copy or excerpt of the Decision for Renunciation of Vietnamese citizenship</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments1_3" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments1$3" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments1_3">Giấy xác nhận mất quốc tịch Việt Nam<span class="spDosierItem">Certificate of loss of Vietnamese citizenship</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments1_4" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments1$4" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments1_4">Hộ chiếu Việt Nam (còn hoặc đã hết giá trị)<span class="spDosierItem">A Vietnamese passport (valid or invalid)</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments1_5" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments1$5" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments1_5">Giấy chứng minh nhân dân (còn hoặc đã hết giá trị)<span class="spDosierItem">An Identity Card (valid or invalid)</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments1_6" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments1$6" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments1_6">Giấy khai sinh<span class="spDosierItem">A Birth Certificate</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments1_7" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments1$7" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments1_7">Thẻ cử tri mới nhất<span class="spDosierItem">The latest voter’s card</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments1_8" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments1$8" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments1_8">Sổ hộ khẩu<span class="spDosierItem">A Family Register Book</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments1_9" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments1$9" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments1_9">Sổ thông hành cấp trước 1975<span class="spDosierItem">A travel document issued before 1975</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments1_10" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments1$10" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments1_10">Thẻ căn cước cấp trước 1975<span class="spDosierItem">An Identity Card issued before 1975</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments1_11" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments1$11" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments1_11">Trích lục Bộ giấy khai sinh cấp trước 1975<span class="spDosierItem">An Excerpt from Birth Register issued before 1975</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments1_12" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments1$12" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments1_12">Giấy tờ do cơ quan có thẩm quyền của nước ngoài cấp nếu trong đó có ghi người được cấp giấy tờ đó có quốc tịch gốc hoặc gốc Việt Nam<span class="spDosierItem">Documents issued by competent foreign authorities if they can prove that the person in question has original Vietnamese citizenship or of Vietnamese origin.</span></label></td>
		</tr><tr>
			<td><span id="itmGiayToKhac1"><input id="dnn_ctr408_Desktop_subctr_cblDocuments1_13" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments1$13" onclick="CheckGiayToKhac(this,'divGiayToKhac1');" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments1_13">Giấy tờ khác<span class="spDosierItem">Other</span></label></span></td>
		</tr>
	</table>
                    <div style="padding-left: 20px;" name="divGiayToKhac1" class="divGiayToKhac">
                        
                        <input name="dnn$ctr408$Desktop$subctr$txtGiayToKhac1" type="text" id="dnn_ctr408_Desktop_subctr_txtGiayToKhac1" style="width: 400px" />
                    </div>
                </div>
                <br />
                <strong>Đối với người nước ngoài là vợ, chồng, con của người Việt Nam định cư ở nước
                    ngoài hoặc công dân Việt Nam</strong><br />
                For foreigners who are spouses, children of Vietnamese overseas or Vietnamese citizens
                <br />
                <div>
                    <table id="dnn_ctr408_Desktop_subctr_cblDocuments2" border="0" style="width:690px;">
		<tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments2_0" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments2$0" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments2_0">Giấy đăng ký kết hôn<span class="spDosierItem">Certificate of Marriage</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments2_1" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments2$1" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments2_1">Giấy khai sinh<span class="spDosierItem">Certificate of Birth </span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments2_2" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments2$2" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments2_2">Giấy xác nhận quan hệ cha, mẹ, con<span class="spDosierItem">A Certificate of relationship with farther, mother, children</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments2_3" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments2$3" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments2_3">Các giấy tờ khác có giá trị theo quy định của pháp luật Việt Nam<span class="spDosierItem">Other valid documents as regulated by the Vietnamese Law</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblDocuments2_4" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments2$4" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments2_4">Quyết định nuôi con nuôi<span class="spDosierItem">Decision of Adoption</span></label></td>
		</tr><tr>
			<td><span id="itmGiayToKhac2"><input id="dnn_ctr408_Desktop_subctr_cblDocuments2_5" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblDocuments2$5" onclick="CheckGiayToKhac(this,'divGiayToKhac2');" /><label for="dnn_ctr408_Desktop_subctr_cblDocuments2_5">Giấy tờ khác<span class="spDosierItem">Other</span></label></span></td>
		</tr>
	</table>
                    <div style="padding-left: 20px;" name="divGiayToKhac2" class="divGiayToKhac">
                        
                        <input name="dnn$ctr408$Desktop$subctr$txtGiayToKhac2" type="text" id="dnn_ctr408_Desktop_subctr_txtGiayToKhac2" style="width: 400px" />
                    </div>
                </div>
            </li>
        </ol>
        <div style="width: 900px; float: left;">
            <ol start="12">
                <li class="RegLI">
                    <div>
                        <div style="float: right; width: 30px; text-align: right">
                            <img id="dnn_ctr408_Desktop_subctr_imgInfo14" title="Trợ giúp / Help" onclick="ChangeHelpBoxDisplay(document.all.divInfo14);" onmouseover="ChangeInfoImage(this)" onmouseout="ChangeInfoImage(this)" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;cursor: hand" />
                        </div>
                        <b>Đối với trường hợp yêu cầu cấp lại Giấy MTT đã được cấp trước đây</b><br />
                        For cases of reissuance of visa exemption certificate<br />
                        Số GMTT <span class="spDosierItem">Serie</span>
                        <input name="dnn$ctr408$Desktop$subctr$txtOldNumber" type="text" maxlength="15" id="dnn_ctr408_Desktop_subctr_txtOldNumber" onkeypress="CheckInputChar(event, this, true, true, true)" style="width:125px;text-transform: uppercase" />
                        &nbsp; <span style="width: 80px">Ngày cấp</span><span class="spDosierItem">Date of issue</span>
                        <input name="dnn$ctr408$Desktop$subctr$txtOldDate$txtDateInput" type="text" id="dnn_ctr408_Desktop_subctr_txtOldDate_txtDateInput" title="dd/MM/yyyy" onblur="if(this.value != '__/__/____') {validateDate(this);}" style="width:85px;" />
<input type="hidden" name="dnn$ctr408$Desktop$subctr$txtOldDate$MaskedEditExtender1_ClientState" id="dnn_ctr408_Desktop_subctr_txtOldDate_MaskedEditExtender1_ClientState" />
                        &nbsp;
                        <br />
                        Lý do <span class="spDosierItem">Reason for reissuance</span>:<br />
                        <table id="dnn_ctr408_Desktop_subctr_cblOldReason" border="0" style="width:690px;">
		<tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblOldReason_0" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblOldReason$0" /><label for="dnn_ctr408_Desktop_subctr_cblOldReason_0">Bị mất<span class="spDosierItem">Lost</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblOldReason_1" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblOldReason$1" /><label for="dnn_ctr408_Desktop_subctr_cblOldReason_1">Bị hỏng<span class="spDosierItem">Damaged</span></label></td>
		</tr><tr>
			<td><input id="dnn_ctr408_Desktop_subctr_cblOldReason_2" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblOldReason$2" /><label for="dnn_ctr408_Desktop_subctr_cblOldReason_2">Hết hạn<span class="spDosierItem">Expired</span></label></td>
		</tr><tr>
			<td><span id="itmGiayToKhac3"><input id="dnn_ctr408_Desktop_subctr_cblOldReason_3" type="checkbox" name="dnn$ctr408$Desktop$subctr$cblOldReason$3" onclick="CheckGiayToKhac(this,'divGiayToKhac3');" /><label for="dnn_ctr408_Desktop_subctr_cblOldReason_3">Có nhu cầu điều chỉnh nội dung (ghi rõ nội dung cần điều chỉnh kèm khung miêu tả)<span class="spDosierItem">Admend content(s) in the visa exemption certificate (specify admendment details)</span></label></span></td>
		</tr>
	</table>
                        <div style="padding-left: 20px;" class="divGiayToKhac" name="divGiayToKhac3">
                            <input name="dnn$ctr408$Desktop$subctr$txtGiayToKhac3" type="text" id="dnn_ctr408_Desktop_subctr_txtGiayToKhac3" style="width: 400px" class="txtGiayToKhac" />
                        </div>
                        <b>Đề nghị cấp giấy miễn thị thực loại:</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        Quyển rời
                        <input id="dnn_ctr408_Desktop_subctr_chkPrintType_1" type="checkbox" name="dnn$ctr408$Desktop$subctr$chkPrintType_1" checked="checked" onclick="DoSelectPrintType(this,'1');" />
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        Giấy dán vào hộ chiếu
                        <input id="dnn_ctr408_Desktop_subctr_chkPrintType_2" type="checkbox" name="dnn$ctr408$Desktop$subctr$chkPrintType_2" onclick="DoSelectPrintType(this,'2');" /><br />
                        Requesting for a Visa Exemption Certificate in form of:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        A detached certificate&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        A stamped certificate
                    </div>
                    <div id="divInfo14" class="HelpBox">
                        Chọn mẫu giấy miễn thị thực
                    </div>
                </li>
            </ol>
        </div>
        <div style="padding-left: 30px;">
            <strong>
                <div style="float: right; width: 30px; text-align: right">
                    <img id="dnn_ctr408_Desktop_subctr_imgInfo13" title="Trợ giúp / Help" onclick="ChangeHelpBoxDisplay(document.all.divInfo13);" onmouseover="ChangeInfoImage(this)" onmouseout="ChangeInfoImage(this)" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;cursor: hand" />
                </div>
                Nộp hồ sơ xin cấp Giấy miễn thị thực tại</strong>&nbsp;<select name="dnn$ctr408$Desktop$subctr$ddlCountry" id="dnn_ctr408_Desktop_subctr_ddlCountry" style="width:170px;">
		<option value="--- Chọn nước ---">--- Chọn nước ---</option>
		<option value="DZA">Algeria</option>
		<option value="AGO">Angola</option>
		<option value="ARG">Argentina</option>
		<option value="AUS">Australia</option>
		<option value="AUT">Austria</option>
		<option value="BGD">Bangladesh</option>
		<option value="BLR">Belarus</option>
		<option value="BEL">Belgium</option>
		<option value="BRA">Brazil</option>
		<option value="BRN">Brunei Darussalam</option>
		<option value="BGR">Bulgaria</option>
		<option value="KHM">Cambodia</option>
		<option value="CAN">Canada</option>
		<option value="CHL">Chile</option>
		<option value="CHN">China</option>
		<option value="CUB">Cuba</option>
		<option value="CZE">Czech Republic</option>
		<option value="DNK">Denmark</option>
		<option value="EGY">Egypt</option>
		<option value="FIN">Finland</option>
		<option value="FRA">France</option>
		<option value="DEU">Germany</option>
		<option value="HKG">Hong Kong</option>
		<option value="HUN">Hungary</option>
		<option value="IND">India</option>
		<option value="IDN">Indonesia</option>
		<option value="IRN">Iran, Islamic Republic of</option>
		<option value="IRQ">Iraq</option>
		<option value="ITA">Italy</option>
		<option value="JPN">Japan</option>
		<option value="PRK">Korea, Democratic People's Republic of</option>
		<option value="KOR">Korea, Republic of</option>
		<option value="KWT">Kuwait</option>
		<option value="LAO">Lao People's Democratic Republic</option>
		<option value="LBY">Libyan Arab Jamahiriya</option>
		<option value="MYS">Malaysia</option>
		<option value="MEX">Mexico</option>
		<option value="MNG">Mongolia</option>
		<option value="MAR">Morocco</option>
		<option value="MMR">Myanmar</option>
		<option value="NLD">Netherlands</option>
		<option value="NZL">New Zealand</option>
		<option value="PAK">Pakistan</option>
		<option value="PAN">Panama</option>
		<option value="PHL">Philippines</option>
		<option value="POL">Poland</option>
		<option value="ROU">Romania</option>
		<option value="RUS">Russian Federation</option>
		<option value="SGP">Singapore</option>
		<option value="SVK">Slovakia</option>
		<option value="ZAF">South Africa</option>
		<option value="ESP">Spain</option>
		<option value="SWE">Sweden</option>
		<option value="CHE">Switzerland</option>
		<option value="TWN">Taiwan</option>
		<option value="TZA">Tanzania, United Republic of</option>
		<option value="THA">Thailand</option>
		<option value="TUR">Turkey</option>
		<option value="UKR">Ukraine</option>
		<option value="ARE">United Arab Emirates</option>
		<option value="GBR">United Kingdom</option>
		<option value="USA">United States</option>
		<option value="UZB">Uzbekistan</option>
		<option value="VEN">Venezuela</option>

	</select>
            &nbsp;<br />
            Submit dossiers to apply for Certificate of visa exemption in
            <div id="divInfo13" class="HelpBox">
                Chọn tên nước có Cơ quan Đại diện Việt Nam ở nước ngoài mà bạn muốn nộp hồ sơ
                <br />
                <span style="text-decoration: underline">Chú ý</span>: bạn có thể nộp hồ sơ tại
                bất kỳ Cơ quan đại diện nào bạn muốn. Tuy nhiên, kết quả sẽ được trả tại Cơ quan
                nộp
            </div>
            <br />
            <br />
            <input id="chkConfirm" type="checkbox" style="font-weight: bold; font-style: italic;" /><i>
                <label for="chkConfirm" style="font-style: normal">
                    <b>Tôi cam đoan nội dung khai trên là đúng và đầy đủ. </b>
                </label>
                <br />
                <label for="chkConfirm">
                    I declare that the information I have given on this form is correct and complete
                    to the best of my knowledge and belief.
                </label>
                <br />
                <br />
                <br />
            </i>
        </div>
        <p align="center">
            <input type="submit" name="dnn$ctr408$Desktop$subctr$cmdFinish" value="Hoàn thành" onclick="return CheckBeforeSaving();" id="dnn_ctr408_Desktop_subctr_cmdFinish" class="Button" style="width:100px;" />
            &nbsp;&nbsp; &nbsp;&nbsp;
            <input id="cmdReset" class="Button" style="width: 100px" type="reset" value="Nhập lại" /><br />
            Complete &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
            &nbsp;&nbsp; Reset
        </p>
        <div style="border-right: #fde9c6 1px solid; padding-right: 6px; border-top: #fde9c6 1px solid; padding-left: 6px; font-size: 11px; padding-bottom: 6px; border-left: #fde9c6 1px solid; padding-top: 6px; border-bottom: #fde9c6 1px solid; background-color: #fdfced">
            <u><b>Ghi chú/ Notes:</b></u><br />
            (1) Mỗi người khai 1 bản, kèm hộ chiếu/giấy tờ có giá trị đi lại quốc tế; nộp trực
            tiếp tại cơ quan đại diện Việt Nam ở nước ngoài hoặc tại Cục Quản lý xuất nhập cảnh<br />
            <i>Submit in person one completed application form enclosed with passport or International
                Travel Document at the Vietnamese Diplomatic Mission or at the Immigration Department</i><br />
            (2) Kèm 02 ảnh mới chụp, cỡ 4x6cm, phông nền trắng, mặt nhìn thẳng, đầu để trần,
            không đeo kính mầu (01 ảnh dán vào tờ khai, 01 ảnh để rời)<br />
            <i>Enclose 02 recently taken photos in 4x6cm size, with white background, front view,
                bare head without sunglasses (one photo on the form and the other separate)</i><br />
            (3) Kèm bản sao một trong các giấy tờ: Giấy khai sinh, Giấy đăng ký kết hôn, Hộ
            chiếu Việt Nam hết giá trị, Giấy chứng minh nhân dân, Sổ hộ khẩu hoặc các giấy tờ
            khác theo quy định của pháp luật Việt Nam<br />
            <i>Enclose one certified copy of one of the following documents: Birth Certificate,
                Marriage Certificate, expired Vietnamese Passport, ID Card, Family Registration
                Booklet or other documents as stipulated by the Vietnamese Law</i>
        </div>
        <br />
    </div>
</div>
<div>
    
</div>


<script type="text/javascript">
    DoChangeFullName();
    hidelp = true;
</script>

<img id="dnn_ctr408_Desktop_subctr_imgS" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/information.gif" style="border-width:0px;display: none; visibility: hidden" />
<img id="dnn_ctr408_Desktop_subctr_imgSGray" src="../../../../../../DesktopModules/VE4N/VE4N/Styles/Default/Images/informationGray.gif" style="border-width:0px;display: none; visibility: hidden" />

<!-- End_Module_408 -->
</div></span>
</div>
                </td>
                
                <td style="" valign="top">
                    <div style="margin: 20px 10px 20px 10px">
                        <div id="dnn_leftpane" style="height: 100%" class="LeftFrame">
                        <a name="423"></a><span id="dnn_ctr423_ContentPane" align="left"><!-- Start_Module_423 --><div id="dnn_ctr423_ModuleContent">
	
<table id="dnn_ctr423_Announcements_Announcements_lstAnnouncements" class="DNN_ANN_DesignTable" cellspacing="0" Summary="Announcements Design Table" border="0" style="border-collapse:collapse;">
	<tr>
		<td>
		
		<p><a href="/Hướngdẫn/Cơsởph&aacute;pl&yacute;/tabid/112/Default.aspx"><img style="max-width:177px; width:100%" src="/Portals/0/cspl.png" alt="" /></a></p>
<p><a href="/Th&ocirc;ngtinvềmiễnthịthực/tabid/142/Default.aspx"><img style="max-width:177px; width:100%" src="/Portals/0/huongdan.png" alt="" /></a></p>
<p><a href="/Đăngk&yacute;/Khaitrựctuyến/tabid/104/Default.aspx"><img style="max-width:177px; width:100%" src="/Portals/0/khaitructuyen.png" alt="" /></a></p>
<p><a href="/Đăngk&yacute;/Inlạitờkhai/tabid/105/Default.aspx"><img style="max-width:177px; width:100%" src="/Portals/0/inlaitokhai.png" alt="" /></a></p>
	</td>
	</tr>
</table>
<!-- End_Module_423 -->
</div></span>
</div>
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="2" bgcolor="#F0F0F0" align="center" class="VE4NFooter" style="color: white;
                    font-size: 11px;">
                    Copyright © 2015 mtt.mofa.gov.vn. All rights reserved
                    <div style="width: 10; height: 3px; overflow: hidden;">
                        <a id="dnn_dnnLogin_cmdLogin" class="SkinObject" href="javascript:__doPostBack('dnn$dnnLogin$cmdLogin','')">Login</a>
                    </div>
                </td>
            </tr>
        </table>
        </div>
    </div>
</div>

<script>
	if(hidelp)
		document.all.dnn_leftpane.style.display = "none";
</script>


        <input name="ScrollTop" type="hidden" id="ScrollTop" />
        <input name="__dnnVariable" type="hidden" id="__dnnVariable" />
    

<script type="text/javascript">
//<![CDATA[
Sys.Application.initialize();
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.MaskedEditBehavior, {"ClientStateFieldID":"dnn_ctr408_Desktop_subctr_diPassportExpireDate_MaskedEditExtender1_ClientState","CultureAMPMPlaceholder":"AM;PM","CultureCurrencySymbolPlaceholder":"$","CultureDateFormat":"MDY","CultureDatePlaceholder":"/","CultureDecimalPlaceholder":".","CultureThousandsPlaceholder":",","CultureTimePlaceholder":":","Mask":"99/99/9999","MaskType":1,"id":"dnn_ctr408_Desktop_subctr_diPassportExpireDate_MaskedEditExtender1"}, null, null, $get("dnn_ctr408_Desktop_subctr_diPassportExpireDate_txtDateInput"));
});
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.MaskedEditBehavior, {"ClientStateFieldID":"dnn_ctr408_Desktop_subctr_diPRDExpireDate_MaskedEditExtender1_ClientState","CultureAMPMPlaceholder":"AM;PM","CultureCurrencySymbolPlaceholder":"$","CultureDateFormat":"MDY","CultureDatePlaceholder":"/","CultureDecimalPlaceholder":".","CultureThousandsPlaceholder":",","CultureTimePlaceholder":":","Mask":"99/99/9999","MaskType":1,"id":"dnn_ctr408_Desktop_subctr_diPRDExpireDate_MaskedEditExtender1"}, null, null, $get("dnn_ctr408_Desktop_subctr_diPRDExpireDate_txtDateInput"));
});
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.MaskedEditBehavior, {"ClientStateFieldID":"dnn_ctr408_Desktop_subctr_diChild1DateOfBirth_MaskedEditExtender1_ClientState","CultureAMPMPlaceholder":"AM;PM","CultureCurrencySymbolPlaceholder":"$","CultureDateFormat":"MDY","CultureDatePlaceholder":"/","CultureDecimalPlaceholder":".","CultureThousandsPlaceholder":",","CultureTimePlaceholder":":","Mask":"99/99/9999","MaskType":1,"id":"dnn_ctr408_Desktop_subctr_diChild1DateOfBirth_MaskedEditExtender1"}, null, null, $get("dnn_ctr408_Desktop_subctr_diChild1DateOfBirth_txtDateInput"));
});
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.MaskedEditBehavior, {"ClientStateFieldID":"dnn_ctr408_Desktop_subctr_diChild2DateOfBirth_MaskedEditExtender1_ClientState","CultureAMPMPlaceholder":"AM;PM","CultureCurrencySymbolPlaceholder":"$","CultureDateFormat":"MDY","CultureDatePlaceholder":"/","CultureDecimalPlaceholder":".","CultureThousandsPlaceholder":",","CultureTimePlaceholder":":","Mask":"99/99/9999","MaskType":1,"id":"dnn_ctr408_Desktop_subctr_diChild2DateOfBirth_MaskedEditExtender1"}, null, null, $get("dnn_ctr408_Desktop_subctr_diChild2DateOfBirth_txtDateInput"));
});
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.MaskedEditBehavior, {"ClientStateFieldID":"dnn_ctr408_Desktop_subctr_diChild3DateOfBirth_MaskedEditExtender1_ClientState","CultureAMPMPlaceholder":"AM;PM","CultureCurrencySymbolPlaceholder":"$","CultureDateFormat":"MDY","CultureDatePlaceholder":"/","CultureDecimalPlaceholder":".","CultureThousandsPlaceholder":",","CultureTimePlaceholder":":","Mask":"99/99/9999","MaskType":1,"id":"dnn_ctr408_Desktop_subctr_diChild3DateOfBirth_MaskedEditExtender1"}, null, null, $get("dnn_ctr408_Desktop_subctr_diChild3DateOfBirth_txtDateInput"));
});
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.MaskedEditBehavior, {"ClientStateFieldID":"dnn_ctr408_Desktop_subctr_txtOldDate_MaskedEditExtender1_ClientState","CultureAMPMPlaceholder":"AM;PM","CultureCurrencySymbolPlaceholder":"$","CultureDateFormat":"MDY","CultureDatePlaceholder":"/","CultureDecimalPlaceholder":".","CultureThousandsPlaceholder":",","CultureTimePlaceholder":":","Mask":"99/99/9999","MaskType":1,"id":"dnn_ctr408_Desktop_subctr_txtOldDate_MaskedEditExtender1"}, null, null, $get("dnn_ctr408_Desktop_subctr_txtOldDate_txtDateInput"));
});
//]]>
</script>
<input type="hidden" name="__VIEWSTATE" id="
__VIEWSTATE" value="" /></form> 
</body>
</html>
