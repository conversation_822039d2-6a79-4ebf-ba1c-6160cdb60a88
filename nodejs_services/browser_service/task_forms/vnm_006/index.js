const moment = require('moment')
const fs = require('fs')
const data_mapping = require('../../shared/data_mapping')
const { get_order_detail, get_order_task, capitalize, fill_pdf_form, fill_docx_form, get_country_name, get_country_name_v2, get_order_task_pod_data } = require('../../shared/helpers')


const create_form = async (data) => {
    [
        'personal_permanent_address_country',
        'passport_core_info_nationality',
        'family_child_home_town',
        'passport_core_info_nationality',
        'family_father_nationality',
        'family_mother_nationality',
    ].forEach(key => {
        if (data[key]) data[key] = get_country_name_v2(data[key])
    })

    const ETHNIC = "KINH"
    data['passport_core_info_name'] = `${data.passport_core_info_surname} ${data.passport_core_info_given_name}`
    data['passport_core_info_gender'] = data_mapping.gender_vnm[data.passport_core_info_gender]
    data['passport_core_info_date_of_birth'] = moment(data.passport_core_info_date_of_birth).format('DD/MM/YYYY')
    data['passport_core_info_place_of_birth'] = `${data.passport_core_info_state_of_birth}, ${data.passport_core_info_country_of_birth}`
    data["personal_core_info_ethnic"] = ETHNIC
    data['personal_permanent_address'] = capitalize(`${data.personal_permanent_address_address}, ${data.personal_permanent_address_city} ${data.personal_permanent_address_zip_code}, ${data.personal_permanent_address_country}`)

    if (typeof data.family_father_surname != 'undefined') {
        data['family_father_name'] = `${data.family_father_surname} ${data.family_father_given_name}`.toUpperCase()
        data['family_father_date_of_birth_year'] = moment(data.family_father_date_of_birth).format('YYYY')
        data['family_child_home_town'] = data.family_father_family_origin
        data['family_father_home_town'] = data.family_father_family_origin
        data['family_father_ethnic'] = data.family_father_nationality == "VIET NAM" ? ETHNIC : ''
    }

    if (typeof data.family_mother_surname != 'undefined') {
        data['family_mother_name'] = `${data.family_mother_surname} ${data.family_mother_given_name}`.toUpperCase()
        data['family_mother_date_of_birth_year'] = moment(data.family_mother_date_of_birth).format('YYYY')
        data['family_mother_home_town'] = data.family_mother_family_origin
        data['family_mother_ethnic'] = data.family_mother_nationality == "VIET NAM" ? ETHNIC : ''
    }

    data["applicant_name"] = data.family_father_name
    data["applicant_permanent_address"] = data.personal_permanent_address
    data["relationship"] = "Cha"

    data.filename = `Application_Form_${data.passport_core_info_surname}_${data.passport_core_info_given_name}_VNM_006.pdf`
    const pdf_file = await fill_docx_form(__dirname + '/vnm_006.docx', data)

    const result = {
        success: true,
        form_file: pdf_file,
        form_callback: {},
    }

    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const result = await create_form(pod_data)
        return result
    } catch (error) {
        console.log(error)
    }
}
// main(4063, 4742)
module.exports = { main, create_form }