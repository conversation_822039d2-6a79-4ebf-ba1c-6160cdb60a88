const _ = require('lodash');

const axiosS = require("axios");
const FormData = require('form-data');
const moment = require('moment')
const { FLIGHT, HOTELS, CITY } = require('./data_mapping')
const helper = require('../../shared/helpers');
const { s3_url_to_bucket_key } = require('../../shared/helpers');
const { get_image_captcha_text } = require('../../shared/captcha');
const { random_address } = require('../../shared/fake');
const { faker } = require('@faker-js/faker');
const fakeAddress = require("fake-address-generator");
const https = require('https');
const { airport_reverse } = require('../../shared/data_mapping');
const axios = axiosS.create({
    httpsAgent: new https.Agent({
        rejectUnauthorized: false
    })
});

const solve_captcha = async () => {
    const resp = await axios.get('https://api.evisa.gov.vn/authorization-service/captcha/generate')
    const base64Data = resp.data.data.image.replace(/^data:image\/\w+;base64,/, "");
    const buffer = Buffer.from(base64Data, 'base64');
    const captcha = await get_image_captcha_text(buffer)
    if (captcha?.length < 5) {
        return solve_captcha()
    }
    return {
        id: resp.data.data.id,
        captcha,
    }
}

let RETRY = 0
const create_form = async (order, data) => {
    let ad_email = ''
    if (["VNM eVisa Urgent New", "VNM eVisa Urgent New Boarding",
        "VNM eVisa Urgent Expedite", "VNM eVisa Urgent Expedite Boarding",
        "VNM eVisa Urgent Fix", "VNM eVisa Urgent Fix Boarding"].includes(order.service.name)) {
        ad_email = process.env.ad_env === 'prod' ? '<EMAIL>' : '<EMAIL>'
        data.travel_enter_flight_enter_airport = 'Noi Bai International Airport'
    } else {
        ad_email = process.env.ad_env === 'prod' ? '<EMAIL>' : '<EMAIL>'
    }

    const DATE_FORMAT = "DD/MM/YYYY"
    const passport_photo_url = data.photo_passport_photo_copy_of_photo.replace('-demo', '')
    const passport_main_page_url = data.document_copy_of_passport_copy_of_passport_main_page ?? data.document_copy_of_passport_copy_of_passport_main_page

    const { bucket: passport_photo_bucket, key: passport_photo_key } = s3_url_to_bucket_key(passport_photo_url)
    const { bucket: passport_main_page_bucket, key: passport_main_page_key } = s3_url_to_bucket_key(passport_main_page_url)

    const photo_buff = await helper.s3_download_to_buff(passport_photo_bucket, passport_photo_key);
    const passport_buff = await helper.s3_download_to_buff(passport_main_page_bucket, passport_main_page_key);

    let form_data = new FormData();
    form_data.append('file', passport_buff, {
        filename: 'passport_photo.jpg',
        contentType: 'image/jpeg'
    });
    const step_upload_passport = await axios({
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://api.evisa.gov.vn/client-service/public/upload',
        headers: {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7',
            'Connection': 'keep-alive',
            'Origin': 'https://evisa.gov.vn',
            'Referer': 'https://evisa.gov.vn/',
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36',
            ...form_data.getHeaders()
        },
        data: form_data
    })

    form_data = new FormData();
    form_data.append('file', photo_buff, {
        filename: 'photo.jpg',
        contentType: 'image/jpeg'
    });

    const step_upload_photo = await axios({
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://api.evisa.gov.vn/client-service/public/upload',
        headers: {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7',
            'Connection': 'keep-alive',
            'Origin': 'https://evisa.gov.vn',
            'Referer': 'https://evisa.gov.vn/',
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36',
            ...form_data.getHeaders()
        },
        data: form_data
    })

    // form_data = new FormData();
    // form_data.append('visa', passport_buff, {
    //     filename: 'passport_photo.jpg',
    //     contentType: 'image/jpeg'
    // });
    // form_data.append('portrait', photo_buff, {
    //     filename: 'passport_main_page.jpg',
    //     contentType: 'image/jpeg'
    // });
    // const step_compare = await axios({
    //     method: 'post',
    //     maxBodyLength: Infinity,
    //     url: 'https://api.evisa.gov.vn/ekyc/process-evisa',
    //     headers: {
    //         'Accept': 'application/json, text/plain, */*',
    //         'Accept-Language': 'en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7',
    //         'Connection': 'keep-alive',
    //         'Origin': 'https://evisa.gov.vn',
    //         'Referer': 'https://evisa.gov.vn/',
    //         'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36',
    //         ...form_data.getHeaders()
    //     },
    //     data: form_data
    // })

    if (data.passport_core_info_nationality == "DEU")
        data.passport_core_info_nationality = "D"
    if (data.passport_core_info_birth_nationality == "DEU")
        data.passport_core_info_birth_nationality = "D"

    const airport_code = airport_reverse[data.travel_enter_flight_enter_airport]
    const hotels = HOTELS[airport_code]
    const random_hotel = hotels[Math.floor(Math.random() * hotels.length)]

    const entry_date = moment(data.service_core_info_entry_date);
    let exit_date = moment(data.service_core_info_exit_date);

    console.log(entry_date.toISOString(), exit_date.toISOString())

    // let validity_days = exit_date.diff(entry_date, 'days') + 1
    // // Buffer validity days
    // validity_days = Math.ceil(validity_days / 30) * 30
    // let validity_days = 90
    let validity_days = order.service.attributes.validity === '1M' ? 29 : 89

    exit_date = entry_date.clone().add(validity_days, 'days')
    const first_address = await new Promise(r => fakeAddress.Generate({
        country: "us",
        sex: "male",
    }, (err, resp) => {
        r(resp);
    }));
    const second_address = await new Promise(r => fakeAddress.Generate({
        country: "us",
        sex: "male",
        city: first_address.address.city,
        state: first_address.address.state
    }, (err, resp) => {
        r(resp);
    }));
    const map_data = {
        "maLhs": "EVISA",
        "type": "EVISA",
        "anhMat": step_upload_photo.data.data.url,
        "anhHoChieu": step_upload_passport.data.data.url,
        "ttcnHo": (['LNU', 'FNU'].includes(data.passport_core_info_surname) ? '' : data.passport_core_info_surname).toUpperCase(),
        "ttcnDemVaTen": data.passport_core_info_given_name.toUpperCase(),
        "ttcnGioiTinh": data.passport_core_info_gender,
        "ttcnNgayThangNamSinhStr": moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT),
        "ttcnLoaiNgayThangNamSinh": "D",
        "xdNtnsinh": true,
        "ttcnMaQt": data.passport_core_info_nationality,
        "ttcnNoiSinh": helper.get_country_name_v2(data.passport_core_info_country_of_birth ?? data.passport_core_info_nationality),
        "ttcnCccd": "",
        "ttcnTonGiao": "N/A",
        "ttcnConfirmEmail": ad_email,
        "hcKhac": [],
        "qtKhac": [],
        "viPham": [],
        "nddnTtDeNghi": (
            order.service.attributes.number_of_entries === 'multiple_entries' ||
            data.visa_info_core_info_number_of_entries === 'multiple_entries' ||
            data.travel_enter_flight_number_of_entries === 'multiple_entries' ||
            data.service_core_info_number_of_entries === 'multiple_entries'
        ) ? 1 : 0,
        "nddnTtdtTuNgayStr": entry_date.format(DATE_FORMAT),
        "hcConGiaTriKhac": [],
        "hcLoai": "PT",
        "hcGhiCuThe": "",
        "hcSo": data.passport_core_info_passport_number,
        "hcNoiCap": helper.get_country_name_v2(data.passport_core_info_nationality),
        "hcNgayCapStr": moment(data.passport_core_info_issue_date).format(DATE_FORMAT),
        "hcGiaTriDenStr": moment(data.passport_core_info_expiration_date).format(DATE_FORMAT),
        "otherValidPassport": false,
        "ttllDcLienHe": `${first_address.address.street}, ${first_address.address.city}, ${first_address.address.state} ${first_address.address.zip}, United States`,
        "ttllDcThuongTru": `${first_address.address.street}, ${first_address.address.city}, ${first_address.address.state} ${first_address.address.zip}, United States`,
        "ttllSdt": faker.phone.number({ style: 'national' }),
        "ttcnEmail": ad_email,
        "ttllLlSdt": faker.phone.number({ style: 'national' }),
        "ttllLlHoTen": faker.person.firstName('male') + " " + data.passport_core_info_surname,
        "ttllLlNoiOHienTai": `${second_address.address.street}, ${second_address.address.city}, ${second_address.address.state} ${second_address.address.zip}, United States`,
        "ttllLlQuanHe": "brother",
        "nnNgheNghiepHienTai": "",
        "nnTenCtyCq": "",
        "nnChucVu": "",
        "nnDiaChi": "",
        "nnSdt": "",
        "ttcdMucDich": data.travel_visa_info_purpose === 'business' ? "38" : "76", //38: business 76: tourism
        "contactInVN": false,
        "ttcdSoNgayTamTru": moment(exit_date).diff(entry_date, 'days') + 1,
        "ttcdThoiGianNcStr": moment(data.service_core_info_entry_date).format(DATE_FORMAT),
        "ttcdNcCuaKhau": random_hotel.code,
        "ttcdXcCuaKhau": random_hotel.code,
        "ttcdDcTamTru": random_hotel.name,
        "ttcdTinhTp": random_hotel.codes[0],
        "ttcdQuanHuyen": random_hotel.codes[1],
        "ttcdPhuongXa": random_hotel.codes[2],
        "ttcdSdt": "",
        "beenToVNLastYear": false,
        "tungDenVn": [],
        "relatives": false,
        "thannhanOVn": [],
        "treEm": [],
        "kpbhCongTyTen": "",
        "kpbhCongTyDiaChi": "",
        "kpbhCongTySdt": "",
        "tuDamBaoKP": true,
        "kpbhGhiCuThe": "",
        "toChucDNCapTT": {},
        "comment": {},
        "taoTkEmail": 0,
        "camDoan": 1,
        "ttcdCqTcCamDoan": 1,
        "coThanNhanVn": 0,
        "daTungDenVn1Nam": 0,
        "hcGiaTriSuDungKhac": 0,
        "nhieuQuocTich": 0,
        "tungSuDungHcKhac": 0,
        "viPhamPl": 0,
        "ttcdCqTcLienHe": 0,
        "lechMuiGio": -7,
        // "anhSoSanh1": step_compare.data.portrait_face_base64,
        // "anhSoSanh2": step_compare.data.card_face_base64,
        // "anhCropHc": step_upload_passport.data.data.url,
        // "tiLeKhop": String(step_compare.data.face_matching),
        "anhSoSanh1": "",
        "anhSoSanh2": "",
        "anhCropHc": "",
        "tiLeKhop": "",
        "cqTcLienHe": [],
        "ocrThayDoi": {
            "ttcnHo": false,
            "ttcnDemVaTen": false,
            "ttcnMaQt": false,
            "ttcnNgayThangNamSinhStr": false,
            "hcGiaTriDenStr": true,
            "hcSo": false,
            "ttcnGioiTinh": false
        },
        "captcha": "400837",
        "captcha-id": "VEA1FA70dzVTZMzOsxEf",
        "fileDinhKem": [],
        "nddnTtdtDenNgayStr": exit_date.format(DATE_FORMAT)
    }
    console.log(JSON.stringify(map_data, null, 2))


    const captcha = await solve_captcha()
    console.log(captcha.captcha)
    map_data.captcha = captcha.captcha
    map_data["captcha-id"] = captcha.id
    const step_2 = await axios({
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://api.evisa.gov.vn/client-service/public/hstt',
        headers: {
            'Accept': '*/*',
            'Accept-Language': 'en',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Origin': 'https://evisa.gov.vn',
            'Referer': 'https://evisa.gov.vn/',
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36',
        },
        data: map_data
    })
    console.log(JSON.stringify(step_2.data.data, null, 2))
    console.log(step_2.data)
    if (step_2.data.message === 'response.captcha_invalid') {
        return create_form(order, data)
    }
    if (!step_2.data.data?.maSoHoso) {
        RETRY++
        if (RETRY > 3) {
            return {
                success: false,
                form_file: "",
                form_callback: {}
            }
        }
        return create_form(order, data)
    }

    const buckets = JSON.parse(process.env.ad_s3);
    const file_resp = await axios.get(`https://api.evisa.gov.vn/client-service/public/hstt/in-ho-so-thi-thuc?maHoSo=${step_2.data.data.maSoHoso}&email=${ad_email}`, { responseType: 'arraybuffer' })
    const file_url = await helper.s3_upload_buff(buckets.ariadirect_prod_applications, `tasks/EVISA_${step_2.data.data.maSoHoso}.pdf`, file_resp.data)

    return {
        success: true,
        form_file: file_url,
        form_callback: {
            tracking_url: `https://evisa.gov.vn/e-visa/search?maHoSo=${step_2.data.data.maSoHoso}&email=${ad_email}&loai=0`,
            email: ad_email,
            dob: moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT),
            document_id: step_2.data.data.maSoHoso,
        }
    }

}

const main = async (order_id, task_id, test = false) => {
    try {
        const { order, task, pod_data } = await helper.get_order_task_pod_data(order_id, task_id)
        RETRY = 0
        const data = await create_form(order, pod_data)
        return data
    } catch (error) {
        console.log(error)
    }

}

module.exports = { main, create_form }
