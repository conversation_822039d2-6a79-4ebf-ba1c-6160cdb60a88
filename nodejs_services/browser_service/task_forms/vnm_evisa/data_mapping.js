const COUNTRY = {
    "AND": "Andorra",
    "ARG": "Argentina",
    "ARM": "Armenia",
    "AUS": "Australia",
    "AUT": "Austria",
    "AZE": "Azerbaijan",
    "BLR": "Belarus",
    "BEL": "Belgium",
    "BIH": "Bosnia and Herzegovina",
    "BRA": "Brazil",
    "BRN": "Brunei",
    "BGR": "Bulgaria",
    "CAN": "Canada",
    "CHL": "Chile",
    "CHN": "China",
    "COL": "Colombia",
    "HRV": "Croatia",
    "CUB": "Cuba",
    "CYP": "Cyprus",
    "CZE": "Czech Republic",
    "DNK": "Denmark",
    "EST": "Estonia",
    "FJI": "Fiji",
    "FIN": "Finland",
    "FRA": "France",
    "GEO": "Georgia",
    "D": "Germany",
    "GRC": "Greece",
    "HUN": "Hungary",
    "ISL": "Iceland",
    "IND": "India",
    "IRL": "Ireland",
    "ITA": "Italy",
    "JPN": "Japan",
    "KAZ": "Kazakhstan",
    "KOR": "Korea (South)",
    "LVA": "Latvia",
    "LIE": "Liechtenstein",
    "LTU": "Lithuania",
    "LUX": "Luxembourg",
    "MKD": "Macedonia",
    "MLT": "Malta",
    "MHL": "Marshall Islands",
    "MEX": "Mexico",
    "FSM": "Micronesia",
    "MDA": "Moldova",
    "MCO": "Monaco",
    "MNG": "Mongolia",
    "MNE": "Montenegro",
    "MMR": "Myanmar",
    "NRU": "Nauru",
    "NLD": "Netherlands",
    "NZL": "New Zealand",
    "NOR": "Norway",
    "PLW": "Palau",
    "PAN": "Panama",
    "PNG": "Papua New Guinea",
    "PER": "Peru",
    "PHL": "Philippines",
    "POL": "Poland",
    "PRT": "Portugal",
    "QAT": "Qatar",
    "ROU": "Romania",
    "RUS": "Russia",
    "SMR": "San Marino",
    "SRB": "Serbia",
    "SVK": "Slovakia",
    "SVN": "Slovenia",
    "SLB": "Solomon Islands",
    "ESP": "Spain",
    "SWE": "Sweden",
    "CHE": "Switzerland",
    "TLS": "Timor Leste",
    "ARE": "United Arab Emirates",
    "GBR": "United Kingdom of Great Britain and Northern Ireland",
    "USA": "United States of America",
    "URY": "Uruguay",
    "VUT": "Vanuatu",
    "VEN": "Venezuela",
    "WSM": "Western Samoa"
}

const VALIDITY = {
    "1M": "30",
    "2M": "60",
}


const FLIGHT = {
    "Cat Bi International Airport": "SCB",
    "Cam Ranh International Airport": "SCR",
    "Can Tho International Airport": "SCT",
    "Da Nang International Airport": "SDN",
    "Noi Bai International Airport": "SNB",
    "Phu Bai International Airport": "SPB",
    "Phu Quoc International Airport": "SPQ",
    "Tan Son Nhat International Airport": "STS",
    "Cam Pha Seaport": "CCP",
    "Chan May Seaport": "CCM",
    "Da Nang Seaport": "CDN",
    "Dung Quat Seaport": "CDQ",
    "Duong Dong Seaport": "CDO",
    "Hai Phong Seaport": "CHP",
    "Ho Chi Minh City Seaport": "CSG",
    "Hon Gai Seaport": "CHG",
    "Nghi Son Seaport": "CNS",
    "Nha Trang Seaport": "CNT",
    "Quy Nhon Seaport": "CQN",
    "Vung Ang Seaport": "CVA",
    "Vung Tau Seaport": "CVT",
};


const HOTELS = {
    "SGN": [
        {
            code: 'STS',
            name: "66 Nguyen Du Street, Ben Nghe Ward, District 1, HCM City",
            codes: ["701", "760", "26740"]
        },
        {
            code: 'STS',
            name: "22 Truong Son Street, Ward 2, Tan Binh District, HCM City",
            codes: ["701", "766", "26965"]
        },
        {
            code: 'STS',
            name: "200 Hoang Van Thu Street, Ward 9, Phu Nhuan District, HCM City",
            codes: ["701", "768", "27049"]
        }
    ],
    "HAN": [
        {
            code: 'SNB',
            name: "K5 Nghi Tam, 11 Xuan Dieu Road, Tay Ho, Hanoi",
            codes: ["101", "3", "94"]
        },
        {
            code: 'SNB',
            name: "40, P. Cat Linh, Phuong Giang Vo, Ha Noi",
            codes: ["101", "1", "31"]
        },
        {
            code: 'SNB',
            name: "1 Thanh Nien, Road, Ba Dinh, Hanoi",
            codes: ["101", "1", "31"]
        }
    ],
    "PQC": [
        {
            code: 'SPQ',
            name: "Ong Lang, Phu Quoc, Kien Giang",
            codes: ["813", "911", "31078"]
        },
        {
            code: 'SPQ',
            name: "Bai Dai Area, Ganh Dau, Phu Quoc, Kien Giang",
            codes: ["813", "911", "31087"]
        },
        {
            code: 'SPQ',
            name: "Khem Beach, An Thoi, Phu Quoc, Kien Giang",
            codes: ["813", "911", "31081"]
        }
    ],
    "DAD": [
        {
            code: 'SDN',
            name: "322- 324, Hoang Dieu Street, BINH HIEN Ward, HAI CHAU District, Da Nang",
            codes: ["501", "492", "20251"]
        },
        {
            code: 'SDN',
            name: "Gold Hotel Da Nang, 24 Nui Thanh Street, BINH THUAN Ward, HAI CHAU District, Da Nang",
            codes: ["501", "492", "20254"]
        },
        {
            code: 'SDN',
            name: "Grand Mercure Danang, Zone Of The Villas Of Green Island, Lot A1, HAI CHAU District, Da Nang",
            codes: ["501", "492", "20257"]
        }
    ],
}

module.exports = { COUNTRY, VALIDITY, FLIGHT, HOTELS }