const axios = require('axios');
const FormData = require('form-data');
const { https } = require('follow-redirects');
const jimp = require('jimp');

const phoneData = require('../../shared/country_phone_code.json')

exports.sleep = async (milliseconds) => await new Promise(resolve => setTimeout(resolve, milliseconds))

exports.upload_file = async (url, cookie) => {
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    const image = await jimp.read(response.data);
    const width = image.bitmap.width;
    const height = image.bitmap.height;
    var data = new FormData();
    // Create a Blob from the image data
    // const imageBlob = new Blob([response.data], { type: 'image/jpeg' });
    data.append('UpFile', response.data, { filename: 'image.jpg', contentType: 'image/jpeg', knownLength: response.data.length });
    data.append('ShearPhotoIW', width);
    data.append('ShearPhotoIH', height);
    data.append('ShearPhotoFW', width);
    data.append('ShearPhotoFH', height);
    data.append('ShearPhotoP', 0.75);
    data.append('shearphoto', 1);
    data.append('lang', "en_US");
    const resp = await axios({
        method: 'post',
        url: 'https://cova.mfa.gov.cn/qztbphoto3/shearphoto_common/php/shearphoto.php',
        data: data,
        headers: {
            'host': 'cova.mfa.gov.cn',
            'cookie': cookie,
            'origin': 'https://cova.mfa.gov.cn',
            'referer': 'https://cova.mfa.gov.cn/qztbphoto3/?m&locale=en_US&checkArguments=;domain=cova.mfa.gov.cn;path=/;',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            ...data.getHeaders()
        }
    })
    const imgUrl = resp.data[0].ImgUrl
    const resp2 = await axios({
        method: 'post',
        url: 'https://cova.mfa.gov.cn/qzPhotoController.do?photocheck',
        data: `path=${imgUrl}&type=11&photoType=`,
        headers: {
            'host': 'cova.mfa.gov.cn',
            'cookie': cookie,
            'origin': 'https://cova.mfa.gov.cn',
            'referer': 'https://cova.mfa.gov.cn/qzCoCommonController.do?show&pageId=278VnrHVmriVYVYV8VKVSVlrHrkVYVnVSVcVcVmrjVSVKV8VaVaVSVYVPVaVPrjVcVYVKriVmrjVKVYVa&DataSource=2&locale=en_US',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',

        }
    })
    return resp2.data.photoPath
}
exports.get_form = async (cookie) => {
    let availableFileResult = null;
    let retryCount = 0;
    const maxRetries = 30; // Maximum number of retries (30s total)
    console.log("Getting Form...")
    // Wait for the form to become available
    while (retryCount < maxRetries && (!availableFileResult || availableFileResult.result !== "true")) {
        try {
            const response = await axios({
                method: 'post',
                url: 'https://cova.mfa.gov.cn/visaFormToPDFController.do?isExistsFile',
                data: `type=1`,
                headers: {
                    'host': 'cova.mfa.gov.cn',
                    'cookie': cookie,
                    'origin': 'https://cova.mfa.gov.cn',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                }

            });

            availableFileResult = response.data;
            if (availableFileResult.result !== "true") {
                console.log('Form not available yet. Retrying...');
                await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 second before retrying
            }
        } catch (error) {
            console.error('Error checking file availability:', error);
            break;
        }
        retryCount++;
    }
    if (availableFileResult && availableFileResult.result === "true") {
        console.log("Form Available, Wait for 15s...")
        //wait for 5s before downloading
        await this.sleep(15000)
        console.log("Downloading Form...")
        // Download the PDF once the file is available
        const resp = await axios({
            method: 'get',
            url: 'https://cova.mfa.gov.cn/visaFormToPDFController.do?downFullPdfAndSimplePDF',
            headers: {
                'host': 'cova.mfa.gov.cn',
                'cookie': cookie,
                'origin': 'https://cova.mfa.gov.cn',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            },
            responseType: 'arraybuffer'
        });
        console.log("Downloaded Form...")
        return resp.data;
    } else {
        console.log("Form did not become available within the retry limit.")
        return null
    }
}
exports.findPrefixOfPhone = (countryCode, phoneValue) => {
    const countryData = phoneData.find(element => element.iso_alpha_3 === countryCode); // Find the country data

    if (!countryData) {
        console.warn(`Country code not found: ${countryCode}`);
        return [null, phoneValue];
    }

    const prefix = countryData.dial_code;

    // Check if the phone value includes the dial code (with the "+" sign)
    if (phoneValue.startsWith(`+${prefix}`)) {
        return [`+${prefix}`, phoneValue.slice(prefix.length + 1)];
    } else {
        return [null, phoneValue];
    }
};