const fs = require('fs');
const moment = require('moment');
const axios = require('axios');
const puppeteer = require('puppeteer');
const helper = require('./helper');
const { PuppeteerScreenRecorder } = require('puppeteer-screen-recorder');
const { get_country_name_v2, get_country_name, s3_upload_buff, get_order_task_pod_data } = require('../../shared/helpers');
const data_mapping = require('../../shared/data_mapping')
const { get_image_captcha_text } = require('../../shared/captcha');
let DEBUG = false
const { RESIDENT_STATUS, RELATIONSHIP } = require('./data_mapping')
const RECODER_NAME = `Recorder_${moment().unix()}.mp4`

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const convert_state = (state) => data_mapping.us_state[state] ? data_mapping.us_state[state] : state
const getDateMonthYear = (input) => {
    const parse_Day = moment(input);
    const year = parse_Day.year().toString();
    const month = (parse_Day.month() + 1).toString().padStart(2, '0')
    const date = parse_Day.date().toString().padStart(2, '0')
    return [date, month, year]
};
const selectOption = async (page, selector, value) => {
    await page.click(selector)
    await page.$eval(selector, (input, value) => { input.value = value }, value)
};
const solve_captcha = async (cookie) => {
    var captcha = ""
    while (captcha.length != 4) {
        const resp = await axios({
            method: 'get',
            url: 'https://cova.mfa.gov.cn/qzCoCommonController.do?checkcode',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
                'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'Cookie': cookie
            },
            responseType: "arraybuffer"
        })

        var data = new FormData();
        data.append('file', resp.data);
        captcha = await get_image_captcha_text(resp.data)
    }
    return captcha
}
async function fillEmploymentInfo(page, employmentInfo, dob) {
    for (const [index, job] of employmentInfo.entries()) {
        if (index > 0) { // Click the "Add" button if it's not the first job
            await page.click('[id="Add_WorkExperience"]', { delay: 100, count: 1 });
        }
        sleep(500)
        //Work From
        const work_form = getDateMonthYear(job.find(info => info.id === 'employee_company_info_work_from').value.fe)
        await page.type(`[id="Begin-year${index + 1}"]`, work_form[2]);
        await page.select(`[id="Begin-month${index + 1}"]`, work_form[1]);
        //Work To
        const work_to_pod = job.find(info => info.id === 'employee_company_info_work_to')
        if (work_to_pod) {
            const workTo = getDateMonthYear(work_to_pod.value.fe)
            await page.type(` [id="End-year${index + 1}"]`, workTo[2]);
            await page.select(`[id="End-month${index + 1}"]`, workTo[1]); 9

        }
        //Job Name
        await page.type(`[id="JobName${index + 1}"]`, job.find(info => info.id === 'employee_company_info_company_school_name').value.fe);
        //Address
        const street_number = job.find(info => info.id === 'employee_company_info_street_number_name').value.fe
        const state = job.find(info => info.id === 'employee_company_info_state').value.fe
        const city = job.find(info => info.id === 'employee_company_info_city').value.fe
        const zip_code = job.find(info => info.id === 'employee_company_info_zip_code').value.fe
        const country = get_country_name_v2(job.find(info => info.id === 'employee_company_info_country').value.fe)
        const address = street_number + ", " + city + ", " + convert_state(state) + " " + zip_code + ", " + country
        await page.type(`[id="JobAddr${index + 1}"]`, address);
        //Phone
        const phone_pod = job.find(info => info.id === 'employee_company_info_phone').value.fe
        const phone_value = helper.findPrefixOfPhone(phone_pod.country, phone_pod.phone)
        await page.type(`[id="Jobtelpre${index + 1}"]`, phone_value[0] || "");
        await page.type(`[id="Jobtelbody${index + 1}"]`, phone_value[1] || "");
        //Job Title auto fill base on age 
        date = new Date(job.find(info => info.id === 'employee_company_info_work_from').value.fe)
        bỉthDate = new Date(dob)
        let age = date.getFullYear() - bỉthDate.getFullYear();
        if (date.getMonth() < bỉthDate.getMonth() || (date.getMonth() === bỉthDate.getMonth() && date.getDate() < bỉthDate.getDate())) {
            age--;
        }
        await page.type(
            `[id="JobPosition${index + 1}"]`,
            age <= 24 ? "Intern" :
                age <= 40 ? "Business Assistant" :
                    age <= 60 ? "Director of Operations" :
                        age <= 100 ? "Retiree" : ""
        );
        //Supervisor Info
        await page.type(`[id="SupervisorName${index + 1}"]`, job.find(info => info.id === 'employee_company_info_supervisor_name').value.fe);
        const supervisor_phone_pod = job.find(info => info.id === 'employee_company_info_supervisor_phone').value.fe
        const supervisor_phone_value = helper.findPrefixOfPhone(supervisor_phone_pod.country, supervisor_phone_pod.phone)
        await page.type(`[id="Supervisortelpre${index + 1}"]`, supervisor_phone_value[0] || "");
        await page.type(`[id="Supervisortelbody${index + 1}"]`, supervisor_phone_value[1] || "");
    }
}
async function fillChildInfo(page, childInfo) {
    for (const [index, job] of childInfo.entries()) {
        if (index > 0) { // Click the "Add" button if it's not the first job
            await page.click('[id="Add_Children"]', { delay: 100, count: 1 });
        }
        //Surname
        const surname = job.find(info => info.id === 'family_child_surname').value.fe
        if (surname) {
            await page.type(`[id="ChildrenFamilyName${index + 1}"]`, surname);
        } else {
            await page.click(`[id="na_Children_FamilyName${index + 1}"]`, { delay: 100, count: 1 });
        }

        //Given Name
        const given_name = job.find(info => info.id === 'family_child_given_name').value.fe
        if (given_name) {
            await page.type(`[id="ChildrenFirstName${index + 1}"]`, given_name);
        } else {
            await page.click(`[id="na_Children_FirstName${index + 1}"]`, { delay: 100, count: 1 });
        }

        //Nationality
        const nationality = job.find(info => info.id === 'family_child_nationality').value.fe

        await page.type(`[name="ChildrenNationalityCountry${index + 1}"]`, get_country_name(nationality))

        //DOB
        const dob = getDateMonthYear(job.find(info => info.id === 'family_child_date_of_birth').value.fe)
        await selectOption(page, `#Childrenbirthdayyear${index + 1}`, dob[2])
        await selectOption(page, `#Childrenbirthdaymonth${index + 1}`, dob[1])
        await selectOption(page, `#Childrenbirthdayday${index + 1}`, dob[0])
    }
}

async function removeHeader(page) {
    await page.evaluate(() => {
        // Remove the first element
        const navbarElement = document.querySelector('div.navbar.navbar-inverse.navbar-fixed-top');
        if (navbarElement) {
            navbarElement.remove();
        }

        // Remove the second element
        const containerElement = document.querySelector('div.container');
        if (containerElement) {
            containerElement.remove();
        }
    });
}
async function waitForElementWithRefresh(page, selector, maxRetries = 3, retryInterval = 1000) {
    let retryCount = 0;
    while (retryCount < maxRetries) {
        try {
            await page.waitForSelector(selector, { timeout: retryInterval });
            return await page.$(selector); // Found the element, return it
        } catch (error) {
            if (retryCount < maxRetries - 1) {
                console.log(`Element ${selector} not found, refreshing page... (Attempt ${retryCount + 1})`);
                await page.reload({ waitUntil: ["networkidle0", "domcontentloaded"] }); // Refresh and wait
                retryCount++;
            } else {
                throw error; // Rethrow the error after max retries
            }
        }
    }
}
async function resumeApplication(page, maxRetries = 10, document_id, passport_number) {
    let retryCount = 0;
    await page.goto('https://cova.mfa.gov.cn/qzCoCommonController.do?show&pageId=001&locale=en_US');
    await page.waitForNetworkIdle()
    await page.click('a[href="#continue"]')
    await page.type('[id="applyid"]', document_id)
    await page.type('[id="passportno"]', passport_number)
    cookies = await page.cookies();
    cookie = cookies.map(ck => `${ck.name}=${ck.value}`).join("; ")
    while (retryCount < maxRetries) {
        try {
            const captcha = await solve_captcha(cookie);
            await page.type('[id="codeid"]', captcha);
            await page.click('[onclick="continueApply()"]');

            await page.waitForSelector('.aui_content', { timeout: 5000 });

            const hasError = await page.$eval('.aui_content', el => el.textContent.trim());

            if (hasError.includes('[CAPTCHA code] error')) {
                retryCount++;
                await page.click('.aui_footer .aui_buttons button');
                await page.$eval('#codeid', input => input.value = '');
                console.log(`Captcha error. Retry attempt ${retryCount}/${maxRetries}`);
            } else {
                console.log("Captcha passed. Continuing...");
                return
            }
        } catch (error) {
            retryCount++;
            console.log(`Error during captcha processing. Retry attempt ${retryCount}/${maxRetries}`);
            if (error.message.includes('timeout')) {
                console.log("Timeout error");
            } else {
                console.log(error);
            }
        }
    }

}
const chn_evisa = async (data, order, task) => {
    const browser = await puppeteer.launch({ headless: DEBUG ? false : "new", args: ['--no-sandbox', '--disable-setuid-sandbox'] });
    const page = await browser.newPage();
    const recorder = new PuppeteerScreenRecorder(page);
    await recorder.start(RECODER_NAME);

    const viewport = DEBUG ? { width: 1200, height: 800, deviceScaleFactor: 1 } : { width: 1000, height: 1500 };
    await page.setViewport(viewport);

    page.setDefaultTimeout(30000);


    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })

    try {
        console.log("Loading...")
        await page.goto('https://cova.mfa.gov.cn/qzCoCommonController.do?show&pageId=001&locale=en_US');
        //================================================First Screen================================================
        await waitForElementWithRefresh(page, '[class="continent arSpercial"]')
        await page.click('[id="NA"]', { delay: 100, count: 2 })
        await page.click('[value="SAN FRANCISCO"]', { delay: 100, count: 2 })

        //Info
        await page.waitForSelector('[onclick="apply();"]')
        await sleep(300)
        await page.waitForNetworkIdle()
        await page.click('[onclick="apply();"]', { delay: 100, count: 2 })
        //===================================================Screen 1================================================
        console.log("Personal Info Screen...")
        await page.waitForNetworkIdle()
        await removeHeader(page)
        //Get Form ID
        const codeElement = await page.waitForSelector('code');
        const document_id = await codeElement.evaluate(el => el.textContent);
        console.log(`Document ID: ${document_id}`)
        //Name
        await waitForElementWithRefresh(page, '[id="PassportFamilyName"]')
        await page.type('[id="PassportFamilyName"]', data.passport_core_info_surname)
        await page.type('[id="PassportFirstName"]', data.passport_core_info_given_name)
        if (data.passport_core_info_other_name) {
            await page.type('[id="OtherName"]', data.passport_core_info_other_name)
        }
        //DOB
        const dob = getDateMonthYear(data.passport_core_info_date_of_birth)
        await selectOption(page, `#Birthday-year`, dob[2])
        await selectOption(page, `#Birthday-month`, dob[1])
        await selectOption(page, `#Birthday-day`, dob[0])
        await page.$eval('#Birthday-year', (input, value) => { input.value = value }, dob[2])
        await page.$eval('#Birthday-month', (input, value) => { input.value = value }, dob[1])
        await page.$eval('#Birthday-day', (input, value) => { input.value = value }, dob[0])

        //Gender
        const isMale = data.passport_core_info_gender == "M"
        if (isMale) {
            //Male
            await page.click('[id="sex1"]', { delay: 100, count: 2 })
        } else {
            //Female
            await page.click('[id="sex0"]', { delay: 100, count: 2 })
        }
        //Place Of Birth
        await page.waitForSelector('[id="select2-BirthPlaceCountry-container"]')
        await page.type('[id="BirthPlaceCountry"]', data.passport_core_info_country_of_birth.text)
        await sleep(500)
        // Check if foreign province field is visible
        const isForeignProvinceVisible = await page.evaluate(() => {
            const foreignProvinceDiv = document.querySelector('#foreign_birthplaceprovince_div');
            return foreignProvinceDiv && window.getComputedStyle(foreignProvinceDiv).display !== 'none';
        });

        // Fill in the appropriate field based on visibility
        if (isForeignProvinceVisible) {
            await page.type('[id="foreign_birthplaceprovince"]', data.passport_core_info_state_of_birth.text);
        } else {
            await page.type('[id="chn_birthplaceprovince"]', data.passport_core_info_state_of_birth.text);
        }

        await sleep(500);

        // Check if foreign city field is visible
        const isForeignCityVisible = await page.evaluate(() => {
            const foreignCityDiv = document.querySelector('#foreign_birthplacecity_div');
            return foreignCityDiv && window.getComputedStyle(foreignCityDiv).display !== 'none';
        });

        // Fill in the appropriate city field based on visibility
        if (isForeignCityVisible) {
            await page.type('[id="foreign_birthplacecity"]', data.passport_core_info_city_of_birth.text);
        } else {
            await page.type('[id="chn_birthplacecity"]', data.passport_core_info_city_of_birth.text);
        }

        //Nationality
        await page.type('[id="NationalityCountry"]', get_country_name(data.passport_core_info_nationality))
        await page.click(`[id="bsy_Nationalityid_Card"]`, { delay: 100, count: 1 });
        await page.click(`[name="IsHavePermanent"][value="0"]`, { delay: 100, count: 2 });

        //Dual Nationality
        if (data.passport_additional_info_is_dual_citizen) {
            await page.click(`[name="isHaveOtherNationality"][value="1"]`, { delay: 100, count: 2 });
            await page.type('[name="OtherNationality1"]', get_country_name(data.passport_core_info_country_of_birth))
            await page.type('[id="PassportNoOfOtherNationality1"]', data.passport_second_passport_passport_number)
        } else {
            await page.click(`[name="isHaveOtherNationality"][value="0"]`, { delay: 100, count: 2 });
        }

        //Previous Nationality
        const formerIsCHN = data.document_copy_of_document_have_ever_held_chinese_nationality
        if (data.passport_additional_info_former_nationality != data.passport_core_info_nationality || formerIsCHN) {
            //Have Former nationality
            await page.click(`[name="isHaveFormerNationality"][value="1"]`, { delay: 100, count: 2 });
            if (formerIsCHN || data.passport_additional_info_former_nationality == 'CHN') {
                await page.type('[name="FormerNationality1"]', get_country_name('CHN'))
                //Have you apply Visa Before
                if (data.additional_question_previous_visa_have_you_apply_china_visa_before) {
                    //Not first time apply Visa
                    await page.click(`[name="isFirstApplyVisaForChina1"][value="0"]`, { delay: 100, count: 2 });
                    //clear alert
                    await page.click(`[class="aui_close"]`);
                } else {
                    //First time apply Visa
                    await page.click(`[name="isFirstApplyVisaForChina1"][value="1"]`, { delay: 100, count: 2 });
                    await page.type('[id="LastChinesePassport1"]', data.additional_question_previous_visa_last_chinese_passport_number)
                    await page.type('[id="FormerChineseIdNumber1"]', data.additional_question_previous_visa_former_chinese_id_number)


                }
            } else {
                await page.type('[name="FormerNationality1"]', get_country_name(data.passport_additional_info_former_nationality))
            }
        } else {
            await page.click(`[name="isHaveFormerNationality"][value="0"]`, { delay: 100, count: 2 });
        }

        //Passport Number
        await page.click(`[name="typeOfPassport"][value="707001"]`, { delay: 100, count: 2 });
        await page.type(`[id="PassportNo"]`, data.passport_core_info_passport_number);
        await page.type('[name="IssueCountry"]', get_country_name(data.passport_core_info_issuing_authority))
        await page.type(`[id="IssuePlace"]`, "UNITED STATES DEPARTMENT OF STATE")
        const passportExp = getDateMonthYear(data.passport_core_info_expiration_date)
        await selectOption(page, `#Expirationdate-year`, passportExp[2])
        await selectOption(page, `#Expirationdate-month`, passportExp[1])
        await selectOption(page, `#Expirationdate-day`, passportExp[0])

        //Marital Status
        await page.click(`[name="maritalstatus"][value="${{
            "single": "706003",
            "married": "706001",
            "divorced": "706002",
            "widower": "706004",
            "widow": "706004"
        }[data.personal_core_info_marital_status] ?? '706003'}"]`, { delay: 100, count: 2 });

        //User Photo
        var cookies = await page.cookies();
        var cookie = cookies.map(ck => `${ck.name}=${ck.value}`).join("; ")
        const photoPath = await helper.upload_file(data.photo_passport_photo_copy_of_photo, cookie)
        const finalImageUrl = `https://cova.mfa.gov.cn/qzPhotoController.do?getPhotoByPath&photoPath=${photoPath}&photoType=photo`;
        await page.evaluate((photoPath, finalImageUrl) => {
            document.getElementById('zpyl').src = finalImageUrl;
            document.getElementById('preProcessPhotoPath').value = '';
            document.getElementById('photoPath').value = photoPath;
            document.getElementById('isCheckedPass').value = '1';
            document.getElementById('failReason').value = '';
        }, photoPath, finalImageUrl);

        await sleep(300)
        await page.click(`[onclick="savePersonInfo('saveandnext');"]`)

        //===================================================Screen 2================================================
        console.log("Type Of Visa Screen...")
        await page.waitForNetworkIdle()
        await removeHeader(page)

        //Visa Type
        await page.select(`[id="VisaType"]`, {
            "tourist": "709001",
            "business": "709002"
        }[order.service.attributes.purpose] ?? '709001');
        await sleep(999)
        await page.click(`[name="VisaPurpose"][value="${{
            "tourist": "710001",
            "business": "710003"
        }[order.service.attributes.purpose] ?? '701001'}"]`, { delay: 100, count: 2 });

        //Process Time
        await page.click(`[name="serviceType"][value="701001"]`, { delay: 100, count: 2 });

        //No Of Entries
        await page.click(`[name="applyVisaTimes"][${order.service.attributes.number_of_entries === 'multiple_entries' ? 'value="703003"' : 'value="703001"'}]`, { delay: 100, count: 2 });

        //Validity
        await page.type('[id="ApplyVisaValidity"]', {
            "10y": "120",
            "1y": "12"
        }[order.service.attributes.validity] ?? '12')

        //Length Of Stay
        const entryDate = moment(data.service_core_info_entry_date);
        const exitDate = moment(data.service_core_info_exit_date);
        lengthOfStay = exitDate.diff(entryDate, 'days')
        if (lengthOfStay == 0) {
            lengthOfStay = 1
        }
        await page.type('[name="ApplyMaxStayDays"]', lengthOfStay.toString())

        await sleep(300)
        await page.click(`[onclick="saveApplyInfo('saveandnext');"]`)

        //===================================================Screen 3================================================
        console.log("Work Information Screen...")
        await page.waitForNetworkIdle()
        await removeHeader(page)
        //Oupcation
        await waitForElementWithRefresh(page, '[id="JobType"]')
        await page.select('[id="JobType"]', {
            'business_person': '713003',
            'company_employee': '713004',
            "student": '713007',
            "retired": '713010',
            "homemaker": '713002',
            "self_employed": '713009'
        }[data.personal_occupation_occupation ?? '713003'])
        const currentOccupation = await page.$eval('#JobType', el => el.value);
        if (currentOccupation == "713002") {
            //Other Case
            await page.type('[id="OtherSpecify"]', data.personal_occupation_occupation)
        }

        //Employment Info
        if (data.employee_work_experience_in_5_years_employment_info && data.employee_work_experience_in_5_years_employment_info.length > 0) {
            await fillEmploymentInfo(page, data.employee_work_experience_in_5_years_employment_info, data.passport_core_info_date_of_birth)
        } else {
            //Should not happen
            await page.click(`[id="bsy_Work"]`, { delay: 100, count: 1 });
            await page.type('[id="desc_Work"]', "No Job")

        }
        await sleep(300)
        await page.click(`[onclick="saveWorkInfo('saveandnext');"]`)

        //===================================================Screen 4================================================
        console.log("Education Screen...")
        await page.waitForNetworkIdle()
        await removeHeader(page)
        await waitForElementWithRefresh(page, '[id="SchoolName1"]')
        //School Info

        await page.type('[id="SchoolName1"]', data.personal_education_name_of_institution)
        const highestEducation = {
            "elementary": null,               // No direct match in their list
            "junior_high": null,             // No direct match in their list
            "high_school": "714003",
            "college": "714004",
            "graduate": "714005",
            "postgraduate": "714006"
        }[data.personal_education_education]
        if (!highestEducation) {
            await page.click(`[id="bsy_Educational_College"]`, { delay: 100, count: 1 })
        } else {
            await page.select('[id="HighestDegree1"]', highestEducation)
        }
        if (["graduate", "postgraduate", "college"].includes(data.personal_education_education)) {
            await page.type('[id="TheSpecialty1"]', 'Business Administration')
        }
        await sleep(300)
        await page.click(`[onclick="saveEducationInfo('saveandnext');"]`)

        //===================================================Screen 5================================================
        console.log("Family Info Screen...")
        await page.waitForNetworkIdle()
        await removeHeader(page)

        //Address

        await waitForElementWithRefresh(page, '[id="StreetAddr"]')
        const fullAddress = data.personal_home_address_street_number_name + ", " + data.personal_home_address_city + ", " + convert_state(data.personal_home_address_state) + " " + data.personal_home_address_zip_code + ", " + get_country_name_v2(data.personal_home_address_country)
        await page.type('[id="StreetAddr"]', fullAddress)

        //Phone
        const personalPhone = helper.findPrefixOfPhone(data.personal_core_info_phone.country, data.personal_core_info_phone.phone)
        await page.type('[id="AreaCode"]', personalPhone[0])
        await page.type('[id="FamilyPhone"]', personalPhone[1])
        await page.type('[id="MobilePhone"]', data.personal_core_info_phone.phone)

        //Spouse Info
        if (data.personal_core_info_marital_status === 'married') {
            const surname = data.family_spouse_surname
            if (surname) {
                //Have surname
                await page.type('[id="SpouseFamilyName1"]', surname)
            } else {
                await page.click(`[id="na_Spouse_FamilyName1"]`, { delay: 100, count: 1 })
            }
            const given_name = data.family_spouse_given_name
            if (given_name) {
                //Have given name
                await page.type('[id="SpouseFirstName1"]', given_name)
            } else {
                await page.click(`[id="na_Spouse_FirstName1"]`, { delay: 100, count: 1 })
            }
            //Nationality

            await page.type('[name="SpouseNationalityCountry1"]', get_country_name(data.family_spouse_nationality))
            //DOB
            const date_of_birth = getDateMonthYear(data.family_spouse_date_of_birth)
            await selectOption(page, '#Spousebirthdayyear1', date_of_birth[2])
            await selectOption(page, '#Spousebirthdaymonth1', date_of_birth[1])
            await selectOption(page, '#Spousebirthdayday1', date_of_birth[0])
            //Country Of Birth
            await page.type('[name="SpouseCountryOfBirth1"]', get_country_name(data.family_spouse_country_of_birth))

            await page.type('[id="SpouseForeignCityOfBirth1"]', data.family_spouse_city_of_birth)
            await page.click('[id="SpouseAddressWithApplicant1"]', { delay: 100, count: 2 })
        }

        //Father Info
        if (!data.family_father_do_you_know_father_info) {
            //Do not have info
            await page.click(`[id="bsy_Father"]`, { delay: 100, count: 1 })
            await page.type('[id="desc_Father"]', data.family_father_explain_unknown_father_info)
        } else {
            const surname = data.family_father_surname
            if (surname) {
                //Have surname
                await page.type('[id="FatherFamilyName1"]', surname)
            } else {
                await page.click(`[id="na_Father_FamilyName1"]`, { delay: 100, count: 1 })
            }
            const given_name = data.family_father_given_name
            if (given_name) {
                //Have given name
                await page.type('[id="FatherFirstName1"]', given_name)
            } else {
                await page.click(`[id="na_Father_FirstName1"]`, { delay: 100, count: 1 })
            }
            //Nationality
            await page.type('[name="FatherNationalityCountry1"]', get_country_name(data.family_father_nationality))
            //DOB
            const date_of_birth = getDateMonthYear(data.family_father_date_of_birth)
            await selectOption(page, '#Fatherbirthdayyear1', date_of_birth[2])
            await selectOption(page, '#Fatherbirthdaymonth1', String(date_of_birth[1]))
            await selectOption(page, '#Fatherbirthdayday1', date_of_birth[0])
            if (data.family_father_is_living_in_cn) {
                await page.click(`[id="FatherIsInChina1"]`, { delay: 100, count: 2 })
                await page.click(`[name="FatherStatusInChina1"][value="${RESIDENT_STATUS[data.family_father_status_in_cn]}"]`)
            } else {
                await page.click(`[id="FatherIsInChina11"]`, { delay: 100, count: 2 })
            }
        }

        //Mother Info
        if (!data.family_mother_do_you_know_mother_info) {
            //Do not have info
            await page.click(`[id="bsy_Mother"]`, { delay: 100, count: 1 })
            await page.type('[id="desc_Mother"]', data.family_mother_explain_unknown_mother_info)

        } else {
            const surname = data.family_mother_surname
            if (surname) {
                //Have surname
                await page.type('[id="MotherFamilyName1"]', surname)
            } else {
                await page.click(`[id="na_Mother_FamilyName1"]`, { delay: 100, count: 1 })
            }
            const given_name = data.family_mother_given_name
            if (given_name) {
                //Have given name
                await page.type('[id="MotherFirstName1"]', given_name)
            } else {
                await page.click(`[id="na_Mother_FirstName1"]`, { delay: 100, count: 1 })
            }
            //Nationality
            await page.type('[name="MotherNationalityCountry1"]', get_country_name(data.family_mother_nationality))
            //DOB
            const date_of_birth = getDateMonthYear(data.family_mother_date_of_birth)
            await selectOption(page, '#Motherbirthdayyear1', date_of_birth[2])
            await selectOption(page, '#Motherbirthdaymonth1', date_of_birth[1])
            await selectOption(page, '#Motherbirthdayday1', date_of_birth[0])
            if (data.family_mother_is_living_in_cn) {
                await page.click(`[id="MotherIsInChina1"]`, { delay: 100, count: 2 })
                await page.click(`[name="MotherStatusInChina1"][value="${RESIDENT_STATUS[data.family_mother_status_in_cn]}"]`, { delay: 100, count: 2 })
            } else {
                await page.click(`[id="MotherIsInChina11"]`, { delay: 100, count: 2 })
            }
        }

        //Child
        if (!data.family_child_child_information || data.family_child_child_information.length < 1) {
            //Do not have info
            await page.click(`[id="bsy_Children"]`, { delay: 100, count: 1 })
        } else {
            await fillChildInfo(page, data.family_child_child_information)
        }

        //Relatives Infp
        if (!data.family_additional_info_have_any_immediate_relatives) {
            //Do not have info
            await page.click(`[id="AnyImmediateRelative1"]`, { delay: 100, count: 2 })
        } else {
            await page.click(`[id="AnyImmediateRelative"]`, { delay: 100, count: 2 })
            var fullName = data.family_additional_info_given_name + " " + data.family_additional_info_surname
            await page.type('[id="ImmediateFamilyName1"]', fullName)
            await page.type('[id="ImmediateRelationshipToYou1"]', RELATIONSHIP[data.family_additional_info_relationship])
            await page.click(`[name="ImmediateStatusInChina1"][value="${RESIDENT_STATUS[data.family_additional_info_relative_status_generic]}"]`, { delay: 100, count: 2 })

            //Details
            if (data.family_additional_info_relative_status_generic === "resident") {
                if (data.family_additional_info_resident_details === "chn_resident_details_1") {
                    await page.click(`[name="ImmediateStatusInChinaDetail1"][value="704001"]`, { delay: 100, count: 2 })
                }
                if (data.family_additional_info_resident_details === "chn_resident_details_2") {
                    await page.click(`[name="ImmediateStatusInChinaDetail1"][value="704002"]`, { delay: 100, count: 2 })
                }
            } else if (data.family_additional_info_relative_status_generic === "stay") {
                if (data.family_additional_info_resident_details === "chn_stay_details_1") {
                    await page.click(`[name="ImmediateStatusInChinaDetail1"][value="704003"]`, { delay: 100, count: 2 })
                }
                if (data.family_additional_info_resident_details === "chn_stay_details_2") {
                    await page.click(`[name="ImmediateStatusInChinaDetail1"][value="704004"]`, { delay: 100, count: 2 })
                }
            }
        }

        await sleep(300)
        await page.click(`[onclick="saveFamilyInfo('saveandnext');"]`)

        //===================================================Screen 6================================================
        console.log("Trip Info Screen...")
        await page.waitForNetworkIdle()
        await removeHeader(page)
        await waitForElementWithRefresh(page, '[id="arrivalcityyear"]')
        //Entry - Exit (Date & City)
        await sleep(2000)
        await selectOption(page, `#arrivalcityyear`, entryDate.year().toString())
        await selectOption(page, `#arrivalcitymonth`, (entryDate.month() + 1).toString().padStart(2, '0'))
        await selectOption(page, `#arrivalcityday`, entryDate.date().toString().padStart(2, '0'))
        await selectOption(page, `#arrivalyear1`, entryDate.year().toString())
        await selectOption(page, `#arrivalmonth1`, (entryDate.month() + 1).toString().padStart(2, '0'))
        await selectOption(page, `#arrivalday1`, entryDate.date().toString().padStart(2, '0'))

        await selectOption(page, `#leavecityyear`, exitDate.year().toString())
        await selectOption(page, `#leavecitymonth`, (exitDate.month() + 1).toString().padStart(2, '0'))
        await selectOption(page, `#leavecityday`, exitDate.date().toString().padStart(2, '0'))

        await selectOption(page, `#leaveyear1`, exitDate.year().toString())
        await selectOption(page, `#leavemonth1`, (exitDate.month() + 1).toString().padStart(2, '0'))
        await selectOption(page, `#leaveday1`, exitDate.date().toString().padStart(2, '0'))
        await page.type('[id="LeaveCity"]', data.travel_visited_country_intended_departure_city)
        await page.type('[id="ArrivalCity"]', data.travel_visited_country_intended_arrival_city)
        await page.type('[id="StayCity1"]', data.travel_visited_country_intended_arrival_city)
        if (order.service.attributes.purpose === "tourist") {
            //Organization - DISABLE
            await page.click('[id="bsy_Organization_InChina"]', { delay: 100, count: 1 })
        } else {
            await page.type('[id="InvitingName"]', data.employee_company_reference_in_destination_company_name)
            await page.click('[id="bsy_InvitingRelationshipToYou"]')
            const companyPhone = helper.findPrefixOfPhone(data.employee_company_reference_in_destination_phone.country, data.employee_company_reference_in_destination_phone.phone)
            await page.type('[id="telpre"]', companyPhone[0])
            await page.type('[id="telbody"]', companyPhone[1])
            await page.type('[id="InvitingProvince"]', data.employee_company_reference_in_destination_state)
            await page.type('[id="InvitingCity"]', data.employee_company_reference_in_destination_city)

        }
        //Emergency Contact
        const personal_emergency_contact_surname = data.personal_emergency_contact_surname
        if (personal_emergency_contact_surname) {
            //Have surname
            await page.type('[id="EmergencyContactFamilyName"]', personal_emergency_contact_surname)
        } else {
            await page.click(`[id="na_EmergencyFamilyName"]`, { delay: 100, count: 1 })
        }
        const personal_emergency_contact_given_name = data.personal_emergency_contact_given_name
        if (personal_emergency_contact_given_name) {
            //Have given name
            await page.type('[id="EmergencyContactFirstName"]', personal_emergency_contact_given_name)
        } else {
            await page.click(`[id="na_EmergencyFirstName"]`, { delay: 100, count: 1 })
        }
        await page.type('[id="EmergencyRealationshipToYou"]', RELATIONSHIP[data.personal_emergency_contact_relationship])
        const emergency_contact_phone = helper.findPrefixOfPhone(data.personal_emergency_contact_phone.country, data.personal_emergency_contact_phone.phone)
        await page.type('[id="Emergencytelpre"]', emergency_contact_phone[0])
        await page.type('[id="Emergencytelbody"]', emergency_contact_phone[1])
        //Who will pay for this travel
        await page.click(`[name="PayForTravel"][id="${{
            "yourself": "PayForTravel0",
            "organization": "PayForTravel2"
        }[data.personal_financial_supported_financial_support_status ?? 'yourself']}"]`, { delay: 100, count: 2 })
        if (data.personal_financial_supported_financial_support_status === "organization") {
            await page.type('[id="PayForTravelOrganizationName"]', data.personal_financial_supported_company_name)
            await page.type('[id="PayForTravelRelationshipToYou"]', "Company")
            const address = data.personal_financial_supported_street_number_name + ", " + data.personal_financial_supported_city + ", " + convert_state(data.personal_financial_supported_state) + " " + data.personal_financial_supported_zip_code
            await page.type('[id="PayForTravelAddress"]', address)
            await page.type('[id="PayForTravelCountry"]', get_country_name(data.personal_financial_supported_country))
        }

        // Accompanying person(s)
        await page.click('[id="nohasFriend"]', { delay: 100, count: 2 })

        await sleep(300)
        await page.click(`[onclick="saveTravelInfo('saveandnext');"]`)
        await sleep(500)
        //===================================================Screen 7================================================
        console.log("Past Travel Screen...")
        await page.waitForNetworkIdle()
        await sleep(500)
        await removeHeader(page)
        await waitForElementWithRefresh(page, '[id="IsHaveChinaVisa"]')
        //Previous Chinese visa
        if (data.additional_question_previous_visa_have_you_granted_visa_before || data.additional_question_previous_visa_have_you_apply_china_visa_before) {
            //Check
            await page.click('[id="IsHaveChinaVisa"]', { delay: 100, count: 2 })
            await sleep(500)
            //Visa Number
            if (data.additional_question_previous_visa_visa_number != null)
                await page.type('[id="VisaNo"]', data.additional_question_previous_visa_visa_number)
            //Issue Place
            await page.type('[id="IssueOrg"]', data.additional_question_previous_visa_issue_place)
            await page.select('[id="VisaType"]', data.additional_question_previous_visa_visa_purpose === "business" ? "722025" : "722009")
            //Issue Date
            const issue_date = getDateMonthYear(data.additional_question_previous_visa_issue_date)
            await selectOption(page, '#issuedate-year', issue_date[2])
            await selectOption(page, '#issuedate-month', issue_date[1])


            //Finger print
            if (data.additional_question_fingerprint_previously_fingerprint_collected_before) {
                await sleep(300)
                await page.click('[id="IsHaveFingerPrinteger"]', { delay: 100, count: 1 })
                await sleep(500)
                //Country
                await page.type('[name="FingerPrintCollectionCountry"]', data.additional_question_fingerprint_previously_country === "CHN" ? "unknown" : get_country_name(data.additional_question_fingerprint_previously_country))
                //City
                await page.type('[id="FingerPrintCollectionPlace"]', data.additional_question_fingerprint_previously_city)
                //Date
                const printed_date = getDateMonthYear(data.additional_question_fingerprint_previously_date)
                await selectOption(page, '#collection_year', printed_date[2])
                await selectOption(page, '#collection_month', printed_date[1])
                await selectOption(page, '#collection_day', printed_date[0])

            } else {
                await page.click('[id="IsHaveFingerPrinteger1"]', { delay: 100, count: 2 })
            }
            await sleep(500)
            //Have you ever been issued a Chinese residence permit?
            if (data.additional_question_residence_immigration_status_cn_3_6) {
                await page.click('[name="IsHaveResidenceLicense"][value="1"]', { delay: 100, count: 2 })
                await page.waitForSelector('[id="ResidenceLicenseNo"]')
                await page.type('[id="ResidenceLicenseNo"]', data.additional_question_residence_immigration_status_document_number)
            } else {
                await page.click('[name="IsHaveResidenceLicense"][value="0"]', { delay: 100, count: 2 })
            }
        } else {
            await page.click('[id="IsHaveChinaVisa1"]', { delay: 100, count: 2 })
        }
        //Have you ever been to China?
        if (data.travel_travel_history_have_you_ever_been) {
            await page.click('[id="hasprevious0"]', { delay: 100, count: 2 })
        } else {
            await page.click('[id="hasprevious1"]', { delay: 100, count: 2 })
        }

        //Valid visas
        if (data.additional_question_previous_visa_other_valid_visa) {
            await page.click('[id="IsHaveOtherVisa"]', { delay: 100, count: 2 })
            for (const country of data.additional_question_previous_visa_valid_visa_list) {
                await page.type('#VisaCountryInfo > div > span > span.selection > span > ul > li > input', get_country_name(country));
                await page.keyboard.press('Enter')
            }
        } else {
            await page.click('[id="IsHaveOtherVisa1"]', { delay: 100, count: 2 })
        }
        //Countries visited in the last 12 months
        if (data.travel_visited_country_countries_visited_in_last_12_months && data.travel_visited_country_countries_visited_in_last_12_months.length > 0) {
            await page.click('[id="IsToOtherCountry"]', { delay: 100, count: 2 });
            for (const country of data.travel_visited_country_countries_visited_in_last_12_months) {
                await page.type('#visitInfo > div > span > span.selection > span > ul > li > input', get_country_name(country));
                await page.keyboard.press('Enter')
            }
        } else {
            await sleep(500)
            await page.click('[id="IsToOtherCountry1"]', { delay: 500, count: 1 });
        }
        await sleep(500)
        await page.click(`[onclick="savePreviousTravel('saveandnext');"]`)

        //===================================================Screen 8================================================
        console.log("Question Screen...")
        await page.waitForNetworkIdle()
        await sleep(1000)
        await removeHeader(page)
        await waitForElementWithRefresh(page, `[id="ItemValueNo1"]`)

        //Have you ever been refused a visa for China, or been refused entry into China? - additional_question_chn_cn_3_2
        if (!data.additional_question_chn_cn_3_2) {
            await page.click('[id="ItemValueNo1"]', { delay: 100, count: 2 })
            await page.click('[id="ItemValueNo2"]', { delay: 100, count: 2 })
        } else {
            //Should not happen
            await page.click('[id="ItemValueYes1"]', { delay: 100, count: 2 })
            await page.click('[id="ItemValueYes2"]', { delay: 100, count: 2 })
        }

        //Have you ever entered China illegally, overstayed, or worked illegally? - additional_question_chn_cn_3_1
        if (!data.additional_question_chn_cn_3_1) {
            await page.click('[id="ItemValueNo3"]', { delay: 100, count: 2 })
        } else {
            //Should not happen
            await page.click('[id="ItemValueYes3"]', { delay: 100, count: 2 })
        }
        //Do you have any criminal record in China or any other country? - additional_question_chn_cn_3_3
        if (!data.additional_question_chn_cn_3_3) {
            await page.click('[id="ItemValueNo4"]', { delay: 100, count: 2 })
        } else {
            //Should not happen
            await page.click('[id="ItemValueYes4"]', { delay: 100, count: 2 })
        }
        //Do you have any serious mental disorder or infectious disease? - additional_question_chn_cn_3_4
        if (!data.additional_question_chn_cn_3_4) {
            await page.click('[id="ItemValueNo5"]', { delay: 100, count: 2 })
        } else {
            //Should not happen
            await page.click('[id="ItemValueYes5"],{delay:100,count:2}')
        }
        //Have you ever visited countries or territories where there is an epidemic in the last 30 days? - additional_question_chn_cn_3_5
        if (!data.additional_question_chn_cn_3_5) {
            await page.click('[id="ItemValueNo6"]', { delay: 100, count: 2 })
        } else {
            //Should not happen
            await page.click('[id="ItemValueYes6"]', { delay: 100, count: 2 })
        }
        //Default Answer
        await page.click('[id="ItemValueNo7"]', { delay: 100, count: 2 })
        await page.click('[id="ItemValueNo8"]', { delay: 100, count: 2 })
        await page.click('[id="ItemValueNo9"]', { delay: 100, count: 2 })
        await page.click('[id="ItemValueNo10"]', { delay: 100, count: 2 })
        await page.click('[id="ItemValueNo11"]', { delay: 100, count: 2 })
        if (order.service.attributes.purpose === 'tourist') {
            await page.click('[id="ItemValueNo13"]', { delay: 100, count: 2 }).catch(() => { })
            await page.click('[id="ItemValueNo14"]', { delay: 100, count: 2 }).catch(() => { })
        }
        await sleep(300)
        await page.click(`[onclick="saveItemInfo('saveandnext');"]`)

        //===================================================Screen 9================================================
        console.log("Signing Screen...")
        await page.waitForNetworkIdle()
        await removeHeader(page)
        await waitForElementWithRefresh(page, `[name="isAgent"]`)
        // Does anyone else assist you in completing the application? 
        await page.click('[name="isAgent"][value="0"]', { delay: 100, count: 2 })

        await sleep(300)
        await page.click(`[onclick="saveSignAndStatement('saveandnext');"]`)

        //===================================================Review Screen================================================
        await page.waitForNetworkIdle()
        await removeHeader(page)

        //Review Screen
        console.log("Complete Fill Form")

        await page.click(`[onclick="ConfirmVisaInfo();"]`)
        //=========================================Declaration of the app licant===========================================
        await sleep(500)
        await page.waitForNetworkIdle()
        await removeHeader(page)
        await page.click(`[id="selfStatement"]`, { delay: 500, count: 1 })
        await sleep(1000)
        console.log("Submit Form")
        await page.click(`[onclick="submitInfo();"]`)
        await sleep(5000)
        await page.click(`[class="aui_state_highlight"]`, { delay: 100, count: 2 })
        await sleep(5000)
        await page.click(`[class="aui_state_highlight"]`, { delay: 100, count: 2 })
        await sleep(5000)
        //=========================================Download PDF===========================================================
        //Clear old session and continue application to fetch form
        await page.waitForSelector('.aui_buttons button');
        await page.click('.aui_buttons button')
        console.log("Clear Session and bypass Captcha")
        await sleep(5000)
        await resumeApplication(page, 10, document_id, data.passport_core_info_passport_number)
        cookies = await page.cookies();
        cookie = cookies.map(ck => `${ck.name}=${ck.value}`).join("; ")
        await sleep(5000)
        await page.waitForSelector('.aui_buttons button');
        await page.click('.aui_buttons button');
        await sleep(1000)
        await page.evaluate(() => {
            window.scrollTo(0, document.body.scrollHeight);
        });
        const form = await helper.get_form(cookie)
        let error = ""
        if (form == null) {
            error = "Form did not become available within the retry limit."
        }
        await recorder.stop();
        await browser.close()
        return { form: form, document_id: document_id, passport_number: data.passport_core_info_passport_number, error: error }
    } catch (error) {
        console.log(error)
        console.log(error.message)
        await recorder.stop();
        await browser.close()
        return null
    }

}

const get_pod_values = (input_pods) => {
    let results = {};

    for (const key in input_pods) {
        const { category, sub_category, name, value, option_choice } = input_pods[key];

        if (value?.fe != null) {
            results[`${category}_${sub_category}_${name}`] = value.fe;
            if (option_choice && Object.keys(option_choice)?.length > 0)
                results = { ...results, ...get_pod_values(option_choice[value?.fe]) };
        }
    }

    return results;
}

const MAX_RETRY = 2;
const create_form = async (pod_data, order, task) => {
    console.log(JSON.stringify(pod_data, null, 2))
    let form_data = null
    for (let i = 0; i < MAX_RETRY; i++) {
        form_data = await chn_evisa(pod_data, order, task)
        if (form_data != null) {
            break
        }
    }

    const result = {
        form_file: "",
        form_callback: {},
        success: false
    }

    const file_name = `Application_Form_${pod_data.passport_core_info_surname}_${pod_data.passport_core_info_given_name}_${moment().unix()}.pdf`
    const buckets = JSON.parse(process.env.ad_s3)
    if (form_data?.form != null) {
        // Save the PDF to a file
        fs.writeFileSync("images/form.pdf", form_data.form);
        await s3_upload_buff(buckets.ariadirect_prod_applications, `tasks/${file_name}`, form_data.form)
        result.form_file = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/tasks/${file_name}`
        result.success = true
    } else {
        if (form_data?.error != null) {
            result.form_callback.error = form_data.error
        }
    }
    result.form_callback.document_id = form_data.document_id
    result.form_callback.passport_number = form_data.passport_number

    await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${RECODER_NAME}`, fs.readFileSync(RECODER_NAME))

    fs.rmSync('images', { recursive: true })
    fs.rmSync(RECODER_NAME, { recursive: true })

    result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${RECODER_NAME}`
    console.log("STEPS:", result.form_callback.steps)
    console.log("Form Id:", result.form_callback.document_id)

    return result
}

const main = async (order_id, task_id) => {
    try {
        const { pod_data, order, task } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form(pod_data, order, task)
        return data
    } catch (error) {
        console.log(error)
    }
}

// const MOCK = {
//     order_id: 3875,
//     task_id: 4508
// }
// main(5576, 9741)
module.exports = { main, create_form };