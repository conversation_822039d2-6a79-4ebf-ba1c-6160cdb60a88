const fs = require('fs');
const axios = require('axios');
const moment = require('moment');
const puppeteer = require('puppeteer');
const { PuppeteerScreenRecorder } = require('puppeteer-screen-recorder');
const FormData = require('form-data');
const { get_order_detail, sleep, s3_upload_buff, get_country_name_v2 } = require('../../shared/helpers');
const { get_image_captcha_text } = require('../../shared/captcha');

let DEBUG = false
const RECODER_NAME = `Recorder_${moment().unix()}.mp4`

function getRandomHotel() {
    const hotels = [
        {
            Name: "Aeris International Hotel",
            Address: "No. 77, Section 2, Liuchuan West Road, West District, Taichung City, Taiwan",
            Phone: "+886423770188",
        },
        {
            Name: "Oriental Pearl International Hotel",
            Address: "606, Taiwan, Chiayi County, Zhongpu Township, 23",
            Phone: "+88652536999",
        },
        {
            Name: "Sun Moon Lake Hotel",
            Address: "No. 419, Zhongshan Road, Yuchi Township, Nantou County, Taiwan",
            Phone: "+************",
        },
    ];
    const randomIndex = Math.floor(Math.random() * hotels.length);
    return hotels[randomIndex];
}

const twn_visa_form = async (data, attributes) => {

    const browser = await puppeteer.launch({ headless: DEBUG ? false : "new", args: ['--no-sandbox', '--disable-setuid-sandbox'] });
    const page = await browser.newPage();
    !DEBUG && await page.setViewport({ width: 1000, height: 1500 });
    page.setDefaultTimeout(30000);
    let index = 1;
    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })

    let email = process.env.ad_env === 'prod' ? '<EMAIL>' : '<EMAIL>'
    const recorder = new PuppeteerScreenRecorder(page);
    await recorder.start(RECODER_NAME);
    try {
        VERIFY_CAPTCHA: for (let c = 0; c < 5; c++) {
            await page.goto('https://visawebapp.boca.gov.tw/BOCA_EVISA/home.do');
            await page.waitForSelector('[onclick="new_gVisa()"]')
            await page.click('[onclick="new_gVisa()"]')
            await sleep(1000)
            // Page 1
            await page.waitForSelector('#nationality')
            await page.select('#nationality', data.passport_core_info_nationality);
            await page.click('#terms_and_conds');
            //Length Of Stay ("mrvApplicationTemp.stayDays2">180 days)
            await sleep(200)
            await page.click('[id="mrvApplicationTemp.stayDays1"]');
            //Num Of Entry ("mrvApplicationTemp.entry1 - Single / mrvApplicationTemp.entry2 - Multiple")
            await page.click(`[id="mrvApplicationTemp.entry${attributes.number_of_entries == "single_entry" ? "1" : "2"}"]`);
            //Purpose
            await page.select('#travelPurpose', attributes.purpose == "tourist" ? "1P" : "2B");
            // await page.select('#travelPurpose', "1P");
            await sleep(500)
            await page.waitForSelector('[data-dismiss="modal"]')
            await page.click('[data-dismiss="modal"]')
            const cookies = await page.cookies();
            const cookie = cookies.map(ck => `${ck.name}=${ck.value}`).join("; ")

            C: for (let i = 0; i < 10; i++) {
                captcha = await solve_captcha(cookie)
                console.log(captcha)
                if (captcha.length === 4 && /^[a-z0-9]+$/.test(captcha)) {
                    break C;
                }
            }
            await page.type('#captcha', captcha)

            await page.click('[onclick="send()"]')
            await page.waitForNetworkIdle()
            try {
                await page.waitForSelector('#surname', { timeout: 5000 })
                break VERIFY_CAPTCHA;
            } catch (e) {
                console.log(e.message)
            }
        }
        // Page 2
        await page.type('#surname', data.passport_core_info_surname)
        await page.type('#givename', data.passport_core_info_given_name)

        //TODO : domesticId
        //DOB
        await page.select('#birthDayY', moment(data.passport_core_info_date_of_birth).format("YYYY"))
        await page.select('#birthDayM', moment(data.passport_core_info_date_of_birth).format("MM"))
        await page.select('#birthDayD', moment(data.passport_core_info_date_of_birth).format("DD"))

        await page.type('#birthPlaceCity', data.passport_additional_info_city_of_birth ?? data.passport_core_info_city_of_birth)
        await page.type('#birthPlaceCountry', get_country_name_v2(data.passport_core_info_country_of_birth))

        await page.click(`[id="mrvApplicationTemp.sex${data.passport_core_info_gender == "M" ? "1" : "2"}"]`)
        await page.select('#maritalStatus', {
            "single": "1",
            "married": "2",
            "divorced": "5",
            "widowed": "3",
            "separate": "4"
        }[data.personal_core_info_marital_status])
        await page.click('[onclick="send2()"]')
        await page.waitForNetworkIdle()
        // Page 3
        await page.waitForSelector("#passportType")
        await page.select('#passportType', "3")
        await page.type('#passportNo', data.passport_core_info_passport_number)
        await page.type('#strIssueDate', moment(data.passport_core_info_issue_date).format("YYYY/MM/DD"))
        await page.type('#strExpiryDate', moment(data.passport_core_info_expiration_date).format("YYYY/MM/DD"))

        await page.type('#issuePlace', get_country_name_v2(data.passport_core_info_issuing_authority ?? "USA"))
        //otherNationality
        if (data.passport_second_passport_nationality) {
            await page.select('#otherNationality', data.passport_second_passport_nationality)
        }
        await page.click('[onclick="send3()"]')
        await page.waitForNetworkIdle()
        // Page 4
        await page.waitForSelector('#countryAddr')
        await page.type('#countryAddr', `${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${data.personal_home_address_state} ${data.personal_home_address_zip_code}, ${get_country_name_v2(data.personal_home_address_country)}`)
        await page.type('#countryTel', data.personal_core_info_phone.phone)
        await page.type('#currentEmail', email)
        const hotel = getRandomHotel()
        await page.type('#taiwanAddr', hotel.Address)
        await page.type('#taiwanTel', hotel.Phone)

        await page.click('[onclick="send4()"]')
        await page.waitForNetworkIdle()
        // Page 5

        //ever been to Taiwan ? YES - mrvApplicationTemp.everTaiwanVisa1 / NO - mrvApplicationTemp.everTaiwanVisa2
        if (data.travel_visited_country_have_you_visited_this_country) {
            await page.click(`[id="mrvApplicationTemp.everTaiwanVisa1"]`)
            await page.select(`#everTravelPurpose1`, data.travel_visited_country_twn_purpose == "tourist" ? "1P" : "2B")

        } else {
            await page.click(`[id="mrvApplicationTemp.everTaiwanVisa2"]`)
        }

        await page.type('#strProposeArrivalDate', moment(data.service_core_info_entry_date).format("YYYY/MM/DD"))
        await page.type('#strProposeDepartureDate', moment(data.service_core_info_exit_date).format("YYYY/MM/DD"))

        if (attributes.purpose == "business") {
            await page.select('#B_occupation ', {
                business_person: '5',
                company_employee: '16',
                retired: '19',
                student: '14',
            }[data.personal_occupation_occupation])
            await page.type('#B_employerOrSchool', data.employee_company_info_company_school_name)
            await page.type('#B_relationSchoolCompany', data.employee_company_reference_in_destination_company_name)
            await page.type('#B_relationTel', data.employee_company_reference_in_destination_phone.phone)

        }

        await page.click('[onclick="send5()"]')
        await page.waitForNetworkIdle()
        //Page 6
        await page.click(`[id="mrvApplicationTemp.appQuestion1${data.additional_question_twn_twn_other_1 == true ? "1" : "2"}"]`)
        await page.click(`[id="mrvApplicationTemp.appQuestion2${data.additional_question_twn_twn_other_2 == true ? "1" : "2"}"]`)
        await page.click(`[id="mrvApplicationTemp.appQuestion3${data.additional_question_twn_twn_other_3 == true ? "1" : "2"}"]`)
        await page.click(`[id="mrvApplicationTemp.appQuestion4${data.additional_question_twn_twn_other_4 == true ? "1" : "2"}"]`)
        await page.click(`[id="mrvApplicationTemp.appQuestion5${data.additional_question_twn_twn_other_5 == true ? "1" : "2"}"]`)
        await page.click(`[id="mrvApplicationTemp.appQuestion6${data.additional_question_twn_twn_other_6 == true ? "1" : "2"}"]`)
        await page.click(`[id="mrvApplicationTemp.appQuestion7${data.additional_question_twn_twn_other_7 == true ? "1" : "2"}"]`)
        await page.click(`[id="mrvApplicationTemp.appQuestion8${data.additional_question_twn_twn_other_8 == true ? "1" : "2"}"]`)

        await page.click('[onclick="send6()"]')
        await page.waitForNetworkIdle()
        //Page 7 
        //Area Asia  - West Asia - Africa - EU - NA - Central & South Ameria
        await page.select('#areaId', "5")
        //Embassy Location - TODO
        await page.select('#officeId ', {
            "twn_embassy_la": '502',
            "twn_embassy_sf": '503',
            "twn_embassy_ny": '504',
        }[data.additional_question_core_info_twn_embassy_wish_apply_to])
        //self fill
        await page.click('[id="mrvApplicationTemp.appAgent1"]')

        await page.click('[onclick="send7()"]')
        await page.waitForNetworkIdle()

        const document_id = await page.$eval('#form1 > div > div.pageframe > div.formbox > label', el => el.textContent)

        await recorder.stop();
        await browser.close()
        return {
            document_id,
            email,
            passport_number: data.passport_core_info_passport_number,
            created_at: moment().format('DD/MM/YYYY')
        }
        // NATURALIZATION
        // education
        // return resp.data
    } catch (error) {
        await recorder.stop();
        console.log(error)
        console.log(error.message)
        await browser.close()
        return null
    }

}


const solve_captcha = async (cookie) => {
    const resp = await axios({
        method: 'get',
        url: 'https://visawebapp.boca.gov.tw/BOCA_EVISA/captcha/captcha.jpg',
        headers: {
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Cookie': cookie
        },
        responseType: "arraybuffer"
    })

    var data = new FormData();
    data.append('file', resp.data);

    const captcha = await get_image_captcha_text(resp.data)
    return captcha
}


const MAX_RETRY = 2;
const create_form = async (order, task_id) => {
    const task = order.tasks.find(v => v.id === task_id)
    const pod_data = task.input_pod_values
    let form = null
    for (let i = 0; i < MAX_RETRY; i++) {
        form = await twn_visa_form(pod_data, order.service.attributes)
        if (form != null) {
            break
        }
    }
    const result = {
        success: false,
        form_callback: {}
    }
    if (form != null) {
        result.form_callback = form
        result.success = true
    }
    const file_name = `Application_Form_${pod_data.passport_core_info_surname}_${pod_data.passport_core_info_given_name}_${moment().unix()}.pdf`

    const buckets = JSON.parse(process.env.ad_s3)
    await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${RECODER_NAME}`, fs.readFileSync(RECODER_NAME))

    fs.rmSync('images', { recursive: true })
    fs.rmSync(RECODER_NAME, { recursive: true })

    result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${RECODER_NAME}`
    console.log("STEPS:", result.form_callback.steps)
    console.log("Passport#:", result.form_callback.passport_number)
    console.log("Form Id:", result.form_callback.document_id)
    return result
}

const main = async (order_id, task_id) => {
    try {
        const resp = await get_order_detail(order_id)
        const data = await create_form(resp.data, task_id)
        return data
    } catch (error) {
        console.log(error)
    }
}



// main(4872, 5728)
module.exports = { main, create_form }