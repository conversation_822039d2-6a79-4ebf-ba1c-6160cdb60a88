const fs = require('fs');
const _ = require('lodash');
const jsdom = require("jsdom");
const { JSDOM } = jsdom;
const bluebird = require('bluebird');
var form_data = require('form-data');
const moment = require('moment')
const axios = require('axios')
const iso = require('iso-3166-1');
const pdfUtil = require('pdf-to-text');
const { create_folder, s3_url_to_bucket_key, get_order_detail, prepareDataByVisaPods, s3_download, s3_upload, capitalize, import_photo_to_application_form, remove_folder, s3_upload_buff, retry_operation } = require('../../shared/helpers');

const data_mapping = require('./data_mapping')

const FRAME = { "x": 35, "y": 725, "width": 89, "height": 105 }

fetch.Promise = bluebird;
var _cookie;

const do_step_1 = () => {
    return new Promise((resolve, reject) => {
        fetch(
            "http://mienthithucvk.mofa.gov.vn/%C4%90%C4%83ngk%C3%BD/Khaitr%E1%BB%B1ctuy%E1%BA%BFn/tabid/104/VE4NCommand/introduction/Default.aspx",
            {
                "credentials": "omit",
                "headers": {
                    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3",
                    "accept-language": "en-US,en;q=0.9",
                    "cache-control": "max-age=0",
                    "upgrade-insecure-requests": "1",
                    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.100 Safari/537.36",
                },
                "referrer": "http://mienthithucvk.mofa.gov.vn/%C4%90%C4%83ngk%C3%BD/Khaitr%E1%BB%B1ctuy%E1%BA%BFn/tabid/104/VE4NCommand/registration/Default.aspx", "referrerPolicy": "no-referrer-when-downgrade",
                "body": null,
                "method": "GET",
                "mode": "cors"
            }
        ).then(res => {
            const headers = res.headers;
            var cookies = headers.get('set-cookie');
            cookies = cookies.split(/;|,/);
            const ASPXANONYMOUS = _.find(cookies, c => _.startsWith(c.trim(), '.ASPXANONYMOUS'));
            const ASPNETSessionId = _.find(cookies, c => _.startsWith(c.trim(), 'ASP.NET_SessionId'));
            _cookie = `${ASPXANONYMOUS ? ASPXANONYMOUS.trim() : ASPXANONYMOUS}; ${ASPNETSessionId.trim()}; language=en-US`
            resolve(res.text());
        }).catch(err => reject(err));
    });
};
const do_step_2 = ({ form }) => {
    return new Promise((resolve, reject) => {
        fetch(
            "http://mienthithucvk.mofa.gov.vn/%C4%90%C4%83ngk%C3%BD/Khaitr%E1%BB%B1ctuy%E1%BA%BFn/tabid/104/VE4NCommand/introduction/Default.aspx",
            {
                "credentials": "include",
                "headers": {
                    ...form.getHeaders(),
                    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3",
                    "accept-language": "en-US,en;q=0.9",
                    "cache-control": "max-age=0",
                    "upgrade-insecure-requests": "1",
                    "cookie": _cookie,
                }
                , "referrer": "http://mienthithucvk.mofa.gov.vn/%C4%90%C4%83ngk%C3%BD/Khaitr%E1%BB%B1ctuy%E1%BA%BFn/tabid/104/VE4NCommand/introduction/Default.aspx", "referrerPolicy": "no-referrer-when-downgrade",
                "body": form,
                "method": "POST",
                "mode": "cors",
            }
        ).then(res => {
            resolve(res.text())
        }).catch(err => reject(err));
    });
}
const do_step_3 = ({ form }) => {
    return new Promise((resolve, reject) => {
        fetch(
            "http://mienthithucvk.mofa.gov.vn/%C4%90%C4%83ngk%C3%BD/Khaitr%E1%BB%B1ctuy%E1%BA%BFn/tabid/104/VE4NCommand/registration/Default.aspx",
            {
                "credentials": "include",
                "headers": {
                    ...form.getHeaders(),
                    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3",
                    "accept-language": "en-US,en;q=0.9",
                    "cache-control": "max-age=0",
                    "upgrade-insecure-requests": "1",
                    "cookie": _cookie,
                },
                "referrer": "http://mienthithucvk.mofa.gov.vn/%C4%90%C4%83ngk%C3%BD/Khaitr%E1%BB%B1ctuy%E1%BA%BFn/tabid/104/VE4NCommand/registration/Default.aspx", "referrerPolicy": "no-referrer-when-downgrade",
                "body": form,
                "method": "POST",
                "mode": "cors",
            }
        ).then(res => {
            resolve(res.text());
        }).catch(err => {
            reject(err);
        });
    });
}
const do_step_4 = async (form) => {
    const resp = await axios({
        method: 'post',
        url: "http://mienthithucvk.mofa.gov.vn/%C4%90%C4%83ngk%C3%BD/Khaitr%E1%BB%B1ctuy%E1%BA%BFn/tabid/104/VE4NCommand/finishregistration/Default.aspx",
        data: form,
        headers: {
            ...form.getHeaders(),
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3",
            "accept-language": "en-US,en;q=0.9",
            "cache-control": "max-age=0",
            "upgrade-insecure-requests": "1",
            "cookie": _cookie,
            "referrer": "http://mienthithucvk.mofa.gov.vn/%C4%90%C4%83ngk%C3%BD/Khaitr%E1%BB%B1ctuy%E1%BA%BFn/tabid/104/VE4NCommand/finishregistration/Default.aspx",
        },
        responseType: 'arraybuffer'
    })
    return resp.data

}
const create_form = async ({ order_id, input_pods, tag, s3 }) => {
    const podData = prepareDataByVisaPods(input_pods)
    console.log(JSON.stringify(podData, null, 2))
    var body = await do_step_1()
    console.log('completed step 1')
    var dom = new JSDOM(body);
    var inputs = dom.window.document.querySelectorAll('input');
    var form = new form_data();
    _.map(inputs, input => form.append(input.name, input.value));
    body = await do_step_2({ form })
    console.log('completed step 2')
    dom = new JSDOM(body);
    inputs = dom.window.document.querySelectorAll('input');
    var req_body = {};
    _.map(inputs, input => req_body[input.name] = input.value || "");

    // prepare data

    if (podData.document_copy_of_document_were_you_born_vn || podData.document_copy_of_document_were_you_born_vn) {
        podData.passport_core_info_country_of_birth = podData.passport_core_info_country_of_birth || "VNM"
        podData.passport_core_info_birth_nationality = podData.passport_core_info_birth_nationality || "VNM"
        podData.passport_additional_info_birth_nationality = podData.passport_additional_info_birth_nationality || "VNM"
    }

    var { country: country_of_birth } = iso.whereAlpha3(podData.passport_core_info_country_of_birth || "VNM")
    var { country: issuing_authority } = iso.whereAlpha3(podData.passport_core_info_issuing_authority || "VNM")
    var { country: home_address_country } = iso.whereAlpha3(podData.personal_home_address_country)


    req_body['dnn$ctr408$Desktop$subctr$txtFirstName'] = podData.passport_core_info_given_name
    req_body['dnn$ctr408$Desktop$subctr$txtLastName'] = podData.passport_core_info_surname

    req_body['dnn$ctr408$Desktop$subctr$txtFirstNameVN'] = podData.passport_core_info_given_name
    req_body['dnn$ctr408$Desktop$subctr$txtMiddleNameVN'] = ''
    req_body['dnn$ctr408$Desktop$subctr$txtLastNameVN'] = podData.passport_core_info_surname


    if (podData.passport_core_info_gender != 'M') {

        delete req_body['dnn$ctr408$Desktop$subctr$chkSex_Male']
        req_body['dnn$ctr408$Desktop$subctr$chkSex_Female'] = 'on';
    }
    req_body['dnn$ctr408$Desktop$subctr$txtDayOfBirth'] = moment(podData.passport_core_info_date_of_birth).format('DD');
    req_body['dnn$ctr408$Desktop$subctr$txtMonthOfBirth'] = moment(podData.passport_core_info_date_of_birth).format('MM');
    req_body['dnn$ctr408$Desktop$subctr$txtYearOfBirth'] = moment(podData.passport_core_info_date_of_birth).format('YYYY');
    req_body['dnn$ctr408$Desktop$subctr$txtPlaceOfBirth'] = country_of_birth || podData.passport_core_info_birth_nationality
    req_body['dnn$ctr408$Desktop$subctr$ddlNationalityAtBirth'] = podData.passport_additional_info_birth_nationality || podData.passport_core_info_birth_nationality
    req_body['dnn$ctr408$Desktop$subctr$ddlNationalityAtPresent'] = podData.passport_core_info_nationality;

    if (podData.document_copy_of_passport_document_type === 'us_green_card') {
        req_body['dnn$ctr408$Desktop$subctr$txtPRDNo'] = podData.passport_core_info_passport_number;
        req_body['dnn$ctr408$Desktop$subctr$diPRDExpireDate$txtDateInput'] = moment(podData.passport_core_info_expiration_date).format('DD/MM/YYYY');
        req_body['dnn$ctr408$Desktop$subctr$txtPRDIssuingAuthority'] = issuing_authority;
    } else {
        req_body['dnn$ctr408$Desktop$subctr$txtPassportNo'] = podData.passport_core_info_passport_number;
        req_body['dnn$ctr408$Desktop$subctr$diPassportExpireDate$txtDateInput'] = moment(podData.passport_core_info_expiration_date).format('DD/MM/YYYY');
        req_body['dnn$ctr408$Desktop$subctr$txtPassportIssuingAuthority'] = issuing_authority;
    }
    if (!podData.personal_occupation_occupation) {
        if (moment(podData.passport_core_info_date_of_birth).isBefore(moment().subtract(21, 'years'))) {
            podData.personal_occupation_occupation = 'business_person'
        } else {
            podData.personal_occupation_occupation = 'student'
        }
    }
    req_body["dnn$ctr408$Desktop$subctr$txtOccupation"] = data_mapping.OCCUPATION[podData.personal_occupation_occupation];
    req_body["dnn$ctr408$Desktop$subctr$txtAddressAtPresent"] = capitalize(`${podData.personal_home_address_street_number_name}, ${podData.personal_home_address_city}, ${podData.personal_home_address_state}${podData.personal_home_address_zip_code ? ' ' + podData.personal_home_address_zip_code : ''}, ${home_address_country}`);
    if (typeof podData.personal_core_info_phone == 'string') {
        req_body["dnn$ctr408$Desktop$subctr$txtPhoneNumber"] = podData.personal_core_info_phone;
    } else {
        req_body["dnn$ctr408$Desktop$subctr$txtPhoneNumber"] = podData.personal_core_info_phone.phone;
    }

    const random_hotel = _.sample(data_mapping.HOTELS)
    req_body["dnn$ctr408$Desktop$subctr$txtContactAddressInVN"] = random_hotel.Name;
    req_body["dnn$ctr408$Desktop$subctr$txtContactPhoneNumber"] = random_hotel.Phone;
    req_body["dnn$ctr408$Desktop$subctr$chkSex_Child1_Male"] = "on";
    req_body["dnn$ctr408$Desktop$subctr$chkSex_Child2_Male"] = "on";
    req_body["dnn$ctr408$Desktop$subctr$chkSex_Child3_Male"] = "on";
    req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$6"] = "on";
    req_body["dnn$ctr408$Desktop$subctr$cblDocuments2$1"] = "on";

    if (tag.includes('stamped')) {
        req_body["dnn$ctr408$Desktop$subctr$chkPrintType_1"] = "";
        req_body["dnn$ctr408$Desktop$subctr$chkPrintType_2"] = "on"; // A stamped certificate
    } else {
        req_body["dnn$ctr408$Desktop$subctr$chkPrintType_1"] = "on"; // A detached certificate
        req_body["dnn$ctr408$Desktop$subctr$chkPrintType_2"] = "";
    }

    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$0"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$1"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$2"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$3"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$4"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$5"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$6"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$7"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$8"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$9"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$10"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$11"]
    req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$12"] = "on"
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments1$13"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments2$0"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments2$1"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments2$2"]
    req_body["dnn$ctr408$Desktop$subctr$cblDocuments2$3"] = "on"
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments2$4"]
    delete req_body["dnn$ctr408$Desktop$subctr$cblDocuments2$5"]


    req_body["dnn$ctr408$Desktop$subctr$txtGiayToKhac1"] = "";
    req_body["dnn$ctr408$Desktop$subctr$txtGiayToKhac2"] = "";
    req_body["dnn$ctr408$Desktop$subctr$txtGiayToKhac3"] = "";



    req_body["dnn$ctr408$Desktop$subctr$ddlCountry"] = "USA";
    req_body["dnn$ctr408$Desktop$subctr$cmdFinish"] = "Hoàn thành";
    delete req_body[""]

    console.log(JSON.stringify(req_body, null, 2))

    var form = new form_data();
    for (const k in req_body) {
        form.append(k, req_body[k] ?? '')
    }
    console.log(JSON.stringify(req_body, null, 2))
    body = await do_step_3({ form })
    console.log('completed step 3')
    dom = new JSDOM(body);
    inputs = dom.window.document.querySelectorAll('input');
    req_body = {};
    _.map(inputs, input => req_body[input.name] = input.value || "");
    req_body['dnn$ctr408$Desktop$subctr$ibDownload.x'] = "0";
    req_body['dnn$ctr408$Desktop$subctr$ibDownload.y'] = "0";
    req_body = _.pick(
        req_body,
        [
            '__EVENTTARGET',
            '__EVENTARGUMENT',
            '__VSTATE',
            'dnn$dnnMENU$RadMenu1',
            'dnn$ctr408$Desktop$subctr$ibDownload.x',
            'dnn$ctr408$Desktop$subctr$ibDownload.y',
            'dnn$ctr408$Desktop$subctr$txtEmail',
            'ScrollTop',
            '__dnnVariable',
            '__VIEWSTATE',
        ]
    )

    var form = new form_data();
    _.map(req_body, (v, k) => form.append(k, v));
    let folder_path = `${__dirname}/`
    //download Pdf

    //create folder visaId
    create_folder(`${folder_path}${order_id}/`)
    folder_path += order_id
    const name_pdf = `${folder_path}/application_${moment().valueOf()}.pdf`
    const step4_resp = await do_step_4(form)
    fs.writeFileSync(name_pdf, step4_resp)
    console.log('completed step 4')

    const pdfText = await new Promise(r => {
        pdfUtil.pdfToText(name_pdf, { from: 0, to: 1 }, function (err, data) {
            if (err) throw (err);
            console.log(data); //print text    
            r(data)
        });
    });

    const document_id = pdfText.split('\n')[0].trim()

    // importPhotoToApplicationForm
    let final_pdf = step4_resp
    let photo_image = podData.photo_passport_photo_copy_of_photo
    if (photo_image) {
        const photo_resp = await axios.get(photo_image, { responseType: 'arraybuffer' })
        final_pdf = await import_photo_to_application_form(photo_resp.data, step4_resp, FRAME);
    }
    //save PDF to S3
    const form_file = await s3_upload_buff(s3.form_upload_s3_bucket, s3.form_upload_s3_key, final_pdf)
    //delete folder
    remove_folder(folder_path + '/')
    console.log('DONE');
    return {
        form_callback: { document_id },
        form_file,
    }
}




const main = async (order_id, task_id) => {
    try {
        const resp = await get_order_detail(order_id)
        const task = resp.data.tasks.find(v => v.id == task_id)
        const s3_buckets = JSON.parse(process.env.ad_s3)
        const s3 = {
            form_upload_s3_bucket: s3_buckets.ariadirect_prod_applications,
            form_upload_s3_key: `visa/Application_Form_${order_id}_${moment().unix()}.pdf`

        }

        const data = await retry_operation(async () => {
            return await create_form({
                order_id: order_id,
                input_pods: task.input_pods,
                tag: resp.data.service.tag,
                s3,
            })
        }, 3, 'create form error after 3 attempts')

        return {
            success: true,
            form_file: data.form_file,
            form_callback: {
                "form_name": 'Application Form',
                "file_name": `Order ${order_id} Application Form`,
                "file_url": data.form_file,
                "document_id": data.form_callback.document_id,
            }
        }
    } catch (error) {
        console.log(error)
    }
}

const MOCK = {
    "order_id": 3834,
    "task_id": 4458,
}
// main(MOCK.order_id, MOCK.task_id)
module.exports = { main, create_form }