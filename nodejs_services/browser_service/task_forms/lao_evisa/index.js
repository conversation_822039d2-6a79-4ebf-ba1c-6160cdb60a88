const fs = require('fs');
const axios = require('axios');
const moment = require('moment');
const puppeteer = require('puppeteer');
const jimp = require('jimp');
const AWS = require('aws-sdk');
const { ALL_COUNTRY } = require('./data_mapping')
const { URL } = require('url');
const { create_zip, s3_upload_buff, s3_url_to_bucket_key, get_order_task_pod_data, get_country_name_v2 } = require('../../shared/helpers');
const { PuppeteerScreenRecorder } = require('puppeteer-screen-recorder');
const BTAPI = require('../../shared/bt_api');
const fakeAddress = require("fake-address-generator");


const { get_image_captcha_text } = require('../../shared/captcha');
const tmp = require('tmp');
let DEBUG = false
const RECODER_NAME = `Recorder_${moment().unix()}.mp4`
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));



const visa_form = async (order, task, pod_data) => {
    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })

    let page = null
    let browser = null
    let recorder = null
    const start_time = moment().unix()
    try {
        browser = await puppeteer.launch({ headless: DEBUG ? false : "new", args: ['--no-sandbox', '--disable-setuid-sandbox'] });
        page = await browser.newPage();
        page.setDefaultTimeout(60000);
        recorder = new PuppeteerScreenRecorder(page);
        await recorder.start(RECODER_NAME);
        await page.goto('https://www.laoevisa.gov.la/apply');
        await page.waitForNetworkIdle({ waitUntil: 'networkidle0' });

        const ad_email = process.env.ad_env === 'prod' ? '<EMAIL>' : '<EMAIL>'
        await sleep(2000)
        await page.select('[name="countryOfPassport"]', ALL_COUNTRY.find(v => v.iso3 === pod_data.passport_core_info_nationality)?.value);
        await page.select('[name="passport_type"]', 'ordinary');
        await page.type('[name="client_email"]', ad_email);
        while (true) {
            await sleep(2000)

            const captcha_element = await page.$('[alt="captcha"]')
            const buffer = await captcha_element.screenshot({ encoding: 'binary' });
            const captcha = await get_image_captcha_text(buffer)
            await page.evaluate(() => { document.querySelector('[name="verify_code"]').value = '' })
            await page.type('[name="verify_code"]', captcha);
            await page.click('[type="submit"]')
            await page.waitForNetworkIdle({ waitUntil: 'networkidle0' });
            const captcha_error = await page.evaluate(() => {
                return document.querySelector('form > div:nth-child(1) > div > div:nth-child(5) > div.text-danger')?.innerText?.trim()
            })
            if (captcha_error === 'Security code is wrong!') {
                await page.click(`div.border.img-rounded.p-2.captcha.mb-2 > i`)
                continue
            }
            break
        }
        const btAPI = new BTAPI();
        let selected_email = null
        while (moment().unix() > start_time && moment().unix() - start_time < 120) {
            const emails = await btAPI.getEmails('<EMAIL>')
            if (emails?.length > 0 && emails[0].time > start_time) {
                selected_email = emails[0]
                break
            }
            await sleep(3000)
        }
        const verifyCode = selected_email.html.match(/Verification Code[^]*?>(\d{4})</)[1];
        await page.type('#pin1', verifyCode, { delay: 100 });
        await page.click('[type="submit"]')
        await page.waitForNetworkIdle({ waitUntil: 'networkidle0' });

        {
            const photoUpload = await page.$("#inputPhotoFile");
            const photo_resp = await axios.get(pod_data.photo_passport_photo_copy_of_photo, { responseType: 'arraybuffer' });
            const image = await jimp.read(Buffer.from(photo_resp.data));
            const image_buff = await image.resize(500, jimp.AUTO).getBufferAsync(jimp.MIME_JPEG)
            const tmp_file = tmp.fileSync({ mode: 0o644, prefix: 'temp-', postfix: '.jpeg' });
            fs.writeFileSync(tmp_file.name, image_buff);
            await photoUpload.uploadFile(tmp_file.name);
            await page.waitForNetworkIdle({ waitUntil: 'networkidle0' });
        }

        await page.type('#passportNo', pod_data.passport_core_info_passport_number);
        await sleep(5000)

        await page.select('#travelPurpose', {
            "tourist": "1P",
            "business": "2B",
        }[order.service.attributes.purpose])

        await sleep(2000)
        await page.click('[onclick="send2()"]')
        await sleep(5000)

        {
            const photoUpload = await page.$("#photo-upload");
            const photo_resp = await axios.get(pod_data.photo_passport_photo_copy_of_photo, { responseType: 'arraybuffer' });
            const image = await jimp.read(Buffer.from(photo_resp.data));
            const image_buff = await image.resize(500, jimp.AUTO).getBufferAsync(jimp.MIME_JPEG)
            const tmp_file = tmp.fileSync({ mode: 0o644, prefix: 'temp-', postfix: '.jpeg' });
            fs.writeFileSync(tmp_file.name, image_buff);
            await photoUpload.uploadFile(tmp_file.name);
            await page.waitForNetworkIdle({ waitUntil: 'networkidle0' });
        }

        {
            const photoUpload = await page.$("#bioPage");
            const photo_resp = await axios.get(pod_data.document_copy_of_passport_copy_of_passport_main_page, { responseType: 'arraybuffer' });
            const image = await jimp.read(Buffer.from(photo_resp.data));
            const image_buff = await image.resize(500, jimp.AUTO).getBufferAsync(jimp.MIME_JPEG)
            const tmp_file = tmp.fileSync({ mode: 0o644, prefix: 'temp-', postfix: '.jpeg' });
            fs.writeFileSync(tmp_file.name, image_buff);
            await photoUpload.uploadFile(tmp_file.name);
            await page.waitForNetworkIdle({ waitUntil: 'networkidle0' });
        }

        await sleep(5000)
        await page.type('#surname', pod_data.passport_core_info_surname);
        await page.type('#givename', pod_data.passport_core_info_given_name);
        await page.select('#birthDayY', moment(pod_data.passport_core_info_date_of_birth).format('YYYY'));
        await page.select('#birthDayM', moment(pod_data.passport_core_info_date_of_birth).format('MM'));
        await page.select('#birthDayD', moment(pod_data.passport_core_info_date_of_birth).format('DD'));
        await page.type('#birthPlaceCity', get_country_name_v2(pod_data.personal_home_address_country));
        await page.type('#birthPlaceCountry', pod_data.personal_home_address_city);
        await page.click({
            'M': '[name="mrvApplicationTemp.sex"][value="M"]',
            'F': '[name="mrvApplicationTemp.sex"][value="F"]',
        }[pod_data.passport_core_info_gender])

        await page.click('[onclick="send3()"]')
        await page.waitForNetworkIdle({ waitUntil: 'networkidle0' });
        await sleep(3000)

        await page.select('#passportType', '3'); // Regular
        await page.type('#strIssueDate', moment(pod_data.passport_core_info_issue_date).format('YYYY/MM/DD'));
        await page.type('#strExpiryDate', moment(pod_data.passport_core_info_expiration_date).format('YYYY/MM/DD'));
        await page.type('#issuePlace', get_country_name_v2(pod_data.passport_core_info_country_of_birth)); // TODO
        await page.click('[onclick="send4()"]')
        await page.waitForNetworkIdle({ waitUntil: 'networkidle0' });
        await sleep(3000)

        const first_address = await new Promise(r => fakeAddress.Generate({
            country: "us",
            sex: pod_data.passport_core_info_gender === 'F' ? "female" : "male",
        }, (err, resp) => {
            r(resp);
        }));

        await page.type('#countryAddr', `${first_address.address.street}, ${first_address.address.city}, ${first_address.address.state} ${first_address.address.zip}, United States`);
        await page.type('#countryTel', first_address.address.phone);
        await page.type('#currentAddr', `${first_address.address.street}, ${first_address.address.city}, ${first_address.address.state} ${first_address.address.zip}, United States`);
        await page.type('#currentTel', first_address.address.phone);
        await page.click('[onclick="send5()"]')
        await page.waitForNetworkIdle({ waitUntil: 'networkidle0' });
        await sleep(3000)

        await page.type('#strProposeArrivalDate', moment(pod_data.service_core_info_entry_date).format('YYYY/MM/DD'));
        await page.type('#strProposeDepartureDate', moment(pod_data.service_core_info_exit_date).format('YYYY/MM/DD'));
        await page.type('#P_taiwanAddr', 'NA');
        await page.type('#P_taiwanTel', 'NA');
        await page.type('#P_employerOrSchool', 'NA');
        await page.type('#P_companyAddr', 'NA');
        await page.type('#P_companyTel', 'NA');
        await page.type('#P_relationName', 'NA');
        await page.type('#P_relationTel', 'NA');
        await page.type('#P_relationShip', 'NA');
        await page.click('[onclick="send6()"]')
        await page.waitForNetworkIdle({ waitUntil: 'networkidle0' });
        await sleep(3000)


        await page.click('[name="mrvApplicationTemp.appQuestion1"][value="N"]');
        await page.click('[name="mrvApplicationTemp.appQuestion2"][value="N"]');
        await page.click('[name="mrvApplicationTemp.appQuestion3"][value="N"]');
        await page.click('[name="mrvApplicationTemp.appQuestion4"][value="N"]');
        await page.click('[name="mrvApplicationTemp.appQuestion5"][value="N"]');
        await page.click('[name="mrvApplicationTemp.appQuestion6"][value="N"]');
        await page.click('[name="mrvApplicationTemp.appQuestion7"][value="N"]');
        await page.click('[name="mrvApplicationTemp.appQuestion8"][value="N"]');

        await page.click('[onclick="send7()"]')
        await page.waitForNetworkIdle({ waitUntil: 'networkidle0' });
        await sleep(3000)

        await page.click('[name="mrvApplicationTemp.appAgent"][value="N"]');
        await page.click('[onclick="send8()"]')
        await page.waitForNetworkIdle({ waitUntil: 'networkidle0' });
        await sleep(3000)

        const document_id = await page.evaluate(() => {
            return document.querySelector('#form1 > div > div.pageframe > div.formbox > label').innerText
        })

        await recorder.stop();
        await browser.close()
        return {
            document_id,
            email: ad_email,
            passport_number: pod_data.passport_core_info_passport_number,
            created_at: moment().format('DD/MM/YYYY'),
            tracking_url: 'https://visawebapp.boca.gov.tw/BOCA_EVISA/home.do',
        }
    } catch (error) {
        console.error(error)
        console.log(error.message)
        await recorder.stop();
        await browser.close()
        return null
    }

}

const ZIP_NAME = "steps.zip"

const create_form = async (order, task, pod_data) => {
    console.log(JSON.stringify(pod_data, null, 2))

    const result = {
        success: false,
        form_file: "",
        form_callback: {},
    }

    let MAX_RETRY = 3
    let form_callback = await visa_form(order, task, pod_data)
    if (!form_callback && MAX_RETRY > 0) {
        MAX_RETRY--
        form_callback = await visa_form(pod_data)
    }
    if (form_callback) {
        result.success = true
        result.form_callback = form_callback
    }

    const buckets = JSON.parse(process.env.ad_s3)
    await create_zip(ZIP_NAME, 'images/')

    await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${RECODER_NAME}`, fs.readFileSync(RECODER_NAME))

    fs.rmSync('images', { recursive: true })
    fs.unlinkSync(RECODER_NAME)

    result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${RECODER_NAME}`
    console.log("STEPS:", result.form_callback.steps)
    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form(order, task, pod_data)
        return data
    } catch (error) {
        console.log(error)
    }
}

module.exports = { main, create_form }