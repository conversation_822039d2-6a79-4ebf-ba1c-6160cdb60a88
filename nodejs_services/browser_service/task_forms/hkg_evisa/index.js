const axios = require('axios');
const iso = require('iso-3166-1');
const moment = require('moment');
const archiver = require('archiver');
const shared_data_mapping = require('../../shared/data_mapping')
const helper = require('./helper');
const PhoneNumber = require('libphonenumber-js');
const BTAPI = require('../../shared/bt_api');
const { get_country_name_v2, get_order_task_pod_data } = require('../../shared/helpers');
const data_mapping = require('../../shared/data_mapping');
const convert_state = (state) => data_mapping.us_state[state] ? data_mapping.us_state[state] : state

// https://www.gov.hk/en/apps/immdes2applyvisit_transit.htm
const create_form = async (order, task, data) => {
    const url_with_ticket_id = await helper.get_url_redirect('https://eservices.es2.immd.gov.hk/surgecontrolgate/ticket/getTicketGet?svcId=838&applicationId=838&language=en&country=US&appType=VT')
    console.log(url_with_ticket_id)
    const ticket_id_matches = url_with_ticket_id.match(/ticketId=([^&]+)/);
    const ticket_id = ticket_id_matches ? ticket_id_matches[1] : null;

    console.log(ticket_id);
    const btAPI = new BTAPI();
    let ad_email = await btAPI.randomEmail("hkg_evisa")

    const transaction_resp = await axios.get('https://webapp.es2.immd.gov.hk/applies2-services/eservice2/common/getTrnNew?appId=838')
    console.log(transaction_resp.data)
    const transaction_id = transaction_resp.data.result
    const upload_photo_resp = await helper.upload_file(data.photo_passport_photo_copy_of_photo, 'PHOTO', transaction_id, ticket_id)
    const upload_pp_resp = await helper.upload_file(data.document_copy_of_passport_copy_of_passport_main_page, 'TRAVEL_DOC', transaction_id, ticket_id)
    const order_config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://webapp.es2.immd.gov.hk/applies2-services/eservice2/pnv/visit/saveCase?lang=',
        headers: {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,zh-TW;q=0.8,zh;q=0.7,zh-CN;q=0.6',
            'Origin': 'https://webapp.es2.immd.gov.hk',
            'Referer': 'https://webapp.es2.immd.gov.hk/applies2-client/visa-app-duet/en-US/form1003A',
            'ticketId': ticket_id
        },
        data: {
            "depDobDay": "",
            "depDobMonth": "",
            "depDobYear": "",
            "depPob": "",
            "depDeclaredCnt": 0,
            "companyKey": "",
            "engGivenName": "",
            "applicationCount": 0,
            "appType": "2VT4",
            "form1003A": {
                "chinName": "",
                "maidenSurName": "",
                "engSurName": "",
                "engGivenName": data.passport_core_info_surname + ', ' + data.passport_core_info_given_name,
                "alias": "",
                "male": data.passport_core_info_gender === 'M' ? true : false,
                "female": data.passport_core_info_gender === 'F' ? true : false,
                "dobDay": moment(data.passport_core_info_date_of_birth).format('DD'),
                "dobMonth": moment(data.passport_core_info_date_of_birth).format('MM'),
                "dobYear": moment(data.passport_core_info_date_of_birth).format('YYYY'),
                "maritalStatus": {
                    "single": "B",
                    "married": "M",
                    "divorced": "D",
                    "widowed": "W",
                }[data.personal_core_info_marital_status],
                "maritalOther": "",
                "nationality": data.passport_core_info_nationality,
                "natOther": "",
                "placeOfBirth": iso.whereAlpha3(data.passport_core_info_country_of_birth).country,
                "travelDocIssueDay": moment(data.passport_core_info_issue_date).format('DD'),
                "travelDocIssueMonth": moment(data.passport_core_info_issue_date).format('MM'),
                "travelDocIssueYear": moment(data.passport_core_info_issue_date).format('YYYY'),
                "travelDocExpDay": moment(data.passport_core_info_expiration_date).format('DD'),
                "travelDocExpMonth": moment(data.passport_core_info_expiration_date).format('MM'),
                "travelDocExpYear": moment(data.passport_core_info_expiration_date).format('YYYY'),
                "travelDocType": "P",
                "travelDocNo": data.passport_core_info_passport_number,
                "travelDocIssuePlace": iso.whereAlpha3(data.passport_core_info_issuing_authority).country,
                "tdIncluded": "N",
                "contactNo": PhoneNumber(data.personal_core_info_phone.phone).nationalNumber ?? "",
                "faxNo": "",
                "phoneExt": "",
                "email": ad_email,
                "reEmail": ad_email,
                "faxNoCtryCd": "",
                "contactNoCtryCd": PhoneNumber(data.personal_core_info_phone.phone).countryCallingCode ?? "",
                "currentDomicile": iso.whereAlpha3(data.passport_core_info_nationality).country,
                "hkIdValue1": "",
                "hkIdValue2": "",
                "residenceMonth": (moment().diff(moment(data.passport_core_info_date_of_birth), 'months') % 12).toString(),
                "residenceYear": moment().diff(moment(data.passport_core_info_date_of_birth), 'years').toString(),
                "acquiredResidence": "Y",
                "deposit": data.employee_company_info_deposit ?? "", // check deposit
                "monthSalary": data.employee_company_info_monthly_salary ?? "",
                "occupation": shared_data_mapping.occupation[data.personal_occupation_occupation],
                "post": "",
                "employCompanyInfo": "",
                "purpose1": data_mapping.purpose[data.travel_travel_history_hkg_entry_history?.[0]?.[0].value?.fe ?? ""] ?? "",
                "purpose2": data_mapping.purpose[data.travel_travel_history_hkg_entry_history?.[1]?.[0].value.fe ?? ""] ?? "",
                "purpose3": data_mapping.purpose[data.travel_travel_history_hkg_entry_history?.[2]?.[0].value.fe ?? ""] ?? "",
                "proposedEntryDay": moment(data.service_core_info_entry_date).format('DD'),
                "proposedEntryMonth": moment(data.service_core_info_entry_date).format('MM'),
                "proposedEntryYear": moment(data.service_core_info_entry_date).format('YYYY'),
                "leisureItineraryInfo": "",// leisure itinerary info
                "transitItineraryInfo": "",
                "otherItineraryInfo": "",
                "flightInfoArr": "",
                "flightInfoDep": "",
                "visitCompanyName": "",
                "companyPersonName": "",
                "companyPersonPost": "",
                "officeTelNo": "",
                "businessActivity": "",
                "familyName": "",
                "personRelationship": "",
                "noInfoReason": "I CAN PAY FOR THIS TRIP MYSELF",
                "durationOfEmployMonth": "",
                "durationOfEmployYear": "",
                "reasonForTransit": "",
                "nextDestination": "",
                "purposeofvisit": "",
                "sponsorName": data.personal_financial_supported_does_application_have_sponsor_in_destination_country === true ? data.personal_financial_supported_surname + ', ' + data.personal_financial_supported_given_name : "",
                "employerName": data.employee_company_info_company_school_name ?? "",
                "accommodation": "KOWLOON HOTEL",
                "personContactNoCtryCd": "",
                "personContactNo": "",
                "signerName": "",
                "nameChanged": "",
                "nameChangedDtl": "",
                "blockedHKEntry": "",
                "blockedHKEntryDtl": "",
                "refusedHKVisa": "",
                "refusedHKVisaDtl": "",
                "webFormCapture": "",
                "entryDay1": data.travel_travel_history_hkg_entry_history?.[0] ? moment(data.travel_travel_history_hkg_entry_history?.[0]?.[1].value.fe ?? "").format('DD') ?? "" : "",
                "entryMonth1": data.travel_travel_history_hkg_entry_history?.[0] ? moment(data.travel_travel_history_hkg_entry_history?.[0]?.[1].value.fe ?? "").format('MM') ?? "" : "",
                "entryYear1": data.travel_travel_history_hkg_entry_history?.[0] ? moment(data.travel_travel_history_hkg_entry_history?.[0]?.[1].value.fe ?? "").format('YYYY') ?? "" : "",
                "entryDay2": data.travel_travel_history_hkg_entry_history?.[1] ? moment(data.travel_travel_history_hkg_entry_history?.[1]?.[1].value.fe ?? "").format('DD') ?? "" : "",
                "entryMonth2": data.travel_travel_history_hkg_entry_history?.[1] ? moment(data.travel_travel_history_hkg_entry_history?.[1]?.[1].value.fe ?? "").format('MM') ?? "" : "",
                "entryYear2": data.travel_travel_history_hkg_entry_history?.[1] ? moment(data.travel_travel_history_hkg_entry_history?.[1]?.[1].value.fe ?? "").format('YYYY') ?? "" : "",
                "entryDay3": data.travel_travel_history_hkg_entry_history?.[2] ? moment(data.travel_travel_history_hkg_entry_history?.[2]?.[1].value.fe ?? "").format('DD') ?? "" : "",
                "entryMonth3": data.travel_travel_history_hkg_entry_history?.[2] ? moment(data.travel_travel_history_hkg_entry_history?.[2]?.[1].value.fe ?? "").format('MM') ?? "" : "",
                "entryYear3": data.travel_travel_history_hkg_entry_history?.[2] ? moment(data.travel_travel_history_hkg_entry_history?.[2]?.[1].value.fe ?? "").format('YYYY') ?? "" : "",
                "anyJoinedTour": "N",
                "proposedNoVisit": "S",
                "proposedNoTransit": "",
                "purposeOther": "",
                "visitPurpose": "",
                "visitForLeisure": "Y",//Check purpose of visit 
                "visitForTransit": "N",
                "visitForBusiness": "N",
                "visitForFamilyVisit": "N",
                "visitForOthers": "N",
                "familyHkId1": "",
                "familyHkId2": "",
                "relativeOccupation": "",
                "haveSponsor": data.personal_financial_supported_does_application_have_sponsor_in_destination_country === false ? "N" : "Y",
                "sponsorRelationship": data_mapping.relationship[data.personal_financial_supported_relationship],
                "sponsorHkId1": "",
                "sponsorHkId2": "",
                "othersPurpose": "",
                "declaredby": "A",
                "travelPurpose": "V",
                "sponsorcontactNoCtryCd": data.personal_financial_supported_does_application_have_sponsor_in_destination_country === true ? PhoneNumber(data.personal_financial_supported_phone.phone).countryCallingCode : "",
                "sponsorcontactNo": data.personal_financial_supported_does_application_have_sponsor_in_destination_country === true ? PhoneNumber(data.personal_financial_supported_phone.phone).nationalNumber : "",
                "officeTelNoCtryCd": "",
                "selectedvaluePerson": "-1",
                "personForm": [],
                "depGivenName": "",
                "depChnName": "",
                "depAliasName": "",
                "depDobDay": "",
                "depDobMonth": "",
                "depDobYear": "",
                "depPob": "",
                "companyKey": "",
                "applicantName": "",
                "applicationCount": 0,
                "presentAddress": {
                    "addrFmt": "O",
                    "langInd": "E",
                    "unitDescptr": null,
                    "floorDescn": null,
                    "unit": null,
                    "addrLine1": `${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${convert_state(data.personal_home_address_state)} ${data.personal_home_address_zip_code}, ${get_country_name_v2(data.personal_home_address_country)}`,
                    "addrLine2": null,
                    "addrLine3": null,
                    "addrDisplay": `${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${convert_state(data.personal_home_address_state)} ${data.personal_home_address_zip_code}, ${get_country_name_v2(data.personal_home_address_country)}`,
                    "fullAddress": null,
                    "adiAddress": null
                },
                "permanentAddress": {
                    "addrFmt": "S",
                    "langInd": "C",
                    "unitDescptr": ""
                },
                "employerAddress": {
                    "addrFmt": "O",
                    "langInd": "E",
                    "unitDescptr": null,
                    "floorDescn": null,
                    "unit": null,
                    "addrLine1": `${data.employee_company_info_street_number_name}, ${data.employee_company_info_city}, ${convert_state(data.employee_company_info_state)} ${data.employee_company_info_zip_code}, ${get_country_name_v2(data.employee_company_info_country)}`,
                    "addrLine2": null,
                    "addrLine3": null,
                    "addrDisplay": `${data.employee_company_info_street_number_name}, ${data.employee_company_info_city}, ${convert_state(data.employee_company_info_state)} ${data.employee_company_info_zip_code}, ${get_country_name_v2(data.employee_company_info_country)}`,
                    "fullAddress": null,
                    "adiAddress": null
                },
                "accommodationAddress": {
                    "addrFmt": "O",
                    "langInd": "E",
                    "unitDescptr": null,
                    "floorDescn": null,
                    "unit": null,
                    "addrLine1": "KOWLOON HOTEL, 19-21 NATHAN ROAD, YAU TSIM MONG DISTRICT, KOWLOON",
                    "addrLine2": null,
                    "addrLine3": null,
                    "addrDisplay": "KOWLOON HOTEL, 19-21 NATHAN ROAD, YAU TSIM MONG DISTRICT, KOWLOON",
                    "fullAddress": null,
                    "adiAddress": null
                },
                "visitCompanyAddress": {
                    "addrFmt": "S",
                    "langInd": "C",
                    "unitDescptr": ""
                },
                "relativeAddress": {
                    "addrFmt": "S",
                    "langInd": "C",
                    "unitDescptr": ""
                },
                "depList": [],
                "suppDocList": [
                    {
                        "fullFilename": upload_photo_resp[0],
                        "filename": upload_photo_resp[0].split('#').pop(),
                        "thumb": upload_photo_resp[1],
                        // "filesize": 220513,
                        "docType": "PHOTO"
                    },
                    {
                        "fullFilename": upload_pp_resp[0],
                        "filename": upload_pp_resp[0].split('#').pop(),
                        "thumb": upload_pp_resp[1],
                        // "filesize": 220513,
                        "docType": "TRAVEL_DOC"
                    }
                ],
                "selectedvalueEntry": Math.min(data?.travel_travel_history_hkg_entry_history?.length ?? 0, 3).toString(),
                "proposedDuration": moment(data.service_core_info_exit_date).diff(moment(data.service_core_info_entry_date), 'days'),
                "proposedDurationUnit": "Day",
                "dtDeclared": moment().format('DD-MM-YYYY'),
                "selectedvalue": "0"
            },
            "form1003B": null,
            "applicationInfo": {
                "lang": "",
                "dobDay": "",
                "dobMonth": "",
                "dobYear": "",
                "flowType": "2VT4",
                "previousArn": "",
                "tdExpDay": "",
                "tdExpMonth": "",
                "tdExpYear": "",
                "option1": "",
                "option2": "",
                "option3": "",
                "option4": "",
                "option5": "",
                "extraInfo": "",
                "altTrn": transaction_id,
                "companyKey": "",
                "needPassword": false,
                "newReference": false,
                "inputInfo": false,
                "readyToSave": true,
                "emailDifferent": false,
                "emailInvalid": false,
                "saveCompleted": false,
                "saveError": false,
                "savedExpDate": moment().add(13, 'days').format('YYYYMMDD'),
                "trn": transaction_id,
                "submissionEmail": ad_email,
                "appDob": "20030201",
                "notificationEmail": ad_email,
                "saveCnt": 1,
            },
            "partyRole": "10",
            "trn": transaction_id,
            "lang": "en-US"
        }
    }
    const order_resp = await axios(order_config)
    console.log(order_resp.data)

    const result = {
        success: true,
        form_file: "",
        form_callback: {
            document_id: order_resp.data.refNo,
            ad_email,
            note: [
                `Code: ${order_resp.data.refNo}`,
                `DOB: ${moment(data.passport_core_info_date_of_birth).format('YYYY-MM-DD')}`,
                `Email: ${ad_email}`,
                `Apply: ${moment().format('DD/MM/YYYY')}`,
                `Payment URL: https://www.gov.hk/en/apps/immdes2applyvisit_transit.htm`
            ].join(',\n'),
            // dob: moment(data.passport_core_info_date_of_birth).format('YYYY-MM-DD'),
            // created_at: moment().format('DD/MM/YYYY'),
            // tracking_url: 'https://www.gov.hk/en/apps/immdes2applyvisit_transit.htm',
        },
    }
    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form(order, task, pod_data)
        return data
    } catch (error) {
        console.log(error)
    }
}


// const MOCK = {
//     order_id: 4055,
//     task_id: 4682
// }

// main(MOCK.order_id, MOCK.task_id)
module.exports = { main, create_form }