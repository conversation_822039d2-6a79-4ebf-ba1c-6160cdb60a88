const axios = require('axios');
const FormData = require('form-data');
const { https } = require('follow-redirects');
const jimp = require('jimp');

exports.solve_captcha = async (binary) => {

    var data = new FormData();
    data.append('file', binary, '1.jpeg');

    try {
        const resp2 = await axios({
            method: 'post',
            url: 'https://api.ariadirectcorp.com/v1/mrz-parser/capcha-solver-by-binary',
            data: data,
            headers: {
                ...data.getHeaders()
            }
        })
        return resp2.data.data
    } catch (err) {
        console.log(err)
        return ''
    }

}
exports.sleep = async (milliseconds) => await new Promise(resolve => setTimeout(resolve, milliseconds))


exports.get_url_redirect = async (url) => {
    return new Promise((resolve, reject) => {
        const request = https.get(url, (response) => {
            resolve(response.responseUrl);
        });

        request.on('error', (err) => {
            reject(err);
        });
    });
}

// photo_type: PHOTO, TRAVEL_DOC
exports.upload_file = async (url, photo_type = 'PHOTO', transaction_id, ticket_id) => {
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    const image = await jimp.read(response.data);
    const buffer = await image.getBufferAsync(jimp.MIME_JPEG);
    const base64 = buffer.toString('base64');
    const now = Date.now()
    const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://webapp.es2.immd.gov.hk/applies2-services/eservice2/common/uploadGetThumbTrn?trn=${transaction_id}&dependent_trn=${transaction_id}`,
        headers: {
            'Origin': 'https://webapp.es2.immd.gov.hk',
            'Referer': 'https://webapp.es2.immd.gov.hk/applies2-client/visa-app-duet/en-US/form1003A',
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'ticketId': ticket_id
        },
        data: {
            "files": [
                {
                    "base64value": base64.startsWith('/9j/') ? base64 : `/9j/${base64}`,
                    "filename": `APP1#${now}.jpg`,
                    "filemime": {
                        "ext": "jpg",
                        "mime": "image/jpeg"
                    },
                    "fileType": photo_type,
                    "rawFilename": `${now}.jpg`
                }
            ]
        }
    }
    try {
        const resp = await axios(config);
        return resp.data.result
    } catch (err) {
        console.log(err)
        return null
    }
}