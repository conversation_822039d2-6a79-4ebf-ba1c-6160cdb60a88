const moment = require('moment')
const fs = require('fs')
const data_mapping = require('../../shared/data_mapping')
const { get_order_detail, get_order_task, capitalize, fill_pdf_form, get_order_task_pod_data, get_country_name_v2 } = require('../../shared/helpers')

const DATE_FORMAT = "DD/MM/YYYY"

const create_form = async (data) => {
    const ETHNIC = "KINH"
    const OCCUPATION = ""

    data['additional_question_foreign_nationality_country'] = get_country_name_v2(data['additional_question_foreign_nationality_country'])
    data['passport_core_info_name'] = `${data.passport_core_info_surname} ${data.passport_core_info_given_name}`
    data['passport_core_info_name_sign'] = data.passport_core_info_name
    data['passport_core_info_date_of_birth_date'] = moment(data.passport_core_info_date_of_birth).format('DD')
    data['passport_core_info_date_of_birth_month'] = moment(data.passport_core_info_date_of_birth).format('MM')
    data['passport_core_info_date_of_birth_year'] = moment(data.passport_core_info_date_of_birth).format('YYYY')
    data['passport_core_info_place_of_birth'] = `${data.passport_core_info_state_of_birth}, ${get_country_name_v2(data.passport_core_info_country_of_birth)}`
    data['passport_core_info_origin'] = `${data.passport_core_info_state_of_birth}, ${get_country_name_v2(data.passport_core_info_country_of_birth)}`
    data["personal_core_info_ethnic"] = ETHNIC

    data[`passport_core_info_gender_${data.passport_core_info_gender.toLowerCase()}`] = true

    data['personal_permanent_address_country'] = get_country_name_v2(data['personal_permanent_address_country'])

    if (data['personal_permanent_address_country'] === 'United States') {
        data['personal_permanent_address_state'] = data_mapping.us_state[data.personal_permanent_address_state] ?? data.personal_permanent_address_state
    }

    data['personal_permanent_address'] = capitalize([
        `${data.personal_permanent_address_address},`,
        data.personal_permanent_address_city,
        data.personal_permanent_address_state,
        `${data.personal_permanent_address_zip_code},`,
        data.personal_permanent_address_country
    ].join(' '))

    if (typeof data.family_father_surname != 'undefined') {
        data['family_father_nationality'] = get_country_name_v2(data['family_father_nationality'])
        data['family_father_name'] = `${data.family_father_surname} ${data.family_father_given_name}`
        data['family_father_date_of_birth_date'] = moment(data.family_father_date_of_birth).format('DD')
        data['family_father_date_of_birth_month'] = moment(data.family_father_date_of_birth).format('MM')
        data['family_father_date_of_birth_year'] = moment(data.family_father_date_of_birth).format('YYYY')
        data['family_father_date_of_birth'] = moment(data.family_father_date_of_birth).format(DATE_FORMAT)
        data['family_father_ethnic'] = data.family_father_nationality == "VIET NAM" ? ETHNIC : ''
        data['family_father_home_town'] = data.family_father_place_of_birth
        data['family_father_occupation'] = OCCUPATION
        data['family_father_what_do_since_1975'] = `${data.family_father_occupation}`
    }

    if (typeof data.family_mother_surname != 'undefined') {
        data['family_mother_nationality'] = get_country_name_v2(data['family_mother_nationality'])
        data['family_mother_name'] = `${data.family_mother_surname} ${data.family_mother_given_name}`
        data['family_mother_date_of_birth_date'] = moment(data.family_mother_date_of_birth).format('DD')
        data['family_mother_date_of_birth_month'] = moment(data.family_mother_date_of_birth).format('MM')
        data['family_mother_date_of_birth_year'] = moment(data.family_mother_date_of_birth).format('YYYY')
        data['family_mother_date_of_birth'] = moment(data.family_mother_date_of_birth).format(DATE_FORMAT)
        data['family_mother_ethnic'] = data.family_mother_nationality == "VIET NAM" ? ETHNIC : ''
        data['family_mother_home_town'] = data.family_mother_place_of_birth
        data['family_mother_occupation'] = OCCUPATION
        data['family_mother_what_do_since_1975'] = `${data.family_mother_occupation}`
    }

    if (typeof data.family_spouse_surname != 'undefined') {
        data['family_spouse_name'] = `${data.family_spouse_surname} ${data.family_spouse_given_name}`
        data['family_spouse_date_of_birth_date'] = moment(data.family_spouse_date_of_birth).format('DD')
        data['family_spouse_date_of_birth_month'] = moment(data.family_spouse_date_of_birth).format('MM')
        data['family_spouse_date_of_birth_year'] = moment(data.family_spouse_date_of_birth).format('YYYY')
        data['family_spouse_date_of_birth'] = moment(data.family_spouse_date_of_birth).format(DATE_FORMAT)
        data['family_spouse_ethnic'] = data.family_spouse_nationality == "VIET NAM" ? ETHNIC : ''
        data['family_spouse_home_town'] = data.family_spouse_city_of_birth
        data['family_spouse_place_of_birth'] = data.family_spouse_country_of_birth
        data['family_spouse_occupation'] = OCCUPATION
        data['family_spouse_permanent_address'] = data.personal_permanent_address
        data['family_spouse_what_do_since_1975'] = `${data.family_spouse_occupation}`

    }
    // if (typeof data.personal_emergency_contact_surname != 'undefined') {
    //     data['personal_emergency_contact'] = `${data.personal_emergency_contact_surname} ${data.personal_emergency_contact_given_name} : ${data.personal_emergency_contact_phone.replace('+84', 0)} - ${data.personal_emergency_contact_address}, ${data.personal_emergency_contact_city}, ${data.personal_emergency_contact_state}`.toUpperCase()
    // }
    if (data.additional_question_contact_in_vnm_full_name) {
        data['personal_emergency_contact'] = [
            data.additional_question_contact_in_vnm_full_name,
            data.additional_question_contact_in_vnm_phone?.phone?.replace('+84', '0'),
            data.additional_question_contact_in_vnm_address
        ].filter(v => v).join(', ')
    }
    data['additional_question_exit_to_usa_date_of_entry_date'] = moment(data.additional_question_exit_to_usa_date_of_entry).format('DD')
    data['additional_question_exit_to_usa_date_of_entry_month'] = moment(data.additional_question_exit_to_usa_date_of_entry).format('MM')
    data['additional_question_exit_to_usa_date_of_entry_year'] = moment(data.additional_question_exit_to_usa_date_of_entry).format('YYYY')

    data['additional_question_state_legal_document_issue_date'] = moment(data.additional_question_state_legal_document_issue_date).format('DD/MM/YYYY')

    const frame = {
        x: 475,
        y: 635,
        width: 75,
        height: 74,
        photo_url: data.photo_passport_photo_copy_of_photo,
    }
    data.filename = `Application_Form_${data.passport_core_info_surname}_${data.passport_core_info_given_name}_VNM_002.pdf`
    const pdf_file = await fill_pdf_form(__dirname + '/vnm_002.pdf', data, frame)

    const result = {
        success: true,
        form_file: pdf_file,
        form_callback: {},
    }

    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const result = await create_form(pod_data)
        return result
    } catch (error) {
        console.log(error)
    }
}
// main(4826)
module.exports = { main, create_form }