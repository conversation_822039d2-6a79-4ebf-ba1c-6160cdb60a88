const fs = require('fs');
const moment = require('moment');
const axios = require('axios');
const jimp = require('jimp');
const tmp = require('tmp');
const { PuppeteerScreenRecorder } = require('puppeteer-screen-recorder');
const { connect } = require('../../shared/browser/index.js');
const { s3_upload_buff, get_order_task_pod_data, sleep, get_country_name_v2, s3_url_to_bucket_key } = require('../../shared/helpers');
const data_mapping = require('../../shared/data_mapping')
const RECODER_NAME = `Recorder_${moment().unix()}.mp4`
const BTAPI = require('../../shared/bt_api');
const AWS = require('aws-sdk');

async function get_image_file(url, name) {
    try {
        const { bucket, key } = s3_url_to_bucket_key(url)
        const s3 = new AWS.S3();
        const params = {
            Bucket: bucket,
            Key: key
        };
        const response = await s3.getObject(params).promise();
        fs.writeFileSync(name, response.Body);
        console.log(`File ${name} downloaded successfully.`);
    } catch (error) {
        console.error('Error downloading file:', error);
    }
}
async function convertBase64StringToFile(base64String, filename) {
    if (!base64String) {
        return null;
    }

    // Extract base64 data (remove "data:image/jpeg;base64," prefix)
    const base64Data = base64String.split(',')[1];
    const byteString = atob(base64Data);
    const uint8Array = new Uint8Array(byteString.length);
    for (let i = 0; i < byteString.length; i++) {
        uint8Array[i] = byteString.charCodeAt(i);
    }


    // Save the buffer to a file
    fs.writeFileSync(filename, uint8Array); // Save file to disk

    return filename; // Return the file path for Puppeteer
}
const visa_form = async (order, task, data) => {
    const { page, browser } = await connect({
        args: [],
        turnstile: true,
        devtools: true,
        headless: false,
        // disableXvfb: true,
        // ignoreAllFlags:true,
        customConfig: {},
        connectOption: {
            defaultViewport: null
        },
        plugins: []
    });
    const recorder = new PuppeteerScreenRecorder(page);
    await recorder.start(RECODER_NAME);

    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })


    const btAPI = new BTAPI();
    const pwd = "Admin123@AriaDirect"
    try {

        let ad_email = await btAPI.randomEmail("japan_evisa")
        //Register
        {
            console.log("Registering")
            await page.goto('https://www.evisa.mofa.go.jp/personal/login');
            await page.click(`button ::-p-text(Register an email address)`)
            await sleep(2000)
            await page.locator('#mailAddress').fill(ad_email);
            await page.select('#languageKbn', 'en')
            await page.select('#icaoCode', data.passport_core_info_nationality)
            await sleep(2000)
            await page.select('#countryCode', '0001')
            await sleep(2000)
            await page.select('#largeClassCode', data.personal_core_info_city_of_residence.toUpperCase())
            await page.click(`button ::-p-text(Check)`)
            await sleep(2000)
            await page.click(`button ::-p-text(Register)`)
            console.log("email : ", ad_email)
        }

        // //Activattion
        {
            console.log("Activating")
            let start_time = moment().unix()
            await sleep(2000)
            let selected_email = null
            while (moment().unix() > start_time && moment().unix() - start_time < 300) {
                const emails = await btAPI.getEmails(ad_email)
                console.log(emails)
                if (emails?.length > 0 && emails[0].time > start_time) {
                    selected_email = emails[0]
                    break
                }
                await new Promise(r => setTimeout(r, 10000))
            }
            console.log("wait time : ", moment().unix() - start_time)
            console.log(selected_email)
            const regex = /https:\/\/www\.evisa\.mofa\.go\.jp\/personal\/user\/password\?key=[A-Za-z0-9\-_=]+/g;
            const urls = selected_email.body.match(regex);
            await page.goto(urls[0])
            await new Promise(r => setTimeout(r, 3000))
            await page.locator('#password').fill(pwd)
            await page.locator('#passwordConf').fill(pwd)
            await page.click(`button ::-p-text(Register)`)
            await sleep(2000)
            await page.click(`button ::-p-text(Back to top page)`)
        }
        //Login
        {
            console.log("Logging in")
            let start_time = moment().unix()
            await sleep(2000)
            await page.goto('https://www.evisa.mofa.go.jp/personal/login')
            await page.locator('#mailAddress').fill(ad_email)
            await page.locator('#password').fill(pwd)
            await page.click(`button ::-p-text(Log in)`)
            let selected_email = null
            while (moment().unix() > start_time && moment().unix() - start_time < 300) {
                const emails = await btAPI.getEmails(ad_email)
                console.log(emails)
                if (emails?.length > 0 && emails[0].time > start_time) {
                    selected_email = emails[0]
                    break
                }
                await new Promise(r => setTimeout(r, 10000))
            }
            console.log("wait time : ", moment().unix() - start_time)
            console.log(selected_email)
            const decodedBody = selected_email.body.replace(/\\u3000/g, '\u3000');
            const regex = /one-time\s*password\s*[^\d]*(\d{6})[^\d]*/i;
            const code = decodedBody.match(regex);
            await page.locator('#onetimePassword').fill(code[1])
            await page.click(`#authen`)
        }
        console.log("Filling Form...")
        await sleep(2000)
        await page.click(`button ::-p-text(New registration)`)
        await sleep(1000)
        await page.click(`#key_yes`)
        await page.click(`button ::-p-text(Next)`)
        await page.waitForNetworkIdle()

        //Passport Photo
        await page.waitForSelector('#atmt_file_001')
        files = await page.$$('#atmt_file_001');
        await get_image_file(data.document_copy_of_passport_copy_of_passport_main_page, "images/passport.jpg")
        await files[0].uploadFile("images/passport.jpg")
        await page.waitForNetworkIdle()

        //User Photo
        files = await page.$$('#atmt_file_002');
        const photo_resp = await axios.get(data.photo_passport_photo_copy_of_photo, { responseType: 'arraybuffer' });
        const image = await jimp.read(Buffer.from(photo_resp.data));
        const image_buff = await image.resize(550, 710).getBufferAsync(jimp.MIME_JPEG)
        const tmp_file = tmp.fileSync({ mode: 0o644, prefix: 'temp-', postfix: '.jpeg' });
        fs.writeFileSync(tmp_file.name, image_buff);
        console.log(tmp_file.name)
        await files[0].uploadFile(tmp_file.name)

        //Info

        console.log("Page 1...")
        await page.locator('#sei').fill(data.passport_core_info_surname)
        await page.locator('#mei').fill(data.passport_core_info_given_name)
        await page.locator('#birthDate').fill(moment(data.passport_core_info_date_of_birth).format("YYYYMMDD"))
        await page.locator('#passportNo').fill(data.passport_core_info_passport_number)
        await page.click({ "M": "#sex1", "F": "#sex2" }[data.passport_core_info_gender])
        await page.locator('#passportExpirationDate').fill(moment(data.passport_core_info_expiration_date).format("YYYY/MM/DD"))
        await page.locator('#passportIssuePlace').fill(`IMMIGRATION DEPARTMENT`)
        await page.locator('#passportIssueDate').fill(moment(data.passport_core_info_issue_date).format("YYYY/MM/DD"))
        await page.locator('#passportIssueMinistryOther').fill(`IMMIGRATION DEPARTMENT`)
        await page.click('#originalSeiNotEntered1')
        await page.click('#originalMeiNotEntered1')
        await page.select('#oldIcaoCode', data.passport_additional_info_former_nationality ?? "999")
        await page.locator('#birthPlace').fill(`${data.passport_core_info_city_of_birth}, ${data_mapping.us_state[data.passport_core_info_state_of_birth] ?? data.passport_core_info_state_of_birth}, ${get_country_name_v2(data.passport_core_info_country_of_birth)}`)
        await page.click({
            "single": "#marriageRecord1",
            "married": "#marriageRecord2",
            "divorced": "#marriageRecord4",
            "widowed": "#marriageRecord3",
        }[data.personal_core_info_marital_status])
        await page.select('#job_select_box', {
            'business_person': '1',
            'company_employee': '2',
            "student": '4',
            "retired": '99',
            "homemaker": '99',
            "self_employed": '1'
        }[data.personal_occupation_occupation])
        if (["homemaker", "retired"].includes(data.personal_occupation_occupation)) {
            await page.locator('#jobOther').fill(data_mapping.occupation[data.personal_occupation_occupation_other])
        }
        if (data.personal_core_info_marital_status === "married") {
            await page.select('#spouse_job_select_box', {
                'business_person': '1',
                'company_employee': '2',
                "student": '4',
                "retired": '99',
                "homemaker": '99',
                "self_employed": '1'
            }[data.family_spouse_occupation])
        } else await page.select('#spouse_job_select_box', '0')
        await sleep(2000)
        await page.select('#categoryMClassCode', '1')
        await sleep(2000)
        await page.select('#categoryCode', 'V010')
        await page.click(`button ::-p-text(Next)`)
        await sleep(5000)

        //Travel Info
        console.log("Page 2...")
        await page.locator('#arrivalDate').fill(moment(data.service_core_info_entry_date).format("YYYY/MM/DD"))
        await page.locator('#returnlDate').fill(moment(data.service_core_info_exit_date).format("YYYY/MM/DD"))
        await page.locator('#flightNo').fill('UNITED AIRLINES, UA875')
        await page.locator('#airportName').fill('HANEDA')
        await page.locator('#visitCode').fill('4')
        await page.locator('#hotelName').fill('THE TOKYO STATION HOTEL, TOKYO')
        await page.locator('#hotelTel').fill('3-5220-1111')
        await page.locator('#hotelAddress').fill('1-9-1 MARUNOUCHI, CHIYODA-KU, TOKYO, CHIYODA, KANTO, JAPAN')
        if (data.travel_travel_history_have_you_ever_been) {
            await page.locator('#pastYearStayDayTotalNum').fill(data.travel_travel_history_total_days_stayed_last_year.toString())
            await page.click("#hotelAddress")
            await page.locator('#preStayDayNum').fill(data.travel_travel_history_days_last_stay.toString())
            await page.locator('#preArrivalDate').fill(moment(data.travel_travel_history_last_entry_date).format("YYYY/MM/DD"))
        } else {
            await page.click('#pastYearStayDayTotalNumNotEntered1')
        }

        await page.click(`button ::-p-text(Next)`)
        await sleep(2000)


        console.log("Page 3...")
        await page.locator('#addressOther').fill(`${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${data_mapping.us_state[data.personal_home_address_state] ?? data.personal_home_address_state} ${data.personal_home_address_zip_code}, ${get_country_name_v2(data.personal_home_address_country)}`)
        await page.locator('#tel').fill(data.personal_core_info_phone.phone.substring(1))
        await page.locator('#mobile').fill(data.personal_core_info_phone.phone.substring(1))
        await page.locator('#mail').fill(process.env.ad_env === 'prod' ? '<EMAIL>' : '<EMAIL>')
        //Employer
        if (['retired', 'homemaker'].includes(data.personal_occupation_occupation)) {
            await page.click('#employerNameNotEntered1')
            await page.click('#employerTelNotEntered1')
            await page.click('#employerAddressNotEntered1')
        }
        await page.locator('#employerName').fill(data.employee_company_info_company_school_name)
        await page.locator('#employerTel').fill(data.employee_company_info_phone.phone.substring(1))
        await page.locator('#employerAddress').fill(`${data.employee_company_info_street_number_name}, ${data.employee_company_info_city}, ${data_mapping.us_state[data.employee_company_info_state] ?? data.employee_company_info_state} ${data.employee_company_info_zip_code}, ${get_country_name_v2(data.employee_company_info_country)}`)
        await page.click(`button ::-p-text(Next)`)
        await sleep(2000)

        console.log("Page 4...")
        //Additional Questions
        await page.click('#specificReasonNotEntered1')
        await page.click('#penaltyHistoryFlag12')
        await page.click('#penaltyHistoryFlag22')
        await page.click('#penaltyHistoryFlag32')
        await page.click('#penaltyHistoryFlag42')
        await page.click('#penaltyHistoryFlag52')
        await page.click('#penaltyHistoryFlag62')
        await page.click(data.document_copy_of_document_apply_as_minor ? '#expensePayerType2' : '#expensePayerType1')
        await page.click(data.document_copy_of_document_apply_as_minor ? '#minorType1' : '#minorType2')
        await page.click('#identification1')
        await page.click(`button ::-p-text(Next)`)
        await sleep(2000)
        await page.waitForNetworkIdle()
        console.log("Complete Filling Info")
        await recorder.stop();
        await browser.close()
        return {
            email: ad_email,
            created_at: moment().format('DD/MM/YYYY'),
            note: [
                `Email: ${ad_email}`,
                `Password:${pwd}`,
                `Tracking Url: https://www.evisa.mofa.go.jp/personal/login`,
            ].join('\n')
        }
    } catch (error) {
        console.log(error)
        console.log(error.message)
        await recorder.stop();
        await browser.close()
        return null
    }

}

const MAX_RETRY = 2;
const create_form = async (order, task, pod_data) => {
    const result = {
        success: false,
        form_callback: {}
    }
    let form = null
    for (let i = 0; i < MAX_RETRY; i++) {
        form = await visa_form(order, task, pod_data)
        if (form != null) {
            result.success = true
            result.form_callback = form
            break
        }
    }
    const buckets = JSON.parse(process.env.ad_s3)
    await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${RECODER_NAME}`, fs.readFileSync(RECODER_NAME))

    fs.rmSync('images', { recursive: true })
    fs.rmSync(RECODER_NAME, { recursive: true })

    result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${RECODER_NAME}`
    console.log("STEPS:", result.form_callback.steps)
    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form(order, task, pod_data)
        console.log(data)
        return data
    } catch (error) {
        console.log(error)
    }
}
module.exports = { main, create_form }