var fs = require('fs-extra');
const axios = require('axios');
const moment = require('moment');
const puppeteer = require('puppeteer');
const qs = require('qs');
const us = require('us');
const AWS = require('aws-sdk');
const { PDFDocument } = require('pdf-lib');
const { get_order_task_pod_data, s3_upload_buff, create_zip, s3_url_to_bucket_key, get_country_name_v2 } = require('../../shared/helpers');
const data_mapping = require('./data_mapping')
const helper = require('./helper')
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));
let DEBUG = false
const query = async (page, valueToFind, parentType, nextPage = 2) => {
    await sleep(1000)
    const elements = await page.$$(parentType);

    for (let element of elements) {
        const text = await page.evaluate(el => el.textContent.trim(), element);
        if (text.includes(valueToFind)) {
            await element.click();
            return true;
        }
    }

    // If no link is found, attempt to navigate to the next page
    const nextButton = await page.$(`a.num[onclick*='fn_search(${nextPage})']`);
    if (nextButton) {
        await nextButton.click();
        return false;
    } else {
        return true;
    }
};
async function get_image_file(url, name) {
    try {
        const { bucket, key } = s3_url_to_bucket_key(url)
        const s3 = new AWS.S3();
        const params = {
            Bucket: bucket,
            Key: key
        };
        const response = await s3.getObject(params).promise();
        fs.writeFileSync(name, response.Body);
        console.log(`File ${name} downloaded successfully.`);
    } catch (error) {
        console.error('Error downloading file:', error);
    }
}
const korea_visa_form = async (data, attributes) => {
    const browser = await puppeteer.launch({ headless: DEBUG ? false : "new", args: ['--no-sandbox', '--disable-setuid-sandbox'] });
    const page = await browser.newPage();
    !DEBUG && await page.setViewport({ width: 1000, height: 1500 });
    page.setDefaultTimeout(10000);

    let i = 1;
    let email = process.env.ad_env === 'prod' ? '<EMAIL>' : '<EMAIL>'
    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })

    await page.goto('https://visa.go.kr/openPage.do?MENU_ID=1020408');

    try {

        //switch to English FIRST
        await page.waitForSelector('#top_a_lang_en', { timeout: 50000 })
        await page.click('#top_a_lang_en')

        await page.waitForSelector('#applyVisa')
        await page.click('#applyVisa')

        // Page 1
        await page.waitForSelector('#regCdSearch', { visible: true })
        await page.click('#regCdSearch')
        var pages = await browser.pages(); // Get all open pages
        var newPage = pages[pages.length - 1]; // Get the latest page
        //Diplomatic Office

        await page.waitForNetworkIdle()
        await newPage.waitForSelector('#NAT_KOR_NM', { visible: true })
        if (data.personal_home_address_country == "USA") {
            // IS US COUNTRY
            await newPage.type('#NAT_KOR_NM', get_country_name_v2(data.personal_home_address_country).replace(/\s+/g, '').toUpperCase())
            await newPage.click('#search')
            if (data.personal_home_address_state == "CA") {
                //Special Check for CA
                state = helper.isNorthOrSouthCalifornia(helper.splitZipCode(data.personal_home_address_zip_code))
                findQuery = await query(newPage, data_mapping.US_STATE_DIPLOMATIC_OFFICE[state], 'a')
                while (!findQuery) {
                    await newPage.waitForNavigation({ waitUntil: 'load' });
                    findQuery = await query(newPage, data_mapping.US_STATE_DIPLOMATIC_OFFICE[state], 'a')
                }
            } else {
                //Progress Normally
                findQuery = await query(newPage, data_mapping.US_STATE_DIPLOMATIC_OFFICE[data.personal_home_address_state], 'a')
                while (!findQuery) {
                    await newPage.waitForNavigation({ waitUntil: 'load' });
                    findQuery = await query(newPage, data_mapping.US_STATE_DIPLOMATIC_OFFICE[data.personal_home_address_state], 'a')
                }
            }

        } else {
            //Default VNM
            await newPage.type('#NAT_KOR_NM', "VIETNAM")
            await newPage.click('#search')
            findQuery = await query(newPage, 'Korean Consulate General in Ho Chi Minh City', 'a')
            while (!findQuery) {
                await newPage.waitForNavigation({ waitUntil: 'load' });
                findQuery = await query(newPage, 'Korean Consulate General in Ho Chi Minh City', 'a')
            }
        }

        //Purpose
        await page.waitForSelector(`#${data_mapping.PURPOSE[attributes.purpose]}`, { visible: true })
        await page.click(`#${data_mapping.PURPOSE[attributes.purpose]}`)

        /**
         * Status of Stay (Support C3 Only Now)
         */
        await page.waitForSelector('#EFORM_STAY', { visible: true })
        await page.select('#EFORM_STAY', "C3")

        /**
         * Do you have an inviting company?	
         * INVIT_YN1 => No
         * INVIT_YN2 => Yes
         */
        if (attributes.purpose == "tourist") {
            await page.waitForSelector('#INVIT_YN1', { visible: true })
            await page.click('#INVIT_YN1')
        } else {
            await page.waitForSelector('#INVIT_YN2', { visible: true })
            await page.click('#INVIT_YN2')
            //TODO
            /**
             * Are you a member of Korea Visa Potal? => only available if have inviting company
             * PTL_MEM_YN1 => No
             * PTL_MEM_YN2 => Yes
             */
            // await page.waitForSelector('#PTL_MEM_YN1', { visible: true })
            // await page.click('#PTL_MEM_YN1')
        }

        /**
         * User Photo 
         */
        await get_image_file(data.photo_passport_photo_copy_of_photo, "images/1.jpg")
        const image = await fs.readFile("images/1.jpg");
        const base64Image = image.toString('base64');
        const imageSrc = `data:image/jpeg;base64,${base64Image}`;
        await page.evaluate((imageSrc) => {
            const imgElement = document.getElementById('PASS_IMG');
            imgElement.src = imageSrc;
        }, imageSrc);
        const files = await page.$$('#PassPort_FILEIMAGE');
        await files[0].uploadFile("images/1.jpg")

        //
        //Surname
        await page.type('#SUR_NM', data.passport_core_info_surname)

        //Given Name
        await page.type('#GIV_NM', data.passport_core_info_given_name)

        //DOB
        await page.type('#BIRTH_YMD', moment(data.passport_core_info_date_of_birth).format("YYYY-MM-DD"))

        //Gender
        await page.click(data.passport_core_info_gender == "F" ? '#SEX_CD_F' : '#SEX_CD_M')

        //Nationality
        await page.waitForSelector('#OPEN_NAT_POPUP', { visible: true })
        await page.click('#OPEN_NAT_POPUP')
        pages = await browser.pages()
        newPage = pages[pages.length - 1]

        await newPage.waitForNetworkIdle()
        await newPage.waitForSelector('#SEARCH_NAT_NM')
        await newPage.type('#SEARCH_NAT_NM', get_country_name_v2(data.personal_home_address_country))
        await newPage.click('#natSearch')
        await newPage.waitForSelector('#natList0_1')
        await newPage.click('#natList0_1')

        //Have you used any other name ? (e.g. Nickname, maiden name, alias, etc.)	OTHER_EK_NM_YN1 - OTHER_EK_NM_YN
        await page.click('#OTHER_EK_NM_YN1')

        //Are you a citizen of any other country ?	MUL_NAT_YN1 - MUL_NAT_YN
        if (data.passport_additional_info_is_dual_citizen) {
            await page.click('#MUL_NAT_YN')
            await page.type('#MUL_NAT_DETAIL', get_country_name_v2(data.passport_second_passport_nationality))
        } else {
            await page.click('#MUL_NAT_YN1')
        }


        //Passport Type (Default Regular)
        await page.select('#PASS_NO_KIND', 'OR')
        //PP#
        await page.type('#PASS_NO', data.passport_core_info_passport_number)

        //EXP Date
        await page.type('#EXPR_YMD', moment(data.passport_core_info_expiration_date).format("YYYY-MM-DD"))

        //Issue Place
        await page.type('#ISS_PLACE', get_country_name_v2(data.passport_core_info_issuing_authority))

        //Date of Issue
        await page.type('#ISS_YMD', moment(data.passport_core_info_issue_date).format("YYYY-MM-DD"))

        //Do you have any other valid passport ? MUL_PASS_YN2 - MUL_PASS_YN
        if (data.passport_additional_info_is_dual_citizen) {
            await page.click('#MUL_PASS_YN')
            await page.select('#MUL_PASS_NO_KIND', 'OR')
            await page.type('#MUL_PASS_NO', data.passport_second_passport_passport_number)
            await page.type('#MUL_EXPR_YMD', moment(data.passport_second_passport_expiration_date).format("YYYY-MM-DD"))
            //Nationality
            await page.waitForSelector('#MUL_OPEN_ISS_NAT_POPUP', { visible: true })
            await page.click('#MUL_OPEN_ISS_NAT_POPUP')
            pages = await browser.pages()
            newPage = pages[pages.length - 1]

            await page.waitForNetworkIdle()
            await newPage.waitForSelector('#SEARCH_NAT_NM')
            await newPage.type('#SEARCH_NAT_NM', get_country_name_v2(data.passport_second_passport_nationality))
            await newPage.click('#natSearch')
            await newPage.waitForSelector('#natList0_1')
            await newPage.click('#natList0_1')
        } else {
            await page.click('#MUL_PASS_YN2')

        }

        //Home Address
        await page.click('#openNatPopAddr')
        pages = await browser.pages()
        newPage = pages[pages.length - 1]

        await newPage.waitForNetworkIdle()
        await newPage.waitForSelector('#SEARCH_NAT_NM')
        await newPage.type('#SEARCH_NAT_NM', get_country_name_v2(data.personal_home_address_country).toUpperCase())
        await newPage.click('#natSearch')
        await newPage.waitForSelector('#natList0_1')
        await newPage.click('#natList0_1')
        //Address Street (38 Char)
        await page.type('#ADDR_STREET', data.personal_home_address_street_number_name)

        await page.type('#ADDR_CITY', data.personal_home_address_city)
        await page.type('#ADDR_STATE', data.personal_home_address_state)

        //Telephone
        await page.type('#TEL_NO', data.personal_core_info_phone.phone.replace("+", ""))
        await page.type('#MOBILE_TEL_NO', data.personal_core_info_phone.phone.replace("+", ""))

        //Email - TODO
        await page.type('#EMAIL', email)

        //Country/Region where you are applying for a visa	CUR_NAT_SAME_YN - CUR_NAT_SAME_Y (CUR_NAT_DETAIL)
        await page.click('#CUR_NAT_SAME_Y')
        //Emergency Contact - EMCY_EK_NM - EMCY_NAT_NM - EMCY_TEL_NO - EMCY_REL => Full name should only contains 19 char
        await page.type('#EMCY_EK_NM', (data.personal_emergency_contact_full_name).toUpperCase())
        await page.evaluate((selector) => {
            const inputElement = document.querySelector(selector);
            if (inputElement) {
                inputElement.value = '';
            }
        }, '#EMCY_NAT_NM');

        await page.type('#EMCY_NAT_NM', get_country_name_v2(data.personal_emergency_contact_region_of_residence))
        await page.type('#EMCY_TEL_NO', data.personal_emergency_contact_phone.phone.replace("+", ""))
        await page.type('#EMCY_REL', data.personal_emergency_contact_relationship.toUpperCase())

        await page.screenshot({ path: `images/Page 1.png`, fullPage: true })
        // Page 2
        await page.click('#REG_STEP1')
        app_no = null
        const app_field = await page.waitForXPath('//*[@id="inviteeInfoStep2"]/table[1]/tbody/tr/td', { timeout: 60000 });
        if (app_field) {
            app_no = await page.evaluate(el => el.textContent, app_field);
            console.log("Found Application No.:" + app_no)
        } else {
            console.log("Not found Application No.")
        }
        await page.waitForSelector('#MARI_STS_CD_S')
        //Marital Status (MARI_STS_CD_S, MARI_STS_CD_M, MARI_STS_CD_D)
        await page.click(`#${data_mapping.MARITAL_STATUS[data.personal_core_info_marital_status]}`)

        /**
         * Education
         * LAST_DEGREE_1 : High school diploma
         * LAST_DEGREE_2 : Graduate
         * LAST_DEGREE_3 : Postgraduate
         * LAST_DEGREE_4 : Others
         */
        await page.click(`#${data_mapping.EDUCATION[data.personal_education_education]}`)
        //School Name: LAST_SCH_NM (50 Char)
        await page.waitForSelector('#LAST_SCH_NM', { timeout: 20000 })
        await page.type('#LAST_SCH_NM', data.personal_education_name_of_institution.toUpperCase())
        //School Address: LAST_SCH_ADDR (80 Char)
        await page.type('#LAST_SCH_ADDR', data.personal_education_address.toUpperCase())

        /**
         * Occupation
         * JOB_CD_1 : Businessperson
         * JOB_CD_2 : Sole proprietor
         * JOB_CD_3 : Office worker
         * JOB_CD_4 : Government employee
         * JOB_CD_5 : Student
         * JOB_CD_6 : Retired employee
         * JOB_CD_7 : Unemployeed
         * JOB_CD_8 : Others
         */
        await page.click(`#${data_mapping.OCCUPATION[data.personal_occupation_occupation]}`)
        //Company Name : COMPY_NM (50 Char)
        await page.type('#COMPY_NM', data.employee_company_info_company_school_name.toUpperCase())
        //Position : POSI_NM
        await page.type('#POSI_NM', data.employee_company_info_job_title.toUpperCase())
        //Company Address : COMPY_ADDR (50  )
        await page.type('#COMPY_ADDR', data.employee_company_info_address.toUpperCase())
        //Company Phone : JOB_TEL_NO
        await page.type('#JOB_TEL_NO', data.employee_company_info_phone.phone.replace("+", ""))

        //Num of Entries to Korea (APPL_VISA_GB_S, APPL_VISA_GB_D, APPL_VISA_GB_M)
        await page.click(attributes.number_of_entries == "single_entry" ? '#APPL_VISA_GB_S' : '#APPL_VISA_GB_M')
        //Length Of Stay : APPL_SOJ_DUR
        const entryDate = moment(data.service_core_info_entry_date);
        const exitDate = moment(data.service_core_info_exit_date);
        lengthOfStay = exitDate.diff(entryDate, 'days')
        if (lengthOfStay === 0) {
            lengthOfStay = 1
        }
        await page.type('#APPL_SOJ_DUR', lengthOfStay.toString())
        //Entry Date : ENTRY_EXP_YMD
        await page.type('#ENTRY_EXP_YMD', moment(data.service_core_info_entry_date).format('YYYY-MM-DD'))

        pages = await browser.pages()
        //Hotel Address 
        await page.click('#addrSearchEn')
        await sleep(1000)
        pages = await browser.pages()
        newPage = pages[pages.length - 1]
        await newPage.waitForNetworkIdle()
        await newPage.waitForSelector('input[name="keyword"]');
        await newPage.type('input[name="keyword"]', "21 Jong-ro 66ga-gil")
        await newPage.click('[type="button"]')
        await newPage.waitForSelector('a[href="javascript:setMaping(\'1\')"]');
        await newPage.click('a[href="javascript:setMaping(\'1\')"]');
        await newPage.type('#rtAddrDetail', "Seoul N Hotel Dongdaemun")
        await newPage.click('.btn-bl');

        //Contact Number (SOJ_EXP_REGION_TEL_NO)
        await page.type('#SOJ_EXP_REGION_TEL_NO', '82263650008')

        //Travel History (KOR) - Have you visited Korea during the past 5 years ? BF_VISIT_N,BF_VISIT_Y
        if (data.travel_visited_country_have_you_visited_this_country) {
            //Have History
            await page.click('#BF_VISIT_Y')
            //Number of visit - BF_VISIT_PURP
            await page.type('#BF_VISIT_CNT', data.travel_visited_country_number_of_times_for_visit)
            await page.type('#BF_VISIT_PURP', data.travel_visited_country_recent_travel_info_this_country[0].find(info => info.id === 'travel_visited_country_purpose').value.fe.toUpperCase())
        } else {
            //No History
            await page.click('#BF_VISIT_N')
        }


        //Have you visited any countries other than Korea during the past 5 years ? VISIT_NAT_N, VISIT_NAT_Y
        if (data.travel_visited_country_have_you_visited_any_other_countries_beside_apply_country_in_5y) {
            await page.click('#VISIT_NAT_Y')
            //input travel history
            if (data.travel_visited_country_visited_country_info.length > 2) {
                totalclick = data.travel_visited_country_visited_country_info.length - 2
                for (let index = 0; index < totalclick; index++) {
                    await page.click('#addVisitOthBtn')
                }
            }
            // Loop through each data object and input values into the table
            for (let i = 0; i < data.travel_visited_country_visited_country_info.length; i++) {
                // Fill in "BF_TRA_CNTR" field for the i-th row
                await page.evaluate((i, value) => {
                    document.querySelectorAll('input[name="BF_TRA_CNTR"]')[i].value = value;
                }, i, get_country_name_v2(data.travel_visited_country_visited_country_info[i].find(info => info.id === 'travel_visited_country_country').value.fe.toUpperCase()));

                // Fill in "BF_TRA_PURP" field for the i-th row
                await page.evaluate((i, value) => {
                    document.querySelectorAll('input[name="BF_TRA_PURP"]')[i].value = value;
                }, i, data.travel_visited_country_visited_country_info[i].find(info => info.id === 'travel_visited_country_purpose').value.fe.toUpperCase());

                // Fill in "BF_TRA_ST_DT" field for the i-th row
                await page.evaluate((i, value) => {
                    document.querySelectorAll('input[name="BF_TRA_ST_DT"]')[i].value = value;
                }, i, moment(data.travel_visited_country_visited_country_info[i].find(info => info.id === 'travel_visited_country_from').value.fe).format("YYYY-MM-DD"));

                // Fill in "BF_TRA_EN_DT" field for the i-th row
                await page.evaluate((i, value) => {
                    document.querySelectorAll('input[name="BF_TRA_EN_DT"]')[i].value = value;
                }, i, moment(data.travel_visited_country_visited_country_info[i].find(info => info.id === 'travel_visited_country_to').value.fe).format("YYYY-MM-DD"));
            }
        } else {
            await page.click('#VISIT_NAT_N')
        }
        //Traveling With Family ENT_FML_N,ENT_FML_Y
        if (data.travel_companions_info_travelling_to_korea_with_family_member) {
            await page.click('#ENT_FML_Y')
            //input family info
            if (data.travel_companions_info_kor_companions_info.length > 2) {
                totalclick = data.travel_companions_info_kor_companions_info.length - 2
                for (let index = 0; index < totalclick; index++) {
                    await page.click('#addFamBtn')
                }
            }
            for (let i = 0; i < data.travel_companions_info_kor_companions_info.length; i++) {
                fullName = data.travel_companions_info_kor_companions_info[i].find(info => info.id === 'travel_companions_info_given_name').value.fe + " " + data.travel_companions_info_kor_companions_info[i].find(info => info.id === 'travel_companions_info_surname').value.fe
                await page.evaluate((i, value) => {
                    document.querySelectorAll('input[name="ACPN_ENTRY_FML_EK_NM"]')[i].value = value;
                }, i, fullName);

                await page.evaluate((i, value) => {
                    document.querySelectorAll('input[name="ACPN_ENTRY_FML_BIRTH_YMD"]')[i].value = value;
                }, i, moment(data.travel_companions_info_kor_companions_info[i].find(info => info.id === 'travel_companions_info_date_of_birth').value.fe).format("YYYY-MM-DD"));

                await page.evaluate((i, value) => {
                    document.querySelectorAll('input[name="ACPN_ENTRY_FML_NAT_NM"]')[i].value = value;
                }, i, get_country_name_v2(data.travel_companions_info_kor_companions_info[i].find(info => info.id === 'travel_companions_info_nationality').value.fe));

                await page.evaluate((i, value) => {
                    document.querySelectorAll('input[name="ACPN_ENTRY_FML_REL"]')[i].value = value;
                }, i, data.travel_companions_info_kor_companions_info[i].find(info => info.id === 'travel_companions_info_kor_relationship').value.fe.toUpperCase());
            }
        } else {

            await page.click('#ENT_FML_N')
        }
        //Visit Cost - VISIT_COST
        totalCost = lengthOfStay * 100
        await page.type('#VISIT_COST', totalCost.toString())
        //Expenses for your visit will be paid by (any person including yourself and / or institute)
        if (data.personal_core_info_entiry_paying_the_trip == "yourself") {
            //Name of person/Company(Institute)	 - COST_PAMNT_EK_NM
            fullName = data.passport_core_info_given_name + " " + data.passport_core_info_surname
            await page.type('#COST_PAMNT_EK_NM', fullName.toUpperCase())
            //Relationship to Applicant		     - COST_PAMNT_REL
            await page.type('#COST_PAMNT_REL', "MY SELF")
            //Type of Support		             - COST_PAMNT_DETAIL
            await page.type('#COST_PAMNT_DETAIL', 'Total Trip Cost'.toUpperCase())
            //Contact No.	                     - COST_PAMNT_TEL_NO
            await page.type('#COST_PAMNT_TEL_NO', data.personal_core_info_phone.phone.replace("+", ""))

        } else {
            //Name of person/Company(Institute)	 - COST_PAMNT_EK_NM
            fullName = data.personal_financial_supported_given_name + " " + data.personal_financial_supported_surname
            await page.type('#COST_PAMNT_EK_NM', fullName.toUpperCase())
            //Relationship to Applicant		     - COST_PAMNT_REL
            await page.type('#COST_PAMNT_REL', data.personal_financial_supported_relationship.toUpperCase())
            //Type of Support		             - COST_PAMNT_DETAIL
            await page.type('#COST_PAMNT_DETAIL', 'Total Trip Cost'.toUpperCase())
            //Contact No.	                     - COST_PAMNT_TEL_NO
            await page.type('#COST_PAMNT_TEL_NO', data.personal_financial_supported_phone.phone.replace("+", ""))

        }
        await page.screenshot({ path: `images/Page 2.png`, fullPage: true })
        //No Help Filling Form- DOC_WRIT_HELP_N
        await page.click('#DOC_WRIT_HELP_N')

        //Confirm Form
        await page.click('#APPLY_VISA')
        await page.waitForSelector('#confirmTrue')
        await page.click('#confirmTrue')

        await page.waitForSelector('#alertClose')
        await page.click('#alertClose')

        await page.screenshot({ path: `images/Review Screen.png`, fullPage: true })
        //remove User Photo
        await fs.unlink("images/1.jpg")

        console.log("DOWNLOADING FORM.................")
        let formData = {
            'CMM_TEST_VAL': 'test',
            'IN_PHOTO': '/biz/ap/ov/od/selectInviteeXvarmImage.do',
            'ONLINE_APPL_NO': `${app_no}`,
            'SEQ': '0',
            'TRAN_TYPE': 'ComSubmit',
            'SE_FLAG_YN': '',
            'LANG_TYPE': 'EN'
        }
        const cookies = await page.cookies();
        const resp = await axios({
            method: 'POST',
            url: 'https://visa.go.kr/biz/ap/ov/od/selectPVisaPrint.do',
            data: qs.stringify(formData),
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': 'https://visa.go.kr',
                'Referer': 'https://visa.go.kr/openPage.do?MENU_ID=1020405',
                'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36',
                'Cookie': cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; '),
            },
            responseType: 'arraybuffer',
        })
        console.log("DOWNLOADED FORM SUCCESS.................")
        fs.writeFileSync('FORM.pdf', resp.data)
        await browser.close()
        return { form_data: resp.data }
    } catch (error) {
        console.log(error)
        console.log(error.message)
        await page.screenshot({ path: `images/screen${i++}.png`, fullPage: true })
        await browser.close()
        return null
    }

}



const ZIP_NAME = "steps.zip"
const MAX_RETRY = 2;
const create_form = async (pod_data, attributes) => {
    let form = null
    for (let i = 0; i < MAX_RETRY; i++) {
        form = await korea_visa_form(pod_data, attributes)
        if (form != null) {
            break
        }
    }

    const result = {
        success: false,
        form_file: "",
        form_callback: {},
    }

    const file_name = `Application_Form_${pod_data.passport_core_info_surname}_${pod_data.passport_core_info_given_name}_${moment().unix()}.pdf`
    const buckets = JSON.parse(process.env.ad_s3)
    if (form != null) {
        await s3_upload_buff(buckets.ariadirect_prod_applications, `tasks/${file_name}`, form.form_data)
        result.success = true
        result.form_file = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/tasks/${file_name}`
    }

    await create_zip(ZIP_NAME, 'images/')
    await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${file_name + '-steps.zip'}`, fs.readFileSync(ZIP_NAME))

    fs.rmSync('images', { recursive: true })
    fs.rmSync(ZIP_NAME, { recursive: true })

    result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${file_name + '-steps.zip'}`
    console.log("STEPS:", result.form_callback.steps)
    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form(pod_data, order.service.attributes)
        return data
    } catch (error) {
        console.log(error)
    }
}

module.exports = { main, create_form }