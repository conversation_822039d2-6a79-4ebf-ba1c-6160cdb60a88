exports.isNorthOrSouthCalifornia = (zipCode) => {
    const zip = parseInt(zipCode, 10); // Ensure zipCode is treated as a number

    // More comprehensive and organized ZIP code ranges for Northern California
    const northCaliforniaZIPRanges = [
        { start: 94000, end: 96199 }, // Broad North California range
        { start: 92697, end: 92799 }, // Irvine (sometimes considered borderline, but often South) - EXCLUDE
        { start: 93000, end: 93599 }, // Ventura County (often considered South) - EXCLUDE
        { start: 93600, end: 93999 }, // Central Coast (can be debated, often South of Bay Area) - EXCLUDE
    ].sort((a, b) => a.start - b.start); // Sort ranges for efficiency

    // More comprehensive and organized ZIP code ranges for Southern California
    const southCaliforniaZIPRanges = [
        { start: 90000, end: 93599 }, // Broad South California range
    ].sort((a, b) => a.start - b.start); // Sort ranges for efficiency

    // Helper function to check if a ZIP code falls within any of the ranges
    const isInRange = (zip, ranges) => {
        for (const range of ranges) {
            if (zip >= range.start && zip <= range.end) {
                return true;
            }
        }
        return false;
    };

    if (isInRange(zip, northCaliforniaZIPRanges)) {
        // Further refine North California by excluding areas often considered South
        if (
            (zip >= 92697 && zip <= 92799) || // Irvine
            (zip >= 93000 && zip <= 93599) || // Ventura County
            (zip >= 93600 && zip <= 93999)    // Central Coast
        ) {
            return "South California";
        }
        return "Northern California";
    }

    if (isInRange(zip, southCaliforniaZIPRanges)) {
        return "South California";
    }

    return "Not in California";
};
exports.splitZipCode = (zipCode) => {
    // Check if the ZIP code has 9 digits (ZIP+4 format)
    if (zipCode.length > 5) {
        // Extract the ZIP code (first 5 digits)
        const zip = zipCode.substring(0, 5);
        return zip;
    } else if (zipCode.length === 5) {
        // If it's already 5 digits, it's the ZIP code
        return zipCode;
    } else {
        // Invalid ZIP code length
        return null;
    }
}