
/**
 * Purpose :
 * ENT_PURP_KIND_CD1 => Tourist
 * ENT_PURP_KIND_CD2 => Meeting, Conference
 * ENT_PURP_KIND_CD3 => Medical Tourism
 * ENT_PURP_KIND_CD4 => Business Trip
 * ENT_PURP_KIND_CD5 => Study / Training
 * ENT_PURP_KIND_CD6 => Work
 * ENT_PURP_KIND_CD7 => Trade / Investment / Branch office
 * ENT_PURP_KIND_CD8 => Visiting Family/Relatives/Friends
 * ENT_PURP_KIND_CD9 => Short or Long term visit of Overseas Korean
 * ENT_PURP_KIND_CD10 => Marriage Migrant
 * ENT_PURP_KIND_CD11 => Diplomatic / Official
 */
exports.PURPOSE = {
    "tourist": "ENT_PURP_KIND_CD1",
};
exports.MARITAL_STATUS = {
    "divorced": "MARI_STS_CD_D",
    "single": "MARI_STS_CD_S",
    "married": "MARI_STS_CD_M"
};
exports.EDUCATION = {
    "elementary": "LAST_DEGREE_4",
    "junior_high": "LAST_DEGREE_4",
    "high_school": "LAST_DEGREE_1",
    "college": "LAST_DEGREE_4",
    "graduate": "LAST_DEGREE_2",
    "postgraduate": "LAST_DEGREE_3"
}
exports.OCCUPATION = {
    "business_person": "JOB_CD_1",
    "company_employee": "JOB_CD_3 ",
    "student": "JOB_CD_5",
    "retired": "JOB_CD_6"
}
exports.US_STATE_DIPLOMATIC_OFFICE = {
    "WA": "Washington D.C.",
    "MD": "Washington D.C.",
    "VA": "Washington D.C.",
    "WV": "Washington D.C.",
    "CT": "New York",
    "DE": "New York",
    "NJ": "New York",
    "NY": "New York",
    "PA": "New York",
    "CO": "San Francisco",
    "UT": "San Francisco",
    "WY": "San Francisco",
    "AZ": "Los Angeles",
    "NV": "Los Angeles",
    "NM": "Los Angeles",
    "NH": "Boston",
    "RI": "Boston",
    "ME": "Boston",
    "MA": "Boston",
    "VT": "Boston",
    "IL": "Chicago",
    "IN": "Chicago",
    "IA": "Chicago",
    "KS": "Chicago",
    "KY": "Chicago",
    "MI": "Chicago",
    "MN": "Chicago",
    "MO": "Chicago",
    "NE": "Chicago",
    "ND": "Chicago",
    "OH": "Chicago",
    "SD": "Chicago",
    "WI": "Chicago",
    "ID": "Seattle",
    "MT": "Seattle",
    "OR": "Seattle",
    "WA": "Seattle",
    "AL": "Atlanta",
    "FL": "Atlanta",
    "GA": "Atlanta",
    "NC": "Atlanta",
    "PR": "Atlanta",
    "SC": "Atlanta",
    "TN": "Atlanta",
    "VI": "Atlanta",
    "AR": "Houston",
    "LA": "Houston",
    "OK": "Houston",
    "MS": "Houston",
    "TX": "Houston",
    "AS": "Honolulu",
    "HI": "Honolulu",
    "GU": "Hagatna",
    "MP": "Hagatna",
    "AK": "Anchorage",
    "PA": "Philadelphia",
    "DE": "Philadelphia",
    //Special Case For North & South California
    "Northern California": "San Francisco",
    "South California": "Los Angeles"
}