const { get_order_task, fill_docx_form, get_order_task_pod_data } = require('../../shared/helpers')


const create_form = async (data) => {
    data['fullname'] = `${data.passport_core_info_surname}, ${data.passport_core_info_given_name}${data.passport_core_info_middle_name || ''} `.toUpperCase()
    if (!data.passport_most_recent_pp_details_damaged_pp) {
        data.passport_most_recent_pp_details_damaged_pp = ''
    }
    if (!data.document_copy_of_document_photo_of_damaged_pp) {
        data.document_copy_of_document_photo_of_damaged_pp = ''
    }
    data.filename = `Application_Form_${data.passport_core_info_surname}_${data.passport_core_info_given_name}_AD_USA_009.pdf`
    const pdf_file = await fill_docx_form(__dirname + '/ad_usa_009.docx', data)

    const result = {
        success: true,
        form_file: pdf_file,
        form_callback: {},
    }

    return result
}

const main = async (order_id, task_id) => {
    try {
        const { pod_data } = await get_order_task_pod_data(order_id, task_id)
        const result = await create_form(pod_data)
        return result
    } catch (error) {
        console.log(error)
    }
}

// main(4066, 4745)
module.exports = { main, create_form }