const moment = require('moment')
const fs = require('fs')
const data_mapping = require('../../shared/data_mapping')
const { get_order_task, fill_pdf_form, get_country_name_v2, get_order_task_pod_data } = require('../../shared/helpers')

const NA = "N/A"
const create_form = async (order, task, data) => {
    //Country
    [
        'passport_core_info_country_of_birth',
        'passport_core_info_nationality',
        'passport_core_info_birth_nationality',
        'passport_core_info_issuing_authority',
        'personal_home_address_country',
        'employee_company_info_country',
        'additional_question_schengen_member_state_entry',
        'personal_individual_reference_in_destination_country',
        'employee_company_reference_in_destination_country'
    ].forEach(key => {
        if (data[key]) data[key] = get_country_name_v2(data[key])
    })
    var test = data.passport_core_info_date_of_birth
    //Date
    var dates = [
        'passport_core_info_date_of_birth',
        'passport_core_info_issue_date',
        'passport_core_info_expiration_date',
        'additional_question_family_nationality',
        'additional_question_fingerprint_previously_date',
        'additional_question_family_date_of_birth',
        'service_core_info_entry_date',
        'service_core_info_exit_date',
        'additional_question_residence_immigration_status_expiration_date'
    ]
    dates.forEach(key => {
        if (data[key]) data[key] = moment(data[key]).format('DD-MM-YYYY')
    })
    if (data.passport_core_info_nationality === data.passport_core_info_birth_nationality) {
        data.passport_core_info_birth_nationality = null
    }
    //gender
    data[data.passport_core_info_gender] = true
    //default ordinary_passport
    data['ordinary_passport'] = true
    data['Official passport'] = 'Off'
    data[data.personal_core_info_marital_status] = true
    data[`additional_question_family_relationship_${data.additional_question_family_relationship}`] = true

    data['personal_home_address'] = `${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${data.personal_home_address_state} ${data.personal_home_address_zip_code}, ${data.personal_home_address_country} - ${data.personal_core_info_email_address}`
    data['personal_core_info_phone'] = data.personal_core_info_phone.phone
    data['residence_other_country_yes'] = true
    data['personal_occupation_occupation'] = data_mapping.occupation[data.personal_occupation_occupation]

    data['employee_company_info'] = `${data.employee_company_info_company_school_name}, ${data.employee_company_info_street_number_name}, ${data.employee_company_info_city}, ${data.employee_company_info_state} ${data.employee_company_info_zip_code} - ${data.employee_company_info_phone.phone}`

    data[order.service.attributes.purpose] = true

    data['member_state'] = data.additional_question_schengen_member_state_destination.map(get_country_name_v2).join(", ")
    data[order.service.attributes.number_of_entries] = true

    if (data.additional_question_fingerprint_previously_fingerprint_collected_before) {
        data['additional_question_fingerprint_previously_fingerprint_collected_before_yes'] = true
    } else {
        data['additional_question_fingerprint_previously_fingerprint_collected_before_no'] = true
        data.additional_question_previous_visa_visa_number = null
    }

    data['personal_individual_reference_in_destination_name'] = `${data.employee_company_reference_in_destination_surname} ${data.employee_company_reference_in_destination_given_name}`
    data['personal_individual_reference_in_destination_address'] = `${data.employee_company_reference_in_destination_street_number_name}, ${data.employee_company_reference_in_destination_city}, ${data.employee_company_reference_in_destination_state}`
    data['personal_individual_reference_in_destination_phone'] = data.employee_company_reference_in_destination_phone.phone
    data['employee_company_reference_in_destination_info'] = `${data.employee_company_reference_in_destination_company_name}, ${data.employee_company_reference_in_destination_street_number_name}, ${data.employee_company_reference_in_destination_city}, ${data.employee_company_reference_in_destination_state} ${data.employee_company_reference_in_destination_zip_code}`
    data['employee_company_reference_in_destination_phone'] = data.employee_company_reference_in_destination_phone.phone
    data['contact_person_in_company'] = `${data.employee_company_reference_in_destination_surname} ${data.employee_company_reference_in_destination_given_name} - ${data.employee_company_reference_in_destination_street_number_name}, ${data.employee_company_reference_in_destination_city}, ${data.employee_company_reference_in_destination_state} ${data.employee_company_reference_in_destination_zip_code} - ${data.employee_company_reference_in_destination_email_address}`
    if (data.personal_financial_supported_financial_support_status === 'yourself') {
        data['self_payment'] = true
        data[`self_payment_cash`] = true
        data[`self_payment_credit_card`] = true
        //remove Reference
        data.employee_company_reference_in_destination_company_name = null
        data.employee_company_info_company_school_name = null
    } else {
        data['sponsor'] = true
        if (data.personal_financial_supported_financial_support_status == 'your_company') {
            //Company Name
            data['sponsor_by_company'] = true
            data.employee_company_reference_in_destination_company_name = null
        } else {
            data['sponsor_by_invitation'] = true
            data.personal_financial_supported_company_name = null
            //Reference in Destination
        }
        //Mean of support
        data[data.personal_financial_supported_means_of_support] = true
    }
    //Date & Place

    data['place_and_date'] = `${data.personal_home_address_city}, ${data_mapping.us_state[data.personal_home_address_state]} ${moment().format('DD-MM-YYYY')}`
    data['additional_question_residence_immigration_status_immigration_status'] = data_mapping.us_immigration[data.additional_question_residence_immigration_status_immigration_status]

    data.filename = `Application_Form_${data.passport_core_info_surname}_${data.passport_core_info_given_name}_schengen.pdf`
    data.font_size = 'auto'
    const pdf_file = await fill_pdf_form(__dirname + '/schengen.pdf', data)

    const result = {
        success: true,
        form_file: pdf_file,
        form_callback: {},
    }

    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const result = await create_form(order, task, pod_data)
        return result
    } catch (error) {
        console.log(error)
    }
}
// main(6481, 7607)
module.exports = { main, create_form }