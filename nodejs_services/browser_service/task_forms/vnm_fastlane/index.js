const fs = require('fs');
const axios = require('axios');
const moment = require('moment');
const puppeteer = require('puppeteer');
const iso = require('iso-3166-1');
const qs = require('qs');
const us = require('us');
const AWS = require('aws-sdk');
const archiver = require('archiver');
const { LANGUAGE, AIRLINE, SOCIAL_NETWORK } = require('./data_mapping')
const FormData = require('form-data');
const PhoneNumber = require('libphonenumber-js');
const { exec } = require('child_process');
const { get_order_detail, create_zip, s3_upload_buff, get_order_task_pod_data } = require('../../shared/helpers');

let DEBUG = false
const isPROD = process.env.ad_env === 'prod'
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));
function getFileNameFromUrl(url) {
    const urlObject = new URL(url);
    return urlObject.pathname.split('/').pop();
}
async function downloadAndSaveFiles(page, listPhoto) {
    const uploadField = await page.$('input[type=file][id="upload-photo-mutil"]');
    for (let index = 0; index < listPhoto.length; index++) {
        const element = listPhoto[index];
        const fileName = decodeURIComponent(getFileNameFromUrl(element));
        const path = __dirname + '/' + fileName;

        try {
            const response = await axios.get(element, { responseType: 'arraybuffer' });
            const fileData = Buffer.from(response.data, 'binary');
            await fs.promises.writeFile(path, fileData);
            await uploadField.uploadFile(path);
            await page.waitForSelector(`input[name="Fullname_${index}"][class="form-control ng-pristine ng-untouched ng-valid ng-not-empty"]`)
            await sleep(1000)
            fs.unlinkSync(path);
        } catch (error) {
            console.error("Error:", error);
        }
    }
}
const vnm_fastlane = async (order, task, pod_data) => {
    const browser = await puppeteer.launch({ headless: DEBUG ? false : "new", args: ['--no-sandbox', '--disable-setuid-sandbox'] });
    const page = await browser.newPage();
    if (DEBUG) {
        await page.setViewport({
            width: 1200,
            height: 1000,
            deviceScaleFactor: 1,
        })
    } else {
        await page.setViewport({ width: 1000, height: 1500 });
    }

    page.setDefaultTimeout(30000);

    let index = 1;

    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })

    const web_base = JSON.parse(process.env.ad_endpoint).web_base_url

    try {

        await page.goto('https://azsvn.com');

        await page.waitForSelector('[placeholder="Tên đăng nhập"]')
        await page.type('[placeholder="Tên đăng nhập"]', 'cindy')
        await page.type('[placeholder="Mật khẩu"]', '*********@')
        await page.click('[type="button"]')

        await sleep(2000)
        await page.goto("https://azsvn.com/Admin/FastlaneTransaction/Package?type=fasttrack")

        await page.waitForSelector('[placeholder="Ngày giờ cất cánh/hạ cánh thực tế"]')
        await page.waitForSelector('#ImmigrationType')
        const service_type = order.service.tasks[0] ?? 'arrival'


        const flightTimestamp = moment(service_type === 'departure' ? pod_data.travel_exit_flight_exit_timestamp : pod_data.travel_enter_flight_enter_timestamp).format('YYYY/MM/DD HH:mm');
        await page.$eval('.datepicker', (element, timestamp) => {
            element.value = timestamp;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
        }, flightTimestamp);
        await sleep(1500)
        await page.waitForSelector('.cg-busy-default-text ng-binding', { hidden: true })

        await page.select('#AirportCode', 'SNB')
        if (DEBUG) {
            await page.select('#TerminalType', 'QT')
        } else {
            await page.select('#TerminalType', order.service.attributes.terminal === 'international' ? 'QT' : 'ND')
        }

        await sleep(1500)
        await page.waitForSelector('.cg-busy-default-text ng-binding', { hidden: true })
        await page.select('#ImmigrationType', service_type == 'departure' ? 'X' : 'N') // N = đón, X = tiễn

        await sleep(1500)
        await page.waitForSelector('.cg-busy-default-text ng-binding', { hidden: true })
        const rows = await page.$$('[ng-repeat="item in data track by $index"]')

        await page.screenshot({ path: `images/screen${index++}-Page 1.png` })

        for (const row of rows) {
            const label = await row.$('.label-info');
            const labelText = await label.evaluate(node => node.textContent);
            console.log(labelText)
            if (labelText.trim() === (service_type == 'departure' ? 'VIP' : 'Basic')) {
                const buttons = await row.$$('button[ng-click="register(item.Id);"]');
                await buttons[0].evaluate(node => node.click());
                break;
            }
        }
        await page.waitForSelector('#credit')
        await page.waitForNetworkIdle()
        let fullName = pod_data.travel_traveler_contact_info_full_name
        if (!fullName) {
            fullName = `${pod_data.travel_traveler_contact_info_given_name} ${pod_data.travel_traveler_contact_info_surname}`;
        }
        await page.type('[ng-model="transaction.ContactInfo_FullName"]', fullName)

        await page.waitForSelector('#ContactInfo_ContactMethod')
        var contactMethod = 'social_network'
        await page.select('#ContactInfo_ContactMethod', contactMethod === 'social_network' ? 'SOCIAL_NETWORK' : 'TELEPHONE');
        if (contactMethod === 'social_network') {
            await page.waitForSelector('#ContactInfo_SocialNetworkChannel')
            var socialNetworkChannel = SOCIAL_NETWORK[pod_data.travel_traveler_contact_info_social_network_channel]
            await page.select('#ContactInfo_SocialNetworkChannel', socialNetworkChannel ?? "ZALO")
            await page.type('[ng-model="transaction.ContactInfo_SocialNetworkID"]', socialNetworkChannel ? pod_data.travel_traveler_contact_info_social_network_id : '6508899891')
        } else {
            await page.type('[ng-model="transaction.ContactInfo_PhoneNumber"]', pod_data.travel_traveler_contact_info_phone.phone)
        }
        await page.evaluate(() => {
            const input = document.querySelector('[ng-model="transaction.WelcomeName"]');
            if (input) {
                input.value = '';
            }
        });
        await page.type('[ng-model="transaction.WelcomeName"]', pod_data.travel_passenger_info_welcome_name)
        for (let i = 2; i <= pod_data.travel_passenger_info_no_of_traveler; i++) {
            await page.click('[ng-click="updateNumberOfTraveleBtn(1)"]')
            await page.waitForNetworkIdle({ idleTime: 100 })
            const severNumOfTravelerText = await page.evaluate(() => {
                return document.querySelector('[ng-model="numberOfTraveler"]').value
            });
            if (String(i) !== severNumOfTravelerText) {
                i = severNumOfTravelerText
            }
        }

        await page.select('#FlightInfo_Airline', AIRLINE[service_type == 'departure' ? pod_data.travel_exit_flight_exit_airline : pod_data.travel_enter_flight_enter_airline] ?? "")
        await page.type('[ng-model="transaction.FlightInfo_FlightNo"]', service_type == 'departure' ? pod_data.travel_exit_flight_exit_flight : pod_data.travel_enter_flight_enter_flight);

        await page.select('#AdditionalInfo_PreferredLanguage', LANGUAGE[pod_data.traveler_additional_information_prefer_language ?? 'en'])

        let reservationNumber = service_type == 'departure' ? pod_data.travel_exit_flight_reservation_number : pod_data.travel_enter_flight_reservation_number
        if (reservationNumber) {
            await page.type('[ng-model="transaction.FlightInfo_ReservationNumber"]', reservationNumber);
        }

        let note = "";
        if (pod_data.traveler_additional_information_note) {
            note += `Note: ${pod_data.traveler_additional_information_note} - `;
        }
        note += `Order #${order.id}: ${web_base}/dashboard/orders/detail?order_id=${order.id}`;
        await page.type('[ng-model="transaction.AdditionalInfo_Note"]', note)

        //Process Passport Photo
        const listPhoto = pod_data.travel_passenger_info_passport_photos
        await downloadAndSaveFiles(page, listPhoto)




        const document_id = await page.$eval('input[type="hidden"][id="packageId"]', el => el.value)
        const document_guid = await page.$eval('input[type="hidden"][id="guid"]', el => el.value)

        await page.screenshot({ path: `images/screen${index++}-Page .png` })
        await sleep(5000)
        if (isPROD) {
            //SUBMIT
            await page.click('[ng-click="doAdd();"]')
        } else {
            await page.click('[ng-click="doAddTemp();"]')
        }
        await sleep(5000)
        await browser.close()
        return {
            document_id,
            created_at: moment().format('DD/MM/YYYY'),
            tracking_url: `https://azsvn.com/admin/FastlaneTransaction/Order?guid=${document_guid}&id=${document_id}`,
        }
    } catch (error) {
        console.log(error)
        console.log(error.message)
        await page.screenshot({ path: `images/screen${index++}.png` })
        await browser.close()
        return null
    }

}

const ZIP_NAME = "steps.zip"
const create_form = async (order, task, pod_data) => {
    const result = {
        form_file: "",
        form_callback: {},
    }
    const form_callback = await vnm_fastlane(order, task, pod_data)
    if (form_callback?.document_id) {
        result.form_callback = form_callback
        result.success = true
    }
    const file_name = `Application_Form_${moment().unix()}.pdf`

    const buckets = JSON.parse(process.env.ad_s3)
    await create_zip(ZIP_NAME, 'images/')

    await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${file_name + '-steps.zip'}`, fs.readFileSync(ZIP_NAME))

    fs.rmSync('images', { recursive: true })
    fs.rmSync(ZIP_NAME, { recursive: true })

    result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${file_name + '-steps.zip'}`
    console.log("STEPS:", result.form_callback.steps)
    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form(order, task, pod_data)
        return data
    } catch (error) {
        console.log(error)
    }
}


// const MOCK = {
//     order_id: 4055,
//     task_id: 4682
// }

// main(4323, 5046)
module.exports = { main, create_form }