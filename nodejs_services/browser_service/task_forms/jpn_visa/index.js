const moment = require('moment')
const fs = require('fs')
const share_data_mapping = require('../../shared/data_mapping')
const data_mapping = require('./data_mapping')
const { get_order_task, fill_pdf_form, get_country_name_v2, get_order_task_pod_data } = require('../../shared/helpers')

function getRandomHotel() {
    const hotels = data_mapping.HOTEL;
    const randomIndex = Math.floor(Math.random() * hotels.length);
    return hotels[randomIndex];
}
const create_form = async (data, attributes) => {
    //Length Of Stay
    const entryDate = moment(data.service_core_info_entry_date);
    const exitDate = moment(data.service_core_info_exit_date);
    const lengthOfStay = exitDate.diff(entryDate, 'days')
    data.length_of_stay = lengthOfStay.toString() + " Day(s)"
    //TODO Dynamic
    data.port_of_entry = "NARITA AIRPORT"
    data.airline_name = "VIETNAM AIRLINES"
    //Country
    var countries = [
        'passport_core_info_country_of_birth',
        'passport_core_info_nationality',
        'passport_core_info_birth_nationality',
        'passport_core_info_issuing_authority',
        'personal_home_address_country',
        'employee_company_info_country'
    ]
    countries.forEach(key => {
        if (data[key]) data[key] = get_country_name_v2(data[key])
    })
    var test = data.passport_core_info_date_of_birth
    //Date
    var dates = [
        'passport_core_info_date_of_birth',
        'passport_core_info_issue_date',
        'passport_core_info_expiration_date',
        'service_core_info_entry_date',
        'service_core_info_exit_date',
    ]
    dates.forEach(key => {
        if (data[key]) data[key] = moment(data[key]).format('DD/MM/YYYY')
    })
    //gender
    data[data.passport_core_info_gender] = true
    data[data.personal_core_info_marital_status] = true
    data.passport_core_info_place_of_birth = `${data.passport_core_info_city_of_birth}, ${data.passport_core_info_state_of_birth}, ${data.passport_core_info_country_of_birth}`
    data['personal_home_address'] = `${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${data.personal_home_address_state} ${data.personal_home_address_zip_code}, ${data.personal_home_address_country}`
    data['personal_core_info_phone'] = data.personal_core_info_phone.phone
    if (data.personal_occupation_occupation == "student") {
        data['personal_occupation_occupation'] = share_data_mapping.occupation[data.personal_occupation_occupation]
    } else {
        data['personal_occupation_occupation'] = share_data_mapping.occupation[data.personal_occupation_occupation] + " - " + data.employee_company_info_job_title
        data['employee_company_info_phone'] = data.employee_company_info_phone.phone
        data['employee_company_info_address'] = `${data.employee_company_info_street_number_name}, ${data.employee_company_info_city}, ${data.employee_company_info_state} ${data.employee_company_info_zip_code}`
    }


    hotel = getRandomHotel()
    data.hotel_name = hotel.Name
    data.hotel_phone = hotel.Phone
    data.hotel_address = hotel.Address

    // data.email = process.env.ad_env === 'prod' ? '<EMAIL>' : '<EMAIL>'
    data.email = data.personal_core_info_email_address

    data.purpose = attributes.purpose == "tourist" ? "SIGHTSEEING" : "BUSINESS"
    //Second Occupation (Parent's Occupation or Spouse's Occupation)
    data.parent_occupation = data.family_spouse_occupation ?? data.family_parent_info_1_occupation
    data.filename = `Application_Form_${data.passport_core_info_surname}_${data.passport_core_info_given_name}_japan.pdf`
    data.font_size = 'auto'
    const pdf_file = await fill_pdf_form(__dirname + '/japan.pdf', data)

    const result = {
        success: true,
        form_file: pdf_file,
        form_callback: {},
    }

    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const result = await create_form(pod_data, order.service.attributes)
        return result
    } catch (error) {
        console.log(error)
    }
}
// main(4840, 5678)
module.exports = { main, create_form }