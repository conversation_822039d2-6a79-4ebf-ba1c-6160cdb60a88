const moment = require('moment')
const fs = require('fs')
const data_mapping = require('../../shared/data_mapping')
const { get_order_detail, get_order_task, capitalize, fill_pdf_form, get_country_name, get_country_name_v2, get_order_task_pod_data } = require('../../shared/helpers')


const create_form = async (order, data) => {
    [
        'personal_permanent_address_country',
        'passport_core_info_country_of_birth',
    ].forEach(key => {
        if (data[key]) data[key] = get_country_name_v2(data[key])
    })
    if (data['personal_permanent_address_country'] === 'United States') {
        data['personal_permanent_address_state'] = data_mapping.us_state[data.personal_permanent_address_state] ?? data.personal_permanent_address_state
    }
    data['passport_core_info_name'] = `${data.passport_core_info_surname} ${data.passport_core_info_given_name}`
    data['passport_core_info_name_sign'] = data.passport_core_info_name
    data['passport_core_info_date_of_birth_date'] = moment(data.passport_core_info_date_of_birth).format('DD')
    data['passport_core_info_date_of_birth_month'] = moment(data.passport_core_info_date_of_birth).format('MM')
    data['passport_core_info_date_of_birth_year'] = moment(data.passport_core_info_date_of_birth).format('YYYY')
    data['passport_core_info_place_of_birth'] = `${data.passport_core_info_state_of_birth}, ${data.passport_core_info_country_of_birth}`

    data[`passport_core_info_gender_${data.passport_core_info_gender.toLowerCase()}`] = true
    data['personal_permanent_address'] = capitalize(
        [
            `${data.personal_permanent_address_address},`,
            data.personal_permanent_address_city,
            data.personal_permanent_address_state,
            `${data.personal_permanent_address_zip_code},`,
            data.personal_permanent_address_country
        ].filter(v => v).join(' '))

    if (data.passport_core_info_issue_date) {
        data['passport_core_info_issue_date_date'] = moment(data.passport_core_info_issue_date).format('DD')
        data['passport_core_info_issue_date_month'] = moment(data.passport_core_info_issue_date).format('MM')
        data['passport_core_info_issue_date_year'] = moment(data.passport_core_info_issue_date).format('YYYY')
    }

    if (data.family_father_surname) {
        data['family_father_name'] = `${data.family_father_surname} ${data.family_father_given_name}`
        data['family_father_date_of_birth_date'] = moment(data.family_father_date_of_birth).format('DD')
        data['family_father_date_of_birth_month'] = moment(data.family_father_date_of_birth).format('MM')
        data['family_father_date_of_birth_year'] = moment(data.family_father_date_of_birth).format('YYYY')
    }

    if (data.family_mother_surname) {
        data['family_mother_name'] = `${data.family_mother_surname} ${data.family_mother_given_name}`
        data['family_mother_date_of_birth_date'] = moment(data.family_mother_date_of_birth).format('DD')
        data['family_mother_date_of_birth_month'] = moment(data.family_mother_date_of_birth).format('MM')
        data['family_mother_date_of_birth_year'] = moment(data.family_mother_date_of_birth).format('YYYY')
    }

    if (data.family_spouse_surname) {
        data['family_spouse_name'] = `${data.family_spouse_surname} ${data.family_spouse_given_name}`
        data['family_spouse_date_of_birth_date'] = moment(data.family_spouse_date_of_birth).format('DD')
        data['family_spouse_date_of_birth_month'] = moment(data.family_spouse_date_of_birth).format('MM')
        data['family_spouse_date_of_birth_year'] = moment(data.family_spouse_date_of_birth).format('YYYY')
    }

    data['tasks'] = `${data.service_core_info_tasks ? data_mapping.passport_tasks[order.service.tasks[0]] : ''}`

    if (data.additional_question_residence_immigration_status_given_name && data.additional_question_residence_immigration_status_surname) {
        const another_name = data.additional_question_residence_immigration_status_surname + ' ' + data.additional_question_residence_immigration_status_given_name
        if (another_name !== data.passport_core_info_name) {
            data['tasks'] += ` - Bị chú: ${another_name}`
        }
    }

    data['additional_question_recommended_content'] = data_mapping.vnm_content[data.additional_question_recommended_reason_request]
    if (data.document_residence_immigration_status_document_type) {
        data['immigration_status'] = `${data_mapping.document_type[data.document_residence_immigration_status_document_type]}: Số  ${data.additional_question_residence_immigration_status_document_number}, ngày cấp ${moment(data.additional_question_residence_immigration_status_issue_date).format('DD/MM/YYYY')}`
    }

    const frame = {
        x: 457,
        // y: 65,
        y: 640,
        width: 78,
        height: 88,
        photo_url: data.photo_passport_photo_copy_of_photo,
    }
    data.filename = `Application_Form_${data.passport_core_info_surname}_${data.passport_core_info_given_name}_VMM_001.pdf`
    const pdf_file = await fill_pdf_form(__dirname + '/vnm_001.pdf', data, frame)

    const result = {
        success: true,
        form_file: pdf_file,
        form_callback: {},
    }

    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const result = await create_form(order, pod_data)
        return result
    } catch (error) {
        console.log(error)
    }
}

module.exports = { main, create_form }