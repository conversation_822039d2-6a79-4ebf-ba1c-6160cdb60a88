const moment = require('moment')
const fs = require('fs')
const data_mapping = require('./data_mapping')
const shared_data_mapping = require('../../shared/data_mapping')
const { get_order_task, fill_pdf_form, get_country_name_v2, get_order_task_pod_data } = require('../../shared/helpers')

const convert_state = (state) => shared_data_mapping.us_state[state] ? shared_data_mapping.us_state[state] : state

function getRandomHotel() {
    const hotels = data_mapping.HOTEL;
    const randomIndex = Math.floor(Math.random() * hotels.length);
    return hotels[randomIndex];
}

const create_form = async (data, attributes) => {
    //Length Of Stay
    // data = upperData(data)
    const entryDate = moment(data.service_core_info_entry_date);
    const exitDate = moment(data.service_core_info_exit_date);
    const lengthOfStay = exitDate.diff(entryDate, 'days')
    data.length_of_stay = lengthOfStay.toString() + " Day(s)"
    //Country
    var countries = [
        'passport_core_info_country_of_birth',
        'passport_core_info_nationality',
        'passport_core_info_birth_nationality',
        'passport_core_info_issuing_authority',
        'personal_home_address_country',
        'employee_company_info_country',
        'family_spouse_nationality'
    ]
    countries.forEach(key => {
        if (data[key]) data[key] = get_country_name_v2(data[key])
    })
    //State
    var states = [
        'personal_home_address_state',
        'passport_core_info_state_of_birth'
    ]
    states.forEach(key => {
        if (data[key]) data[key] = convert_state(data[key])
    })
    //Date
    var dates = [
        'passport_core_info_date_of_birth',
        'passport_core_info_issue_date',
        'passport_core_info_expiration_date',
        'service_core_info_entry_date',
        'service_core_info_exit_date',
    ]
    dates.forEach(key => {
        if (data[key]) data[key] = moment(data[key]).format('DD MM YYYY')
    })
    //Name (max length 25 for each line)
    fullName = data.passport_core_info_surname + " " + data.passport_core_info_given_name
    if (fullName.length > 25) {
        data.fullName1 = fullName.substring(0, 25)
        data.fullName2 = fullName.substring(25)
    } else {
        data.fullName1 = fullName
    }
    // gender
    data[data.passport_core_info_gender] = true
    data[data.personal_core_info_marital_status] = true
    // hotel   
    hotel = getRandomHotel()
    data.hotel_no = hotel.No
    data.hotel_name = hotel.Name
    data.hotel_phone = hotel.Phone
    data.hotel_address = hotel.Address
    data.hotel_zip_code = hotel.Zipcode
    //religion
    data.relegion = "N/A"
    // Local contact
    data.local_contact_line_1 = hotel.Name.substring(0, 25)
    data.local_contact_line_2 = hotel.Name.substring(25, 50)
    data.local_contact_phone = hotel.Phone
    data.local_contact_email = hotel.Email
    data.local_relationship = "tenant"
    data.email = '<EMAIL>'
    data.phone = data.personal_core_info_phone.phone

    // Spouse
    data.nationality_spouse_not_sgp = data.family_spouse_nationality !== 'SGP'
    // data.personal_occupation_occupation = data.personal_occupation_occupation
    data[data.personal_education_education] = true
    data.detail_purpose = "Having a meeting with partners and clients"
    //uppercase all
    for (const key in data) {
        if (data[key] != null && typeof data[key] === 'string' && !data[key].startsWith('http'))
            data[key] = data[key].toUpperCase();
    }
    data.create_form_date = moment().format('DD/MM/YYYY')
    data.filename = `Application_Form_${data.passport_core_info_surname}_${data.passport_core_info_given_name}_singapore.pdf`
    data.font_size = 'auto'


    const pdf_file = await fill_pdf_form(__dirname + '/singapore.pdf', data)

    const result = {
        success: true,
        form_file: pdf_file,
        form_callback: {},
    }

    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const result = await create_form(pod_data, order.service.attributes)
        return result
    } catch (error) {
        console.error(error)
    }
}
// main(6409, 7517)
module.exports = { main, create_form }