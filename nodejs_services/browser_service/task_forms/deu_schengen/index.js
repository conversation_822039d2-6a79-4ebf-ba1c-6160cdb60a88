const fs = require('fs');
const moment = require('moment');
const puppeteer = require('puppeteer');
const archiver = require('archiver');
const { s3_upload_buff, get_order_task_pod_data } = require('../../shared/helpers');
const shared_data_mapping = require('../../shared/data_mapping')
let DEBUG = false
function getRandomHotel() {
    const hotels = [{
        name: "Hotel Regent Berlin",
        street: "Charlottenstraße",
        house_number: "49",
        zipcode: "10117",
        city: "Berlin",
    }, {
        name: "Hotel Louis C. Jacob",
        street: "Elbchaussee",
        house_number: "401-403",
        zipcode: "22609",
        city: "Hamburg"
    },
    {
        name: "Hotel Sofitel Munich Bayerpost",
        street: "Bayerstraße",
        house_number: "12",
        zipcode: "80335",
        city: "München"
    }];
    const randomIndex = Math.floor(Math.random() * hotels.length);
    return hotels[randomIndex];
}
function extractHouseNumberAndStreet(address) {
    // Regular expression to match house number and street name
    const regex = /^(\d+(?:\/\d+)?)\s+(.*)$/;
    const match = regex.exec(address);

    if (match) {
        const houseNumber = match[1];
        const street = match[2];
        return { houseNumber, street };
    } else {
        return { houseNumber: null, street: null };
    }
}
const chn_evisa = async (data, attributes) => {
    const browser = await puppeteer.launch({
        headless: DEBUG ? false : "new", args: ['--no-sandbox', '--disable-setuid-sandbox']
        , ignoreDefaultArgs: ["--enable-automation"]
    });
    const page = await browser.newPage();
    const viewport = DEBUG ? { width: 1200, height: 800, deviceScaleFactor: 1 } : { width: 1000, height: 1500 };
    await page.setViewport(viewport);

    page.setDefaultTimeout(30000);

    let index = 1;

    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })

    try {
        console.log("Loading...")
        await page.goto('https://videx.diplo.de/videx/visum-erfassung/videx-kurzfristiger-aufenthalt');
        await page.waitForSelector('[class="form-control form-control-sm"]')
        await page.select('[class="form-control form-control-sm"]', "en")
        //================================================Personal Info================================================
        await page.waitForSelector('[id="antragsteller.familienname"]')
        await page.type('[id="antragsteller.familienname"]', data.passport_core_info_surname)
        await page.type('[id="antragsteller.geburtsname"]', data.passport_core_info_former_surname)

        await page.type('[id="antragsteller.vorname"]', data.passport_core_info_given_name)
        await page.type('[id="antragsteller.geburtsdatum"]', moment(data.passport_core_info_date_of_birth).format("DD.MM.YYYY"))
        await page.type('[id="antragsteller.geburtsort"]', data.passport_core_info_city_of_birth)
        await page.select('[id="antragsteller.geburtsland"]', data.passport_core_info_country_of_birth)
        await page.select('[id="antragsteller.geschlecht"]', data.passport_core_info_gender == "F" ? "W" : "M")
        await page.select('[id="antragsteller.familienstand"]', {
            "single": "ledig",
            "married": "verheiratet",
            "divorced": "geschieden",
            "widower": "verwitwet",
            "widow": "verwitwet"
        }[data.personal_core_info_marital_status] ?? 'ledig')
        await page.select('[id="antragsteller.staatsangehoerigkeitListe[0]"]', data.passport_core_info_nationality)
        await page.select('[id="antragsteller.staatsangehoerigkeitBeiGeburtListe[0]"]', data.passport_core_info_birth_nationality)
        //================================================Occupation================================================
        await page.select('[id="antragsteller.personendaten.berufdaten.berufAuswahl"]', {
            "business_person": "07",
            "company_executive": "36",
            "computer_expert": "17",
            "manager": "08",
            "self_employed": "25",
            "student": "31",
            "retired": "99",
            "company_employee": "99"
        }[data.personal_occupation_occupation] ?? 'retired')
        if (data.personal_occupation_occupation != "retired") {
            await page.type('[id="antragsteller.personendaten.berufdaten.firmenname"]', data.employee_company_info_company_school_name)
            companyStreetAddress = extractHouseNumberAndStreet(data.employee_company_info_street_number_name)
            await page.type('[id="antragsteller.personendaten.berufdaten.strasse"]', companyStreetAddress.street)
            await page.type('[id="antragsteller.personendaten.berufdaten.hausnummer"]', companyStreetAddress.houseNumber)
            await page.type('[id="antragsteller.personendaten.berufdaten.plz"]', data.employee_company_info_zip_code)
            await page.type('[id="antragsteller.personendaten.berufdaten.ort"]', data.employee_company_info_city)
            await page.select('[id="antragsteller.personendaten.berufdaten.land"]', data.employee_company_info_country)
        } else {
            await page.type('[id="antragsteller.personendaten.berufdaten.firmenname"]', "RETIRED")
        }

        //================================================Address================================================
        homeStreetAddress = extractHouseNumberAndStreet(data.personal_home_address_street_number_name)
        await page.type('[id="antragsteller.personendaten.staendigeAnschrift.strasse"]', homeStreetAddress.street)
        await page.type('[id="antragsteller.personendaten.staendigeAnschrift.hausnummer"]', homeStreetAddress.houseNumber)
        await page.type('[id="antragsteller.personendaten.staendigeAnschrift.plz"]', data.personal_home_address_zip_code)
        await page.type('[id="antragsteller.personendaten.staendigeAnschrift.ort"]', data.personal_home_address_city)
        await page.select('[id="antragsteller.personendaten.staendigeAnschrift.land"]', data.personal_home_address_country)

        await page.type('[id="antragsteller.personendaten.staendigeAnschrift.kontaktdaten.telefon"]', data.personal_core_info_phone.phone.substring(1))
        await page.type('[id="antragsteller.personendaten.staendigeAnschrift.kontaktdaten.email"]', data.personal_core_info_email_address)
        if (data.passport_core_info_nationality != attributes.region_of_residence) {
            await page.click('[id="antragsteller.aufenthaltsberechtigung"]')
            await page.type('[id="antragsteller.aufenthaltsberechtigung.artDerRueckkehrberechtigung"]', shared_data_mapping.us_immigration[data.additional_question_residence_immigration_status_immigration_status])
            await page.type('[id="antragsteller.aufenthaltsberechtigung.rueckkehrDokumentNr"]', data.additional_question_residence_immigration_status_document_number)
            await page.type('[id="antragsteller.aufenthaltsberechtigung.rueckkehrGueltigBis"]', moment(data.additional_question_residence_immigration_status_expiration_date).format("DD.MM.YYYY"))
        }
        //================================================Passport Info================================================
        await page.select('[id="antragsteller.pass.passArt"]', "01") //Ordinary passport
        await page.type('[id="antragsteller.pass.passnummer"]', data.passport_core_info_passport_number)
        await page.type('[id="antragsteller.pass.gueltigVon"]', moment(data.passport_core_info_issue_date).format("DD.MM.YYYY"))
        await page.type('[id="antragsteller.pass.gueltigBis"]', moment(data.passport_core_info_expiration_date).format("DD.MM.YYYY"))
        await page.select('[id="antragsteller.pass.ausstellenderStaat"]', data.passport_core_info_issuing_authority)
        //================================================Biometric data================================================
        if (data.additional_question_previous_visa_have_you_granted_visa_before == true) {
            await page.click('[id="reisedaten.letzteVisumStickernummer"]')
            await page.type('[id="reisedaten.letzteVisumStickernummer.nummer"]', data.additional_question_previous_visa_visa_number)
            //TODO Issue byy Country ??
            await page.select('[id="reisedaten.letzteVisumStickernummer.schengenLaendercode"]', "DEU")
        }
        if (data.additional_question_fingerprint_previously_fingerprint_collected_before == true) {
            await page.click('[id="antragsteller.biometrie.fingerabdrueckeErfassungsDatum_vorhanden"]')
            await page.type('[id="antragsteller.biometrie.fingerabdrueckeErfassungsDatum"]', moment(data.additional_question_fingerprint_previously_date).format("DD.MM.YYYY"))
        }
        //================================================Travel data================================================
        await page.select('[id="reisedaten.aufenthaltszweckListe[0]"]', attributes.purpose == "tourist" ? "10" : "01")
        await page.select('[id="reisedaten.ersteinreiseStaat"]', data.additional_question_schengen_member_state_entry)
        for (let i = 0; i < data.additional_question_schengen_member_state_destination.length; i++) {
            await page.select(`[id="reisedaten.hauptzielListe[${i}]"]`, data.additional_question_schengen_member_state_destination[i])
        }
        await page.select('[id="visumdaten.anzahlEinreisen"]', {
            "double_entries": "02",
            "single_entry": "01",
            "multiple_entries": "MULT",
        }[attributes.number_of_entries] ?? '01')
        await page.type('[id="visumdaten.gueltigkeit.von"]', moment(data.service_core_info_entry_date).format("DD.MM.YYYY"))
        await page.type('[id="visumdaten.gueltigkeit.bisGenau.value"]', moment(data.service_core_info_exit_date).format("DD.MM.YYYY"))
        //================================================Reference================================================
        await page.select('[id = "referenz.referenzArt"]', attributes.purpose == "tourist" ? "12: 4" : "11: 3")
        if (attributes.purpose == "tourist") {
            hotel = getRandomHotel()
            await page.type('[id="referenz.organisation.name"]', hotel.name)
            await page.select('[id="referenz.organisation.land"]', "DEU")

            await page.type('[id="referenz.ansprechpartner.anschrift.strasse"]', hotel.street)
            await page.type('[id="referenz.ansprechpartner.anschrift.hausnummer"]', hotel.house_number)
            await page.type('[id="referenz.ansprechpartner.anschrift.plz"]', hotel.zipcode)
            await page.type('[id="referenz.ansprechpartner.anschrift.ort"]', hotel.city)
            await page.select('[id="referenz.ansprechpartner.anschrift.land"]', "DEU")
            //Tourist
        } else {
            //Business
            await page.type('[id="referenz.organisation.name"]', data.employee_company_reference_in_destination_company_name)
            await page.type('[id="referenz.organisation.sitz"]', data.employee_company_reference_in_destination_city)
            await page.select('[id="referenz.organisation.land"]', data.employee_company_reference_in_destination_country)
            await page.type('[id="referenz.ansprechpartner.familienname"]', data.employee_company_reference_in_destination_surname)
            await page.type('[id="referenz.ansprechpartner.vorname"]', data.employee_company_reference_in_destination_given_name)

            referenceStreetAddress = extractHouseNumberAndStreet(data.employee_company_reference_in_destination_street_number_name)
            await page.type('[id="referenz.ansprechpartner.anschrift.strasse"]', referenceStreetAddress.street)
            await page.type('[id="referenz.ansprechpartner.anschrift.hausnummer"]', referenceStreetAddress.houseNumber)
            await page.type('[id="referenz.ansprechpartner.anschrift.plz"]', data.employee_company_reference_in_destination_zip_code)
            await page.type('[id="referenz.ansprechpartner.anschrift.ort"]', data.employee_company_reference_in_destination_city)
            await page.select('[id="referenz.ansprechpartner.anschrift.land"]', data.employee_company_reference_in_destination_country)
        }
        //================================================Assumption of costs================================================
        switch (data.personal_financial_supported_financial_support_status) {
            case "your_company": {
                await page.click('[id="reisedaten.reisekostenUebernahme.dritte"]')
                await page.click('[id="reisedaten.reisekostenUebernahme.organisation"]')
                await page.select('[id="verpflichtungserklaerungsgeber.art"]', "2")
                await page.waitForSelector('[id="verpflichtungserklaerungsgeber.organisation.name"]')
                await page.type('[id="verpflichtungserklaerungsgeber.organisation.name"]', data.employee_company_info_company_school_name)
                companyStreetAddress = extractHouseNumberAndStreet(data.employee_company_info_street_number_name)
                await page.type('[id="verpflichtungserklaerungsgeber.ansprechpartner.anschrift.strasse"]', companyStreetAddress.street)
                await page.type('[id="verpflichtungserklaerungsgeber.ansprechpartner.anschrift.hausnummer"]', companyStreetAddress.houseNumber)
                await page.type('[id="verpflichtungserklaerungsgeber.ansprechpartner.anschrift.plz"]', data.employee_company_info_zip_code)
                await page.type('[id="verpflichtungserklaerungsgeber.ansprechpartner.anschrift.ort"]', data.employee_company_info_city)
                await page.select('[id="verpflichtungserklaerungsgeber.ansprechpartner.anschrift.land"]', data.employee_company_info_country)
                await page.type('[id="verpflichtungserklaerungsgeber.ansprechpartner.familienname"]', data.personal_financial_supported_surname)
                await page.type('[id="verpflichtungserklaerungsgeber.ansprechpartner.vorname"]', data.personal_financial_supported_given_name)
                //TODO - Name 
                break;
            }
            case "inviting_company": {
                await page.click('[id="reisedaten.reisekostenUebernahme.dritte"]')
                await page.click('[id="reisedaten.reisekostenUebernahme.einlader"]')
                break;
            }
            default: {
                await page.click('[id="reisedaten.reisekostenUebernahme.antragsteller"]')
                break;
            }
        }
        switch (data.personal_financial_supported_means_of_support) {
            case "accommodation_provied": {
                await page.click('[id="reisedaten.lebensunterhalt.unterkunft"]')
                break;

            }
            case "all_expenses_covered": {
                await page.click('[id="reisedaten.lebensunterhalt.vollstaendigeKostenuebernahme"]')
                break;

            }
            case "prepaid_transport": {
                await page.click('[id="reisedaten.lebensunterhalt.befoerderung"]')
                break;
            }
            default: {
                await page.click('[id="reisedaten.lebensunterhalt.bar"]')
                break;
            }
        }
        const downloadPromise = new Promise(resolve => {
            page.once('response', async (response) => {
                if (response.url().includes('pdf')) {
                    const pdfBuffer = await response.buffer();
                    resolve(pdfBuffer);
                }
            });
        });
        await page.screenshot({ path: `images/Page 1.png`, fullPage: true })
        const timeoutPromise = new Promise(resolve => setTimeout(resolve, 5000)); // Adjust timeout as needed
        await page.click('body > app-root > ng-component > app-visum-form > div > app-button-bar > div.row.mt-3.mb-3.justify-content-between > div.col-md-8.button-bar-align > button:nth-child(2) > span')
        const form = await Promise.race([downloadPromise, timeoutPromise]);
        if (form) {
            await browser.close()
            return { form: form, passport_number: data.passport_core_info_passport_number }
        } else {
            console.log('Download timed out');
        }

    } catch (error) {
        console.log(error)
        console.log(error.message)
        await page.screenshot({ path: `images/errorImage_${moment().valueOf()}.png`, fullPage: true })
        await browser.close()
        return null
    }

}
const get_pod_values = (input_pods) => {
    let results = {};

    for (const key in input_pods) {
        const { category, sub_category, name, value, option_choice } = input_pods[key];

        if (value?.fe != null) {
            results[`${category}_${sub_category}_${name}`] = value.fe;
            if (option_choice && Object.keys(option_choice)?.length > 0)
                results = { ...results, ...get_pod_values(option_choice[value?.fe]) };
        }
    }

    return results;
}

const ZIP_NAME = "steps.zip"
const create_zip = async () => {
    return new Promise((resolve, reject) => {
        const output = fs.createWriteStream(ZIP_NAME);
        const archive = archiver('zip', { zlib: { level: 9 } });

        output.on('close', () => {
            console.log('ZIP archive created successfully.');
            resolve();
        });

        archive.on('error', (err) => {
            reject(err);
        });

        archive.pipe(output);
        archive.directory("images/", false);
        archive.finalize();
    });
}
const MAX_RETRY = 2;
const create_form = async (pod_data, order, task) => {
    console.log(JSON.stringify(pod_data, null, 2))
    let form_data = null
    for (let i = 0; i < MAX_RETRY; i++) {
        form_data = await chn_evisa(pod_data, order.service.attributes)
        if (form_data != null) {
            break
        }
    }

    const result = {
        form_file: "",
        form_callback: {},
    }

    const file_name = `Application_Form_${pod_data.passport_core_info_surname}_${pod_data.passport_core_info_given_name}_${moment().unix()}.pdf`

    const buckets = JSON.parse(process.env.ad_s3)
    if (form_data.form != null) {
        // Save the PDF to a file
        fs.writeFileSync("images/form.pdf", form_data.form);

        await s3_upload_buff(buckets.ariadirect_prod_applications, `tasks/${file_name}`, form_data.form)
        result.form_file = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/tasks/${file_name}`
    }
    result.form_callback.passport_number = form_data.passport_number
    await create_zip()
    await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${file_name + '-steps.zip'}`, fs.readFileSync(ZIP_NAME))

    fs.rmSync('images', { recursive: true })
    fs.rmSync(ZIP_NAME, { recursive: true })

    result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${file_name + '-steps.zip'}`
    console.log("STEPS:", result.form_callback.steps)

    result.success = true
    return result
}

const main = async (order_id, task_id) => {
    try {
        const { pod_data, order, task } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form(pod_data, order, task)
        return data
    } catch (error) {
        console.log(error)
    }
}
module.exports = { main, create_form };