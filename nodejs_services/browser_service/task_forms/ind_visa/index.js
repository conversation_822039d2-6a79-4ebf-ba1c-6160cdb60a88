const fs = require('fs');
const axios = require('axios');
const moment = require('moment');
const puppeteer = require('puppeteer');
const { PuppeteerScreenRecorder } = require('puppeteer-screen-recorder');
const archiver = require('archiver');
const { AIRPORT, AIRPORT_CODES } = require('./data_mapping')
const convert_airport = (airport) => AIRPORT[airport] ? AIRPORT[airport] : airport
const FormData = require('form-data');
const PhoneNumber = require('libphonenumber-js');
const { get_order_detail, create_zip, s3_upload_buff, get_order_task_pod_data } = require('../../shared/helpers');
const { get_image_captcha_text } = require('../../shared/captcha');

let DEBUG = false
const RECODER_NAME = `Recorder_${moment().unix()}.mp4`
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));


const ind_visa_form = async (order, task, data) => {
    const browser = await puppeteer.launch({ devtools: true, headless: DEBUG ? false : "new", args: ['--no-sandbox', '--disable-setuid-sandbox'] });
    const page = await browser.newPage();
    const recorder = new PuppeteerScreenRecorder(page);
    await recorder.start(RECODER_NAME);

    !DEBUG && await page.setViewport({ width: 1000, height: 1500 });
    page.setDefaultTimeout(30000);

    let index = 1;
    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })

    let email = process.env.ad_env === 'prod' ? '<EMAIL>' : '<EMAIL>'

    try {
        VERIFY_CAPTCHA: for (let c = 0; c < 5; c++) {
            await page.goto('https://indianvisaonline.gov.in/evisa/tvoa.html');
            await sleep(5000)
            await page.evaluate(() => {
                const buttons = Array.from(document.querySelectorAll('button'));
                for (const button of buttons) {
                    if (button.innerText.includes('CLOSE')) {
                        button.click()
                    }
                }
            });

            await sleep(5000)
            await page.waitForSelector('[title="e-Visa Application"]')
            await page.click('[title="e-Visa Application"]')

            // Page 1
            await page.waitForSelector('#nationality_id')
            await page.waitForSelector('#jouryney_id')
            await page.waitForSelector('[value="Continue"]')
            await page.evaluate(() => {
                window.alert = () => { };
                $.fn.dialog = () => { };
            });

            await page.select('#nationality_id', data.passport_core_info_nationality);
            await sleep(2000)


            // 1 ORDINARY PASSPORT                          
            // 2 OFFICIAL PASSPORT
            await page.select('#ppt_type_id', '1');

            await page.select('#missioncode_id', AIRPORT_CODES[data.travel_enter_flight_enter_airport] ?? 'I022');
            await page.type('#dob_id', moment(data.passport_core_info_date_of_birth).format('DD/MM/YYYY'))
            await page.keyboard.press('Enter');

            await page.type('#email_id', email)
            await page.type('#email_re_id', email)
            await page.keyboard.press('Enter');

            // TODO: Fill captcha
            await page.click('#read_instructions_check')
            await sleep(2000)

            if (order.service.attributes.purpose === 'business') {
                await page.waitForSelector('[name="evisa_service"]');
                await page.click('[name="evisa_service"][value="1"]');
                await sleep(2000)
                await page.waitForSelector('[name="evisa_purpose_1"][value="27"]')
                await page.click('[name="evisa_purpose_1"][value="27"]')
            } else {
                if (order.service.attributes.validity === '1M') {
                    await page.waitForSelector('[name="evisa_service"]');
                    await page.click('[name="evisa_service"][value="31"]');
                    await sleep(2000)
                    await page.waitForSelector('[name="evisa_purpose_31"]')
                    await page.click('[name="evisa_purpose_31"]')
                }

                if (order.service.attributes.validity === '1y') {
                    await page.waitForSelector('[name="evisa_service"]');
                    await page.click('[name="evisa_service"][value="3"]');
                    await sleep(2000)
                    await page.waitForSelector('[name="evisa_purpose_3"][value="21"]')
                    await page.click('[name="evisa_purpose_3"][value="21"]')
                }

                if (order.service.attributes.validity === '5y') {
                    await page.waitForSelector('[name="evisa_service"]');
                    await page.click('[name="evisa_service"][value="32"]');
                    await sleep(2000)
                    await page.waitForSelector('[name="evisa_purpose_32"]')
                    await page.click('[name="evisa_purpose_32"]')
                }
            }


            await page.waitForSelector('#jouryney_id')
            await page.evaluate((val) => {
                $('#jouryney_id').val(val).trigger('change');
            }, moment(data.service_core_info_entry_date).format('DD/MM/YYYY'));

            // await page.keyboard.press('Enter');

            const cookies = await page.cookies();
            const cookie = cookies.map(ck => `${ck.name}=${ck.value}`).join("; ")

            var captcha = ""
            C: for (let i = 0; i < 10; i++) {
                captcha = await solve_captcha(cookie)
                console.log(captcha)
                if (captcha.length === 6 && /^[a-z0-9]+$/.test(captcha)) {
                    break C;
                }
            }
            await page.type('#captcha', captcha)

            await page.click('[value="Continue"]')
            await sleep(2000)
            await page.evaluate(() => {
                const buttons = Array.from(document.querySelectorAll('button'));
                for (const button of buttons) {
                    if (button.innerText.includes('Ok')) {
                        button.click()
                    }
                }
            });
            await page.waitForNetworkIdle()
            try {
                await page.waitForSelector('#surname')
                break VERIFY_CAPTCHA;
            } catch (e) {
                console.log(e.message)
            }
        }

        await page.type('#surname', data.passport_core_info_surname)
        await page.type('#givenName', data.passport_core_info_given_name)
        await page.type('#gender', data.passport_core_info_gender)
        await page.type('#birth_place', data.passport_additional_info_city_of_birth ?? data.passport_core_info_city_of_birth)// TODO:
        await page.select('#country_birth', data.passport_core_info_country_of_birth)// TODO:
        await page.type('#nic_number', 'NA')
        await page.select('#religion', 'BUDDHISM')
        await page.type('#identity_marks', 'NA')

        await page.select('#education ', {
            high_school: 'GRADUATE',
            college: 'GRADUATE',
            graduate: 'GRADUATE',
            postgraduate: 'GRADUATE',
        }[data.personal_core_info_education] || 'GRADUATE')

        await page.select('#nationality_by ', {
            nationality_by_birth: 'BY BIRTH',
            nationality_by_naturalization: 'NATURALIZATION',
        }[data.passport_additional_info_nationality_by_birth_or_naturalization] ?? 'BY BIRTH')

        // Only select previous nationality if the field is visible
        const prevNationalityVisible = await page.$eval('#prev_nationality', el => el.offsetParent !== null);
        if (prevNationalityVisible && data.passport_previous_passport_nationality) {
            await page.select('#prev_nationality ', data.passport_previous_passport_nationality)
        }

        await page.click('#refer_flag1')

        await page.type('#passport_no', data.passport_core_info_passport_number)
        await page.type('#passport_issue_place', data.passport_core_info_nationality)
        await page.type('#passport_issue_date', moment(data.passport_core_info_issue_date ?? '2020-01-01T00:00:00Z').format('DD/MM/YYYY'))// TODO:
        await page.type('#passport_expiry_date', moment(data.passport_core_info_expiration_date).format('DD/MM/YYYY'))
        await page.keyboard.press('Enter');

        await page.click('#other_ppt_2') // Any other valid Passport/Identity Certificate(IC) held

        await page.click('#continue')

        await page.waitForSelector('#pres_add1')
        await page.waitForSelector('#sameAddress_id')
        await page.evaluate(() => {
            window.confirm = () => { };
        });

        await page.type('#pres_add1', data.personal_home_address_street_number_name)
        await page.type('#pres_add2', data.personal_home_address_city)
        await page.select('#pres_country', data.personal_home_address_country)
        await page.type('#pres_add3', data.personal_home_address_state)
        await page.type('#pincode', data.personal_home_address_zip_code)
        await page.type('#pres_phone', data.personal_core_info_phone?.phone?.replace('+', ''))
        await page.type('#mobile', data.personal_core_info_phone?.phone?.replace('+', ''))
        await page.click('#sameAddress_id')

        await page.type('#fthrname', data.family_father_given_name + ' ' + data.family_father_surname)
        await page.select('#father_nationality', data.family_father_nationality)
        // await page.select('#father_previous_nationality', '')
        await page.type('#father_place_of_birth', data.family_father_city_of_birth)
        await page.select('#father_country_of_birth', data.family_father_country_of_birth)
        data.family_father_former_nationality && await page.select('#father_previous_nationality', data.family_father_former_nationality)

        await page.type('#mother_name', data.family_mother_given_name + ' ' + data.family_mother_surname)
        await page.select('#mother_nationality', data.family_mother_nationality)
        // await page.select('#mother_previous_nationality', '')
        await page.type('#mother_place_of_birth', data.family_mother_city_of_birth)
        await page.select('#mother_country_of_birth', data.family_mother_country_of_birth)
        data.family_mother_former_nationality && await page.select('#mother_previous_nationality', data.family_mother_former_nationality)
        await page.select('#marital_status', {
            "married": "0",
            "single": "1",
            "divorced": "2",
        }[data.personal_core_info_marital_status])
        if (data.personal_core_info_marital_status === 'married') {
            await page.waitForSelector('#spouse_name')
            await page.type('#spouse_name', data.family_spouse_given_name + ' ' + data.family_spouse_surname)
            await page.select('#spouse_nationality', data.family_spouse_nationality)
            await page.type('#spouse_place_of_birth', data.family_spouse_city_of_birth)
            await page.select('#spouse_country_of_birth', data.family_spouse_country_of_birth)
        }

        await page.waitForSelector('#grandparent_flag2')
        await page.click('#grandparent_flag2')


        if (data.personal_occupation_occupation === 'retired') {
            await page.select('#occupation', 'RETIRED')
            await sleep(2000)
            await page.type('#empname', 'NA')
            await page.type('#empaddress', 'NA')
        }

        if (data.personal_occupation_occupation === 'student') {
            await page.select('#occupation', 'STUDENT')
            await sleep(2000)
            await page.select('#occ_flag', 'F')
            await page.type('#empname', data.employee_company_info_company_school_name)
            await page.type('#empaddress', `${data.employee_company_info_street_number_name} ${data.employee_company_info_city} ${data.employee_company_info_state} ${data.employee_company_info_zip_code}`)
        }
        if (data.personal_occupation_occupation === 'company_employee') {
            await page.select('#occupation', 'ENGINEER')
            await sleep(2000)
            // await page.waitForSelector('#occupationOther')
            // await page.type('#occupationOther', data.employee_company_info_job_title?.toUpperCase())
            await page.type('#empname', data.employee_company_info_company_school_name)
            await page.type('#empaddress', `${data.employee_company_info_street_number_name} ${data.employee_company_info_city} ${data.employee_company_info_state} ${data.employee_company_info_zip_code}`)
        }
        if (data.personal_occupation_occupation === 'business_person') {
            await page.select('#occupation', 'BUSINESS PERSON')
            await sleep(2000)
            await page.type('#empname', data.employee_company_info_company_school_name)
            await page.type('#empaddress', `${data.employee_company_info_street_number_name} ${data.employee_company_info_city} ${data.employee_company_info_state} ${data.employee_company_info_zip_code}`)
        }
        await sleep(2000)
        await page.click('#continue')

        // Details of Visa Sought
        await page.waitForSelector('#placesToBeVisited1_id')
        await page.type('#placesToBeVisited1_id', data.travel_core_info_locations_plan_to_visit?.toUpperCase())
        await page.select('#exitpoint', convert_airport(data.travel_exit_flight_exit_airport))

        await sleep(2000)
        await page.waitForSelector('#haveYouBookedRoomInHotel_no_id')
        await page.click('#haveYouBookedRoomInHotel_no_id')

        const titles = await page.$$eval('.col-11', els => els.map(el => el.textContent))
        // Details of the Applicants Company
        if (await page.$('#visa_serreq_id_20')) {
            if (data.employee_company_info_company_school_name) {
                await page.type('#visa_serreq_id_20', data.employee_company_info_company_school_name).catch(e => console.log(e.message))
            }
            if (data.employee_company_info_street_number_name && data.personal_core_info_phone) {
                await page.type('#visa_serreq_id_26', data.employee_company_info_street_number_name + ', ' + PhoneNumber(data.personal_core_info_phone?.phone).nationalNumber)
            }
            await page.type('#visa_serreq_id_27', data.employee_company_info_website ?? 'https://ariadirect.com')

            data.employee_company_reference_in_destination_company_name && await page.type('#visa_serreq_id_80', data.employee_company_reference_in_destination_company_name)
            if (data.employee_company_reference_in_destination_company_name && data.employee_company_reference_in_destination_street_number_name) {
                await page.type('#visa_serreq_id_81', data.employee_company_reference_in_destination_street_number_name.substring(0, 30) + ', ' + PhoneNumber(data.employee_company_reference_in_destination_phone?.phone).nationalNumber.substring(0, 30))
            }
            await page.type('#visa_serreq_id_29', data.employee_company_reference_in_destination_website ?? 'https://ariadirect.com')
        }
        if (data.additional_question_ind_ind_other_7) {
            await page.click('#old_visa_flag1')
            await page.type('#prv_visit_add1', data.additional_question_ind_ind_other_8)
            await page.type('#visited_city', data.additional_question_ind_ind_other_9)
            await page.type('#old_visa_no', data.additional_question_previous_visa_visa_number)
            await page.type('#old_visa_type_id', data.additional_question_previous_visa_visa_purpose === 'business' ? 'BUSINESS VISA' : 'TOURIST VISA') // 1: Business, 3: Tourist
            await page.type('#oldvisaissueplace', data.additional_question_previous_visa_issue_place)
            await page.type('#oldvisaissuedate', moment(data.additional_question_previous_visa_issue_date).format('DD/MM/YYYY'))
        }

        await page.click('#old_visa_flag2')
        await page.click('#refuse_flag2')
        await page.click('#saarc_flag2')


        await page.type('#nameofsponsor_ind', data.personal_individual_reference_in_destination_given_name + ' ' + data.personal_individual_reference_in_destination_surname)
        await page.type('#add1ofsponsor_ind', data.personal_individual_reference_in_destination_street_number_name)
        await page.select('#stateofsponsor_ind', data.personal_individual_reference_in_destination_state.toUpperCase())

        await sleep(2000)

        const districts = await page.$$eval('#districtofsponsor_ind', options =>
            options.map(option => option.value)
        );
        const district = districts.find(d => d === data.personal_individual_reference_in_destination_city.toUpperCase())

        await page.$eval('#districtofsponsor_ind', (input, value) => { input.value = value }, district ?? 'NOT KNOWN')


        await page.type('#phoneofsponsor_ind', PhoneNumber(data.personal_individual_reference_in_destination_phone?.phone).nationalNumber)
        await page.type('#nameofsponsor_msn', data.personal_individual_reference_in_residence_given_name + ' ' + data.personal_individual_reference_in_residence_surname)
        await page.type('#add1ofsponsor_msn', data.personal_individual_reference_in_residence_street_number_name + ' ' + data.personal_individual_reference_in_residence_city)
        await page.type('#phoneofsponsor_msn', PhoneNumber(data.personal_individual_reference_in_residence_phone?.phone).nationalNumber)

        await page.click('#haveYouBookedRoomInHotel_no_id')


        await page.waitForSelector('#continue')
        await page.click('#continue')

        await sleep(5000)
        await page.waitForSelector('#question_no_1')
        await page.click('#question_no_1')
        await page.click('#question_no_2')
        await page.click('#question_no_3')
        await page.click('#question_no_4')
        await page.click('#question_no_5')
        await page.click('#question_no_6')
        await page.click('#verifyQuestions')

        await page.click('#continue')
        await page.waitForSelector('.red_heading.text_bold')
        const document_id = await page.$eval('.red_heading.text_bold', el => el.textContent)
        await recorder.stop();
        await browser.close()
        return {
            document_id,
            email,
            dob: moment(data.passport_core_info_date_of_birth).format('DD/MM/YYYY'),
            created_at: moment().format('DD/MM/YYYY'),
            tracking_url: 'https://indianvisaonline.gov.in/evisa/PaymentCheck',
        }
        // NATURALIZATION
        // education
        // return resp.data
    } catch (error) {
        console.log(error)
        console.log(error.message)
        await recorder.stop();
        await browser.close()
        return null
    } finally {
    }

}


const solve_captcha = async (cookie) => {
    const resp = await axios({
        method: 'get',
        url: 'https://indianvisaonline.gov.in/evisa/captcha',
        headers: {
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Cookie': cookie
        },
        responseType: "arraybuffer"
    })

    var data = new FormData();
    data.append('file', resp.data);

    const captcha = await get_image_captcha_text(resp.data)
    return captcha
}


const create_form = async (order, task, pod_data) => {
    let form_callback = await ind_visa_form(order, task, pod_data)
    let MAX_RETRY = 3;
    if (!form_callback?.document_id && MAX_RETRY > 0) {
        form_callback = await ind_visa_form(order, task, pod_data)
        MAX_RETRY--;
    }
    const result = {
        success: form_callback?.document_id ? true : false,
        form_file: "",
        form_callback: form_callback ?? {},
    }

    const buckets = JSON.parse(process.env.ad_s3)

    await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${RECODER_NAME}`, fs.readFileSync(RECODER_NAME))

    fs.rmSync('images', { recursive: true })
    fs.rmSync(RECODER_NAME, { recursive: true })

    result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${RECODER_NAME}`
    console.log("STEPS:", result.form_callback.steps)
    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form(order, task, pod_data)
        return data
    } catch (error) {
        console.log(error)
    }
}

module.exports = { main, create_form }