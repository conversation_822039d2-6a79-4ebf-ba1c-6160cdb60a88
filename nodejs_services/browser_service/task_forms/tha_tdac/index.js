const fs = require('fs');
const moment = require('moment');
const { PuppeteerScreenRecorder } = require('puppeteer-screen-recorder');
const { connect } = require('../../shared/browser/index.js');
const { s3_upload_buff, get_order_task_pod_data, sleep, get_country_name } = require('../../shared/helpers');
const data_mapping = require('../../shared/data_mapping')
const PhoneNumber = require('libphonenumber-js');
const RECODER_NAME = `Recorder_${moment().unix()}.mp4`
async function waitForTurnstileValue(page) {
    await page.waitForFunction(() => {
        const element = document.querySelector('[name="cf-turnstile-response"]');
        return element && element.value !== '';
    });
    const token = await page.$eval('[name="cf-turnstile-response"]', el => el.value);
    return token;
}
const visa_form = async (order, task, data) => {
    const { page, browser } = await connect({
        args: ["--start-maximized"],
        turnstile: true,
        headless: false,
        // disableXvfb: true,
        // ignoreAllFlags:true,
        customConfig: {},
        connectOption: {
            defaultViewport: null
        },
        plugins: []
    });
    const recorder = new PuppeteerScreenRecorder(page);
    await recorder.start(RECODER_NAME);

    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })


    try {
        await page.goto('https://tdac.immigration.go.th/arrival-card/#/home');
        try {
            const turnstileToken = await waitForTurnstileValue(page);
            console.log('Turnstile Token:', turnstileToken);
        } catch (e) {
            console.log("No Cloudflare or can't bypass. Please Debug")
        }

        await page.locator('button ::-p-text(Arrival Card)').click()
        await sleep(5000)
        await page.type('#mat-input-0', data.passport_core_info_surname, { delay: 100 })
        await page.type('#mat-input-1', data.passport_core_info_given_name, { delay: 100 })
        await page.type('input[formcontrolname="passportNo"]', data.passport_core_info_passport_number, { delay: 100 })
        await page.type('#mat-input-25', data.passport_core_info_nationality == "USA" ? "American" : get_country_name(data.passport_core_info_nationality), { delay: 100 })
        await page.locator(`span ::-p-text(${data.passport_core_info_nationality} :)`).click()
        await page.type('#mat-input-18', moment(data.passport_core_info_date_of_birth).format('YYYY'), { delay: 100 })
        await page.type('#mat-input-19', moment(data.passport_core_info_date_of_birth).format('MM'), { delay: 100 })
        await page.type('#mat-input-20', moment(data.passport_core_info_date_of_birth).format('DD'), { delay: 100 })
        await page.type('input[formcontrolname="occupation"]', data_mapping.occupation[data.personal_occupation_occupation], { delay: 100 })
        await page.click('#mat-radio-0-input', {
            "F": "IyUWdXH1pyZPwyTUw0srtA==",
            "M": "n/ue8HbzI2kQuokfmNXf0Q==",
        }[data.passport_core_info_gender])


        await page.type('#mat-input-26', get_country_name(data.personal_home_address_country), { delay: 100 })
        await page.locator(`span ::-p-text(${data.personal_home_address_country} :)`).click()

        await page.type('#mat-input-27', data_mapping.us_state[data.personal_home_address_state], { delay: 100 })
        await page.locator(`span ::-p-text(${data_mapping.us_state[data.personal_home_address_state].toUpperCase()})`).click()

        await page.locator('#mat-input-6').fill(PhoneNumber(data.personal_core_info_phone.phone).countryCallingCode, { delay: 100 })
        await page.type('#mat-input-7', PhoneNumber(data.personal_core_info_phone.phone).nationalNumber, { delay: 100 })
        await page.click(`button ::-p-text(Continue)`)
        await sleep(2000)

        await page.type('#mat-input-8', moment(data.service_core_info_entry_date).format('YYYYMMDD'), { delay: 100 })
        await page.type('#mat-input-28', data.travel_core_info_country_you_boarded, { delay: 100 })
        await page.locator(`span ::-p-text(${data.travel_core_info_country_you_boarded})`).click()

        await page.click('#mat-select-value-1')
        await page.click(`span ::-p-text(${{
            "tourist": "HOLIDAY",
            "business": "MEETING",
        }[order.service.attributes.purpose]})`)
        await page.click('#mat-radio-3-input')
        await sleep(2000)
        await page.click('#mat-select-value-2')
        await page.locator(`span ::-p-text(COMMERCIAL FLIGHT)`).click()
        await page.type('#mat-input-11', data.travel_enter_flight_enter_flight)
        //Hotel Info
        await page.click('#mat-select-value-4')
        await page.locator(`span ::-p-text(HOTEL)`).click()

        await page.type('#mat-input-29', "BANGKOK", { delay: 100 })
        await page.locator(`span ::-p-text(BANGKOK)`).click()

        await page.type('#mat-input-30', "PATHUM WAN", { delay: 100 })
        await page.locator(`span ::-p-text(PATHUM WAN)`).click()

        await page.type('#mat-input-31', "LUMPHINI", { delay: 100 })
        await page.locator(`span ::-p-text(LUMPHINI)`).click()

        await page.type('#mat-input-17', "973 Phloen Chit Rd", { delay: 100 })
        await sleep(2000)
        await page.click(`::-p-xpath(//*[@id="cdk-stepper-0-content-1"]/mat-card/div[3]/button[2]/span[2])`)
        await sleep(2000)

        await page.click(`button ::-p-text(Preview)`)
        await sleep(5000)
        await page.locator('#mat-input-32').fill(data.personal_core_info_email_address)
        await page.locator(`#mat-mdc-checkbox-13-input`).click()
        await page.locator(`button ::-p-text(Agree)`).click()
        await sleep(2000)
        await page.locator(`button ::-p-text(Submit)`).click()
        await sleep(2000)
        await page.locator(`button ::-p-text(Confirm)`).click()
        await recorder.stop();
        await browser.close()
        return {}
    } catch (error) {
        console.log(error)
        console.log(error.message)
        await recorder.stop();
        await browser.close()
        return null
    }

}

const MAX_RETRY = 1;
const create_form = async (order, task, pod_data) => {
    const result = {
        success: false,
        form_callback: {}
    }
    let form = null
    for (let i = 0; i < MAX_RETRY; i++) {
        form = await visa_form(order, task, pod_data)
        if (form != null) {
            result.success = true
            break
        }
    }


    const buckets = JSON.parse(process.env.ad_s3)


    await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${RECODER_NAME}`, fs.readFileSync(RECODER_NAME))

    fs.rmSync('images', { recursive: true })
    fs.rmSync(RECODER_NAME, { recursive: true })

    result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${RECODER_NAME}`
    console.log("STEPS:", result.form_callback.steps)
    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form(order, task, pod_data)
        console.log(data)
        return data
    } catch (error) {
        console.log(error)
    }
}
module.exports = { main, create_form }