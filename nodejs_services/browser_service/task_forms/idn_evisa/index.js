const fs = require('fs');
const moment = require('moment');
const { connect } = require('../../shared/browser/index.js');
const AWS = require('aws-sdk');
const { exec } = require('child_process');
const PhoneNumber = require('libphonenumber-js');
const { PuppeteerScreenRecorder } = require('puppeteer-screen-recorder');
const { solveReCaptcha, fetch_email, get_country_name, s3_upload_buff, get_order_task_pod_data, s3_url_to_bucket_key } = require('../../shared/helpers');
const { HOTEL } = require('./data_mapping')
const BTAPI = require('../../shared/bt_api');

const RECODER_NAME = `Recorder_${moment().unix()}.mp4`
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));
exec('ssh -i jump.pem -CND 8007 ec2-user@54.202.35.245', (error, stdout, stderr) => {
    if (error) {
        console.error(`exec error: ${error}`);
        return;
    }
    console.log(`stdout: ${stdout}`);
    console.error(`stderr: ${stderr}`);
});
async function get_image_file(url, name) {
    try {
        const { bucket, key } = s3_url_to_bucket_key(url)
        const s3 = new AWS.S3();
        const params = {
            Bucket: bucket,
            Key: key
        };
        const response = await s3.getObject(params).promise();
        fs.writeFileSync(name, response.Body);
        console.log(`File ${name} downloaded successfully.`);
    } catch (error) {
        console.error('Error downloading file:', error);
    }
}
async function selectField(page, idSelector, value) {
    await page.waitForSelector(`#${idSelector}`)
    // Step 1: Open the dropdown
    await page.click(`#select2-${idSelector}-container`, { delay: 50 });
    //Step 2 : Type
    await page.type('.select2-search__field', value, { delay: 50 });
    //Press Enter
    await page.keyboard.press('Enter');
    sleep(1000)
}
async function inputDate(page, selector, value) {
    await page.evaluate((selector) => {
        const input = document.querySelector(selector);
        input.readOnly = false;
    }, selector);
    await page.type(selector, value, { delay: 50 })
}
const idn_evisa = async (data, order, task) => {
    const { page, browser } = await connect({
        args: ["--start-maximized", '--proxy-server=socks5://localhost:8007'],
        turnstile: true,
        headless: true,
        customConfig: {},
        connectOption: {
            defaultViewport: null
        },
        plugins: []
    });
    const recorder = new PuppeteerScreenRecorder(page);
    await recorder.start(RECODER_NAME);
    page.setDefaultTimeout(30000);


    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })
    const pwd = "Admin123@AriaDirect"
    try {
        //Get all Image before start

        console.log("Loading...")
        await page.goto('https://evisa.imigrasi.go.id/front/register/wna');
        //================================================Register================================================
        await page.waitForNetworkIdle()
        await selectField(page, 'document_travel_id', "Passport")

        //Passport Photo
        await page.waitForSelector('#attachment-image')
        files = await page.$$('#attachment');
        await get_image_file(data.document_copy_of_passport_copy_of_passport_main_page, "images/passport.jpg")
        await files[0].uploadFile("images/passport.jpg")
        await page.waitForNetworkIdle()
        await sleep(10000)
        //User Photo
        files = await page.$$('#picture');

        await get_image_file(data.photo_passport_photo_copy_of_photo, "images/photo.jpg")
        await files[0].uploadFile("images/photo.jpg")
        await page.waitForNetworkIdle()
        await sleep(10000)
        //User Info
        await page.type('#full_name', data.passport_core_info_given_name + " " + data.passport_core_info_surname, { delay: 50 })
        await page.click(`#gender-${data.passport_core_info_gender}`, { delay: 50 })
        await page.type('#birth_place', get_country_name(data.passport_core_info_country_of_birth), { delay: 50 })
        await inputDate(page, '#birthday', moment(data.passport_core_info_date_of_birth).format('DD/MM/YYYY'))
        phoneNumber = PhoneNumber(data.personal_core_info_phone.phone)
        await page.type('#mobile_phone', phoneNumber.nationalNumber, { delay: 50 })
        await selectField(page, 'phone_code', `+${phoneNumber.countryCallingCode}`)
        await page.type('#mother', data.family_mother_given_name + " " + data.family_mother_surname, { delay: 50 })

        //Document Info
        await page.type('#number', data.passport_core_info_passport_number, { delay: 100 })
        await selectField(page, 'country_id', get_country_name(data.passport_core_info_nationality))
        await inputDate(page, '#release_date', moment(data.passport_core_info_issuing_date).format('DD/MM/YYYY'))
        await inputDate(page, '#expired_date', moment(data.passport_core_info_expiration_date).format('DD/MM/YYYY'))
        await page.type('#release_place', get_country_name(data.passport_core_info_issuing_authority), { delay: 50 })

        //Email & Password
        const btAPI = new BTAPI();
        let ad_email = await btAPI.randomEmail(data.passport_core_info_surname)

        console.log("Email: ", ad_email.toLowerCase())
        await page.type('#username', ad_email.toLowerCase(), { delay: 50 })
        await page.type('#confirm_email', ad_email.toLowerCase(), { delay: 50 })
        await page.type('#password', pwd, { delay: 50 })
        await page.type('#confirm_password', pwd, { delay: 50 })
        let start_time = moment().unix()
        await page.click('[name="send"]', { delay: 50 })
        await sleep(2000)
        await page.waitForSelector('#username').catch(() => null)
        await sleep(2000)
        console.log("Activation...")
        let selected_email = null
        while (moment().unix() > start_time && moment().unix() - start_time < 500) {
            const emails = await btAPI.getEmails(ad_email)
            if (emails?.length > 0 && emails[0].time > start_time) {
                selected_email = emails[0]
                break
            }
            await new Promise(r => setTimeout(r, 10000))
        }
        console.log("wait time : ", moment().unix() - start_time)
        console.log(selected_email)
        //Activation
        const regex = /http:\/\/evisa\.imigrasi\.go\.id\/front\/login\?token=[A-Za-z0-9._-]+/g;
        const urls = selected_email.html.match(regex);
        await page.goto(urls[1])
        await new Promise(r => setTimeout(r, 3000))
        await page.click('.swal2-confirm.swal2-styled.swal2-default-outline');
        //===================================================Login===================================================
        console.log("Login...")
        await page.goto('https://evisa.imigrasi.go.id/front/login')
        await sleep(5000)
        await solveReCaptcha(page)
        await sleep(5000)
        await page.type('#username', ad_email.toLowerCase(), { delay: 50 })
        await page.type('#password', pwd, { delay: 50 })
        await page.click('#flexCheckDefault')
        await sleep(1000)
        await page.click('#btn-submit')
        await sleep(5000)
        //===================================================Apply Visa================================================
        console.log("Login Successed - Filling Form...")
        await page.goto('https://evisa.imigrasi.go.id/front/visa-selection')
        await sleep(5000)
        //Purpose
        await selectField(page, 'selectParentActivity', 'General')
        await sleep(5000)
        await page.waitForSelector('#selectActivity')
        await selectField(page, 'selectActivity', 'Tourism')
        await sleep(5000)
        /**
         * List Visa Category:
         * B1 - Tourist (Visa On Arrival)
         * C1 - Tourist Single Entry Visitor Visa -  60 Days
         * D1 - Tourist Multiple Entry Visa (1 or 2 or 5 Years)
         */
        await page.waitForSelector('#selectVisaCategory')
        await selectField(page, 'selectVisaCategory', 'C1')
        await sleep(5000)
        await page.click('#buttonModalDetail')
        await page.waitForSelector('#buttonApply')
        await sleep(5000)
        await page.click('#buttonApply')
        await sleep(5000)

        //Passport Photo
        await page.waitForSelector('#passport-attachment')
        files = await page.$$('#passport-attachment');
        await files[0].uploadFile("images/passport.jpg")
        await sleep(10000)
        //User Photo
        files = await page.$$('#picture');
        await files[0].uploadFile("images/photo.jpg")
        await sleep(10000)
        await page.click('#btn-submit')
        await sleep(5000)
        //update ZipCode First
        let hotel = getRandomHotel()
        let maxRetries = 3
        let retryCount = 0
        let updateZipCode = false
        while (!updateZipCode && retryCount < maxRetries) {
            if (retryCount > 0) {
                await page.reload({ waitUntil: 'networkidle0' })
                await sleep(500)
            }

            await page.click('#postal_code')
            await page.type('#postal_code', hotel.Zipcode, { delay: 100 })
            await sleep(1000)

            const errorElement = await page.$('#swal2-title')
            if (!errorElement) {
                updateZipCode = true
            } else {
                await page.click('.swal2-confirm.swal2-styled');
                await sleep(2000)
                console.log(`Attempt ${retryCount + 1} failed. Retrying...`)
                retryCount++
            }
        }
        if (!updateZipCode) {
            console.log(`Failed to enter postal code after ${maxRetries} attempts`)
        }
        await page.waitForSelector('#paymentMethod-ONLINE')
        await page.click('#paymentMethod-ONLINE')
        await page.type('#full_name', data.passport_core_info_given_name + " " + data.passport_core_info_surname, { delay: 50 })
        await page.click(`#gender-${data.passport_core_info_gender}`, { delay: 50 })
        await page.type('#birth_place', get_country_name(data.passport_core_info_country_of_birth), { delay: 50 })
        await inputDate(page, '#birthday', moment(data.passport_core_info_date_of_birth).format('DD/MM/YYYY'))
        await page.type('#mobile_phone', data.personal_core_info_phone.phone)
        await page.type('#number', data.passport_core_info_passport_number, { delay: 50 })
        await inputDate(page, '#expired_date', moment(data.passport_core_info_expiration_date).format('DD/MM/YYYY'))
        await page.type('#release_place', get_country_name(data.passport_core_info_issuing_authority), { delay: 50 })
        //HOTEL
        await selectField(page, 'residence_type_id', 'HOTEL')
        await page.type('#address', hotel.Address, { delay: 100 })
        await sleep(1000)
        //Bank Statement
        files = await page.$$('#attachment-C1-1');
        await get_image_file(data.document_copy_of_document_copy_of_bank_statement, "images/document.pdf")
        await files[0].uploadFile("images/document.pdf")
        await sleep(5000)
        await page.click('#btn-submit')
        await sleep(10000)
        //Check all 
        const checkboxes = await page.$$('input[type="checkbox"]');
        // Iterate over each checkbox and check it
        for (const checkbox of checkboxes) {
            await checkbox.evaluate(node => node.checked = true);
        }
        await sleep(5000)
        await page.click('#btn-submit')
        await sleep(5000)
        await page.waitForSelector('.swal2-confirm.swal2-styled')
        await page.click('.swal2-confirm.swal2-styled');
        await sleep(5000)
        console.log("Finish Filling Form...")
        await recorder.stop();
        await browser.close()

        return {
            email: ad_email,
            created_at: moment().format('DD/MM/YYYY'),
            note: [
                `Email: ${ad_email}`,
                `Password:${pwd}`,
                `Tracking Url: https://evisa.imigrasi.go.id/front/login`,
            ].join('\n')
        }
    } catch (error) {
        console.log(error)
        console.log(error.message)
        await recorder.stop();
        await browser.close()
        return null
    }

}
function getRandomHotel() {
    const randomIndex = Math.floor(Math.random() * HOTEL.length);
    return HOTEL[randomIndex];
}

const get_pod_values = (input_pods) => {
    let results = {};

    for (const key in input_pods) {
        const { category, sub_category, name, value, option_choice } = input_pods[key];

        if (value?.fe != null) {
            results[`${category}_${sub_category}_${name}`] = value.fe;
            if (option_choice && Object.keys(option_choice)?.length > 0)
                results = { ...results, ...get_pod_values(option_choice[value?.fe]) };
        }
    }

    return results;
}

const MAX_RETRY = 3;
const create_form = async (pod_data, order, task) => {
    console.log(JSON.stringify(pod_data, null, 2))
    let form = null
    for (let i = 0; i < MAX_RETRY; i++) {
        form = await idn_evisa(pod_data, order, task)
        if (form != null) {
            break
        }
    }

    const result = {
        success: false,
        form_callback: {}
    }
    const buckets = JSON.parse(process.env.ad_s3)
    if (form != null) {
        result.form_callback = form
        result.success = true
    }
    await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${RECODER_NAME}`, fs.readFileSync(RECODER_NAME))

    fs.rmSync('images', { recursive: true })
    fs.rmSync(RECODER_NAME, { recursive: true })

    result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${RECODER_NAME}`
    console.log("STEPS:", result.form_callback.steps)
    return result
}

const main = async (order_id, task_id) => {
    try {
        const { pod_data, order, task } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form(pod_data, order, task)
        return data
    } catch (error) {
        console.log(error)
    }
}

// const MOCK = {
//     order_id: 3875,
//     task_id: 4508
// }
// main(5585, 6572)
module.exports = { main, create_form };