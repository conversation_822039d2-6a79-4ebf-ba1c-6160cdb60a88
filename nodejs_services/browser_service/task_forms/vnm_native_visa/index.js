const fs = require('fs');
const _ = require('lodash');
const jsdom = require("jsdom");
const { JSDOM } = jsdom;
const bluebird = require('bluebird');
var FormData = require('form-data');
const moment = require('moment')
const axios = require('axios')
const iso = require('iso-3166-1');
const pdfUtil = require('pdf-to-text');
const cheerio = require('cheerio');
const tmp = require('tmp');
const { faker } = require('@faker-js/faker');
const fakeAddress = require("fake-address-generator");

const { capitalize, import_photo_to_application_form, s3_upload_buff, get_order_task_pod_data, retry_operation } = require('../../shared/helpers');

const data_mapping = require('./data_mapping')

const FRAME = { "x": 35, "y": 725, "width": 89, "height": 105 }

fetch.Promise = bluebird;

const create_form = async (order, task, pod_data) => {
    if (pod_data.document_copy_of_document_were_you_born_vn || pod_data.document_copy_of_document_were_you_born_vn) {
        pod_data.passport_core_info_country_of_birth = pod_data.passport_core_info_country_of_birth || "VNM"
        pod_data.passport_core_info_birth_nationality = pod_data.passport_core_info_birth_nationality || "VNM"
        pod_data.passport_additional_info_birth_nationality = pod_data.passport_additional_info_birth_nationality || "VNM"
    }

    var { country: country_of_birth } = iso.whereAlpha3(pod_data.passport_core_info_country_of_birth || "VNM")
    var { country: issuing_authority } = iso.whereAlpha3(pod_data.passport_core_info_issuing_authority || "VNM")
    // var { country: home_address_country } = iso.whereAlpha3(pod_data.personal_home_address_country)
    const random_hotel = _.sample(data_mapping.HOTELS)

    const home_address = await new Promise(r => fakeAddress.Generate({
        country: "us",
        sex: pod_data.passport_core_info_gender === 'M' ? "male" : "female",
    }, (err, resp) => {
        r(resp);
    }));

    const step1 = await fetch(
        "http://mienthithucvk.mofa.gov.vn/Đăngký/Khaitrựctuyến/tabid/104/VE4NCommand/registration/Default.aspx",
        {
            "credentials": "omit",
            "headers": {
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3",
                "accept-language": "en-US,en;q=0.9",
                "cache-control": "max-age=0",
                "upgrade-insecure-requests": "1",
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.100 Safari/537.36",
            },
            "referrer": "http://mienthithucvk.mofa.gov.vn/Đăngký/Khaitrựctuyến/tabid/104/VE4NCommand/registration/Default.aspx",
            "referrerPolicy": "no-referrer-when-downgrade",
            "body": null,
            "method": "GET",
            "mode": "cors"
        }
    )
    const headers = step1.headers;
    const cookies = headers.get('set-cookie');
    let cookie_items = cookies.split(',')
        .map(cookie => cookie.split(';')[0].trim())
    const sessionId = cookie_items.find(cookie => cookie.startsWith('ASP.NET_SessionId')).split('=')[1]
    const step1_text = await step1.text()
    const $1 = cheerio.load(step1_text)

    let data = new FormData();
    data.append('__EVENTTARGET', '');
    data.append('__EVENTARGUMENT', '');
    data.append('__VSTATE', $1('form input[name="__VSTATE"]').val());
    data.append('dnn$dnnMENU$RadMenu1', '');
    data.append('dnn$ctr408$Desktop$subctr$btAccept', 'Khai trực tuyến');
    data.append('ScrollTop', '102');
    data.append('__dnnVariable', '');
    data.append('__VIEWSTATE', '');

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'http://mienthithucvk.mofa.gov.vn/%C4%90%C4%83ngk%C3%BD/Khaitr%E1%BB%B1ctuy%E1%BA%BFn/tabid/104/VE4NCommand/introduction/Default.aspx',
        headers: {
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64; rv:133.0) Gecko/20100101 Firefox/133.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Origin': 'http://mienthithucvk.mofa.gov.vn',
            'DNT': '1',
            'Sec-GPC': '1',
            'Connection': 'keep-alive',
            'Referer': 'http://mienthithucvk.mofa.gov.vn/%C4%90%C4%83ngk%C3%BD/Khaitr%E1%BB%B1ctuy%E1%BA%BFn/tabid/104/VE4NCommand/introduction/Default.aspx',
            'Cookie': `ASP.NET_SessionId=${sessionId}; language=en-US; language=en-US`,
            'Upgrade-Insecure-Requests': '1',
            'Priority': 'u=0, i',
            ...data.getHeaders()
        },
        data: data
    };
    const step2 = await axios(config)

    const $2 = cheerio.load(step2.data)
    let form = new FormData();
    form.append('__EVENTTARGET', '');
    form.append('__EVENTARGUMENT', '');
    form.append('__VSTATE', $2('form input[name="__VSTATE"]').val());
    form.append('dnn$dnnMENU$RadMenu1', '');
    form.append('ScrollTop', '');
    form.append('__dnnVariable', '');
    form.append('__VIEWSTATE', '');

    const step3_data = {
        txtFirstName: pod_data.passport_core_info_given_name,
        txtMiddleName: '',
        txtLastName: pod_data.passport_core_info_surname,
        txtFirstNameVN: pod_data.passport_core_info_given_name,
        txtMiddleNameVN: '',
        txtLastNameVN: pod_data.passport_core_info_surname,
        txtDayOfBirth: moment(pod_data.passport_core_info_date_of_birth).format('DD'),
        txtMonthOfBirth: moment(pod_data.passport_core_info_date_of_birth).format('MM'),
        txtYearOfBirth: moment(pod_data.passport_core_info_date_of_birth).format('YYYY'),
        txtPlaceOfBirth: country_of_birth || pod_data.passport_core_info_birth_nationality,
        ddlNationalityAtBirth: pod_data.passport_additional_info_birth_nationality || pod_data.passport_core_info_birth_nationality,
        ddlNationalityAtPresent: pod_data.passport_core_info_nationality,
        txtPassportNo: pod_data.passport_core_info_passport_number,
        diPassportExpireDate$txtDateInput: moment(pod_data.passport_core_info_expiration_date).format('DD/MM/YYYY'),
        diPassportExpireDate$MaskedEditExtender1_ClientState: '',
        txtPassportIssuingAuthority: issuing_authority,
        txtPRDNo: '',
        txtPRDIssuingAuthority: '',
        // txtAddressAtPresent: capitalize(`${pod_data.personal_home_address_street_number_name}, ${pod_data.personal_home_address_city}, ${pod_data.personal_home_address_state}${pod_data.personal_home_address_zip_code ? ' ' + pod_data.personal_home_address_zip_code : ''}, ${home_address_country}`),
        txtAddressAtPresent: `${home_address.address.street}, ${home_address.address.city}, ${home_address.address.state} ${home_address.address.zip}, United States`,
        // txtPhoneNumber: typeof pod_data.personal_core_info_phone == 'string' ? pod_data.personal_core_info_phone : pod_data.personal_core_info_phone.phone,
        txtPhoneNumber: faker.phone.number({ style: 'international' }),
        txtAddressBeforeResidingAbroad: '',
        txtContactAddressInVN: random_hotel.Address,
        txtContactPhoneNumber: random_hotel.Phone,
        txtChild1Fullname: '',
        chkSex_Child1_Male: 'on',
        diChild1DateOfBirth$txtDateInput: '',
        diChild1DateOfBirth$MaskedEditExtender1_ClientState: '',
        txtChild2Fullname: '',
        chkSex_Child2_Male: 'on',
        diChild2DateOfBirth$txtDateInput: '',
        diChild2DateOfBirth$MaskedEditExtender1_ClientState: '',
        txtChild3Fullname: '',
        chkSex_Child3_Male: 'on',
        diChild3DateOfBirth$txtDateInput: '',
        diChild3DateOfBirth$MaskedEditExtender1_ClientState: '',
        cblDocuments1$12: 'on',
        txtGiayToKhac1: '',
        cblDocuments2$3: 'on',
        txtGiayToKhac2: '',
        txtOldNumber: '',
        txtOldDate$txtDateInput: '',
        txtOldDate$MaskedEditExtender1_ClientState: '',
        txtGiayToKhac3: '',
        chkPrintType_1: 'on',
        ddlCountry: 'USA',
        cmdFinish: 'Hoàn thành',
    }
    if (pod_data.document_copy_of_passport_document_type === 'us_green_card') {
        step3_data.txtPRDNo = pod_data.passport_core_info_passport_number;
        step3_data["diPRDExpireDate$txtDateInput"] = moment(pod_data.passport_core_info_expiration_date).format('DD/MM/YYYY');
        step3_data.txtPRDIssuingAuthority = issuing_authority;
    } else {
        step3_data.txtPassportNo = pod_data.passport_core_info_passport_number;
        step3_data.diPassportExpireDate$txtDateInput = moment(pod_data.passport_core_info_expiration_date).format('DD/MM/YYYY');
        step3_data.txtPassportIssuingAuthority = issuing_authority;
    }

    if (pod_data.passport_core_info_gender === 'M') {
        step3_data.chkSex_Male = 'on'
    } else {
        step3_data.chkSex_Female = 'on'
    }
    if (!pod_data.personal_occupation_occupation) {
        if (moment(pod_data.passport_core_info_date_of_birth).isBefore(moment().subtract(21, 'years'))) {
            step3_data.txtOccupation = 'Business person'
        } else {
            step3_data.txtOccupation = 'Student'
        }
    }

    for (const k in step3_data) {
        form.append("dnn$ctr408$Desktop$subctr$" + k, step3_data[k])
    }

    const step4 = await axios({
        method: 'post',
        maxBodyLength: Infinity,
        url: 'http://mienthithucvk.mofa.gov.vn/%C4%90%C4%83ngk%C3%BD/Khaitr%E1%BB%B1ctuy%E1%BA%BFn/tabid/104/VE4NCommand/registration/Default.aspx',
        headers: {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Cookie': `ASP.NET_SessionId=${sessionId}; language=en-US; language=en-US`,
            'Origin': 'http://mienthithucvk.mofa.gov.vn',
            'Referer': 'http://mienthithucvk.mofa.gov.vn/%C4%90%C4%83ngk%C3%BD/Khaitr%E1%BB%B1ctuy%E1%BA%BFn/tabid/104/VE4NCommand/registration/Default.aspx',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
            ...form.getHeaders()
        },
        data: form
    })

    const $4 = cheerio.load(step4.data)
    form = new FormData();
    form.append('__EVENTTARGET', '');
    form.append('__EVENTARGUMENT', '');
    form.append('__VSTATE', $4('form input[name="__VSTATE"]').val());
    form.append('dnn$dnnMENU$RadMenu1', '');
    form.append('dnn$ctr408$Desktop$subctr$ibDownload.x', '0');
    form.append('dnn$ctr408$Desktop$subctr$ibDownload.y', '0');
    form.append('dnn$ctr408$Desktop$subctr$txtEmail', '');
    form.append('ScrollTop', '');
    form.append('__dnnVariable', '');

    const step5 = await axios({
        method: 'post',
        maxBodyLength: Infinity,
        url: 'http://mienthithucvk.mofa.gov.vn/%C4%90%C4%83ngk%C3%BD/Khaitr%E1%BB%B1ctuy%E1%BA%BFn/tabid/104/VE4NCommand/finishregistration/Default.aspx',
        headers: {
            'Upgrade-Insecure-Requests': '1',
            'Cookie': 'language=en-US',
            ...form.getHeaders()
        },
        data: form,
        responseType: 'arraybuffer'
    })

    let pdf_buff = step5.data;
    let photo_image = pod_data.photo_passport_photo_copy_of_photo
    if (photo_image) {
        const photo_resp = await axios.get(photo_image, { responseType: 'arraybuffer' })
        pdf_buff = await import_photo_to_application_form(photo_resp.data, pdf_buff, FRAME);
    }
    const buckets = JSON.parse(process.env.ad_s3);
    const form_file = await s3_upload_buff(buckets.ariadirect_prod_applications, `tasks/Application_Form_${pod_data.passport_core_info_surname}_${pod_data.passport_core_info_given_name}_Native_Visa.pdf`, pdf_buff)
    const tmp_file = tmp.fileSync({ mode: 0o644, prefix: 'temp-', postfix: '.pdf' });
    fs.writeFileSync(tmp_file.name, pdf_buff);
    const pdfText = await new Promise(r => {
        pdfUtil.pdfToText(tmp_file.name, { from: 0, to: 1 }, function (err, data) {
            if (err) throw (err);
            console.log(data); //print text    
            r(data)
        });
    });
    const document_id = pdfText.split('\n')[0].trim()

    return {
        form_callback: { document_id },
        form_file,
    }
}
const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        // const data = await create_form(order, task, pod_data)

        const data = await retry_operation(async () => {
            return await create_form(order, task, pod_data)
        }, 3, 'create form error after 3 attempts')

        return {
            success: true,
            form_file: data.form_file,
            form_callback: {
                "form_name": 'Application Form',
                "file_name": `Order ${order_id} Application Form`,
                "file_url": data.form_file,
                "document_id": data.form_callback.document_id,
            }
        }
    } catch (error) {
        console.log(error)
        return {
            success: false,
            error: error.message
        }
    }
}
module.exports = { main, create_form }