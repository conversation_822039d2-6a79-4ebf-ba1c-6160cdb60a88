const fs = require('fs');
const _ = require('lodash');
const axios = require('axios');
const moment = require('moment');
const { connect } = require('puppeteer-real-browser');
const AWS = require('aws-sdk');
const { COUNTRY, AIRPORT, RANDOM_HOTEL } = require('./data_mapping')
const convert_country = (s) => COUNTRY[s] ? String(COUNTRY[s]) : s
const convert_airport = (s) => AIRPORT[s] ? String(AIRPORT[s]) : s
const PhoneNumber = require('libphonenumber-js');
const { URL } = require('url');
const { create_zip, s3_upload_buff, s3_url_to_bucket_key, get_order_task_pod_data } = require('../../shared/helpers');
const { PuppeteerScreenRecorder } = require('puppeteer-screen-recorder');

const { get_image_captcha_text } = require('../../shared/captcha');
let DEBUG = false
const RECODER_NAME = `Recorder_${moment().unix()}.mp4`
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

async function get_image_file(url, name) {
    try {
        const { bucket, key } = s3_url_to_bucket_key(url)
        const s3 = new AWS.S3();
        const params = {
            Bucket: bucket,
            Key: key
        };
        const response = await s3.getObject(params).promise();
        fs.writeFileSync(name, response.Body);
        console.log(`File ${name} downloaded successfully.`);
    } catch (error) {
        console.error('Error downloading file:', error);
    }
}


const set_date_value = async (page, selector, value) => {
    await page.waitForSelector(selector)
    await sleep(2000)
    const date_string = moment(value).toISOString()
    console.log(date_string)
    await page.evaluate((selector, date_string) => {
        console.log(date_string)
        new Pikaday({
            field: document.querySelector(selector),
            format: 'D MMM YYYY'
        }).setDate(date_string);
    }, selector, date_string);
};
const overrideType = async (page, selector, value) => {
    await page.waitForSelector(selector)
    await page.evaluate(selector => {
        const inputElement = document.querySelector(selector);
        if (inputElement) {
            inputElement.value = '';
        }
    }, selector);

    await page.type(selector, value, { delay: 100 });
}

const visa_cam_form = async (data) => {
    let index = 1;
    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })

    const { page, browser } = await connect({
        headless: false,
        turnstile: false,
        disableXvfb: false,
        customConfig: {
            ignoreDefaultArgs: ['--enable-automation'],  // Disable automation flag
        },
        connectOption: {
            defaultViewport: {
                width: 1920,
                height: 1080
            },
            args: [
                '--no-sandbox', '--disable-setuid-sandbox'
            ],
            ignoreHTTPSErrors: true,
            waitForInitialPage: true,
        },
    });
    let recorder = null

    try {
        page.setDefaultTimeout(60000);
        recorder = new PuppeteerScreenRecorder(page);
        await recorder.start(RECODER_NAME);
        await page.goto('https://www.evisa.gov.kh/application');
        const cookies = await page.cookies();
        const cookie = cookies.map(ck => `${ck.name}=${ck.value}`).join("; ")
        VERIFY_CAPTCHA: for (let c = 0; c < 50; c++) {
            await page.waitForSelector('[model="captcha"] img.w-full')
            await sleep(2000)
            const captcha_element = await page.$('[model="captcha"] img.w-full')
            const buffer = await captcha_element.screenshot({ encoding: 'binary' });
            var captcha = await solve_captcha(Buffer.from(buffer))
            console.log(captcha)

            await page.waitForSelector('[placeholder="captcha"]');

            await page.type('[placeholder="captcha"]', captcha, { delay: 100 })

            const buttons = await page.$$('button.evisa.primary');
            for (let button of buttons) {
                const text = await page.evaluate(el => el.textContent, button);
                if (text.includes('Apply Now')) {
                    await button.click();
                    break;
                }
            }

            try {
                await sleep(2000)
                try {
                    const captchaText = await page.$eval('[model="captcha"]', el => el.textContent);
                    if (captchaText.includes('Invalid captcha')) {
                        continue VERIFY_CAPTCHA;
                    }
                } catch (error) {

                }

                await page.waitForSelector('[model="app.passport_image"]')
                break VERIFY_CAPTCHA;
            } catch (e) {
                console.log(e.message)
            }
        }

        await page.waitForNetworkIdle()
        await sleep(2000)
        {
            // Trigger the file input dialog
            await page.waitForSelector('[model="app.passport_image"] input[type="file"]');
            await get_image_file(data.document_copy_of_passport_copy_of_passport_main_page, "images/1.jpeg")
            const files = await page.$$('[model="app.passport_image"] input[type="file"]');
            await files[1].uploadFile("images/1.jpeg")
        }
        {
            // Trigger the file input dialog
            await get_image_file(data.photo_passport_photo_copy_of_photo, "images/2.jpeg")
            const files = await page.$$('[model="app.app_photo"] input[type="file"]');
            await files[0].uploadFile("images/2.jpeg")
            await sleep(2000)
            await page.click('[model="app.app_photo"] .evisa.small.success');
        }


        await sleep(5000)

        await overrideType(page, 'input[placeholder="Surname"]', data.passport_core_info_surname)
        await overrideType(page, 'input[placeholder="Given Name"]', data.passport_core_info_given_name)
        // await page.type('input[placeholder="Surname"]', data.passport_core_info_surname)
        // await page.type('input[placeholder="Given Name"]', data.passport_core_info_given_name)
        const phone_obj = data.personal_core_info_phone ?? data.travel_traveler_contact_info_phone
        const phone = PhoneNumber(phone_obj?.phone).nationalNumber
        await page.type('[model="app.contact_phone"] input', phone)

        let email = process.env.ad_env === 'prod' ? '<EMAIL>' : '<EMAIL>'
        await page.type('[model="app.contact_email_1"] input', email)
        await page.type('[model="app.re_email_1"] input', email)
        await page.select('[model="app.app_sex"] select', data.passport_core_info_gender === 'M' ? 'Male' : 'Female')

        // await page.type('[model="app.app_cob_id"] select', 'Albania')
        await page.select('[model="app.app_cob_id"] select', convert_country(data.passport_core_info_country_of_birth))
        await sleep(5000)
        await page.select('[model="app.app_nationality_country_id"] select', convert_country(data.passport_core_info_nationality))
        await sleep(5000)
        const hotel = _.sample(RANDOM_HOTEL[data.travel_enter_flight_enter_airport])
        await page.type('[model="app.tmp_address"] textarea', hotel)

        await page.waitForSelector('[model="app.passport_no"] input');
        await page.waitForSelector('[model="app.passport_issue_date"] input');

        await overrideType(page, '[model="app.passport_no"] input', data.passport_core_info_passport_number)
        await sleep(2000)
        await page.select('[model="app.country_issue_id"] select', convert_country(data.service_core_info_region_of_residence))
        await sleep(2000)
        await page.select('[model="app.port_entry"] select', convert_airport(data.travel_enter_flight_enter_airport))
        await sleep(2000)

        await page.select('[model="app.visa_type_id"] select', '1')

        await sleep(2000)

        await page.select('[model="app.occupation_id"] select', {
            'company_employee': '6',
            'homemaker': '17',
            'student': '1',
            'retired': '8'
        }[data.personal_occupation_occupation] ?? '8');

        if (data.personal_occupation_occupation === 'homemaker') {
            await page.type('[model="app.other_occupation"] input', "Homemaker")
        }


        await set_date_value(page, '[model="app.app_dob"] input', data.passport_core_info_date_of_birth)
        await set_date_value(page, '[model="app.passport_issue_date"] input', data.passport_core_info_issue_date)
        await set_date_value(page, '[model="app.passport_expire_date"] input', data.passport_core_info_expiration_date)
        await set_date_value(page, '[model="app.entry_date"] input', data.service_core_info_entry_date)

        await sleep(2000)

        await page.evaluate(() => {
            for (const selector of [
                'verify.app_surname',
                'verify.app_givenname',
                'verify.passport_issue_date',
                'verify.passport_expire_date',
                'verify.app_sex',
                'verify.app_dob',
                'verify.app_cob_id',
                'verify.app_nationality_country_id',
                'verify.passport_no',
            ]) {
                const el = document.querySelector(`input[type="checkbox"][wire\\:model="${selector}"]`);
                el.click()
                el.checked = true;
                el.value = 'on'
            }
        });


        {
            const buttons = await page.$$('button.evisa.primary');
            await buttons[1].click()
        }

        await sleep(2000)
        await page.waitForSelector('#agree');
        await page.click('#agree');

        {
            const buttons = await page.$$('button.evisa.primary');
            await buttons[0].click()
        }


        await page.waitForNetworkIdle()
        await sleep(5000)


        const website_url = page.url()
        const resp = await axios.get(website_url);
        const documents = /T2\d+/.exec(resp.data)

        const document_id = documents.reduce((l, c) => c.length > l.length ? c : l, '')
        await recorder.stop();
        await browser.close()

        const note = [
            "Code: " + document_id,
            "Email: " + email,
            "Apply: " + moment().format('DD/MM/YYYY'),
            "Payment URL: " + 'https://www.evisa.gov.kh/check_change',
        ].join('\n')

        return {
            document_id,
            email,
            note,
            created_at: moment().format('DD/MM/YYYY'),
            tracking_url: 'https://www.evisa.gov.kh/check_change',
        }
    } catch (error) {
        console.log(error)
        console.log(error.message)
        await recorder.stop();
        await browser.close()
        return null
    }

}


const solve_captcha = async (buffer) => {
    const captcha = await get_image_captcha_text(buffer)
    return captcha
}


const ZIP_NAME = "steps.zip"

const create_form = async (order, task, pod_data) => {
    console.log(JSON.stringify(pod_data, null, 2))

    const result = {
        success: false,
        form_file: "",
        form_callback: {},
    }

    let MAX_RETRY = 3
    let form_callback = await visa_cam_form(pod_data)
    if (!form_callback && MAX_RETRY > 0) {
        MAX_RETRY--
        form_callback = await visa_cam_form(pod_data)
    }
    if (form_callback) {
        result.success = true
        result.form_callback = form_callback
    }

    const buckets = JSON.parse(process.env.ad_s3)
    await create_zip(ZIP_NAME, 'images/')

    await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${RECODER_NAME}`, fs.readFileSync(RECODER_NAME))

    fs.rmSync('images', { recursive: true })
    fs.rmSync(RECODER_NAME, { recursive: true })

    result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${RECODER_NAME}`
    console.log("STEPS:", result.form_callback.steps)
    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form(order, task, pod_data)
        return data
    } catch (error) {
        console.log(error)
    }
}

module.exports = { main, create_form }