const COUNTRY = {
    "AFG": "1",    // Afghanistan
    "ALA": "244",  // Aland Islands
    "ALB": "2",    // Albania
    "DZA": "3",    // Algeria
    "ASM": "4",    // American Samoa
    "AND": "5",    // Andorra
    "AGO": "6",    // Angola
    "AIA": "7",    // Anguilla
    "ATA": "8",    // Antarctica
    "ATG": "9",    // Antigua and Barbuda
    "ARG": "10",   // Argentina
    "ARM": "11",   // Armenia
    "ABW": "12",   // Aruba
    "AUS": "13",   // Australia
    "AUT": "14",   // Austria
    "AZE": "15",   // Azerbaijan
    "BHS": "16",   // Bahamas
    "BHR": "17",   // Bahrain
    "BGD": "18",   // Bangladesh
    "BRB": "19",   // Barbados
    "BLR": "20",   // Belarus
    "BEL": "21",   // Belgium
    "BLZ": "22",   // Belize
    "BEN": "23",   // Benin
    "BMU": "24",   // Bermuda
    "BTN": "25",   // Bhutan
    "BOL": "26",   // Bolivia (Plurinational State of)
    "BES": "245",  // Bonaire Saint Eustatius and Saba
    "BIH": "27",   // Bosnia & Herzegovina
    "BWA": "28",   // Botswana
    "BVT": "29",   // Bouvet Island
    "BRA": "30",   // Brazil
    "IOT": "31",   // British Indian Ocean Territory
    "BRN": "32",   // Brunei Darussalam
    "BGR": "33",   // Bulgaria
    "BFA": "34",   // Burkina Faso
    "BDI": "35",   // Burundi
    "KHM": "36",   // Cambodia
    "CMR": "37",   // Cameroon
    "CAN": "38",   // Canada
    "CPV": "39",   // Cape Verde
    "CYM": "40",   // Cayman Islands
    "CAF": "41",   // Central African Republic
    "TCD": "42",   // Chad
    "CHL": "43",   // Chile
    "CHN": "44",   // China
    "CXR": "45",   // Christmas Island
    "CCK": "46",   // Cocos (Keeling) Islands
    "COL": "47",   // Colombia
    "COM": "48",   // Comoros
    "COG": "49",   // Congo
    "COK": "50",   // Cook Islands
    "CRI": "51",   // Costa Rica
    "CIV": "52",   // Cote d'Ivoire
    "HRV": "53",   // Croatia
    "CUB": "54",   // Cuba
    "CUW": "246",  // Curacao
    "CYP": "55",   // Cyprus
    "CZE": "56",   // Czech Republic
    "PRK": "112",  // Democratic People's Republic of Korea (North Korea)
    "COD": "237",  // Democratic Republic of Congo
    "DNK": "57",   // Denmark
    "DJI": "58",   // Djibouti
    "DMA": "59",   // Dominica
    "DOM": "60",   // Dominican Republic
    "ECU": "62",   // Ecuador
    "EGY": "63",   // Egypt
    "SLV": "64",   // El Salvador
    "GNQ": "65",   // Equatorial Guinea
    "ERI": "66",   // Eritrea
    "EST": "67",   // Estonia
    "SWZ": "202",  // Eswatini (formerly "Swaziland" in ISO-3 list)
    "ETH": "68",   // Ethiopia
    "FLK": "69",   // Falkland Islands (Malvinas)
    "FRO": "70",   // Faroe Islands
    "FJI": "71",   // Fiji
    "FIN": "72",   // Finland
    "FRA": "74",   // France
    "GUF": "75",   // French Guiana
    "PYF": "76",   // French Polynesia
    "ATF": "77",   // French Southern Territories
    "GAB": "78",   // Gabon
    "GMB": "79",   // Gambia
    "GEO": "80",   // Georgia
    "DEU": "81",   // Germany
    "GHA": "82",   // Ghana
    "GIB": "83",   // Gibraltar
    "GRC": "84",   // Greece
    "GRL": "85",   // Greenland
    "GRD": "86",   // Grenada
    "GLP": "87",   // Guadeloupe
    "GUM": "88",   // Guam (not in options, best guess)
    "GTM": "89",   // Guatemala
    "GGY": "256",  // Guernsey
    "GIN": "90",   // Guinea
    "GNB": "91",   // Guinea-Bissau
    "GUY": "92",   // Guyana
    "HTI": "93",   // Haiti
    "HMD": "94",   // Heard and McDonald Islands
    "HND": "95",   // Honduras
    "HKG": "96",   // Hong Kong (not in options, best guess)
    "HUN": "97",   // Hungary
    "ISL": "98",   // Iceland
    "IND": "99",   // India
    "IDN": "100",  // Indonesia
    "IRN": "101",  // Islamic Republic of Iran
    "IRQ": "102",  // Iraq
    "IRL": "103",  // Ireland
    "IMN": "254",  // Isle of Man
    "ISR": "104",  // Israel
    "ITA": "105",  // Italy
    "JAM": "106",  // Jamaica
    "JPN": "107",  // Japan
    "JEY": "257",  // Jersey
    "JOR": "108",  // Jordan
    "KAZ": "109",  // Kazakhstan
    "KEN": "110",  // Kenya
    "KIR": "111",  // Kiribati
    "KWT": "114",  // Kuwait
    "KGZ": "115",  // Kyrgyzstan
    "LAO": "116",  // Lao People's Democratic Republic
    "LVA": "117",  // Latvia
    "LBN": "118",  // Lebanon
    "LSO": "119",  // Lesotho
    "LBR": "120",  // Liberia
    "LBY": "121",  // Libya
    "LIE": "122",  // Liechtenstein
    "LTU": "123",  // Lithuania
    "LUX": "124",  // Luxembourg
    "MAC": "125",  // Macau (not in options, best guess)
    "MKD": "259",  // Republic of North Macedonia
    "MDG": "127",  // Madagascar
    "MWI": "128",  // Malawi
    "MYS": "129",  // Malaysia
    "MDV": "130",  // Maldives
    "MLI": "131",  // Mali
    "MLT": "132",  // Malta
    "MHL": "133",  // Marshall Islands
    "MTQ": "134",  // Martinique
    "MRT": "135",  // Mauritania
    "MUS": "136",  // Mauritius
    "MYT": "137",  // Mayotte
    "MEX": "138",  // Mexico
    "FSM": "139",  // Micronesia (Federated States of)
    "MDA": "140",  // Republic of Moldova
    "MCO": "141",  // Monaco
    "MNG": "142",  // Mongolia
    "MNE": "242",  // Montenegro
    "MSR": "143",  // Montserrat
    "MAR": "144",  // Morocco
    "MOZ": "145",  // Mozambique
    "MMR": "146",  // Myanmar
    "NAM": "147",  // Namibia
    "NRU": "148",  // Nauru
    "NPL": "149",  // Nepal
    "NLD": "150",  // Netherlands
    "ANT": "151",  // Netherlands Antilles
    "NCL": "152",  // New Caledonia
    "NZL": "153",  // New Zealand
    "NIC": "154",  // Nicaragua
    "NER": "155",  // Niger
    "NGA": "156",  // Nigeria
    "NIU": "157",  // Niue
    "NFK": "158",  // Norfolk Island
    "MNP": "159",  // Northern Mariana Islands
    "NOR": "160",  // Norway
    "OMN": "161",  // Oman
    "PAK": "162",  // Pakistan
    "PLW": "163",  // Palau
    "PSE": "247",  // Occupied Palestinian Territory
    "PAN": "164",  // Panama
    "PNG": "165",  // Papua New Guinea
    "PRY": "166",  // Paraguay
    "PER": "167",  // Peru
    "PHL": "168",  // Philippines
    "PCN": "169",  // Pitcairn
    "POL": "170",  // Poland
    "PRT": "171",  // Portugal
    "PRI": "172",  // Puerto Rico
    "QAT": "173",  // Qatar
    "KOR": "113",  // Republic of Korea (South Korea)
    "REU": "174",  // Reunion
    "ROU": "175",  // Romania
    "RUS": "176",  // Russian Federation
    "RWA": "177",  // Rwanda
    "KNA": "178",  // Saint Kitts and Nevis
    "LCA": "179",  // Saint Lucia
    "VCT": "180",  // Saint Vincent and the Grenadines
    "BLM": "293",  // Saint-Barthelemy
    "MAF": "303",  // Saint-Martin (French part)
    "WSM": "181",  // Samoa
    "SMR": "182",  // San Marino
    "STP": "183",  // Sao Tome and Principe
    "SAU": "184",  // Saudi Arabia
    "SEN": "185",  // Senegal
    "SRB": "243",  // Serbia
    "SYC": "186",  // Seychelles
    "SLE": "187",  // Sierra Leone
    "SGP": "188",  // Singapore
    "SVK": "189",  // Slovakia
    "SVN": "190",  // Slovenia
    "SLB": "191",  // Solomon Islands
    "SOM": "192",  // Somalia
    "ZAF": "193",  // South Africa
    "SGS": "194",  // South Georgia and the South Sandwich Islands
    "SSD": "248",  // South Sudan
    "ESP": "195",  // Spain
    "LKA": "196",  // Sri Lanka
    "SHN": "197",  // St. Helena
    "SPM": "198",  // St. Pierre and Miquelon
    "SDN": "199",  // Sudan
    "SUR": "200",  // Suriname
    "SJM": "201",  // Svalbard and Jan Mayen Islands
    "SWE": "203",  // Sweden
    "CHE": "204",  // Switzerland
    "SYR": "205",  // Syrian Arab Republic
    "TWN": "206",  // Taiwan (China)
    "TJK": "207",  // Tajikistan
    "TZA": "208",  // United Republic of Tanzania
    "THA": "209",  // Thailand
    "TLS": "61",   // Timor-Leste
    "TGO": "210",  // Togo
    "TKL": "211",  // Tokelau
    "TON": "212",  // Tonga
    "TTO": "213",  // Trinidad and Tobago
    "TUN": "214",  // Tunisia
    "TUR": "215",  // Turkey
    "TKM": "216",  // Turkmenistan
    "TCA": "217",  // Turks and Caicos Islands
    "TUV": "218",  // Tuvalu
    "UGA": "219",  // Uganda
    "UKR": "220",  // Ukraine
    "ARE": "221",  // United Arab Emirates
    "GBR": "222",  // United Kingdom
    "USA": "223",  // United States
    "UMI": "224",  // United States Minor Outlying Islands
    "URY": "225",  // Uruguay
    "UZB": "226",  // Uzbekistan
    "VUT": "227",  // Vanuatu
    "VAT": "228",  // Vatican City State (Holy See)
    "VEN": "229",  // Venezuela (Bolivarian Republic of)
    "VNM": "230",  // Viet Nam
    "VGB": "231",  // Virgin Islands (British)
    "VIR": "232",  // Virgin Islands (U.S.)
    "WLF": "233",  // Wallis and Futuna Islands
    "ESH": "234",  // Western Sahara
    "YEM": "235",  // Yemen
    "ZMB": "238",  // Zambia
    "ZWE": "239"   // Zimbabwe
};

const AIRPORT = {
    "Phnom Penh International Airport": "1",
    "Siem Reap International Airport": "2",
    "Preah Sihanouk International Airport": "3",
}

const RANDOM_HOTEL = {
    "Phnom Penh International Airport": [
        "NagaWorld Integrated Resort, Samdach Hun Sen Park, Samdech Techo, Phnom Penh, Cambodia",
        "Hyatt Regency Phnom Penh, #55, Street 178, Sangkat Chey Chumnas Phnom Penh, Cambodia"
    ],
    "Siem Reap International Airport": [
        "The Cyclo d'Angkor Boutique Hotel, Preah Sihanouk Ave, Krong Siem Reap, Cambodia",
        "Pandora Suite D'Angkor, Trorpeang Ses, Kok Chork, Krong Siem Reap, Cambodia"
    ],
    "Preah Sihanouk International Airport": [
        "La Vogue Boutique Hotel & Casino, 1171, Land Lot, 502 Serendipity Beach Street (502), Preah Sihanouk, Cambodia",
        "Queenco Hotel & Casino, Victory Beach, Mohavithey Krong, District 3, Preah Sihanouk, Cambodia"
    ]
}
module.exports = { COUNTRY, AIRPORT, RANDOM_HOTEL }