const fs = require('fs');
const moment = require('moment');
const puppeteer = require('puppeteer');
const { s3_upload_buff, get_order_task_pod_data } = require('../../shared/helpers');
const axios = require('axios');
const { get_image_captcha_text } = require('../../shared/captcha');
let DEBUG = false

const visa_form = async (order, task, pod_data) => {
    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })

    let page = null
    let browser = null
    valid = false
    macValue = null
    receiveNoValue = null
    form = null
    MAX_RETRY = 50
    try {
        let email = process.env.ad_env === 'prod' ? '<EMAIL>' : '<EMAIL>'
        browser = await puppeteer.launch({ headless: DEBUG ? false : "new", args: ['--no-sandbox', '--disable-setuid-sandbox'] });
        page = await browser.newPage();
        page.setDefaultTimeout(60000);
        while (!valid && MAX_RETRY > 0) {
            await page.goto('https://niaspeedy.immigration.gov.tw/nia_southeast/imageCodeAction.action');
            await page.waitForNetworkIdle({ waitUntil: 'networkidle0' });
            cookies = await page.cookies();
            cookie = cookies.map(ck => `${ck.name}=${ck.value}`).join("; ")
            // const captcha = await solve_captcha(cookie)
            const captcha_element = await page.$('body > img')
            const buffer = await captcha_element.screenshot({ encoding: 'binary' });
            const captcha = await get_image_captcha_text(buffer, 'claude-3-7-sonnet-20250219')
            console.log("Captcha: ", captcha)
            const formData = new FormData();
            formData.append('language', '1');
            formData.append('resutlMsg', '');
            formData.append('nation', {
                "VNM": "34",
                "IND": "8",
                "IDN": "9",
                "MMR": "4",
                "LAO": "17",
                "KHM": "5"

            }[pod_data.passport_core_info_nationality]);
            formData.append('surname', pod_data.passport_core_info_surname);
            formData.append('given', pod_data.passport_core_info_given_name);
            formData.append('englishName', pod_data.passport_core_info_surname + " " + pod_data.passport_core_info_given_name);
            formData.append('passno', pod_data.passport_core_info_passport_number);
            formData.append('otherPassportNo', '');
            formData.append('passportExpiryDate', moment(pod_data.passport_core_info_expiration_date).format('YYYYMMDD'));
            formData.append('passport_expired_date_year', moment(pod_data.passport_core_info_expiration_date).format('YYYY'));
            formData.append('passport_expired_date_month', moment(pod_data.passport_core_info_expiration_date).format('MM'));
            formData.append('passport_expired_date_day', moment(pod_data.passport_core_info_expiration_date).format('DD'));
            formData.append('birthDate', moment(pod_data.passport_core_info_date_of_birth).format('YYYYMMDD'));
            formData.append('birth_date_year', moment(pod_data.passport_core_info_date_of_birth).format('YYYY'));
            formData.append('birth_date_month', moment(pod_data.passport_core_info_date_of_birth).format('MM'));
            formData.append('birth_date_day', moment(pod_data.passport_core_info_date_of_birth).format('DD'));
            formData.append('birthPlace1', pod_data.passport_core_info_country_of_birth);
            formData.append('birthPlace2', pod_data.passport_core_info_city_of_birth);
            formData.append('sex', pod_data.passport_core_info_gender === "M" ? "0" : "1");
            formData.append('rq.flightNo', pod_data.travel_core_info_arrival_flight_number);
            formData.append('expectEntryDate_year', moment(pod_data.service_core_info_entry_date).format('YYYY'));
            formData.append('expectEntryDate_month', moment(pod_data.service_core_info_entry_date).format('MM'));
            formData.append('expectEntryDate_day', moment(pod_data.service_core_info_entry_date).format('DD'));
            formData.append('rq.occupation', {
                "business_person": "10",
                "company_employee": "5",
                "homemaker": "8",
                "student": "20",
                "retired": "5",
            }[pod_data.personal_occupation_occupation]);
            formData.append('rq.reason', {
                "tourist": "1",
                "business": "16",
            }[order.service.attributes.purpose]);
            formData.append('rq.liveCountry', pod_data.personal_home_address_country);
            formData.append('rq.liveAddress', `${pod_data.personal_home_address_street_number_name}, ${pod_data.personal_home_address_city}, ${pod_data.personal_home_address_state} ${pod_data.personal_home_address_zip_code}`);
            formData.append('contactName', pod_data.personal_individual_reference_in_destination_surname ? `${pod_data.personal_individual_reference_in_destination_surname} ${pod_data.personal_individual_reference_in_destination_given_name}` : 'Courtyard Taipei Downtown');
            formData.append('contactAddress', pod_data.personal_individual_reference_in_destination_street_number_name ? `${pod_data.personal_individual_reference_in_destination_street_number_name}, ${pod_data.personal_individual_reference_in_destination_city}, ${pod_data.personal_individual_reference_in_destination_state} ${pod_data.personal_individual_reference_in_destination_zip_code}, Taiwan` : 'No. 6, Section 3, Minsheng E Rd, Zhongshan District, Taipei City, Taiwan 10480');
            formData.append('relTel', pod_data.personal_individual_reference_in_destination_phone ? pod_data.personal_individual_reference_in_destination_phone : '+8862-7750-3399');
            formData.append('relMobile', pod_data.personal_individual_reference_in_destination_phone ? pod_data.personal_individual_reference_in_destination_phone : '+8862-7750-3399');
            formData.append('relation', pod_data.personal_individual_reference_in_destination_relationship ? pod_data.personal_individual_reference_in_destination_relationship : 'Hotel');
            formData.append('rq.hotelFlag', '1');
            formData.append('rq.zipCode', '1');
            formData.append('rq.address', 'Courtyard Taipei Downtown');
            formData.append('fatherMark', '1');
            formData.append('fatherName', pod_data.family_father_surname + " " + pod_data.family_father_given_name);
            formData.append('fatherAddress', pod_data.family_father_address);
            formData.append('fatherTel', pod_data.family_father_phone.phone);
            formData.append('fatherMobile', pod_data.family_father_phone.phone);
            formData.append('motherMark', '1');
            formData.append('motherName', pod_data.family_mother_surname + " " + pod_data.family_mother_given_name);
            formData.append('motherAddress', pod_data.family_mother_address);
            formData.append('motherTel', pod_data.family_mother_phone.phone);
            formData.append('motherMobile', pod_data.family_mother_phone.phone);
            formData.append('tel', pod_data.personal_core_info_phone.phone);
            formData.append('mobile', '');
            formData.append('phone_number', pod_data.personal_core_info_phone.phone);
            formData.append('email', email);
            formData.append('capacity', {
                "h1b": "2",
                "us_green_card": "1"
            }[pod_data.additional_question_residence_immigration_status_immigration_status]);
            formData.append('capacity_1_type', ' ');
            formData.append('capacity_2_type', '315');
            formData.append('latest_arrival_date', '0');
            formData.append('latest_depart_date', '0');
            formData.append('credentials', pod_data.additional_question_residence_immigration_status_document_number);
            formData.append('cardExpiryDate', moment(pod_data.additional_question_residence_immigration_status_expiration_date).format('YYYYMMDD'));
            formData.append('s_expired_date_year', moment(pod_data.additional_question_residence_immigration_status_expiration_date).format('YYYY'));
            formData.append('s_expired_date_month', moment(pod_data.additional_question_residence_immigration_status_expiration_date).format('MM'));
            formData.append('s_expired_date_day', moment(pod_data.additional_question_residence_immigration_status_expiration_date).format('DD'));
            formData.append('jcaptchaResponse', captcha);
            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: 'https://niaspeedy.immigration.gov.tw/nia_southeast/doApproval',
                headers: {
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'en-US,en;q=0.9,zh-TW;q=0.8,zh;q=0.7,zh-CN;q=0.6',
                    'Origin': 'https://niaspeedy.immigration.gov.tw',
                    'Referer': 'https://niaspeedy.immigration.gov.tw/nia_southeast/doConfirm',
                    'Cookie': cookie,
                },
                data: formData
            };
            response = await axios(config)
            if (!response.data.includes('jcaptchaResponse')) {
                console.log("Bypassed Captcha")
                const macRegex = /<input type="hidden" name="mac" value="([^"]*)">/;
                const receiveNoRegex = /<input type="hidden" name="receiveNo" value="([^"]*)">/;
                const macMatch = response.data.match(macRegex);
                const receiveNoMatch = response.data.match(receiveNoRegex);
                macValue = macMatch ? macMatch[1] : null;
                receiveNoValue = receiveNoMatch ? receiveNoMatch[1] : null;
            }
            if (macValue && receiveNoValue) {
                valid = true
            } else {
                MAX_RETRY--
            }
        }
        if (valid) {
            console.log("Submit form successfully - Getting Form ...")
            console.log("macValue: ", macValue)
            console.log("receiveNoValue: ", receiveNoValue)
            formData = new FormData();
            formData.append('mac', macValue);
            formData.append('receiveNo', receiveNoValue);
            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: 'https://niaspeedy.immigration.gov.tw/nia_southeast/southeastPrint',
                headers: {
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'en-US,en;q=0.9,zh-TW;q=0.8,zh;q=0.7,zh-CN;q=0.6',
                    'Origin': 'https://niaspeedy.immigration.gov.tw',
                    'Referer': 'https://niaspeedy.immigration.gov.tw/nia_southeast/doConfirm',
                    'Cookie': cookie,
                },
                data: formData,
                withCredentials: true,
                responseType: 'arraybuffer'
            };
            response = await axios(config)
            form = response.data
        }
        await browser.close()
        return {
            form: form
        }
    } catch (error) {
        console.error(error)
        console.log(error.message)
        await browser.close()
        return null
    }

}

const ZIP_NAME = "steps.zip"

const create_form = async (order, task, pod_data) => {
    console.log(JSON.stringify(pod_data, null, 2))

    const result = {
        success: false,
        form_file: "",
        form_callback: {},
    }

    let MAX_RETRY = 3
    let form_data = await visa_form(order, task, pod_data)
    if (!form_data && MAX_RETRY > 0) {
        MAX_RETRY--
        form_data = await visa_form(pod_data)
    }
    const file_name = `Application_Form_${pod_data.passport_core_info_surname}_${pod_data.passport_core_info_given_name}_${moment().unix()}.pdf`
    const buckets = JSON.parse(process.env.ad_s3)
    if (form_data?.form != null) {
        // Save the PDF to a file
        fs.writeFileSync("images/form.pdf", form_data.form);
        await s3_upload_buff(buckets.ariadirect_prod_applications, `tasks/${file_name}`, form_data.form)
        result.form_file = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/tasks/${file_name}`
        result.success = true
    } else {
        if (form_data?.error != null) {
            result.form_callback.error = form_data.error
        }
    }
    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form(order, task, pod_data)
        return data
    } catch (error) {
        console.log(error)
    }
}
// main(6568, 7762)
module.exports = { main, create_form }