const moment = require('moment')
const fs = require('fs')
const data_mapping = require('./data_mapping')
const shared_data_mapping = require('../../shared/data_mapping')
const { get_order_task, fill_pdf_form, get_country_name_v2, get_order_task_pod_data } = require('../../shared/helpers')

const NA = "N/A"
function getRandomHotel() {
    const hotels = data_mapping.HOTEL;
    const randomIndex = Math.floor(Math.random() * hotels.length);
    return hotels[randomIndex];
}

const create_form = async (order, task, data) => {
    //Country
    [
        'passport_core_info_country_of_birth',
        'passport_core_info_nationality',
        'passport_core_info_issuing_authority',
        'personal_home_address_country',
        'employee_company_info_country',
        'additional_question_schengen_member_state_entry',
        'personal_individual_reference_in_destination_country',
        'employee_company_reference_in_destination_country',
        'family_spouse_nationality',
        'family_spouse_residence_address_country',
        'personal_education_country',
        'personal_emergency_contact_region_of_residence',
        'passport_second_passport_nationality'
    ].forEach(key => {
        if (data[key]) data[key] = get_country_name_v2(data[key])
    })
    //Length Of Stay
    const entryDate = moment(data.service_core_info_entry_date);
    const exitDate = moment(data.service_core_info_exit_date);
    const lengthOfStay = exitDate.diff(entryDate, 'days')
    data.length_of_stay = lengthOfStay.toString() + " Day(s)"
    //Date
    var dates = [
        'passport_core_info_date_of_birth',
        'passport_core_info_issue_date',
        'passport_core_info_expiration_date',
        'service_core_info_entry_date',
        'service_core_info_exit_date',
        'family_spouse_date_of_birth',
        'passport_second_passport_expiration_date'
    ]
    dates.forEach(key => {
        if (data[key]) data[key] = moment(data[key]).format('YYYY/MM/DD')
    })
    //gender
    data[data.passport_core_info_gender] = "X"
    //Dual Citizen
    if (data.passport_additional_info_is_dual_citizen) {
        data.passport_additional_info_is_dual_citizen_y = "X"
        data.secondary_regular_passport = "X"
    } else {
        data.passport_additional_info_is_dual_citizen_n = "X"
    }
    data.personal_emergency_contact_relationship = shared_data_mapping.relationship[data.personal_emergency_contact_relationship]
    data.home_address = `${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${data.personal_home_address_state}${data.personal_home_address_zip_code ? " " + data.personal_home_address_zip_code : ""}, ${data.personal_home_address_country}`
    data["personal_phone"] = data.personal_core_info_phone.phone
    if (data.family_spouse_phone) {
        data.family_spouse_phone = data.family_spouse_phone.phone
    }
    data["emergency_name"] = data.personal_emergency_contact_given_name + " " + data.personal_emergency_contact_surname
    data["personal_emergency_contact_phone"] = data.personal_emergency_contact_phone.phone
    data[data.personal_core_info_marital_status] = "X"
    if (data.personal_core_info_marital_status == "married") {
        data.family_spouse_family_address = "SAME AS SPOUSE"
    }
    data["status_of_stay"] = "C-3"
    if (data.family_child_how_many_children > 0) {
        data.have_child = "X"
    } else {
        data.no_child = "X"
        data.family_child_how_many_children = null
    }
    data.personal_education_address = `${data.personal_education_street_number_name}, ${data.personal_education_city}, ${data.personal_education_state}${data.personal_education_zip_code ? " " + data.personal_education_zip_code : ""}, ${data.personal_education_country}`
    if (data.personal_education_education in data_mapping.education) {
        //High School >>>
        data[data.personal_education_education] = "X"
        data.personal_education_education = null
    } else {
        //<<< Secondary School
        data.other_education = "X"
        data.personal_education_education = data_mapping.kid_education[data.personal_education_education]
    }


    //Default Value
    data.other_name_n = "X"
    data.short_tern = "X"
    data.regular_passport = "X"
    data.family_in_korea_n = "X"
    //Employee
    data.employee_company_info_address = `${data.employee_company_info_street_number_name}, ${data.employee_company_info_city}, ${data.employee_company_info_state}${data.employee_company_info_zip_code ? " " + data.employee_company_info_zip_code : ""}, ${data.employee_company_info_country}`
    data.employee_company_info_phone = data.employee_company_info_phone.phone
    data[data.personal_occupation_occupation] = "X"
    data[order.service.attributes.purpose] = "X"
    //Hotel
    const hotel = getRandomHotel()
    data.hotel_address = hotel.Address
    data.hotel_phone = hotel.Phone
    //Companion
    if (data.travel_companions_info_travelling_to_korea_with_family_member == true) {
        data.companion_y = "X"
        for (let index = 0; index < data.travel_companions_info_kor_companions_info.length; index++) {
            //Only Fill 4
            if (index <= 3) {
                const companionInfo = data.travel_companions_info_kor_companions_info[index];
                data[`companion_name_${index}`] = companionInfo.find(info => info.id === 'travel_companions_info_given_name').value.fe + " " + companionInfo.find(info => info.id === 'travel_companions_info_surname').value.fe
                data[`companion_dob_${index}`] = moment(companionInfo.find(info => info.id === 'travel_companions_info_date_of_birth').value.fe).format('YYYY/MM/DD')
                data[`companion_nationality_${index}`] = get_country_name_v2(companionInfo.find(info => info.id === 'travel_companions_info_nationality').value.fe)
                data[`companion_relationship_${index}`] = shared_data_mapping.relationship[companionInfo.find(info => info.id === 'travel_companions_info_kor_relationship').value.fe]
            }
        }
    } else {
        data.companion_n = "X"
    }
    //Previous Visited KOR
    if (data.travel_visited_country_have_you_visited_this_country) {
        data.travel_visited_country_have_you_visited_this_country_y = "X"
        for (let index = 0; index < data.travel_visited_country_recent_travel_info_this_country.length; index++) {
            //Only Fill last 5
            if (index <= 4) {
                const visitInfo = data.travel_visited_country_recent_travel_info_this_country[index];
                data[`recent_travel_purpose_${index}`] = shared_data_mapping.purpose[visitInfo.find(info => info.id === 'travel_visited_country_purpose').value.fe] ?? visitInfo.find(info => info.id === 'travel_visited_country_purpose').value.fe
                //Length Of Stay
                const entryDate = moment(visitInfo.find(info => info.id === 'travel_visited_country_from').value.fe).format('YYYY/MM/DD')
                const exitDate = moment(visitInfo.find(info => info.id === 'travel_visited_country_to').value.fe).format('YYYY/MM/DD')
                data[`recent_travel_length_of_stay_${index}`] = `(${entryDate}) ~ (${exitDate})`

            }
        }
    } else {
        data.travel_visited_country_have_you_visited_this_country_n = "X"
    }

    //Travel History
    if (data.travel_visited_country_have_you_visited_any_other_countries_beside_apply_country_in_5y == true) {
        data.visited_country_y = "X"
        for (let index = 0; index < data.travel_visited_country_visited_country_info.length; index++) {
            //Only Fill last 5
            if (index <= 4) {
                const visitInfo = data.travel_visited_country_visited_country_info[index];
                data[`travel_visited_country_purpose_${index}`] = shared_data_mapping.purpose[visitInfo.find(info => info.id === 'travel_visited_country_purpose').value.fe] ?? visitInfo.find(info => info.id === 'travel_visited_country_purpose').value.fe
                data[`travel_visited_country_country_${index}`] = get_country_name_v2(visitInfo.find(info => info.id === 'travel_visited_country_country').value.fe)
                //Length Of Stay
                const entryDate = moment(visitInfo.find(info => info.id === 'travel_visited_country_from').value.fe).format('YYYY/MM/DD')
                const exitDate = moment(visitInfo.find(info => info.id === 'travel_visited_country_to').value.fe).format('YYYY/MM/DD')
                data[`travel_visited_country_date_${index}`] = `(${entryDate}) ~ (${exitDate})`

            }


        }
    } else {
        data.visited_country_n = "X"
    }
    //Finance
    data.travel_cost = "$ " + (lengthOfStay * 100 > 5000 ? lengthOfStay * 100 : 5000)
    if (order.service.attributes.purpose === 'tourist') {
        data.finance_support_name = data.passport_core_info_given_name + " " + data.passport_core_info_surname
        data.finance_support_relationship = "Myself"
        data.finance_support_phone = data["personal_phone"]
        data.finance_support_type_of_support = "Total Trip Cost"
    }
    data.filename = `Application_Form_${data.passport_core_info_surname}_${data.passport_core_info_given_name}_korea.pdf`
    data.font_size = 'auto'
    const pdf_file = await fill_pdf_form(__dirname + '/korea.pdf', data)

    const result = {
        success: true,
        form_file: pdf_file,
        form_callback: {},
    }

    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const result = await create_form(order, task, pod_data)
        return result
    } catch (error) {
        console.log(error)
    }
}
// main(4392, 5121)
module.exports = { main, create_form }