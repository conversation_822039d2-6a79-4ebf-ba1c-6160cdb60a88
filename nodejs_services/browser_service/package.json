{"name": "browser-service", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@faker-js/faker": "^9.2.0", "@pdf-lib/fontkit": "^1.1.1", "archiver": "^5.3.1", "aws-sdk": "^2.1386.0", "axios": "^1.9.0", "axios-cookiejar-support": "^5.0.5", "bluebird": "^3.7.2", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "docx-templates": "^4.13.0", "dotenv": "^16.3.1", "ejs": "^3.1.10", "express": "^4.21.1", "extract-json-from-string": "^1.0.1", "fake-address-generator": "^2.0.0", "fill-pdf-utf8": "^2.2.1", "follow-redirects": "^1.15.6", "fs-extra": "^11.2.0", "http-cookie-agent": "^7.0.1", "imap": "^0.8.19", "imap-parser": "^0.0.3", "iso-3166-1": "^2.1.1", "jsdom": "^24.1.0", "libphonenumber-js": "^1.10.53", "lodash": "^4.17.21", "mailparser": "^3.7.1", "moment": "^2.29.4", "moment-timezone": "^0.5.45", "msgpackr": "^1.11.2", "pdf-lib": "^1.17.1", "pdf-to-text": "^0.0.7", "puppeteer": "^24.9.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-real-browser": "1.3.17", "puppeteer-screen-recorder": "^3.0.6", "redis": "^4.7.0", "s3-url-parser": "^1.0.3", "sequelize": "^6.37.5", "slugify": "^1.6.6", "sqs-consumer": "^7.2.0", "terminal-image": "^2.0.0", "tmp": "^0.2.3", "tough-cookie": "^5.1.2", "us": "^2.0.0"}}