
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>U.S. Passport Application Status</title><script>window['adrum-start-time'] = new Date().getTime(); var appKey='EUM-AAB-AUM';</script><script type="text/javascript" src="https://eum.state.gov/appd.21.7.0.3493.js"></script>
    <link href="https://fonts.googleapis.com/css?family=Oswald:500|Roboto+Condensed:400,700" rel="stylesheet">
    <link href="/Content/font-awesome.min.css" rel="stylesheet" />
    <link href="/Content/bootstrap.min.css" rel="stylesheet" />

    <link href="/BotDetectCaptcha.ashx?get=layoutStyleSheet"
          rel="stylesheet" type="text/css" />
    <link href="/Content/Site.css" rel="stylesheet" />
    <script src="/Scripts/modernizr-2.6.2.js"></script>



        <!-- Federated UA Script for Production -->
        <script async type="text/javascript" language="javascript" id="_fed_an_ua_tag"
                src="https://dap.digitalgov.gov/Universal-Federated-Analytics-Min.js?agency=DOS&subagency=CA"></script>

    <!-- OPSS Google Tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-TVB87S3CZT"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());
        gtag('config', 'G-TVB87S3CZT');
    </script>

</head>
<body>
    
    <!--Hamburger Menu-->
    <div class="opssHamburgerMenu">
        <nav class="navbar navbar-toggleable navbar-inverse d-sm-none" style="background-color:#06284C;">
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarTogglerDemo01" aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarTogglerDemo01">
                <ul class="navbar-nav mr-auto mt-2 mt-lg-0">
                    
                    <li class="footerTitle">Travel.State.Gov</li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel.html">Travel.State.Gov</a> </li>

                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/passports.html">U.S. Passports</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/international-travel.html">International Travel</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/us-visas.html">U.S. Visas</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/Intercountry-Adoption.html">Intercountry Adoptions</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/International-Parental-Child-Abduction.html">International Parental Child Abduction</a> </li>

                    <li class="footerTitle">Popular Links</li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel.html">Home</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/traveladvisories/traveladvisories.html">Travel Advisories</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/News/newsroom.html">Newsroom</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/about-us.html">About Us</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/contact-us.html">Contact Us</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://careers.state.gov/">Careers</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://www.usembassy.gov/">Find U.S. Embassies & Consulates</a> </li>
                    
                    <li class="footerTitle">Stay Connected</li>
                    <li class="nav-item footerLink">
                        <a class="socialMeadiaLink" href="https://blogs.state.gov/"> <i class="fa fa-star" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://www.facebook.com/travelgov/"> <i class="fa fa-facebook" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://www.flickr.com/photos/statephotos"> <i class="fa fa-flickr" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://twitter.com/TravelGov"> <i class="fa fa-twitter" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://www.youtube.com/user/statevideo"> <i class="fa fa-youtube" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://travel.state.gov/content/travel/en/rss.html"> <i class="fa fa-rss" aria-hidden="true"></i></a>
                    </li>

                    <li class="footerTitle">Legal Resources</li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/legal.html">Info for Lawyers and Judges</a>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/consularnotification.html">Info for U.S. Law Enforcement</a> </li>
                </ul>
            </div>
        </nav>
    </div><!-- /hamburger menu for portrait phones -->
    <!-- header -->
    <div class="opssHeader">
        <div class="container">
            <div class="opssHeaderBackground">
                <div class="row pl-sm-3 py-3">
                    <div class="stateLogoFrame ml-1 ml-md-2">
                        <a href="https://travel.state.gov/content/travel.html">
                            <img src="/Images/Seal_of_Department_of_state.svg" class="img-fluid" />
                        </a>
                    </div>
                    <a class="titleFrameLink" href="https://travel.state.gov/content/travel/en/passports.html">
                        <div class="titleFrame ml-2 ml-md-3 mt-2 mt-md-3 text-white">
                            <div class="h1 titleFrameLineOne">Travel.State.Gov</div>
                            <div class="titleFrameLineTwo">U.S. DEPARTMENT of STATE — BUREAU of CONSULAR AFFAIRS</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div><!-- /header -->
    
    <!-- Body -->
    <div id="theContainersContainer">
        <div class="container py-4">
            <h1>U.S. Passport Application Status</h1>
            <hr />
            




<form action="/Search" method="post"><input name="__RequestVerificationToken" type="hidden" value="JAjufYcvbx_SLI8DU8YLnFWjnnFrpe_lFBT9D9TMUrfAjU4T3DXsCxXiUbsujQq6geryEhdW0VZsZq6-4rTpyd2hziJQQcmjmL_SgOIRdAo1" />    <div class="row pt-2">
        <div class="col-md-6 col-lg-8">

            <div class="pb-4">Please enter the following information to check the status of your passport request.</div>

            <div class="form-group">
                <label class="col-form-label" for="LastName">Last Name</label>
                <input class="form-control col-sm-9 col-lg-6" data-val="true" data-val-length="The Last Name must be less than 40 characters long." data-val-length-max="40" data-val-regex="Invalid Last Name" data-val-regex-pattern="([a-zA-Z &#39;-]+)" data-val-required="The Last Name field is required." id="LastName" maxlength="40" name="LastName" placeholder="Last Name" type="text" value="" />
                <div class="hint-text"><ul style='margin-bottom:0'><li>Include suffixes (Jones III, Patton Jr, etc.)</li><li>Include hyphens as appropriate (Jackson-Smith)</li></ul></div>
                <span class="field-validation-valid" data-valmsg-for="LastName" data-valmsg-replace="true"></span>
            </div>

            <div class="form-group">
                <label class="col-form-label" for="DateOfBirth">Date of Birth</label>
                <input class="form-control col-sm-9 col-lg-6" data-val="true" data-val-length="The Date of Birth must be at least 6 characters long." data-val-length-max="100" data-val-length-min="6" data-val-regex="Invalid Date of Birth" data-val-regex-pattern="(\d{1,2}/\d{1,2}/\d{4})" data-val-required="The Date of Birth field is required." id="DateOfBirth" maxlength="10" name="DateOfBirth" placeholder="mm/dd/yyyy" type="text" value="" />  <!-- TODO, write JS to pick HTML5 Date control for Chrome --> <!-- FYI, changed to TextBoxFor because EditorFor does not accept HTML attributes as a parameter, and added type Date. There is a workaround in MVC 5.1 -->
                <span class="field-validation-valid" data-valmsg-for="DateOfBirth" data-valmsg-replace="true"></span>
            </div>

            <div class="form-group">
                <label class="col-form-label" for="Last4SSN">Social Security Number (Last 4 Digits)</label>
                <input class="form-control col-sm-9 col-lg-6" data-val="true" data-val-regex="SSN must be numeric" data-val-regex-pattern="^[0-9]*$" data-val-required="The Social Security Number (Last 4 Digits) field is required." id="Last4SSN" maxlength="4" name="Last4SSN" placeholder="Last 4 of SSN" type="text" value="" />
                <span class="field-validation-valid" data-valmsg-for="Last4SSN" data-valmsg-replace="true"></span>
            </div>

            


            <div class="form-group my-4">
                


  <div class="LBD_CaptchaDiv" id="SearchCaptcha_CaptchaDiv" style="width: 285px !important; height: 50px !important;"><!--
 --><div class="LBD_CaptchaImageDiv" id="SearchCaptcha_CaptchaImageDiv" style="width: 255px !important; height: 50px !important;"><!--
   --><a target="_blank" href="//captcha.com/captcha.html?asp.net" title="BotDetect CAPTCHA ASP.NET Form Validation" onclick="SearchCaptcha.OnHelpLinkClick(); return SearchCaptcha.FollowHelpLink;"><img class="LBD_CaptchaImage" id="SearchCaptcha_CaptchaImage" src="/BotDetectCaptcha.ashx?get=image&amp;c=SearchCaptcha&amp;t=7165baa81d7f4303ba1eb1f84a990064" alt="CAPTCHA" /></a><!-- --></div><!--
 --><div class="LBD_CaptchaIconsDiv" id="SearchCaptcha_CaptchaIconsDiv" style="width: 24px !important;"><!--
   --><a class="LBD_ReloadLink" id="SearchCaptcha_ReloadLink" href="#" onclick="SearchCaptcha.ReloadImage(); this.blur(); return false;" title="Change the CAPTCHA code"><img class="LBD_ReloadIcon" id="SearchCaptcha_ReloadIcon" src="/BotDetectCaptcha.ashx?get=ReloadIcon" alt="Change the CAPTCHA code" /></a><!--
   --><a class="LBD_SoundLink" id="SearchCaptcha_SoundLink" href="/BotDetectCaptcha.ashx?get=sound&amp;c=SearchCaptcha&amp;t=7165baa81d7f4303ba1eb1f84a990064&amp;s=XhU9JcKdTK2oXi3EmN4wxElWvX2v%2f2NWpE3qCoixmxB7gf5xuJVndKL%2bFMmgpXR6kMsZKV%2fKWOuSzgLLN99rbQ%3d%3d" onclick="SearchCaptcha.PlaySound(); this.blur(); return false;" title="Speak the CAPTCHA code"><img class="LBD_SoundIcon" id="SearchCaptcha_SoundIcon" src="/BotDetectCaptcha.ashx?get=SoundIcon" alt="Speak the CAPTCHA code" /></a><!--
   --><div class="LBD_Placeholder" id="SearchCaptcha_AudioPlaceholder">&nbsp;</div><!--
 --></div>
    <script src="/BotDetectCaptcha.ashx?get=clientScriptInclude" type="text/javascript"></script>
    <script type="text/javascript">
    //<![CDATA[
      BotDetect.Init('SearchCaptcha', '7165baa81d7f4303ba1eb1f84a990064', 'CaptchaCode', true, true, true, true, 1200, 7200, 0, true);
    //]]>
    </script>
    <script type="text/javascript">
    //<![CDATA[
      try{(function(){var bdrsn = document.createElement('script'); bdrsn.type = 'text/javascript'; bdrsn.async = true; bdrsn.src = document.location.protocol + '//remote.captcha.com/include.js?i=ATABMAExATMBMAIxOBRJpk6AR3yR6dEhn_DNoPlobx4z7w'; var fsn = document.getElementsByTagName('script')[0]; fsn.parentNode.insertBefore(bdrsn, fsn);})();} catch(err){}
    //]]>
    </script>
    <input type="hidden" name="LBD_VCID_SearchCaptcha" id="LBD_VCID_SearchCaptcha" value="7165baa81d7f4303ba1eb1f84a990064" />
    <input type="hidden" name="LBD_BackWorkaround_SearchCaptcha" id="LBD_BackWorkaround_SearchCaptcha" value="0" />
  </div>


                <label class="col-form-label" for="CaptchaCode">Retype the Code</label>
                <input class="form-control col-sm-9 col-lg-6 captchaVal" id="CaptchaCode" name="CaptchaCode" type="text" value="" />
                <span class="field-validation-valid" data-valmsg-for="CaptchaCode" data-valmsg-replace="true"></span>
            </div>
        
            <div class="p-2 col-lg-6 col-sm-9 mb-4" style="font-size:.75rem;background-color:#DDDDDD">
                <p class="font-weight-bold">PRIVACY ACT STATEMENT</p>
                <p><span class="font-weight-bold">AUTHORITIES</span>: Collection of this information is authorized by 22 U.S.C. 211a et seq.; 8 U.S.C. 1104; 26 U.S.C. 6039E; 22 U.S.C. 2714a(f); Section 236 of the Admiral James W. Nance and Meg Donovan Foreign Relations Authorization Act, Fiscal Years 2000 and 2001; Executive Order 11295 (August 5, 1966); and 22 C.F.R. parts 50 and 51.</p>
                <p><span class="font-weight-bold">PURPOSE</span>: This information is being collected in order to access and locate the status of your passport application.  The last four digits of your Social Security number are used to locate the status of your application.</p>
                <p><span class="font-weight-bold">ROUTINE USES</span>: This information may be disclosed to another domestic government agency, a private contractor, a foreign government agency, or to a private person or private employer in accordance with certain approved routine uses. The last four digits of your Social Security number are stored temporarily and are not used in any upstream applications.  More information on the Routine Uses for the system can be found in System of Records Notices State-05, Overseas Citizens Services Records and Other Overseas Records, State-26, Passport Records, and the Department’s Prefatory Statement of Routine Uses.</p>
                <p class="mb-0"><span class="font-weight-bold">DISCLOSURE</span>: Providing information on this form is voluntary.  The information must be provided to receive the status of your passport application. Failure to provide the last four digits of your Social Security number will result in the system not being able to display the status of your application.</p>
            </div>




            <div class="form-group">
                <button type="submit" class="btn btn-block col-sm-9 col-lg-6" onclick="gtag('event', 'SubmitButton_Click', { 'Action': 'Search'});">Submit</button>
            </div>

            <hr class="hideMedAndUp" />
        </div>

        

<div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header">Need Help?</div>
            <div class="card-block">
                <div class="card-title"></div>
                <div class="card-text">
                    If you are traveling internationally within 14 days or need a foreign visa within 28 days, go to our <a href="https://travel.state.gov/content/travel/en/contact-us/passports.html" target="_blank">Contact Us webpage</a> for information on how to call us.
                </div>
                <div class="card-title"></div>
                <div class="card-text">If you are reporting technical issues with this status tool, email <a href="mailto:<EMAIL>" target="_blank"><EMAIL>.</a></div>
                <div class="card-title"></div>
                <div class="card-text">If you applied for a special issuance passport, contact your federal travel office or check <a href="https://passportstatus.state.gov/" target="_blank">passportstatus.state.gov</a> on a U.S. government computer or device.</div>
            </div>
        </div>

</div>


    </div>
</form>
        </div>
    </div>
    <!-- /Body -->
    
    <!-- footer -->
    <div class="opssFooter py-2 hidden-xs-down">
        <!-- displayed in mobile landscape and up -->
        <div class="container">
            <div class="row">
                <div class="col-sm-4">
                    <div class="d-flex flex-column">
                        <div class="footerTitle">Travel.State.Gov</div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel.html">Travel.State.Gov</a> </div>

                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/passports.html">U.S. Passports</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/international-travel.html">International Travel</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/us-visas.html">U.S. Visas</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/Intercountry-Adoption.html">Intercountry Adoptions</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/International-Parental-Child-Abduction.html">International Parental Child Abduction</a> </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="d-flex flex-column">
                        <div class="footerTitle">Popular Links</div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel.html">Home</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/traveladvisories/traveladvisories.html">Travel Advisories</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/News/newsroom.html">Newsroom</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/about-us.html">About Us</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/contact-us.html">Contact Us</a> </div>
                        <div class="footerLink"> <a href="https://careers.state.gov/">Careers</a> </div>
                        <div class="footerLink"> <a href="https://www.usembassy.gov/">Find U.S. Embassies & Consulates</a> </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="d-flex flex-column">
                        <div class="footerTitle">Stay Connected</div>
                        <div class="footerLink">
                            <a class="socialMeadiaLink" href="https://blogs.state.gov/"> <i class="fa fa-star" aria-hidden="true"></i> </a>
                            <a class="socialMeadiaLink" href="https://www.facebook.com/travelgov/"> <i class="fa fa-facebook" aria-hidden="true"></i> </a>
                            <a class="socialMeadiaLink" href="https://www.flickr.com/photos/statephotos"> <i class="fa fa-flickr" aria-hidden="true"></i> </a>
                            <a class="socialMeadiaLink" href="https://twitter.com/TravelGov"><i class="fa fa-twitter" aria-hidden="true"></i></a>
                            <a class="socialMeadiaLink" href="https://www.youtube.com/user/statevideo"> <i class="fa fa-youtube" aria-hidden="true"></i> </a>
                            <a class="socialMeadiaLink" href="https://travel.state.gov/content/travel/en/rss.html"><i class="fa fa-rss" aria-hidden="true"></i></a>
                        </div>
                        <div class="footerTitle legalResourceTitle">Legal Resources</div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/legal.html">Info for Lawyers and Judges</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/consularnotification.html">Info for U.S. Law Enforcement</a> </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!-- /footer -->
    <!-- tsgdisclaimer -->
    <div class="tsgDisclaimer text-white py-2">
        <div class="container">
            <div class="row pl-3">
                <div class="buttonFlagFrame pt-1">
                    <img src="/Images/Flag_of_the_United_States.svg" class="img-fluid" style="width:100px;" />  <!-- bug with IE 11 and svg format - need to use width 100 instead of img-fluid -->
                </div>
                <div class="tsgDisclaimerLinkFrame ml-1 ml-md-3">
                    <div>
                        <a href="https://www.state.gov/privacy-policy/">Privacy</a> | <a href="https://travel.state.gov/content/travel/en/copyright-disclaimer.html">Copyright and Disclaimer</a> 
                            | <a href="https://foia.state.gov/">FOIA</a>  |  <a href="https://www.state.gov/s/ocr/c11528.htm">No Fear Act Data</a> 
                            | <a href="https://www.stateoig.gov/">Office of the Inspector</a> | <a href="https://www.usa.gov/">USA.gov</a> 
                            | <a href="https://gobierno.usa.gov/">GobiernoUSA.gov</a>
                    </div>
                    <div>
                        This site is managed by the <a href="https://www.state.gov/">U.S. Department of State</a>.
                        External links to other Internet sites should not be construed as an endorsement of the views or privacy policies contained therein.
                    </div>
                </div>
            </div>
        </div>
    </div><!-- /tsgdisclaimer -->
    
    <script src="/Scripts/jquery-3.7.1.min.js"></script>
    <script src="/Scripts/jquery.validate.min.js"></script>
    <script src="/Scripts/jquery.validate.unobtrusive.min.js"></script>
    <script src="/Scripts/jquery-ajax-unobtrusive.js"></script>
    <script src="/Scripts/tether.min.js"></script>
    <script src="/Scripts/bootstrap.min.js"></script>

    <script language="javascript" type="text/javascript">
        $(document).ready(function () {
            try {
                $("input[type='text']").each(function () {
                    $(this).attr("autocomplete", "off");
                });
            }
            catch (e)
            { }
        });

    </script>

    <script>
        //Put our input DOM element into a jQuery Object
        var $jqDate = jQuery('input[name="DateOfBirth"]');

        //Bind keyup/keydown to the input
        $jqDate.on('keydown keyup', function (e) {

            //To accomdate for backspacing, we detect which key was pressed - if backspace, do nothing:
            if (e.which !== 8) {
                var numChars = $jqDate.val().length;
                if (numChars === 2 || numChars === 5) {
                    var thisVal = $jqDate.val();
                    thisVal += '/';
                    $jqDate.val(thisVal);
                }
            }
        });
    </script>

    
</body>
</html>
