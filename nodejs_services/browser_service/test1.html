
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>U.S. Passport Application Status</title><script>window['adrum-start-time'] = new Date().getTime(); var appKey='EUM-AAB-AUM';</script><script type="text/javascript" src="https://eum.state.gov/appd.21.7.0.3493.js"></script>
    <link href="https://fonts.googleapis.com/css?family=Oswald:500|Roboto+Condensed:400,700" rel="stylesheet">
    <link href="/Content/font-awesome.min.css" rel="stylesheet" />
    <link href="/Content/bootstrap.min.css" rel="stylesheet" />
    <link href="/Content/Site.css" rel="stylesheet" />
    <script src="/Scripts/modernizr-2.6.2.js"></script>



        <!-- Federated UA Script for Production -->
        <script async type="text/javascript" language="javascript" id="_fed_an_ua_tag"
                src="https://dap.digitalgov.gov/Universal-Federated-Analytics-Min.js?agency=DOS&subagency=CA"></script>

    <!-- OPSS Google Tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-TVB87S3CZT"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());
        gtag('config', 'G-TVB87S3CZT');
    </script>

</head>
<body>
    
    <!--Hamburger Menu-->
    <div class="opssHamburgerMenu">
        <nav class="navbar navbar-toggleable navbar-inverse d-sm-none" style="background-color:#06284C;">
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarTogglerDemo01" aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarTogglerDemo01">
                <ul class="navbar-nav mr-auto mt-2 mt-lg-0">
                    
                    <li class="footerTitle">Travel.State.Gov</li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel.html">Travel.State.Gov</a> </li>

                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/passports.html">U.S. Passports</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/international-travel.html">International Travel</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/us-visas.html">U.S. Visas</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/Intercountry-Adoption.html">Intercountry Adoptions</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/International-Parental-Child-Abduction.html">International Parental Child Abduction</a> </li>

                    <li class="footerTitle">Popular Links</li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel.html">Home</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/traveladvisories/traveladvisories.html">Travel Advisories</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/News/newsroom.html">Newsroom</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/about-us.html">About Us</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/contact-us.html">Contact Us</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://careers.state.gov/">Careers</a> </li>
                    <li class="nav-item"> <a class="footerLink" href="https://www.usembassy.gov/">Find U.S. Embassies & Consulates</a> </li>
                    
                    <li class="footerTitle">Stay Connected</li>
                    <li class="nav-item footerLink">
                        <a class="socialMeadiaLink" href="https://blogs.state.gov/"> <i class="fa fa-star" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://www.facebook.com/travelgov/"> <i class="fa fa-facebook" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://www.flickr.com/photos/statephotos"> <i class="fa fa-flickr" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://twitter.com/TravelGov"> <i class="fa fa-twitter" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://www.youtube.com/user/statevideo"> <i class="fa fa-youtube" aria-hidden="true"></i> </a>
                        <a class="socialMeadiaLink" href="https://travel.state.gov/content/travel/en/rss.html"> <i class="fa fa-rss" aria-hidden="true"></i></a>
                    </li>

                    <li class="footerTitle">Legal Resources</li>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/legal.html">Info for Lawyers and Judges</a>
                    <li class="nav-item"> <a class="footerLink" href="https://travel.state.gov/content/travel/en/consularnotification.html">Info for U.S. Law Enforcement</a> </li>
                </ul>
            </div>
        </nav>
    </div><!-- /hamburger menu for portrait phones -->
    <!-- header -->
    <div class="opssHeader">
        <div class="container">
            <div class="opssHeaderBackground">
                <div class="row pl-sm-3 py-3">
                    <div class="stateLogoFrame ml-1 ml-md-2">
                        <a href="https://travel.state.gov/content/travel.html">
                            <img src="/Images/Seal_of_Department_of_state.svg" class="img-fluid" />
                        </a>
                    </div>
                    <a class="titleFrameLink" href="https://travel.state.gov/content/travel/en/passports.html">
                        <div class="titleFrame ml-2 ml-md-3 mt-2 mt-md-3 text-white">
                            <div class="h1 titleFrameLineOne">Travel.State.Gov</div>
                            <div class="titleFrameLineTwo">U.S. DEPARTMENT of STATE — BUREAU of CONSULAR AFFAIRS</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div><!-- /header -->
    
    <!-- Body -->
    <div id="theContainersContainer">
        <div class="container py-4">
            <h1>U.S. Passport Application Status</h1>
            <hr />
            


<form action="/" method="post"><input name="__RequestVerificationToken" type="hidden" value="6JrcGYpb9tHNN4kbe7zynD80jxR49JKzwuT0ijYnuUN7dEK55aVR6usSqYyX0VqU3NUjpf9-hC9wX-IFE_Mkn3tob6riFflcuS2uuleDjPY1" />    <div class="row pt-2">
        <div class="col-md-6 col-lg-8">
            <div class="mb-4 fraudAndAbuseLinkLine">Before you may enter your personal information to get a passport application status check, you must review the Department of State's <a target="_blank" href="https://www.state.gov/privacy-policy/">Privacy and Computer Fraud and Abuse Acts Notices and Disclaimers.</a> </div>  
            <div class="mb-4">This link will open up a new page. Once you have read the notice and disclaimer, close that window and click on the box below to indicate you have read them. You will then be able to input your last name, date of birth, and last four digits of your Social Security Number.</div>
            <div class="mb-4 form-check offset-lg-1">
                <input data-val="true" data-val-equalto="You must click on the box to indicate that you have read the Privacy and Computer Fraud and Abuse Acts Notices and Disclaimers in order to continue to the next page." data-val-equalto-other="*.isTrue" data-val-required="The I have read the Privacy and Computer Fraud and Abuse Acts Notices and Disclaimers. field is required." id="FraudAndAbuseCheckBox" name="FraudAndAbuseCheckBox" type="checkbox" value="true" /><input name="FraudAndAbuseCheckBox" type="hidden" value="false" />
                <label for="FraudAndAbuseCheckBox">I have read the Privacy and Computer Fraud and Abuse Acts Notices and Disclaimers.</label>
                <br />
                <span class="field-validation-valid" data-valmsg-for="FraudAndAbuseCheckBox" data-valmsg-replace="true"></span>
            </div>

            <div class="row">
                <div class="col-lg-6 mx-auto mb-4">
                    <button type="submit" class="btn btn-block" onclick="gtag('event', 'SubmitButton_Click', { 'Action': 'ConfirmDisclaimer' });">Submit</button>
                </div>
            </div>

            <hr class="hideMedAndUp" />
        </div>



<div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header">Need Help?</div>
            <div class="card-block">
                <div class="card-title"></div>
                <div class="card-text">
                    If you are traveling internationally within 14 days or need a foreign visa within 28 days, go to our <a href="https://travel.state.gov/content/travel/en/contact-us/passports.html" target="_blank">Contact Us webpage</a> for information on how to call us.
                </div>
                <div class="card-title"></div>
                <div class="card-text">If you are reporting technical issues with this status tool, email <a href="mailto:<EMAIL>" target="_blank"><EMAIL>.</a></div>
                <div class="card-title"></div>
                <div class="card-text">If you applied for a special issuance passport, contact your federal travel office or check <a href="https://passportstatus.state.gov/" target="_blank">passportstatus.state.gov</a> on a U.S. government computer or device.</div>
            </div>
        </div>

</div>

    </div>
</form>
        </div>
    </div>
    <!-- /Body -->
    
    <!-- footer -->
    <div class="opssFooter py-2 hidden-xs-down">
        <!-- displayed in mobile landscape and up -->
        <div class="container">
            <div class="row">
                <div class="col-sm-4">
                    <div class="d-flex flex-column">
                        <div class="footerTitle">Travel.State.Gov</div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel.html">Travel.State.Gov</a> </div>

                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/passports.html">U.S. Passports</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/international-travel.html">International Travel</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/us-visas.html">U.S. Visas</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/Intercountry-Adoption.html">Intercountry Adoptions</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/International-Parental-Child-Abduction.html">International Parental Child Abduction</a> </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="d-flex flex-column">
                        <div class="footerTitle">Popular Links</div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel.html">Home</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/traveladvisories/traveladvisories.html">Travel Advisories</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/News/newsroom.html">Newsroom</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/about-us.html">About Us</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/contact-us.html">Contact Us</a> </div>
                        <div class="footerLink"> <a href="https://careers.state.gov/">Careers</a> </div>
                        <div class="footerLink"> <a href="https://www.usembassy.gov/">Find U.S. Embassies & Consulates</a> </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="d-flex flex-column">
                        <div class="footerTitle">Stay Connected</div>
                        <div class="footerLink">
                            <a class="socialMeadiaLink" href="https://blogs.state.gov/"> <i class="fa fa-star" aria-hidden="true"></i> </a>
                            <a class="socialMeadiaLink" href="https://www.facebook.com/travelgov/"> <i class="fa fa-facebook" aria-hidden="true"></i> </a>
                            <a class="socialMeadiaLink" href="https://www.flickr.com/photos/statephotos"> <i class="fa fa-flickr" aria-hidden="true"></i> </a>
                            <a class="socialMeadiaLink" href="https://twitter.com/TravelGov"><i class="fa fa-twitter" aria-hidden="true"></i></a>
                            <a class="socialMeadiaLink" href="https://www.youtube.com/user/statevideo"> <i class="fa fa-youtube" aria-hidden="true"></i> </a>
                            <a class="socialMeadiaLink" href="https://travel.state.gov/content/travel/en/rss.html"><i class="fa fa-rss" aria-hidden="true"></i></a>
                        </div>
                        <div class="footerTitle legalResourceTitle">Legal Resources</div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/legal.html">Info for Lawyers and Judges</a> </div>
                        <div class="footerLink"> <a href="https://travel.state.gov/content/travel/en/consularnotification.html">Info for U.S. Law Enforcement</a> </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!-- /footer -->
    <!-- tsgdisclaimer -->
    <div class="tsgDisclaimer text-white py-2">
        <div class="container">
            <div class="row pl-3">
                <div class="buttonFlagFrame pt-1">
                    <img src="/Images/Flag_of_the_United_States.svg" class="img-fluid" style="width:100px;" />  <!-- bug with IE 11 and svg format - need to use width 100 instead of img-fluid -->
                </div>
                <div class="tsgDisclaimerLinkFrame ml-1 ml-md-3">
                    <div>
                        <a href="https://www.state.gov/privacy-policy/">Privacy</a> | <a href="https://travel.state.gov/content/travel/en/copyright-disclaimer.html">Copyright and Disclaimer</a> 
                            | <a href="https://foia.state.gov/">FOIA</a>  |  <a href="https://www.state.gov/s/ocr/c11528.htm">No Fear Act Data</a> 
                            | <a href="https://www.stateoig.gov/">Office of the Inspector</a> | <a href="https://www.usa.gov/">USA.gov</a> 
                            | <a href="https://gobierno.usa.gov/">GobiernoUSA.gov</a>
                    </div>
                    <div>
                        This site is managed by the <a href="https://www.state.gov/">U.S. Department of State</a>.
                        External links to other Internet sites should not be construed as an endorsement of the views or privacy policies contained therein.
                    </div>
                </div>
            </div>
        </div>
    </div><!-- /tsgdisclaimer -->
    
    <script src="/Scripts/jquery-3.7.1.min.js"></script>
    <script src="/Scripts/jquery.validate.min.js"></script>
    <script src="/Scripts/jquery.validate.unobtrusive.min.js"></script>
    <script src="/Scripts/jquery-ajax-unobtrusive.js"></script>
    <script src="/Scripts/tether.min.js"></script>
    <script src="/Scripts/bootstrap.min.js"></script>

    <script language="javascript" type="text/javascript">
        $(document).ready(function () {
            try {
                $("input[type='text']").each(function () {
                    $(this).attr("autocomplete", "off");
                });
            }
            catch (e)
            { }
        });

    </script>

    <script>
        //Put our input DOM element into a jQuery Object
        var $jqDate = jQuery('input[name="DateOfBirth"]');

        //Bind keyup/keydown to the input
        $jqDate.on('keydown keyup', function (e) {

            //To accomdate for backspacing, we detect which key was pressed - if backspace, do nothing:
            if (e.which !== 8) {
                var numChars = $jqDate.val().length;
                if (numChars === 2 || numChars === 5) {
                    var thisVal = $jqDate.val();
                    thisVal += '/';
                    $jqDate.val(thisVal);
                }
            }
        });
    </script>

    
</body>
</html>
