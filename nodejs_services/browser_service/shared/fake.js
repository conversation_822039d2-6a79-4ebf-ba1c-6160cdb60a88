const axios = require('axios');
const random_address = async (query) => {
    const resp = await axios.get(`https://nominatim.openstreetmap.org/search?q=${query}&format=json&addressdetails=1`, {
        headers: {
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36'
        }
    })
    const item = resp.data[0].address
    const address = `${item.house_number} ${item.road}, ${item.city}, ${item.state} ${item.postcode}, ${item.country}`
    return {
        ...item,
        address,
    }
}

module.exports = {
    random_address,
}