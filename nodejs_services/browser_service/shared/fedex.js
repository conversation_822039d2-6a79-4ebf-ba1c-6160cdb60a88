const iso = require('iso-3166-1');
const axios = require("axios");
const fs = require("fs");
const moment = require("moment");
const uuid = require("uuid");
const FormData = require('form-data');

const CONFIG = {
    stag: {
        BASE_URL: 'https://apis-sandbox.fedex.com',
        ACCOUNT_NUMBER: '*********',
        CLIENT_ID: 'l74612e656f22a4b1a99c39b99bc2f2e54',
        CLIENT_SECRET: '08e535a5393548e2a990f5a89406c561',
    },
    prod: {
        BASE_URL: 'https://apis.fedex.com',
        ACCOUNT_NUMBER: '*********',
        CLIENT_ID: 'l7bac1ff37feec4fbbb09ce593515433f3',
        CLIENT_SECRET: '21e82c0d-d3f8-4676-9344-ed14cbb2ad28',
    }
}[process.env.NODE_ENV || 'stag']

const login = async () => {
    try {
        const resp = await axios.post(`${CONFIG.BASE_URL}/oauth/token`, {
            grant_type: 'client_credentials',
            client_id: CONFIG.CLIENT_ID,
            client_secret: CONFIG.CLIENT_SECRET
        }, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
        console.log(resp.data)
        return resp.data.access_token
    } catch (error) {
        console.log(error)
    }

}

const search_address = async ({ }) => {
    const access_token = await login()
    const data = {
        "location": {
            "address": {
                "city": "Paris",
                "countryCode": "FR"
            }
        }
    };
    try {
        const response = await axios.post(`${CONFIG.BASE_URL}/location/v1/locations`, data, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${access_token}`,
                'X-locale': 'en_US'
            }
        });

        console.log('Shipment created successfully:', response.data);
        return response.data
    } catch (error) {
        console.log('Error creating shipment:', error.response ? error.response.data : error.message);
    }
}
// search_address({})

const create_shipemnt = async ({
    shipper_name = 'KFC',
    shipper_phone = '**********',
    shipper_address = '552 E Santa Clara St',
    shipper_city = 'San Jose',
    shipper_state = 'CA',
    shipper_postal_code = '95112',
    shipper_country = 'US',
    recipient_name = 'KFC',
    recipient_phone = '**********',
    recipient_address = '2210 Texas Parkway',
    recipient_city = 'Missouri City',
    recipient_state = 'TX',
    recipient_postal_code = '77489',
    recipient_country = 'US',
}) => {
    const access_token = await login()
    const data = {
        mergeLabelDocOption: 'LABELS_ONLY',
        requestedShipment: {
            shipper: {
                contact: {
                    personName: shipper_name,
                    phoneNumber: shipper_phone
                },
                address: {
                    streetLines: [shipper_address],
                    city: shipper_city,
                    stateOrProvinceCode: shipper_state,
                    postalCode: shipper_postal_code,
                    countryCode: shipper_country
                }
            },
            recipients: [
                {
                    contact: {
                        personName: recipient_name,
                        phoneNumber: recipient_phone
                    },
                    address: {
                        streetLines: [recipient_address],
                        city: recipient_city,
                        stateOrProvinceCode: recipient_state,
                        postalCode: recipient_postal_code,
                        countryCode: recipient_country
                    }
                }
            ],
            pickupType: 'USE_SCHEDULED_PICKUP',
            serviceType: 'STANDARD_OVERNIGHT',
            packagingType: 'FEDEX_ENVELOPE',
            totalWeight: 1,
            shippingChargesPayment: {
                paymentType: 'SENDER',
                payor: {
                    responsibleParty: {
                        accountNumber: {
                            value: CONFIG.ACCOUNT_NUMBER
                        }
                    }
                }
            },
            labelSpecification: {
                imageType: 'PDF',
                labelStockType: 'PAPER_85X11_TOP_HALF_LABEL'
            },
            requestedPackageLineItems: [
                {
                    sequenceNumber: 1,
                    weight: {
                        value: 1,
                        units: 'LB'
                    }
                }
            ],
            customsClearanceDetail: {
                totalCustomsValue: {
                    amount: 50,
                    currency: "USD"
                },
            }
        },
        labelResponseOptions: "LABEL",
        accountNumber: {
            value: CONFIG.ACCOUNT_NUMBER,
        },

    };

    try {
        const response = await axios.post(`${CONFIG.BASE_URL}/ship/v1/shipments`, data, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${access_token}`,
                'X-locale': 'en_US'
            }
        });

        console.log('Shipment created successfully:', response.data);
        const base64 = response.data.output.transactionShipments[0].pieceResponses[0].packageDocuments[0].encodedLabel
        const buffer = Buffer.from(base64, 'base64')
        fs.writeFileSync('label.png', buffer)
        return buffer
        // Handle the response here (e.g., save tracking number, print label)
    } catch (error) {
        console.log('Error creating shipment:', error.response ? error.response.data : error.message);
    }
}

const create_shipment_internaltional = async ({
    shipper_name = 'KFC',
    shipper_phone = '**********',
    shipper_address = '552 E Santa Clara St',
    shipper_city = 'San Jose',
    shipper_state = 'CA',
    shipper_postal_code = '95112',
    shipper_country = 'US',
    recipient_name = 'Jean Dupont',
    recipient_phone = '0123456789',
    recipient_address = '123 Rue de Paris',
    recipient_city = 'Paris',
    recipient_postal_code = '75001',
    recipient_country = 'FR',
}) => {
    const access_token = await login()
    const data = {
        mergeLabelDocOption: 'LABELS_ONLY',
        requestedShipment: {
            shipper: {
                contact: {
                    personName: shipper_name,
                    phoneNumber: shipper_phone
                },
                address: {
                    streetLines: [shipper_address],
                    city: shipper_city,
                    stateOrProvinceCode: shipper_state,
                    postalCode: shipper_postal_code,
                    countryCode: shipper_country
                }
            },
            recipients: [
                {
                    contact: {
                        personName: recipient_name,
                        phoneNumber: recipient_phone
                    },
                    address: {
                        streetLines: [recipient_address],
                        city: recipient_city,
                        postalCode: recipient_postal_code,
                        countryCode: recipient_country
                    }
                }
            ],
            pickupType: 'USE_SCHEDULED_PICKUP',
            serviceType: 'INTERNATIONAL_PRIORITY',
            packagingType: 'FEDEX_ENVELOPE',
            totalWeight: 1,
            shippingChargesPayment: {
                paymentType: 'SENDER',
                payor: {
                    responsibleParty: {
                        accountNumber: {
                            value: CONFIG.ACCOUNT_NUMBER
                        }
                    }
                }
            },
            labelSpecification: {
                imageType: 'PDF',
                labelStockType: 'PAPER_85X11_TOP_HALF_LABEL'
            },
            customsClearanceDetail: {
                dutiesPayment: {
                    paymentType: 'SENDER'
                },
                commodities: [
                    {
                        numberOfPieces: 1,
                        description: 'Gift',
                        countryOfManufacture: 'US',
                        weight: {
                            units: 'LB',
                            value: 1
                        },
                        quantity: 1,
                        quantityUnits: 'EA',
                        unitPrice: {
                            amount: 50,
                            currency: 'USD'
                        },
                        customsValue: {
                            amount: 50,
                            currency: 'USD'
                        }
                    }
                ]
            },
            requestedPackageLineItems: [
                {
                    sequenceNumber: 1,
                    weight: {
                        value: 1,
                        units: 'LB'
                    }
                }
            ]
        },
        labelResponseOptions: "LABEL",
        accountNumber: {
            value: CONFIG.ACCOUNT_NUMBER,
        },
    };

    try {
        const response = await axios.post(`${CONFIG.BASE_URL}/ship/v1/shipments`, data, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${access_token}`,
                'X-locale': 'en_US'
            }
        });

        console.log('Shipment created successfully:', response.data);
        const base64 = response.data.output.transactionShipments[0].pieceResponses[0].packageDocuments[0].encodedLabel
        const buffer = Buffer.from(base64, 'base64')
        fs.writeFileSync('label.pdf', buffer)
        return buffer
    } catch (error) {
        console.log('Error creating shipment:', error.response ? error.response.data : error.message);
    }
}

module.exports = { create_shipemnt, create_shipment_internaltional }