var AWS = require('aws-sdk')
var fs = require('fs-extra')
var _ = require('lodash')
const axios = require('axios')
const s3ParseUrl = require('s3-url-parser');
const FormData = require('form-data');
const archiver = require('archiver');
var fill_pdf = require('./pdffiller');
const moment = require('moment');
var s3 = new AWS.S3()
const slugify = require('slugify');
const { createReport } = require('docx-templates');
const tmp = require('tmp');
const { exec } = require('child_process');
const iso = require('iso-3166-1');
const { country_custom_names, country_iso3_to_iso2 } = require('./data_mapping');
const { PDFDocument, StandardFonts } = require('pdf-lib');
const fontkit = require('@pdf-lib/fontkit');
exports.create_folder = (dir) => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, '0777')
    }
    return dir
}

exports.remove_folder = function (dir) {
    if (fs.existsSync(dir)) {
        fs.removeSync(dir)
    }
    return dir
}

exports.s3_download = async (bucket, key, local_dest) => {
    console.log(bucket, key)
    if (typeof local_dest == 'undefined') {
        local_dest = key
    }

    let params = {
        Bucket: bucket,
        Key: key
    }


    const data = await s3.getObject(params).promise();
    fs.writeFileSync(local_dest, data.Body);
}

exports.s3_download_to_buff = async (bucket, key) => {
    console.log(bucket, key)
    if (typeof local_dest == 'undefined') {
        local_dest = key
    }

    let params = {
        Bucket: bucket,
        Key: key
    }


    const data = await s3.getObject(params).promise();
    return data.Body
}

exports.s3_upload = (bucket, key, local_dest) => {
    return new Promise((resolve, reject) => {
        let extn = local_dest.split('.').pop();
        let contentType = 'application/octet-stream';
        if (extn == 'pdf') contentType = "application/pdf";
        if (extn == 'html') contentType = "text/html";
        if (extn == 'css') contentType = "text/css";
        if (extn == 'js') contentType = "application/javascript";
        if (extn == 'docx') contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        if (extn == 'png' || extn == 'jpg' || extn == 'gif') contentType = "image/" + extn;

        fs.readFile(local_dest, (err) => {
            if (err) throw err
            const params = {
                Bucket: bucket,
                Key: key,
                Body: fs.readFileSync(local_dest),
                ContentType: contentType
            }
            s3.upload(params, function (error, data) {
                if (error) return reject(error)
                console.log(`File uploaded successfully at ${data.Location}`)
                return resolve()
            })
        })
    })
}


exports.update_packer_status = (token, url, body) => {
    console.log(token)
    console.log(url)
    console.log(JSON.stringify(body, null, 2))
    return requestHaveBody("POST", token, url, body)
}

const requestHaveBody = async (method, token, uri, body) => {
    try {
        const resp = await axios({
            method,
            url: uri,
            headers: {
                'x-ad-token': token,
                'Content-Type': 'application/json',
            },
            data: body
        })
        return resp.data
    } catch (e) {
        console.log(e)
    }
}


exports.import_photo_to_application_form = async (photo_buff, pdf_buff, { x, y, width, height }) => {
    try {
        const pdfDoc = await PDFDocument.load(pdf_buff, { ignoreEncryption: true });

        const image = await pdfDoc.embedJpg(photo_buff);

        const page = pdfDoc.getPage(0);
        page.drawImage(image, {
            x,
            y,
            width,
            height,
        });

        const modifiedPdfBytes = await pdfDoc.save();
        return modifiedPdfBytes
    } catch (error) {
        console.log(error)
        throw 'PHOTO_WRITE_FAILED'
    }
}


exports.s3_upload_buff = (bucket, key, buffer) => {
    return new Promise((resolve, reject) => {
        const params = {
            Bucket: bucket,
            Key: key,
            Body: buffer,
            ACL: 'public-read'
        }
        s3.upload(params, function (error, data) {
            if (error) return reject(error)
            console.log(`File uploaded successfully at ${data.Location}`)
            return resolve(data.Location)
        })
    })
}


exports.prepareDataByVisaPods = (inputPods) => {
    let results = {};

    const processInputPod = (inputPod) => {
        const { id, value, option_choice } = inputPod;

        if (value?.fe != null) {
            results[id] = value.fe;
            if (option_choice && Object.keys(option_choice)?.length > 0)
                processOptionChoice(option_choice[value?.fe]);
        }
        if (inputPod.type == 'array') {
            if (_.isArray(value?.fe)) {
                for (const choice of Array(value?.fe ?? [])) {
                    if (option_choice && option_choice?.[choice])
                        processOptionChoice(option_choice[choice]);
                }
            }
        }
    };

    const processOptionChoice = (optionChoice) => {
        if (optionChoice) {
            for (const inputPod of optionChoice) {
                processInputPod(inputPod);
            }
        }
    };

    for (const inputPod of inputPods) {
        processInputPod(inputPod);
    }

    return results;
};


exports.capitalize = (str) => {
    if (typeof str !== 'string') return ''
    str = str.toLowerCase()
    return str.replace(/\w\S*/g, function (txt) { return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase(); });
}

exports.s3_url_to_bucket_key = (url) => {
    let photo_image = url?.split('?')[0]
    if (!photo_image.startsWith('http')) {
        const bucket = photo_image.split('/')[0]
        const key = photo_image.split('/').slice(1).join('/')
        if (bucket && key) {
            return { bucket, key }
        }
    }
    return s3ParseUrl(photo_image)
}

exports.get_order_detail = async (order_id) => {
    const api_token = JSON.parse(process.env.ad_api_token)
    const api_base = JSON.parse(process.env.ad_endpoint).api_base_url
    const url = `${api_base}/v1/pkg/internal/orders/${order_id}`

    return requestHaveBody("GET", api_token?.token, url)
}

exports.get_order_task = async (order_id, task_id) => {
    const api_token = JSON.parse(process.env.ad_api_token)
    const api_base = JSON.parse(process.env.ad_endpoint).api_base_url
    const url = `${api_base}/v1/pkg/internal/orders/${order_id}/tasks/${task_id}`

    return requestHaveBody("GET", api_token?.token, url)
}

exports.get_order_task_pod_data = async (order_id, task_id) => {
    const order_resp = await this.get_order_detail(order_id)
    if (!task_id) {
        task_id = order_resp.data.tasks[0].id
    }
    const task_resp = await this.get_order_task(order_id, task_id)
    return {
        order: order_resp.data,
        task: task_resp.data,
        pod_data: {
            ...order_resp.data.input_pod_values,
            ...task_resp.data.input_pod_values
        }
    }
}

exports.create_fastlane_order_from_visa_order = async (order_id) => {
    const api_token = JSON.parse(process.env.ad_api_token)
    const api_base = JSON.parse(process.env.ad_endpoint).api_base_url
    const url = `${api_base}/v1/pkg/internal/orders/${order_id}/create_fastlane_orders`

    return requestHaveBody("POST", api_token?.token, url)
}


let ORDER_NOTIF_ZALO_GROUP = 'https://zalo.me/g/axudwf575';
if (process.env.ad_env === 'prod') {
    ORDER_NOTIF_ZALO_GROUP = 'https://zalo.me/g/aeqtrv970';
}

exports.ORDER_NOTIF_ZALO_GROUP = ORDER_NOTIF_ZALO_GROUP

exports.send_zalo_message = async (shareLink, message) => {
    let data = new FormData();
    data.append('shareLink', shareLink);
    data.append('message', message);
    let config3 = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://zalo.ariadirectcorp.com/api/zalo_chat/group/send_message',
        headers: {
            ...data.getHeaders()
        },
        data
    };
    const resp3 = await axios(config3).catch(console.log)
    console.log(resp3.data)
}
exports.fetch_email = async (email) => {
    const api_base = JSON.parse(process.env.ad_endpoint).api_base_url;
    const endpoint = `${api_base}/v1/email-temp/inboxs?email=${email}`;

    let emailsList = [];
    try {
        const response = await axios.get(endpoint);
        emailsList = response.data?.data || [];
        return emailsList
    } catch (error) {
        console.error('Error fetching emails:', error);
    }
    return emailsList;
};
exports.solveReCaptcha = async (page) => {
    //TODO UPDATE KEY FOR PROD
    const API_KEY = "next_6797dfa6da5ae878f752838e11c9fdb3a4"
    // Wait for reCAPTCHA to load
    await page.waitForSelector('.g-recaptcha');

    // Extract sitekey dynamically
    const siteKey = await page.$eval('.g-recaptcha', el => el.getAttribute('data-sitekey'));
    const currentPageUrl = page.url(); // Get current page URL

    console.log('Sitekey:', siteKey);
    console.log('Current Page URL:', currentPageUrl);

    // Step 1: Create a task on NextCaptcha
    const createTaskResponse = await axios.post('https://api.nextcaptcha.com/createTask', {
        clientKey: API_KEY,
        task: {
            type: "RecaptchaV2TaskProxyless",
            websiteURL: currentPageUrl,  // Use dynamic page URL
            websiteKey: siteKey
        }
    });

    if (!createTaskResponse.data || createTaskResponse.data.errorId !== 0 || !createTaskResponse.data.taskId) {
        console.error("Error creating CAPTCHA task:", createTaskResponse.data);
        return;
    }

    const taskId = createTaskResponse.data.taskId;
    console.log('Task Created, Task ID:', taskId);

    // Step 2: Poll for the solution
    let captchaToken = null;
    while (!captchaToken) {
        console.log('Waiting for CAPTCHA solution...');
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds before checking

        const taskResultResponse = await axios.post('https://api.nextcaptcha.com/getTaskResult', {
            clientKey: API_KEY,
            taskId: taskId
        });

        if (taskResultResponse.data && taskResultResponse.data.status === "ready" && taskResultResponse.data.solution) {
            captchaToken = taskResultResponse.data.solution.gRecaptchaResponse;
            console.log('CAPTCHA Solved:', captchaToken);
        }
    }

    // Step 6: Submit the captcha solution
    await page.$eval('#g-recaptcha-response', (el, token) => (el.value = token), captchaToken);
    await page.evaluate(() => {
        document.querySelector('#g-recaptcha-response').style.display = 'block'; // Optional: for debugging
    });
    return
}
exports.send_zalo_file = async (shareLink, file_url) => {
    const file_resp = await axios.get(file_url, { responseType: 'arraybuffer' }).catch(console.log)
    let data = new FormData();
    data.append('shareLink', shareLink);
    data.append('file', file_resp.data, {
        filename: file_url.split('/').pop(),
    });
    let config3 = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://zalo.ariadirectcorp.com/api/zalo_chat/group/send_file',
        headers: {
            ...data.getHeaders()
        },
        data
    };
    const resp3 = await axios(config3).catch(console.log)
    console.log(resp3.data)
}

exports.create_zip = async (zip_name, folder) => {
    return new Promise((resolve, reject) => {
        const output = fs.createWriteStream(zip_name);
        const archive = archiver('zip', { zlib: { level: 9 } });

        output.on('close', () => {
            console.log('ZIP archive created successfully.');
            resolve();
        });

        archive.on('error', (err) => {
            reject(err);
        });

        archive.pipe(output);
        archive.directory(folder, false);
        archive.finalize();
    });
}
exports.text_slugify = (text) => slugify(text, { replacement: '_', locale: 'vi', trim: true })


exports.fill_pdf_form = async (form_path, data, frame) => {
    for (const key of Object.keys(data)) {
        if (_.isNil(data[key])) {
            data[key] = "";
        }
    }

    const result_file = this.text_slugify(data.filename) ?? ('Application_Form_' + moment().format('YYYYMMDDHHmmss') + '.pdf');
    const buckets = JSON.parse(process.env.ad_s3);

    const pdfDoc = await PDFDocument.load(await fs.readFile(form_path), { ignoreEncryption: true });

    // Get the form
    const form = pdfDoc.getForm();

    // Fill the form fields
    const form_fields = form.getFields();
    for (const [key, value] of Object.entries(data)) {
        try {
            const field = form.getFieldMaybe(key)
            if (field) {
                if (field.constructor.name === 'PDFTextField') {
                    field.setText(value);
                } else if (field.constructor.name === 'PDFCheckBox') {
                    if (value == true) {
                        field.check();
                    } else {
                        field.uncheck();
                    }
                } else if (field.constructor.name === 'PDFRadioGroup') {
                    field.select(value);
                } else if (field.constructor.name === 'PDFDropdown') {
                    field.select(value);
                } else {
                    console.log(`Field type not supported: ${field.constructor.name}`);
                }
            }
        } catch (error) { }

    }
    const defaultFont = await fs.readFile(__dirname + "/pdffiller/source/times.ttf")
    // Register the `fontkit` instance
    pdfDoc.registerFontkit(fontkit)
    const font = await pdfDoc.embedFont(StandardFonts[data.font] || defaultFont);
    // Set font and font size
    if (data.font_size !== "auto") {
        const fontSize = data.font_size ?? 10.0;
        form.updateFieldAppearances(font, fontSize);
    } else {
        form.updateFieldAppearances(font);
    }
    //flattern
    form.flatten()

    // Save the PDF
    const pdfBytes = await pdfDoc.save();
    await fs.writeFile(result_file, pdfBytes);

    let result_file_buff = fs.readFileSync(result_file)

    if (frame && frame.photo_url) {
        const photo_resp = await axios.get(frame.photo_url, { responseType: 'arraybuffer' })
        result_file_buff = await this.import_photo_to_application_form(photo_resp.data, result_file_buff, frame)
    }
    await this.s3_upload_buff(buckets.ariadirect_prod_applications, `tasks/${result_file}`, result_file_buff)
    const file = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/tasks/${result_file}`
    fs.unlinkSync(result_file)
    // Remove all temp pdf files
    fs.readdirSync(".").forEach(file => {
        if (file.endsWith('.pdf') || file.endsWith('.xfdf')) {
            fs.removeSync(`${file}`);
        }
    });
    return file
}
exports.random = (list) => {
    return list[Math.floor(Math.random() * list.length)]
}

exports.fill_docx_form = async (form_path, data) => {
    const result_file = this.text_slugify(data.filename) ?? ('Application_Form_' + moment().format('YYYYMMDDHHmmss') + '.pdf')
    const buckets = JSON.parse(process.env.ad_s3)
    const template = fs.readFileSync(form_path);
    const buffer = await createReport({
        template,
        data,
        additionalJsContext: {
            image_func: async (width, height, image_key) => {
                const url = data[image_key]
                console.log(width, height, url)
                const resp = await axios.get(url, { responseType: 'arraybuffer' });
                return { width, height, data: resp.data, extension: '.png' };
            },
        },
    });
    const tmp_file = tmp.fileSync({ mode: 0o644, prefix: 'temp-', postfix: '.docx' });
    fs.writeFileSync(tmp_file.name, buffer);
    const command = `libreoffice --headless --convert-to pdf ${tmp_file.name}`;
    const convert_result = await new Promise(resolve => exec(command, (error, stdout, stderr) => {
        if (error) {
            console.log(`Conversion error: ${error.message}`);
            return resolve(null)
        }
        if (stderr) {
            console.log(`Conversion stderr: ${stderr}`);
        }
        resolve(tmp_file.name.split('/').pop().replace('.docx', '.pdf'));
    }))
    if (!convert_result) {
        return null
    }
    const result_file_buff = fs.readFileSync(convert_result)
    await this.s3_upload_buff(buckets.ariadirect_prod_applications, `tasks/${result_file}`, result_file_buff)
    const file = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/tasks/${result_file}`
    fs.unlinkSync(convert_result)
    return file
}

exports.get_task_name = (task) => {
    const { passport_core_info_given_name, passport_core_info_surname, travel_passenger_info_passenger_name_list } = task.input_pod_values
    if (passport_core_info_given_name && passport_core_info_surname) {
        return `${passport_core_info_given_name} ${passport_core_info_surname}`
    }
    if (travel_passenger_info_passenger_name_list) {
        return travel_passenger_info_passenger_name_list
    }
    return ''
}


exports.get_country_name = (iso3) => iso.whereAlpha3(iso3).country
exports.get_country_iso2 = (iso3) => iso.whereAlpha3(iso3).alpha2
exports.get_country_name_v2 = (iso3) => country_custom_names[iso3] ?? iso.whereAlpha3(iso3).country

exports.retry_operation = async (operation, max_retry, errorMessage) => {
    const RETRY_DELAY = 10000
    for (let attempt = 0; attempt <= max_retry; attempt++) {
        try {
            return await operation();
        } catch (error) {
            if (attempt === max_retry) {
                console.error(`${errorMessage}: ${error.message}`);
                throw error;
            }
            console.warn(`Attempt ${attempt + 1} failed. Retrying...`);
            await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        }
    }
};

exports.extract_json = (str) => {
    if (!str) return null;

    const result = [];

    for (let i = 0; i < str.length; i++) {
        if (str[i] === '{' || str[i] === '[') {
            let start = i;
            let depth = 1;

            for (let j = i + 1; j < str.length; j++) {
                if (str[j] === '{' || str[j] === '[') {
                    depth++;
                } else if (str[j] === '}' || str[j] === ']') {
                    depth--;
                }

                if (depth === 0) {
                    const potential = str.substring(start, j + 1);
                    try {
                        const validJSON = JSON.parse(potential);
                        result.push(validJSON);
                    } catch (e) {
                        break;
                    }
                    i = j;
                    break;
                }
            }
        }
    }

    return result;
}

exports.sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms))

exports.country_iso2_to_iso3 = (iso2) => Object.keys(country_iso3_to_iso2).find(iso3 => country_iso3_to_iso2[iso3] === iso2) || null;
