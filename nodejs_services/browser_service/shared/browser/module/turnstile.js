const checkTurnstile = ({ page }) => {
    return new Promise(async (resolve, reject) => {
        var waitInterval = setTimeout(() => { clearInterval(waitInterval); resolve(false) }, 10000);
        try {
            const elements = await page.$$('[name="cf-turnstile-response"]');
            for (const element of elements) {
                try {
                    const value = await element.evaluate(el => el.value);
                    if (value.length > 0) {
                        clearInterval(waitInterval)
                        return resolve(true)
                    } else {
                        const parentElement = await element.evaluateHandle(el => el.parentElement);
                        const box = await parentElement.boundingBox();
                        x = box.x + 30;
                        y = box.y + box.height / 2;
                        await page.mouse.click(x, y);
                        x = box.x + box.width / 2 - 30;
                        y = box.y + box.height / 2;
                        await page.mouse.click(x, y);
                        console.log("Clicking on turnstile")
                    }
                } catch (err) { }
            }
            clearInterval(waitInterval)
            resolve(true)
        } catch (err) {
            clearInterval(waitInterval)
            resolve(false)
        }
    })
}

module.exports = { checkTurnstile }