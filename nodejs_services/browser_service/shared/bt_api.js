const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs/promises');
const uuid = require('uuid');
const redis = require('./redis');

class BTApi {
    constructor() {
        // this.API = process.env.ad_env === 'prod' ? 'https://api.ariadirect.com' : 'https://api.ariadirectcorp.com';
        this.API = 'https://api.ariadirectcorp.com'
    }

    async randomEmail(prefix) {
        let mail_domain = process.env.ad_env === 'prod' ? 'ariadirectcorp.com' : 'innovationjourney.org';
        const key = `email_index:${mail_domain}_${prefix}`
        const index = await redis.redis.get(key) || 0
        await redis.redis.set(key, Number(index) + 1)
        return `${prefix}${Number(index) + 1}@${mail_domain}`.toLowerCase()
    }

    async getEmails(email = '<EMAIL>',) {
        const url = `${this.API}/v1/email-temp/inboxs?email=${email}`;
        console.log(url)
        const resp = await axios.get(url)
        return resp.data.data
    }
}

module.exports = BTApi;
