module.exports = {
    name_change_reason: {
        'changed_by_marriage': "marriage",
        'changed_by_court': 'court_order'
    },
    gender: {
        'M': 'Male',
        'F': 'Female'
    },
    gender_vnm: {
        'M': 'Nam',
        'F': 'Nữ'
    },
    purpose: {
        'business': 'Business Travel',
        'tourist': 'Tourist'
    },
    purpose_vn: {
        'business': 'DN',
        'tourist': 'DL'
    },
    number_of_entries: {
        'single_entry': 'Single entry',
        'multiple_entries': 'Multiple entries'
    },
    duration_month: {
        '1M': 1,
        '3M': 3,
        '6M': 6,
        '1y': 12,
    },
    occupation: {
        'business_person': 'Business person',
        'company_employee': 'Company employee',
        'student': 'Student',
        'retired': 'Retired',
        'homemaker': 'Homemaker',
    },
    education: {
        'high_school': 'High school',
        'college': 'College',
        'graduate': 'Graduate',
        'postgraduate': 'Postgraduate'
    },
    validity: [
        {
            validity: '3m',
            entries: 'single_entry',
            value: '2.2.1'
        },
        {
            validity: '3m',
            entries: 'double_entries',
            value: '2.2.2'
        },
        {
            validity: '6m',
            entries: 'double_entries',
            value: '2.2.2'
        },
        {
            validity: '6m',
            entries: 'multiple_entries',
            value: '2.2.3'
        },
        {
            validity: '1y',
            entries: 'multiple_entries',
            value: '2.2.4'
        }
    ],
    form_type: {
        visaForm: 'visa_form',
        approvalLetter: 'approval_letter',
        passportForm: 'passport_form',
        singleCertificate: 'single_certificate'
    },
    relationship: {
        "spouse": "Spouse",
        "mother": "Mother",
        "father": "Father",
        "grandparent": "Grand Parent",
        "sibling": "Sibling",
        "child": "Child",
        "aunt": "Aunt",
        "uncle": "Uncle",
        "cousin": "Cousin",
        "nephew": "Nephew",
        "niece": "Niece",
        "siblings_in_law": "Siblings in law",
        "friend": "Friend"
    },
    us_immigration: {
        "us_green_card": "USA Green Card",
        "h1b": "H1B visa"
    },
    hair_color: {
        "black": "BLACK",
        "blonde": "BLONDE",
        "brown": "BROWN",
        "gray": "GRAY",
        "red": "RED",
        "bald": "BALD",
        "blue": "BLUE",
        "green": "GREEN",
        "orange": "ORANGE",
        "pink": "PINK",
        "purple": "PURPLE",
        "sandy": "SANDY",
        "white": "WHITE",
        "other": "OTHER"
    },
    eye_color: {
        "black": "BLACK",
        "blue": "BLUE",
        "brown": "BROWN",
        "gray": "GRAY",
        "green": "GREEN",
        "hazel": "HAZEL",
        "maroon": "MAROON",
        "multicolored": "MULTICOLORED",
        "pink": "PINK",
        "other": "OTHER"
    },
    vnm_content: {
        "lost": "Xin cấp lại hộ chiếu do bị mất",
        "damaged": "Xin cấp lại hộ chiếu do bị rách",
        "new": "Xin hộ chiếu mới",
        "renew": "Xin cấp đổi hộ chiếu",
        "child_born_abroad": "Xin hộ chiếu cho con sinh ở nước ngoài",
        "change_name": "Chỉnh sửa tên",
        "change_gender": "Đổi giới tính",
        "same_name_as_foreign_pp": "Lấy tên giống hộ chiếu nước ngoài"
    },
    passport_tasks: {
        "new": "NEW PASSPORT",
        "renew": "RENEW PASSPORT",
        "lost_stolen": "LOST OR STOLEN PASSPORT",
        "damaged": "DAMAGED PASSPORT"
    },
    document_type: {
        "residence_passport": "Passport",
        "us_green_card": "Green Card",
        "us_visa_i20": "Visa I20",
        "us_visa_i94": "Visa I94",
        "id_card": "ID Card"
    },
    country_custom_names: {
        'USA': 'United States',
    },
    us_state: {
        "AL": "Alabama",
        "AK": "Alaska",
        "AS": "American Samoa",
        "AZ": "Arizona",
        "AR": "Arkansas",
        "CA": "California",
        "CO": "Colorado",
        "CT": "Connecticut",
        "DE": "Delaware",
        "DC": "District of Columbia",
        "FL": "Florida",
        "GA": "Georgia",
        "GU": "Guam",
        "HI": "Hawaii",
        "ID": "Idaho",
        "IL": "Illinois",
        "IN": "Indiana",
        "IA": "Iowa",
        "KS": "Kansas",
        "KY": "Kentucky",
        "LA": "Louisiana",
        "ME": "Maine",
        "MD": "Maryland",
        "MA": "Massachusetts",
        "MI": "Michigan",
        "MN": "Minnesota",
        "MS": "Mississippi",
        "MO": "Missouri",
        "MT": "Montana",
        "NE": "Nebraska",
        "NV": "Nevada",
        "NH": "New Hampshire",
        "NJ": "New Jersey",
        "NM": "New Mexico",
        "NY": "New York",
        "NC": "North Carolina",
        "ND": "North Dakota",
        "MP": "Northern Mariana Islands",
        "OH": "Ohio",
        "OK": "Oklahoma",
        "OR": "Oregon",
        "PA": "Pennsylvania",
        "PR": "Puerto Rico",
        "RI": "Rhode Island",
        "SC": "South Carolina",
        "SD": "South Dakota",
        "TN": "Tennessee",
        "TX": "Texas",
        "UT": "Utah",
        "VT": "Vermont",
        "VI": "Virgin Islands",
        "VA": "Virginia",
        "WA": "Washington",
        "WV": "West Virginia",
        "WI": "Wisconsin",
        "WY": "Wyoming"
    },
    country_iso3_to_iso2: {
        "AFG": "AF",
        "ALB": "AL",
        "DZA": "DZ",
        "ASM": "AS",
        "AND": "AD",
        "AGO": "AO",
        "AIA": "AI",
        "ATA": "AQ",
        "ATG": "AG",
        "ARG": "AR",
        "ARM": "AM",
        "ABW": "AW",
        "AUS": "AU",
        "AUT": "AT",
        "AZE": "AZ",
        "BHS": "BS",
        "BHR": "BH",
        "BGD": "BD",
        "BRB": "BB",
        "BLR": "BY",
        "BEL": "BE",
        "BLZ": "BZ",
        "BEN": "BJ",
        "BMU": "BM",
        "BTN": "BT",
        "BOL": "BO",
        "BES": "BQ",
        "BIH": "BA",
        "BWA": "BW",
        "BVT": "BV",
        "BRA": "BR",
        "IOT": "IO",
        "BRN": "BN",
        "BGR": "BG",
        "BFA": "BF",
        "BDI": "BI",
        "CPV": "CV",
        "KHM": "KH",
        "CMR": "CM",
        "CAN": "CA",
        "CYM": "KY",
        "CAF": "CF",
        "TCD": "TD",
        "CHL": "CL",
        "CHN": "CN",
        "CXR": "CX",
        "CCK": "CC",
        "COL": "CO",
        "COM": "KM",
        "COG": "CG",
        "COD": "CD",
        "COK": "CK",
        "CRI": "CR",
        "CIV": "CI",
        "HRV": "HR",
        "CUB": "CU",
        "CUW": "CW",
        "CYP": "CY",
        "CZE": "CZ",
        "DNK": "DK",
        "DJI": "DJ",
        "DMA": "DM",
        "DOM": "DO",
        "ECU": "EC",
        "EGY": "EG",
        "SLV": "SV",
        "GNQ": "GQ",
        "ERI": "ER",
        "EST": "EE",
        "ETH": "ET",
        "FLK": "FK",
        "FRO": "FO",
        "FJI": "FJ",
        "FIN": "FI",
        "FRA": "FR",
        "GUF": "GF",
        "PYF": "PF",
        "ATF": "TF",
        "GAB": "GA",
        "GMB": "GM",
        "GEO": "GE",
        "DEU": "DE",
        "GHA": "GH",
        "GIB": "GI",
        "GRC": "GR",
        "GRL": "GL",
        "GRD": "GD",
        "GLP": "GP",
        "GUM": "GU",
        "GTM": "GT",
        "GGY": "GG",
        "GIN": "GN",
        "GNB": "GW",
        "GUY": "GY",
        "HTI": "HT",
        "HMD": "HM",
        "VAT": "VA",
        "HND": "HN",
        "HKG": "HK",
        "HUN": "HU",
        "ISL": "IS",
        "IND": "IN",
        "IDN": "ID",
        "IRN": "IR",
        "IRQ": "IQ",
        "IRL": "IE",
        "IMN": "IM",
        "ISR": "IL",
        "ITA": "IT",
        "JAM": "JM",
        "JPN": "JP",
        "JEY": "JE",
        "JOR": "JO",
        "KAZ": "KZ",
        "KEN": "KE",
        "KIR": "KI",
        "PRK": "KP",
        "KOR": "KR",
        "KWT": "KW",
        "KGZ": "KG",
        "LAO": "LA",
        "LVA": "LV",
        "LBN": "LB",
        "LSO": "LS",
        "LBR": "LR",
        "LBY": "LY",
        "LIE": "LI",
        "LTU": "LT",
        "LUX": "LU",
        "MAC": "MO",
        "MKD": "MK",
        "MDG": "MG",
        "MWI": "MW",
        "MYS": "MY",
        "MDV": "MV",
        "MLI": "ML",
        "MLT": "MT",
        "MHL": "MH",
        "MTQ": "MQ",
        "MRT": "MR",
        "MUS": "MU",
        "MYT": "YT",
        "MEX": "MX",
        "FSM": "FM",
        "MDA": "MD",
        "MCO": "MC",
        "MNG": "MN",
        "MNE": "ME",
        "MSR": "MS",
        "MAR": "MA",
        "MOZ": "MZ",
        "MMR": "MM",
        "NAM": "NA",
        "NRU": "NR",
        "NPL": "NP",
        "NLD": "NL",
        "NCL": "NC",
        "NZL": "NZ",
        "NIC": "NI",
        "NER": "NE",
        "NGA": "NG",
        "NIU": "NU",
        "NFK": "NF",
        "MNP": "MP",
        "NOR": "NO",
        "OMN": "OM",
        "PAK": "PK",
        "PLW": "PW",
        "PSE": "PS",
        "PAN": "PA",
        "PNG": "PG",
        "PRY": "PY",
        "PER": "PE",
        "PHL": "PH",
        "PCN": "PN",
        "POL": "PL",
        "PRT": "PT",
        "PRI": "PR",
        "QAT": "QA",
        "REU": "RE",
        "ROU": "RO",
        "RUS": "RU",
        "RWA": "RW",
        "BLM": "BL",
        "SHN": "SH",
        "KNA": "KN",
        "LCA": "LC",
        "MAF": "MF",
        "SPM": "PM",
        "VCT": "VC",
        "WSM": "WS",
        "SMR": "SM",
        "STP": "ST",
        "SAU": "SA",
        "SEN": "SN",
        "SRB": "RS",
        "SYC": "SC",
        "SLE": "SL",
        "SGP": "SG",
        "SXM": "SX",
        "SVK": "SK",
        "SVN": "SI",
        "SLB": "SB",
        "SOM": "SO",
        "ZAF": "ZA",
        "SGS": "GS",
        "SSD": "SS",
        "ESP": "ES",
        "LKA": "LK",
        "SDN": "SD",
        "SUR": "SR",
        "SJM": "SJ",
        "SWZ": "SZ",
        "SWE": "SE",
        "CHE": "CH",
        "SYR": "SY",
        "TWN": "TW",
        "TJK": "TJ",
        "TZA": "TZ",
        "THA": "TH",
        "TLS": "TL",
        "TGO": "TG",
        "TKL": "TK",
        "TON": "TO",
        "TTO": "TT",
        "TUN": "TN",
        "TUR": "TR",
        "TKM": "TM",
        "TCA": "TC",
        "TUV": "TV",
        "UGA": "UG",
        "UKR": "UA",
        "ARE": "AE",
        "GBR": "GB",
        "USA": "US",
        "UMI": "UM",
        "URY": "UY",
        "UZB": "UZ",
        "VUT": "VU",
        "VEN": "VE",
        "VNM": "VN",
        "VGB": "VG",
        "VIR": "VI",
        "WLF": "WF",
        "ESH": "EH",
        "YEM": "YE",
        "ZMB": "ZM",
        "ZWE": "ZW"
    },
    form_name: {
        ad_usa_009: "AD USA 009 Form",
        chn_evisa: "China eVisa",
        hkg_evisa: "Hong Kong eVisa",
        ind_visa: "India Visa",
        korean_visa: "Korean Visa",
        schengen: "Schengen Visa",
        us_passport: "US Passport",
        visa_cam: "Cambodia Visa",
        vnm_001: "Vietnam Visa Type 001",
        vnm_002: "Vietnam Visa Type 002",
        vnm_006: "Vietnam Visa Type 006",
        vnm_007: "Vietnam Visa Type 007",
        vnm_evisa: "Vietnam eVisa",
        vnm_fastlane: "Vietnam Fastlane Visa",
        vnm_ht_001: "Vietnam HT 001 Visa",
        vnm_native_visa: "Vietnam Native Visa",
        fedex_label: "FedEx Shipping Label",
        vnm_approval_letter: "Vietnam Approval Letter",
        vnm_fastlane_for_order: "Vietnam Fastlane Visa for Order"
    },
    airport: {
        HAN: "Noi Bai International Airport",
        SGN: "Tan Son Nhat International Airport",
        DAD: "Da Nang International Airport",
    },
    airport_reverse: {
        "Noi Bai International Airport": "HAN",
        "(HAN) Noi Bai International Airport": "HAN",
        "Tan Son Nhat International Airport": "SGN",
        "(SGN) Tan Son Nhat International Airport": "SGN",
        "Da Nang International Airport": "DAD",
        "(DAD) Da Nang International Airport": "DAD",
    },
    airport_fastlane_id: {
        HAN_arrival: 1000017,
        HAN_departure: 1000021,
        SGN_arrival: 1000005,
        SGN_departure: 1000009,
    }
}