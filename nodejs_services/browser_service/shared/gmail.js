const Imap = require('imap');
const simpleParser = require('mailparser').simpleParser;
const fs = require('fs').promises;
const path = require('path');

const imapConfig = {
    user: '<EMAIL>',
    password: 'trps rfgw lyjg wmrd',
    host: 'imap.gmail.com',
    port: 993,
    tls: true,
    tlsOptions: { rejectUnauthorized: false }
};

async function fetchLatestEmails() {
    return new Promise((resolve, reject) => {
        const imap = new Imap(imapConfig);
        const emails = [];
        let fetchTimeout;

        imap.once('ready', () => {
            imap.openBox('INBOX', false, (err, box) => {
                if (err) {
                    console.error('Error opening mailbox:', err);
                    imap.end();
                    return reject(err);
                }

                console.log(`Mailbox opened. Total messages: ${box.messages.total}`);

                if (box.messages.total === 0) {
                    console.log('No messages to fetch');
                    imap.end();
                    return resolve([]);
                }

                const fetchEmails = (seqno) => {
                    const f = imap.seq.fetch(seqno, {
                        bodies: ['HEADER', 'TEXT'],
                        struct: true
                    });

                    f.on('message', (msg) => {
                        console.log(`Processing message ${seqno}`);
                        const email = {};

                        msg.on('body', (stream, info) => {
                            let buffer = '';
                            stream.on('data', (chunk) => {
                                buffer += chunk.toString('utf8');
                            });
                            stream.once('end', () => {
                                if (info.which === 'HEADER') {
                                    const header = Imap.parseHeader(buffer);
                                    email.subject = header.subject ? header.subject[0] : 'No Subject';
                                    email.from = header.from ? header.from[0] : 'Unknown Sender';
                                    email.date = header.date ? new Date(header.date[0]) : new Date();
                                } else if (info.which === 'TEXT') {
                                    email.body = buffer;
                                }
                            });
                        });

                        msg.once('attributes', (attrs) => {
                            email.uid = attrs.uid;
                            email.flags = attrs.flags;
                            email.attachments = [];

                            if (attrs.struct) {
                                const attachments = findAttachmentParts(attrs.struct);
                                attachments.forEach((attachment) => {
                                    email.attachments.push({
                                        filename: attachment.params.name,
                                        contentType: attachment.type + '/' + attachment.subtype
                                    });
                                });
                            }
                        });

                        msg.once('end', () => {
                            console.log(`Parsed email: ${email.subject}`);
                            emails.push(email);
                        });
                    });

                    f.once('error', (err) => {
                        console.error('Fetch error:', err);
                    });

                    f.once('end', () => {
                        console.log(`Finished fetching message ${seqno}`);
                        if (seqno > 1 && Date.now() - startTime < 10000) {
                            fetchEmails(seqno - 1);
                        } else {
                            clearTimeout(fetchTimeout);
                            imap.end();
                            resolve(emails);
                        }
                    });
                };

                const startTime = Date.now();
                fetchEmails(box.messages.total);

                // Set a 10-second timeout
                fetchTimeout = setTimeout(() => {
                    console.log('Fetch timeout reached');
                    imap.end();
                    resolve(emails);
                }, 10000);
            });
        });

        imap.once('error', (err) => {
            console.error('IMAP connection error:', err);
            clearTimeout(fetchTimeout);
            reject(err);
        });

        imap.once('end', () => {
            console.log('IMAP connection ended');
            clearTimeout(fetchTimeout);
        });

        imap.connect();
    });
}

function findAttachmentParts(struct, attachments = []) {
    for (let i = 0; i < struct.length; i++) {
        if (Array.isArray(struct[i])) {
            findAttachmentParts(struct[i], attachments);
        } else if (struct[i].disposition && struct[i].disposition.type.toUpperCase() === 'ATTACHMENT') {
            attachments.push(struct[i]);
        }
    }
    return attachments;
}

async function main() {
    try {
        console.log('Starting to fetch emails...');
        const latestEmails = await fetchLatestEmails();
        console.log(`Fetched ${latestEmails.length} recent emails:`);
        latestEmails.forEach((email, index) => {
            console.log(`${index + 1}. Subject: ${email.subject}, From: ${email.from}, Date: ${email.date}`);
        });
    } catch (error) {
        console.error('Error in main function:', error);
    }
}

main();