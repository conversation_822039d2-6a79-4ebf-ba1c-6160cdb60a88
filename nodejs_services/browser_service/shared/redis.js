const { createClient } = require('redis');
const client = createClient({
    url: 'redis://209.74.72.129:6379',
    password: '8dc2835978f2c981',
    database: 1,
    connectTimeout: 15000 // 15 seconds
});

client.on('error', err => console.log('Redis Client Error', err));
client.on('connect', () => console.log('Redis Client Connected'));

(async () => {
    await client.connect();
})()

const setObj = async (key, obj, timeout = 0) => {
    await client.set(key, JSON.stringify(obj), { 'EX': timeout }).catch((error) => console.log(error));
}

const getObj = async (key) => {
    try {
        const data = await client.get(key);
        return JSON.parse(data);
    } catch (error) {
        console.log(error);
    }
};



module.exports = { getObj, setObj, redis: client };