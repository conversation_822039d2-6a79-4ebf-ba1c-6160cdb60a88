const iso = require('iso-3166-1');
const axios = require("axios");
const uuid = require("uuid");
const FormData = require('form-data');
const fileType = require('file-type');
const X_API_KEY = "************************************************************************************************************"
const get_country_name = (iso3) => COUNTRY[iso3] || iso.whereAlpha3(iso3).country

const get_image_captcha_text = async (buffer, model = 'claude-3-haiku-20240307') => {
    try {
        const base64Image = buffer.toString('base64');

        const type = await fileType.fromBuffer(buffer);
        const mediaType = type ? type.mime : 'image/jpeg';

        const response = await axios({
            method: 'post',
            url: 'https://api.anthropic.com/v1/messages',
            headers: {
                'anthropic-version': '2023-06-01',
                'content-type': 'application/json',
                'x-api-key': X_API_KEY
            },
            data: {
                model,
                max_tokens: 1024,
                messages: [{
                    role: 'user',
                    content: [
                        {
                            type: 'text',
                            text: 'Please read and return only the text shown in this captcha image. Return only the alphanumeric characters with no spaces or special characters.'
                        },
                        {
                            type: 'image',
                            source: {
                                type: 'base64',
                                media_type: mediaType,
                                data: base64Image
                            }
                        }
                    ]
                }]
            }
        });
        console.log("Raw Captcha Response:", response.data.content[0].text.trim())
        const extractedCaptcha = response.data.content[0].text.trim().match(/[a-zA-Z0-9]+$/);

        let captchaText = '';
        if (extractedCaptcha && extractedCaptcha[0]) {
            captchaText = extractedCaptcha[0];
        }
        return captchaText;

    } catch (error) {
        console.log(error.message);
        return null;
    }
};

module.exports = { get_country_name, get_image_captcha_text }