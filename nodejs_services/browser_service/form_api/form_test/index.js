const vnm_evisa = require('./../../task_forms/vnm_evisa');
const helper = require('../../shared/helpers');
const moment = require('moment');
const { append_sheet } = require('../../../shared/google');

const form_test = async (req, res) => {
   const MOCK_ORDERS = [
      {
         service: 'vnm_evisa',
         order_id: 5534,
         pods: {
            service_core_info_entry_date: moment().add(10, 'days').toISOString(),
            service_core_info_exit_date: moment().add(20, 'days').toISOString(),
         }
      }
   ]

   let test_result = []
   for (const MOCK_ORDER of MOCK_ORDERS) {
      let { order, task, pod_data } = await helper.get_order_task_pod_data(MOCK_ORDER.order_id)
      pod_data = { ...pod_data, ...MOCK_ORDER.pods }
      const resp = await vnm_evisa.create_form(order, pod_data)
      test_result.push({
         form: MOCK_ORDER.service,
         status: resp.success ? 'success' : 'fail',
         test_order_id: MOCK_ORDER.order_id,
         test_result: JSON.stringify(resp, null, 2),
         test_at: moment().format('YYYY-MM-DD HH:mm:ss'),
      })
   }
   append_sheet("1bbhomc13UyfujVBqStxqNe-dlen-tWogcOaoJ4hwgk8", {
      "Summary": test_result
   })
}
// form_test()

module.exports = {
   form_test,
}