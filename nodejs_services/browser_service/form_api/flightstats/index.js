const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');
const moment = require('moment-timezone');
const _ = require('lodash');
const { FlightTracker, Flight, FlightAircraft, ETSPrice } = require('../../../shared/database');
const data_mapping = require('../../shared/data_mapping');
const { Op } = require('sequelize');
const ejs = require('ejs');
const path = require('path');
const { pack, unpack } = require('msgpackr');
const { COUNTRY_AIRPORTS, FS_TO_IATA, FS_TO_ICAO, NAME_TO_ICAO } = require('./data_mapping');

const data = {
    name: "<PERSON>",
    age: 25,
    hobbies: ["reading", "gaming"],
    address: {
        city: "New York",
        country: "USA"
    },
    active: true
};

// Encode data
const encoded = pack(data);
console.log('Encoded buffer:', encoded.toString('base64'));
const buffer = Buffer.from(encoded.toString('base64'), 'base64');
console.log(unpack(buffer))

const summary = async (req, res) => {
    const templatePath = path.join(__dirname, 'ejs', 'summary.ejs');
    const data = {};

    ejs.renderFile(templatePath, data, (err, html) => {
        if (err) {
            console.error(err);
            res.status(500).send('Error rendering template');
        } else {
            res.send(html);
        }
    });
}

const airline_logo = async (req, res) => {
    const { icao } = req.query;
    try {
        const logoDirectories = ['airline_logo_icao', 'fr24_logos', 'flightaware_logos', 'radarbox_logos'];
        let logoPath;

        for (const dir of logoDirectories) {
            logoPath = path.join(__dirname, 'images', dir, `${icao}.png`);
            if (fs.existsSync(logoPath)) break;
        }

        const resp = fs.readFileSync(logoPath);
        res.setHeader('Content-Type', 'image/png');
        res.send(resp);
    } catch (error) {
        res.status(404).send('Not found');
    }
}


const post_summary = async (req, res) => {
    const { airport, service_type, service_date_time, airline_fs } = req.query;

    const timezone = 'Asia/Ho_Chi_Minh'
    const timezone_offset = moment(service_date_time).tz(timezone).utcOffset() / 60

    const nearest_same_weekday = moment()
        .subtract((moment().day() - moment(service_date_time).day() + 7) % 7, 'days')
        .hour(moment(service_date_time).hour())
        .minute(moment(service_date_time).minute())
        .second(moment(service_date_time).second())

    console.log(service_date_time)
    console.log(nearest_same_weekday.toISOString())

    const check_date_time = nearest_same_weekday.clone().add(-timezone_offset, 'hours')
    console.log(check_date_time.toISOString())
    const diff = moment(service_date_time).diff(check_date_time, 'seconds')

    const flight_trackers = await FlightTracker.findAll({
        where: {
            airport: airport,
            airport_fs: { [Op.notIn]: COUNTRY_AIRPORTS["VNM"] },
            service_type: service_type,
            // is_code_shared: false,
            service_date_time: {
                [Op.or]: [7, 0, -7, -14, -21, -28].map(v => ({
                    [Op.between]: [
                        check_date_time.clone().add(v, 'days').add(-1, 'hours').format('YYYY-MM-DD HH:mm:ss'),
                        check_date_time.clone().add(v, 'days').add(1, 'hours').format('YYYY-MM-DD HH:mm:ss')
                    ]
                }))
            }
        },
        raw: true
    })

    const uiq_flight_trackers = _.chain(flight_trackers)
        .sortBy('service_date_time')
        .reverse()
        .uniqBy('flight_id')
        .value()

    console.log(uiq_flight_trackers.map(v => v.flight_id).includes('339980'))
    const db_flight_aircrafts = await FlightAircraft.findAll({})

    const db_flights = await Flight.findAll({
        where: {
            id: uiq_flight_trackers.map(v => v.flight_id),
            // working_rules: {
            //     [Op.contains]: [check_date_time.format('dddd').toLowerCase()]
            // }
        },
        raw: true
    })

    const ets_prices = await ETSPrice.findAll({
        where: {
            ets_id: Object.values(data_mapping.airport_fastlane_id)
        },
        raw: true
    })
    const flights = uiq_flight_trackers.map(flight => {
        let db_flight = db_flights.find(f => String(f.id) === String(flight.flight_id))
        if (!db_flight) {
            return null
        }
        let db_flight_aircraft = db_flight_aircrafts.find(f => f.aircraft_code === db_flight.aircraft_code)
        const airline_logo = `https://form.ariadirectcorp.com/api/flightstats/airline_logo?icao=${NAME_TO_ICAO[db_flight.airline_name] || db_flight.airline_fs}`
        const ets_price = ets_prices.find(v => v.ets_id === data_mapping.airport_fastlane_id[airport + '_' + service_type])
        let new_service_datetime = moment(service_date_time).format('YYYY-MM-DD') + 'T' + moment(flight.service_date_time).tz(timezone).format('HH:mm:ss') + 'Z'
        // let new_service_datetime = moment(flight.service_date_time).add(diff,'seconds').tz(timezone).format('YYYY-MM-DDTHH:mm:ss') + 'Z'

        if (Number(db_flight_aircraft?.capacity) === 0) {
            return null
        }
        if (moment(new_service_datetime).isBefore(moment(service_date_time).clone().add(-12, 'hours'))) {
            new_service_datetime = moment(new_service_datetime).add(1, 'days').format('YYYY-MM-DDTHH:mm:ss[Z]')
        } else if (moment(new_service_datetime).isAfter(moment(service_date_time).clone().add(12, 'hours'))) {
            new_service_datetime = moment(new_service_datetime).add(-1, 'days').format('YYYY-MM-DDTHH:mm:ss[Z]')
        }

        return {
            name: `${db_flight.airline_fs}-${db_flight.flight_number}`,
            aircraft: db_flight_aircraft?.aircraft_name,
            aircraft_capacity: Number(flight.is_code_shared ? 0 : db_flight_aircraft?.capacity),
            airline_logo,
            airline_name: db_flight.airline_name,
            service_date_time: new_service_datetime,
            price: ets_price ? Number(ets_price.price) + _.sum(Object.values(ets_price.additional_fee)) : 0,
            currency: ets_price?.currency || 'USD',
            is_code_shared: flight.is_code_shared,
            code_shared_note: flight.code_shared_note,
            ref_id: pack(JSON.stringify({
                ref_name: 'flightstats',
                airport,
                country: 'VNM',
                service_type,
                service_id: data_mapping.airport_fastlane_id[airport + '_' + service_type],
                airport_name: data_mapping.airport[airport],
                airline_name: db_flight.airline_name,
                airline_fs: db_flight.airline_fs,
                service_date_time: new_service_datetime,
                flight_number: `${db_flight.airline_fs}-${db_flight.flight_number}`,
            })).toString('base64')
        }
    }).filter(v => v).sort((a, b) => {
        if (a.airline_name < b.airline_name) return -1;
        if (a.airline_name > b.airline_name) return 1;
        return moment(a.service_date_time).unix() - moment(b.service_date_time).unix();
    })
    const result = {
        flights,
        total_flights: flights.length,
        total_passengers: _.sum(flights.map(v => v.aircraft_capacity)),
    }

    res.json({
        success: true,
        data: result
    });
}

const cron_update_flight_working_rules = async (req, res) => {
    try {
        const flights = await Flight.findAll({
            where: {
                [Op.or]: [
                    { working_rules: { [Op.eq]: null } },
                    { working_rules: { [Op.eq]: [] } }
                ]
            }
        });
        const flightRules = new Map(flights.map(f => [f.id, new Set()]));

        for (const airport of ['SGN', 'HAN', 'DAD']) {
            for (const service_type of ['arrival', 'departure']) {
                const trackers = await FlightTracker.findAll({
                    where: {
                        airport,
                        service_type,
                        service_date_time: {
                            [Op.between]: [
                                moment().subtract(3, 'weeks').startOf('isoWeek').format('YYYY-MM-DD HH:mm:ss'),
                                moment().endOf('isoWeek').format('YYYY-MM-DD HH:mm:ss')
                            ]
                        }
                    },
                    raw: true
                });

                trackers.forEach(t => {
                    const day = moment(t.service_date_time).format('dddd').toLowerCase();
                    flightRules.get(t.flight_id)?.add(day);
                });
            }
        }

        const daysOrder = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        await Promise.all(flights.map(f =>
            Flight.update(
                { working_rules: Array.from(flightRules.get(f.id) || []).sort((a, b) => daysOrder.indexOf(a) - daysOrder.indexOf(b)) },
                { where: { id: f.id } }
            )
        ));

        return res.json({ message: 'Flight working rules updated', updated: flights.length });
    } catch (error) {
        return res.json({ message: 'Update failed', error: error.message });
    }
};


const get_aircraft_info = async (check_date, flight) => {
    const url = `https://www.flightstats.com/v2/flight-tracker/${flight.airline_fs}/${flight.flight_number}?year=${check_date.format('YYYY')}&month=${check_date.format('MM')}&date=${check_date.format('DD')}`;
    console.log(url);

    try {
        const resp = await axios({
            method: 'get',
            maxBodyLength: Infinity,
            url: url,
            headers: {
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language': 'en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7',
                'priority': 'u=0, i',
                'upgrade-insecure-requests': '1',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            },
            timeout: 10000 // 10 second timeout
        });

        const $ = cheerio.load(resp.data);
        const code = $('h4').filter(function () {
            return $(this).text().trim() === 'Code';
        }).next('h5').text().trim();
        const description = $('h4').filter(function () {
            return $(this).text().trim() === 'Description';
        }).next('h5').text().trim();

        return { code, description };
    } catch (error) {
        console.log(`Error fetching data for flight ${flight.flight_number} on ${check_date.format('YYYY-MM-DD')}:`, error.message);
        return { code: null, description: null };
    }
};

const cron_get_flights = async (req, res) => {
    res.json({ success: true })
    try {
        for (const airport of ['SGN', 'HAN', 'DAD']) {
            let result = []
            for (const arr of ['arr', 'dep']) {
                for (let i = -3; i <= 3; i++) {
                    const now = moment().add(i, 'days')
                    for (const hour of [0, 6, 12, 18]) {
                        const config = {
                            method: 'get',
                            maxBodyLength: Infinity,
                            url: `https://www.flightstats.com/v2/api-next/flight-tracker/${arr}/${airport}/${now.format('YYYY/MM/DD')}/${hour}?numHours=6`,
                            headers: {
                                'accept': 'application/json, text/plain, */*',
                                'accept-language': 'en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7',
                                'priority': 'u=1, i',
                                'traceparent': '00-09a653de9cc8e8fbcb6899b3f278f961-87f97e297594f468-01',
                                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                                'x-aws-waf-token': '84483291998a:EQoAdE5JOIe1AAAA:HcECoehReAM3BqyRFli8gUkFSE7uT6os8RoQYWVyII1gaRU7kzWO+QCNRE10kgOKOJrb7xsw5oE8ECXlh274dH++PZUrMeYqXT1V/0dZZewPEf84c0VBJ7D0LDAd8LgIzmrygnrVP4E1kkKXB+i2IhMnVW2qdUHluoir8w1yhc/6GsLqWxcBbIT0eBhx8zzLIx2HlRzMDlHSanfA5YCAbC9DyMRyPJHDQSvgq9ZxuEykxSH/j4u7g9IXQVVRpBQuJxLSCaZa55V5Dw=='
                            }
                        }
                        try {
                            const resp = await axios(config)
                            resp.data.data.flights.forEach(v => {
                                v.destOriginTitle = resp.data.data.destOriginTitle
                                result.push(v)
                            })
                        } catch (error) {
                            console.log(error)
                        }
                    }
                }

                const bulk = result.map(v => {
                    return {
                        airport: airport,
                        service_type: v.destOriginTitle === 'Destination' ? 'departure' : 'arrival',
                        service_date_time: v.sortTime,
                        airport_fs: v.airport.fs,
                        airport_name: v.airport.city || '',
                        airline_fs: v.carrier.fs,
                        flight_number: v.carrier.flightNumber,
                        airline_name: v.carrier.name,
                        tracking_url: 'https://www.flightstats.com/v2' + v.url,
                        is_code_shared: v.isCodeshare || false,
                        code_shared_note: v.isCodeshare ? v.operatedBy : '',
                        raw_data: v
                    }
                })

                const test = bulk.find(v => v.flight_number == 228 && v.airline_fs === 'VJC')
                if (test) {
                    console.log(test)
                }
                for (const chunk of _.chunk(bulk, 100)) {
                    await Flight.bulkCreate(chunk.map(v => ({
                        airline_fs: v.airline_fs,
                        airline_name: v.airline_name,
                        flight_number: v.flight_number,
                        aircraft_code: '',
                        aircraft_name: '',
                        capacity: 0
                    })), { ignoreDuplicates: true })
                }
                const flights = await Flight.findAll({
                    where: {
                        airline_fs: bulk.map(v => v.airline_fs),
                        flight_number: bulk.map(v => v.flight_number),
                    },
                    raw: true
                })

                // Fill aircraft info
                for (const flight of flights) {
                    if (flight.aircraft_code) {
                        continue
                    }
                    for (let i = -3; i < 4; i++) {
                        let { code, description } = await get_aircraft_info(moment().add(i, 'days'), flight);

                        if (code) {
                            await Flight.update(
                                {
                                    aircraft_code: code,
                                    aircraft_name: description
                                },
                                {
                                    where: {
                                        airline_fs: flight.airline_fs,
                                        flight_number: flight.flight_number,
                                    }
                                }
                            );
                            await FlightAircraft.findOrCreate({
                                where: {
                                    aircraft_code: code
                                },
                                defaults: {
                                    aircraft_code: code,
                                    aircraft_name: description,
                                    capacity: 0
                                }
                            })
                            break;
                        }
                    }
                }
                for (const chunk of _.chunk(bulk, 100)) {
                    await FlightTracker.bulkCreate(chunk.map(v => {
                        const flight = flights.find(f => f.airline_fs === v.airline_fs && f.flight_number === v.flight_number)
                        if (!flight) {
                            console.log('flight not found', v)
                            return null
                        }
                        return {
                            airport: v.airport,
                            service_type: v.service_type,
                            service_date_time: v.service_date_time,
                            airport_fs: v.airport_fs,
                            airport_name: v.airport_name,
                            flight_id: flight.id,
                            tracking_url: v.tracking_url,
                            is_code_shared: v.is_code_shared,
                            code_shared_note: v.code_shared_note,
                        }
                    }).filter(v => v), { ignoreDuplicates: true, }).catch(console.log)
                }

            }
        }
        console.log('DONE')
    } catch (error) {
        console.log(error)
    }
}

const get_flight_count = async (req, res) => {
    const {
        travel_enter_flight_enter_airport,
        travel_enter_flight_enter_timestamp,
        travel_exit_flight_exit_airport,
        travel_exit_flight_exit_timestamp,
    } = req.query;


    let service_type = travel_enter_flight_enter_airport ? 'arrival' : 'departure'
    let airport = Object.keys(data_mapping.airport).find(v => data_mapping.airport[v] === (travel_enter_flight_enter_airport || travel_exit_flight_exit_airport))

    let service_date_time = travel_enter_flight_enter_timestamp || travel_exit_flight_exit_timestamp
    const timezone = 'Asia/Ho_Chi_Minh'
    const timezone_offset = moment(service_date_time).tz(timezone).utcOffset() / 60

    const nearest_same_weekday = moment()
        .subtract((moment().day() - moment(service_date_time).day() + 7) % 7, 'days')
        .hour(moment(service_date_time).hour())
        .minute(moment(service_date_time).minute())
        .second(moment(service_date_time).second())

    console.log(service_date_time)
    console.log(nearest_same_weekday.toISOString())

    const check_date_time = nearest_same_weekday.clone().add(-timezone_offset, 'hours')
    console.log(check_date_time.toISOString())


    const flight_trackers = await FlightTracker.findAll({
        where: {
            airport: airport,
            service_type: service_type,
            service_date_time: {
                [Op.or]: [7, 0, -7, -14, -21, -28].map(v => ({
                    [Op.between]: [
                        check_date_time.clone().add(v, 'days').add(-1, 'hours').format('YYYY-MM-DD HH:mm:ss'),
                        check_date_time.clone().add(v, 'days').add(1, 'hours').format('YYYY-MM-DD HH:mm:ss')
                    ]
                }))
            }
        },
        raw: true
    })

    const uiq_flight_trackers = _.chain(flight_trackers)
        .sortBy('service_date_time')
        .reverse()
        .uniqBy('flight_id')
        .value()

    const flights = await Flight.findAll({
        where: {
            id: uiq_flight_trackers.map(v => v.flight_id)
        },
    })

    res.json({
        success: true,
        type: 'select',
        data: uiq_flight_trackers.map(v => {
            let flight = flights.find(f => f.id === v.flight_id)
            return {
                text: `${flight.airline_fs}-${flight.flight_number} (${flight.airline_name})`,
                value: `${flight.airline_fs}-${flight.flight_number}`,
                data: JSON.stringify({
                    airport: v.airport,
                    airport_fs: v.airport_fs,
                    airport_name: v.airport_name,
                    airline_fs: flight.airline_fs,
                    airline_name: flight.airline_name,
                    flight_number: `${flight.airline_fs}-${flight.flight_number}`,
                }),
            }
        }).sort((a, b) => a.text.localeCompare(b.text))
    })
}
// cron_get_flights(null, { json: console.log })


module.exports = {
    airline_logo,
    summary,
    post_summary,
    cron_get_flights,
    get_flight_count,
}