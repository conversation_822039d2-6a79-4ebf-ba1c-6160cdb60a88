<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>AriaDirect Flight Count Summary</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>

<body>
    <section class="section">
        <div class="container">
            <h1 class="title">AriaDirect Flight Count Summary</h1>

            <!-- Input Form -->
            <div class="box">
                <form id="dateForm">
                    <div class="field">
                        <label class="label">Select Date</label>
                        <div class="control">
                            <input class="input" type="datetime-local" step="60" id="selectedDate" required>
                        </div>
                    </div>

                    <div class="columns">
                        <div class="column">
                            <div class="field">
                                <label class="label">Airport</label>
                                <div class="control">
                                    <div class="select is-fullwidth">
                                        <select id="selectedAirport" required>
                                            <option value="SGN">Ho Chi Minh City (SGN)</option>
                                            <option value="HAN">Hanoi (HAN)</option>
                                            <option value="DAD">Da Nang (DAD)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="column">
                                    <div class="field">
                                        <label class="label">Service Type</label>
                                        <div class="control">
                                            <div class="select is-fullwidth">
                                                <select id="selectedServiceType" required>
                                                    <option value="departure">Departure</option>
                                                    <option value="arrival">Arrival</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button class="button is-primary is-fullwidth" type="submit">Get Summary</button>
                            </div>
                        </div>
                </form>
            </div>

            <!-- Chart Section -->
            <div id="chartSection" class="box mb-5 is-hidden">
                <div class="tabs">
                    <ul>
                        <li class="is-active"><a onclick="switchChart('flights')">Flights</a></li>
                        <li><a onclick="switchChart('capacity')">Capacity</a></li>
                    </ul>
                </div>
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="flightsChart"></canvas>
                    <canvas id="capacityChart" style="display: none;"></canvas>
                </div>
            </div>

            <!-- Results Section -->
            <div id="results" class="is-hidden">
                <h2 class="subtitle">
                    Summary for <span id="summaryDate"></span> -
                    <span id="summaryAirport"></span> -
                    <span id="summaryServiceType"></span>
                </h2>

                <!-- Loading Spinner -->
                <div id="loading" class="has-text-centered">
                    <div class="button is-loading is-large">Loading</div>
                </div>

                <!-- Results Content -->
                <div id="summaryContent"></div>
            </div>
        </div>
    </section>

    <script>
        // Cache DOM elements
        const chartSection = document.getElementById('chartSection');
        const flightsChartDiv = document.getElementById('flightsChart');
        const capacityChartDiv = document.getElementById('capacityChart');
        const dateForm = document.getElementById('dateForm');
        const resultsSection = document.getElementById('results');
        const loadingElement = document.getElementById('loading');
        const summaryContent = document.getElementById('summaryContent');
        const summaryDate = document.getElementById('summaryDate');
        const summaryAirport = document.getElementById('summaryAirport');
        const summaryServiceType = document.getElementById('summaryServiceType');

        // Configure axios defaults
        axios.defaults.timeout = 5000; // 5 seconds timeout

        // Airport display names mapping
        const airportNames = {
            'SGN': 'Ho Chi Minh City (SGN)',
            'HAN': 'Hanoi (HAN)',
            'DAD': 'Da Nang (DAD)'
        };

        // Service type display names mapping
        const serviceTypeNames = {
            'departure': 'Departures',
            'arrival': 'Arrivals'
        };

        // Create flight summary HTML
        function createHourSummary(hour) {
            return `
                <div class="box">
                    <h3 class="title is-4">${String(hour.hour).padStart(2, '0')}:00</h3>
                    <div class="content">
                        <div class="level mb-4">
                            <div class="level-item has-text-centered">
                                <div>
                                    <p class="heading">Total Flights</p>
                                    <p class="title is-4">${hour.total_flights}</p>
                                </div>
                            </div>
                            <div class="level-item has-text-centered">
                                <div>
                                    <p class="heading">Total Capacity</p>
                                    <p class="title is-4">${hour.flights.reduce((sum, flight) => sum + (flight.aircraft_capacity || 0), 0)} passengers</p>
                                </div>
                            </div>
                        </div>
                        ${hour.flights && hour.flights.length > 0
                    ? `<div class="columns is-multiline">
                                ${hour.flights.map(flight => `
                                    <div class="column is-one-fifth">
                                        <div class="box has-background-light">
                                            <div class="is-flex is-justify-content-space-between">
                                                <span class="tag is-info is-medium">${flight.name}</span>
                                                <span class="tag is-warning is-medium">
                                                    Wait: ${flight.wait_time || 0} min
                                                </span>
                                            </div>
                                            <div class="mt-2">
                                                <p class="is-size-7">
                                                    <strong>Aircraft:</strong> ${flight.aircraft || 'N/A'}<br>
                                                    <strong>Capacity:</strong> ${flight.aircraft_capacity || 'N/A'} passengers
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                               </div>`
                    : '<p>No flights scheduled</p>'
                }
                    </div>
                </div>
            `;
        }

        // Function to load flight data
        function switchChart(type) {
            const tabs = document.querySelectorAll('.tabs li');
            tabs.forEach(tab => tab.classList.remove('is-active'));

            if (type === 'flights') {
                flightsChartDiv.style.display = 'block';
                capacityChartDiv.style.display = 'none';
                tabs[0].classList.add('is-active');
            } else {
                flightsChartDiv.style.display = 'none';
                capacityChartDiv.style.display = 'block';
                tabs[1].classList.add('is-active');
            }
        }

        let flightsChart, capacityChart;

        function switchChart(type) {
            const tabs = document.querySelectorAll('.tabs li');
            const flightsCanvas = document.getElementById('flightsChart');
            const capacityCanvas = document.getElementById('capacityChart');

            tabs.forEach(tab => tab.classList.remove('is-active'));

            if (type === 'flights') {
                flightsCanvas.style.display = 'block';
                capacityCanvas.style.display = 'none';
                tabs[0].classList.add('is-active');
            } else {
                flightsCanvas.style.display = 'none';
                capacityCanvas.style.display = 'block';
                tabs[1].classList.add('is-active');
            }
        }

        function renderCharts(data) {
            const chartData = data.map(hour => ({
                hour: `${String(hour.hour).padStart(2, '0')}:00`,
                flights: hour.total_flights,
                capacity: hour.flights.reduce((sum, flight) => sum + (flight.aircraft_capacity || 0), 0)
            }));

            const labels = chartData.map(item => item.hour);
            const flightValues = chartData.map(item => item.flights);
            const capacityValues = chartData.map(item => item.capacity);

            // Destroy existing charts if they exist
            if (flightsChart) flightsChart.destroy();
            if (capacityChart) capacityChart.destroy();

            // Create Flights Chart
            flightsChart = new Chart(document.getElementById('flightsChart'), {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Number of Flights',
                        data: flightValues,
                        backgroundColor: '#3298dc',
                        borderColor: '#3298dc',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });

            // Create Capacity Chart
            capacityChart = new Chart(document.getElementById('capacityChart'), {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Total Passenger Capacity',
                        data: capacityValues,
                        backgroundColor: '#48c774',
                        borderColor: '#48c774',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Show chart section
            chartSection.classList.remove('is-hidden');
        }

        async function loadFlightData(date, airport, serviceType) {
            // Show loading state
            resultsSection.classList.remove('is-hidden');
            loadingElement.classList.remove('is-hidden');
            summaryContent.classList.add('is-hidden');

            // Update summary headers
            summaryDate.textContent = new Date(date).toLocaleDateString();
            summaryAirport.textContent = airportNames[airport];
            summaryServiceType.textContent = serviceTypeNames[serviceType];

            try {
                const response = await axios.get('/api/flightstats/post_summary', {
                    params: {
                        service_date_time: date,
                        airport: airport,
                        service_type: serviceType,
                        debug: true,
                    },
                    cache: {
                        maxAge: 15 * 60 * 1000,
                        exclude: { query: false }
                    }
                });

                const sortedHours = response.data.data.hours.sort((a, b) => a.hour - b.hour);

                // Render charts
                renderCharts(sortedHours);
                chartSection.classList.remove('is-hidden');

                // Render hour summaries
                const hoursHtml = sortedHours.map(createHourSummary).join('');
                summaryContent.innerHTML = hoursHtml;

            } catch (error) {
                let errorMessage = 'Error loading flight summary. Please try again.';

                if (error.response) {
                    if (error.response.status === 404) {
                        errorMessage = 'No flight data found for the selected criteria.';
                    } else if (error.response.status === 429) {
                        errorMessage = 'Too many requests. Please wait a moment and try again.';
                    }
                } else if (error.request) {
                    errorMessage = 'Unable to connect to the server. Please check your internet connection.';
                }

                summaryContent.innerHTML = `
                    <div class="notification is-danger">
                        ${errorMessage}
                    </div>
                `;
            } finally {
                loadingElement.classList.add('is-hidden');
                summaryContent.classList.remove('is-hidden');
            }
        }

        // Handle form submission
        dateForm.addEventListener('submit', async function (e) {
            e.preventDefault();
            const date = document.getElementById('selectedDate').value;
            const airport = document.getElementById('selectedAirport').value;
            const serviceType = document.getElementById('selectedServiceType').value;
            await loadFlightData(date, airport, serviceType);
        });

        // Initialize with default values and load data
        const today = new Date();
        const dateInput = document.getElementById('selectedDate');
        dateInput.valueAsDate = today;

        // Format today's date as YYYY-MM-DD for API
        const formattedDate = today.toISOString().split('T')[0];
        const defaultAirport = 'SGN';
        const defaultServiceType = 'departure';

        // Set default values in selects
        document.getElementById('selectedAirport').value = defaultAirport;
        document.getElementById('selectedServiceType').value = defaultServiceType;

        // Load initial data
        loadFlightData(formattedDate, defaultAirport, defaultServiceType);
    </script>
</body>

</html>