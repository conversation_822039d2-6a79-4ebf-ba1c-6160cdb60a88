const fs = require("fs");
const axios = require("axios");
const moment = require('moment-timezone')
const iso = require('iso-3166-1');
const { country_iso2_to_iso3 } = require("../../shared/helpers");
const excluedCountry = ['ALA', 'ASM', 'AIA', 'ATA', 'ABW', 'BMU', 'BES', 'BVT', 'IOT', 'CYM', 'CXR', 'CCK', 'COK', 'CUW', 'FLK', 'FRO', 'GUF', 'PYF', 'ATF', 'GIB', 'GRL', 'GLP', 'GUM', 'GGY', 'HMD', 'IMN', 'JEY', 'MTQ', 'MYT', 'MSR', 'NCL', 'NFK', 'MNP', 'PCN', 'PRI', 'REU', 'BLM', 'SHN', 'MAF', 'SPM', 'SXM', 'SGS', 'SJM', 'TKL', 'TCA', 'UMI', 'VGB', 'VIR', 'WLF', 'ESH']
const getVisaCheckFromPassportIndex = async (country) => {
    let attempt = 0;
    retries = 50
    delay = 100
    const year = moment().year()
    while (attempt < retries) {
        try {
            const response = await fetch("https://www.passportindex.org/incl/compare2.php", {
                headers: {
                    "accept": "*/*",
                    "accept-language": "vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7,id;q=0.6",
                    "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                    "priority": "u=1, i",
                    "sec-ch-ua": "\"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "sec-fetch-dest": "empty",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-site": "same-origin",
                    "x-requested-with": "XMLHttpRequest",
                    "cookie": "_ga=GA1.1.1056062071.1727668751; _fbp=fb.1.1727668751382.998367811799410261; __insp_wid=1425776728; __insp_nv=true; __insp_targlpu=aHR0cHM6Ly93d3cucGFzc3BvcnRpbmRleC5vcmcvY29tcGFyZWJ5UGFzc3BvcnQucGhwP3AxPXJvJnkxPTIwMjQmcDI9ZmomeTI9MjAyNA%3D%3D; __insp_targlpt=Q29tcGFyZSBQYXNzcG9ydHMgUG93ZXIgfCBQYXNzcG9ydCBJbmRleCAyMDI0; __insp_norec_sess=true; PHPSESSID=82c7377742134a4ce891cda2d53f655a; __insp_slim=1727675428419; _ga_7Z48X951ET=GS1.1.1727671872.2.1.1727676658.0.0.0; cf_clearance=s77mUFtu9xNvoEtekjsR8lCAvgxmsMejP6KuiHfUuHg-1727676661-*******-ozj7LNffL9IWAw9EZ.a6xu2UFtt8G_9K.TrkLL8aWkPfVpe2K7ruS9XtHS1FzJv1fw3WU4fSoG7RheFn6U44sV7Wct1oi_UGun9kazQf2TsMnvUUJWzA7ITbVhdgfPdrOI4IudvcBCEoVwl8xQT5rn.i.57.D.BnAZgKdK5remYLs4utnanTfO3pa3s1Fvd7yZxCp7rMDbGTxXwn9WLbijnBnF5wBeIgd56W5qwhDxf5XeQSWkrX3xtQSgrhSrBmiZyHy.YYgrOv3NhKKtmw5uE7szsjcbk9tau_ASLXcip7OEDerhq7V6qdQulHmeygMOH3oWVkWZW.ge5UTu8zPevEa8FMdpJvws.mm.q56fzru83DEV89UfXT8UTyczSb",
                    "Referer": `https://www.passportindex.org/comparebyPassport.php?p1=${country}&y1=${year}`,
                    "Referrer-Policy": "strict-origin-when-cross-origin"
                },
                body: `compare=3&year=${year}&cc=${country}&csrf_token=6ffcc21376992e156e08d7fb818b3e7920c96ecfece297d3a4a0f5c8d4efb60f`,
                method: "POST"
            });

            if (response.ok) {
                const data = await response.json();
                if (data.length > 1) {
                    return data;
                }
            } else {
            }
        } catch (error) {
        }
        attempt++;
        await new Promise(res => setTimeout(res, delay));
    }
    return null;
}
const create_form = async () => {
    countries = iso.all()
    const result = []
    for (const fromCountry of countries) {
        if (excluedCountry.includes(fromCountry.alpha3)) continue;
        const data = await getVisaCheckFromPassportIndex(fromCountry.alpha2.toLowerCase());
        if (!data) {
            console.log(`Failed To Get Country: ${fromCountry.country} - ${fromCountry.alpha3}`);
            continue;
        }
        data.forEach(item => {
            result.push({
                from_country_alpha3: fromCountry.alpha3,
                to_country_alpha3: country_iso2_to_iso3(item.code),
                requirement: item.text,
            });
        });
        console.log(`Success Country: ${fromCountry.country} - ${fromCountry.alpha3}`);
    }
    return result
}

const crawl_data = async (req, res) => {
    try {
        res.json({ success: true })
        const data = await create_form()
        const api_base = JSON.parse(process.env.ad_endpoint).api_base_url
        const resp = await axios.post(`${api_base}/v1/master-data/public/visa_check`, data)
        console.log(resp.data)
        return data
    } catch (error) {
        console.log(error)
    }
}
module.exports = { crawl_data }