const axios = require('axios');
const fs = require('fs');
const { extract_json } = require('../../shared/helpers');
const { COUNTRIES, PROVICES, CITIES } = require('./mapping');

const initial = async () => {

}

const china_visa_countries = (req, res) => {
    res.json({
        success: true,
        type: 'select',
        data: COUNTRIES.filter(v => !["EUE", "UNO", "NON", "CHN", "CHNL", "TWNC"].includes(v.id)).map(v => ({
            text: v.text,
            value: v.id,
        }))
    })
}

const china_visa_states = (req, res) => {
    const { passport_core_info_country_of_birth } = req.query;
    const raw_provices = PROVICES[passport_core_info_country_of_birth];

    res.json(raw_provices?.length > 0 ? {
        success: true,
        type: 'select',
        data: raw_provices.map(v => ({
            text: v.text,
            value: v.id,
        }))
    } : {
        success: true,
        type: 'input',
    })
}

const china_visa_cities = (req, res) => {
    const { passport_core_info_state_of_birth } = req.query;
    const raw_cities = CITIES[passport_core_info_state_of_birth];

    res.json(raw_cities?.length > 0 ? {
        success: true,
        type: 'select',
        data: raw_cities.map(v => ({
            text: v.text,
            value: v.id,
        }))
    } : {
        success: true,
        type: 'input',
    })
}

module.exports = {
    china_visa_countries,
    china_visa_states,
    china_visa_cities,
}