const puppeteer = require('puppeteer');
const { get_image_captcha_text } = require('../../shared/captcha');
const { sleep } = require('../../shared/helpers');
const { exec } = require('child_process');
const moment = require('moment');
const qs = require('qs');
const cheerio = require('cheerio');
const fs = require('fs');
const axios = require('axios');
const { CookieJar } = require('tough-cookie');
const { wrapper } = require('axios-cookiejar-support');

const jar = new CookieJar();
const client = wrapper(axios.create({ jar, timeout: 60000 }));

exec('ssh -i jump.pem -CND 8007 ec2-user@54.202.35.245', (error, stdout, stderr) => {
    if (error) {
        console.error(`exec error: ${error}`);
        return;
    }
    console.log(`stdout: ${stdout}`);
    console.error(`stderr: ${stderr}`);
});

const mergeCookies = (existingCookies, newCookies) => {
    const cookieMap = {};

    if (existingCookies) {
        existingCookies.split('; ').forEach(cookie => {
            const [name, value] = cookie.split('=');

            cookieMap[name] = value;
        });
    }

    if (newCookies) {
        console.log("newCookies")
        newCookies.forEach(cookie => {
            const [cookieString] = cookie.split(';');
            const [name, value] = cookieString.split('=');
            console.log(name, value)
            cookieMap[name] = value;
        });
    }

    return Object.entries(cookieMap)
        .map(([name, value]) => `${name}=${value}`)
        .join('; ');
};

const get_us_passport_status = async (req, res) => {
    const { surname, dob, ssn } = req.query
    const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox', '--proxy-server=socks5://localhost:8007']
    });
    const page = await browser.newPage();
    await sleep(1000)
    try {
        await page.goto('https://passportstatus.state.gov')
        await page.click('[name="FraudAndAbuseCheckBox"]')
        await page.click('button[type="submit"]')
        await page.waitForSelector('[name="LastName"]')
        await sleep(1000)
        await page.type('[name="LastName"]', surname)
        await page.type('[name="DateOfBirth"]', moment(dob).format('MMDDYYYY'))
        await page.type('[name="Last4SSN"]', ssn)

        let isOK = false
        for (let i = 0; i < 10; i++) {
            const captcha_element = await page.$('#SearchCaptcha_CaptchaImage')
            const buffer = await captcha_element.screenshot({ encoding: 'binary' });
            const captcha = await get_image_captcha_text(buffer)
            await page.type('[name="CaptchaCode"]', captcha)
            await page.click('button[type="submit"]')
            await sleep(3000)

            const captcha_result = await page.evaluate(() => {
                return document.querySelector('[data-valmsg-for="CaptchaCode"]')?.textContent ?? null
            })
            if (captcha_result === 'Incorrect CAPTCHA Code.') {
                console.log(captcha_result)
                continue
            }
            isOK = true
            break
        }

        if (!isOK) {
            throw new Error('Failed to solve captcha')
        }

        const status_text = await page.evaluate(() => {
            return document.querySelector('.app-status')?.textContent ?? null
        })
        console.log(status_text)

        res.json({
            success: true,
            data: {
                status_text,
                status: '',
            }
        })
    } catch (e) {
        console.log(e)
        res.json({
            success: false,
            message: e.message,
        })
    } finally {
        await browser.close()
    }
}

const get_us_passport_status_v2 = async (req, res) => {
    const { surname, dob, ssn } = req.query

    try {
        const resp1 = await client({
            method: 'get',
            maxBodyLength: Infinity,
            url: 'https://passportstatus.state.gov/',
            headers: {
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'en-US,en;q=0.9',
                'connection': 'keep-alive',
                'host': 'passportstatus.state.gov',
                'upgrade-insecure-requests': '1',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            },
            withCredentials: true
        });
        fs.writeFileSync('test1.html', resp1.data)
        const $ = cheerio.load(resp1.data);
        let requestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
        const resp2 = await client({
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://passportstatus.state.gov/',
            headers: {
                'content-type': 'application/x-www-form-urlencoded',
                'origin': 'https://passportstatus.state.gov',
                'referer': 'https://passportstatus.state.gov/',
                'upgrade-insecure-requests': '1',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            },
            data: qs.stringify({
                '__RequestVerificationToken': requestVerificationToken,
                'FraudAndAbuseCheckBox': 'true'
            }),
            withCredentials: true
        });


        for (let i = 0; i < 10; i++) {
            const resp3 = await client({
                method: 'get',
                maxBodyLength: Infinity,
                url: 'https://passportstatus.state.gov/Search',
                headers: {
                    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                    'accept-encoding': 'gzip, deflate, br, zstd',
                    'accept-language': 'en-US,en;q=0.9',
                    'cache-control': 'max-age=0',
                    'connection': 'keep-alive',
                    'host': 'passportstatus.state.gov',
                    'referer': 'https://passportstatus.state.gov/',
                    'upgrade-insecure-requests': '1',
                    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                },
                withCredentials: true
            })

            const $3 = cheerio.load(resp3.data);
            fs.writeFileSync('test3.html', resp3.data)
            let requestVerificationToken3 = $3('input[name="__RequestVerificationToken"]').val();
            const captchaUrl = $3('#SearchCaptcha_CaptchaImage').attr('src');

            const resp4 = await client({
                method: 'get',
                maxBodyLength: Infinity,
                url: 'https://passportstatus.state.gov' + captchaUrl,
                headers: {
                    'accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                    'accept-encoding': 'gzip, deflate, br, zstd',
                    'accept-language': 'en-US,en;q=0.9',
                    'connection': 'keep-alive',
                    'host': 'passportstatus.state.gov',
                    'referer': 'https://passportstatus.state.gov/Search',
                    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                },
                responseType: 'arraybuffer'
            });
            const captcha = await get_image_captcha_text(resp4.data);
            console.log("captcha", captcha)
            console.log(captchaUrl)
            console.log(captchaUrl.match(/\&t=([^&]+)/)[1])
            const reps5 = await client({
                method: 'post',
                maxBodyLength: Infinity,
                url: 'https://passportstatus.state.gov/Search',
                headers: {
                    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                    'accept-encoding': 'gzip, deflate, br, zstd',
                    'accept-language': 'en-US,en;q=0.9',
                    'cache-control': 'max-age=0',
                    'connection': 'keep-alive',
                    'content-type': 'application/x-www-form-urlencoded',
                    'host': 'passportstatus.state.gov',
                    'origin': 'https://passportstatus.state.gov',
                    'referer': 'https://passportstatus.state.gov/Search',
                    'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                    'upgrade-insecure-requests': '1',
                    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                },
                data: qs.stringify({
                    '__RequestVerificationToken': requestVerificationToken3,
                    'LastName': surname.toUpperCase(),
                    'DateOfBirth': dob,
                    'Last4SSN': ssn.slice(-4),
                    'LBD_VCID_SearchCaptcha': captchaUrl.match(/\&t=([^&]+)/)[1],
                    'LBD_BackWorkaround_SearchCaptcha': '1',
                    'CaptchaCode': captcha
                })
            })
            if (reps5.data.includes('Incorrect CAPTCHA Code')) {
                continue;
            }

            const $5 = cheerio.load(reps5.data);
            const status_text = $5('.app-status').text();

            console.log(status_text)
            const status = {
                "Application Status: Approved": "approved",
                "Application Status: In Process": "pending",
                // TODO: add more
            }[status_text] || 'unknown'
            res.json({
                success: true,
                data: {
                    status_text,
                    status,
                }
            })
            return
        }
        res.json({
            success: false,
            message: 'Failed to get status',
        })
    } catch (e) {
        console.log(e)
        res.json({
            success: false,
            message: e.message,
        })
    }
}
module.exports = {
    get_us_passport_status,
    get_us_passport_status_v2,
}