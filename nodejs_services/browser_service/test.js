const axios = require('axios');
const fs = require('fs');

(async () => {
    const result = [];
    for (let i = 20; i < 27; i++) {
        for (const j of [0, 6, 12, 18]) {
            const resp = await axios({
                method: 'get',
                maxBodyLength: Infinity,
                url: `https://www.flightstats.com/v2/api-next/flight-tracker/arr/DAD/2024/12/${i}/${j}?carrierCode=&numHours=6`,
                headers: {
                    'accept': 'application/json, text/plain, */*',
                    'accept-language': 'en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7',
                    'priority': 'u=1, i',
                    'traceparent': '00-09a653de9cc8e8fbcb6899b3f278f961-87f97e297594f468-01',
                    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
                    'x-aws-waf-token': '6219842b-fe5a-48f2-8133-881f18307d8c:EQoAdF4rMBplAAAA:3g+tzJ5I9FRmT4tus2dvRD4LWg08OPq9jsqgUwMNg/jpX/5hsFFtbrWoqGENyUwLi23Bv5pEGe4FVyTCKDKuVC5KR6XizABMTIHmJ8VisBU5w205CSs4JPOp4fyD/3YS0vEzRBXH8S71cQdHSjH/FEkYaHeBF6Z2QFsoJwr2g6J5qYlxAZ5hNwfyFvNZxwdzFBZfzIKQb0DFjSC2+FcZQakSYJqdb1VbwexbCvchumdRx595bTfGauQHEvwxbbLiOb8Wmez89KVU7Q=='
                }
            })
            result.push(...resp.data.data.flights);
        }
    }

    fs.writeFileSync('dataDAD.json', JSON.stringify(result, null, 4));
})();