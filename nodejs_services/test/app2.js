const axios = require('axios');
const fs = require('fs');
const cheerio = require('cheerio');
const FormData = require('form-data');

(async () => {
   let config1 = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'https://www.remove.bg/upload',
      headers: {
         'origin': 'https://www.remove.bg',
         'referer': 'https://www.remove.bg/upload',
         'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
      }
   }
   const step1 = await axios(config1);
   const $ = cheerio.load(step1.data);
   const csrfToken = $('meta[name="csrf-token"]').attr('content');
   console.log('CSRF Token:', csrfToken);
   // Extract the session cookie from response
   let cookies = step1.headers['set-cookie'];
   console.log('Cookies:', cookies.map(v => v.split(';')[0]).join('; '));

   let config2 = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://www.remove.bg/trust_tokens',
      headers: {
         'cookie': cookies.map(v => v.split(';')[0]).join('; '),
         'origin': 'https://www.remove.bg',
         'referer': 'https://www.remove.bg/upload',
         'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
         'x-csrf-token': csrfToken,
         'x-requested-with': 'XMLHttpRequest'
      }
   };
   try {
      const step2 = await axios(config2);
      console.log(step2.data)
   } catch (err) {
      console.log(err.response.data);
   }

   cookies = step2.headers['set-cookie'];

   const tokenMatch = step2.data.request.match(/useToken\('([^']+)'\)/);
   const trust_token = tokenMatch ? tokenMatch[1] : '';
   console.log('Token extracted:', trust_token);

   let data = new FormData();
   const buff = fs.readFileSync('Test.jpg');
   data.append('image[original]', buff, 'Test.jpg');
   data.append('trust_token', trust_token);
   data.append('new_editor', 'true');



   let config3 = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://www.remove.bg/images',
      headers: {
         ...data.getHeaders(),
         'cookie': cookies.join('; '),
         'origin': 'https://www.remove.bg',
         'referer': 'https://www.remove.bg/upload',
         'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
         'x-csrf-token': csrfToken,
         'x-requested-with': 'XMLHttpRequest'

      },
      data
   };
   const step3 = await axios(config3);
   console.log(step3.data);

})();