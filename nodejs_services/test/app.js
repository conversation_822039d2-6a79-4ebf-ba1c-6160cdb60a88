const { connect } = require('puppeteer-real-browser');

(async () => {
   const { page, browser } = await connect({
      headless: false,
      turnstile: false,
      disableXvfb: true,
      customConfig: {
         ignoreDefaultArgs: ['--enable-automation'],  // Disable automation flag
      },
      connectOption: {
         defaultViewport: {
            width: 1920,
            height: 1080
         },
         args: [
         ],
         ignoreHTTPSErrors: true,
         waitForInitialPage: true,
      },
   });


   page.setDefaultNavigationTimeout(30000); // Increase timeout to 2 minutes
   page.setDefaultTimeout(30000);
   page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0 Safari/537.36');

   await page.goto('https://www.remove.bg/upload')

})();
