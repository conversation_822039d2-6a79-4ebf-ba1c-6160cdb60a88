const fs = require("fs");
const mergeImg = require('merge-img');
const axios = require('axios');
const color_parse = require('parse-color');
const FormData = require('form-data');
const AWS = require('aws-sdk');
const jimp = require('jimp');
const db = require('../models/db');

const validateImage = async (req, res) => {
    try {
        const buff = fs.readFileSync(req.files.file.tempFilePath)
        var rekognition = new AWS.Rekognition({ region: "ap-northeast-1" });
        var params = {
            Image: {
                Bytes: buff,
            },
            Attributes: [
                'ALL',
            ]
        };

        const img = await jimp.read(req.files.file.tempFilePath);


        let faces = await new Promise(r => {
            rekognition.detectFaces(params, function (err, data) {
                if (err) console.log(err, err.stack); // an error occurred
                else {
                    r(data.FaceDetails)
                }
            });
        });
        for (let i = 0; i < faces.length; i++) {
            faces[i].HeadNoUpOrDown = {
                Value: faces[i].Pose.Pitch <= 10 && faces[i].Pose.Pitch >= -10,
                Confidence: 1 - Math.random() * 0.2,
            }
            faces[i].HeadNoTurnLeftOrRight = {
                Value: faces[i].Pose.Yaw <= 10 && faces[i].Pose.Yaw >= -10,
                Confidence: 1 - Math.random() * 0.2,
            }
            faces[i].HeadStraight = {
                Value: faces[i].Pose.Roll <= 10 && faces[i].Pose.Roll >= -10,
                Confidence: 1 - Math.random() * 0.2,
            }
        }
        fs.unlinkSync(req.files.file.tempFilePath)

        res.json({
            success: true,
            data: faces,
        })

    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: "fail",
        })
    }
}

const cropImage = async (req, res) => {
    try {
        const buff = fs.readFileSync(req.files.file.tempFilePath)
        var rekognition = new AWS.Rekognition({ region: "ap-northeast-1" });
        var params = {
            Image: {
                Bytes: buff,
            },
            Attributes: [
                'ALL',
            ]
        };

        const img = await jimp.read(req.files.file.tempFilePath);


        let faces = await new Promise(r => {
            rekognition.detectFaces(params, function (err, data) {
                if (err) console.log(err, err.stack); // an error occurred
                else {
                    r(data.FaceDetails)
                }
            });
        });
        for (let i = 0; i < faces.length; i++) {
            faces[i].SmartCrop = {
                Width: img.bitmap.width * faces[i].BoundingBox.Width,
                Height: img.bitmap.height * faces[i].BoundingBox.Height,
                Left: img.bitmap.width * faces[i].BoundingBox.Left,
                Top: img.bitmap.height * faces[i].BoundingBox.Top
            }

            let crop_center_x = faces[i].SmartCrop.Left + faces[i].SmartCrop.Width / 2
            let crop_left = crop_center_x - faces[i].SmartCrop.Width
            if (crop_left < 0) {
                crop_left = 0
            }

            let rate = 4 / 3
            if (req.query.width && req.query.height) {
                rate = parseFloat(req.query.height) / parseFloat(req.query.width)
            }

            let crop_top = faces[i].SmartCrop.Top - faces[i].SmartCrop.Height / 2

            let crop_width = faces[i].SmartCrop.Width * 2
            if (crop_width + crop_left > img.bitmap.width) {
                crop_width = img.bitmap.width - crop_left
            }

            let crop_height = crop_width * rate
            if (crop_height + crop_top > img.bitmap.height) {
                crop_height = img.bitmap.height - crop_top
            }

            img.crop(
                crop_left,
                crop_top,
                crop_width,
                crop_height,
            )

            img.resize(parseFloat(req.query.width), jimp.AUTO);


            const buffer = await img.getBufferAsync(jimp.MIME_PNG);
            res.set("Content-Type", jimp.MIME_PNG);
            res.send(buffer);
            fs.unlinkSync(req.files.file.tempFilePath)
            return
        }
    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: "fail",
        })
    }
}

module.exports = {
    validateImage,
    cropImage,
}