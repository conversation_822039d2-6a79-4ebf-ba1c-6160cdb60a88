const { s3_upload_signed_url, s3_signed_url } = require('../models/s3');
const { v4 } = require('uuid');
const aws_s3 = JSON.parse(process.env.ad_s3);

const generateFileUpload = async (req, res) => {
    try {
        const uuid = v4();

        const upload_url = await s3_upload_signed_url(aws_s3.ariadirect_prod_passport_images, `qr-code-images/${uuid}.png`)
        const download_url = s3_signed_url(aws_s3.ariadirect_prod_passport_images, `qr-code-images/${uuid}.png`)
        console.log(upload_url)
        res.json({
            data: {
                upload_url: upload_url,
                download_url: download_url,
            },
            message: "OK",
            success: true,
        })


    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: "fail",
        })
    }
}

module.exports = {
    generateFileUpload,
}