const axios = require('axios');
const crypto = require('crypto');
const cheerio = require('cheerio');
const FormData = require('form-data');
const jimp = require('jimp');

const imageToRemovedImageV2 = async function (buff) {
    let data = new FormData();
    data.append('source_image_file', buff, "file.png");
    try {
        const step0 = await axios({
            method: 'get',
            url: 'https://www.slazzer.com/upload',
        }).catch(e => {
            console.log(e)
        })

        const cookie = step0.headers["set-cookie"][0].split(";")[0];
        const $ = cheerio.load(step0.data);
        const csrt_token = $('meta[name="csrf-token"]').attr('content');

        const step1_config = {
            method: 'post',
            url: 'https://www.slazzer.com/upload_image',
            headers: {
                'authority': 'www.slazzer.com',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
                'x-requested-with': 'XMLHttpRequest',
                'x-csrftoken': csrt_token,
                'referer': 'https://www.slazzer.com/upload',
                'Cookie': cookie,
                ...data.getHeaders()
            },
            data: data
        };

        const step1 = await axios(step1_config).catch(e => {
            console.log(e)
        })

        console.log(JSON.stringify(step1.data))

        const step2_config = {
            method: 'get',
            url: "https://www.slazzer.com" + step1.data.preview_size_output_image,
            headers: {},
            responseType: 'arraybuffer'
        };

        const step2 = await axios(step2_config)
        console.log("STEP2");

        return step2.data

    } catch (error) {
        console.log(error);
    }
}

const imageToRemovedImageV3 = async function (buff) {
    try {
        let data = new FormData();
        data.append('image_file', buff, "file.png");

        const api_keys = [
            // "4778a0136fd750be7ba3d41939d92d3230706061",
            // "85ec9dcfc4dad1e802961a5266ea0e18d43a670f",
            // "102279905dd5fd330cfa4d33c0ee13646946d683",
            "f6a35b3f209ecdcd2f693a5c150347384053e580",
            "822c506f149aaaf1b2a4947b8aca85b18bc4619a",
            "a0a9a767a41af372abbc82f0b377dfcd92443792",
            "a9eaa2d4d0acff822ef809ed689da9be5cd90939",
            "a391536da5a8fec6155aca831bd7faf935acb025",
            "91ff705b1fd14d075ee825587d34eb4bff947708",
            "302937c00bf3f8eb2727f91d9734a35e0bcd7eb7",
            "224ed8f2f4899dc631d67017e6d9d17a3a5a3481",
            "439a1a4fe50d00ce52fc021a7a2c925ec92002a9",
            "1ec8d33f744b07af2de3ebd0e11e21996f3b2841",
            "ac8b31c8dc009b60ea14436088841c54c00bf155"
        ]
        const step1_config = {
            method: 'post',
            url: 'https://sdk.photoroom.com/v1/segment',
            headers: {
                'authority': 'sdk.photoroom.com',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36',
                'x-api-key': api_keys[Math.floor(Math.random() * api_keys.length)],
                ...data.getHeaders()
            },
            data: data,
            responseType: 'arraybuffer'

        }

        const step1 = await axios(step1_config).catch(e => {
            console.log(e)
        })
        return step1.data
    } catch (error) {
        console.log(error);
    }
}

const imageToRemovedImageV4 = async function (buff) {
    try {
        const step1_config = {
            method: 'post',
            url: 'https://www.googleapis.com/identitytoolkit/v3/relyingparty/signupNewUser?key=AIzaSyAJGrgbFGB_-h8V2oJLr4b-_ipetqM0duU',
            headers: {
                'Content-Type': 'application/json',
                'X-Android-Package': 'com.photoroom.app',
                'X-Android-Cert': '0424A4898A4B33940D8BF16E44251B876E97F8D0',
                'Accept-Language': 'vi-VN, en-US',
                'X-Client-Version': 'Android/Fallback/X21000003/FirebaseCore-Android',
                'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 9; SM-G955F Build/PPR1.180610.011)',
            },
            data: {}
        };

        const step1 = await axios(step1_config).catch(e => console.log(e))
        console.log(step1.data.idToken)

        const step2_config = {
            method: 'post',
            url: 'https://api.artizans.ai/v1/mask',
            headers: {
                'authorization': step1.data.idToken,
                'content-type': 'application/json; charset=UTF-8',
                'user-agent': 'okhttp/4.9.0',
            },
            data: {
                "model_type": "person",
                "b64_img": Buffer.from(buff).toString('base64'),
                "package": "com.photoroom.app"
            }
        }

        const step2 = await axios(step2_config).catch(e => console.log(e))
        const mask_buffer = Buffer.from(step2.data.b64_mask, 'base64');
        const src_image = await jimp.read(buff);
        const mask_image = await jimp.read(mask_buffer);

        const result_buffer = await src_image.mask(mask_image).getBufferAsync(jimp.MIME_PNG)
        return result_buffer
    } catch (error) {
        console.log(error);
    }
}

// https://pixlr.com/remove-background/
const imageToRemovedImageV5 = async function (buff) {
    try {
        const step1_config = {
            method: 'get',
            url: 'https://pixlr.com/api/auth/ai/remove-background/',
            headers: {
                'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 9; SM-G955F Build/PPR1.180610.011)',
            }
        };

        const step1 = await axios(step1_config).catch(e => console.log(e))
        console.log(step1.data)

        const token = step1.data.data

        var data = new FormData();
        data.append('image_file', buff, 'file.png');
        const step2_config = {
            method: 'post',
            url: 'https://pixlr.com/api/ai/remove-background',
            headers: {
                'authorizationtoken': token,
                'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
                ...data.getHeaders()
            },
            data: data
        }

        const step2 = await axios(step2_config).catch(e => console.log(e))
        const mask_buffer = Buffer.from(step2.data.data, 'base64');

        const src_image = await jimp.read(buff);
        const mask_image = await jimp.read(mask_buffer);

        src_image.mask(mask_image);

        const result_buffer = await src_image.getBufferAsync(jimp.MIME_PNG)
        return result_buffer
    } catch (error) {
        console.log(error);
    }
}

// https://www.remove.bg
const imageToRemovedImageV1 = async function (url, color) {
    const api_keys = [
        "GLzGo7BuHWGPgrFfs8VhiXjC",
        // "SkY67Ng9DLZ4g471aCcMUUf4",
        "7gp7uCdZFVCf5YJtrReVXcSP",
        "6ggoeW7tNngnaTLzEMfMCaih",
        "Y3Vz717VipNXBbru4vgijf8B",
        "JLf8fykc7FoxGzApCkW6UcWG",
        "nCzqKZwjr1FienDjiYUutPLF",
        "nhKgWxTZKuVFVhHazrx8HjZ4",
        "kypgrbvFuGEECyFVL1J7XNaH",
        "717w5DLXmu1pFyH76FBm2KH5",
        "yxbweqR2Xx8HWagWR5iy2VGW",

    ].sort(() => Math.random() - 0.5)

    var data = new FormData();
    // data.append('image_file', buff, "file.jpg");
    data.append('image_url', url);
    data.append('size', "preview");
    data.append('bg_color', color.hex);
    data.append('crop', 'false');
    data.append('format', 'jpg');
    data.append('type_level', '1');

    C: for (const api_key of api_keys) {
        const step1_config = {
            method: 'post',
            url: 'https://api.remove.bg/v1.0/removebg',
            headers: {
                'X-API-Key': api_key,
                ...data.getHeaders()
            },
            data: data,
            responseType: 'arraybuffer'
        };
        try {
            const step1 = await axios(step1_config)
            return step1.data
        } catch (e) {
            console.log(e.response.statusText)
            if (e.response.statusText === 'Payment Required') {
                await new Promise(resolve => setTimeout(resolve, 2000));
                continue C
            }
            return null
        }
    }

}

const { sign_header } = require('./visafoto.com/sign')

// https://visafoto.com/
const imageToRemovedImageV6 = async function (buff, image_size) {
    const document_type = {
        '2x2': 'vn_visa_2x2',
        '3x4': 'vi_apec_business_travel_card',
        '4x6': 'vn_visa',
    };
    var data = new FormData();
    // data.append('image_file', buff, "file.jpg");
    data.append('imageFile', buff, "fine.png");
    console.log(new Date())
    const hex_string = crypto.randomBytes(16).toString('hex');

    const step1_config = {
        method: 'post',
        url: `https://visafoto.com/upload?z=${hex_string}&docType=${document_type[image_size] || "vn_visa"}&tl=1&bg=1&ct=1`,
        headers: {
            'authority': 'visafoto.com',
            'origin': 'https://visafoto.com',
            'referer': 'https://visafoto.com/',
            'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-requested-with': 'XMLHttpRequest',
            ...data.getHeaders()
        },
        data: data,
        // responseType: 'arraybuffer'
    };
    try {
        const step1 = await axios(step1_config)
        const image_code = step1.data.url


        const step2 = await axios({
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://visafoto.com/api/photoDone?z=${hex_string}&time=${Date.now()}`,
            headers: {
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'x-requested-with': 'XMLHttpRequest',
            }
        })
        console.log(step2.data)
        console.log('STEP 2: https://visafoto.com/preview?i=' + image_code)
        const step3 = await axios({
            method: "get",
            url: 'https://visafoto.com/preview?i=' + image_code,
            responseType: "arraybuffer"
        })
        console.log(new Date())
        data = new FormData();
        data.append('file', step3.data, 'test2.png');
        data.append('path', '');
        data.append('filenameOverride', 'true');
        const sign_headers = sign_header("946781")
        const step4 = await axios({
            method: 'POST',
            maxBodyLength: Infinity,
            url: 'https://api.pixelbin.io/service/panel/assets/v1.0/org/946781/upload/direct',
            headers: {
                'x-ebg-signature': sign_headers['x-ebg-signature'],
                'x-ebg-param': sign_headers['x-ebg-param'],
                "cookie": "s:Belpbdp4rchCXQNFMK_zVl-rqbcxsYhL.2yhXBQIy6dff+DY2KWN8gAVAqLu9MEJg/gkq/U+1q3E",
                'Referer': 'https://console.pixelbin.io/',
                'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                "Content-Type": `multipart/form-data; boundary=${data._boundary}`,
            },
            data: data
        })

        const step4_image = step4.data.url
        console.log('STEP 4: ' + step4_image)

        const step5_url = step4_image.replace('original', 'wm.remove()') + '?preview=true'
        console.log('STEP 5: ' + step5_url)
        // await new Promise(resolve => setTimeout(resolve, 2000))
        const step5 = await axios({
            method: "get",
            url: step5_url,
            responseType: "arraybuffer"
        })
        console.log(new Date())
        return step5.data

    } catch (e) {
        console.log(e.response.statusText)
        return null
    }

}

function detectImageType(width, height) {
    const aspectRatios = {
        '2x2': 1, // 2x2
        '3x4': 0.75, // 3x4
        '4x6': 0.67 // 4x6
    };

    const aspectRatio = width / height;

    let imageType = "4x6";
    for (const type in aspectRatios) {
        if (Math.abs(aspectRatio - aspectRatios[type]) < 0.05) {
            imageType = type;
            break;
        }
    }

    // return the detected image type, or null if not found
    return imageType;
}

const imageToRemovedImageV7 = async function (buff, image_size) {
    const document = {
        "2x2": "19332",
        "4x6": "16479",
        "3x4": "19716",
        "passport_online": "23218",
    }
    var data = new FormData();
    data.append('UploadForm[documentTranslationId]', document[image_size] || "19332");
    data.append('UploadForm[language]', "en-US");
    data.append('UploadForm[appName]', "2");
    data.append('UploadForm[imageFile]', buff, "file.png");
    console.log("Start", new Date())

    const step1_config = {
        method: 'post',
        url: `https://api.photoaid.com/api/upload?web=1`,
        headers: {
            'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            ...data.getHeaders()
        },
        data: data,
    };

    try {
        const step1 = await axios(step1_config)

        const photo_key = step1.data.photoKey
        const step1_url = "https://photoaid.com/get-photo/result?photoKey=" + photo_key
        await new Promise(r => setTimeout(r, 500))
        const step2 = await axios({
            method: "get",
            url: step1_url,
            responseType: "arraybuffer",
        })

        return {
            buff: step2.data,
            errors: {}
        }
    } catch (e) {
        console.log(e.message)
        return null
    }

}

const imageToRemovedImageV71 = async function (buff, image_size) {
    const step1 = await axios.get(`https://removal.ai/upload/`)
    console.log(step1.data)
    const securityMatch = step1.data.match(/var ajax_upload_object = .*?"security":"([^"]+)".*?}/);
    const securityCode = securityMatch ? securityMatch[1] : null;
    console.log('Security code:', securityCode);
    const step2 = await axios.get(`https://removal.ai/wp-admin/admin-ajax.php?action=ajax_get_webtoken&security=` + securityCode)

    let data = new FormData();
    data.append('image_file', buff, 'file.jpg');

    const step3 = await axios({
        method: 'post',
        url: 'https://api.removal.ai/3.0/remove',
        headers: {
            'referer': 'https://removal.ai/upload/',
            'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            ...data.getHeaders(),
            'web-token': step2.data.data.webtoken
        },
        data: data
    });

    const step4 = await axios.get(step3.data.url, { responseType: 'arraybuffer' })
    return imageToRemovedImageV7(step4.data, image_size)
}



const imageToRemovedImageV8 = async function (buff, image_size) {
    const document = {
        "2x2": "ab117e33-3cf3-4a09-adce-a97539359dd0|8bab382f-33a8-4102-8a60-e752341ef279",
        "4x6": "b91b92e3-6ed2-4c64-b183-12041a59b182|f3496a37-e446-425e-9b55-3e777188462d",
        "3x4": "b91b92e3-6ed2-4c64-b183-12041a59b182|e61fe480-82ec-4016-9441-1dea5bf803c4",
        "passport_online": "b91b92e3-6ed2-4c64-b183-12041a59b182|f3496a37-e446-425e-9b55-3e777188462d",
    }

    try {
        const step1 = await axios({
            method: 'post',
            url: 'https://photogov.net/api/v1/orders/create-order',
            data: {
                "selected_country_id": document[image_size].split('|')[0],
                "selected_document_id": document[image_size].split('|')[1],
            }
        })

        const base64Image = buff.toString('base64');
        const chunkSize = 1000000;
        const totalChunks = Math.ceil(base64Image.length / chunkSize);

        for (let i = 0; i < totalChunks; i++) {
            const chunk = base64Image.slice(i * chunkSize, (i + 1) * chunkSize);
            await axios({
                method: 'post',
                url: 'https://photogov.net/api/v1/widget/upload-photo',
                data: {
                    "order_id": step1.data.data.id,
                    "chunk_number": i + 1,
                    "chunk_all": totalChunks,
                    "data": [chunk]
                }
            });
        }

        let photo_key = ''
        let errors = {}
        const startTime = Date.now();
        while (!photo_key && (Date.now() - startTime) < 30000) {
            const resp3 = await axios({
                method: 'post',
                url: 'https://photogov.net/api/v1/widget/check-photo-processing',
                data: { order_id: step1.data.data.id, }
            });
            photo_key = resp3.data.data.processed_photo;
            if (!photo_key) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            } else {
                const resp3Err = JSON.parse(resp3.data.data.errors)
                errors = {
                    is_body_cut: resp3Err.isBodyCutted || false,
                    is_head_cut: resp3Err.isHeadCutted || false,
                    is_shoulder_cut: resp3Err.isShouldersCutted || false,
                    is_image_too_small: resp3Err.isResultImageTooSmall || false,
                }
            }

        }

        const step1_url = "https://photogov.net" + photo_key
        await new Promise(r => setTimeout(r, 500))
        const step4 = await axios({
            method: "get",
            url: step1_url,
            responseType: "arraybuffer",
        })

        return {
            buff: step4.data,
            errors
        }

    } catch (e) {
        console.log(e.message)
        return null
    }

}

module.exports = {
    imageToRemovedImageV1,
    imageToRemovedImageV2,
    imageToRemovedImageV3,
    imageToRemovedImageV4,
    imageToRemovedImageV5,
    imageToRemovedImageV6,
    imageToRemovedImageV7,
    imageToRemovedImageV71,
    imageToRemovedImageV8,
    detectImageType,
}