const { ApiGatewayV2 } = require('aws-sdk');
const axios = require('axios');
const jimp = require('jimp');
const { v4 } = require('uuid');
const { s3_upload, s3_signed_url, s3_to_bucket_key, s3_get_buffer } = require('../models/s3');
const aws_s3 = JSON.parse(process.env.ad_s3);

const aws_config = JSON.parse(process.env.ad_aws)
console.log(aws_s3)
console.log(aws_config)
const REGION = aws_config.region;
const AWS = require('aws-sdk');

AWS.config.region = REGION;
const rekognition = new AWS.Rekognition({ region: REGION });

const autoRotate = async (req, res) => {
    try {
        console.log("PROCESSING IMAGE:", req.body.image)

        // Image to url
        const { bucket, key } = s3_to_bucket_key(req.body.image)


        console.log('image_rotate start', new Date())
        const image_rotate = await get_image_orientation(bucket, key)
        console.log('image_rotate end', new Date())
        // 180 => left => -90
        //

        const buffer = await s3_get_buffer(bucket, key)

        // Image url to base64
        // const buffer = await axios({
        //     method: 'get',
        //     url: req.body.image,
        //     responseType: 'arraybuffer'
        // })


        const image_src = await jimp.read(buffer)
        const image_desc = image_src.rotate(-image_rotate)
        const image_desc_buff = await image_desc.quality(60).getBufferAsync(jimp.MIME_PNG)


        if (req.query.debug) {
            res.set("Content-Type", jimp.MIME_PNG);
            res.send(image_desc_buff);
            return
        }

        const uuid = v4();

        console.log('s3_upload start', new Date())
        await s3_upload(
            aws_s3.ariadirect_prod_passport_images,
            `auto-rotate/${uuid}.png`,
            jimp.MIME_PNG,
            image_desc_buff
        )
        console.log('s3_upload end', new Date())

        const url_full = s3_signed_url(aws_s3.ariadirect_prod_passport_images, `auto-rotate/${uuid}.png`)
        console.log(new Date())
        console.log('s3_signed_url end', new Date())
        res.json({
            success: true,
            data: {
                url: url_full
            }
        })
    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: "fail",
            error: error
        })
    }
}

const get_image_orientation = async (bucket, key) => {
    const faces = await new Promise(r => rekognition.detectFaces({
        Image: {
            S3Object: {
                Bucket: bucket,
                Name: key,
            }
        },
        Attributes: ["ALL"]
    }, (err, data) => {
        r(data)
    }))

    const face = faces.FaceDetails[0]


    const land_marks = face.Landmarks;
    const eye_left = land_marks.find(x => x.Type === 'eyeLeft');
    const eye_right = land_marks.find(x => x.Type === 'eyeRight');
    const middle_eye = { X: (eye_left.X + eye_right.X) / 2, Y: (eye_left.Y + eye_right.Y) / 2 }

    const mouth_left = land_marks.find(x => x.Type === 'mouthLeft');
    const mouth_right = land_marks.find(x => x.Type === 'mouthRight');
    const middle_mouth = { X: (mouth_left.X + mouth_right.X) / 2, Y: (mouth_left.Y + mouth_right.Y) / 2 }

    let face_angle = find_angle({ X: middle_mouth.X, Y: 0 }, middle_mouth, middle_eye)

    const MIN_ANGLE = 20

    if (face_angle.between(-MIN_ANGLE, MIN_ANGLE)) {
        face_angle = 0
    }

    if (face_angle.between(90 - MIN_ANGLE, 90 + MIN_ANGLE)) {
        face_angle = 90
    }

    if (face_angle.between(-90 - MIN_ANGLE, -90 + MIN_ANGLE)) {
        face_angle = -90
    }

    if (face_angle.between(180 - MIN_ANGLE, 180)) {
        face_angle = 180
    }
    return face_angle
}

Number.prototype.between = function (a, b) {
    var min = Math.min(a, b),
        max = Math.max(a, b);

    return this > min && this < max;
};

function find_angle(A, B, C) {
    var AB = Math.sqrt(Math.pow(B.X - A.X, 2) + Math.pow(B.Y - A.Y, 2));
    var BC = Math.sqrt(Math.pow(B.X - C.X, 2) + Math.pow(B.Y - C.Y, 2));
    var AC = Math.sqrt(Math.pow(C.X - A.X, 2) + Math.pow(C.Y - A.Y, 2));

    let angle = Math.acos((BC * BC + AB * AB - AC * AC) / (2 * BC * AB)) * (180 / Math.PI)
    console.log(angle)
    console.log(A, B, C)
    if (B.X < C.X) {
        angle = -angle
    }
    return angle
}

module.exports = {
    autoRotate,
}