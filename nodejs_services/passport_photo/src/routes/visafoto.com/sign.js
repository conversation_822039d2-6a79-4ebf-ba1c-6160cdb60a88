const sha256 = require("crypto-js/sha256");
const hmacSHA256 = require("crypto-js/hmac-sha256");

function hmac(key, string, encoding) {
    return hmacSHA256(string, key).toString();
}
function hash(string, encoding) {
    return sha256(string).toString();
}

function trimAll(header) {
    return header.toString().trim().replace(/\s+/g, " ");
}

function base64Encode(text) {
    return Buffer.from(text).toString("base64");
}


// 20230420T092130Z
const sign_header = (org_id) => {
    const now = new Date();
    const date_time = now.toISOString().replace(/[:\-]|\.\d{3}/g, "");
    console.log(date_time);
    // const date_time = "20230420T105810Z"

    const headers = {
        "Host": "api.pixelbin.io",
        "x-ebg-param": date_time
    }

    const canonicalHeaders = Object.keys(headers).sort(function (a, b) {
        return a.toLowerCase() < b.toLowerCase() ? -1 : 1;
    }).map(function (key) {
        return key.toLowerCase() + ":" + trimAll(headers[key]);
    })
        .join("\n");

    const signedHeaders = Object.keys(headers).map(function (key) {
        return key.toLowerCase();
    }).sort().join(";");

    let bodyHash = hash("", "hex");

    const canonicalReq = [
        "POST",
        `/service/panel/assets/v1.0/org/${org_id}/upload/direct`,
        "",
        canonicalHeaders + "\n",
        signedHeaders,
        bodyHash,
    ].join("\n");


    const stringToSign = [date_time, hash(canonicalReq, "hex")].join("\n");

    let kCredentials = "1234567";
    return {
        "x-ebg-param": base64Encode(date_time),
        "x-ebg-signature": `v1:${hmac(kCredentials, stringToSign, "hex")}`
    }
}

module.exports = { sign_header }