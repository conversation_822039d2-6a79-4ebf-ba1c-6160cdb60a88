const axios = require('axios');
const fs = require('fs');
const { v4 } = require('uuid');

let IMAGES = [
    "https://ariadirect-jp-files.s3.amazonaws.com/7d4d03a8a11903e08d2c3885a2a366cf/2025-04-28-14-42-1-.png"
];
let STORE_NUMBER = "3445"
let PROMISE_TIME = "05-02-2025 8:20 AM";

const step1 = async () => {
    const config1 = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://api.ariadirect.com/v1/passport-photo/walgreen-nearby-stores',
        headers: {
            'content-type': 'application/json',
            'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        },
        data: {
            "address": null
        }
    }
    const resp = await axios(config1)
    console.log(JSON.stringify(resp.data, null, 2))
}
const step2 = async () => {
    let index = 0
    for (const IMAGE of IMAGES) {
        const config1 = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://api.ariadirect.com/v1/passport-photo/print-photo',
            headers: {
                'content-type': 'application/json',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            },
            data: {
                "image": IMAGE,
                "name": "Order#4000 2x2", // 192
                "width": 170,
                "height": 170,
            }
        }
        const resp1 = await axios(config1)
        console.log(resp1.data.data.url)
        const resp1_buffer = await axios.get(resp1.data.data.url, { responseType: 'arraybuffer' })
        fs.writeFileSync(`${++index}.png`, resp1_buffer.data)
        const config2 = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://api.ariadirect.com/v1/passport-photo/walgreen-submit-order',
            headers: {
                'content-type': 'application/json',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            },
            data: {
                "order_id": 4000,
                "store_number": STORE_NUMBER,
                "promise_time": PROMISE_TIME,
                "image": resp1.data.data.url
            }
        }
        const resp2 = await axios(config2)
        console.log(JSON.stringify(resp2.data))
    }
}

// step1();
step2();
module.exports = {}