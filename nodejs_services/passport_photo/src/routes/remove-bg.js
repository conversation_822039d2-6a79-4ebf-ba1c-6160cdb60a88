const fs = require("fs");
const mergeImg = require('merge-img');
const jimp = require('jimp');
const axios = require('axios');
const path = require('path');
const url = require('url');
const color_parse = require('parse-color');
const FormData = require('form-data');
const { s3_upload, s3_signed_url, s3_copy, s3_to_bucket_key } = require('../models/s3');
const { smartCrop } = require('./smart-crop');
const { v4 } = require('uuid');
const aws_s3 = JSON.parse(process.env.ad_s3);
const { detectImageType, imageToRemovedImageV7, imageToRemovedImageV8, imageToRemovedImageV71 } = require('./remove-bg-core');


async function trimImageFromBottom(src) {
    // Read the image buffer using Jimp
    const image = await jimp.read(src);

    // Get the width and height of the image
    const width = image.getWidth();
    const height = image.getHeight();

    let trimHeight = 0;

    // Iterate from the bottom of the image
    for (let y = height - 1; y >= 0; y--) {
        let isSameColor = true;

        // Check if all points in the line have the same color
        for (let x = 0; x < width; x++) {
            const pixelColor = image.getPixelColor(x, y);

            // Compare the color of the current pixel with the previous one
            if (y < height - 1 && pixelColor !== image.getPixelColor(x, y + 1)) {
                isSameColor = false;
                break;
            }
        }

        // If all points in the line have the same color, increment the trim height
        if (isSameColor) {
            trimHeight++;
        } else {
            // Exit the loop if a different color is found
            break;
        }
    }

    image.crop(0, 0, width, height - trimHeight);

    return image;
}

const removeBackgroundToS3 = async (req, res) => {
    try {
        console.log("PROCESSING IMAGE:", req.body.image)

        const color = color_parse(req.body.bg || '#ffffff')
        if (!color.rgba) {
            res.json({
                success: false,
                message: "invalid background color",
            })
            return
        }


        // const url_parsed = url.parse(req.body.image)
        // const file_name = path.basename(url_parsed.pathname)

        const stream = await axios({
            method: "get",
            url: req.body.image,
            responseType: "arraybuffer"
        })
        // const tempFilePath = file_name
        // fs.writeFileSync(tempFilePath, stream.data)

        console.log("Start: remove background")
        const { width, height } = req.body
        let image_type = detectImageType(width, height)

        const order = await get_order_detail(req.body.order_id)

        if (order?.service?.name?.startsWith("Renew US Passport")) {
            image_type = "passport_online"
        }

        let { buff, errors } = await imageToRemovedImageV7(stream.data, image_type);
        console.log("End: remove background")

        // buff = await cropDiagonalWhiteLine(buff)
        if (!buff) {
            if (req.body.service_type) {
                await ad_request_callback({
                    url: `${JSON.parse(process.env.ad_endpoint).api_base_url}/v1/pkg/internal/update-id-photos`,
                    data: {
                        app_id: req.body.app_id,
                        service_type: req.body.service_type,
                        image: req.body.image,
                    },
                    headers: {
                        "x-ad-token": JSON.parse(process.env.ad_api_token).token,
                    }
                })
            }

            res.json({
                data: {
                    url: req.body.image,
                    url_demo: req.body.image,
                    valid_photo: false
                },
                success: true,
            })
        }
        // buff = await smartCrop(buff, req.body.width || 300, req.body.height || 400, req.body.head_zoom || 0)
        // if (!buff) {
        //     res.json({
        //         success: false,
        //         message: "smart crop fail",
        //     })
        //     return
        // }

        // if (req.query.debug) {
        //     res.set("Content-Type", jimp.MIME_PNG);
        //     res.send(buff);
        //     return
        // }

        // let merged = await trimImageFromBottom(buff);

        // Replace color
        // merged.scan(0, 0, merged.bitmap.width, merged.bitmap.height, (x, y, idx) => {
        //     if (merged.bitmap.data[idx + 3] == 0) {
        //         merged.bitmap.data[idx + 0] = color.rgba[0]
        //         merged.bitmap.data[idx + 1] = color.rgba[1]
        //         merged.bitmap.data[idx + 2] = color.rgba[2]
        //         merged.bitmap.data[idx + 3] = 255
        //     }
        // })

        // buff = await merged.getBufferAsync(jimp.MIME_JPEG)


        if (req.query.debug2) {
            res.set("Content-Type", jimp.MIME_JPEG);
            res.send(buff);
            return
        }

        const uuid = v4();

        await s3_upload(aws_s3.ariadirect_prod_passport_images, `removebg-passport/${uuid}.jpg`, jimp.MIME_JPEG, buff)

        // let demo = merged

        // const watermark = await jimp.read("demo.png")
        // let base_x = demo.bitmap.width / 5
        // watermark.resize(base_x, jimp.AUTO);
        // for (let i = 10; i < demo.bitmap.width; i += (base_x + 10)) {
        //     for (let j = 10; j < demo.bitmap.height; j += 100) {
        //         demo.composite(watermark, i, j, {
        //             mode: jimp.BLEND_SOURCE_OVER,
        //             opacityDest: 1,
        //             opacitySource: 0.15
        //         });
        //     }
        // }

        // const buff_demo = await demo.getBufferAsync(jimp.MIME_JPEG)
        // await s3_upload(aws_s3.ariadirect_prod_passport_images, `removebg-passport/${uuid}-demo.jpg`, jimp.MIME_JPEG, buff_demo)

        // Save source file
        {
            s3_upload(aws_s3.ariadirect_prod_passport_images, `removebg-passport/${uuid}-full.jpg`, jimp.MIME_JPEG, stream.data)
        }


        // let url_demo = s3_signed_url(aws_s3.ariadirect_prod_passport_images, `removebg-passport/${uuid}-demo.jpg`)
        let url_resized = `${aws_s3.ariadirect_prod_passport_images}/removebg-passport/${uuid}.jpg`



        url_resized = s3_signed_url(aws_s3.ariadirect_prod_passport_images, `removebg-passport/${uuid}.jpg`)


        if (req.body.service_type) {
            await ad_request_callback({
                url: `${JSON.parse(process.env.ad_endpoint).api_base_url}/v1/pkg/internal/update-id-photos`,
                data: {
                    app_id: req.body.app_id,
                    service_type: req.body.service_type,
                    image: url_resized,
                },
                headers: {
                    "x-ad-token": JSON.parse(process.env.ad_api_token).token,
                }
            })
        }

        console.log("RESULT IMAGE: ", url_resized)

        if (url_resized === null) {
            res.json({
                success: false,
                message: "upload s3 error, please try again",
            })
            return
        }
        res.json({
            data: {
                url: url_resized,
                url_demo: url_resized,
                valid_photo: !Object.keys(errors).some(v => errors[v] === true),
                valid_errors: errors,
            },
            success: true,
        })


    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: "fail",
            error: error
        })
    }
}


const removeBackground = async (req, res) => {
    try {
        const color = color_parse(req.query.bg || '#ffffff')
        if (!color.rgba) {
            res.json({
                success: false,
                message: "invalid background color",
            })
            return
        }
        const parse = await imageToMask(req.files.file)

        const image = await jimp.read(parse.src_data);
        const mask = await jimp.read(parse.mask_data);

        let merged = image.mask(mask)

        merged.scan(0, 0, merged.bitmap.width, merged.bitmap.height, (x, y, idx) => {
            if (merged.bitmap.data[idx + 3] == 0) {
                merged.bitmap.data[idx + 0] = color.rgba[0]
                merged.bitmap.data[idx + 1] = color.rgba[1]
                merged.bitmap.data[idx + 2] = color.rgba[2]
                merged.bitmap.data[idx + 3] = 255
            }
        })

        const watermark = await jimp.read("demo2.png")
        let base_x = merged.bitmap.width / 5
        watermark.resize(base_x, jimp.AUTO);

        for (let i = 10; i < merged.bitmap.width; i += (base_x + 10)) {
            for (let j = 10; j < merged.bitmap.height; j += 100) {
                merged.composite(watermark, i, j, {
                    mode: jimp.BLEND_SOURCE_OVER,
                    opacityDest: 1,
                    opacitySource: 0.1
                });
            }

        }

        merged.getBuffer(jimp.MIME_PNG, function (err, buff) {
            res.set("Content-Type", jimp.MIME_PNG);
            res.send(buff);
        })


    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: "fail",
        })
    }
}

const imageToMask = async function (file) {
    let data = new FormData();
    data.append('image', fs.createReadStream(file.tempFilePath), file.name);
    try {
        const step1_config = {
            method: 'post',
            url: 'https://photoscissors.com/upload',
            headers: {
                'x-requested-with': 'XMLHttpRequest',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36',
                'origin': 'https://photoscissors.com',
                "Content-Type": "multipart/form-data",
                ...data.getHeaders()
            },
            data: data
        };


        const step1 = await axios(step1_config)
        fs.unlinkSync(file.tempFilePath)

        const step2_config = {
            method: 'get',
            url: 'https://var.photoscissors.com/images/' + step1.data.secret + '/alpha.png',
            headers: {},
            responseType: 'arraybuffer'
        };

        const step2 = await axios(step2_config)
        console.log("STEP2");

        const step3_config = {
            method: 'get',
            url: 'https://var.photoscissors.com/images/' + step1.data.secret + '/image.jpg',
            headers: {},
            responseType: 'arraybuffer'
        };

        const step3 = await axios(step3_config)
        console.log("STEP3");
        return {
            mask_data: step2.data,
            src_data: step3.data,
        }

    } catch (error) {
        console.log(error);
    }
}

const ad_request_callback = async function ({ url, data, headers }) {
    console.log("CALLBACK TO AD", url, data, headers)
    const response = await axios({
        method: 'post',
        url: url,
        data: data,
        headers: headers
    })
    return response.data
}

const get_order_detail = async function (order_id) {
    const api_token = JSON.parse(process.env.ad_api_token)
    const api_base = JSON.parse(process.env.ad_endpoint).api_base_url
    const url = `${api_base}/v1/pkg/internal/orders/${order_id}`

    const response = await axios({
        method: 'get',
        url: url,
        headers: {
            "x-ad-token": api_token.token
        }
    })
    return response?.data?.data || null
}



module.exports = {
    removeBackground,
    removeBackgroundToS3,
}