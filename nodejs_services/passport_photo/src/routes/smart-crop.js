const fs = require("fs");
const mergeImg = require('merge-img');
const axios = require('axios');
const color_parse = require('parse-color');
const FormData = require('form-data');
const AWS = require('aws-sdk');
const jimp = require('jimp');

const smartCrop = async (buff, width, height, head_zoom) => {
    try {
        const img = await jimp.read(buff);
        const temp_image = "temp.png"
        let new_width_resize = 640
        if (img.bitmap.width < 640) {
            new_width_resize = img.bitmap.width
        }
        const img_resized = img.resize(new_width_resize, jimp.AUTO).quality(60).write(temp_image)

        const img_resized_buffer = await img_resized.getBufferAsync(jimp.MIME_PNG);

        var rekognition = new AWS.Rekognition({ region: "ap-northeast-1" });
        var params = {
            Image: {
                Bytes: img_resized_buffer,
            },
            Attributes: [
                'ALL',
            ]
        };


        let faces = await new Promise(r => {
            rekognition.detectFaces(params, function (err, data) {
                if (err) {
                    console.log(err, err.stack); // an error occurred
                    throw err;
                }
                else {
                    r(data.FaceDetails)
                }
            });
        });


        for (let i = 0; i < faces.length; i++) {
            for (let i = 0; i < faces.length; i++) {
                try {
                    let rate = 4 / 3;
                    if (height && width) {
                        rate = parseFloat(height) / parseFloat(width);
                    }

                    const { Left, Top, Width, Height } = faces[i].BoundingBox;
                    const { width: image_width, height: image_height } = img.bitmap;

                    // Calculate crop dimensions
                    // let cropWidth = Width * image_width * 2;
                    // let cropHeight = cropWidth * rate;

                    let cropHeight = Height * image_height * (2.1 - head_zoom);
                    if (Math.abs(rate - 1) < 0.1) // 2x2
                        cropHeight = Height * image_height * (1.9 - head_zoom)

                    cropHeight = Height * image_height * 1.6
                    let cropWidth = cropHeight / rate;

                    // Calculate crop coordinates
                    let cropX = Math.max(Left * image_width - (cropWidth - Width * image_width) / 2, 0);
                    let cropY = Math.max(Top * image_height - (cropHeight - Height * image_height) / 2, 0);


                    // Check if crop values are out of range
                    if (cropX < 0) {
                        cropWidth += cropX;
                        cropX = 0;
                    }
                    if (cropY < 0) {
                        cropHeight += cropY;
                        cropY = 0;
                    }
                    if (cropX + cropWidth > image_width) {
                        cropWidth = image_width - cropX;
                    }
                    if (cropY + cropHeight > image_height) {
                        cropHeight = image_height - cropY;
                    }

                    img.crop(
                        cropX,
                        cropY,
                        cropWidth,
                        cropHeight,
                    );

                    const buffer = await img.getBufferAsync(jimp.MIME_PNG);
                    return buffer;
                } catch (error) {
                    console.log(error.message);
                    return null;
                }
            }
        }
    } catch (error) {
        console.log(error);
        return null
    }
}

module.exports = {
    smartCrop
}