const axios = require('axios');
const jimp = require('jimp');
const fs = require('fs');
const { s3_upload, s3_signed_url } = require('../models/s3');
const aws_s3 = JSON.parse(process.env.ad_s3);
const { v4 } = require('uuid');
const url = require('url');
const path = require('path');

const printPhoto = async (req, res) => {
    try {
        const stream = await axios({
            method: "get",
            url: req.body.image,
            responseType: "arraybuffer"
        })

        let photo_name = "AriaDirect Photo"
        if (req.body.name) {
            photo_name = req.body.name
        }
        photo_name += " - VietNam Visa"

        const image_background = await jimp.read(__dirname + '/../data/white_background_4x6.png'); // 1600x2400 , 4x6
        const image_source = await jimp.read(stream.data);

        let image_single_photo = image_source.resize(800, 800)
        let photo_per_image = 2
        if (req.body.width && req.body.height) {
            let x = 1600 / 4 * req.body.width / 96 // 1 inch = 96px
            let y = 1600 / 4 * req.body.height / 96 // 1 inch = 96px
            if (x > 800) {
                photo_per_image = 1
            }

            image_single_photo = image_source.resize(parseInt(x), parseInt(y))
        }

        // image_single_photo = addBorder(image_single_photo)
        const font = await jimp.loadFont(jimp.FONT_SANS_64_BLACK)

        const font_width = jimp.measureText(font, photo_name)
        let image_merged = image_background.
            print(font, 800 - font_width / 2, 200, photo_name).
            composite(image_single_photo, (1600 - image_single_photo.bitmap.width) / 2, 500)

        if (photo_per_image === 2) {
            image_merged = image_merged.composite(image_single_photo, (1600 - image_single_photo.bitmap.width) / 2, 500 + 100 + 800) // image height: 800 + 50x2 margin
        }

        const buff = await image_merged.getBufferAsync(jimp.MIME_PNG)

        const uuid = v4();
        const key = `print-passport-photo/${uuid}.png`
        await s3_upload(
            aws_s3.ariadirect_prod_passport_images,
            key,
            jimp.MIME_PNG,
            buff
        )

        res.json({
            success: true,
            data: {
                url: s3_signed_url(aws_s3.ariadirect_prod_passport_images, key),
            },
        })
    } catch (error) {
        res.json({
            success: false,
            message: error.message,
        })
    }

}

function addBorder(img) {
    const color = 0xED143DFF
    img.scan(0, 0, img.bitmap.width, 5, function (x, y, offset) {
        if (x % 5 <= 1)
            this.bitmap.data.writeUInt32BE(color, offset, true);
    }); // top
    img.scan(0, img.bitmap.height - 5, img.bitmap.width, 5, function (x, y, offset) {
        if (x % 5 <= 1)
            this.bitmap.data.writeUInt32BE(color, offset, true);
    }); // bottom

    img.scan(0, 0, 5, img.bitmap.height, function (x, y, offset) {
        if (y % 5 <= 1)
            this.bitmap.data.writeUInt32BE(color, offset, true);
    }); // left
    img.scan(img.bitmap.width - 5, 0, 5, img.bitmap.height, function (x, y, offset) {
        if (y % 5 <= 1)
            this.bitmap.data.writeUInt32BE(color, offset, true);
    }); // right
    return img;
}



module.exports = {
    printPhoto
}