const axios = require('axios');
const Jimp = require('jimp');
const AWS = require('aws-sdk');

const aws_config = JSON.parse(process.env.ad_aws)
const rekognition = new AWS.Rekognition({ region: aws_config.region });
const ANTHROPIC_API_KEY = '************************************************************************************************************';
const photoCheckList = async function (req, res, next) {
    const { photo_image, passport_image } = req.body

    try {
        if (photo_image && passport_image) {
            // Get both images
            const [photoStream, passportStream] = await Promise.all([
                axios({
                    method: "get",
                    url: photo_image,
                    responseType: "arraybuffer"
                }),
                axios({
                    method: "get",
                    url: passport_image,
                    responseType: "arraybuffer"
                })
            ]);

            // Convert images to JPEG using Jimp
            const [photoJimp, passportJimp] = await Promise.all([
                Jimp.read(photoStream.data),
                Jimp.read(passportStream.data)
            ]);

            // Resize images to width 800px, height auto
            const [resizedPhotoJimp, resizedPassportJimp] = await Promise.all([
                photoJimp.resize(800, Jimp.AUTO),
                passportJimp.resize(800, Jimp.AUTO)
            ]);

            // Get buffers in JPEG format
            const [photoBuffer, passportBuffer] = await Promise.all([
                resizedPhotoJimp.getBufferAsync(Jimp.MIME_JPEG),
                resizedPassportJimp.getBufferAsync(Jimp.MIME_JPEG)
            ]);

            // Compare faces using AWS Rekognition
            const compareFacesResponse = await rekognition.compareFaces({
                SourceImage: { Bytes: photoBuffer },
                TargetImage: { Bytes: passportBuffer },
                SimilarityThreshold: 80
            }).promise();

            const match = compareFacesResponse.FaceMatches.length > 0;
            const similarity = match ? compareFacesResponse.FaceMatches[0].Similarity : 0;

            // Convert to base64 for Claude API
            const photoBase64 = photoBuffer.toString('base64');
            const passportBase64 = passportBuffer.toString('base64');

            // Make request to Claude API for other attributes
            const claudeResponse = await axios({
                method: 'post',
                url: 'https://api.anthropic.com/v1/messages',
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': ANTHROPIC_API_KEY,
                    'anthropic-version': '2023-06-01'
                },
                data: {
                    model: "claude-3-sonnet-20240229",
                    max_tokens: 1024,
                    messages: [{
                        role: "user",
                        content: [
                            {
                                type: "text",
                                text: `Analyze these two passport photos and respond ONLY with a JSON object using this exact format, no other text:\n\n{\n  \"looking_straight\": boolean,\n  \"no_shadows\": boolean,\n  \"balanced_shoulders\": boolean,\n  \"face_uncovered\": boolean,\n  \"correct_distance\": boolean,\n  \"clear_image\": boolean,\n  \"neutral_expression\": boolean\n}\n\nCriteria:\n1. looking_straight: Is the person looking directly at the camera?\n2. no_shadows: Are there any shadows on the face? (true = no shadows)\n3. balanced_shoulders: Are the shoulders level and balanced?\n4. face_uncovered: (no convered by mask or glasses) ?\n5. correct_distance: Is the face at an appropriate distance from the camera?\n6. clear_image: Is the image clear and not blurry?\n7. neutral_expression: If the user's photo shows excessively intense emotional expression, return false?`
                            },
                            {
                                type: "image",
                                source: {
                                    type: "base64",
                                    media_type: "image/jpeg",
                                    data: photoBase64
                                }
                            },
                            {
                                type: "image",
                                source: {
                                    type: "base64",
                                    media_type: "image/jpeg",
                                    data: passportBase64
                                }
                            }
                        ]
                    }]
                }
            });

            let analysis;
            try {
                const jsons = extract_json(claudeResponse.data.content[0].text);
                analysis = jsons[0]
            } catch (parseError) {
                console.error('Failed to parse Claude response:', claudeResponse.data.content[0].text);
                throw new Error('Invalid response format from Claude');
            }

            const data = {
                "passport_photo_match": match,
                "similarity_score": similarity,
                "look_straight_ahead": analysis.looking_straight || false,
                "no_shadow": analysis.no_shadows || false,
                "balance_shoulder": analysis.balanced_shoulders || false,
                "face_not_covered": analysis.face_uncovered || false,
                "face_distance": analysis.correct_distance || false,
                "not_blur": analysis.clear_image || false,
                "neutral_face": analysis.neutral_expression || false
            }

            res.json({
                success: true,
                data: data
            });
            return
        } else if (photo_image) {
            // Single photo analysis logic remains unchanged
            const photoStream = await axios({
                method: "get",
                url: photo_image,
                responseType: "arraybuffer"
            });

            const photoJimp = await Jimp.read(photoStream.data);
            const resizedPhotoJimp = photoJimp.resize(800, Jimp.AUTO)

            const photoBuffer = await resizedPhotoJimp.getBufferAsync(Jimp.MIME_JPEG);
            const photoBase64 = photoBuffer.toString('base64');

            const claudeResponse = await axios({
                method: 'post',
                url: 'https://api.anthropic.com/v1/messages',
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': ANTHROPIC_API_KEY,
                    'anthropic-version': '2023-06-01'
                },
                data: {
                    model: "claude-3-sonnet-20240229",
                    max_tokens: 1024,
                    messages: [{
                        role: "user",
                        content: [
                            {
                                type: "text",
                                text: `Analyze this photo and respond ONLY with a JSON object using this exact format, no other text:{\n  \"looking_straight\": boolean,\n  \"no_shadows\": boolean,\n  \"balanced_shoulders\": boolean,\n  \"face_uncovered\": boolean,\n  \"correct_distance\": boolean,\n  \"clear_image\": boolean,\n  \"neutral_expression\": boolean\n}\n\nCriteria:\n1. looking_straight: Is the person looking directly at the camera?\n2. no_shadows: Are there any shadows on the face? (true = no shadows)\n3. balanced_shoulders: Are the shoulders level and balanced?\n4. face_uncovered: (no convered by mask or glasses) ?\n5. correct_distance: Is the face at an appropriate distance from the camera?\n6. clear_image: Is the image clear and not blurry?
                                7. neutral_expression: If the user's photo shows excessively intense emotional expression, return false?`
                            },
                            {
                                type: "image",
                                source: {
                                    type: "base64",
                                    media_type: "image/jpeg",
                                    data: photoBase64
                                }
                            }
                        ]
                    }]
                }
            });

            let analysis;
            try {
                const jsons = extract_json(claudeResponse.data.content[0].text);
                analysis = jsons[0];
            } catch (parseError) {
                console.error('Failed to parse Claude response:', claudeResponse.data.content[0].text);
                throw new Error('Invalid response format from Claude');
            }

            const data = {
                "look_straight_ahead": analysis.looking_straight || false,
                "no_shadow": analysis.no_shadows || false,
                "balance_shoulder": analysis.balanced_shoulders || false,
                "face_not_covered": analysis.face_uncovered || false,
                "face_distance": analysis.correct_distance || false,
                "not_blur": analysis.clear_image || false,
                "neutral_face": analysis.neutral_expression || false
            }

            res.json({
                success: true,
                data: data
            });
        } else {
            res.json({
                success: false,
                error_message: "No photo provided"
            });
        }
    } catch (error) {
        console.error('Error:', error);
        res.json({
            success: false,
            error_message: error.message || "Error processing images"
        });
    }
}

const extract_json = (str) => {
    if (!str) return null;

    const result = [];

    for (let i = 0; i < str.length; i++) {
        if (str[i] === '{' || str[i] === '[') {
            let start = i;
            let depth = 1;

            for (let j = i + 1; j < str.length; j++) {
                if (str[j] === '{' || str[j] === '[') {
                    depth++;
                } else if (str[j] === '}' || str[j] === ']') {
                    depth--;
                }

                if (depth === 0) {
                    const potential = str.substring(start, j + 1);
                    try {
                        const validJSON = JSON.parse(potential);
                        result.push(validJSON);
                    } catch (e) {
                        break;
                    }
                    i = j;
                    break;
                }
            }
        }
    }

    return result;
}

module.exports = {
    photoCheckList,
}