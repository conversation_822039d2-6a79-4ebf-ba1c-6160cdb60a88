const axios = require('axios');
const { v4 } = require('uuid');

const walgreen_config = process.env.ad_walgreen ? JSON.parse(process.env.ad_walgreen) : {
    "service_url": "https://services.walgreens.com",
    "api_key": "d12ddc87a36f1cfb422dccb4ff0a7184",
    "print_photo_product_id": "8360001",
    "email": "<EMAIL>",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "phone": "6508899891",
}

console.log(JSON.stringify(walgreen_config, null, 2));
console.log(JSON.parse(process.env.ad_endpoint));

const walgreenApplyCoupon = async (req, res) => {
    try {
        const validate_coupon = await walgreen_get_coupon_validate(req.body.coupon)

        if (validate_coupon.err !== "") {
            res.json({
                success: false,
                message: "invalid coupon code, err: " + validate_coupon.errDesc,
            })
            return
        }
        res.json({
            success: true,
            data: validate_coupon.products[0]
        })
    } catch (error) {
        res.json({
            success: false,
            message: error.message,
        })
    }
}

const walgreenGetStore = async (req, res) => {
    try {
        const store_resp = await axios({
            method: 'post',
            url: `${walgreen_config.service_url}/api/photo/store/v4`,
            headers: {
                'Content-Type': 'application/json',
            },
            data: {
                "act": "photoStores",
                "address1": req.body.address ? req.body.address.toLowerCase() : "2121 Shadow Ridge Way, San Jose, CA 95138",
                "apikey": walgreen_config.api_key,
                "city": "",
                "county": "",
                "fulfillmentType": "SDPU",
                "productDetails": [
                    {
                        "categoryName": "WAGPRINTS",
                        "productId": walgreen_config.print_photo_product_id,
                        "quantity": "1"
                    }
                ],
                "sdkVer": "28",
                "state": "",
                "vendorCd": "WAGAND",
                "zipCode": ""
            }
        })

        if (!store_resp.data.photoStoreResults) {
            res.json({
                success: true,
                data: [],
            })
            return
        }
        res.json({
            success: true,
            data: store_resp.data.photoStoreResults.results.slice(0, 10).map(v => {
                return {
                    store_number: v.storeNumber,
                    distance: v.distance,
                    address: v.store.address,
                    store_name: v.store.name,
                    phone_number: v.store.phone.number,
                    promise_time: v.promiseTime,
                }
            }), // return 10 is fine
        })
    } catch (error) {
        res.json({
            success: false,
            message: error.message,
        })
    }
}

const walgreenSubmitOrder = async (req, res) => {
    try {
        const walgreen_file_url = await walgreen_upload_url({
            url: req.body.image
        })

        const walgreen_order_id = await walgreen_submit_order({
            store_number: req.body.store_number,
            store_promise_time: req.body.promise_time,
            url: walgreen_file_url,
        })

        const callback_config = {
            method: 'post',
            url: `${JSON.parse(process.env.ad_endpoint).api_base_url}/v1/pkg/internal/id-photo-walgreen-callback`,
            data: {
                app_id: Number(req.body.task_id),
                service_type: req.body.service_type,
                print_photo_info: {
                    walgreen_order_id: walgreen_order_id,
                    store_name: req.body.store_name,
                    store_number: req.body.store_number,
                    promise_time: req.body.promise_time,
                    phone_number: req.body.phone_number,
                    address: req.body.address
                }
            },
            headers: {
                "x-ad-token": JSON.parse(process.env.ad_api_token).token,
            }
        }
        console.log(callback_config);
        const callback_resp = await axios(callback_config).catch(err => console.log(err))

        res.json({
            success: true,
            data: {
                walgreen_order_id: walgreen_order_id,
                callback_data: callback_resp.data,
            }
        })
    } catch (error) {
        res.json({
            success: false,
            message: error.message,
        })
    }
}

const walgreen_get_coupon_validate = async function (coupon) {
    const coupon_resp = await axios({
        method: 'post',
        url: `${walgreen_config.service_url}/api/photo/order/coupon/v4`,
        headers: {
            'Content-Type': 'application/json',
        },
        data: {
            "act": "getdiscount",
            "apiKey": walgreen_config.api_key,
            "appver": "43.0",
            "couponCode": [
                coupon
            ],
            "productDetails": [
                {
                    "categoryName": "WAGPRINTS",
                    "productId": walgreen_config.print_photo_product_id,
                    "qty": 1
                }
            ],
            "vendorCd": "WAGAND",
            "view": "getdiscountJSON"
        }
    })
    return coupon_resp.data
}

const walgreen_upload_url = async function ({ url }) {
    const stream = await axios({
        method: "get",
        url: url,
        responseType: "arraybuffer"
    })

    const get_upload_resp = await axios({
        method: 'post',
        url: `${walgreen_config.service_url}/api/util/mweb5url/v3`,
        headers: {
            'brandName': 'WAG',
            'appversion': '43.0',
            'Content-Type': 'application/json',
        },
        data: {
            "act": "checkoutInfo",
            "affId": "qpdevand",
            "apiKey": walgreen_config.api_key,
            "appver": "43.0",
            "send": "Send",
            "transaction": "photocheckout",
            "view": "checkoutInfoJSON"
        }
    })

    // https://pstrgqp01.blob.core.windows.net/qpcontainerin?sig=DcaNqo5LPCRG0slLWJSQkjcXuUSGCu15GcoPQQwouJw%3D&se=2021-10-12T12%3A22%3A22Z&sv=2018-03-28&sp=w&sr=c
    // https://pstrgqp01.blob.core.windows.net/qpcontainerin/1634024001016_image_1-a7776451-154e-4d3a-aa78-88da6141f0e7-845111662.jpg?sig=rBrwVIqv80oAWoD7gkliadxHsGCpbHYLHbcL5IQGJvA%3D&se=2021-10-12T09%3A50%3A03Z&sv=2018-03-28&sp=w&sr=c
    let walgreen_photo_upload_url = get_upload_resp.data.cloud[0].sasKeyToken
    walgreen_photo_upload_url = walgreen_photo_upload_url.replace("/qpcontainerin", `/qpcontainerin/1634024001016_image_1-${v4()}.jpg`)
    console.log(walgreen_photo_upload_url);

    const upload_file_to_walgreen_resp = await axios({
        method: 'put',
        url: walgreen_photo_upload_url,
        headers: {
            'x-ms-blob-type': 'BlockBlob',
            'Content-Type': ':	application/x-www-form-urlencoded'
        },
        data: stream.data
    })

    return walgreen_photo_upload_url.split("?").shift()
}


// "promiseTime": "10-12-2021 9:10 AM",
const walgreen_submit_order = async function ({ coupon, store_number, store_promise_time, url }) {
    const genRanHex = size => [...Array(size)].map(() => Math.floor(Math.random() * 16).toString(16)).join('');

    let data = {
        "act": "",
        "apiKey": walgreen_config.api_key,
        "appver": "43.0",
        "atkn": "",
        "customerInfo": {
            "email": walgreen_config.email,
            "firstName": walgreen_config.first_name,
            "lastName": walgreen_config.last_name,
            "phone": walgreen_config.phone,
            "profileId": ""
        },
        "deviceToken": "210da6b0ead2b98f",
        "devinf": "android 28",
        "fulfillmentType": "SDPU",
        "photoEmailOptIn": true,
        "photoSMSOptin": false,
        "price": {
            "freight": 0,
            "freightDiscount": 0,
            "subTotal": 0.37,
            "tax": 0.03,
            "totalDiscount": 0
        },
        "productDetails": [
            {
                "categoryName": "WAGPRINTS",
                "discountTotal": 0,
                "imageDetails": [
                    {
                        "qty": "1",
                        "url": url
                    }
                ],
                "priceBeforeDiscount": 0.37,
                "productId": walgreen_config.print_photo_product_id,
                "totalQty": "1",
                "unitPrice": 0.37
            }
        ],
        "promiseTime": store_promise_time,
        "promotionList": [],
        "send": "",
        "sourceId": "",
        "storeNum": store_number,
        "transId": "",
        "transactionId": "",
        "vendorCd": "WAGAND",
        "view": ""
    }
    if (coupon) {
        const coupon_validate = await walgreen_get_coupon_validate(coupon)
        if (coupon_validate.err === "" && coupon_validate.products.length > 0) {
            data.price.totalDiscount = coupon_validate.orderDiscountPrice
            data.productDetails[0].discountTotal = coupon_validate.orderDiscountPrice
            data.promotionList = [
                {
                    "couponCode": coupon_validate.products[0].pluDesc,
                    "pluNumber": coupon_validate.products[0].pluNbr,
                    "pluType": "O",
                    "qtyApplied": coupon_validate.products[0].qty,
                    "totalDiscountForPlu": coupon_validate.orderDiscountPrice
                }
            ]
        }
    }
    const submit_resp = await axios({
        method: 'post',
        url: `${walgreen_config.service_url}/api/photo/order/submit/v4`,
        headers: {
            'id': genRanHex(16),
            'brandName': 'WAG',
            'appversion': '43.0',
            'apikey': walgreen_config.api_key,
            'user-agent': 'Walgreens/43.0 (Android 9; SM-G950F/PPR1.180610.011)',
            'Content-Type': 'application/json',
        },
        data: data
    });
    console.log(submit_resp.data)
    return submit_resp.data.orders[0].vendorOrderId
}

module.exports = {
    walgreenApplyCoupon,
    walgreenGetStore,
    walgreenSubmitOrder,
}