const fs = require("fs");
const mergeImg = require('merge-img');
const jimp = require('jimp');
const axios = require('axios');
const cheerio = require('cheerio');
const path = require('path');
const url = require('url');
const color_parse = require('parse-color');
const FormData = require('form-data');
const { s3_upload, s3_signed_url } = require('../models/s3');
const { smartCrop } = require('./smart-crop');
const { v4 } = require('uuid');

const aws_s3 = JSON.parse(process.env.ad_s3);
const { detectImageType, imageToRemovedImageV7 } = require('./remove-bg-core');

const removeBackgroundToS3ForProvider = async (req, res) => {
    try {
        console.log("PROCESSING IMAGE:", req.body.image)
        let { bg, image, head_zoom, width, height } = req.body

        const color = color_parse(bg || '#ffffff')
        if (!color.rgba) {
            res.json({
                success: false,
                message: "invalid background color",
            })
            return
        }

        const url_parsed = url.parse(image)
        const file_name = path.basename(url_parsed.pathname)

        const stream = await axios({
            method: "get",
            url: image,
            responseType: "arraybuffer"
        })
        const tempFilePath = file_name
        fs.writeFileSync(tempFilePath, stream.data)



        let buff = await smartCrop(stream.data, width || 300, height || 400, req.body.head_zoom || 0)
        if (!buff) {
            res.json({
                success: false,
                message: "smart crop fail",
            })
            return
        }
        const image_type = detectImageType(width, height)
        const imgResp = await imageToRemovedImageV7(buff, image_type);
        buff = imgResp.buff;
        let merged = await jimp.read(buff);
        // Replace color
        // merged.scan(0, 0, merged.bitmap.width, merged.bitmap.height, (x, y, idx) => {
        //     if (merged.bitmap.data[idx + 3] < 210) {
        //         merged.bitmap.data[idx + 0] = color.rgba[0]
        //         merged.bitmap.data[idx + 1] = color.rgba[1]
        //         merged.bitmap.data[idx + 2] = color.rgba[2]
        //         merged.bitmap.data[idx + 3] = 255 - merged.bitmap.data[idx + 3]
        //     }
        // })

        buff = await merged.getBufferAsync(jimp.MIME_PNG)

        if (req.query.debug) {
            res.set("Content-Type", jimp.MIME_PNG);
            res.send(buff);
            return
        }

        const uuid = v4();


        await s3_upload(
            aws_s3.ariadirect_prod_passport_images,
            `removebg-passport/${uuid}.png`,
            jimp.MIME_PNG,
            buff
        )
        console.log(3, new Date())

        const url_full = s3_signed_url(aws_s3.ariadirect_prod_passport_images, `removebg-passport/${uuid}.png`)
        console.log(4, new Date())

        console.log("RESULT IMAGE: ", url_full)
        res.json({
            data: {
                url: url_full,
            },
            success: true,
        })
    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: "fail",
            error: error
        })
    }
}

module.exports = {
    removeBackgroundToS3ForProvider,
}