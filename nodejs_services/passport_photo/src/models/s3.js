const AWS = require('aws-sdk')
const s3 = new AWS.S3({ apiVersion: '2006-03-01' });

function s3_upload(bucket, key, contentType, buff) {
    return new Promise(r => {
        const params = {
            Bucket: bucket,
            Key: key,
            Body: buff,
            ContentType: contentType,
            ContentDisposition: 'attachment;',
            ACL: 'public-read',
        }
        s3.upload(params, function (error, data) {
            if (error) return console.log(error)
            console.log(`File uploaded successfully at ${data}`)
            console.log(JSON.stringify(data, null, 2));
            return r(data)
        })
    })
}

function s3_copy(bucket, key, to_bucket, to_key) {
    return new Promise(r => {
        const params = {
            Bucket: to_bucket,
            Key: to_key,
            CopySource: `/${bucket}/${key}`,
            ACL: 'public-read'
        }
        s3.copyObject(params, function (error, data) {
            if (error) return console.log(error)
            return r(data)
        })
    })
}

function s3_signed_url(bucket, key) {
    const MAX_RETRY = 3

    for (let i = 0; i < MAX_RETRY; i++) {
        const url = s3.getSignedUrl('getObject', {
            Bucket: bucket,
            Key: key,
            Expires: 24 * 60 * 60 // 1 day
        });
        if (url === "https://s3.amazonaws.com/")
            continue
        if (!url)
            continue
        return url
    }
    return null

}

function s3_upload_signed_url(bucket, key) {
    const url = s3.getSignedUrl('putObject', {
        Bucket: bucket,
        Key: key,
        Body: 'body',
        // ContentType: 'images/png',
        // Expires: 24 * 60 * 60 // 1 day
    });
    return url
}

function s3_get_buffer(bucket, key) {
    return new Promise(r => {
        s3.getObject({
            Bucket: bucket,
            Key: key,
        }, function (err, data) {
            r(data.Body)
        })
    })
}

function s3_to_bucket_key(url) {
    var _decodedUrl = decodeURIComponent(url);

    var _result = null;

    // http://s3.amazonaws.com/bucket/key1/key2
    var _match = _decodedUrl.match(/^https?:\/\/s3.amazonaws.com\/([^\/]+)\/?(.*?)$/);
    if (_match) {
        _result = {
            bucket: _match[1],
            key: _match[2].split('?')[0],
            region: '',
        };
    }

    // http://s3-aws-region.amazonaws.com/bucket/key1/key2
    _match = _decodedUrl.match(/^https?:\/\/s3-([^.]+).amazonaws.com\/([^\/]+)\/?(.*?)$/);
    if (_match) {
        _result = {
            bucket: _match[2],
            key: _match[3].split('?')[0],
            region: _match[1],
        };
    }

    // http://bucket.s3.amazonaws.com/key1/key2
    _match = _decodedUrl.match(/^https?:\/\/([^.]+).s3.amazonaws.com\/?(.*?)$/);
    if (_match) {
        _result = {
            bucket: _match[1],
            key: _match[2].split('?')[0],
            region: '',
        };
    }

    // http://bucket.s3-aws-region.amazonaws.com/key1/key2 or,
    // http://bucket.s3.aws-region.amazonaws.com/key1/key2
    _match = _decodedUrl.match(/^https?:\/\/([^.]+).(?:s3-|s3\.)([^.]+).amazonaws.com\/?(.*?)$/);
    if (_match) {
        _result = {
            bucket: _match[1],
            key: _match[3].split('?')[0],
            region: _match[2],
        };
    }

    return _result;
}


module.exports = {
    s3_upload,
    s3_signed_url,
    s3_upload_signed_url,
    s3_to_bucket_key,
    s3_get_buffer,
    s3_copy,
}