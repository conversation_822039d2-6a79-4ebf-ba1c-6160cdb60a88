const jwt = require('express-jwt');
var jsonwebtoken = require('jsonwebtoken');

function getTokenFromHeader(req) {
    console.log('********* BEGIN: Request ************')
    console.log(req.originalUrl)
    console.log(req.method)
    console.log('*********  END: Request  ************')
    if (req.headers["x-access-token"]) {
        console.log(req.headers["x-access-token"]);
        return req.headers["x-access-token"]
    }

    return null;
}

const secret = JSON.parse(process.env.ad_secrets)
const secretCallback = function (req, payload, done) {
    // TODO: Limit permission
    req.claims = verifyJWT(getTokenFromHeader(req));

    done(null, secret.jwt);
};

const verifyJWT = function (token) {
    return jsonwebtoken.verify(token, secret.jwt);
};


var auth = {
    required: [jwt({
        algorithms: ["HS256"],
        secret: secretCallback,
        userProperty: 'payload',
        getToken: getTokenFromHeader
    })],
    optional: [],
};

module.exports = auth;