from PIL import Image, ImageDraw, ImageFont

# load background image
background = Image.open("white_background_4x6.png")

resolution =  400
# define dimensions and margins of each photo
photo_width = int( 35 * (resolution/ 25.4)) # mm to px
photo_height = int(45 * (resolution/ 25.4)) # mm to px
margin_left = 50  # px
margin_top = 150  # px
spacing = 20  # px

# load portrait image
portrait = Image.open("1.jpeg")
# resize image to fit within photo dimensions
portrait.thumbnail((photo_width, photo_height), Image.ANTIALIAS)

# create new image with correct dimensions for 4x6 print
print_width = 6 * resolution  # convert inches to pixels at 300dpi
print_height = 4 * resolution
print_image = Image.new("RGB", (print_width, print_height), "white")

# add header to print
header_font = ImageFont.truetype("arial.ttf", 36)
header_text = "AriaDirect Photo - VietNam Visa"
header_size = header_font.getsize(header_text)
header_pos = ((print_width - header_size[0]) // 2, 50)
header_draw = ImageDraw.Draw(print_image)
header_draw.text(header_pos, header_text, fill="black", font=header_font)

# loop over grid of photos and paste portrait into each
num_cols = 4
num_rows = 2
for row in range(num_rows):
    for col in range(num_cols):
        # calculate position of current photo
        x_pos = margin_left + col * (photo_width + spacing)
        y_pos = margin_top + row * (photo_height + spacing)
        # paste portrait into current photo
        print_image.paste(portrait, (x_pos, y_pos))

# save the final image
print_image.save("visa_photo.png")