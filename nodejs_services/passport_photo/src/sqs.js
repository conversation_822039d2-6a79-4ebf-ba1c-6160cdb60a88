const AWS = require('aws-sdk');

const sqs_config = JSON.parse(process.env.ad_sqs)
if (!sqs_config.url_prefix) {
    console.log('missing url_prefix in ad_sqs')
    process.exit()
}
if (!sqs_config.passport_photo_process_photo_sqs_name) {
    console.log('missing passport_photo_process_photo_sqs_name in ad_sqs')
    process.exit()
}
if (!sqs_config.region) {
    console.log('missing region in ad_aws')
    process.exit()
}

// Set the region
AWS.config.update({
    region: sqs_config.region,
})


// Create an SQS service object
const sqs = new AWS.SQS({ apiVersion: '2012-11-05' });

const queueURL = `${sqs_config.url_prefix}/${sqs_config.passport_photo_process_photo_sqs_name}`;

const params = {
    AttributeNames: [
        "SentTimestamp"
    ],
    MaxNumberOfMessages: 10,
    MessageAttributeNames: [
        "All"
    ],
    QueueUrl: queueURL,
    VisibilityTimeout: 20,
    WaitTimeSeconds: 0
};

sqs.receiveMessage(params, function (err, data) {
    if (err) {
        console.log("Receive Error", err);
    } else if (data.Messages) {

        const deleteParams = {
            QueueUrl: queueURL,
            ReceiptHandle: data.Messages[0].ReceiptHandle
        };

        sqs.deleteMessage(deleteParams, function (err, data) {
            if (err) {
                console.log("Delete Error", err);
            } else {
                console.log("Message Deleted", data);
            }
        });
    }
});