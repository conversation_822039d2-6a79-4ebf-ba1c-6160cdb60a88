const express = require('express')
const fileUpload = require('express-fileupload');
require('express-group-routes');
const cors = require('cors');
const { versionHandler } = require('./routes/version');
const { removeBackground, removeBackgroundToS3 } = require('./routes/remove-bg');
const { removeBackgroundToS3ForProvider } = require('./routes/remove-bg-for-provider');
const { generateFileUpload } = require('./routes/qr-photo');
const { printPhoto } = require('./routes/print-photo');
const { walgreenGetStore, walgreenSubmitOrder, walgreenApplyCoupon } = require('./routes/walgreen');
const { photoCheckList } = require('./routes/photo-checklist');
const { autoRotate } = require('./routes/auto-rotate');
const auth = require('./auth');

const PORT = process.env.PORT || 3000

var app = express()
app.use(fileUpload({ debug: false, useTempFiles: true, tempFileDir: "temp" }));
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cors())

app.group("/v1/passport-photo", (router) => {
    router.get("/status", auth.optional, versionHandler)
    router.get("/version", auth.optional, versionHandler)
    router.post("/remove-bg", removeBackground)
    router.post("/remove-bg-to-s3", auth.required, removeBackgroundToS3)
    router.post("/remove-bg-for-provider", auth.required, removeBackgroundToS3ForProvider)
    router.post("/generate-qr-code-url", auth.required, generateFileUpload)
    router.post("/auto-rotate", autoRotate)
    router.post("/print-photo", printPhoto)
    router.post("/checklist", photoCheckList)
    router.post("/walgreen-apply-coupon", walgreenApplyCoupon)
    router.post("/walgreen-nearby-stores", walgreenGetStore)
    router.post("/walgreen-submit-order", walgreenSubmitOrder)
});


console.log("Listen " + PORT)
app.listen(PORT)