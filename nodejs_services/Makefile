SHELL :=/bin/bash
GIT_SHA=$(shell git rev-parse --short=9 HEAD)

build-images: build-packer-online-image build-packer-online-captcha-image build-packer-offline-image build-passport-photo-image build-helper-service-image

build-packer-online-image:
	@docker build -t 853431205376.dkr.ecr.us-west-2.amazonaws.com/packer-online:$(GIT_SHA) --no-cache -f ../dockerfiles/packer-online.dockerfile .

build-packer-online-captcha-image:
	@docker build -t 853431205376.dkr.ecr.us-west-2.amazonaws.com/packer-online-captcha:$(GIT_SHA) --no-cache -f ../dockerfiles/packer-online-captcha.dockerfile .

build-packer-offline-image:
	@docker build -t 853431205376.dkr.ecr.us-west-2.amazonaws.com/packer-offline:$(GIT_SHA) --no-cache -f ../dockerfiles/packer-offline.dockerfile .

build-passport-photo-image:
	@docker build -t 853431205376.dkr.ecr.us-west-2.amazonaws.com/passport-photo-service:$(GIT_SHA) -f ../dockerfiles/passport-photo.dockerfile .

build-helper-service-image:
	@docker build -t 853431205376.dkr.ecr.us-west-2.amazonaws.com/helper-service:$(GIT_SHA) -f ../dockerfiles/helper-service.dockerfile .

build-third-party-service-image:
	@docker build -t 853431205376.dkr.ecr.us-west-2.amazonaws.com/third-party-service:$(GIT_SHA) -f ../dockerfiles/third-party-service.dockerfile .

ecr-login:
	@eval `aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 853431205376.dkr.ecr.us-west-2.amazonaws.com`

push-images: ecr-login
	@docker push 853431205376.dkr.ecr.us-west-2.amazonaws.com/packer-online:$(GIT_SHA)
	@docker push 853431205376.dkr.ecr.us-west-2.amazonaws.com/packer-offline:$(GIT_SHA)
	@docker push 853431205376.dkr.ecr.us-west-2.amazonaws.com/packer-online-captcha:$(GIT_SHA)
	@docker push 853431205376.dkr.ecr.us-west-2.amazonaws.com/passport-photo-service:$(GIT_SHA)
	@docker push 853431205376.dkr.ecr.us-west-2.amazonaws.com/helper-service:$(GIT_SHA)

deploy-dev:
	# sudo apt-get install poppler-utils pdftk
	rsync -av --exclude 'node_modules' ./browser_service/* ad-stag-vps:~/browser_service_stag
	rsync -av --exclude 'node_modules' ./shared/* ad-stag-vps:~/shared
	rsync -av .env_stag ad-stag-vps:~/browser_service_stag/.env
	ssh ad-stag-vps "source ~/.nvm/nvm.sh; cd ~/shared && npm install; cd ~/browser_service_stag && npm install --force && pm2 restart browser-service-stag"

deploy-prod:
	# sudo apt-get install poppler-utils pdftk
	rsync -av --exclude 'node_modules' ./browser_service/* ad-prod-vps:~/browser_service
	rsync -av --exclude 'node_modules' ./shared/* ad-prod-vps:~/shared
	rsync -av .env_prod ad-prod-vps:~/browser_service/.env
	ssh ad-prod-vps "source ~/.nvm/nvm.sh; cd ~/browser_service && npm install --force && pm2 restart browser-service"