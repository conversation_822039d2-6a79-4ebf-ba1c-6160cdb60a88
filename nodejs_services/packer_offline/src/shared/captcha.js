const iso = require('iso-3166-1');
const axios = require("axios");
const FormData = require('form-data');

const get_country_name = (iso3) => COUNTRY[iso3] || iso.whereAlpha3(iso3).country

const get_image_captcha_text = async (buffer) => {
    var data = new FormData();
    data.append('file', buffer, { filename: 'captcha.jpg', contentType: 'image/jpeg' });

    const resp2 = await axios({
        method: 'post',
        url: 'https://api.ariadirectcorp.com/v1/mrz-parser/capcha-solver-by-binary',
        data: data,
        headers: {
            ...data.getHeaders()
        }
    })
    return resp2.data.data

}

module.exports = { get_country_name, get_image_captcha_text }