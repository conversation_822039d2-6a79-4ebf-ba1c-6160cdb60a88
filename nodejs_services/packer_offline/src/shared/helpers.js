var AWS = require('aws-sdk')
var fs = require('fs-extra')
var _ = require('lodash')
const axios = require('axios')
const s3ParseUrl = require('s3-url-parser');
const { PDFDocument } = require('pdf-lib');
const FormData = require('form-data');
const archiver = require('archiver');

var s3 = new AWS.S3()



exports.create_folder = (dir) => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, '0777')
    }
    return dir
}

exports.remove_folder = function (dir) {
    if (fs.existsSync(dir)) {
        fs.removeSync(dir)
    }
    return dir
}

exports.s3_download = async (bucket, key, local_dest) => {
    console.log(bucket, key)
    if (typeof local_dest == 'undefined') {
        local_dest = key
    }

    let params = {
        Bucket: bucket,
        Key: key
    }


    const data = await s3.getObject(params).promise();
    fs.writeFileSync(local_dest, data.Body);
}

exports.s3_upload = (bucket, key, local_dest) => {
    return new Promise((resolve, reject) => {
        let extn = local_dest.split('.').pop();
        let contentType = 'application/octet-stream';
        if (extn == 'pdf') contentType = "application/pdf";
        if (extn == 'html') contentType = "text/html";
        if (extn == 'css') contentType = "text/css";
        if (extn == 'js') contentType = "application/javascript";
        if (extn == 'docx') contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        if (extn == 'png' || extn == 'jpg' || extn == 'gif') contentType = "image/" + extn;

        fs.readFile(local_dest, (err) => {
            if (err) throw err
            const params = {
                Bucket: bucket,
                Key: key,
                Body: fs.readFileSync(local_dest),
                ContentType: contentType
            }
            s3.upload(params, function (error, data) {
                if (error) return reject(error)
                console.log(`File uploaded successfully at ${data.Location}`)
                return resolve()
            })
        })
    })
}


exports.update_packer_status = (token, url, body) => {
    console.log(token)
    console.log(url)
    console.log(JSON.stringify(body, null, 2))
    return requestHaveBody("POST", token, url, body)
}

const requestHaveBody = async (method, token, uri, body) => {
    try {
        const resp = await axios({
            method,
            url: uri,
            headers: {
                'x-ad-token': token,
                'Content-Type': 'application/json',
            },
            data: body
        })
        return resp.data
    } catch (e) {
        console.log(e)
    }
}


exports.import_photo_to_application_form = async (user_photo, name_pdf, { x, y, width, height }) => {
    try {
        const pdfBuff = fs.readFileSync(name_pdf);
        const pdfDoc = await PDFDocument.load(pdfBuff, { ignoreEncryption: true });

        const photoBuffer = fs.readFileSync(user_photo);
        const image = await pdfDoc.embedJpg(photoBuffer);

        const page = pdfDoc.getPage(0);
        page.drawImage(image, {
            x,
            y,
            width,
            height,
        });

        const modifiedPdfBytes = await pdfDoc.save();
        return modifiedPdfBytes
    } catch (error) {
        console.log(error)
        throw 'PHOTO_WRITE_FAILED'
    }
}


exports.s3_upload_buff = (bucket, key, buffer) => {
    return new Promise((resolve, reject) => {
        const params = {
            Bucket: bucket,
            Key: key,
            Body: buffer,
            ACL: 'public-read'
        }
        s3.upload(params, function (error, data) {
            if (error) return reject(error)
            console.log(`File uploaded successfully at ${data.Location}`)
            return resolve(data.Location)
        })
    })
}


exports.prepareDataByVisaPods = (inputPods) => {
    let results = {};

    const processInputPod = (inputPod) => {
        const { id, value, option_choice } = inputPod;

        if (value && value.fe != null) {
            results[id] = value.fe;
            if (option_choice && Object.keys(option_choice).length > 0)
                processOptionChoice(option_choice[value.fe]);
        }
        if (inputPod.type == 'array') {
            if (_.isArray(value.fe)) {
                for (const choice of Array(value.fe || [])) {
                    if (option_choice && option_choice[choice])
                        processOptionChoice(option_choice[choice]);
                }
            }
        }
    };

    const processOptionChoice = (optionChoice) => {
        if (optionChoice) {
            for (const inputPod of optionChoice) {
                processInputPod(inputPod);
            }
        }
    };

    for (const inputPod of inputPods) {
        processInputPod(inputPod);
    }

    return results;
};


exports.capitalize = (str) => {
    if (typeof str !== 'string') return ''
    str = str.toLowerCase()
    return str.replace(/\w\S*/g, function (txt) { return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase(); });
}

exports.s3_url_to_bucket_key = (url) => {
    let photo_image = url.split('?')[0]
    if (!photo_image.startsWith('http')) {
        const bucket = photo_image.split('/')[0]
        const key = photo_image.split('/').slice(1).join('/')
        if (bucket && key) {
            return { bucket, key }
        }
    }
    return s3ParseUrl(photo_image)
}

exports.get_order_detail = async (order_id) => {
    const api_token = JSON.parse(process.env.ad_api_token)
    const api_base = JSON.parse(process.env.ad_endpoint).api_base_url
    const url = `${api_base}/v1/pkg/internal/orders/${order_id}`

    return requestHaveBody("GET", api_token.token, url)
}

exports.get_order_task = async (order_id, task_id) => {
    const api_token = JSON.parse(process.env.ad_api_token)
    const api_base = JSON.parse(process.env.ad_endpoint).api_base_url
    const url = `${api_base}/v1/pkg/internal/orders/${order_id}/tasks/${task_id}`

    return requestHaveBody("GET", api_token.token, url)
}


exports.send_zalo_message = async (shareLink, message) => {
    let data = new FormData();
    data.append('shareLink', shareLink);
    data.append('message', message);
    let config3 = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://zalo.ariadirectcorp.com/api/zalo_chat/group/send_message',
        headers: {
            ...data.getHeaders()
        },
        data
    };
    const resp3 = await axios(config3).catch(console.log)
    console.log(resp3.data)
}

exports.send_zalo_file = async (shareLink, file_url) => {
    const file_resp = await axios.get(file_url, { responseType: 'arraybuffer' }).catch(console.log)
    let data = new FormData();
    data.append('shareLink', shareLink);
    data.append('file', file_resp.data, {
        filename: file_url.split('/').pop(),
    });
    let config3 = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://zalo.ariadirectcorp.com/api/zalo_chat/group/send_file',
        headers: {
            ...data.getHeaders()
        },
        data
    };
    const resp3 = await axios(config3).catch(console.log)
    console.log(resp3.data)
}

exports.create_zip = async (zip_name, folder) => {
    return new Promise((resolve, reject) => {
        const output = fs.createWriteStream(zip_name);
        const archive = archiver('zip', { zlib: { level: 9 } });

        output.on('close', () => {
            console.log('ZIP archive created successfully.');
            resolve();
        });

        archive.on('error', (err) => {
            reject(err);
        });

        archive.pipe(output);
        archive.directory(folder, false);
        archive.finalize();
    });
}