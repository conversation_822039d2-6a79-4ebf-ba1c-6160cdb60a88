const { Consumer } = require('sqs-consumer')
const AWS = require('aws-sdk')

const processVisa = require('./handlers/index')

if (!process.env.ad_aws || !process.env.ad_sqs) {
    console.log('missing ad_aws or ad_sqs')
    process.exit(0)
}
const sqs_config = JSON.parse(process.env.ad_sqs)
const aws_config = JSON.parse(process.env.ad_aws)
const api_token = JSON.parse(process.env.ad_api_token)
if (!sqs_config.url_prefix) {
    console.log('missing url_prefix in ad_sqs')
    process.exit()
}
if (!sqs_config.packer_offline_sqs_name) {
    console.log('missing packer_offline_sqs_name in ad_sqs')
    process.exit()
}
if (!aws_config.region) {
    console.log('missing region in ad_aws')
    process.exit()
}
if (!api_token.token) {
    console.log('missing token in ad_api_token')
    process.exit()
}

AWS.config.update({
    region: aws_config.region,
})
SQS_QUEUE_URL = `${sqs_config.url_prefix}/${sqs_config.packer_offline_sqs_name}`

const app = Consumer.create({
    sqs: new AWS.SQS(),
    queueUrl: SQS_QUEUE_URL,
    batchSize: 1,
    waitTimeSeconds: 2,
    visibilityTimeout: 0,
    handleMessage: async (message) => {
        console.log(message)
        await processVisa(JSON.parse(message.Body))
        await deleteMessage(message)
        return true
    },
})

const deleteMessage = (message) => new Promise((resolve, reject) => {
    var deleteParams = {
        QueueUrl: SQS_QUEUE_URL,
        ReceiptHandle: message.ReceiptHandle
    }
    app.sqs.deleteMessage(deleteParams, function (err, data) {
        if (err) {
            console.log("Delete Error", err)
            resolve(false)
        } else {
            console.log("Message Deleted", data)
            resolve(true)
        }
    })
})
app.on('error', (err) => {
    console.error(err.message)
})

app.on('processing_error', (err) => {
    console.error(err.message)
})

app.on('timeout_error', (err) => {
    console.error(err.message)
})

app.start()

process.on("SIGINT", () => {
    console.log("SIGINT Received, stopping consumer");
    app.stop();
    setTimeout(process.exit, 10000);
});

module.exports = { app }