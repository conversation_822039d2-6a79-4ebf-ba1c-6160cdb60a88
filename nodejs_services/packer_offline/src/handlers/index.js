const moment = require('moment')
const pdfFiller = require('pdffiller')
const fillPdf = require('./pdffiller/index')
const iso = require('iso-3166-1');
const ejs = require('ejs')
const puppeteer = require('puppeteer')

const helper = require('./helpers')
const dataMapping = require('./data_mapping');
const { get_order_task, get_order_detail, update_packer_status } = require('../shared/helpers');

const FORM_TYPE = dataMapping.form_type

const prepareDataByPassportPods = (inputPods) => {
    var results = {}
    for (const key in inputPods) {
        if (inputPods.hasOwnProperty(key)) {
            const element = inputPods[key];
            if (element.value && typeof element.value.fe != 'undefined') {
                const newKey = `${element.category}_${element.sub_category}_${element.name}`

                // convert country from iso_alpha3 to name
                if (element.type_fe == "select_country" || element.type_fe == "list_country") {
                    try {
                        var { country: name } = iso.whereAlpha3(element.value.fe)
                    } catch (error) {
                        console.log("Error: cant find this country", element.value.fe)
                    }
                    element.value.fe = name.toUpperCase()
                }


                // convert countries from iso_alpha3 to name
                if (element.type_fe == "select_countries") {
                    if (element.value.fe && element.value.fe.length > 0) {
                        var temp = ""
                        element.value.fe.forEach(element => {
                            try {
                                var { country } = iso.whereAlpha3(element)
                            } catch (error) {
                                console.log("Error: cant find this country", element)
                            }
                            temp += country.toUpperCase() + ", "
                        });
                        temp = temp.slice(0, -2)
                        element.value.fe = temp
                    }
                }

                // convert phone
                if (element.type_fe == "input_phone") {
                    if (element.value.fe && element.value.fe != "") {
                        if (typeof element.value.fe != 'string') {
                            element.value.fe = element.value.fe.phone
                        }
                        results[newKey + "_prefix"] = helper.findPrefixOfPhone(element.value.fe)
                    }
                }

                // uppercase input
                if (element.type_fe == "input") {
                    element.value.fe = element.value.fe.toUpperCase()
                }


                results[newKey] = element.value.fe
            }
        }
    }
    return results
}

const fillForm = (source, destination, data) => new Promise((resolve, reject) => {
    pdfFiller.fillFormWithFlatten(source, destination, data, false, function (err) {
        if (err) {
            reject(err)
        } else {
            resolve(true)
        }
    });
})
const fillFormUnicode = (source, destination, data) => new Promise((resolve, reject) => {
    fillPdf.generatePdf({ fields: data }, source, { fontSize: 8.0 }, destination, function (error, stdout, stderr) {
        if (error) {
            reject(error);
        }
        resolve(true)
    })
})

const createPassportForm = async (data) => {
    let folder_path = `${__dirname}/${data.order_id}`
    helper.createFolder(folder_path)
    try {
        // 1. prepare data
        var pods = prepareDataByPassportPods(data.input_pods)
        if (data.shipment) {
            if (data.shipment.shipping_address) {
                if (data.shipment.shipping_address.country) {
                    shipmentInfo = helper.prepareDataShipment(data.shipment)
                    pods = Object.assign(pods, shipmentInfo);
                }
            }
        }
        const prepareSpecialData = require(`${__dirname}/${data.form_type}/${data.form_name}.js`)
        var final_data = await prepareSpecialData(pods);
        console.log(JSON.stringify(final_data))

        // 2. fill data to pdf form
        const source = `${folder_path}/${data.form_name}.pdf`
        await helper.s3Download(data.s3.form_template_s3_bucket, data.s3.form_template_s3_key, source)
        var destination = `${folder_path}/${data.form_type}_${moment().valueOf()}.pdf`
        if (final_data.unicode) {
            await fillFormUnicode(source, destination, final_data);
        } else {
            await fillForm(source, destination, final_data);
        }

        // 3. insert photo to pdf application (if need)
        if (final_data.frame) {
            // 4. fetch form file from S3, if not cached
            const bucket = data.s3.photo_download_s3_bucket
            const key = data.s3.photo_download_s3_key
            if (bucket && key) {
                var ext = key.substring(key.lastIndexOf(".") + 1);
                const user_photo = `${folder_path}/user_photo_${moment().valueOf()}.${ext}`
                await helper.s3Download(bucket, key, user_photo)
                var temp = destination
                destination = await helper.importPhotoToApplicationForm(user_photo, folder_path, `_${moment().valueOf()}.pdf`, temp, final_data.frame);
            }
        }
        // 4. upload pdf form to S3
        await helper.s3Upload(data.s3.form_upload_s3_bucket, data.s3.form_upload_s3_key, destination)

        // 5. delete file locally
        helper.removeFolder(folder_path + '/')

        // 6. Update the application status
        let result = {
            form_file: `https://${data.s3.form_upload_s3_bucket}.s3.amazonaws.com/${data.s3.form_upload_s3_key}`,
            form_callback: null
        }
        return result

    } catch (error) {
        console.log(error)
        helper.removeFolder(folder_path + '/')
        throw (error)
    }
}
const main = async ({ order_id, task_id, form_name }) => {
    try {
        const order_resp = await get_order_detail(order_id)
        const task_resp = await get_order_task(order_id, task_id)

        const s3_buckets = JSON.parse(process.env.ad_s3)
        const form_result = await createPassportForm({
            input_pods: task_resp.data.input_pods,
            shipment: order_resp.data.shipment,
            form_name: form_name,
            form_type: 'passport_form',
            s3: {
                form_upload_s3_bucket: s3_buckets.ariadirect_prod_applications,
                form_upload_s3_key: `offline_forms/Application_Form_${order_id}_${moment().unix()}.pdf`
            }

        })

        const callback_data = {
            task_id: task_id,
            form_name: form_name,
            form_file: form_result.form_file
        }

        const api_token = JSON.parse(process.env.ad_api_token)
        await update_packer_status(
            api_token.token,
            `${api_base}/v1/packer/internals-packers/${order_id}/update-service-task-form`,
            callback_data,
        )


        return true
    } catch (error) {
        throw (error)
    }
}


module.exports = main