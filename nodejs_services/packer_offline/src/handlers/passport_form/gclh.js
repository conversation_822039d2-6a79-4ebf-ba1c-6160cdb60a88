const moment = require('moment')
const helpers = require('../helpers')
const data_mapping = require('../data_mapping')

const DATE_FORMAT = 'DD/MM/YYYY'

module.exports = (data) => {
    data['passport_core_info_name'] = `${data.passport_core_info_surname}, ${data.passport_core_info_given_name}${typeof data.passport_core_info_middle_name != 'undefined' ? ' ' + data.passport_core_info_middle_name : ''}`
    data['personal_core_info_nationality'] = `${data.passport_core_info_nationality}`
    data['passport_core_info_passport_number'] = `${data.passport_core_info_passport_number}`
    data['personal_permanent_address'] = `${data.personal_permanent_address_address}, ${data.personal_permanent_address_city}, ${data.personal_permanent_address_state} ${data.personal_permanent_address_zip_code}`.toUpperCase()
    data['personal_core_info_phone'] = `${data.personal_core_info_phone}`
    data['personal_core_info_relationship_with_the_person'] = `${data.personal_core_info_relationship_with_the_person}`
    data['passport_core_info_gender'] = data_mapping.gender_vnm[data.passport_core_info_gender]
    data['passport_core_info_date_of_birth'] = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)    
    
    data.unicode = false
    return data
}