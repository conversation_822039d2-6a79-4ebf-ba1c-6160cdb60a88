const moment = require('moment')

const data_mapping = require('../data_mapping')
const helpers = require('../helpers')

const DATE_FORMAT = 'MM/DD/YYYY'

module.exports = (data) => {
    data['passport_core_info_date_of_birth_date'] = moment(data.passport_core_info_date_of_birth).format('DD')
    data['passport_core_info_date_of_birth_month'] = moment(data.passport_core_info_date_of_birth).format('MM')
    data['passport_core_info_date_of_birth_year'] = moment(data.passport_core_info_date_of_birth).format('YYYY')

    data['passport_core_info_issue_date'] = moment(data.passport_core_info_issue_date).format(DATE_FORMAT)

    var social_security_numbers = data.personal_core_info_social_security_number.split('-');
    data['personal_core_info_social_security_number_1'] = social_security_numbers[0]
    data['personal_core_info_social_security_number_2'] = social_security_numbers[1]
    data['personal_core_info_social_security_number_3'] = social_security_numbers[2]

    data.personal_core_info_phone = data.personal_core_info_phone.replace(data.personal_core_info_phone_prefix, "")
    data['personal_core_info_phone_3'] = data.personal_core_info_phone.slice(-4)
    data['personal_core_info_phone_2'] = data.personal_core_info_phone.slice(-7, -4)
    data['personal_core_info_phone_1'] = data.personal_core_info_phone.slice(-10, -7)

    data['passport_core_info_name'] = `${data.passport_core_info_surname}, ${data.passport_core_info_given_name}${typeof data.passport_core_info_middle_name != 'undefined' ? ' ' + data.passport_core_info_middle_name : ''}`
    data['passport_core_info_name_pp_book'] = `${data.passport_core_info_given_name}${typeof data.passport_core_info_middle_name != 'undefined' ? ' ' + data.passport_core_info_middle_name : ''} ${data.passport_core_info_surname}`
    data['passport_core_info_date_of_birth'] = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)

    if (typeof data.additional_question_name_change_date_of_name_change != 'undefined') {
        data['additional_question_name_change_date_of_name_change'] = moment(data.additional_question_name_change_date_of_name_change).format(DATE_FORMAT)
    }

    if (data.additional_question_core_info_passport_document == "card") {
        data.additional_question_core_info_passport_book_type = 'none'
    } else {
        data.additional_question_core_info_passport_book_type = 'large'
    }

    if (typeof data.document_most_recent_pp_have_you_applied_issued_us_pp_book_pp_card != 'undefined') {
        if (typeof data.passport_most_recent_pp_pp_book_issue_date != 'undefined') {
            data.passport_most_recent_pp_pp_book_issue_date = moment(data.passport_most_recent_pp_pp_book_issue_date).format("MMDDYYYY")
        }
        if (typeof data.passport_most_recent_pp_pp_card_issue_date != 'undefined') {
            data.passport_most_recent_pp_pp_card_issue_date = moment(data.passport_most_recent_pp_pp_card_issue_date).format("MMDDYYYY")
        }
    }

    // map hair
    data.personal_core_info_hair_color = data_mapping.hair_color[data.personal_core_info_hair_color]

    // map eye
    data.personal_core_info_eye_color = data_mapping.eye_color[data.personal_core_info_eye_color]

    // handle zipcode
    if (data.personal_permanent_address_zip_code){
        data.personal_permanent_address_zip_code = data.personal_permanent_address_zip_code.split("-")[0]
    }
    data.personal_mailing_address_zip_code = data.personal_mailing_address_zip_code.split("-")[0]
    if (data.personal_mailing_address_country == 'UNITED STATES OF AMERICA') {
        delete data.personal_mailing_address_country
    }

    // handle travel plan for rush
    if (data.service_core_info_processing_time == '4-6w') {
        data['travel_travel_plan_departure_date'] = moment().add(1, 'M').format(DATE_FORMAT) // add 1 month
        data['travel_travel_plan_return_date'] = moment().add(1, 'M').add(10, 'd').format(DATE_FORMAT) // add 1 month and 10 days
        data['travel_travel_plan_countries'] = "Italy, Germany"
    }
    
    data = helpers.removeStateForUSForm(data)
    // handle additional question
    // correct data
    if (data.additional_question_core_info_additional_question_pp_1) {
        data.additional_question_core_info_additional_question_pp_1 = "Yes"
        if (typeof data.additional_question_correct_data_printed_last_name == "string" ){
            data.additional_question_correct_data_printed_last_name_faq = "Yes"
            data.passport_core_info_surname = data.additional_question_correct_data_printed_last_name 
        }
        if (typeof data.additional_question_correct_data_printed_first_name == "string" ){
            data.additional_question_correct_data_printed_first_name_faq = "Yes"
            data.passport_core_info_given_name = data.additional_question_correct_data_printed_first_name 
        }
        if (typeof data.additional_question_correct_data_printed_middle_name == "string" ){
            data.additional_question_correct_data_printed_middle_name_faq = "Yes"
            data.passport_core_info_middle_name = data.additional_question_correct_data_printed_middle_name 
        }
        if (typeof data.additional_question_correct_data_printed_gender == "string" ){
            data.additional_question_correct_data_printed_gender_faq = "Yes"
            data.passport_core_info_gender = data.additional_question_correct_data_printed_gender 
        }
        if (typeof data.additional_question_correct_data_printed_date_of_birth == "string") {
            data.additional_question_correct_data_printed_date_of_birth_faq = "Yes"
            data['passport_core_info_date_of_birth_date'] = moment(data.additional_question_correct_data_printed_date_of_birth).format('DD')
            data['passport_core_info_date_of_birth_month'] = moment(data.additional_question_correct_data_printed_date_of_birth).format('MM')
            data['passport_core_info_date_of_birth_year'] = moment(data.additional_question_correct_data_printed_date_of_birth).format('YYYY')
            data.additional_question_correct_data_printed_date_of_birth = moment(data.additional_question_correct_data_printed_date_of_birth).format(DATE_FORMAT)
        }
        if (typeof data.additional_question_correct_data_printed_place_of_birth == "string" ){
            data.additional_question_correct_data_printed_place_of_birth_faq = "Yes"
            data.passport_core_info_place_of_birth = data.additional_question_correct_data_printed_place_of_birth 
        }
    } else {
        data.additional_question_core_info_additional_question_pp_1 = "No"
    }
    // change name
    if (data.additional_question_core_info_additional_question_pp_3 == true) {
        data.additional_question_core_info_additional_question_pp_3 = "Yes"
        if (typeof data.additional_question_change_data_last_name == "string" ){
            data.passport_core_info_surname = data.additional_question_change_data_last_name 
        }
        if (typeof data.additional_question_change_data_first_name == "string" ){
            data.passport_core_info_given_name = data.additional_question_change_data_first_name 
        }
        if (typeof data.additional_question_change_data_middle_name == "string" ){
            data.passport_core_info_middle_name = data.additional_question_change_data_middle_name 
        }
    } else {
        data.additional_question_core_info_additional_question_pp_3 = "No"
    }

    if (data.additional_question_core_info_additional_question_pp_10 == true) {
        data.additional_question_core_info_additional_question_pp_10 = "Yes"
    } else {
        data.additional_question_core_info_additional_question_pp_10 = "No"
    }
    data.unicode = false
    return data
}