const moment = require('moment')
const helpers = require('../helpers')
const data_mapping = require('../data_mapping')

const DATE_FORMAT = 'DD/MM/YYYY'

module.exports = (data) => {
    if (typeof data.passport_core_info_gender == 'M') {
        data['passport_core_info_name_male'] = `${data.passport_core_info_surname}, ${data.passport_core_info_given_name}${typeof data.passport_core_info_middle_name != 'undefined' ? ' ' + data.passport_core_info_middle_name : ''}`
        data['passport_core_info_date_of_birth_male'] = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)
        data['personal_core_info_ethnic_male'] = `${data.personal_core_info_ethnic}`
        data['personal_core_info_nationality_male'] = `${data.passport_core_info_nationality}`
        data['personal_permanent_address_male'] = `${data.personal_permanent_address_address}, ${data.personal_permanent_address_city}, ${data.personal_permanent_address_state} ${data.personal_permanent_address_zip_code}`.toUpperCase()
        data['passport_core_info_passport_number_male'] = `${data.passport_core_info_passport_number}`
        data['personal_occupation_male'] = `${data.personal_occupation_occupation}`
    } else {
        data['passport_core_info_name_female'] = `${data.passport_core_info_surname}, ${data.passport_core_info_given_name}${typeof data.passport_core_info_middle_name != 'undefined' ? ' ' + data.passport_core_info_middle_name : ''}`
        data['passport_core_info_date_of_birth_female'] = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)
        data['personal_core_info_ethnic_female'] = `${data.personal_core_info_ethnic}`
        data['personal_core_info_nationality_female'] = `${data.passport_core_info_nationality}`
        data['personal_permanent_address_female'] = `${data.personal_permanent_address_address}, ${data.personal_permanent_address_city}, ${data.personal_permanent_address_state} ${data.personal_permanent_address_zip_code}`.toUpperCase()
        data['passport_core_info_passport_number_female'] = `${data.passport_core_info_passport_number}`
        data['personal_occupation_female'] = `${data.personal_occupation_occupation}`
    }
    data.unicode = false
    return data
}