const moment = require('moment')
const helpers = require('../helpers')
const data_mapping = require('../data_mapping')

module.exports = (data) => {
    data['passport_core_info_name'] = `${data.passport_core_info_surname} ${data.passport_core_info_given_name}`
    data['passport_core_info_date_of_birth_date'] = moment(data.passport_core_info_date_of_birth).format('DD')
    data['passport_core_info_date_of_birth_month'] = moment(data.passport_core_info_date_of_birth).format('MM')
    data['passport_core_info_date_of_birth_year'] = moment(data.passport_core_info_date_of_birth).format('YYYY')
    data['passport_core_info_place_of_birth'] = `${data.passport_core_info_state_of_birth}, ${data.passport_core_info_country_of_birth}`
    data[`passport_core_info_gender_${data.passport_core_info_gender.toLowerCase()}`] = 'x'
    data['personal_citizen_issue_date'] = moment(data.personal_citizen_issue_date).format('DD/MM/YYYY')
    data['passport_core_info_issue_date'] = moment(data.passport_core_info_issue_date).format('DD/MM/YYYY')

    if (typeof data.family_father_surname != 'undefined') {
        data['family_father_name'] = `${data.family_father_surname} ${data.family_father_given_name}`
        data['family_father_date_of_birth'] = moment(data.family_father_date_of_birth).format('DD/MM/YYYY')
    }

    if (typeof data.family_mother_surname != 'undefined') {
        data['family_mother_name'] = `${data.family_mother_surname} ${data.family_mother_given_name}`
        data['family_mother_date_of_birth'] = moment(data.family_mother_date_of_birth).format('DD/MM/YYYY')
    }

    if (typeof data.family_spouse_surname != 'undefined') {
        data['family_spouse_name'] = `${data.family_spouse_surname} ${data.family_spouse_given_name}`
        data['family_spouse_date_of_birth'] = moment(data.family_spouse_date_of_birth).format('DD/MM/YYYY')
    }

    data['tasks'] = data_mapping.passport_tasks[data.service_core_info_tasks[0]]
    data['additional_question_recommended_content'] = data_mapping.vnm_content[data.additional_question_recommended_reason_request]

    data.unicode = true
    return data
}