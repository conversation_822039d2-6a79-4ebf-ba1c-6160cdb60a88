const moment = require('moment-timezone');

const data_mapping = require('../data_mapping')
const helpers = require('../helpers')

const DATE_FORMAT = 'DD/MM/YYYY'
const OCCUPATION_MAPPING = data_mapping.occupation
const RELATIONSHIP = data_mapping.relationship
const US_IMMIGRATION = data_mapping.us_immigration
const NA = 'N/A'

module.exports = (data) => {
    data.passport_core_info_gender = data.passport_core_info_gender === 'F' ? 'Bà (Mrs)' : 'Ông (Mr)'
    data.passport_core_info_date_of_birth = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)
    data.passport_core_info_full_name = data.passport_core_info_surname + ' ' + data.passport_core_info_given_name

    const service_core_info_entry_date = moment(data.service_core_info_entry_date)
    const now = service_core_info_entry_date.add(-1, "day");
    data.created_at_day = now.format('DD')
    data.created_at_month = now.format('MM')
    data.created_at_year = now.format('YYYY')
    data.created_at_eng = now.format('DD MMM YYYY');

    const travel_enter_flight_enter_timestamp = moment(data.travel_enter_flight_enter_timestamp)
    data.service_core_info_entry_date = travel_enter_flight_enter_timestamp.format(DATE_FORMAT)
    data.travel_enter_flight_enter_timestamp = travel_enter_flight_enter_timestamp.format(DATE_FORMAT)
    data.visa_info_core_info_exit_date = travel_enter_flight_enter_timestamp.add(30, 'day').format(DATE_FORMAT)

    data.passport_core_info_nationality = data.passport_core_info_nationality === 'USA' ? 'United States of America' : data.passport_core_info_nationality

    return data
}