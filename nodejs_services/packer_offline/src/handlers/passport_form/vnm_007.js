const moment = require('moment')
const helpers = require('../helpers')
const data_mapping = require('../data_mapping')
const ETHNIC = "KINH"

module.exports = (data) => {
    data['passport_core_info_name'] = `${data.passport_core_info_surname} ${data.passport_core_info_given_name}`
    data['passport_core_info_gender'] = data_mapping.gender_vnm[data.passport_core_info_gender]
    data['passport_core_info_date_of_birth'] = moment(data.passport_core_info_date_of_birth).format('DD/MM/YYYY')
    data['passport_core_info_place_of_birth'] = `${data.passport_core_info_state_of_birth}, ${data.passport_core_info_country_of_birth}`
    data["personal_core_info_ethnic"] = ETHNIC
    data['personal_permanent_address'] = helpers.capitalize(`${data.personal_permanent_address_address}, ${data.personal_permanent_address_city} ${data.personal_permanent_address_zip_code}, ${data.personal_permanent_address_country}`)

    if (typeof data.family_father_surname != 'undefined') {
        data['family_father_name'] = `${data.family_father_surname} ${data.family_father_given_name}`
        data['family_father_date_of_birth_date'] = moment(data.family_father_date_of_birth).format('DD')
        data['family_father_date_of_birth_month'] = moment(data.family_father_date_of_birth).format('MM')
        data['family_father_date_of_birth_year'] = moment(data.family_father_date_of_birth).format('YYYY')
        data['family_father_ethnic'] = data.family_father_nationality == "VIET NAM" ? ETHNIC : ''
        data['family_father_permanent_address'] = data.personal_permanent_address
    }

    if (typeof data.family_mother_surname != 'undefined') {
        data['family_mother_name'] = `${data.family_mother_surname} ${data.family_mother_given_name}`
        data['family_mother_date_of_birth_date'] = moment(data.family_mother_date_of_birth).format('DD')
        data['family_mother_date_of_birth_month'] = moment(data.family_mother_date_of_birth).format('MM')
        data['family_mother_date_of_birth_year'] = moment(data.family_mother_date_of_birth).format('YYYY')
        data['family_mother_ethnic'] = data.family_mother_nationality == "VIET NAM" ? ETHNIC : ''
        data['family_mother_permanent_address'] = data.personal_permanent_address
    }

    data.unicode = true
    return data
}