const moment = require('moment')
const DATE_FORMAT = 'MM/DD/YYYY'
const helpers = require('../helpers')

module.exports = (data) => {
    // handle lost/stolen
    if (typeof data.passport_most_recent_pp_status_pp_book_lost_stolen != 'undefined') {
        data.passport_most_recent_pp_status_pp_book = data.passport_most_recent_pp_status_pp_book_lost_stolen
        if (typeof data.passport_most_recent_pp_name_pp_book_lost_stolen != 'undefined') {
            data.passport_most_recent_pp_name_pp_book = data.passport_most_recent_pp_name_pp_book_lost_stolen
        }
        if (typeof data.passport_most_recent_pp_pp_book_number_lost_stolen != 'undefined') {
            data.passport_most_recent_pp_pp_book_number = data.passport_most_recent_pp_pp_book_number_lost_stolen
        }
        if (typeof data.passport_most_recent_pp_pp_book_issue_date_lost_stolen != 'undefined') {
            data.passport_most_recent_pp_pp_book_issue_date = data.passport_most_recent_pp_pp_book_issue_date_lost_stolen
        }
    }
    if (typeof data.passport_most_recent_pp_status_pp_card_lost_stolen != 'undefined') {
        data.passport_most_recent_pp_status_pp_card = data.passport_most_recent_pp_status_pp_card_lost_stolen
        if (typeof data.passport_most_recent_pp_name_pp_card_lost_stolen != 'undefined') {
            data.passport_most_recent_pp_name_pp_card = data.passport_most_recent_pp_name_pp_card_lost_stolen
        }
        if (typeof data.passport_most_recent_pp_pp_card_number_lost_stolen != 'undefined') {
            data.passport_most_recent_pp_pp_card_number = data.passport_most_recent_pp_pp_card_number_lost_stolen
        }
        if (typeof data.passport_most_recent_pp_pp_card_issue_date_lost_stolen != 'undefined') {
            data.passport_most_recent_pp_pp_card_issue_date = data.passport_most_recent_pp_pp_card_issue_date_lost_stolen
        }
    }
    // end 
    data['passport_core_info_date_of_birth'] = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)
    data['additional_question_lost_stolen_date_pp_lost_stolen'] = moment(data.additional_question_lost_stolen_date_pp_lost_stolen).format(DATE_FORMAT)

    data['personal_core_info_phone_2'] = data.personal_core_info_phone.replace(data.personal_core_info_phone_prefix, "")
    data['personal_core_info_phone_1'] = data.personal_core_info_phone_prefix ? data.personal_core_info_phone_prefix : ""

    if (data.passport_most_recent_pp_status_pp_book && data.passport_most_recent_pp_pp_book_issue_date != '' && (data.passport_most_recent_pp_status_pp_book == "lost" || data.passport_most_recent_pp_status_pp_book == "stolen")) {
        data.passport_most_recent_pp_pp_book_issue_date = moment(data.passport_most_recent_pp_pp_book_issue_date).format(DATE_FORMAT)
    } else {
        data.passport_most_recent_pp_pp_book_issue_date = ''
        data.passport_most_recent_pp_pp_book_number = ''
    }
    if (data.passport_most_recent_pp_status_pp_card && (data.passport_most_recent_pp_status_pp_card == "lost" || data.passport_most_recent_pp_status_pp_card == "stolen")) {
        data.passport_most_recent_pp_pp_card_issue_date = moment(data.passport_most_recent_pp_pp_card_issue_date).format(DATE_FORMAT)
    } else {
        data.passport_most_recent_pp_pp_card_issue_date = ''
        data.passport_most_recent_pp_pp_card_number = ''
    }
    if (typeof data.additional_question_lost_stolen_approximate_dates_lost_stolen != 'undefined') {
        data.additional_question_lost_stolen_approximate_dates_lost_stolen = moment(data.additional_question_lost_stolen_approximate_dates_lost_stolen).format(DATE_FORMAT)
    }
    data = helpers.removeStateForUSForm(data)
    data.unicode = false
    return data
}