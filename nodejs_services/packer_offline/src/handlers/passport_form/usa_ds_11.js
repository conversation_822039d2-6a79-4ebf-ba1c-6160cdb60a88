const moment = require('moment')

const data_mapping = require('../data_mapping')
const helpers = require('../helpers')

const DATE_FORMAT = 'MM/DD/YYYY'

module.exports = (data) => {
    // handle lost/stolen
    if (typeof data.passport_most_recent_pp_status_pp_book_lost_stolen != 'undefined') {
        data.passport_most_recent_pp_status_pp_book = data.passport_most_recent_pp_status_pp_book_lost_stolen
        // data.passport_most_recent_pp_name_pp_book = data.passport_most_recent_pp_name_pp_book_lost_stolen
        if (typeof data.passport_most_recent_pp_pp_book_number_lost_stolen != 'undefined') {
            data.passport_most_recent_pp_pp_book_number = data.passport_most_recent_pp_pp_book_number_lost_stolen
        }
        if (typeof data.passport_most_recent_pp_pp_book_issue_date_lost_stolen != 'undefined') {
            data.passport_most_recent_pp_pp_book_issue_date = data.passport_most_recent_pp_pp_book_issue_date_lost_stolen
        }
    }
    if (typeof data.passport_most_recent_pp_status_pp_card_lost_stolen != 'undefined') {
        data.passport_most_recent_pp_status_pp_card = data.passport_most_recent_pp_status_pp_card_lost_stolen
        // data.passport_most_recent_pp_name_pp_card = data.passport_most_recent_pp_name_pp_card_lost_stolen
        if (typeof data.passport_most_recent_pp_pp_card_number_lost_stolen != 'undefined') {
            data.passport_most_recent_pp_pp_card_number = data.passport_most_recent_pp_pp_card_number_lost_stolen
        }
        if (typeof data.passport_most_recent_pp_pp_card_issue_date_lost_stolen != 'undefined') {
            data.passport_most_recent_pp_pp_card_issue_date = data.passport_most_recent_pp_pp_card_issue_date_lost_stolen
        }
    }
    // end 
    data['passport_core_info_date_of_birth_date'] = moment(data.passport_core_info_date_of_birth).format('DD')
    data['passport_core_info_date_of_birth_month'] = moment(data.passport_core_info_date_of_birth).format('MM')
    data['passport_core_info_date_of_birth_year'] = moment(data.passport_core_info_date_of_birth).format('YYYY')

    // Check less than 16 year olds
    if (moment().diff(moment(data.passport_core_info_date_of_birth), 'years') < 16) {
        let full_name = '', relationship = data['passport_core_info_gender'] === 'F' ? 'MOTHER' : 'FATHER'
        if (data['family_parent_info_1_surname'] && data['family_parent_info_1_given_name']) {
            full_name = data['family_parent_info_1_given_name'] + ' ' + data['family_parent_info_1_surname']
        } else if (data['family_parent_info_2_surname'] && data['family_parent_info_2_given_name']) {
            full_name = data['family_parent_info_2_given_name'] + ' ' + data['family_parent_info_2_surname']
        } else {
            full_name = ''
        }
        data['family_parent_info_full_name'] = full_name === '' ? '' : ('IN CARE OF-' + full_name + ',' + relationship)
    }

    var social_security_numbers = data.personal_core_info_social_security_number.split('-');
    data['personal_core_info_social_security_number_1'] = social_security_numbers[0]
    data['personal_core_info_social_security_number_2'] = social_security_numbers[1]
    data['personal_core_info_social_security_number_3'] = social_security_numbers[2]

    data.personal_core_info_phone = data.personal_core_info_phone.replace(data.personal_core_info_phone_prefix, "")
    data['personal_core_info_phone_3'] = data.personal_core_info_phone.slice(-4)
    data['personal_core_info_phone_2'] = data.personal_core_info_phone.slice(-7, -4)
    data['personal_core_info_phone_1'] = data.personal_core_info_phone.slice(-10, -7)

    if (data.family_parent_info_1_do_you_have_your_parent_info) {
        data['family_parent_info_1_date_of_birth_date'] = moment(data.family_parent_info_1_date_of_birth).format('DD')
        data['family_parent_info_1_date_of_birth_month'] = moment(data.family_parent_info_1_date_of_birth).format('MM')
        data['family_parent_info_1_date_of_birth_year'] = moment(data.family_parent_info_1_date_of_birth).format('YYYY')
        data['family_parent_info_1_nationality'] = data.family_parent_info_1_nationality == 'UNITED STATES OF AMERICA' ? data.family_parent_info_1_nationality : 'other'
        data['family_parent_info_1_place_of_birth'] = `${data.family_parent_info_1_city_of_birth} ${data.family_parent_info_1_state_of_birth ? ", " + data.family_parent_info_1_state_of_birth : ""}, ${data.family_parent_info_1_country_of_birth != "UNITED STATES OF AMERICA" ? data.family_parent_info_1_country_of_birth : ""} `
        data['family_parent_info_1_given_name'] = `${data.family_parent_info_1_given_name}${typeof data.family_parent_info_1_middle_name != 'undefined' ? ' ' + data.family_parent_info_1_middle_name : ''} `
    } else {
        data['family_parent_info_1_given_name'] = "UNKNOWN"
    }

    if (data.family_parent_info_2_do_you_have_your_parent_info) {
        data['family_parent_info_2_date_of_birth_date'] = moment(data.family_parent_info_2_date_of_birth).format('DD')
        data['family_parent_info_2_date_of_birth_month'] = moment(data.family_parent_info_2_date_of_birth).format('MM')
        data['family_parent_info_2_date_of_birth_year'] = moment(data.family_parent_info_2_date_of_birth).format('YYYY')
        data['family_parent_info_2_nationality'] = data.family_parent_info_2_nationality == 'UNITED STATES OF AMERICA' ? data.family_parent_info_2_nationality : 'other'
        data['family_parent_info_2_place_of_birth'] = `${data.family_parent_info_2_city_of_birth} ${data.family_parent_info_2_state_of_birth ? ", " + data.family_parent_info_2_state_of_birth : ""}, ${data.family_parent_info_2_country_of_birth != "UNITED STATES OF AMERICA" ? data.family_parent_info_2_country_of_birth : ""} `
        data['family_parent_info_2_given_name'] = `${data.family_parent_info_2_given_name} ${typeof data.family_parent_info_2_middle_name != 'undefined' ? ' ' + data.family_parent_info_2_middle_name : ''} `
    } else {
        data['family_parent_info_2_given_name'] = "UNKNOWN"
    }

    if (data.personal_core_info_marital_status != 'single') {
        data['family_spouse_date_of_birth_date'] = moment(data.family_spouse_date_of_birth).format('DD')
        data['family_spouse_date_of_birth_month'] = moment(data.family_spouse_date_of_birth).format('MM')
        data['family_spouse_date_of_birth_year'] = moment(data.family_spouse_date_of_birth).format('YYYY')
        data['family_spouse_nationality'] = data.family_spouse_nationality == 'UNITED STATES OF AMERICA' ? data.family_spouse_nationality : 'other'
        data['family_spouse_name'] = `${data.family_spouse_surname} ${data.family_spouse_given_name}${typeof data.family_spouse_middle_name != 'undefined' ? ' ' + data.family_spouse_middle_name : ''} `
        if (data.family_spouse_country_of_birth == 'United States of America' || data.family_spouse_country_of_birth == 'United States of America'.toUpperCase()) {
            data['family_spouse_place_of_birth'] = `${data.family_spouse_city_of_birth}${data.family_spouse_state_of_birth ? ", " + data.family_spouse_state_of_birth : ""}`
        } else {
            data['family_spouse_place_of_birth'] = `${data.family_spouse_city_of_birth}${data.family_spouse_state_of_birth ? ", " + data.family_spouse_state_of_birth : ""}, ${data.family_spouse_country_of_birth}`
        }
        data['family_spouse_date_of_marrige'] = moment(data.family_spouse_date_of_marrige).format(DATE_FORMAT)
        data['personal_core_info_marital_status_check'] = 'no'
        if (data.personal_core_info_marital_status == 'widowed' || data.personal_core_info_marital_status == 'divorced') {
            data['family_spouse_divorce_date'] = moment(data.family_spouse_divorce_date).format(DATE_FORMAT)
            data['personal_core_info_marital_status_check'] = 'yes'
        }
    }

    data['passport_core_info_name'] = `${data.passport_core_info_surname}, ${data.passport_core_info_given_name}${typeof data.passport_core_info_middle_name != 'undefined' ? ' ' + data.passport_core_info_middle_name : ''} `
    data['passport_core_info_date_of_birth'] = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)

    if (typeof data.travel_travel_plan_departure_date != 'undefined') {
        data['travel_travel_plan_departure_date'] = moment(data.travel_travel_plan_departure_date).format(DATE_FORMAT)
    }
    if (typeof data.travel_travel_plan_return_date != 'undefined') {
        data['travel_travel_plan_return_date'] = moment(data.travel_travel_plan_return_date).format(DATE_FORMAT)
    }

    if (data.additional_question_core_info_passport_document == "card") {
        data.additional_question_core_info_passport_book_type = 'none'
    } else {
        data.additional_question_core_info_passport_book_type = 'large'
    }
    if (typeof data.document_most_recent_pp_have_you_applied_issued_us_pp_book_pp_card != 'undefined') {
        if (data.document_most_recent_pp_have_you_applied_issued_us_pp_book_pp_card == "none") {
            data.document_most_recent_pp_have_you_applied_issued_us_pp_book_pp_card = 'no'
        } else {
            data.document_most_recent_pp_have_you_applied_issued_us_pp_book_pp_card = 'yes'
            if (typeof data.passport_most_recent_pp_pp_book_issue_date != 'undefined') {
                data.passport_most_recent_pp_name_pp_book = `${data.passport_core_info_given_name}${typeof data.passport_core_info_middle_name != 'undefined' ? ' ' + data.passport_core_info_middle_name : ''} ${data.passport_core_info_surname} `
                data.passport_most_recent_pp_pp_book_issue_date = moment(data.passport_most_recent_pp_pp_book_issue_date).format(DATE_FORMAT)
            }
            if (typeof data.passport_most_recent_pp_pp_card_issue_date != 'undefined') {
                data.passport_most_recent_pp_name_pp_card = `${data.passport_core_info_given_name}${typeof data.passport_core_info_middle_name != 'undefined' ? ' ' + data.passport_core_info_middle_name : ''} ${data.passport_core_info_surname} `
                data.passport_most_recent_pp_pp_card_issue_date = moment(data.passport_most_recent_pp_pp_card_issue_date).format(DATE_FORMAT)
            }
        }
    }
    // map hair
    data.personal_core_info_hair_color = data_mapping.hair_color[data.personal_core_info_hair_color]

    // map eye
    data.personal_core_info_eye_color = data_mapping.eye_color[data.personal_core_info_eye_color]

    // handle zipcode
    if (data.personal_permanent_address_zip_code) {
        data.personal_permanent_address_zip_code = data.personal_permanent_address_zip_code.split("-")[0]
    }
    data.personal_mailing_address_zip_code = data.personal_mailing_address_zip_code.split("-")[0]
    if (data.personal_mailing_address_country == 'UNITED STATES OF AMERICA') {
        delete data.personal_mailing_address_country
    }

    if (typeof data.additional_question_state_legal_document_issue_date != 'undefined') {
        data['additional_question_state_legal_document_issue_date'] = moment(data.additional_question_state_legal_document_issue_date).format(DATE_FORMAT)
    }

    data.personal_emergency_contact_surname = data.personal_emergency_contact_surname || ""
    data.personal_emergency_contact_given_name = data.personal_emergency_contact_given_name || ""
    data.personal_emergency_contact_name = [data.personal_emergency_contact_given_name, data.personal_emergency_contact_surname].join(" ")
    data.personal_emergency_contact_relationship = data.personal_emergency_contact_relationship || ""
    data.personal_emergency_contact_phone = data.personal_emergency_contact_phone || ""
    data.personal_emergency_contact_country = data.personal_emergency_contact_country || ""
    data.personal_emergency_contact_state = data.personal_emergency_contact_state || ""
    data.personal_emergency_contact_city = data.personal_emergency_contact_city || ""
    data.personal_emergency_contact_address = data.personal_emergency_contact_address || ""
    data.personal_emergency_contact_zip_code = data.personal_emergency_contact_zip_code || ""

    data = helpers.removeStateForUSForm(data)
    data.unicode = false
    return data
}