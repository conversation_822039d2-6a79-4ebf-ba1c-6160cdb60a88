const moment = require('moment')
const helpers = require('../helpers')
const data_mapping = require('../data_mapping')

const FRAME = { "x": 487, "y": 54, "width": 78, "height": 102 }

module.exports = (data) => {
    data['passport_core_info_name'] = `${data.passport_core_info_surname} ${data.passport_core_info_given_name}`
    data['passport_core_info_name_sign'] = data.passport_core_info_name
    data['passport_core_info_date_of_birth_date'] = moment(data.passport_core_info_date_of_birth).format('DD')
    data['passport_core_info_date_of_birth_month'] = moment(data.passport_core_info_date_of_birth).format('MM')
    data['passport_core_info_date_of_birth_year'] = moment(data.passport_core_info_date_of_birth).format('YYYY')
    data['passport_core_info_place_of_birth'] = `${data.passport_core_info_state_of_birth}, ${data.passport_core_info_country_of_birth}`

    data[`passport_core_info_gender_${data.passport_core_info_gender.toLowerCase()}`] = 'x'
    data['personal_permanent_address'] = helpers.capitalize(`${data.personal_permanent_address_address}, ${data.personal_permanent_address_city} ${data.personal_permanent_address_zip_code}, ${data.personal_permanent_address_country}`)

    if (typeof data.passport_core_info_issue_date != 'undefined') {
        data['passport_core_info_issue_date_date'] = moment(data.passport_core_info_issue_date).format('DD')
        data['passport_core_info_issue_date_month'] = moment(data.passport_core_info_issue_date).format('MM')
        data['passport_core_info_issue_date_year'] = moment(data.passport_core_info_issue_date).format('YYYY')
    }

    if (typeof data.family_father_surname != 'undefined') {
        data['family_father_name'] = `${data.family_father_surname} ${data.family_father_given_name}`
        data['family_father_date_of_birth_date'] = moment(data.family_father_date_of_birth).format('DD')
        data['family_father_date_of_birth_month'] = moment(data.family_father_date_of_birth).format('MM')
        data['family_father_date_of_birth_year'] = moment(data.family_father_date_of_birth).format('YYYY')
    }

    if (typeof data.family_mother_surname != 'undefined') {
        data['family_mother_name'] = `${data.family_mother_surname} ${data.family_mother_given_name}`
        data['family_mother_date_of_birth_date'] = moment(data.family_mother_date_of_birth).format('DD')
        data['family_mother_date_of_birth_month'] = moment(data.family_mother_date_of_birth).format('MM')
        data['family_mother_date_of_birth_year'] = moment(data.family_mother_date_of_birth).format('YYYY')
    }

    if (typeof data.family_spouse_surname != 'undefined') {
        data['family_spouse_name'] = `${data.family_spouse_surname} ${data.family_spouse_given_name}`
        data['family_spouse_date_of_birth_date'] = moment(data.family_spouse_date_of_birth).format('DD')
        data['family_spouse_date_of_birth_month'] = moment(data.family_spouse_date_of_birth).format('MM')
        data['family_spouse_date_of_birth_year'] = moment(data.family_spouse_date_of_birth).format('YYYY')
    }

    data['tasks'] = data_mapping.passport_tasks[data.service_core_info_tasks[0]]
    data['additional_question_recommended_content'] = data_mapping.vnm_content[data.additional_question_recommended_reason_request]
    if (data.document_residence_immigration_status_document_type && data.additional_question_residence_immigration_status_document_number && data.additional_question_residence_immigration_status_issue_date) {
        data['immigration_status'] = `${data_mapping.document_type[data.document_residence_immigration_status_document_type]}: Số  ${data.additional_question_residence_immigration_status_document_number}, ngày cấp ${moment(data.additional_question_residence_immigration_status_issue_date).format('DD/MM/YYYY')}`
    }
    data.unicode = true
    data.frame = FRAME
    return data
}