const moment = require('moment')
const helpers = require('../helpers')
const data_mapping = require('../data_mapping')

const DATE_FORMAT = 'DD/MM/YYYY'

module.exports = (data) => {
    data['passport_core_info_name'] = `${data.passport_core_info_surname}, ${data.passport_core_info_given_name}${typeof data.passport_core_info_middle_name != 'undefined' ? ' ' + data.passport_core_info_middle_name : ''}`
    data[`passport_core_info_gender_${data.passport_core_info_gender.toLowerCase()}`] = 'x'
    data['passport_core_info_date_of_birth'] = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)    
    data['passport_core_info_place_of_birth'] = `${data.passport_core_info_country_of_birth}`
    data['personal_core_info_social_security_number'] = `${data.personal_core_info_social_security_number}`
    data['personal_occupation'] = `${data.personal_occupation_occupation}`
    data['personal_company_name'] = `${data.personal_occupation_company_name}`
    data['personal_permanent_address'] = `${data.personal_permanent_address_address}, ${data.personal_permanent_address_city}, ${data.personal_permanent_address_state} ${data.personal_permanent_address_zip_code}`.toUpperCase()            
    data['passport_core_info_passport_number'] = `${data.passport_core_info_passport_number}`

    data.unicode = false
    return data
}