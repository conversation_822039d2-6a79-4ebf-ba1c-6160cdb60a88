const moment = require('moment')
const helpers = require('../helpers')
const data_mapping = require('../data_mapping')

module.exports = (data) => {
    data['passport_core_info_name'] = `${data.passport_core_info_surname}, ${data.passport_core_info_given_name}${typeof data.passport_core_info_middle_name != 'undefined' ? ' ' + data.passport_core_info_middle_name : ''}`      

    data.unicode = false
    return data
}