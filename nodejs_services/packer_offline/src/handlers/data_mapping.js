module.exports = {
    name_change_reason: {
        'changed_by_marriage': "marriage",
        'changed_by_court': 'court_order'
    },
    gender: {
        'M': 'Male',
        'F': 'Female'
    },
    gender_vnm: {
        'M': 'Nam',
        'F': 'Nữ'
    },
    purpose: {
        'business': 'Business Travel',
        'tourist': 'Tourist'
    },
    purpose_vn: {
        'business': 'DN',
        'tourist': 'DL'
    },
    number_of_entries: {
        'single_entry': 'Single entry',
        'multiple_entries': 'Multiple entries'
    },
    duration_month: {
        '1M': 1,
        '3M': 3,
        '6M': 6,
        '1y': 12,
    },
    occupation: {
        'business_person': 'Business person',
        'company_employee': 'Company employee',
        'student': 'Student',
        'retired': 'Retired'
    },
    education: {
        'high_school': 'High school',
        'college': 'College',
        'graduate': 'Graduate',
        'postgraduate': 'Postgraduate'
    },
    validity: [
        {
            validity: '3m',
            entries: 'single_entry',
            value: '2.2.1'
        },
        {
            validity: '3m',
            entries: 'double_entries',
            value: '2.2.2'
        },
        {
            validity: '6m',
            entries: 'double_entries',
            value: '2.2.2'
        },
        {
            validity: '6m',
            entries: 'multiple_entries',
            value: '2.2.3'
        },
        {
            validity: '1y',
            entries: 'multiple_entries',
            value: '2.2.4'
        }
    ],
    form_type: {
        visaForm: 'visa_form',
        approvalLetter: 'approval_letter',
        passportForm: 'passport_form',
        singleCertificate: 'single_certificate'
    },
    relationship: {
        "spouse": "Spouse",
        "mother": "Mother",
        "father": "Father",
        "grandparent": "Grand Parent",
        "sibling": "Sibling",
        "child": "Child",
        "aunt": "Aunt",
        "uncle": "Uncle",
        "cousin": "Cousin",
        "nephew": "Nephew",
        "niece": "Niece",
        "siblings_in_law": "Siblings in law",
        "friend": "Friend"
    },
    us_immigration: {
        "green_card": "Green card",
        "h1b_visa": "H1B visa"
    },
    hair_color: {
        "black": "BLACK",
        "blonde": "BLONDE",
        "brown": "BROWN",
        "gray": "GRAY",
        "red": "RED",
        "bald": "BALD",
        "blue": "BLUE",
        "green": "GREEN",
        "orange": "ORANGE",
        "pink": "PINK",
        "purple": "PURPLE",
        "sandy": "SANDY",
        "white": "WHITE",
        "other": "OTHER"
    },
    eye_color: {
        "black": "BLACK",
        "blue": "BLUE",
        "brown": "BROWN",
        "gray": "GRAY",
        "green": "GREEN",
        "hazel": "HAZEL",
        "maroon": "MAROON",
        "multicolored": "MULTICOLORED",
        "pink": "PINK",
        "other": "OTHER"
    },
    vnm_content: {
        "lost": "Xin cấp lại hộ chiếu do bị mất",
        "damaged": "Xin cấp lại hộ chiếu do bị rách",
        "new": "Xin hộ chiếu mới",
        "renew": "Xin cấp đổi hộ chiếu",
        "child_born_abroad": "Xin hộ chiếu cho con sinh ở nước ngoài",
        "change_name": "Chỉnh sửa tên",
        "change_gender": "Đổi giới tính",
        "same_name_as_foreign_pp": "Lấy tên giống hộ chiếu nước ngoài"
    },
    passport_tasks: {
        "new": "NEW PASSPORT",
        "renew": "RENEW PASSPORT",
        "lost_stolen": "LOST OR STOLEN PASSPORT",
        "damaged": "DAMAGED PASSPORT"
    },
    document_type: {
        "residence_passport": "Passport",
        "us_green_card": "Green Card",
        "us_visa_i20": "Visa I20",
        "us_visa_i94": "Visa I94",
        "id_card": "ID Card"
    }
}