const { CloudSearch } = require('aws-sdk');
var AWS = require('aws-sdk')
var fs = require('fs-extra')
var hummus_recipe = require('hummus-recipe')
const iso = require('iso-3166-1');
require('request')
const request = require('request-promise')
const phoneData = require('./library/country_phone_code.json')

var s3 = new AWS.S3()

exports.findPrefixOfPhone = (phone) => {
    var prefix = ""
    phoneData.forEach(element => {
        if (phone.indexOf(element.dial_code) != -1) {
            prefix = element.dial_code
        }
    });
    return prefix
}

exports.importPhotoToApplicationForm = async (user_photo, folder_path, file_name, name_pdf, { x, y, width, height }) => {
    try {
        //insert photo to PDF form with x, y, width, height
        var source_pdf = name_pdf
        var destination_pdf = folder_path + '/' + file_name
        const pdf_doc = new hummus_recipe(source_pdf, destination_pdf)
        pdf_doc
            .editPage(1)
            .image(user_photo, x, y, {
                width: width,
                height: height,
                keepAspectRatio: false,
                opacity: 1.0,
            })
            .endPage()
            .endPDF()
        return destination_pdf
    } catch (error) {
        console.log(error)
        throw 'PHOTO_WRITE_FAILED'
    }
}

exports.createFolder = (dir) => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, '0777')
    }
    return dir
}

exports.removeFolder = function (dir) {
    if (fs.existsSync(dir)) {
        fs.removeSync(dir)
    }
    return dir
}

exports.s3Download = (bucket, key, local_dest) => {
    if (typeof local_dest == 'undefined') {
        local_dest = key
    }

    let params = {
        Bucket: bucket,
        Key: key
    }

    let file = fs.createWriteStream(local_dest)

    return new Promise((resolve, reject) => {
        s3.getObject(params).createReadStream()
            .on('end', () => {
                setTimeout(() => {
                    return resolve()
                }, 2000)
            })
            .on('error', (error) => {
                return reject(error)
            }).pipe(file)
    })
}

exports.s3Upload = (bucket, key, local_dest) => {
    return new Promise((resolve, reject) => {

        let extn = local_dest.split('.').pop();
        let contentType = 'application/octet-stream';
        if (extn == 'pdf') contentType = "application/pdf";
        if (extn == 'html') contentType = "text/html";
        if (extn == 'css') contentType = "text/css";
        if (extn == 'js') contentType = "application/javascript";
        if (extn == 'png' || extn == 'jpg' || extn == 'gif') contentType = "image/" + extn;

        fs.readFile(local_dest, (err) => {
            if (err) throw err
            const params = {
                Bucket: bucket,
                Key: key,
                Body: fs.readFileSync(local_dest),
                ContentType: contentType
            }
            s3.upload(params, function (error, data) {
                if (error) return reject(error)
                console.log(`File uploaded successfully at ${data.Location}`)
                return resolve()
            })
        })
    })
}

exports.updatePackerStatus = (token, url, body) => {
    return requestHaveBody("POST", token, url, body)
}
const requestHaveBody = async (method, token, uri, body) => {
    try {
        const options = {
            headers: {
                'x-ad-token': token
            },
            method,
            uri,
            body,
            json: true,
        }
        const results = await request(options)
        console.log(results)
        return results
    } catch (e) {
        throw e.error;
    }
}

exports.capitalize = (str) => {
    if (typeof str !== 'string') return ''
    str = str.toLowerCase()
    return str.replace(/\w\S*/g, function (txt) { return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase(); });
}

exports.readFileSync = function readFileSync(path, options) {
    return fs.readFileSync(path, options)
}

exports.removeStateForUSForm = (data) => {
    var us = 'United States of America'
    var ca = 'Canada'
    us = us.toUpperCase()
    ca = ca.toUpperCase()
    var IsCountryOfBithUSOrCA = data.passport_core_info_country_of_birth == us || data.passport_core_info_country_of_birth == ca
    if (IsCountryOfBithUSOrCA) {
        data.passport_core_info_place_of_birth = `${data.passport_core_info_city_of_birth}, ${data.passport_core_info_state_of_birth}`.toUpperCase()
    } else {
        data.passport_core_info_place_of_birth = `${data.passport_core_info_city_of_birth}, ${data.passport_core_info_country_of_birth}`.toUpperCase()
    }

    var IsCountryHomeAddressUSorCA = data.personal_permanent_address_country == us || data.personal_permanent_address_country == ca
    if (!IsCountryHomeAddressUSorCA) {
        data.personal_permanent_address_state = ""
    }
    if (data.personal_permanent_address_country == us) {
        data['personal_permanent_address'] = `${data.personal_permanent_address_address}, ${data.personal_permanent_address_city}, ${data.personal_permanent_address_state} ${data.personal_permanent_address_zip_code}`.toUpperCase()
    } else {
        data['personal_permanent_address'] = `${data.personal_permanent_address_address}, ${data.personal_permanent_address_city} ${data.personal_permanent_address_zip_code}, ${data.personal_permanent_address_country}`.toUpperCase()
    }
    return data
}

exports.prepareDataShipment = (shipment) => {
    var { country: name } = iso.whereAlpha3(shipment.shipping_address.country)
    var result = {
        "shipment_mailing_address_address": shipment.shipping_address.address.toUpperCase(),
        "shipment_mailing_address_city": shipment.shipping_address.city.toUpperCase(),
        "shipment_mailing_address_state": shipment.shipping_address.state.toUpperCase(),
        "shipment_mailing_address_zip_code": shipment.shipping_address.zip_code,
        "shipment_mailing_address_country": name.toUpperCase(),
    }
    return result
}

exports.prepareDataByVisaPods = (inputPods) => {
    let results = {};

    for (const inputPod of inputPods) {
        const { category, sub_category, name, value, option_choice } = inputPod;

        if (value.fe != null) {
            results[`${category}_${sub_category}_${name}`] = value.fe;
            if (option_choice && Object.keys(option_choice).length > 0)
                results = { ...results, ...prepareDataByVisaPods(option_choice[value.fe]) };
        }
    }

    return results;
}