const moment = require('moment')

const data_mapping = require('../data_mapping')
const helpers = require('../helpers')

const DATE_FORMAT = 'MM/DD/YYYY'
const OCCUPATION = data_mapping.occupation
const NA = 'N/A'

module.exports = (data) => {
    data.personal_home_address_street_number_name = helpers.capitalize(`${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}`)
    data['length_of_stay'] = moment(data.service_core_info_exit_date).diff(moment(data.service_core_info_entry_date), 'days') + 1
    data.passport_core_info_date_of_birth = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)
    data.service_core_info_entry_date = moment(data.service_core_info_entry_date).format(DATE_FORMAT)
    data.service_core_info_exit_date = moment(data.service_core_info_exit_date).format(DATE_FORMAT)
    data.passport_core_info_issue_date = moment(data.passport_core_info_issue_date).format(DATE_FORMAT)
    data.passport_core_info_expiration_date = moment(data.passport_core_info_expiration_date).format(DATE_FORMAT)

    data.whopay_relationship = data.visa_info_core_info_purpose == 'tourist' ? "MYSELF" : "COMPANY"
    if (data.family_spouse_given_name && data.family_spouse_surname) {
        data["family_spouse_name"] = `${data.family_spouse_given_name} ${data.family_spouse_surname}`
    }

    if (!data.employee_company_info_street_number_name || data.employee_company_info_street_number_name == "") {
        data.employee_company_info_address = NA
    } else {
        data.employee_company_info_address = helpers.capitalize(`${data.employee_company_info_street_number_name}, ${data.employee_company_info_city}, ${data.employee_company_info_state}${data.employee_company_info_zip_code ? ' ' + data.employee_company_info_zip_code : ''}, ${data.employee_company_info_country}`)
    }

    data.personal_occupation_occupation = OCCUPATION[data.personal_occupation_occupation]
    data.additional_question_phl_phl_other_1 = data.additional_question_phl_phl_other_1 ? "YES" : "NO"

    return data
}
