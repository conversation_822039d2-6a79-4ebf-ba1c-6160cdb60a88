const moment = require('moment')

const data_mapping = require('../data_mapping')
const helpers = require('../helpers')

const DATE_FORMAT = 'DD/MM/YYYY'
const OCCUPATION_MAPPING = data_mapping.occupation
const RELATIONSHIP = data_mapping.relationship
const NA = 'N/A'

module.exports = (data) => {
    data['length_of_stay'] = moment(data.service_core_info_exit_date).diff(moment(data.service_core_info_entry_date), 'days') + 1
    data.passport_core_info_date_of_birth = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)
    data.service_core_info_entry_date = moment(data.service_core_info_entry_date).format(DATE_FORMAT)
    data.service_core_info_exit_date = moment(data.service_core_info_exit_date).format(DATE_FORMAT)
    data.passport_core_info_issue_date = moment(data.passport_core_info_issue_date).format(DATE_FORMAT)
    data.passport_core_info_expiration_date = moment(data.passport_core_info_expiration_date).format(DATE_FORMAT)

    data.passport_core_info_place_of_birth = data.passport_additional_info_city_of_birth + ", " + data.passport_core_info_country_of_birth
    data.personal_home_address = helpers.capitalize(`${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${data.personal_home_address_state}, ${data.personal_home_address_country}`)
    if (data.additional_question_previous_visa_have_you_granted_visa_before) {
        data.additional_question_previous_visa_have_you_granted_visa_before = moment(data.additional_question_previous_visa_issue_dates).format(DATE_FORMAT) + " - " + moment(data.additional_question_previous_visa_expriration_date).format(DATE_FORMAT)
    }

    data.personal_occupation_occupation = OCCUPATION_MAPPING[data.personal_occupation_occupation]
    if (data.employee_company_info_job_title) {
        data.personal_occupation_occupation += " - " + data.employee_company_info_job_title
    }

    if (!data.employee_company_info_street_number_name || data.employee_company_info_street_number_name == "") {
        data.employee_company_info_address = NA
    } else {
        data.employee_company_info_address = `${data.employee_company_info_company_name},${data.employee_company_info_street_number_name}, ${data.employee_company_info_city}, ${data.employee_company_info_state}, ${data.employee_company_info_country}, ${data.personal_core_info_phone}`
    }

    if (data.personal_individual_reference_in_destination_surname && data.personal_individual_reference_in_destination_surname != "") {
        data.personal_individual_reference_in_destination_name = data.personal_individual_reference_in_destination_surname + " " + data.personal_individual_reference_in_destination_given_name
        data.personal_individual_reference_in_destination_address = `${data.personal_individual_reference_in_destination_street_number_name}, ${data.personal_individual_reference_in_destination_city}, ${data.personal_individual_reference_in_destination_state}, ${data.personal_individual_reference_in_destination_country}`
        data.personal_individual_reference_in_destination_date_of_birth = moment(data.personal_individual_reference_in_destination_date_of_birth).format(DATE_FORMAT)
        data.personal_individual_reference_in_destination_relationship = RELATIONSHIP[data.personal_individual_reference_in_destination_relationship]
        data.inviter = 'same as above'
    }
    data.current_date = moment().format(DATE_FORMAT)
    return data
}
