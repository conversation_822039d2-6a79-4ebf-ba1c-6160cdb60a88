const moment = require('moment')
const data_mapping = require('../data_mapping')

const DURATION_MAPPING = data_mapping.duration_month
const NUMBER_OF_ENTRIES = data_mapping.number_of_entries
const PURPOSE = data_mapping.purpose_vn

const DATE_FORMAT = 'DD/MM/YYYY'

module.exports = (data) => {

    if (data.passport_core_info_nationality == 'United States of America')
        data.passport_core_info_nationality = 'USA'

    data.passport_core_info_date_of_birth = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)
    data.passport_core_info_full_name = `${data.passport_core_info_given_name} ${data.passport_core_info_surname}`
    var exit_date = moment(data.service_core_info_entry_date).add(DURATION_MAPPING[data.visa_info_core_info_validity], 'M').toString()
    data.service_core_info_exit_date = moment(exit_date).format(DATE_FORMAT)
    data.service_core_info_entry_date = moment(data.service_core_info_entry_date).format(DATE_FORMAT)

    data.visa_info_core_info_number_of_entries = NUMBER_OF_ENTRIES[data.visa_info_core_info_number_of_entries]
    data.visa_info_core_info_purpose = PURPOSE[data.visa_info_core_info_purpose]
    data['current_date'] = moment().format(DATE_FORMAT)

    return data
}