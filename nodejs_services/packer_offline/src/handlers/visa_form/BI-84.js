const moment = require('moment')

const data_mapping = require('../data_mapping')
const helpers = require('../helpers')

const DATE_FORMAT = 'YYYY/MM/DD'
const DATE_CLEAN_FORMAT = 'YYYYMMDD'
const OCCUPATION_MAPPING = data_mapping.occupation
const RELATIONSHIP = data_mapping.relationship
const NA = 'N/A'

module.exports = (data) => {
    console.log(JSON.stringify(data))
    data['length_of_stay'] = moment(data.service_core_info_exit_date).diff(moment(data.service_core_info_entry_date), 'days') + 1
    data.passport_core_info_date_of_birth = moment(data.passport_core_info_date_of_birth).format(DATE_CLEAN_FORMAT)
    data.passport_core_info_expiration_date = moment(data.passport_core_info_expiration_date).format(DATE_FORMAT)
    data.personal_home_address = helpers.capitalize(`${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${data.personal_home_address_state}${data.personal_home_address_zip_code ? ' ' + data.personal_home_address_zip_code : ''}, ${data.personal_home_address_country}`)
    data.personal_occupation_occupation = OCCUPATION_MAPPING[data.personal_occupation_occupation]
    if (!data.employee_company_info_street_number_name || data.employee_company_info_street_number_name == "") {
        data.employee_company_info = NA
    } else {
        data.employee_company_info = `${data.employee_company_info_company_name},${data.employee_company_info_street_number_name}, ${data.employee_company_info_city}, ${data.employee_company_info_state}, ${data.employee_company_info_country}`
    }
    if (data.family_spouse_date_of_birth) {
        data.family_spouse_date_of_birth = moment(data.family_spouse_date_of_birth).format(DATE_CLEAN_FORMAT)
        data.family_spouse_name = `${data.family_spouse_surname} ${data.family_spouse_given_name}`
    }
    data.service_core_info_entry_date = moment(data.service_core_info_entry_date).format(DATE_FORMAT)
    data.visa_info_core_info_validity = data.length_of_stay + " days"
    data.travel_hotel_stay_hotel = `${data.travel_hotel_stay_hotel_name}, ${data.travel_hotel_stay_hotel_address}`
    data.personal_individual_reference_in_destination_name = `${data.personal_individual_reference_in_destination_surname} ${data.personal_individual_reference_in_destination_given_name}`
    data.personal_individual_reference_in_destination_relationship = RELATIONSHIP[data.personal_individual_reference_in_destination_relationship]
    data.personal_individual_reference_in_destination_address = helpers.capitalize(`${data.personal_individual_reference_in_destination_street_number_name}, ${data.personal_individual_reference_in_destination_city}, ${data.personal_individual_reference_in_destination_state}${data.personal_individual_reference_in_destination_zip_code ? ' ' + data.personal_individual_reference_in_destination_zip_code : ''}, ${data.personal_individual_reference_in_destination_country}`)
    data.current_date = moment().format(DATE_FORMAT)
    return data
}
