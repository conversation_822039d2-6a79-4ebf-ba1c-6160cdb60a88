const moment = require('moment')
const data_mapping = require('../data_mapping')
const helpers = require('../helpers')

const DURATION_MAPPING = data_mapping.duration_month
const GENDER_MAPPING = data_mapping.gender
const NUMBER_OF_ENTRIES = data_mapping.number_of_entries
const PURPOSE = data_mapping.purpose

const DATE_FORMAT = 'DD/MM/YYYY'

const CONSULATE = 'Viet Nam Embassy in China'
const FRAME = { "x": 502, "y": 37, "width": 73, "height": 90 }

module.exports = (data) => {

    data.passport_core_info_full_name = `${data.passport_core_info_given_name} ${data.passport_core_info_surname}`
    data.passport_core_info_gender = GENDER_MAPPING[data.passport_core_info_gender]
    data.passport_core_info_date_of_birth = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)
    data.passport_core_info_expiration_date = moment(data.passport_core_info_expiration_date).format(DATE_FORMAT)

    var exit_date = moment(data.service_core_info_entry_date).add(DURATION_MAPPING[data.visa_info_core_info_validity], 'M').toString()
    data.service_core_info_exit_date = moment(exit_date).format(DATE_FORMAT)
    data.service_core_info_entry_date = moment(data.service_core_info_entry_date).format(DATE_FORMAT)

    data.visa_info_core_info_number_of_entries = NUMBER_OF_ENTRIES[data.visa_info_core_info_number_of_entries]
    data.visa_info_core_info_purpose = PURPOSE[data.visa_info_core_info_purpose]

    data.personal_core_info_home_address = helpers.capitalize(`${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${data.personal_home_address_state}${data.personal_home_address_zip_code ? ' ' + data.personal_home_address_zip_code : ''}, ${data.personal_home_address_country}`)

    data['consulate'] = CONSULATE
    data['current_date'] = moment().format(DATE_FORMAT)
    data['frame'] = FRAME

    return data
}