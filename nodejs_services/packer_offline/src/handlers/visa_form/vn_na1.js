const moment = require('moment')

const data_mapping = require('../data_mapping')
const helpers = require('../helpers')

const DURATION_MAPPING = data_mapping.duration_month
const OCCUPATION_MAPPING = data_mapping.occupation

const FRAME = { "x": 76, "y": 22, "width": 78, "height": 94 }

module.exports = (data) => {
    data['passport_core_info_full_name'] = `${data.passport_core_info_given_name} ${data.passport_core_info_surname}`

    data['passport_core_info_date_of_birth_date'] = moment(data.passport_core_info_date_of_birth).format('DD')
    data['passport_core_info_date_of_birth_month'] = moment(data.passport_core_info_date_of_birth).format('MM')
    data['passport_core_info_date_of_birth_year'] = moment(data.passport_core_info_date_of_birth).format('YYYY')

    data['passport_core_info_expiration_date_date'] = moment(data.passport_core_info_expiration_date).format('DD')
    data['passport_core_info_expiration_date_month'] = moment(data.passport_core_info_expiration_date).format('MM')
    data['passport_core_info_expiration_date_year'] = moment(data.passport_core_info_expiration_date).format('YYYY')

    data['length_of_stay'] = moment(data.service_core_info_exit_date).diff(moment(data.service_core_info_entry_date), 'days') + 1

    data['service_core_info_entry_date_date'] = moment(data.service_core_info_entry_date).format('DD')
    data['service_core_info_entry_date_month'] = moment(data.service_core_info_entry_date).format('MM')
    data['service_core_info_entry_date_year'] = moment(data.service_core_info_entry_date).format('YYYY')


    data.service_core_info_exit_date = moment(data.service_core_info_entry_date).add(DURATION_MAPPING[data.visa_info_core_info_validity], 'M').toString()
    data['service_core_info_exit_date_date'] = moment(data.service_core_info_exit_date).format('DD')
    data['service_core_info_exit_date_month'] = moment(data.service_core_info_exit_date).format('MM')
    data['service_core_info_exit_date_year'] = moment(data.service_core_info_exit_date).format('YYYY')

    data['travel_hotel_stay_hotel'] = `${data.travel_hotel_stay_hotel_name}, ${data.travel_hotel_stay_hotel_address}`

    data["personal_core_info_home_address"] = helpers.capitalize(`${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${data.personal_home_address_state}${data.personal_home_address_zip_code ? ' ' + data.personal_home_address_zip_code : ''}, ${data.personal_home_address_country}`)

    data["personal_occupation_occupation"] = OCCUPATION_MAPPING[data.personal_occupation_occupation]

    data['frame'] = FRAME

    return data
}