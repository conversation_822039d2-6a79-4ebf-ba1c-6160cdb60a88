const moment = require('moment')
const iso = require('iso-3166-1')

const data_mapping = require('../data_mapping')
const helpers = require('../helpers')

const DATE_FORMAT = 'YYYY-MM-DD'
const NA = 'N/A'

const VALIDITY_MAPPING = data_mapping.validity
const EDUCATION = data_mapping.education

module.exports = (data) => {
    // 1.5
    data['passport_core_info_date_of_birth'] = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)

    // 1.8
    data['place_of_birth'] = `${data.passport_additional_info_city_of_birth}, ${data.passport_core_info_country_of_birth}`

    // 1.9
    data['citizenship_number'] = NA

    // 1.12
    data['passport_core_info_issue_date'] = moment(data.passport_core_info_issue_date).format(DATE_FORMAT)

    // 1.14
    data['passport_core_info_expiration_date'] = moment(data.passport_core_info_expiration_date).format(DATE_FORMAT)

    // 1.16
    //   "high_school"
    //   "college"
    //   "graduate"
    //   "postgraduate"
    if (data.personal_core_info_education == "high_school" || data.personal_core_info_education == "graduate") {
        data['personal_core_info_education_other'] = EDUCATION[data.personal_core_info_education]
        data['personal_core_info_education'] = 'other'
    }
    // 1.17
    if (!data.employee_company_info_address || data.employee_company_info_address == "") {
        data.employee_company_info_address = NA
        data.employee_company_info_company_name = NA
        data.employee_company_info_phone = NA
        data.employee_company_info_zip_code = NA
    } else {
        data['employee_company_info_address'] = `${data.employee_company_info_street_number_name}, ${data.employee_company_info_city}, ${data.employee_company_info_state}, ${data.employee_company_info_country}`
    }
    // 1.18
    data["personal_core_info_home_address"] = helpers.capitalize(`${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${data.personal_home_address_state}, ${data.personal_home_address_country}`)

    // 1.21
    if (data.personal_core_info_marital_status != "married" && data.personal_core_info_marital_status != "single") {
        data['personal_core_info_marital_status_other'] = helpers.capitalize(data.personal_core_info_marital_status)
        data['personal_core_info_marital_status'] = 'other'
    }
    // 1.23
    data["family_father_full_name"] = `${data.family_father_given_name} ${data.family_father_surname}`
    data["family_mother_full_name"] = `${data.family_mother_given_name} ${data.family_mother_surname}`
    data["family_father_relationship"] = "Father"
    data["family_mother_relationship"] = "Mother"
    if (data.family_spouse_given_name && data.family_spouse_surname) {
        data["family_spouse_full_name"] = `${data.family_spouse_given_name} ${data.family_spouse_surname}`
        data["family_spouse_relationship"] = "Spouse"
    }
    // 1.24
    data["personal_emergency_contact_full_name"] = `${data.personal_emergency_contact_given_name} ${data.personal_emergency_contact_surname}`
    data["personal_emergency_contact_relationship"] = helpers.capitalize(data.personal_emergency_contact_relationship)
    // 2.2
    var intended_number_entries = ""
    VALIDITY_MAPPING.forEach((item) => {
        if (item.validity == data.visa_info_core_info_validity && item.entries == data.visa_info_core_info_number_of_entries) {
            intended_number_entries = item.value
        }
    });
    if (intended_number_entries == "") {
        intended_number_entries = '2.2.5'
        data['intended_number_entries_special'] = '10 years, Multiple entries'
    }
    data['intended_number_entries'] = intended_number_entries
    // 2.3
    data['express_service'] = 'no' // Note: express service, no, default
    // 2.5
    data['length_of_stay'] = moment(data.service_core_info_exit_date).diff(moment(data.service_core_info_entry_date), 'days') + 1
    // 2.4
    data['service_core_info_entry_date'] = moment(data.service_core_info_entry_date).format(DATE_FORMAT)
    // 2.6
    data['travel_hotel_stay_hotel'] = `${data.travel_hotel_stay_hotel_name}, ${data.travel_hotel_stay_hotel_address}`
    // 2.7
    data['who_pay'] = 'Self' // Note: default
    // 2.8
    data['inviter_name'] = NA
    data['inviter_address'] = NA
    data['inviter_phone'] = NA
    data['inviter_relationship'] = NA
    // 2.9
    data['cn_2_9'] = data.additional_question_previous_visa_have_you_granted_visa_before == true ? 'Yes' : 'No'
    // 3.1
    data['cn_3_1'] = data.additional_question_chn_cn_3_1 == true ? true : false
    // 3.2
    data['cn_3_2'] = data.additional_question_chn_cn_3_2 == true ? true : false
    // 3.3
    data['cn_3_3'] = data.additional_question_chn_cn_3_3 == true ? true : false
    // 3.4
    data['cn_3_4'] = data.additional_question_chn_cn_3_4 == true ? true : false
    // 3.5
    data['cn_3_5'] = data.additional_question_chn_cn_3_5 == true ? true : false
    // 3.8
    data['3.8-1-1'] = NA  // TODO: travel together
    data['3.8-1-2'] = NA  // TODO: travel together
    data['3.8-1-3'] = NA  // TODO: travel together
    // 4.3
    data['current_day'] = moment().format(DATE_FORMAT)
    return data
}
