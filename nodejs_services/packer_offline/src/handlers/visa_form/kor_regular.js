const moment = require('moment')

const data_mapping = require('../data_mapping')
const helpers = require('../helpers')

const DATE_FORMAT = 'YYYY/MM/DD'
const RELATIONSHIP = data_mapping.relationship
const NA = 'N/A'

module.exports = (data) => {
    data.personal_home_address = helpers.capitalize(`${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${data.personal_home_address_state}${data.personal_home_address_zip_code ? ' ' + data.personal_home_address_zip_code : ''}, ${data.personal_home_address_country}`)
    data.personal_emergency_contact_name = data.personal_emergency_contact_surname + " " + data.personal_emergency_contact_given_name
    data.personal_emergency_contact_relationship = RELATIONSHIP[data.personal_emergency_contact_relationship]
    data['length_of_stay'] = moment(data.service_core_info_exit_date).diff(moment(data.service_core_info_entry_date), 'days') + 1
    data.passport_core_info_date_of_birth = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)
    data.service_core_info_entry_date = moment(data.service_core_info_entry_date).format(DATE_FORMAT)
    data.service_core_info_exit_date = moment(data.service_core_info_exit_date).format(DATE_FORMAT)
    data.passport_core_info_issue_date = moment(data.passport_core_info_issue_date).format(DATE_FORMAT)
    data.passport_core_info_expiration_date = moment(data.passport_core_info_expiration_date).format(DATE_FORMAT)
    data.sponsor = data.visa_info_core_info_purpose == 'tourist' ? false : true
    data.whopay_relationship = data.visa_info_core_info_purpose == 'tourist' ? "MYSELF" : "COMPANY"
    data.whopay_phone = data.visa_info_core_info_purpose == 'tourist' ? data.personal_core_info_phone : data.employee_company_info_phone
    if (data.family_spouse_residence_address_street_number_name && data.family_spouse_residence_address_street_number_name != "") {
        data.family_spouse_date_of_birth = moment(data.family_spouse_date_of_birth).format(DATE_FORMAT)
        data.family_spouse_residence_address = helpers.capitalize(`${data.family_spouse_residence_address_street_number_name}, ${data.family_spouse_residence_address_city}, ${data.family_spouse_residence_address_state}${data.family_spouse_residence_address_zip_code ? ' ' + data.family_spouse_residence_address_zip_code : ''}, ${data.family_spouse_residence_address_country}`)
        data.family_child_question = data.family_child_how_many_chirdren == 0 ? false : true
    }

    if (!data.employee_company_info_street_number_name || data.employee_company_info_street_number_name == "") {
        data.employee_company_info_address = NA
    } else {
        data.employee_company_info_address = helpers.capitalize(`${data.employee_company_info_street_number_name}, ${data.employee_company_info_city}, ${data.employee_company_info_state}${data.employee_company_info_zip_code ? ' ' + data.employee_company_info_zip_code : ''}, ${data.employee_company_info_country}`)
    }

    if (data.passport_second_passport_expiration_date) {
        data.passport_second_passport_expiration_date = moment(data.passport_second_passport_expiration_date).format(DATE_FORMAT)
    }
    data.travel_hotel_stay_hotel = `${data.travel_hotel_stay_hotel_name}, ${data.travel_hotel_stay_hotel_address}`
    return data
}
