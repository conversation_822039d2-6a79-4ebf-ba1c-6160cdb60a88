const moment = require('moment')
const data_mapping = require('../data_mapping')
const helpers = require('../helpers')
const PURPOSE = data_mapping.purpose

module.exports = (data) => {
    data['passport_core_info_date_of_birth_date'] = moment(data.passport_core_info_date_of_birth).format('DD')
    data['passport_core_info_date_of_birth_month'] = moment(data.passport_core_info_date_of_birth).format('MM')
    data['passport_core_info_date_of_birth_year'] = moment(data.passport_core_info_date_of_birth).format('YYYY')

    data['passport_core_info_expiration_date_date'] = moment(data.passport_core_info_expiration_date).format('DD')
    data['passport_core_info_expiration_date_month'] = moment(data.passport_core_info_expiration_date).format('MM')
    data['passport_core_info_expiration_date_year'] = moment(data.passport_core_info_expiration_date).format('YYYY')

    data['passport_core_info_issue_date_date'] = moment(data.passport_core_info_issue_date).format('DD')
    data['passport_core_info_issue_date_month'] = moment(data.passport_core_info_issue_date).format('MM')
    data['passport_core_info_issue_date_year'] = moment(data.passport_core_info_issue_date).format('YYYY')

    data['service_core_info_entry_date_date'] = moment(data.service_core_info_entry_date).format('DD')
    data['service_core_info_entry_date_month'] = moment(data.service_core_info_entry_date).format('MM')
    data['service_core_info_entry_date_year'] = moment(data.service_core_info_entry_date).format('YYYY')

    data['age'] = moment().diff(moment(data.passport_core_info_date_of_birth), 'years') + 1

    data['personal_home_address'] = helpers.capitalize(`${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${data.personal_home_address_state}${data.personal_home_address_zip_code ? ' ' + data.personal_home_address_zip_code : ''}, ${data.personal_home_address_country}`)
    data['visa_info_core_info_purpose'] = PURPOSE[data.visa_info_core_info_purpose]

    return data
}