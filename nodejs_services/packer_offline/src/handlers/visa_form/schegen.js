const moment = require('moment')

const data_mapping = require('../data_mapping')
const helpers = require('../helpers')

const DATE_FORMAT = 'DD/MM/YYYY'
const OCCUPATION_MAPPING = data_mapping.occupation
const RELATIONSHIP = data_mapping.relationship
const US_IMMIGRATION = data_mapping.us_immigration
const NA = 'N/A'

module.exports = (data) => {
    if (data.additional_question_family_relationship) {
        if (data.additional_question_family_relationship == 'nephew' || data.additional_question_family_relationship == 'niece') {
            data.additional_question_family_relationship = 'grandchild'
        } else if (data.additional_question_family_relationship != 'spouse' && data.additional_question_family_relationship != 'child') {
            data.additional_question_family_relationship_other = RELATIONSHIP[data.additional_question_family_relationship]
            data.additional_question_family_relationship = 'other'
        }
    }

    if (!data.employee_company_info_street_number_name || data.employee_company_info_street_number_name == "") {
        data.employee_company_info = NA
    } else {
        data.employee_company_info = `${data.employee_company_info_company_school_name}, ${data.employee_company_info_street_number_name}, ${data.employee_company_info_city}, ${data.employee_company_info_state}, ${data.employee_company_info_country}, ${data.personal_core_info_phone}`
    }

    if (!data.personal_company_reference_in_destination_company_name || data.personal_company_reference_in_destination_company_name == "") {
        data.personal_company_reference_in_destination_address = NA
    } else {
        data.personal_company_reference_in_destination_address = `${data.personal_company_reference_in_destination_company_name}, ${data.personal_company_reference_in_destination_street_number_name}, ${data.personal_company_reference_in_destination_street_number_name}, ${data.personal_company_reference_in_destination_city}, ${data.personal_company_reference_in_destination_state}, ${data.personal_company_reference_in_destination_country}, ${data.personal_company_reference_in_destination_phone}`
    }

    if (!data.personal_individual_reference_in_destination_surname || data.personal_individual_reference_in_destination_surname == "") {
        data.personal_individual_reference_in_destination_contact = NA
    } else {
        data.personal_individual_reference_in_destination_contact = `${data.personal_individual_reference_in_destination_surname} ${data.personal_individual_reference_in_destination_given_name}, ${data.personal_individual_reference_in_destination_street_number_name}, ${data.personal_individual_reference_in_destination_city}, ${data.personal_individual_reference_in_destination_state}, ${data.personal_individual_reference_in_destination_country}, ${data.personal_individual_reference_in_destination_phone}, ${data.personal_individual_reference_in_destination_email_address}`
    }

    if (data.additional_question_fingerprint_previously_date && data.additional_question_fingerprint_previously_date != "") {
        data.additional_question_fingerprint_previously_date = moment(data.additional_question_fingerprint_previously_date).format(DATE_FORMAT)
    }

    // data.immigration_status_us_immigration_usa_green_card_or_h1b_visa = US_IMMIGRATION[data.immigration_status_us_immigration_usa_green_card_or_h1b_visa]
    data.personal_occupation_occupation = OCCUPATION_MAPPING[data.personal_occupation_occupation]
    data.personal_core_info_home_address = helpers.capitalize(`${data.personal_home_address_street_number_name}, ${data.personal_home_address_city}, ${data.personal_home_address_state}, ${data.personal_home_address_country} - ${data.personal_core_info_email_address}`)
    data.travel_hotel_stay_address = `${data.travel_hotel_stay_hotel_name}, ${data.travel_hotel_stay_hotel_address}`
    data.passport_core_info_date_of_birth = moment(data.passport_core_info_date_of_birth).format(DATE_FORMAT)
    data.passport_core_info_issue_date = moment(data.passport_core_info_issue_date).format(DATE_FORMAT)
    data.passport_core_info_expiration_date = moment(data.passport_core_info_expiration_date).format(DATE_FORMAT)
    if (data.additional_question_family_date_of_birth) {
        data.additional_question_family_date_of_birth = moment(data.additional_question_family_date_of_birth).format(DATE_FORMAT)
    }
    data.immigration_status_us_immigration_expiration_date = moment(data.immigration_status_us_immigration_expiration_date).format(DATE_FORMAT)
    data.service_core_info_entry_date = moment(data.service_core_info_entry_date).format(DATE_FORMAT)
    data.service_core_info_exit_date = moment(data.service_core_info_exit_date).format(DATE_FORMAT)

    return data
}