const moment = require('moment')

const data_mapping = require('../data_mapping')

const DURATION_MAPPING = data_mapping.duration_month
const GENDER_MAPPING = data_mapping.gender
const DATE_FORMAT = 'DD/MM/YYYY'

module.exports = (data) => {

    var exit_date = moment(data.visa_info.entry_date).add(DURATION_MAPPING[data.visa_info.visa_validity], 'M').toString()
    var apps = []
    for (let index = 0; index < data.applications.length; index++) {
        apps.push({
            fullname: `${data.applications[index].given_name} ${data.applications[index].surname}`,
            date_of_birth: moment(data.applications[index].date_of_birth).format(DATE_FORMAT),
            nationality: data.applications[index].nationality,
            passport_number: data.applications[index].passport_number,
            gender: GENDER_MAPPING[data.applications[0].gender],
            gender_vi: data.applications[0].gender == "M" ? "Ông (Mr)" : "<PERSON><PERSON> (Mrs)"
        })
    }

    return {
        ref_no: data.document.input.ref_no,
        issue_date: moment(data.document.input.issue_date).format('DD MMM YYYY'),
        issue_date_vi: moment(data.document.input.issue_date).format(DATE_FORMAT),
        issue_date_day: moment(data.document.input.issue_date).format('DD'),
        issue_date_month: moment(data.document.input.issue_date).format('MM'),
        issue_date_year: moment(data.document.input.issue_date).format('YYYY'),
        company_name_en: data.document.input.company_name_en,
        company_name_vi: data.document.input.company_name_vi,
        letter_number: data.document.input.letter_number,
        purpose_vi: "du lịch", //default
        purpose_en: "tourism", //default
        entry_date_vi: moment(data.visa_info.entry_date).format(DATE_FORMAT),
        exit_date_vi: moment(exit_date).format(DATE_FORMAT),
        entry_date_en: moment(data.visa_info.entry_date).format(DATE_FORMAT),
        exit_date_en: moment(exit_date).format(DATE_FORMAT),
        apps
    }
}