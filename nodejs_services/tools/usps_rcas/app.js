const axios = require('axios');
const { GoogleSpreadsheet } = require('google-spreadsheet');
const { JWT } = require('google-auth-library');
// https://docs.google.com/spreadsheets/d/1D3A_HwhT7NjlEBg1aBAZXF0MUjzjAkLOgzsdGMzaSpI/edit#gid=0
async function updateSheetWithAPIResponse() {
    const serviceAccountAuth = new JWT({
        email: "<EMAIL>",
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        scopes: [
            'https://www.googleapis.com/auth/spreadsheets',
        ],
    });

    const doc = new GoogleSpreadsheet('1D3A_HwhT7NjlEBg1aBAZXF0MUjzjAkLOgzsdGMzaSpI', serviceAccountAuth); // Replace with your Google Sheets ID
    await doc.loadInfo();
    console.log(doc.title);
    const sheet = doc.sheetsByIndex[0];

    const resp_dates = await axios.post(
        'https://tools.usps.com/UspsToolsRestServices/rest/v2/postalBusinessDays',
        {},
        {
            headers: {
                'authority': 'tools.usps.com',
                'content-type': 'application/json;charset=UTF-8',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'x-requested-with': 'XMLHttpRequest',
            },
        }
    );
    await sheet.loadCells('B2:B5');
    const config = {
        zip_code: String(sheet.getCellByA1('B2').value),
        number_of_adults: String(sheet.getCellByA1('B3').value),
        from_date: Number(sheet.getCellByA1('B4').value),
        to_date: Number(sheet.getCellByA1('B5').value),
    };

    const resp_zipcodes = await axios.get(`https://api4free.vercel.app/nearby_zip_codes?zip_code=${config.zip_code}&limit=10`);

    let row_index = 9;
    for (const working_date of resp_dates.data.filter(v => Number(v) >= config.from_date && Number(v) <= config.to_date).slice(0, 5)) {
        for (const zip_code of resp_zipcodes.data.data) {
            const resp_search = await axios.post(
                'https://tools.usps.com/UspsToolsRestServices/rest/v2/facilityScheduleSearch',
                {
                    poScheduleType: 'PASSPORT',
                    date: working_date,
                    numberOfAdults: config.number_of_adults,
                    numberOfMinors: '0',
                    radius: '20',
                    zip5: zip_code,
                    city: '',
                    state: '',
                },
                {
                    headers: {
                        'authority': 'tools.usps.com',
                        'content-type': 'application/json;charset=UTF-8',
                        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'x-requested-with': 'XMLHttpRequest',
                    },
                }
            );

            for (const facilityDetail of resp_search.data.facilityDetails) {
                console.log(facilityDetail);
                for (const date of facilityDetail.date) {
                    if (date.status === 'true') {
                        await sheet.loadCells(`${'A' + row_index}:${'F' + row_index}`);

                        sheet.getCellByA1('A' + row_index).value = date.date;
                        sheet.getCellByA1('B' + row_index).value = facilityDetail.address.postalCode;
                        sheet.getCellByA1('C' + row_index).value = `${facilityDetail.name}, ${facilityDetail.address.addressLineOne}, ${facilityDetail.address.city}, ${facilityDetail.address.stateProvence}, ${facilityDetail.address.postalCode}`;
                        sheet.getCellByA1('D' + row_index).value = `Go to website and search by zipcode ${zip_code} and date: ${working_date}`;
                        sheet.getCellByA1('E' + row_index).value = facilityDetail.distance;
                        sheet.getCellByA1('F' + row_index).value = (new Date()).toLocaleDateString();

                        await sheet.saveUpdatedCells();

                        row_index++;
                    }
                }
            }
        }
    }
}

// Call the function
updateSheetWithAPIResponse()
    .then(() => {
        console.log('Sheet updated successfully.');
    })
    .catch((err) => {
        console.error('Error updating sheet:', err);
    });