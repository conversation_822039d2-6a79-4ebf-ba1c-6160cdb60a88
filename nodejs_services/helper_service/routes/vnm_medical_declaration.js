const FormData = require('form-data');
const axios = require('axios');
const cheerio = require('cheerio');

// https://tokhaiyte.vn/?page=Passenger.PrintFull.detail&groupId=5e50878976801b710216b807&id=6268ab75c05ba632801aefe3&code=TK-*********&passport=*********&lang=vi
const init_medical_declaration = async () => {
    const resp = await axios({
        method: 'get',
        url: 'https://tokhaiyte.vn/?&module=Content.Form&moduleId=1009&layout=Project.TKYT.Group.HealthRecord.Passenger.edit&submitService=Project.TKYT.Home.Passenger.sendRequest&service=Project.TKYT.Home.Passenger.select&hasCaptcha=1&hideHeader=1&tab=1&lang=&langView=&itemId=&code=&passport=&healthId=&gateId=&site=2001432',
        headers: {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36',
        }
    })

    const auth = resp.headers["set-cookie"].map(x => x.split(";")[0]).join(";")
    const $ = cheerio.load(resp.data);

    const form = $('form').serializeArray().reduce((obj, item) => {
        obj[item.name] = item.value;
        return obj;
    }, {});
    return {
        auth: auth,
        form: form,
    }
}

const solve_captcha = async (cookie) => {
    const resp = await axios({
        method: 'get',
        url: 'https://tokhaiyte.vn/api/Common/Captcha/getCaptcha?returnType=image&site=2001432&width=150&height=50&t=' + new Date().getTime(),
        headers: {
            'Cookie': cookie
        },
        responseType: "stream"
    })

    var data = new FormData();
    data.append('file', resp.data);

    const resp2 = await axios({
        method: 'post',
        url: 'https://api.jp.ariadirect.com/v1/mrz-parser/capcha-solver-by-binary',
        data: data,
        headers: {
            ...data.getHeaders()
        }
    })
    return resp2.data.data

}
const vnmMedicalDeclaration = async (req, res) => {
    try {
        const init_data = await init_medical_declaration()
        console.log(init_data)
        const data = new FormData();
        data.append('groupId', init_data.form["groupId"]);
        data.append('type', "");
        data.append('fields[deviceId]', "");
        data.append('fields[ip]', "***********");
        data.append('fields[lat]', "");
        data.append('fields[long]', "");
        data.append('fields[lang]', 'en');
        data.append('fields[object]', 'expert');
        data.append('fields[gateId]', '5e5377a376801b3e76078197');
        data.append('fields[fullName]', 'NGUYEN VAN THANH');
        data.append('fields[birthDay]', '13');
        data.append('fields[birthMonth]', '01');
        data.append('fields[birthYear]', '1990');
        data.append('fields[gender]', 'Nam');
        data.append('fields[nation]', '5b0ec228e138230cb0072f82');
        data.append('fields[passport]', '*********');
        data.append('fields[vehicleChoice]', 'airplane');
        data.append('fields[airplane]', '1');
        data.append('fields[vehicleCode]', 'A350-942');
        data.append('fields[seat]', '');
        data.append('fields[startDay]', '21');
        data.append('fields[startMonth]', '04');
        data.append('fields[startYear]', '2022');
        data.append('fields[entryDay]', '21');
        data.append('fields[entryMonth]', '04');
        data.append('fields[entryYear]', '2022');
        data.append('fields[countryStartPlace]', '5d8c3cae76801b4a0103e55f');
        data.append('fields[startProvinceId]', '5e55fec876801b31db380e62');
        data.append('fields[startPlace]', 'Alaska');
        data.append('fields[countryEndPlace]', '5b0ec228e138230cb0072f82');
        data.append('fields[endProvinceId]', '5657e3ab7f8b9a117c8b4599');
        data.append('fields[endPlace]', 'Thành phố Hồ Chí Minh');
        data.append('fields[countryPassing]', 'USA');
        data.append('fields[afterIsolationStayProvinceId]', '5657e3ab7f8b9a117c8b4599');
        data.append('fields[afterIsolationStayDistrictId]', '5657e3ac7f8b9a117c8b479c');
        data.append('fields[afterIsolationStayWardId]', '5e6a0c4db0b627213042ef73');
        data.append('fields[addressStayAfterIsolation]', 'Pham Ngu Lao');
        data.append('fields[provinceId]', '5657e3ab7f8b9a117c8b4599');
        data.append('fields[districtId]', '5657e3ac7f8b9a117c8b479c');
        data.append('fields[wardId]', '5e6a0c4db0b627213042ef73');
        data.append('fields[address]', 'Pham Ngu Lao');
        data.append('fields[phone]', '0908675124');
        data.append('fields[email]', '');
        data.append('fields[hasFever]', '2');
        data.append('fields[hasCough]', '2');
        data.append('fields[hasBreath]', '2');
        data.append('fields[hasSoreThroat]', '2');
        data.append('fields[hasNausea]', '2');
        data.append('fields[hasDiarrhea]', '2');
        data.append('fields[hasHemorrhage]', '2');
        data.append('fields[hasRash]', '2');
        data.append('fields[vacxin]', '');
        data.append('fields[hasAnimal]', '2');
        data.append('fields[hasPatient]', '2');
        data.append('fields[vaccines]', '');
        data.append('fields[testResult]', '');
        data.append('fields[testResultTime]', '');
        data.append('fields[testType]', '');
        data.append('fields[testCountryId]', '');
        data.append('file', '');
        data.append('fields[testResultFile]', '');

        const captcha = await solve_captcha(init_data.auth)
        console.log(captcha)
        data.append('captcha_code', captcha);

        data.append('securityToken', init_data.form["securityToken"]);
        data.append('submitFormId', init_data.form["submitFormId"]);
        data.append('moduleId', init_data.form["moduleId"]);


        const resp = await axios({
            method: 'post',
            url: 'https://tokhaiyte.vn/api/Project/TKYT/Home/Passenger/sendRequest?site=2001432',
            headers: {
                'Accept': '*/*',
                'X-Requested-With': 'XMLHttpRequest',
                'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36',
                'sec-ch-ua-platform': '"Linux"',
                'Origin': 'https://tokhaiyte.vn',
                'Referer': 'https://tokhaiyte.vn/',
                'Accept-Language': 'en',
                'Cookie': init_data.auth,
                ...data.getHeaders()
            },
            data: data
        })
        console.log(resp.data)
        res.json({
            data: resp.data,
            success: resp.data.status == "SUCCESS",
        })
    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: "fail",
        })
    }
}

module.exports = {
    vnmMedicalDeclaration,
}