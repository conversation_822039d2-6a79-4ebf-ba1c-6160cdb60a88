const { createReport } = require('docx-templates');
const fs = require('fs');
const tmp = require('tmp');

const generateDocxFromTemplate = async (req, res) => {
    try {
        // 1. read template file
        const template = fs.readFileSync(__dirname + `/../templates/${req.query.template}.docx`);

        // 2. process the template
        const data = req.body
        const buffer = await createReport({
            template,
            data,
        });
        // 3. save output
        const tmp_file = tmp.fileSync({ mode: 0o644, prefix: 'temp-', postfix: '.docx' });
        fs.writeFileSync(tmp_file.name, buffer);
        res.download(tmp_file.name, 'export.docx')
    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: "fail",
        })
    }
}

module.exports = {
    generateDocxFromTemplate,
}