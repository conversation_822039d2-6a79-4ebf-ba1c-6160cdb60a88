const fs = require('fs');
const tmp = require('tmp');
const { TemplateHandler } = require('easy-template-x');
const XlsxTemplate = require('xlsx-template');

const generateXlsxFromTemplate = async (req, res) => {
    console.log(JSON.stringify(req.body))
    try {
        // 1. read template file
        const templateFile = fs.readFileSync(__dirname + `/../templates/${req.query.template}.xlsx`);

        // 2. create tmp file
        const template = new XlsxTemplate(templateFile);

        // Replacements take place on first sheet
        const sheetNumber = 1;

        // Set up some placeholder values matching the placeholders in the template
        const values = req.body

        // Perform substitution
        template.substitute(sheetNumber, values);

        // Get binary data
        const data = template.generate({ type: 'uint8array' });



        // 3. save output
        const tmp_file = tmp.fileSync({ mode: 0o644, prefix: 'temp-', postfix: '.xlsx' });
        fs.writeFileSync(tmp_file.name, data);
        res.download(tmp_file.name, 'export.xlsx')
        // fs.unlinkSync(tmp_file.name);
    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: "fail",
        })
    }
}

module.exports = {
    generateXlsxFromTemplate,
}