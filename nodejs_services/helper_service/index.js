const express = require('express')
require('express-group-routes');
const cors = require('cors');
const { versionHand<PERSON> } = require('./routes/version');
const { generateDocxFromTemplate } = require('./routes/docx');
const { generateXlsxFromTemplate } = require('./routes/xlsx');
const { vnmMedicalDeclaration } = require('./routes/vnm_medical_declaration');

const PORT = process.env.PORT || 3000

var app = express()
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: false }));
app.use(cors())


app.group("/v1/helper-service", (router) => {
    router.get("/version", versionHandler)
    router.get("/status", versionHandler)
    router.post("/docx", generateDocxFromTemplate)
    router.post("/xlsx", generateXlsxFromTemplate)
    router.post("/vnm-medical-declaration", vnmMedicalDeclaration)
});


console.log("Listen " + PORT)
app.listen(PORT)