const axios = require("axios");
const X_API_KEY = "************************************************************************************************************"

const get_image_captcha_text = async (buffer, model = 'claude-3-haiku-20240307') => {
    try {
        const base64Image = buffer.toString('base64');

        const mediaType = buffer[0] === 0x89 && buffer[1] === 0x50 ? 'image/png' :
            buffer[0] === 0xff && buffer[1] === 0xd8 ? 'image/jpeg' :
                'image/png';

        const response = await axios({
            method: 'post',
            url: 'https://api.anthropic.com/v1/messages',
            headers: {
                'anthropic-version': '2023-06-01',
                'content-type': 'application/json',
                'x-api-key': X_API_KEY
            },
            data: {
                model,
                max_tokens: 1024,
                messages: [{
                    role: 'user',
                    content: [
                        {
                            type: 'text',
                            text: 'Please read and return only the text shown in this captcha image. Return only the alphanumeric characters with no spaces or special characters.'
                        },
                        {
                            type: 'image',
                            source: {
                                type: 'base64',
                                media_type: mediaType,
                                data: base64Image
                            }
                        }
                    ]
                }]
            }
        });

        const captchaText = response.data.content[0].text.trim().replace(/[^a-zA-Z0-9]/g, '');
        return captchaText;

    } catch (error) {
        console.log(error.message);
        return null;
    }
};

module.exports = { get_image_captcha_text }