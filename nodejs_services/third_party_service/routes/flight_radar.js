const puppeteer = require('puppeteer');
const qs = require('qs');
const FormData = require('form-data');
const axios = require('axios');
const moment = require('moment-timezone');


const getFlightList = async (req, res) => {
    try {
        let { airport, date, service } = req.query

        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            // url: `https://api.flightradar24.com/common/v1/airport.json?code=${airport.toLowerCase()}&plugin[]=schedule&plugin-setting[schedule][mode]=${service === 'arrival' ? "arrivals" : "departures"}&plugin-setting[schedule][timestamp]=${moment(date).unix()}&limit=100&page=1`,
            url: `https://api.flightradar24.com/common/v1/airport.json?code=SGN&plugin[]=schedule&plugin-setting[schedule][mode]=arrivals&plugin-setting[schedule][timestamp]=${moment(date).unix()}&limit=100&page=-1`,
            headers: {
                'origin': 'https://www.flightradar24.com',
                'referer': 'https://www.flightradar24.com/',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
            },
        };
        const resp = await axios(config)
        let result = []
        resp.data.result.response.airport.pluginData.schedule.arrivals.data.map(v => {
            result.push({
                type: 'arrival',
                flight: v.flight.identification.number.default,
                from: v.flight.airport.origin.code.iata,
                to: airport.toUpperCase(),
                status: v.flight.status.generic.status.type,
                schedule_time_utc: moment.unix(v.flight.time.scheduled.arrival).toISOString(),
                timezone: v.flight.airport.destination.timezone.name,
                schedule_time_local: moment.unix(v.flight.time.scheduled.arrival).tz(v.flight.airport.destination.timezone.name).toLocaleString(),
            })
        })
        resp.data.result.response.airport.pluginData.schedule.departures.data.map(v => {
            result.push({
                type: 'departure',
                flight: v.flight.identification.number.default,
                from: airport.toUpperCase(),
                to: v.flight.airport.destination.code.iata,
                status: v.flight.status.generic.status.type,
                schedule_time: moment.unix(v.flight.time.scheduled.departure).toISOString(),
                timezone: v.flight.airport.origin.timezone.name,
                schedule_time_local: moment.unix(v.flight.time.scheduled.departure).tz(v.flight.airport.origin.timezone.name).toLocaleString(),
            })
        })
        res.json({
            success: true,
            data: result,
        })
    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: error.message,
        })
    }
}

getFlightList({
    query: {
        airport: 'SGN',
        date: '2024-05-25T20:10:00Z',
        service: 'arrival',
    }
}, {
    json: console.log
})

module.exports = {
    getFlightList,
}