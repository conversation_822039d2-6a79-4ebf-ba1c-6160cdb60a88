const axios = require('axios');
const AWS = require('aws-sdk');



const sqs_config = JSON.parse(process.env.ad_sqs)
if (!sqs_config.url_prefix) {
    console.log('missing url_prefix in ad_sqs')
    process.exit()
}

if (!sqs_config.notification_sqs_name) {
    console.log('missing notification_sqs_name in ad_sqs')
    process.exit()
}

const aws_config = JSON.parse(process.env.ad_aws)
if (!aws_config.region) {
    console.log('missing region in ad_aws')
    process.exit()
}

const ad_email = JSON.parse(process.env.ad_email)
if (!ad_email.info) {
    console.log('missing info in ad_email')
    process.exit()
}


// Set the region
AWS.config.update({
    region: aws_config.region,
})


const SQS_QUEUE_URL = `${sqs_config.url_prefix}/${sqs_config.notification_sqs_name}`

// Create an SQS service object
const sqs = new AWS.SQS({ apiVersion: '2012-11-05' });

const USPS_URL = "https://tools.usps.com/UspsToolsRestServices/rest/v2"

const getAppointmentTimes = async (req, res) => {
    try {
        const number_of_adults = Number(req.body.number_of_adults) || 1
        const number_of_minors = Number(req.body.number_of_minors) || 0
        const zip_codes = req.body.zip_codes.split(' ')

        const zip_code = zip_codes.length === 1 ? req.body.zip_codes : ""

        const city = zip_codes.length > 1 ? zip_codes.slice(0, zip_codes.length - 1).join('') : ""
        const state = zip_codes.length > 1 ? zip_codes.slice(zip_codes.length - 1, zip_codes.length).join('') : ""
        const radius = req.body.radius || 100

        const now = new Date()
        const date = req.body.date || `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}`

        const data = []
        const config = {
            "poScheduleType": "PASSPORT",
            "date": date,
            "numberOfAdults": number_of_adults.toString(),
            "numberOfMinors": number_of_minors.toString(),
            "radius": radius.toString(),
            "zip5": zip_code,
            "city": city,
            "state": state
        }
        console.log(config)
        // 1.
        const resp1 = await axios({
            method: 'POST',
            maxBodyLength: Infinity,
            url: `${USPS_URL}/facilityScheduleSearch`,
            headers: {
                'authority': 'tools.usps.com',
                'content-type': 'application/json;charset=UTF-8',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'x-requested-with': 'XMLHttpRequest',
            },
            data: config

        })

        const facilities = resp1.data.facilityDetails
        console.log(resp1.data)
        // 2
        for (const facility of facilities) {
            const resp2 = await axios({
                url: `${USPS_URL}/appointmentDateSearch`,
                method: 'post',
                maxBodyLength: Infinity,
                headers: {
                    'authority': 'tools.usps.com',
                    'content-type': 'application/json;charset=UTF-8',
                    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'x-requested-with': 'XMLHttpRequest',
                },
                data: {
                    "numberOfAdults": number_of_adults.toString(),
                    "numberOfMinors": number_of_minors.toString(),
                    "fdbId": facility.fdbId.toString(),
                    "productType": "PASSPORT"
                }

            })

            if (resp2.data.dates && resp2.data.dates.length > 0) {
                const dates = []
                for (const date of resp2.data.dates.slice(0, 3)) {
                    const item = {
                        date: date,
                        date_display: `${date.slice(0, 4)}-${date.slice(4, 6).toString().padStart(2, '0')}-${date.slice(6, 8).padStart(2, '0')}`,
                        times: []
                    }
                    const resp3 = await axios({
                        url: `${USPS_URL}/appointmentTimeSearch`,
                        method: 'post',
                        maxBodyLength: Infinity,
                        headers: {
                            'authority': 'tools.usps.com',
                            'content-type': 'application/json;charset=UTF-8',
                            'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                            'x-requested-with': 'XMLHttpRequest',
                        },
                        data: {
                            "date": date,
                            "productType": "PASSPORT",
                            "numberOfAdults": number_of_adults.toString(),
                            "numberOfMinors": number_of_minors.toString(),
                            "excludedConfirmationNumber": [
                                ""
                            ],
                            "fdbId": [
                                facility.fdbId.toString(),
                            ],
                            "skipEndOfDayRecord": true
                        }

                    })

                    if (resp3.data.appointmentTimeDetailExtended) {
                        item.times = resp3.data.appointmentTimeDetailExtended.filter(v => v.appointmentStatus === 'Available').map(v => {
                            return {
                                appointment_id: v.appointmentId,
                                start_time: v.startTime,
                            }
                        })

                        item.times.sort((a, b) => Number(a.start_time) - Number(b.start_time))
                    }


                    if (item.times.length > 0) {
                        dates.push(item)
                    }
                }

                dates.sort((a, b) => Number(a.date) - Number(b.date))
                if (dates.length) {
                    data.push({
                        name: facility.name,
                        distance: Number(facility.distance).toFixed(2),
                        address: {
                            address: facility.address.addressLineOne,
                            city: facility.address.city,
                            state: facility.address.stateProvence,
                        },
                        fdb_id: facility.fdbId.toString(),
                        dates: dates,
                    })
                }

            }
        }

        data.sort((a, b) => Number(a.dates[0].date) - Number(b.dates[0].date))

        res.json({
            success: true,
            data: data
        })
    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: error.message,
        })
    }
}

const bookingAppointment = async (req, res) => {
    try {
        if (!req.body.customer)
            return res.json({ success: false, error_message: "missing customer" })

        if (!req.body.customer.first_name)
            return res.json({ success: false, error_message: "missing customer first_name" })

        if (!req.body.customer.last_name)
            return res.json({ success: false, error_message: "missing customer last_name" })

        if (!req.body.customer.email)
            return res.json({ success: false, error_message: "missing customer email" })

        if (!req.body.customer.phone)
            return res.json({ success: false, error_message: "missing customer phone" })

        if (req.body.customer.phone.length != 12)
            return res.json({ success: false, error_message: "phone number is invalid" })

        if (!req.body.date)
            return res.json({ success: false, error_message: "missing customer date" })

        if (!req.body.start_time)
            return res.json({ success: false, error_message: "missing customer start_time" })

        if (!req.body.fdb_id)
            return res.json({ success: false, error_message: "missing customer fdb_id" })


        const number_of_adults = Number(req.body.number_of_adults) || 1
        const number_of_minors = Number(req.body.number_of_minors) || 0
        let URL = "https://tools.usps.com/rcas-confirmation.htm?confirmationNumber=8UfGeNuaSbcG8fK5B801k3Q=="

        const data = {
            "customer": {
                "firstName": req.body.customer.first_name.toUpperCase(),
                "lastName": req.body.customer.last_name.toUpperCase(),
                "regId": ""
            },
            "customerEmailAddress": req.body.customer.email.toUpperCase(),
            "customerPhone": {
                "areaCode": req.body.customer.phone.slice(2, 5),
                "exchange": req.body.customer.phone.slice(5, 8),
                "line": req.body.customer.phone.slice(8),
                "textable": true
            },
            "date": req.body.date.toString(),
            "fdbId": req.body.fdb_id.toString(),
            "numberOfAdults": number_of_adults.toString(),
            "numberOfMinors": number_of_minors.toString(),
            "schedulingType": "PASSPORT",
            "serviceCenter": "Web Service Center",
            "time": req.body.start_time.toLowerCase(),
            "ipAddress": "**************",
            "passportPhotoIndicator": 0
        }

        const resp_proxy = await axios({
            method: 'post',
            url: 'http://************:3000',
            headers: {
                'Content-Type': 'application/json'
            },
            data: data
        })
        console.log(resp_proxy.data.data)
        if (resp_proxy.data.data.result.success === false) {
            return res.json({
                success: false,
                error_message: resp_proxy.data.data.result.resultCodeMessageList[0].message
            })
        }
        console.log(resp_proxy.data.data.scheduling.confirmationNumber)
        URL = "https://tools.usps.com/rcas-confirmation.htm?confirmationNumber=" + encodeURIComponent(resp_proxy.data.data.scheduling.confirmationNumber)
        res.json({
            success: true,
            data: {
                url: URL
            }
        })

        sqs.sendMessage({
            DelaySeconds: 0,
            MessageBody: JSON.stringify({
                "bcc": [ad_email.info],
                "parameters": {
                    "URL": URL,
                    "FullName": req.body.customer.first_name + " " + req.body.customer.last_name,
                },
                "template_name": "us_pp_appointment_booking_to_user",
                "to": req.body.customer.email
            }),
            QueueUrl: SQS_QUEUE_URL
        }, function (err, data) {
            if (err) {
                console.log("Error", err);
            } else {
                console.log("Success", data.MessageId);
            }
        });
    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: "fail",
        })
    }
}

module.exports = {
    getAppointmentTimes,
    bookingAppointment,
}