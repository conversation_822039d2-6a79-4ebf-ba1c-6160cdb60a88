const axios = require('axios');
const qs = require('qs');
const fs = require('fs');
const FormData = require('form-data');
var AWS = require('aws-sdk');
const { get_image_captcha_text } = require('../shared/captcha');
var s3 = new AWS.S3()

const EMAIL = '<EMAIL>'
const APP_PASSWORD = 'mnjwkhadbzynwzmq'

const getEmailInboxList = async (req, res) => {
    try {
        // Create Base64 encoded credentials
        const auth = Buffer.from(`${EMAIL}:${APP_PASSWORD}`).toString('base64');

        // Fetch emails using Gmail API
        const response = await axios.get('https://gmail.googleapis.com/gmail/v1/users/me/messages', {
            headers: {
                'Authorization': `Basic ${auth}`,
            },
            params: {
                maxResults: 20,  // Adjust number of results as needed
                orderBy: 'date',
                q: 'in:inbox'
            }
        });

        const messages = response.data.messages || [];
        const emailDetails = [];

        // Fetch details for each email
        for (const message of messages) {
            const messageDetails = await axios.get(
                `https://gmail.googleapis.com/gmail/v1/users/me/messages/${message.id}`,
                {
                    headers: {
                        'Authorization': `Basic ${auth}`,
                    }
                }
            );

            const headers = messageDetails.data.payload.headers;
            emailDetails.push({
                id: message.id,
                subject: headers.find(h => h.name === 'Subject')?.value || '(no subject)',
                from: headers.find(h => h.name === 'From')?.value || '',
                date: headers.find(h => h.name === 'Date')?.value || '',
                snippet: messageDetails.data.snippet
            });
        }

        res.json({
            data: emailDetails,
            success: true,
        });
    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: error.message,
        });
    }
}


module.exports = {
    getEmailInboxList,
}