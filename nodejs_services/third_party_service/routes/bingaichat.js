
const axios = require('axios');
const moment = require('moment');
const fs = require('fs');
const uuid = require('uuid');
const Jimp = require('jimp');
const FormData = require('form-data');
const WebSocketAsPromised = require("websocket-as-promised");

const COOKIE = 'MUID=38BB2249117D617D0062369E106F60A7; MUIDB=38BB2249117D617D0062369E106F60A7; SRCHD=AF=NOFORM; SRCHUID=V=2&GUID=348B48F4DD6F4C6F9FC3D76F36106B8E&dmnchg=1; _UR=QS=0&TQS=0; MicrosoftApplicationsTelemetryDeviceId=a7b0ad2c-1d5c-46ea-b869-308d214cceca; _HPVN=CS=eyJQbiI6eyJDbiI6MSwiU3QiOjAsIlFzIjowLCJQcm9kIjoiUCJ9LCJTYyI6eyJDbiI6MSwiU3QiOjAsIlFzIjowLCJQcm9kIjoiSCJ9LCJReiI6eyJDbiI6MSwiU3QiOjAsIlFzIjowLCJQcm9kIjoiVCJ9LCJBcCI6dHJ1ZSwiTXV0ZSI6dHJ1ZSwiTGFkIjoiMjAyNC0wOC0yM1QwMDowMDowMFoiLCJJb3RkIjowLCJHd2IiOjAsIlRucyI6MCwiRGZ0IjpudWxsLCJNdnMiOjAsIkZsdCI6MCwiSW1wIjo0LCJUb2JuIjowfQ==; ak_bmsc=85313CA95939F5A51B7DEC42EFE448C1~000000000000000000000000000000~YAAQxRFFdmZABcGRAQAANQCZxhnRN5+MHv/su7aW9Br09Y1c6ZtdKGmg+JUSSJj6nIKvMle3NwRI5KavT+HVq/a/vTYfhgfZMTSVQMPzXifmjriEOpquo7ov6B3atZWg2QYk+4XHthpxT/4T4QLnQizSLUzKoegTlK1V9TSakAI0lz6zk2pxsYHx5lXtgo2kmIjBV8RbXTo0pp1LEZWB8hvWyc2qdCUlUPWOovadiR/KBTKf6BE97GHNX+jwzvyZDwcxrCWJdwyRlwT10Nm99w+GLC+WjpmQS5xom4YmG1FWKfUJBPsdejAvXw6XtiDjmzFRW7/nYvXB3tGyCevX0FkDaUx6nFB9vcTOngArD5Af5E6LD9eKIwm9fwU1tZ+gxoD4cScisfDz; USRLOC=HS=1&ELOC=LAT=10.805426597595215|LON=106.66710662841797|N=Ward%202%2C%20Ho%20Chi%20Minh%20City|ELT=4|; _Rwho=u=d&ts=2024-09-06; MUIDB=38BB2249117D617D0062369E106F60A7; BFBUSR=CMUID=38BB2249117D617D0062369E106F60A7; dsc=order=News; ipv6=hit=1725622194440&t=4; CSRFCookie=7ed4303a-7bea-4b0e-bfd1-eafa9aa0b71a; SRCHUSR=DOB=20240823&T=1725618589000&TPC=1725613804000&POEX=W; _EDGE_S=SID=20D46631679A6D9E1DB972C366FC6C31; ANON=A=E4E29367EA050CAA72426250FFFFFFFF&E=1e36&W=1; NAP=V=1.9&E=1ddc&C=TJW-_CY7ucYBV84oaOn_INEEOBkE6v0UjS8plcvPBZOhDDU1Z7r52w&W=1; PPLState=1; KievRPSSecAuth=FACaBBRaTOJILtFsMkpLVWSG6AN6C/svRwNmAAAEgAAACMBh/yKlpXweWAQ38rK/RZCfSNgc9FlqPMDRPDO2hJ+aW4vVs8FZvOvuXcLyTIVCZ/Y2OummGZAwyFKyAPIxCnI7o3UDC8VmXRfKahKAebSMHua8vl3tuvGDZXz4+az4Wi8HAmWdOyqbWwe1j3JUA7PMribzYfainavySJZJMGi+yWYyWCIuTwM6P1vlVJ9HbH1pIWGB6qwkf2n0gkrOYV/EVQ2XS/8WmP5ffZknUetgDCAwWytinqNc6K89NwPwrDpIHlcB2b+LPPk9/mipv+1M04CIBgNGb4wlmxIGrMHRBaxk0c62VcesnWgSfj0xjbKjQAr1+pVKle37W4+QhmcXR3VG8xwGLMlMboqwgLNIf3yYt8JjpiA8miZRIXTC9EbKQ1fmJibIuMiWRnWjo8d/nLiq3S/0n0/BociigP8u7twayq65+zfqATBQNEs7/N6Zn8/YsXkusvWZiPOim1EMdQEk757MUnmeFwGktI36KapVzX3XbOZdycWE2Aelj6TJsf9cCWix+E5wPttSbqCAGzRRpci3JoXCrPkF/HEVVUa5uFBwx6UMpRcZhWzRrImp1tXJ8Po+vjs10o5SMox4Kn1BXU72iSU7UMC51TxMI0CLzfk+pRKXhsaXlxve507L8QmwAzp72Kn7N5xUD+wRwS9qhuis5rgcr10xssAdIFK9oG1tw7sEhr/6Qy2YJnzlpeWEmgTb0lqjGUiUP3DTGka843WEji2JHzmOoCukUljVAAYD5ZNYR8IGqBzbcw6QLLz9x9YLbS6BarUQKCeNpIGZmXkyIC6UMn0NbSUv6l87T/aB+4CdlOOdwTo6oA2nYgtmJlI/Oipko02l9F5MIxEEw8Cl3uwicj1aqHFiLnomQ8q07MfqtGrqx6m/6OwPkyImXpH6H0IHsFN5RZoFoiSj3BaFq9j4w3biFIj4/j/Tm6v4gU9rQtPYfLUI6E7AzQf/NycBKuXXh+abEeo3YeqW6oKYnSx0W0CnsybI4UuG8JPGFvbvZcL6yop1l1qmpgLs7ng1hZWPnW9CVi45vMget7p98g0Zgga/2HcFlfqRNIq8mH+a9srw2W8gnaPzHGL2JXZN/0MYRaWontqeNYHZMvvm7UTpROi1kUnovT9MhSSJkOYNJ/FCm3xB0RyAbf82CJg0fBcwjJnMXQvCWQGjeR2xnVe81OZDlzxJx/bbfDGCsl/HpuMTvWlBAU4Sls2N6UQJK19FfA3H4aiYGGHdwY6zHjes/PfCseLnRMXGTiipZ1A4eb6dgb4+uCkCEFxf+9KrCWJSQulecKriTH9CyG0gZXCHXxOO/iUObKJMCHwAaNIXQd6/M33U51U7O7M7zNs/60ClQc60/iqTC2BjzMId8TYugkyx9tWGzp6jwhADhYNKdRw69ap9NIxAwNBDLh3VQLF6SFkzEV52r28qKB+Zf/ICuNGbVajt3sTD57y/xZejxEG/mjY+1MB3Bk/ng0lXsatOTsHqMhnfEhQAJDXmi/kBb+eiUBQIb2FwoD2uAMs=; _U=1g6WVwn82lBLdLfscjn7lvzMwn9i7-fTC_f4-hwA4UAryqHqUPx4EgWv7PaGQSLoKp7qzZiHEyNGYrc3gW3YZ88rnQEkpeBeyAY8Y1zuPiKv4PLIbctdmIPfXAskxnmCwDbdc1uUy-tXkm_2mD88XS4h-Ag7e6j5p6gGvyKgH-PmrF1FGuSPr5l0AOYuGa3dVhJ3awccxIjNwrZtW47drzsBFX5042vMfR1dJMCY4xikE_Nxq6H6Vm8tHv_yzCv3l; WLS=C=b6c93762bcc3f4d7&N=%c4%90%c3%a0o+Tr%e1%bb%8dng; WLID=3ac1CRItl8RncOv9ElnoxpGiy6BicFnyGa1jZC18MdEADmWbosLvR7HbEIGVv4ZtNC/HDD44OMU3Nrz0SGoJw4q2apD/nijC7AN3YWiarXE=; _SS=SID=2373CD7931C6627D052CD98B30F86300&R=443&RB=443&GB=0&RG=0&RP=15; _RwBf=r=0&ilt=16&ihpd=0&ispd=2&rc=443&rb=443&gb=0&rg=0&pc=15&mtu=0&rbb=0.0&g=0&cid=&clo=0&v=11&l=2024-09-06T07:00:00.0000000Z&lft=0001-01-01T00:00:00.0000000&aof=0&ard=0001-01-01T00:00:00.0000000&rwdbt=0001-01-01T16:00:00.0000000-08:00&rwflt=0001-01-01T16:00:00.0000000-08:00&o=0&p=bingcopilotwaitlist&c=MY00IA&t=869&s=2023-03-23T03:18:14.4461565+00:00&ts=2024-09-06T10:52:09.5910714+00:00&rwred=0&wls=2&wlb=0&wle=0&ccp=2&cpt=0&lka=0&lkt=0&aad=0&TH=&mta=0&e=VUN3DISDTnJJpRboVNAX_0XjVq3V35mCNhmZ5qA3yvmX1uqXiSZO7huAky7rHaCNeTtvcCBGrWozLTYgxFUxSA&A=E4E29367EA050CAA72426250FFFFFFFF; bm_sv=60F7F8AA384500687520947084968FAC~YAAQ2RBFdpCHPMORAQAANoj2xhnluBoTM4CkNjt+ZilEB37ErjlY9vCRa5Ks0GGdZw+ce54O6UhXYfPoqecNl7J8IMVX5n716l1XYcvTmHYDIqgo7wepWSQjL9MC5AsVN/LcIzTMmIJgFHViM8AkkVqqLDcBANPaTGYgx6ucLa0xJIJXbihRtRWfeSWZKeNe4KX9eTlXSzefA7LI86GdrFf+nt8U+JvaePQiRC9vin5BqsyR+fCbsN2NH8ChOd8=~1; SRCHHPGUSR=SRCHLANG=en&DM=0&BRW=NOTP&BRH=M&CW=476&CH=795&SCW=476&SCH=224&DPR=2.0&UTC=420&CIBV=1.1808.0&PV=5.15.0&WTS=63861215389&IG=D830389E15BF46E79A57349A02A7BC1F&PRVCW=1669&PRVCH=795&HV=1725619930&THEME=0&WEBTHEME=0&EXLTT=2&CMUID=38BB2249117D617D0062369E106F60A7&EXLKNT=1&LSL=0&VSRO=1&BCML=1&BCTTSOS=110&AS=1&ADLT=OFF&NNT=1&HAP=0&CHTRSP=1'
const step1 = async () => {
    let config1 = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://www.bing.com/turing/conversation/create?bundleVersion=1.1808.0',
        headers: {
            'accept': 'application/json',
            'accept-language': 'en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7',
            'cookie': COOKIE,
            'priority': 'u=1, i',
            'referer': 'https://www.bing.com/chat',
            'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }
    }
    const resp1 = await axios(config1)
    console.log(resp1.data);
    console.log(resp1.headers);
    return { ...resp1.data, headers: resp1.headers };
}

const step2 = async (step1_resp, buff) => {
    const formData = new FormData();
    formData.append('knowledgeRequest', JSON.stringify({
        "imageInfo": {},
        "knowledgeRequest": {
            "invokedSkills": ["ImageById"],
            "subscriptionId": "Bing.Chat.Multimodal",
            "invokedSkillsRequestData": {
                "enableFaceBlur": true
            },
            "convoData": {
                "convoid": step1_resp.conversationId,
                "convotone": "Balanced"
            }
        }
    }));

    const buffBase64 = buff.toString('base64');
    formData.append('imageBase64', buffBase64);

    let config1 = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://www.bing.com/images/kblob',
        headers: {
            ...formData.getHeaders(),
            'cookie': COOKIE,
            'origin': 'https://www.bing.com',
            'priority': 'u=1, i',
            'referer': 'https://www.bing.com/chat',
            'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        },
        data: formData
    }
    const resp1 = await axios(config1)
    console.log(resp1.data);
    return resp1.data
}


const step3 = async (step1_resp, step2_resp) => {
    return new Promise((resolve, reject) => {
        try {
            const seperator = String.fromCharCode(30);
            // If secAccessToken is not set, use the old way to pass conversationSignature
            const url = `wss://sydney.bing.com/sydney/ChatHub?sec_access_token=${encodeURIComponent(
                step1_resp.headers["x-sydney-encryptedconversationsignature"],
            )}`;
            const wsp = new WebSocketAsPromised(url, {
                packMessage: (data) => {
                    return JSON.stringify(data) + seperator;
                },
                unpackMessage: (data) => {
                    return data
                        .toString()
                        .split(seperator)
                        .filter(Boolean)
                        .map((r) => JSON.parse(r));
                },
            });

            wsp.onOpen.addListener(() => {
                wsp.sendPacked({ protocol: "json", version: 1 });
            });

            let beginning = "";
            let body = "";
            let ending = "";
            wsp.onUnpackedMessage.addListener(async (events) => {
                try {
                    for (const event of events) {
                        // console.log(JSON.stringify(event));

                        if (JSON.stringify(event) === "{}") {
                            const rand_id = uuid.v4();
                            wsp.sendPacked({ type: 6 });
                            wsp.sendPacked({
                                "arguments": [
                                    {
                                        "source": "cib",
                                        "optionsSets": [
                                            "nlu_direct_response_filter",
                                            "deepleo",
                                            "disable_emoji_spoken_text",
                                            "responsible_ai_policy_235",
                                            "enablemm",
                                            "dv3sugg",
                                            "iyxapbing",
                                            "iycapbing",
                                            "galileo",
                                            "gptvppapisvc"
                                        ],
                                        "allowedMessageTypes": [
                                            "ActionRequest",
                                            "Chat",
                                            "ConfirmationCard",
                                            "Context",
                                            "InternalSearchQuery",
                                            "InternalSearchResult",
                                            "Disengaged",
                                            "InternalLoaderMessage",
                                            "Progress",
                                            "RenderCardRequest",
                                            "RenderContentRequest",
                                            "AdsQuery",
                                            "SemanticSerp",
                                            "GenerateContentQuery",
                                            "SearchQuery",
                                            "GeneratedCode",
                                            "InternalTasksMessage",
                                            "Disclaimer",
                                            "RecommendPlugin"
                                        ],
                                        "sliceIds": [
                                            "styleqnacon",
                                            "inputdestf",
                                            "815deepleov3s0",
                                            "722codegenv4",
                                            "0808gpt4om365",
                                            "0822fudpimps0",
                                            "0904gptvpp"
                                        ],
                                        "verbosity": "verbose",
                                        "scenario": "SERP",
                                        "plugins": [],
                                        "traceId": rand_id,
                                        "conversationHistoryOptionsSets": [
                                            "autosave",
                                            "savemem",
                                            "uprofupd",
                                            "uprofgen"
                                        ],
                                        "gptId": "copilot",
                                        "isStartOfSession": false,
                                        "requestId": rand_id,
                                        "message": {
                                            "locale": "en-US",
                                            "market": "en-US",
                                            "region": "WW",
                                            "location": "lat:47.639557;long:-122.128159;re=1000m;",
                                            "locationHints": [
                                                {
                                                    "SourceType": 1,
                                                    "RegionType": 2,
                                                    "Center": {
                                                        "Latitude": 10.77649974822998,
                                                        "Longitude": 106.70099639892578
                                                    },
                                                    "Radius": 24902,
                                                    "Name": "Ho Chi Minh City, Ho Chi Minh City",
                                                    "Accuracy": 24902,
                                                    "FDConfidence": 0.5,
                                                    "CountryName": "Viet Nam",
                                                    "CountryConfidence": 8,
                                                    "Admin1Name": "Ho Chi Minh City",
                                                    "PopulatedPlaceName": "Ho Chi Minh City",
                                                    "PopulatedPlaceConfidence": 5,
                                                    "PostCodeName": "700000",
                                                    "UtcOffset": 7,
                                                    "Dma": 0
                                                }
                                            ],
                                            "userIpAddress": "**************",
                                            "timestamp": moment().format('YYYY-MM-DDTHH:mm:ssZ'),
                                            "adaptiveCards": [],
                                            "author": "user",
                                            "inputMethod": "Keyboard",
                                            "text": "give me mrz_line_1, mrz_line_2 and issue_date (YYYY-MM-DD) only as json and dont tell anything else (no ref, no extra info",
                                            "imageUrl": "https://www.bing.com/images/blob?bcid=" + step2_resp.processedBlobId,
                                            "originalImageUrl": "https://www.bing.com/images/blob?bcid=" + step2_resp.blobId,
                                            "messageType": "Chat",
                                            "requestId": rand_id,
                                            "messageId": rand_id
                                        },
                                        "tone": "Balanced",
                                        "extraExtensionParameters": {
                                            "gpt-creator-persona": {
                                                "personaId": "copilot"
                                            }
                                        },
                                        "spokenTextMode": "None",
                                        "conversationId": step1_resp.conversationId,
                                        "participant": {
                                            "id": "985156767730869"
                                        }
                                    }
                                ],
                                "invocationId": "1",
                                "target": "chat",
                                "type": 4
                            });
                        } else if (event.type === 6) {
                            wsp.sendPacked({ type: 6 });
                        } else if (event.type === 3) {
                            wsp.removeAllListeners();
                            wsp.close();
                            resolve(body);
                        } else if (event.type === 2) {

                            wsp.removeAllListeners();
                            wsp.close();
                            resolve(body);
                        } else if (event.type === 1) {
                            // Content response
                            if (event.arguments[0].messages?.length > 0) {
                                const message = event.arguments[0].messages[0];
                                if (message.messageType === "InternalSearchQuery") {
                                    beginning += "> " + message.text + "\n";
                                } else {
                                    body = message.adaptiveCards[0]?.body[0]?.text;
                                    const moreLinks = message.adaptiveCards[0]?.body[1]?.text;
                                    if (moreLinks !== undefined) {
                                        ending = `> ${moreLinks}`;
                                    }
                                }
                                console.log('CONTENT: ', `${beginning}\n${body}\n${ending}`)

                            }
                        } else if (event.type === 7) {
                            wsp.removeAllListeners();
                            wsp.close();
                            reject(new Error(event.error));
                        } else {
                            console.warn("Unknown Copilot response:", event);
                        }
                    }
                } catch (error) {
                    reject(error);
                }
            });

            wsp.onError.addListener((event) => {
                wsp.removeAllListeners();
                wsp.close();
                reject(
                    new Error(
                        i18n.global.t("error.failedConnectUrl", {
                            url: event.target.url,
                        }),
                    ),
                );
            });

            wsp.onClose.addListener(() => {
                resolve('done');
            });

            wsp.open();
        } catch (error) {
            reject(error);
        }
    });
}

const getBingAIChat = async (req, res) => {
    try {
        const { url } = req.body;
        const buff = await axios.get(url, { responseType: 'arraybuffer' });

        const step1_resp = await step1();

        const image = await Jimp.read(buff.data);

        // image.resize(500, 300);

        const resizedBuffer = await image.getBufferAsync(Jimp.MIME_JPEG);

        const step2_resp = await step2(step1_resp, resizedBuffer);
        const step3_resp = await step3(step1_resp, step2_resp)
        console.log(step3_resp)

        const json = JSON.parse(step3_resp.match(/```json\s*([\s\S]*?)\s*```/)[1]);
        console.log(json);

        res.json({
            data: json,
            success: true,
        })
    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: error.message,
        })
    }
}


module.exports = {
    getBingAIChat,
}