const axiosS = require('axios');
const qs = require('qs');
const fs = require('fs');
const FormData = require('form-data');
var AWS = require('aws-sdk');
const { get_image_captcha_text } = require('../shared/captcha');
var s3 = new AWS.S3()
const https = require('https');
const axios = axiosS.create({
    httpsAgent: new https.Agent({
        rejectUnauthorized: false
    })
});


const solve_captcha = async () => {
    const resp = await axios({
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://api.evisa.gov.vn/authorization-service/captcha/generate',
        headers: {
            'Accept': '*/*',
            'Accept-Language': 'en',
            'Connection': 'keep-alive',
            'Origin': 'https://evisa.gov.vn',
            'Referer': 'https://evisa.gov.vn/',
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
        },
    })
    const base64Data = resp.data.data.image.replace(/^data:image\/\w+;base64,/, "");
    const buffer = Buffer.from(base64Data, 'base64');
    const captcha = await get_image_captcha_text(buffer)
    if (captcha?.length < 5) {
        return solve_captcha()
    }
    return {
        id: resp.data.data.id,
        captcha,
    }
}

const getVietnamEvisa = async (req, res) => {
    try {
        const { maSoHoSo, email, ngaySinh, callback } = req.body
        const step_1 = await axios({
            method: 'get',
            maxBodyLength: Infinity,
            url: 'https://evisa.xuatnhapcanh.gov.vn/web/guest/tra-cuu-thi-thuc',
            headers: {
                'Origin': 'https://evisa.xuatnhapcanh.gov.vn',
                'Referer': 'https://evisa.xuatnhapcanh.gov.vn/web/guest/tra-cuu-thi-thuc',
            },
        })
        const cookie = step_1.headers["set-cookie"][0].split(";")[0]
        console.log(cookie)
        const form_get_visa_url = step_1.data.match(/action="([^"]*)"/)?.[1]
        console.log(form_get_visa_url)

        L: for (let j = 0; j < 20; j++) {
            var captcha = ""
            C: for (let i = 0; i < 10; i++) {
                captcha = await solve_captcha(cookie)
                console.log(captcha)
                if (captcha.length === 4 && /^[a-z0-9]+$/.test(captcha)) {
                    break C;
                }
            }
            const step_2 = await axios({
                method: 'post',
                maxBodyLength: Infinity,
                url: 'https://evisa.xuatnhapcanh.gov.vn/web/guest/tra-cuu-thi-thuc?p_p_id=tracuuthongtinTTDT_WAR_eVisaportlet&p_p_lifecycle=2&p_p_state=normal&p_p_mode=view&p_p_resource_id=getInfoHSTTGuest&p_p_cacheability=cacheLevelPage&p_p_col_id=column-2&p_p_col_pos=1&p_p_col_count=2',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    'Cookie': cookie,
                    'Origin': 'https://evisa.xuatnhapcanh.gov.vn',
                    'Referer': 'https://evisa.xuatnhapcanh.gov.vn/web/guest/tra-cuu-thi-thuc',
                    'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36',
                    'X-Requested-With': 'XMLHttpRequest',
                },
                data: `_tracuuthongtinTTDT_WAR_eVisaportlet_maHS=${maSoHoSo}&_tracuuthongtinTTDT_WAR_eVisaportlet_email=${email}&_tracuuthongtinTTDT_WAR_eVisaportlet_ngaySinh=${ngaySinh}&_tracuuthongtinTTDT_WAR_eVisaportlet_captchaText=${captcha}`
            })

            if (step_2.data.captcha === "false") {
                continue L
            }

            if (step_2.data.trangThai === "EDITABLE") {
                // Chưa thanh toán
            }

            if (step_2.data.trangThai === "Granted visa") {
                const step_3 = await axios({
                    method: 'post',
                    maxBodyLength: Infinity,
                    url: form_get_visa_url,
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                        'Cookie': cookie,
                        'Origin': 'https://evisa.xuatnhapcanh.gov.vn',
                        'Referer': 'https://evisa.xuatnhapcanh.gov.vn/web/guest/tra-cuu-thi-thuc',
                        'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                    data: qs.stringify({
                        '_tracuuthongtinTTDT_WAR_eVisaportlet_type': 'exportHSTT-TraCuu',
                        '_tracuuthongtinTTDT_WAR_eVisaportlet_hsttId': step_2.data.hsttId,
                        '_tracuuthongtinTTDT_WAR_eVisaportlet_maSoHoSo': maSoHoSo,
                        '_tracuuthongtinTTDT_WAR_eVisaportlet_ngaySinh': ngaySinh,
                        '_tracuuthongtinTTDT_WAR_eVisaportlet_captchaText': captcha
                    }),
                    responseType: "arraybuffer"
                })

                const aws_s3 = JSON.parse(process.env.ad_s3);
                await s3.upload({
                    Bucket: aws_s3.ariadirect_prod_passport_images,
                    Key: `evisa/${maSoHoSo}.pdf`,
                    ACL: 'public-read',
                    Body: step_3.data,
                    ContentType: 'application/pdf'
                }).promise()

                const url = await s3.getSignedUrlPromise('getObject', {
                    Bucket: aws_s3.ariadirect_prod_passport_images,
                    Key: `evisa/${maSoHoSo}.pdf`
                });

                res.json({
                    data: url.split('?').shift(),
                    success: true,
                })
                return
            }
            break L
        }

        res.json({
            data: "",
            success: true,
        })
    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: error.message,
        })
    }
}

const getVietnamEvisaV2 = async (req, res) => {
    try {
        const { maSoHoSo, email, ngaySinh } = req.body

        for (let j = 0; j < 20; j++) {
            var captcha = await solve_captcha()
            console.log(captcha)
            const step_1 = await axios.post(`https://api.evisa.gov.vn/client-service/public/hstt/tra-cuu-chua-dang-nhap`, {
                "maHoSo": maSoHoSo,
                "email": email,
                "ngaySinh": ngaySinh,
                "loaiNgaySinh": "D",
                "captcha": captcha.captcha,
                "captcha-id": captcha.id
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'Origin': 'https://evisa.gov.vn',
                    'Referer': 'https://evisa.gov.vn/',
                    'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
                }
            })
            if (step_1.data.message === 'response.captcha_invalid') {
                continue
            }
            if (step_1.data?.data?.fileTt) {
                console.log(`https://api.evisa.gov.vn/client-service/public/generate-ev${step_1.data?.data?.fileTt}`)
                const file_buff = await axios.get(`https://api.evisa.gov.vn/client-service/public/generate-ev${step_1.data?.data?.fileTt}`, { responseType: 'arraybuffer' })
                const aws_s3 = JSON.parse(process.env.ad_s3);
                await s3.upload({
                    Bucket: aws_s3.ariadirect_prod_passport_images,
                    Key: `evisa/${maSoHoSo}.pdf`,
                    ACL: 'public-read',
                    Body: file_buff.data,
                    ContentType: 'application/pdf'
                }).promise()

                const url = await s3.getSignedUrlPromise('getObject', {
                    Bucket: aws_s3.ariadirect_prod_passport_images,
                    Key: `evisa/${maSoHoSo}.pdf`
                });

                res.json({
                    data: url.split('?').shift(),
                    success: true,
                })
                return
            }
            res.json({
                data: "",
                success: true,
            })
            return
        }

        res.json({
            data: "",
            success: true,
        })
    } catch (error) {
        console.log(error);
        res.json({
            success: false,
            message: error.message,
        })
    }
}

module.exports = {
    getVietnamEvisa,
    getVietnamEvisaV2,
}