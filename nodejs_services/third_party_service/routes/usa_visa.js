const puppeteer = require('puppeteer');
const qs = require('qs');
const FormData = require('form-data');
const axios = require('axios');

const solve_captcha = async (buff) => {

    var data = new FormData();
    data.append('file', buff, "file.png");

    const resp2 = await axios({
        method: 'post',
        url: 'https://api.ariadirectcorp.com/v1/mrz-parser/capcha-solver-by-binary',
        data: data,
        headers: {
            ...data.getHeaders()
        }
    })
    return resp2.data

}

const getUSAEvisa = async (req, res) => {
    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
        defaultViewport: null,
        // ignoreHTTPSErrors: true,
        // slowMo: 100,
        // devtools: true,
    });


    try {
        L: for (let j = 0; j < 20; j++) {
            try {
                const page = await browser.newPage();
                await page.goto('https://ceac.state.gov/CEACStatTracker/Status.aspx', {
                    waitUntil: 'networkidle2',
                });

                await page.waitForSelector('[name="ctl00$ContentPlaceHolder1$Visa_Application_Type"]', { timeout: 10000 });
                await page.select('[name="ctl00$ContentPlaceHolder1$Visa_Application_Type"]', 'NIV')
                await page.waitForNetworkIdle({ timeout: 60000 });

                var captcha = ""
                C: for (let i = 0; i < 20; i++) {
                    const captchaImg = await page.$('img.LBD_CaptchaImage')
                    const buff = await captchaImg.screenshot()
                    const resp = await solve_captcha(buff)
                    console.log(resp)
                    if (resp.success && resp.data.length >= 4 && /[a-zA-Z0-9]+$/.test(resp.data)) {
                        captcha = resp.data
                        break  C
                    } else {
                        continue L
                    }
                }
                await page.select('[name="ctl00$ContentPlaceHolder1$Location_Dropdown"]', "HAN")
                await page.type('[name="ctl00$ContentPlaceHolder1$Visa_Case_Number"]', "AA00D81SI7")
                await page.type('[name="ctl00$ContentPlaceHolder1$Passport_Number"]', "********")
                await page.type('[name="ctl00$ContentPlaceHolder1$Surname"]', "NGUYE")

                await page.type('[name="ctl00$ContentPlaceHolder1$Captcha"]', captcha)
                await page.click('#ctl00_ContentPlaceHolder1_imgFolder')
                await page.waitForSelector('#ctl00_ContentPlaceHolder1_ucApplicationStatusView_lblStatus', { timeout: 10000 })
                const status = await page.$eval('#ctl00_ContentPlaceHolder1_ucApplicationStatusView_lblStatus', element => element.textContent);
                console.log(status);
                if (!status)
                    continue L
                break L
            } catch (error) {
                console.log(error);
                continue L
            }
        }
        await browser.close()


        res.json({
            data: "",
            success: true,
        })
    } catch (error) {
        await browser.close()


        console.log(error);
        res.json({
            success: false,
            message: error.message,
        })
    }
}

module.exports = {
    getUSAEvisa,
}