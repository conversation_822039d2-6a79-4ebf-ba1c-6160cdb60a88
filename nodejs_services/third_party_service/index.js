const express = require('express')
require('express-group-routes');
const cors = require('cors');
const { versionHand<PERSON> } = require('./routes/version');
const { getAppointmentTimes, bookingAppointment } = require('./routes/appointment');
const { getVietnamEvisa, getVietnamEvisaV2 } = require('./routes/vnm_evisa');
const { getEmailInboxList } = require('./routes/email');

const PORT = process.env.PORT || 3001

var app = express()
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cors())


app.group("/v1/third-party-service", (router) => {
    router.get("/version", versionHandler)
    router.get("/status", versionHandler)
    router.post("/usps/get-appointment-times", getAppointmentTimes)
    router.post("/usps/book-appointments", bookingAppointment)
    router.post("/evisa/get-vnm-visa", getVietnamEvisaV2)
    router.get("/email/inbox-list", getEmailInboxList)
});


console.log("Listen " + PORT)
app.listen(PORT)