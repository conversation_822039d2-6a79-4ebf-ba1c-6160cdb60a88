data = {
    "id": 1,
    "package_id": 4,
    "state": "reviewed",
    "status": "in_progress",
    "surname": "hu",
    "given_name": "huajuan",
    "email": "<EMAIL>",
    "passport_info": {
        "id": 1,
        "passport_number": "E78852795",
        "surname": "<PERSON><PERSON><PERSON>",
        "given_name": "<PERSON><PERSON><PERSON>",
        "surname_in_native": "",
        "given_name_in_native": "",
        "birthday": "1984-10-15T00:00:00Z",
        "nationality": "CHN",
        "gender": "F",
        "expiration_date": "2026-04-10T00:00:00Z",
        "issue_date": "2019-11-02T00:00:00Z",
        "city_of_birth": "city of birth",
        "country_of_birth": "country_of_birth",
        "birth_nationality": "birth_nationality",
        "former_nationality": "former_nationality",
        "issuing_authority": "issuing_authority",
        "passport_photo": "ariadirect-prod-passport-images-us-west-2/1/2019112/4/1/inputs/zhang_passport_photo.jpg",
        "passport_images": [
            "ariadirect-prod-passport-images-us-west-2/1/2019112/4/1/inputs/zhang_passport.jpg"
        ]
    },
    "visa_info": {
        "id": 1,
        "visa_country": "",
        "entry_date": "2019-11-02T00:00:00Z",
        "exit_date": "2019-11-13T00:00:00Z",
        "visa_product": None,
        "visa_validity": "17",
        "visa_number_of_entries": "Multiple",
        "visa_purpose": "visa_purpose",
        "visa_processing_time": "visa_processing_time"
    },
    "travel_info": {
        "id": 1,
        "departureTimestamp": None,
        "departureAirport": "departureAirport",
        "departureAirline": "departureAirline",
        "departureFlight": "departureFlight",
        "arrivalTimestamp": None,
        "arrivalAirport": "arrivalAirport",
        "arrivalAirline": "arrivalAirline",
        "arrivalFlight": "arrivalFlight"
    },
    "review_json": {

    },
    "preview_json": {
        "application_id": "1",
        "application_state": "previewed",
        "package_id": "4",
        "passport": {
            "birthday": "1984-10-15",
            "country": "CHN",
            "expiration_date": "2026-04-10",
            "gender": "M",
            "given_name": "HAO",
            "nationality": "CHN",
            "passport_number": "E78852795",
            "surname": "ZHANG"
        }
    },
    "input_files": {

    },
    "output_files": {

    },
    "additional_info": {

    },
    "created_at": "2019-11-02T02:25:36.531366Z",
    "updated_at": "2019-11-02T05:39:03.569671Z"
}

import os
import shutil
import sys

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.vn_na1_writer import VNNA1Writer

form_template = 'vn_na1.pdf'
photo_file = 'sample_photo.png'
data_output_pdf = 'output.pdf'

pdf_writer = VNNA1Writer(form_template, photo=photo_file)

fp = pdf_writer.run(data)

# data_dict = pdf_writer.data_normalization(data)
# fp = pdf_writer.write_data(form_template, data_dict)

shutil.copyfile(fp.name, data_output_pdf)
