"""
Notes:
To view the PDF form properties: https://www.pdfescape.com/
Right click on the field : unlock. Right click again : get properties.
"""
import fitz
import json
import os
import pprint

from pdfrw import PdfDict, PdfReader, PdfWriter, PdfName, PdfObject

ANNOT_KEY = '/Annots'
ANNOT_FIELD_KEY = '/T'


FORM_FILE_NAME = 'cn_v2013.pdf'
MAP_FILE_NAME = "cn_v2013_map.json"
form_file = os.path.dirname(__file__) + '/../forms/' + FORM_FILE_NAME
map_file = os.path.dirname(__file__) + '/../forms/' + MAP_FILE_NAME

output_text_file = os.path.dirname(__file__) + '/output_text.pdf'
output_choice_file = os.path.dirname(__file__) + '/output_choice.pdf'
output_photo_file = os.path.dirname(__file__) + '/output_photo.pdf'


def run():
    with open(map_file) as fp:
        _maps = json.load(fp)
    text_map, choice_map, photo_map = _maps['text'], _maps['choice'], _maps['photo']

    run_text(text_map, form_file, output_text_file)
    run_choice(choice_map, output_text_file, output_choice_file)
    run_photo(photo_map, output_choice_file, output_photo_file)


def run_text(text_map, input_pdf, output_pdf):
    """
    :param text_map:
    :param input_pdf:
    :param output_pdf:
    :return:
    """
    doc = fitz.open(input_pdf)
    for each_block in list(text_map.values()):
        page_id, position, text = each_block
        _insert_text(doc, page_id, position, text)

    doc.save(output_pdf, clean=True)


def run_choice(choice_map, input_pdf, output_pdf):
    """
    :param choice_map:
    :param input_pdf:
    :param output_pdf:
    :return:
    """
    selected = [
        "1.4-1",
        "1.10-1",
        "2.1-2",
        "1.4-1",
        "3.1-1",
        "3.1-2",
        "3.2-1",
        "3.2-2",
        "3.3-1",
        "3.3-2",
        "3.4-1",
        "3.4-2",
        "3.5-1",
        "3.5-2"
    ]

    input_pdf_obj = PdfReader(input_pdf, decrypt=True, decompress=True)
    writer = PdfWriter(output_pdf)

    selected_annots = [choice_map.get(s) for s in selected]

    for pid, page in enumerate(input_pdf_obj.pages):
        for each_annot in selected_annots:
            page_id, rect, expected, _ = each_annot

            if page_id != pid:
                continue

            annotations = page[ANNOT_KEY]
            for annotation in annotations:
                if annotation['/Rect'] == rect:
                    annotation.update(PdfDict(V=PdfName(expected)))
                    annotation.update(PdfDict(NeedAppearances=PdfObject('true')))
                    annotation.update(PdfDict(Ff=1))

                    # annotation.update(PdfDict(AP=''))  # does not work
                    # annotation.update(PdfDict(AP=True))  # does not work
                    # annotation.update(PdfDict(AP=PdfObject('true')))  # # does not work

        writer.addpage(page)

    writer.write()


def run_photo(photo_map, input_pdf, output_pdf):
    """
    :param photo_map:
    :param input_pdf:
    :param output_pdf:
    :return:
    """
    PHOTO_FILE = os.path.dirname(__file__) + '/sample_photo.png'
    page_id, photo_postition = photo_map

    # define the position (upper-right corner)
    image_rectangle = fitz.Rect(photo_postition)
    # retrieve the page of the PDF
    file_handle = fitz.open(input_pdf)
    # Photo is in first page
    page = file_handle[page_id]
    page.insertImage(image_rectangle, filename=PHOTO_FILE)

    file_handle.save(output_pdf)
    return output_pdf


def _insert_text(doc, page_id, position, text):
    """
    :param doc:
    :param page_id:
    :param position:
    :param text:
    :return:
    """
    page = doc[page_id]
    where = fitz.Point(position)  # text starts here

    page.insertText(where,
                    text,
                    fontname="Times-Roman",  # arbitrary if fontfile given TimesNewRoman Times-Roman
                    fontfile=None,
                    fontsize=10,
                    rotate=0,        # rotate text
                    color=(0, 0, 0), # some color (black)
                    overlay=True     # text in foreground
                    )


def _guess_text_positions(doc):
    """
    May not need anymore
    :param doc:
    :return:
    """
    xy_dict = dict()
    for pid, each_page in enumerate(doc):

        tdict = each_page.getText("dict")

        for i, each in enumerate(tdict['blocks']):
            if not each.get('lines'): continue

            x_pos = round(each['bbox'][0] + 50, 2)
            y_pos = round((each['bbox'][1] + each['bbox'][3]) / 2, 2)

            each_attributes = [pid, [x_pos, y_pos], each['lines'][0]['spans'][0]['text'][:15]]
            xy_dict['{}_{}'.format(pid, i)] = each_attributes

    pprint.pprint(xy_dict)
    return xy_dict


def _get_choice_position():
    """
    May not need anymore
    :return:
    """
    input_pdf_obj = PdfReader(output_text_file, decrypt=True, decompress=True)

    for page in input_pdf_obj.pages:
        annotations = page[ANNOT_KEY]
        for annotation in annotations:
            if annotation['/Parent']:
                print(annotation.Rect, annotation.Parent.T)
            else:
                print(annotation.Rect, annotation.T)



run()

