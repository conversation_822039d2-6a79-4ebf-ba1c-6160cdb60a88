import argparse
import boto3
import botocore
import json
import logging
import os
import tempfile

from botocore.exceptions import Client<PERSON><PERSON>r
from retrying import retry

from cn_v2013_writer import CNV2013Writer
from vn_na1_writer import VNNA1Writer
from xrequests import XRequests


ad_aws = json.loads(os.environ.get('ad_aws', '{}'))
ad_sqs = json.loads(os.environ.get('ad_sqs', '{}'))

aws_region_name = ad_aws.get('region', 'us-west-2')
sqs_queue_url = '/'.join((ad_sqs.get('url_prefix', ''), ad_sqs.get('packer_sqs_name', '')))

session = boto3.Session(region_name=aws_region_name)
sqs_client = session.client("sqs")
s3_client = session.client("s3")

APPLICATION_STATE_READY = 'ready'
CACHED_FORMS_DIR = '/forms'

logger = logging.getLogger(__name__)


def run():
    """
    :return:
    """
    while True:
        messages = receive_sqs_messages()
        for msg in messages:
            logger.info('Got the SQS message %s', msg)
            package_id, application_id, form_template, application_data, form_download_s3_bucket, form_download_s3_key, \
            photo_download_s3_bucket, photo_download_s3_key, form_upload_s3_bucket, form_upload_s3_key, callback = \
                parse_message_body(msg.get('Body'))

            try:
                # 1. fetch form file from S3, if not cached
                form_template_path = '/'.join([CACHED_FORMS_DIR, form_template])
                if not os.path.exists(form_template_path):
                    logger.info('No local form file: %s', form_template_path)
                    logger.info('Download form template from %s/%s', form_download_s3_bucket, form_download_s3_key)
                    download_from_s3(form_download_s3_bucket, form_download_s3_key, form_template_path)

                # 2. process writer
                if photo_download_s3_bucket:
                    photo_file_fp = tempfile.NamedTemporaryFile()
                    logger.info('Download photo file from %s/%s', photo_download_s3_bucket, photo_download_s3_key)
                    download_from_s3(photo_download_s3_bucket, photo_download_s3_key, photo_file_fp.name)
                    pdf_fp = write_pdf(application_data, form_template_path, photo=photo_file_fp.name, mode=form_template)
                    photo_file_fp.close()
                else:
                    pdf_fp = write_pdf(application_data, form_template_path, photo=None, mode=form_template)

                # 3. Upload the passport data
                logger.info('Upload pdf %s to S3', pdf_fp.name)
                upload_to_s3(form_upload_s3_bucket, form_upload_s3_key, pdf_fp.name)
                pdf_fp.close()

                # 4. Update the application status
                result_data = {
                    'package_id': package_id,
                    'application_id': application_id,
                    'visa_product_id': application_data.get('visa_info', {}).get('visa_product'),
                    'application_state': APPLICATION_STATE_READY,
                    'form_upload_s3_bucket': form_upload_s3_bucket,
                    'form_upload_s3_key': form_upload_s3_key,
                    'form_template': form_template
                }
                update_packer_status(callback, result_data)

                # 5. delete the processed image message from queue
                logger.info('Received and deleted message: %s', msg)
                sqs_client.delete_message(
                    QueueUrl=sqs_queue_url,
                    ReceiptHandle=msg['ReceiptHandle']
                )
            except Exception as exc:
                logger.exception('FAILURE, PACKER, %s', exc)
                raise


def receive_sqs_messages():
    """
    :return:
    """
    # Receive message from SQS queue
    logger.debug("Listen to SQS: %s", sqs_queue_url)
    response = sqs_client.receive_message(
        QueueUrl=sqs_queue_url,
        AttributeNames=[
            'SentTimestamp'
        ],
        MaxNumberOfMessages=1,
        MessageAttributeNames=[
            'All'
        ],
        VisibilityTimeout=0,
        WaitTimeSeconds=20
    )
    # If there is message in queue
    if response.get('Messages'):
        return response['Messages']
    else:
        logger.debug("No message in queue")
        return []


def parse_message_body(body_str):
    """
    :param body_str:
    :return:
    """
    try:
        msg = json.loads(body_str)
    except Exception as exc:
        logger.exception('Fail to parse the message:\n %s \n %s', body_str, exc)
        raise

    package_id = msg.get('package_id')
    application_id = msg.get('application_id')
    form_template = msg.get('form_template')
    callback = msg.get('callback')
    application_data = msg.get('application_data')

    form_download_s3_bucket = msg.get('s3', {}).get('form_download_s3_bucket')
    form_download_s3_key = msg.get('s3', {}).get('form_download_s3_key')
    photo_download_s3_bucket = msg.get('s3', {}).get('photo_download_s3_bucket')
    photo_download_s3_key = msg.get('s3', {}).get('photo_download_s3_key')
    form_upload_s3_bucket = msg.get('s3', {}).get('form_upload_s3_bucket')
    form_upload_s3_key = msg.get('s3', {}).get('form_upload_s3_key')

    return package_id, application_id, form_template, application_data, form_download_s3_bucket, form_download_s3_key, \
           photo_download_s3_bucket, photo_download_s3_key, form_upload_s3_bucket, form_upload_s3_key, callback


@retry(wait_fixed=500, stop_max_attempt_number=2)  # Retry 2 times with 0.5 second
def download_from_s3(s3_bucket, s3_key, local_file):
    """
    :param s3_bucket:
    :param s3_key:
    :param local_file:
    :return:
    """
    try:
        s3_client.download_file(s3_bucket, s3_key, local_file)
    except botocore.exceptions.ClientError as exc:
        if exc.response['Error']['Code'] == "404":
            logger.error('The object does NOT exist: %s; %s', s3_bucket, s3_key)
    else:
        logger.info('Save the file: %s', local_file)
    return local_file


@retry(wait_fixed=500, stop_max_attempt_number=3)  # Retry 3 times with 0.5 second
def upload_to_s3(s3_bucket, s3_key, local_file):
    """
    :param s3_bucket:
    :param s3_key:
    :param local_file:
    :return:
    """
    try:
        s3_client.upload_file(local_file, s3_bucket, s3_key)
    except ClientError as exc:
        logger.error(exc)
        return False
    return True


@retry(wait_fixed=500, stop_max_attempt_number=3)  # Retry 3 times with 0.5 second
def update_packer_status(callback, data):
    """
    :param callback:
    :param data:
    :return:
    """
    logger.info('Update application state to %s', data)
    update_result = XRequests(callback)
    update_result.post(data=data)


##############################
#     Writer process         #
##############################
def write_pdf(data, form_template_path, photo=None, mode=None):
    """
    :param data:
    :param form_template_path:
    :param photo:
    :param mode:
    :return:
    """
    if mode.lower() == "cn_v2013.pdf":
        pdf_writer = CNV2013Writer(form_template_path, data, photo=None)
        return pdf_writer.run()
    elif mode.lower() == "vn_na1.pdf":
        pdf_writer = VNNA1Writer(form_template_path, data, photo=photo)
        return pdf_writer.run()
    else:
        logger.info("Not supported form: %s", mode)
        return


def main():
    parser = argparse.ArgumentParser(description='preview service parameters')
    args = parser.parse_args()

    logger.debug('Parsed arguments: %s', vars(args))
    run()


if __name__ == "__main__":
    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.INFO
    )
    # Really don't need to hear about connections being brought up again after server has closed it
    logging.getLogger('requests.packages.urllib3.connectionpool').setLevel(logging.WARNING)
    logging.getLogger('botocore.vendored.requests.packages.urllib3.connectionpool').setLevel(logging.WARNING)
    main()
