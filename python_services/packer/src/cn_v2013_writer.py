import fitz
import json
import logging
import os
import tempfile

from datetime import datetime
from pdfrw import PdfDict, PdfReader, PdfWriter, PdfName, PdfObject
from xrequests import XRequests


ANNOT_FIELD_KEY = '/T'
ANNOT_KEY = '/Annots'

DB_DATE_FORMAT = "%Y-%m-%dT%H:%M:%SZ"
FORM_DATE_FORMAT = "%Y-%m-%d"  # The date format required by cn_v2013 form

NA = 'N/A'
ONE_SPACE = ' '
FORM_MAP_FILE = "/forms/cn_v2013_map.json"

ad_packer_service = json.loads(os.environ.get('ad_packer_service', '{}'))
search_url = ad_packer_service.get('search_url')

logger = logging.getLogger(__name__)


class CNV2013Writer(object):

    def __init__(self, form_template, data, photo=None):
        """
        :param form_template:
        :param data:
        :param photo:
        """
        self.form_template = form_template
        self.data = data
        self.photo = photo

    def run(self):
        """
        :return:
        """
        with open(FORM_MAP_FILE) as fp:
            _maps = json.load(fp)

        text_map, choice_map, photo_map = _maps['text'], _maps['choice'], _maps['photo']
        choice_list, text_dict = self.normalize_data(self.data)

        output_text_fp = self.run_text(text_dict, text_map, self.form_template)
        output_choice_fp = self.run_choice(choice_list, choice_map, output_text_fp)
        output_photo_fp = self.run_photo(self.photo, photo_map, output_choice_fp)

        output_text_fp.close()
        if self.photo:
            output_choice_fp.close()

        return output_photo_fp

    def run_text(self, text_dict, text_map, input_pdf):
        """
        :param text_dict:
        :param text_map:
        :param input_pdf:
        :return:
        """
        output_text_fp = tempfile.NamedTemporaryFile()

        file_handler = fitz.open(input_pdf)
        for item, each_block in text_map.items():
            page_id, position, _ = each_block
            logger.info('Write the text: Page: %s;  Position: %s; Text: %s',
                        page_id, position, text_dict.get(item, ''))
            self.insert_by_position(file_handler[page_id], position, text_dict.get(item, ''))

        logger.info('Fill the text to the pdf form. Save it to %s', output_text_fp.name)
        file_handler.save(output_text_fp.name, clean=True)
        return output_text_fp

    @staticmethod
    def run_choice(choice_list, choice_map, input_pdf_fp):
        """
        :param choice_list:
        :param choice_map:
        :param input_pdf_fp:
        :return:
        """
        output_choice_fp = tempfile.NamedTemporaryFile()
        writer = PdfWriter(output_choice_fp.name)

        input_pdf_obj = PdfReader(input_pdf_fp.name, decrypt=True, decompress=True)

        selected_annots = [choice_map.get(c) for c in choice_list]

        for pid, page in enumerate(input_pdf_obj.pages):
            for page_id, rect, expected, _ in selected_annots:
                if page_id != pid:
                    continue
                annotations = page[ANNOT_KEY]
                for annotation in annotations:
                    if annotation['/Rect'] == rect:
                        annotation.update(PdfDict(V=PdfName(expected)))
                        annotation.update(PdfDict(NeedAppearances=PdfObject('true')))
                        annotation.update(PdfDict(Ff=1))

            writer.addpage(page)

        logger.info('Fill the choice to the pdf form. Save it to %s', output_choice_fp.name)
        writer.write()

        return output_choice_fp

    @staticmethod
    def run_photo(photo_file, photo_position, input_pdf_fp):
        """
        :param photo_file:
        :param photo_position:
        :param input_pdf_fp:
        :return:
        """
        if photo_file is None:
            return input_pdf_fp

        output_photo_fp = tempfile.NamedTemporaryFile()

        file_handle = fitz.open(input_pdf_fp.name)

        page_id, position = photo_position
        page = file_handle[page_id]
        # define the position (upper-right corner)
        image_rectangle = fitz.Rect(position)

        page.insertImage(image_rectangle, filename=photo_file)

        logger.info('Fill photo to the pdf form. Save it to %s', output_photo_fp.name)
        file_handle.save(output_photo_fp.name)
        return output_photo_fp

    def normalize_data(self, data):
        """
        Covert the SQS payload data to the data dict for the form
        :param data:
        :return:
        """
        # Original info
        pp_info = data.get('passport_info', {})
        visa_info = data.get('visa_info', {})
        travel_info = data.get('travel_info', {})
        additional_info = data.get('additional_info', {})

        # Output data
        text_dict = dict()
        choice_list = []

        logger.info('Normalize the data')

        text_dict['1.1-1'] = pp_info.get('surname')
        text_dict['1.1-2'] = pp_info.get('middle_name', NA)
        text_dict['1.1-3'] = pp_info.get('given_name')

        # TODO: set it to NA since, FE not allow input Chinese in 2.1
        # if pp_info.get('surname_in_native') and pp_info.get('given_name_in_native'):
        #     text_dict['1.2'] = pp_info.get('given_name_in_native') + ONE_SPACE + pp_info.get('surname_in_native')
        # else:
        #     text_dict['1.2'] = NA
        text_dict['1.2'] = NA

        text_dict['1.3'] = NA

        if pp_info.get('gender').lower() == 'm':
            choice_list.append('1.4-1')
        else:
            choice_list.append('1.4-2')

        text_dict['1.5'] = self.get_form_date_format(pp_info.get('birthday'))
        text_dict['1.6'] = pp_info.get('nationality')
        text_dict['1.7'] = pp_info.get('former_nationality', NA)
        text_dict['1.8'] = pp_info.get('city_of_birth', '') + ONE_SPACE + pp_info.get('country_of_birth', '')
        text_dict['1.9'] = additional_info.get('personal_info', {}).get('citizenship_number', NA)

        choice_list.append('1.10-3')  # Note: Passport type "ordinary" by default

        text_dict['1.11'] = pp_info.get('passport_number')
        text_dict['1.12'] = self.get_form_date_format(pp_info.get('issue_date', ''))
        text_dict['1.13'] = pp_info.get('issuing_authority', NA)  # Note: place_of_issue
        text_dict['1.14'] = self.get_form_date_format(pp_info.get('expiration_date', ''))

        _occupation = additional_info.get('personal_info', {}).get('occupation', '')
        occupation_mapping = {
                                 "business_person":  "1.15-1",
                                 "company_employee": "1.15-2",
                                 "student":          "1.15-5",
                                 "retired":          "1.15-9"
                             }
        selected = occupation_mapping.get(_occupation)
        if selected:
            choice_list.append(selected)
        else:
            choice_list.append("1.15-16")  # Note: other, by default

        choice_list.append("1.16-2")  # TODO: Education, college, by defualt

        _company_info = additional_info.get('personal_info', {}).get('company_info', {})
        text_dict['1.17-1'] = _company_info.get('company_name', NA)
        text_dict['1.17-2'] = _company_info.get('phone', NA)
        text_dict['1.17-3'] = self.get_address_format(_company_info.get('address'),
                                                      _company_info.get('city'),
                                                      _company_info.get('state'),
                                                      self.get_country_name(_company_info.get('country'))
                                                      )
        text_dict['1.17-4'] = _company_info.get('zip_code', NA)

        _home_address = additional_info.get('personal_info', {}).get('home_address', {})
        text_dict['1.18'] = self.get_address_format(_home_address.get('address'),
                                                    _home_address.get('city'),
                                                    _home_address.get('state'),
                                                    self.get_country_name(_home_address.get('country'))
                                                    )
        text_dict['1.19'] = _home_address.get('zip_code', NA)
        text_dict['1.20'] = _home_address.get('phone', NA)
        text_dict['1.21'] = _home_address.get('email', NA)

        _marital_status = additional_info.get('personal_info', {}).get('marital_status', '')
        if _marital_status.lower() == 'married':
            choice_list.append('1.22-1')
        elif _marital_status.lower() == 'single':
            choice_list.append('1.22-2')
        else:
            choice_list.append('1.22-3')

        _family_members = list(additional_info.get('family_info', {}).values())
        if len(_family_members) == 0:
            text_dict['1.23-11'] = NA
            text_dict['1.23-12'] = NA
            text_dict['1.23-13'] = NA
            text_dict['1.23-14'] = NA
        if len(_family_members) >= 1:
            text_dict['1.23-11'] = _family_members[0].get('firstName') \
                                   + ONE_SPACE \
                                   + _family_members[0].get('lastName')
            text_dict['1.23-12'] = self.get_country_name(_family_members[0].get('nationality'))
            text_dict['1.23-13'] = _family_members[0].get('occupation')
            text_dict['1.23-14'] = _family_members[0].get('relationship')
        if len(_family_members) >= 2:
            text_dict['1.23-21'] = _family_members[1].get('firstName') \
                                   + ONE_SPACE \
                                   + _family_members[0].get('lastName')
            text_dict['1.23-22'] = self.get_country_name(_family_members[1].get('nationality'))
            text_dict['1.23-23'] = _family_members[1].get('occupation')
            text_dict['1.23-24'] = _family_members[1].get('relationship')
        if len(_family_members) >= 3:
            text_dict['1.23-31'] = _family_members[2].get('firstName') \
                                   + ONE_SPACE \
                                   + _family_members[0].get('lastName')
            text_dict['1.23-32'] = self.get_country_name(_family_members[2].get('nationality'))
            text_dict['1.23-33'] = _family_members[2].get('occupation')
            text_dict['1.23-34'] = _family_members[2].get('relationship')
        if len(_family_members) >= 4:
            text_dict['1.23-41'] = _family_members[3].get('firstName') \
                                   + ONE_SPACE \
                                   + _family_members[0].get('lastName')
            text_dict['1.23-42'] = self.get_country_name(_family_members[3].get('nationality'))
            text_dict['1.23-43'] = _family_members[3].get('occupation')
            text_dict['1.23-44'] = _family_members[3].get('relationship')

        text_dict['1.24'] = NA  # TODO emergency
        text_dict['1.25'] = self.get_country_name((data.get('region_of_residence', NA)))

        _visa_purpose = visa_info.get('visa_purpose')

        if _visa_purpose.lower() == 'business':
            choice_list.append('2.1-4')
        else:
            choice_list.append('2.1-2')  # Note: tourist, default

        _validity = visa_info.get('visa_validity')
        _entries = visa_info.get('visa_number_of_entries')

        if _validity == '3M' and _entries == 'single_entry':
            choice_list.append('2.2-1')
        elif _validity == '3M' and _entries == 'double_entries':
            choice_list.append('2.2-2')
        elif _validity == '6M' and _entries == 'multiple_entries':
            choice_list.append('2.2-3')
        elif _validity == '1y' and _entries == 'multiple_entries':
            choice_list.append('2.2-4')
        else:
            choice_list.append('2.2-5')
            if _validity == '10y':
                _entries_str = ONE_SPACE.join(_entries.split('_')).title()
                text_dict['2.2'] = '10 years, ' + _entries_str
            else:
                text_dict['2.2'] = _validity + ONE_SPACE + _entries

        choice_list.append('2.3-2')  # Note: express service, no, default

        text_dict['2.4'] = self.get_form_date_format(visa_info.get('entry_date'))\
            if visa_info.get('entry_date') else NA

        text_dict['2.5'] = '15'  # TODO: stay duration 15 days, default

        text_dict['2.6-1-1'] = NA  # TODO
        text_dict['2.6-1-2'] = NA  # TODO

        text_dict['2.7'] = 'Self'  # Note: default

        text_dict['2.8-1'] = NA  # TODO default
        text_dict['2.8-2'] = NA  # TODO default
        text_dict['2.8-3'] = NA  # TODO default
        text_dict['2.8-4'] = NA  # TODO default

        _other_information = additional_info.get('other_information', {})

        _had_visa = _other_information.get('cn_2_9')
        if _had_visa is None:
            text_dict['2.9'] = NA
        elif _had_visa is False:
            text_dict['2.9'] = 'No'
        elif _had_visa is True:
            text_dict['2.9'] = 'Yes'
        else:
            text_dict['2.9'] = _had_visa

        text_dict['2.10'] = _other_information.get('cn_2_10', NA)

        if _other_information.get('cn_3_1') is True:
            choice_list.append('3.1-1')
        else:
            choice_list.append('3.1-2')
        if _other_information.get('cn_3_2') is True:
            choice_list.append('3.2-1')
        else:
            choice_list.append('3.2-2')
        if _other_information.get('cn_3_3') is True:
            choice_list.append('3.3-1')
        else:
            choice_list.append('3.3-2')
        if _other_information.get('cn_3_4') is True:
            choice_list.append('3.4-1')
        else:
            choice_list.append('3.4-2')
        if _other_information.get('cn_3_5') is True:
            choice_list.append('3.5-1')
        else:
            choice_list.append('3.5-2')

        text_dict['3.8-1-1'] = NA  # TODO: travel together
        text_dict['3.8-1-2'] = NA  # TODO: travel together
        text_dict['3.8-1-3'] = NA  # TODO: travel together

        text_dict['4.3-2'] = datetime.strftime(datetime.today(), FORM_DATE_FORMAT)  # Note Today

        logger.info('The text dict: %s', text_dict)
        logger.info('The selected choice: %s', choice_list)
        return choice_list, text_dict

    @staticmethod
    def get_form_date_format(date_string):
        """
        :param date_string:
        :return:
        """
        if not date_string:
            return ''
        else:
            return datetime.strftime(datetime.strptime(date_string, DB_DATE_FORMAT), FORM_DATE_FORMAT)

    @staticmethod
    def get_address_format(address, city, state, country):
        """
        :param address:
        :param city:
        :param state:
        :param country:
        :return:
        """
        JOINT = ', '
        address_str = ''
        if address:
            address_str = address
        if city:
            address_str = address_str + JOINT + city if address_str else city
        if state:
            address_str = address_str + JOINT + state if address_str else state
        if country:
            address_str = address_str + JOINT + country if address_str else country
        return address_str if address_str else NA


    @staticmethod
    def insert_by_position(page, position, text, fontsize=10):
        """
        :param page:
        :param position:
        :param text:
        :param fontsize:
        :return:
        """
        where = fitz.Point(position)     # text starts here

        page.insertText(where,
                        text,
                        fontname="Times-Roman",  # arbitrary if font file given
                        fontfile=None,
                        fontsize=fontsize,
                        rotate=0,         # rotate text
                        color=(0, 0, 0),  # same color (black)
                        overlay=True      # text in foreground
                        )

    @staticmethod
    def get_country_name(country_alpha3):
        """
        :param country_alpha3:
        :return:
        """
        logger.info('Get country name for %s', country_alpha3)
        if not country_alpha3:
            return country_alpha3

        data = XRequests(search_url).get(
            value_only=True,
            path='v1/search/country',
            params={"iso_alpha3": country_alpha3
                    },
        )
        if data.get('data'):
            country_name = data.get('data')[0].get('name')
            logger.info('The country name for %s is %s', country_alpha3, country_name)
            return country_name
        else:
            logger.info('No country name found for %s', country_alpha3)
            return country_alpha3
