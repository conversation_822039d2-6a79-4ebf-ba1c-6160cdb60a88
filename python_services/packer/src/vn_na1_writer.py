"""
Notes:
To view the PDF form properties: https://www.pdfescape.com/
Right click on the field : unlock. Right click again : get properties.
"""
import fitz
import logging
import random
import tempfile

from datetime import datetime
from dateutil.relativedelta import relativedelta
from pdfrw import PdfDict, PdfReader, PdfWriter

ANNOT_KEY = '/Annots'
ANNOT_FIELD_KEY = '/T'
ANNOT_VAL_KEY = '/V'
ANNOT_RECT_KEY = '/Rect'
SUBTYPE_KEY = '/Subtype'
WIDGET_SUBTYPE_KEY = '/Widget'

DATEFORMAT = "%Y-%m-%dT%H:%M:%SZ"

RANDOM_VN_HOTELS = [
    'InterContinental Saigon, Corner Hai Ba Trung, Ho Chi Minh City, Vietnam',
    'Holiday Inn & Suites Saigon Airport, 18E Cong Hoa Street, Ho Chi Minh City, Vietnam',
    'Crowne Plaza West Hanoi, 36 Le Duc Tho Street, Hanoi, 10000 Vietnam',
    'InterContinental Hanoi Westlake, 05 Tu Hoa, Hanoi, 10000 Vietnam',
    'New World Saigon Hotel, 76 Le Lai Street, District 1, Ho Chi Minh City'
]

# vn_na1.pdf item position
#"x":76,"y":22,"width":78,"height":94
PHOTO_POSITION = (76, 22, 154, 116)

GENDER_MALE_POSITION = (217.5, 169)
GENDER_FEMALE_POSITION = (268, 169)
SINGLE_ENTRY_POSITION = (318, 323.5)
MULTIPLE_ENTRY_POSITION = (399.5, 323.5)

FIRST_PAGE_ENTRY_DATE_POSITION = (320, 635)
FIRST_PAGE_ENTRY_MONTH_POSITION = (340, 635)
FIRST_PAGE_ENTRY_YEAR_POSITION = (365, 635)

SECOND_PAGE_ENTRY_DATE_POSITION = (185, 350)
SECOND_PAGE_ENTRY_MONTH_POSITION = (210.5, 350)
SECOND_PAGE_ENTRY_YEAR_POSITION = (232.5, 350)

VN_NA1_FORM_KEYS = [
    'passportInfo_fullName',                # Id 1
    'passportInfo_birthday_date',           # Id 3
    'passportInfo_birthday_month',          # Id 3
    'passportInfo_birthday_year',           # Id 3
    'passportInfo_countryOfBirth_name',     # Id 4
    'passportInfo_birthNationality_name',   # Id 5
    'passportInfo_issuingAuthority_name',   # Id 12
    'passportInfo_passportNumber',          # Id 12
    'passportInfo_expiredDate_date',        # Id 12
    'passportInfo_expiredDate_month',       # Id 12
    'passportInfo_expiredDate_year',        # Id 12
    'passportInfo_nationality_name',        # Id 6
    'occupation',                           # Id 8
    'visaInfo_purpose_name',                # Id 15
    'hotel',                                # Id 16
    'personalInfo_homeAddress_fullAddress', # Id 10
    'email',                                # Id 10
    'lengthOfStay',                         # Id 14
    'visaInfo_entryDate_date',              # Id 14, 19  Shared same property?
    'visaInfo_entryDate_month',             # Id 14, 19  Shared same property?
    'visaInfo_entryDate_year',              # Id 14, 19  Shared same property?
    'visaInfo_expiredDate_date',            # Id 19
    'visaInfo_expiredDate_month',           # Id 19
    'visaInfo_expiredDate_year',            # Id 19
    'untitled3',                            # Id 10 Permanent residential address
    'untitled23',                           # Id 12 passport type
    'untitled52'                            # Id 20 other request
    # Id 7 Religion
    # Id 9 Employer and business address
    # ID 11 Family members
    # ID 13 Date of the previous entry into VietNam
    # Id 17 Hosting organisation/individual in Viet Nam
    # Id 18 Accompanying child(ren) under 14 years old included in your passport (if any)
]

logger = logging.getLogger(__name__)


class VNNA1Writer(object):

    def __init__(self, pdf_template, data, photo=None):
        """
        :param pdf_template:
        :param photo:
        """
        self.photo = photo
        self.data = data
        self.pdf_template = pdf_template

    def run(self, photo=None):
        """
        :param photo:
        :return:
        """
        if photo is None:
            photo = self.photo

        data_dict = self.data_normalization(self.data)
        data_output_pdf_fp = self.write_data(self.pdf_template, data_dict)

        if photo:
            photo_output_pdf_fp = self.insert_photo(photo, data_output_pdf_fp.name)
            data_output_pdf_fp.close()
            return photo_output_pdf_fp
        else:
            return data_output_pdf_fp

    # """
    # # Example data from the SQS
    # {
    #     "id": 1,
    #     "package_id": 4,
    #     "state": "reviewed",
    #     "status": "in_progress",
    #     "surname": "hu",
    #     "given_name": "huajuan",
    #     "email": "<EMAIL>",
    #     "passport_info": {
    #         "id": 1,
    #         "passport_number": "E78852795",
    #         "surname": "ZHANG",
    #         "given_name": "HAO",
    #         "surname_in_native": "",
    #         "given_name_in_native": "",
    #         "birthday": "1984-10-15T00:00:00Z",
    #         "nationality": "CHN",
    #         "gender": "M",
    #         "expiration_date": "2026-10-13T00:00:00Z",
    #         "issue_date": "2019-11-02T00:00:00Z",
    #         "city_of_birth": "",
    #         "country_of_birth": "",
    #         "birth_nationality": "",
    #         "former_nationality": "",
    #         "issuing_authority": "",
    #         "passport_photo": "ariadirect-prod-passport-images-us-west-2/1/2019112/4/1/inputs/zhang_passport_photo.jpg",
    #         "passport_images": [
    #             "ariadirect-prod-passport-images-us-west-2/1/2019112/4/1/inputs/zhang_passport.jpg"
    #         ]
    #     },
    #     "visa_info": {
    #         "id": 1,
    #         "visa_country": "",
    #         "entry_date": "2019-11-02T00:00:00Z",
    #         "exit_date": "2019-11-02T00:00:00Z",
    #         "visa_product": null,
    #         "visa_validity": "5d",
    #         "visa_number_of_entries": "",
    #         "visa_purpose": "",
    #         "visa_processing_time": ""
    #     },
    #     "travel_info": {
    #         "id": 1,
    #         "departureTimestamp": null,
    #         "departureAirport": "",
    #         "departureAirline": "",
    #         "departureFlight": "",
    #         "arrivalTimestamp": null,
    #         "arrivalAirport": "",
    #         "arrivalAirline": "",
    #         "arrivalFlight": ""
    #     },
    #     "review_json": {
    #
    #     },
    #     "preview_json": {
    #         "application_id": "1",
    #         "application_state": "previewed",
    #         "package_id": "4",
    #         "passport": {
    #             "birthday": "1984-10-15",
    #             "country": "CHN",
    #             "expiration_date": "2026-04-10",
    #             "gender": "M",
    #             "given_name": "HAO",
    #             "nationality": "CHN",
    #             "passport_number": "E78852795",
    #             "surname": "ZHANG"
    #         }
    #     },
    #     "input_files": {
    #
    #     },
    #     "output_files": {
    #
    #     },
    #     "additional_info": {
    #
    #     },
    #     "created_at": "2019-11-02T02:25:36.531366Z",
    #     "updated_at": "2019-11-02T05:39:03.569671Z"
    # }
    # """

    def data_normalization(self, data):
        """
        :param data:
        :return:
        """
        logger.info('Normalize the data')
        data_dict = dict()

        # passport info
        pp_info = data.get('passport_info', {})
        data_dict['passportInfo_fullName'] = ' '.join((pp_info.get('given_name', ''), pp_info.get('surname', '')))

        if pp_info.get('birthday'):
            dt = datetime.strptime(pp_info.get('birthday'), DATEFORMAT)
            data_dict['passportInfo_birthday_date'] = dt.day
            data_dict['passportInfo_birthday_month'] = dt.month
            data_dict['passportInfo_birthday_year'] = dt.year

        data_dict['passportInfo_countryOfBirth_name'] = pp_info.get('country_of_birth', '')
        data_dict['passportInfo_birthNationality_name'] = pp_info.get('birth_nationality', '')
        data_dict['passportInfo_issuingAuthority_name'] = pp_info.get('issuing_authority', '')
        data_dict['passportInfo_passportNumber'] = pp_info.get('passport_number', '')

        if pp_info.get('expiration_date'):
            dt = datetime.strptime(pp_info.get('expiration_date'), DATEFORMAT)
            data_dict['passportInfo_expiredDate_date'] = dt.day
            data_dict['passportInfo_expiredDate_month'] = dt.month
            data_dict['passportInfo_expiredDate_year'] = dt.year

        data_dict['passportInfo_nationality_name'] = pp_info.get('nationality', '')

        # visa info
        visa_info = data.get('visa_info', {})
        data_dict['visaInfo_purpose_name'] = visa_info.get('visa_purpose', '')

        # Calculate the LengthOfStay, the unit is days
        if visa_info.get('exit_date') and visa_info.get('entry_date'):
            exit_dt = datetime.strptime(visa_info.get('exit_date'), DATEFORMAT)
            entry_dt = datetime.strptime(visa_info.get('entry_date'), DATEFORMAT)
            data_dict['lengthOfStay'] = str((exit_dt - entry_dt).days)
        else:
            vv = visa_info.get('visa_validity', '')
            if len(vv) > 1:
                num, unit = vv[:-1], vv[-1]
                if unit.lower() == 'y':
                    num = str(int(num) * 365)
                elif unit.lower() == 'm':
                    num = str(int(num) * 30)
                elif unit.lower() == 'd':
                    num = str(num)
                else:
                    num = vv  # Keep the input, whatever
                data_dict['lengthOfStay'] = num
            else:
                data_dict['lengthOfStay'] = vv  # Keep the input, whatever

        # Calculate the visa expire date
        _time_delta = None
        if visa_info.get('visa_validity'):
            vv = visa_info.get('visa_validity', '')
            if len(vv) > 1:
                num, unit = vv[:-1], vv[-1]
                if unit.lower() == 'y':
                    _time_delta = relativedelta(years=+int(num))
                elif unit.lower() == 'm':
                    _time_delta = relativedelta(months=+int(num))
                elif unit.lower() == 'd':
                    _time_delta = relativedelta(days=+int(num))

        if _time_delta:
            if visa_info.get('entry_date'):
                dt = datetime.strptime(visa_info.get('entry_date'), DATEFORMAT) + _time_delta
                data_dict['visaInfo_expiredDate_date'] = dt.day
                data_dict['visaInfo_expiredDate_month'] = dt.month
                data_dict['visaInfo_expiredDate_year'] = dt.year
        else:
            if visa_info.get('exit_date'):
                dt = datetime.strptime(visa_info.get('exit_date'), DATEFORMAT)
                data_dict['visaInfo_expiredDate_date'] = dt.day
                data_dict['visaInfo_expiredDate_month'] = dt.month
                data_dict['visaInfo_expiredDate_year'] = dt.year

        # Random data
        data_dict['occupation'] = 'Business Person'  # TODO
        data_dict['hotel'] = random.choice(RANDOM_VN_HOTELS)

        # Others
        data_dict['email'] = data['email']
        data_dict['untitled23'] = 'Ordinary'  # Passport type
        data_dict['untitled52'] = 'N/A'  # other requests

        # Note: PLEASE READ
        # Gender/VISA_NUMBER_OF_ENTRIES are selection, Can NOT work with pdfrw Widget update
        # these will use insertText method
        # Ref: https://github.com/pmaupin/pdfrw/issues/84#issuecomment-*********
        data_dict['gender'] = pp_info.get('gender')
        data_dict['visa_number_of_entries'] = visa_info.get('visa_number_of_entries', '')

        # Note: this one will use insertText method
        if visa_info.get('entry_date'):
            dt = datetime.strptime(visa_info.get('entry_date'), DATEFORMAT)
            data_dict['visaInfo_entryDate_date'] = dt.day
            data_dict['visaInfo_entryDate_month'] = dt.month
            data_dict['visaInfo_entryDate_year'] = dt.year

        # TODO
        # data_dict['personalInfo_homeAddress_fullAddress'] = 'home address'
        # data_dict['untitled3'] = 'Permanent residential address',

        return data_dict

    def write_data(self, pdf_template, data_dict):
        """
        :param pdf_template:
        :param data_dict:
        :return:
        """
        widget_output_pdf_fp = tempfile.NamedTemporaryFile()

        logger.info('The following data is going to write to PDF %s', data_dict)

        # Step 1: write for widget
        pdf_template_obj = PdfReader(pdf_template)
        writer = PdfWriter(widget_output_pdf_fp.name)

        for page in pdf_template_obj.pages:
            annotations = page[ANNOT_KEY]
            for annotation in annotations:
                if annotation[SUBTYPE_KEY] != WIDGET_SUBTYPE_KEY or not annotation[ANNOT_FIELD_KEY]:
                    continue

                key = annotation[ANNOT_FIELD_KEY].strip('()')
                if key in VN_NA1_FORM_KEYS:
                    # Write the value to the form
                    annotation.update(
                        PdfDict(V='{}'.format(data_dict.get(key, '')))
                    )
                    annotation.update(
                        PdfDict(AP=True)
                    )
            writer.addpage(page)

        # Write to a file
        logger.info('Write to PDF form with widget')
        writer.write()

        # Step 2: write with position
        position_output_pdf_fp = self.write_pdf_with_position(widget_output_pdf_fp, data_dict)

        widget_output_pdf_fp.close()
        return position_output_pdf_fp

    def write_pdf_with_position(self, input_pdf_fp, data_dict):
        """
        :param input_pdf_fp:
        :param data_dict:
        :return:
        """
        position_output_pdf_fp = tempfile.NamedTemporaryFile()

        def _insert(page, position, text):
            where = fitz.Point(position)  # text starts here

            page.insertText(where,
                            text,
                            fontname="Times-Roman",  # arbitrary if fontfile given
                            fontfile=None,
                            fontsize=10,
                            rotate=0,  # rotate text
                            color=(0, 0, 0),  # some color (black)
                            overlay=True)  # text in foreground
            return page

        # retrieve the first/second page of the PDF
        file_handle = fitz.open(input_pdf_fp.name)
        first_page = file_handle[0]
        second_page = file_handle[1]

        gender = data_dict.get('gender', '').lower()
        if gender:
            if gender.startswith('m'):
                position = GENDER_MALE_POSITION
            elif gender.startswith('f'):
                position = GENDER_FEMALE_POSITION
            _insert(first_page, position, 'X')

        entries = data_dict.get('visa_number_of_entries', '').lower()
        if entries:
            if entries.startswith('single'):
                position = SINGLE_ENTRY_POSITION
            elif entries.startswith('multi'):
                position = MULTIPLE_ENTRY_POSITION
            _insert(second_page, position, 'X')

        if all([data_dict.get('visaInfo_entryDate_date'),
                data_dict.get('visaInfo_entryDate_month'),
                data_dict.get('visaInfo_entryDate_year')]
               ):

            # Page 1, Id 14
            _insert(first_page, FIRST_PAGE_ENTRY_DATE_POSITION, str(data_dict.get('visaInfo_entryDate_date')))
            _insert(first_page, FIRST_PAGE_ENTRY_MONTH_POSITION, str(data_dict.get('visaInfo_entryDate_month')))
            _insert(first_page, FIRST_PAGE_ENTRY_YEAR_POSITION, str(data_dict.get('visaInfo_entryDate_year')))

            # Page 2, Id 19
            _insert(second_page, SECOND_PAGE_ENTRY_DATE_POSITION, str(data_dict.get('visaInfo_entryDate_date')))
            _insert(second_page, SECOND_PAGE_ENTRY_MONTH_POSITION, str(data_dict.get('visaInfo_entryDate_month')))
            _insert(second_page, SECOND_PAGE_ENTRY_YEAR_POSITION, str(data_dict.get('visaInfo_entryDate_year')))

        logger.info('Write to PDF form with position')
        file_handle.save(position_output_pdf_fp.name)
        return position_output_pdf_fp

    def insert_photo(self, photo_file, input_pdf):
        """
        :param self:
        :param photo_file:
        :param input_pdf:
        :return:
        """
        photo_output_pdf_fp = tempfile.NamedTemporaryFile()

        # define the position (upper-right corner)
        image_rectangle = fitz.Rect(PHOTO_POSITION)
        # retrieve the first page of the PDF
        file_handle = fitz.open(input_pdf)
        # Photo is in first page
        first_page = file_handle[0]
        first_page.insertImage(image_rectangle, filename=photo_file)

        logger.info('Insert photo to the application form')
        file_handle.save(photo_output_pdf_fp.name)
        return photo_output_pdf_fp
