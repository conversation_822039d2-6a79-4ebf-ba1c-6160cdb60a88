{"choice": {"1.4-1": [0, ["120.311", "533.143", "132.446", "545.383"], "Choice1", "Male"], "1.4-2": [0, ["177.731", "533.781", "189.971", "546.021"], "Choice2", "Female"], "1.10-1": [0, ["181.527", "434.335", "193.767", "446.575"], "Choice1", "Passport type diplomatic"], "1.10-2": [0, ["298.036", "434.772", "310.276", "447.012"], "Choice2", "Passport type service official"], "1.10-3": [0, ["181.527", "421.245", "193.767", "433.485"], "Choice3", "Passport type ordinary"], "1.10-4": [0, ["298.036", "421.245", "310.276", "433.485"], "Choice4", "Passport type other"], "1.15-1": [0, ["134.798", "351.213", "147.038", "363.453"], "Yes", "Current occupation business_person"], "1.15-2": [0, ["134.798", "329.566", "147.038", "341.806"], "Yes", "Current occupation company_employee"], "1.15-3": [0, ["134.798", "307.92", "147.038", "320.16"], "Yes", "Current occupation entertainer"], "1.15-4": [0, ["134.798", "286.273", "147.038", "298.513"], "Yes", "Current occupation industrial worker"], "1.15-5": [0, ["134.798", "264.627", "147.038", "276.867"], "Yes", "Current occupation student"], "1.15-6": [0, ["134.798", "242.98", "147.038", "255.22"], "Yes", "Current occupation crew"], "1.15-7": [0, ["134.798", "221.334", "147.038", "233.574"], "Yes", "Current occupation self employed"], "1.15-8": [0, ["134.798", "199.687", "147.038", "211.927"], "Yes", "Current occupation unemployed"], "1.15-9": [0, ["134.798", "178.041", "147.038", "190.281"], "Yes", "Current occupation retired"], "1.15-10": [0, ["315.81", "349.927", "328.05", "362.167"], "Yes", "Current occupation former parliament"], "1.15-11": [0, ["315.81", "307.837", "328.05", "320.077"], "Yes", "Current occupation former official"], "1.15-12": [0, ["315.81", "264.617", "328.05", "276.857"], "Yes", "Current occupation military"], "1.15-13": [0, ["315.81", "221.91", "328.05", "234.15"], "Yes", "Current occupation ngo"], "1.15-14": [0, ["315.81", "199.295", "328.05", "211.535"], "Yes", "Current occupation religious"], "1.15-15": [0, ["315.81", "177.048", "328.05", "189.288"], "Yes", "Current occupation staff of media"], "1.15-16": [0, ["134.798", "156.394", "147.038", "168.634"], "Yes", "Current occupation other"], "1.16-1": [0, ["134.541", "132.809", "146.781", "145.049"], "Choice1", "Education postgraduate"], "1.16-2": [0, ["316.49", "132.809", "328.73", "145.049"], "Choice2", "Education college"], "1.16-3": [0, ["134.541", "117.536", "146.781", "129.776"], "Choice3", "Education other"], "1.22-1": [1, ["173.672", "726.262", "185.912", "738.502"], "Choice1", "Marital status married"], "1.22-2": [1, ["252.654", "726.698", "264.894", "738.938"], "Choice2", "Marital status single"], "1.22-3": [1, ["323.345", "726.262", "335.585", "738.502"], "Choice3", "Marital status other"], "2.1-1": [1, ["120.684", "478.276", "132.924", "490.516"], "Choice1", "Major purpose of your visit official visit"], "2.1-2": [1, ["120.684", "456.779", "132.924", "469.019"], "Choice2", "Major purpose of your visit tourism"], "2.1-3": [1, ["120.684", "435.282", "132.924", "447.522"], "Choice3", "Major purpose of your visit non-business visit"], "2.1-4": [1, ["120.684", "413.785", "132.924", "426.025"], "Choice4", "Major purpose of your visit business"], "2.1-5": [1, ["120.684", "392.288", "132.924", "404.528"], "Choice5", "Major purpose of your visit talent"], "2.1-6": [1, ["120.684", "370.792", "132.924", "383.032"], "Choice6", "Major purpose of your visit crew"], "2.1-7": [1, ["120.684", "346.544", "132.924", "358.784"], "Choice7", "Major purpose of your visit short term visit chinese"], "2.1-8": [1, ["120.684", "294.793", "132.924", "307.033"], "Choice8", "Major purpose of your visit short term visit work"], "2.1-9": [1, ["120.684", "251.365", "132.924", "263.605"], "Choice9", "Major purpose of your visit short term study"], "2.1-10": [1, ["120.684", "228.203", "132.924", "240.443"], "Choice10", "Major purpose of your visit journalist"], "2.1-11": [1, ["339.271", "479.276", "351.511", "491.516"], "Choice11", "Major purpose of your visit resident diplomat"], "2.1-12": [1, ["339.271", "434.848", "351.511", "447.088"], "Choice12", "Major purpose of your visit permanent resident"], "2.1-13": [1, ["339.271", "413.134", "351.511", "425.374"], "Choice13", "Major purpose of your visit work"], "2.1-14": [1, ["339.271", "391.782", "351.511", "404.022"], "Choice14", "Major purpose of your visit child in foster care"], "2.1-15": [1, ["339.271", "370.068", "351.511", "382.308"], "Choice15", "Major purpose of your visit transit"], "2.1-16": [1, ["339.271", "351.611", "351.511", "363.851"], "Choice16", "Major purpose of your visit family reunion"], "2.1-17": [1, ["339.271", "294.069", "351.511", "306.309"], "Choice19", "Major purpose of your visit visit foreigner"], "2.1-18": [1, ["339.271", "251.365", "351.511", "263.605"], "Choice20", "Major purpose of your visit long term study"], "2.1-19": [1, ["339.271", "228.927", "351.511", "241.167"], "Choice21", "Major purpose of your resident journalist"], "2.1-20": [1, ["120.874", "204.68", "133.114", "216.92"], "Choice22", "Major purpose of your visit other"], "2.2-1": [1, ["120.684", "183.991", "132.924", "196.231"], "Choice1", "Intended number of entries one entry"], "2.2-2": [1, ["120.684", "165.663", "132.924", "177.903"], "Choice2", "Intended number of entries two entries"], "2.2-3": [1, ["120.684", "147.336", "132.924", "159.576"], "Choice3", "Intended number of entries multiple entries 6 months"], "2.2-4": [1, ["120.684", "129.009", "132.924", "141.249"], "Choice4", "Intended number of entries multiple entries 1 year"], "2.2-5": [1, ["120.684", "110.682", "132.924", "122.922"], "Choice5", "Intended number of entries other"], "2.3-1": [1, ["398.399", "85.2453", "410.639", "97.4853"], "Choice1", "Express service yes"], "2.3-2": [1, ["455.126", "84.3726", "467.366", "96.6126"], "Choice2", "Express service no"], "3.1-1": [2, ["456.254", "293.827", "468.493", "306.067"], "Choice1", "Overstayed yes"], "3.1-2": [2, ["503.69", "293.827", "515.93", "306.067"], "Choice2", "Overstayed no"], "3.2-1": [2, ["456.254", "265.438", "468.493", "277.678"], "Choice1", "Refused yes"], "3.2-2": [2, ["503.69", "265.438", "515.93", "277.678"], "Choice2", "Refused no"], "3.3-1": [2, ["456.254", "239.219", "468.493", "251.459"], "Choice1", "Criminal yes"], "3.3-2": [2, ["503.69", "239.219", "515.93", "251.459"], "Choice2", "Criminal no"], "3.4-1": [2, ["456.254", "204.329", "468.493", "216.569"], "Choice1", "Disease yes"], "3.4-2": [2, ["503.69", "204.329", "515.93", "216.569"], "Choice2", "Disease no"], "3.5-1": [2, ["456.254", "167.51", "468.493", "179.75"], "Choice1", "Infectious diseases yes"], "3.5-2": [2, ["503.69", "167.51", "515.93", "179.75"], "Choice2", "Infectious diseases no"]}, "photo": [0, [422.08, 186, 576, 314.72]], "text": {"1.1-1": [0, [230, 202], "Last name"], "1.1-2": [0, [230, 229], "Middle name"], "1.1-3": [0, [230, 255], "First name"], "1.2": [0, [120, 276], "Name in Chinese"], "1.3": [0, [350, 276], "Other name(s)"], "1.5": [0, [350, 305], "DOB"], "1.6": [0, [150, 330], "Current nationality(ies)"], "1.7": [0, [420, 330], "Former nationality(ies)"], "1.8": [0, [230, 353], "Place of birth(city, province/ state, country)"], "1.9": [0, [180, 380], "Local ID/ Citizenship number"], "1.11": [0, [120, 435], "Passport number"], "1.12": [0, [400, 435], "Date of issue"], "1.13": [0, [120, 460], "Place of issue"], "1.14": [0, [400, 460], "Date of expiry"], "1.17-1": [0, [180, 742], "Employer/School/Name"], "1.17-2": [0, [460, 742], "Employer/School/Phone number"], "1.17-3": [0, [180, 772], "Employer/School/Address"], "1.17-4": [0, [460, 772], "Employer/School/Zip Code"], "1.18": [1, [130, 55], "Home address"], "1.19": [1, [410, 55], "Zip Code"], "1.20": [1, [150, 85], "Home/mobile phone number"], "1.21": [1, [410, 85], "E-mail address"], "1.23-11": [1, [125, 160], "Major family/Name"], "1.23-12": [1, [260, 160], "Nationality"], "1.23-13": [1, [350, 160], "Occupation"], "1.23-14": [1, [458, 160], "Relationship"], "1.23-21": [1, [125, 185], "Major family/Name"], "1.23-22": [1, [260, 185], "Nationality"], "1.23-23": [1, [350, 185], "Occupation"], "1.23-24": [1, [458, 185], "Relationship"], "1.23-31": [1, [125, 210], "Major family/Name"], "1.23-32": [1, [260, 210], "Nationality"], "1.23-33": [1, [350, 210], "Occupation"], "1.23-34": [1, [458, 210], "Relationship"], "1.23-41": [1, [125, 235], "Major family/Name"], "1.23-42": [1, [260, 235], "Nationality"], "1.23-43": [1, [350, 235], "Occupation"], "1.23-44": [1, [458, 235], "Relationship"], "1.24-1": [1, [150, 260], "Emergency Contact/Name"], "1.24-2": [1, [430, 260], "Emergency Contact/Mobile"], "1.24-3": [1, [250, 285], "Emergency Contact/Relationship"], "1.25": [1, [350, 310], "Country or territory where the applicant is located when applying for this visa"], "2.2": [1, [330, 728], "Intended number of entries: other"], "2.4": [1, [345, 780], "Expected date of your first entry into China on this trip"], "2.5": [2, [380, 53], "Longest intended stay in China among all entries"], "2.6-1-1": [2, [125, 115], "Itinerary in China/Date"], "2.6-1-2": [2, [275, 115], "Itinerary in China/Address"], "2.6-2-1": [2, [125, 145], "Itinerary in China/Date"], "2.6-2-2": [2, [275, 145], "Itinerary in China/Address"], "2.6-3-1": [2, [125, 175], "Itinerary in China/Date"], "2.6-3-2": [2, [275, 175], "Itinerary in China/Address"], "2.6-4-1": [2, [125, 205], "Itinerary in China/Date"], "2.6-4-2": [2, [275, 205], "Itinerary in China/Address"], "2.6-5-1": [2, [125, 235], "Itinerary in China/Date"], "2.6-5-2": [2, [275, 235], "Itinerary in China/Address"], "2.7": [2, [350, 265], "Who will pay for"], "2.8-1": [2, [200, 295], "Inviter in China/Name"], "2.8-2": [2, [200, 327], "Itinerary in China/Address"], "2.8-3": [2, [200, 358], "Itinerary in China/Phone"], "2.8-4": [2, [260, 392], "Itinerary in China/Relationship"], "2.9": [2, [345, 425], "Have you ever been\ngranted a Chinese visa\nIf applicable, please specify\nthe date and place"], "2.10": [2, [345, 470], "Other countries or territories\nyou visited in the last 12 months"], "3.6": [2, [50, 720], "If you select Yes to any questions\nfrom 3.1 to 3.5, please \ngive details below"], "3.7": [3, [50, 85], "If you have more information about \nyour visa application other than the above to declare, \nplease give details below or type on a separate paper."], "3.8-1-1": [3, [170, 285], "Travel Partner/Name"], "3.8-1-2": [3, [170, 312], "Travel Partner/Sex"], "3.8-1-3": [3, [170, 340], "Travel Partner/DOB"], "3.8-2-1": [3, [300, 285], "Travel Partner/Name"], "3.8-2-2": [3, [300, 312], "Travel Partner/Sex"], "3.8-2-3": [3, [300, 340], "Travel Partner/DOB"], "3.8-3-1": [3, [438, 285], "Travel Partner/Name"], "3.8-3-2": [3, [438, 312], "Travel Partner/Sex"], "3.8-3-3": [3, [438, 340], "Travel Partner/DOB"], "4.3-2": [3, [410, 530], "Date"]}}