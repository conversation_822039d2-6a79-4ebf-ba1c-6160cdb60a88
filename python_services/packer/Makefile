IMAGE=853431205376.dkr.ecr.us-west-2.amazonaws.com/packer
COMMIT=$(shell git rev-parse --short HEAD)

all: build push

build:
	docker build -t $(IMAGE):$(COMMIT) .

push:
	@eval `aws ecr get-login --region us-west-2 --no-include-email`
	docker push $(IMAGE):$(COMMIT)

# Display --help to check that dependencies are satisfied and free of syntax errors.
help: build
	docker run -it --rm $(IMAGE):$(COMMIT) python /src/packer.py --help
