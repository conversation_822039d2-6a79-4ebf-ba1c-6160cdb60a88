import argparse
import boto3
import botocore
import json
import logging
import os
import tempfile
import time

from botocore.exceptions import ClientError
from datetime import datetime
from passporteye import read_mrz
from retrying import retry

from xrequests import XRequests


logger = logging.getLogger(__name__)

ad_aws = json.loads(os.environ.get('ad_aws', '{}'))
ad_sqs = json.loads(os.environ.get('ad_sqs', '{}'))

aws_region_name = ad_aws.get('region', 'us-west-2')
sqs_queue_url = '/'.join((ad_sqs.get('url_prefix', ''), ad_sqs.get('preview_sqs_name', '')))

session = boto3.Session(region_name=aws_region_name)
sqs_client = session.client("sqs")
s3_client = session.resource('s3')

APPLICATION_STATE_PREVIEWED = 'previewed'


def run(cleanup=True):
    while True:
        messages = receive_sqs_messages()
        for msg in messages:
            package_id, application_id, s3_bucket, s3_key, callback =\
                parse_metadata(msg['MessageAttributes'])

            # 1. download passport image from s3
            pp_image_file = download_passport_image(s3_bucket, s3_key)

            # 2. process passport image
            start_time = time.time()
            passport_data = passporteye_process(pp_image_file)
            elapse = time.time() - start_time
            logger.info('PassportEye process time: %s seconds for image %s', elapse, s3_key)

            # 3. Submit the passport data
            pp_preview_result = {
                'package_id':        package_id,
                'application_id':    application_id,
                'application_state': APPLICATION_STATE_PREVIEWED,
                'passport':          passport_data,
            }
            update_preview_result(callback, pp_preview_result)

            # 4. delete the processed image message from queue
            logger.info('Received and deleted message: %s', msg)
            sqs_client.delete_message(
                QueueUrl=sqs_queue_url,
                ReceiptHandle=msg['ReceiptHandle']
            )

            if cleanup:
                os.remove(pp_image_file)


def receive_sqs_messages():
    """
    :return:
    """
    # Receive message from SQS queue
    logger.debug("Listen to SQS: %s", sqs_queue_url)
    response = sqs_client.receive_message(
        QueueUrl=sqs_queue_url,
        AttributeNames=[
            'SentTimestamp'
        ],
        MaxNumberOfMessages=1,
        MessageAttributeNames=[
            'All'
        ],
        VisibilityTimeout=0,
        WaitTimeSeconds=20
    )
    # If there is message in queue
    if response.get('Messages'):
        return response['Messages']
    else:
        logger.debug("No message in queue")
        return []


def parse_metadata(metadata):
    """
    :param metadata:
    :return:
    """
    package_id = metadata.get('package_id').get('StringValue')
    application_id = metadata.get('application_id').get('StringValue')
    s3_bucket = metadata.get('s3_bucket').get('StringValue')
    s3_key = metadata.get('s3_key').get('StringValue')
    callback = metadata.get('callback').get('StringValue')
    return package_id, application_id, s3_bucket, s3_key, callback


@retry(wait_fixed=500, stop_max_attempt_number=2)  # Retry 2 times with 0.5 second
def download_passport_image(s3_bucket, s3_key):
    """
    :param s3_bucket:
    :param s3_key:
    :return:
    """
    logger.info('Download passport image from %s/%s', s3_bucket, s3_key)
    with tempfile.NamedTemporaryFile(delete=False) as fp:
        try:
            s3_client.Bucket(s3_bucket).download_file(s3_key, fp.name)
        except botocore.exceptions.ClientError as exc:
            if exc.response['Error']['Code'] == "404":
                logger.error('The object does NOT exist: %s; %s', s3_bucket, s3_key)
        else:
            logger.info('Save the passport image to file: %s', fp.name)
    return fp.name


@retry(wait_fixed=500, stop_max_attempt_number=3)  # Retry 3 times with 0.5 second
def update_preview_result(callback, pp_preview_result):
    """
    :param callback:
    :param pp_preview_result:
    :return:
    """
    logger.info('Update the passport preview result: %s', pp_preview_result)
    update_result = XRequests(callback)
    update_result.post(data=pp_preview_result)

##############################
#    PassportEys process     #
##############################

# >>> dir(mrz)
# ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__',
#  '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__',
#  '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__',
#  '__weakref__', '_check_date', '_guess_type', '_parse', '_parse_mrv', '_parse_td1', '_parse_td2', '_parse_td3',
#  'aux', 'check_composite', 'check_date_of_birth', 'check_expiration_date', 'check_number', 'check_personal_number',
#  'country', 'date_of_birth', 'expiration_date', 'from_ocr', 'mrz_type', 'names', 'nationality', 'number',
#  'personal_number', 'sex', 'surname', 'to_dict', 'type', 'valid', 'valid_check_digits', 'valid_composite',
#  'valid_date_of_birth', 'valid_expiration_date', 'valid_line_lengths', 'valid_misc', 'valid_number',
#  'valid_personal_number', 'valid_score']

# >>> mrz.to_dict()
# OrderedDict([('mrz_type', 'TD3'), ('valid_score', 40), ('type', 'PO'), ('country', 'CHN'),
# ('number', '624733780'), ('date_of_birth', '340625'), ('expiration_date', '171023'), ('nationality', 'CHN'),
# ('sex', 'M'), ('names', 'X CCCCCCCCECCCCCCCCCCCCCCCCC'), ('surname', 'FUC ERIC'),
# ('personal_number', '*********<<<<<'), ('check_number', '6'), ('check_date_of_birth', '6'),
# ('check_expiration_date', '6'), ('check_composite', '<'), ('check_personal_number', '<'),
# ('valid_number', True), ('valid_date_of_birth', True), ('valid_expiration_date', False),
# ('valid_composite', False), ('valid_personal_number', False), ('method', 'direct')])

def passporteye_process(image_file):
    """
    :param image_file:
    :return:
    """

    def ymd_converter(ymd):
        """
        :param ymd:
        :return:
        """
        ymd_datefmt = '%y%m%d'
        passport_datefmt = '%Y-%m-%d'

        ret = ''
        try:
            ret = datetime.strftime(datetime.strptime(ymd, ymd_datefmt), passport_datefmt)
        except Exception as exc:
            logger.error('Unexpected date format: %s', ymd)
            logger.error(exc)
        return ret

    logger.info('Run preview process in PassportEye model')

    mrz = read_mrz(image_file)
    if mrz:
        logger.info('Finish process the passport images')
        passport_data = {'country':              mrz.country,
                         'passport_number':      mrz.number,
                         'surname':              mrz.surname,
                         "given_name":           mrz.names,
                         'birthday':             ymd_converter(mrz.date_of_birth),
                         'nationality':          mrz.nationality,
                         'gender':               mrz.sex,
                         'expiration_date':      ymd_converter(mrz.expiration_date)
                         # TODO Unknown: Only update the known values
                         # 'issue_date':           '',
                         # 'city_of_birth':        '',
                         # 'issuing_authority':    '',
                         # 'surname_in_native':    '',
                         # 'given_name_in_native': '',
                         # 'country_of_birth':     '',
                         # 'birth_nationality':    '',
                         # 'former_nationality':   '',
                         }
    else:
        logger.error('Can not process the passport images')
        passport_data = {}
    return passport_data


def main():
    parser = argparse.ArgumentParser(description='preview service parameters')
    parser.add_argument('--cleanup', action='store_true', help='clean the temp file')
    args = parser.parse_args()

    logger.debug('Parsed arguments: %s', vars(args))
    run(cleanup=args.cleanup)


if __name__ == "__main__":
    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.INFO
    )
    # Really don't need to hear about connections being brought up again after server has closed it
    logging.getLogger('requests.packages.urllib3.connectionpool').setLevel(logging.WARNING)
    logging.getLogger('botocore.vendored.requests.packages.urllib3.connectionpool').setLevel(logging.WARNING)
    main()
