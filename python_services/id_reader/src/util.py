from typing import Text
import requests
import json
import re

def image_base_64_to_text(result, document):
    url = "https://vision.googleapis.com/v1/images:annotate?key=AIzaSyB26MFJHe7SMNyRBBUoKRgx1uo8onuQ9TU"
    # bytes to string
    payload = {
        "requests": []
    }

    labels = []
    for label, text in result.items():
        payload["requests"].append({
            "features": [
                {
                "maxResults": 10,
                "type": "TEXT_DETECTION"
                }
            ],
            "image": {
                "content": text.decode('utf-8')
            }
        })
        labels.append(label)

    headers = {
        'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
  
    # for each item in response
    i = 0
    test = {}
    for item in response.json()['responses']:
        if item and item["fullTextAnnotation"] is not None:
            test[labels[i]] = item["fullTextAnnotation"]["text"]
            if document == 'vnm_regular_visa':
                result[labels[i]] = parse_vnm_regular_visa_info(labels[i], item["fullTextAnnotation"]["text"])
            else:
                result[labels[i]] = parse_driver_license_info(labels[i], item["fullTextAnnotation"]["text"])
        else:
            result[labels[i]] = ''
        i += 1
        
    if document == 'vnm_regular_visa':
        result['validity'] = '' # y,m,d
        if 'entry_date' in result.keys() and 'visa_expired' in result.keys():
            parse_entry_date = re.findall(r'([0-9]{4})-([0-9]{2})-([0-9]{2})T', result['entry_date'])
            parse_visa_expired = re.findall(r'([0-9]{4})-([0-9]{2})-([0-9]{2})T', result['visa_expired'])
            if len(parse_entry_date) > 0 and len(parse_visa_expired) > 0:
                if len(parse_entry_date[0]) == 3 and len(parse_visa_expired[0]) == 3:
                    if parse_entry_date[0][0] != parse_visa_expired[0][0]:
                        result['validity'] = '%sy'% ( int(parse_visa_expired[0][0]) - int(parse_entry_date[0][0]))
                    elif parse_entry_date[0][1] != parse_visa_expired[0][1]:
                        result['validity'] = '%sm'%  ( int(parse_visa_expired[0][1]) - int(parse_entry_date[0][1]))
                    elif parse_entry_date[0][2] != parse_visa_expired[0][2]:
                        result['validity'] =  '%sd'%  ( int(parse_visa_expired[0][2]) - int(parse_entry_date[0][2]))
    return result

def parse_driver_license_info(label, text):
    # print(label + ": " + text)
    text = text.strip().replace('\n',' ')
    if text == '':
        return ''

    text_splits = text.strip().replace('\n',' ').split(' ', 1)
    if len(text_splits) > 1:
        text = text_splits[1]

    if label == 'weight':
        founds = re.findall(r'\d+', text)
        text = '%slb' % founds[0]  if len(founds) > 0 else ''
    
    if label == 'height':
        founds = re.findall(r'\d+', text)
        text = '%sft.%sin.' % (founds[0], int(founds[1])) if len(founds) > 1 else ''
    
    if label == 'sex':
        if "m" in text.lower():
            text = 'M'
        elif "f" in text.lower():
            text = 'F'
        else:
            text = ''
        
    if label == 'eyes':
        text = text.upper().split()[0]
    
    if label == 'hair':
        text = text.upper().split()[0]

    if label == 'issue_date':
        founds = re.findall(r'\d+', text)
        if len(founds) > 5:
            if len(founds[5]) == 2:
                founds[5] = '20' + founds[5]
            text = '%s-%s-%sT00:00:00Z' % (founds[5], founds[3], founds[4])
        elif len(founds) > 2:
            if len(founds[2]) == 2:
                founds[2] = '20' + founds[2]
            text = '%s-%s-%sT00:00:00Z' % (founds[2], founds[0], founds[1])
        else:
            text = ''

    return text

def parse_vnm_regular_visa_info(label, text):
    # print(label + ": " + text)
    text = text.strip().replace('.','')
    if text == '':
        return ''
 
    text = text.upper()

    # remove Vietnamese accents for all characters
    text = re.sub(r'[ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴ]', 'A', text)
    text = re.sub(r'[ÈÉẸẺẼÊỀẾỆỂỄ]', 'E', text)
    text = re.sub(r'[ÌÍỊỈĨ]', 'I', text)
    text = re.sub(r'[ÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠ]', 'O', text)
    text = re.sub(r'[ÙÚỤỦŨƯỪỨỰỬỮ]', 'U', text)
    text = re.sub(r'[ỲÝỴỶỸ]', 'Y', text)
    text = re.sub(r'[Đ]', 'D', text)


    if label == 'name':
        # regex get only name and space but not include \n
        founds = re.findall(r'[A-Z\s]+', text)
        # get item have longest length in list
        text = max(founds, key=len).strip() if len(founds) > 0 else ''
    
    if label == 'entry_date' or label == 'visa_expired' or label == 'dob':
        founds = re.findall(r'[0-9A-Z\s]+', text)
        # get item have longest length in list
        text = max(founds, key=len).strip() if len(founds) > 0 else ''
        text = parse_date(text)
    
    if label == 'country':
        founds = re.findall(r'[A-Z\/]+', text) # USA/Hoa Ky
        # get item have longest length in list
        text = max(founds, key=len).strip() if len(founds) > 0 else ''
        text = text.split('/')[0]

    if label == 'number_of_entries':
        if "SINGLE" in text or "MOT" in text:
            text = "single"
        elif "MULTI" in text or "NHIEU" in text:
            text = "multiple"
        else:
            print("number_of_entries: " + text)
            text = ''

    if label == 'passport_number':
        founds = re.findall(r'[0-9]+', text)
        text = max(founds, key=len).strip() if len(founds) > 0 else ''
    
    text = text.split('\n')[0]
    return text

def parse_date(text):
    # convert month string to number
    months = {
        'jan': '01',
        'feb': '02',
        'mar': '03',
        'apr': '04',
        'may': '05',
        'jun': '06',
        'jul': '07',
        'aug': '08',
        'sep': '09',
        'oct': '10',
        'nov': '11',
        'dec': '12',
    }
    founds = re.findall(r'([0-9]{2})\s*([A-Z]{3})\s*([0-9]{4})', text)
    # get all group in regex

    if len(founds) > 0:
        if len(founds[0]) == 3:
            text = '%s-%s-%sT00:00:00Z' % (founds[0][2], months[founds[0][1].lower()], founds[0][0] )
        else:
            print("date: " + text)
            text = ''
    else:
        text = ''
    return text