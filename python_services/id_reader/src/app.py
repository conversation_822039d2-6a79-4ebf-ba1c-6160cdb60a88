import base64
import os
import requests
import cv2  
import base64
import numpy as np

from flask import Flask, request
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

import util as util

# Model
net = cv2.dnn.readNetFromDarknet("models/model.cfg", "models/driver_license_tiny.model")
net.setPreferableBackend(cv2.dnn.DNN_BACKEND_OPENCV)
net.setPreferableTarget(cv2.dnn.DNN_TARGET_CPU)
ln = net.getLayerNames()
ln = [ln[i[0] - 1] for i in net.getUnconnectedOutLayers()]
LABELS = ["eyes", "hair", "height","sex","weight"]

net_green_card = cv2.dnn.readNetFromDarknet("models/model.cfg", "models/green_card_tiny.model")
ln_green_card = net_green_card.getLayerNames()
ln_green_card = [ln_green_card[i[0] - 1] for i in net_green_card.getUnconnectedOutLayers()]
LABEL_GREEN_CARD = ["issue_date"]

net_vnm_native_visa = cv2.dnn.readNetFromDarknet("models/model.cfg", "models/vnm_native_visa.model")
ln_vnm_native_visa = net_vnm_native_visa.getLayerNames()
ln_vnm_native_visa = [ln_vnm_native_visa[i[0] - 1] for i in net_vnm_native_visa.getUnconnectedOutLayers()]
LABEL_VNM_NATIVE_VISA = ["country", "dob", "expire", "name", "passport_number", "sex"]

net_vnm_regular_visa = cv2.dnn.readNetFromDarknet("models/model.cfg", "models/vnm_regular_visa.model")
ln_vnm_regular_visa = net_vnm_regular_visa.getLayerNames()
ln_vnm_regular_visa = [ln_vnm_regular_visa[i[0] - 1] for i in net_vnm_regular_visa.getUnconnectedOutLayers()]
LABEL_VNM_REGULAR_VISA = ["country", "dob", "number_of_entries", "name","passport_number", "entry_date", "visa_expired"]

@app.route('/v1/id-reader/status', methods=['GET'])
@app.route('/v1/id-reader/version', methods=['GET'])
def health_check():
    return {"success": True}

@app.route('/v1/id-reader/driver_licenses', methods=['POST'])
def read_driver_license():
    # Images
    img = request.json["image"] 
    img_buff = requests.get(img).content
    image = cv2.imdecode(np.array(bytearray(img_buff), dtype=np.uint8), -1)
    image = cv2.cvtColor(image, cv2.COLOR_BGRA2BGR)
    
    (H, W) = image.shape[:2]
    blob = cv2.dnn.blobFromImage(image, 1 / 255.0, (416, 416),swapRB=True, crop=False)
    net.setInput(blob)
    layerOutputs = net.forward(ln)
    # Initializing for getting box coordinates, confidences, classid 
    boxes = []
    confidences = []
    classIDs = []
    threshold = 0.1

    for output in layerOutputs:
        for detection in output:
            scores = detection[5:]
            classID = np.argmax(scores)
            confidence = scores[classID]
            if confidence > threshold:
                box = detection[0:4] * np.array([W, H, W, H])
                (centerX, centerY, width, height) = box.astype("int")           
                x = int(centerX - (width / 2))
                y = int(centerY - (height / 2))    
                boxes.append([x, y, int(width), int(height)])
                confidences.append(float(confidence))
                classIDs.append(classID)
    idxs = cv2.dnn.NMSBoxes(boxes, confidences, threshold, 0.1)

    result = {}
    if len(idxs) > 0:
        for i in idxs.flatten():
            (x, y) = (boxes[i][0], boxes[i][1])
            (w, h) = (boxes[i][2], boxes[i][3])
            label = LABELS[classIDs[i]]

            y = int(y-h*0.1)
            h = int(h+h*0.2)

            if label == "eyes":
                w = int(w*2.8)
            if label == "hair":
                w = int(w*2.8)
            if label == "height":
                w = int(w*2.8)
            if label == "sex":
                w = int(w*2)
            if label == "weight":
                w = int(w*2.8)

            crop_img = image[y:y+h, x:x+w]
            _, im_arr = cv2.imencode('.jpg', crop_img) 
            crop_img_base64 = base64.b64encode(im_arr.tobytes())
            result[LABELS[classIDs[i]]] = crop_img_base64
    
    if result == {}:
        return {"success":False, "message": "No object detected"}
    result = util.image_base_64_to_text(result, "driver_license")
   
    fields = {}
    for label, text in result.items():
        if text is not None:
            fields[label] = text
    
    return {"success":True, "data": fields}

@app.route('/v1/id-reader/green_card_front', methods=['POST'])
def read_green_card_front():
    # Images
    img = request.json["image"] 
    img_buff = requests.get(img).content
    image = cv2.imdecode(np.array(bytearray(img_buff), dtype=np.uint8), -1)
    image = cv2.cvtColor(image, cv2.COLOR_BGRA2BGR)
    
    (H, W) = image.shape[:2]
    blob = cv2.dnn.blobFromImage(image, 1 / 255.0, (416, 416),swapRB=True, crop=False)
    net_green_card.setInput(blob)
    layerOutputs = net_green_card.forward(ln_green_card)
    # Initializing for getting box coordinates, confidences, classid 
    boxes = []
    confidences = []
    classIDs = []
    threshold = 0.01

    for output in layerOutputs:
        for detection in output:
            scores = detection[5:]
            classID = np.argmax(scores)
            confidence = scores[classID]
            if confidence > threshold:
                box = detection[0:4] * np.array([W, H, W, H])
                (centerX, centerY, width, height) = box.astype("int")           
                x = int(centerX - (width / 2))
                y = int(centerY - (height / 2))    
                boxes.append([x, y, int(width), int(height)])
                confidences.append(float(confidence))
                classIDs.append(classID)
    idxs = cv2.dnn.NMSBoxes(boxes, confidences, threshold, 0.1)

    result = {}
    if len(idxs) > 0:
        for i in idxs.flatten():
            (x, y) = (boxes[i][0], boxes[i][1])
            (w, h) = (boxes[i][2], boxes[i][3])
            
            w = int(w*2)

            crop_img = image[y:y+h, x:x+w]
            _, im_arr = cv2.imencode('.jpg', crop_img) 
            crop_img_base64 = base64.b64encode(im_arr.tobytes())
            result[LABEL_GREEN_CARD[classIDs[i]]] = crop_img_base64
    
    if result == {}:
        return {"success":False, "message": "No object detected"}
    result = util.image_base_64_to_text(result, "green_card")
   
    fields = {}
    for label, text in result.items():
        if text is not None:
            fields[label] = text
    
    return {"success":True, "data": fields}

@app.route('/v1/id-reader/vnm_native_visa', methods=['POST'])
def read_vnm_native_visa():
    # Images
    img = request.json["image"] 
    img_buff = requests.get(img).content
    image = cv2.imdecode(np.array(bytearray(img_buff), dtype=np.uint8), -1)
    image = cv2.cvtColor(image, cv2.COLOR_BGRA2BGR)
    
    (H, W) = image.shape[:2]
    blob = cv2.dnn.blobFromImage(image, 1 / 255.0, (416, 416),swapRB=True, crop=False)
    net_vnm_native_visa.setInput(blob)
    layerOutputs = net_vnm_native_visa.forward(ln_vnm_native_visa)
    # Initializing for getting box coordinates, confidences, classid 
    boxes = []
    confidences = []
    classIDs = []
    threshold = 0.01

    for output in layerOutputs:
        for detection in output:
            scores = detection[5:]
            classID = np.argmax(scores)
            confidence = scores[classID]
            if confidence > threshold:
                
                box = detection[0:4] * np.array([W, H, W, H])

                (centerX, centerY, width, height) = box.astype("int")           
                x = int(centerX - (width / 2))
                y = int(centerY - (height / 2))    
                boxes.append([x, y, int(width), int(height)])

                confidences.append(float(confidence))
                classIDs.append(classID)
    idxs = cv2.dnn.NMSBoxes(boxes, confidences, threshold, 0.1)
    result = {}
    if len(idxs) > 0:
        for i in idxs.flatten():
            (x, y) = (boxes[i][0], boxes[i][1])
            (w, h) = (boxes[i][2], boxes[i][3])

            x = x + w 
            h = int(h*2.2)
            name = LABEL_VNM_NATIVE_VISA[classIDs[i]]
            if name == "country":
                w = h*5
            if name == "dob":
                w = h*4
            if name == "expire":
                x = x - int(h/2)
                w = h*4
            if name == "name":
                w = h*12
            if name == "passport_number":
                w = h*4
            if name == "sex":
                w = h*3
            
            cv2.rectangle(image, (x, y), (x + w, y + h), (255,0,0), 2)
            crop_img = image[y:y+h, x:x+w]
            _, im_arr = cv2.imencode('.jpg', crop_img) 
            crop_img_base64 = base64.b64encode(im_arr.tobytes())
            result[LABEL_VNM_NATIVE_VISA[classIDs[i]]] = crop_img_base64
    
    if result == {}:
        return {"success":False, "message": "No object detected"}
    # cv2.imwrite("2."+os.path.basename(img), image)

    result = util.image_base_64_to_text(result, "vnm_native_visa")
   
    fields = {}
    for label, text in result.items():
        if text is not None:
            fields[label] = text
    
    return {"success":True, "data": fields}

@app.route('/v1/id-reader/vnm_regular_visa', methods=['POST'])
def read_vnm_regular_visa():
    # Images
    img = request.json["image"] 
    img_buff = requests.get(img).content
    image = cv2.imdecode(np.array(bytearray(img_buff), dtype=np.uint8), -1)
    image = cv2.cvtColor(image, cv2.COLOR_BGRA2BGR)
    
    (H, W) = image.shape[:2]
    blob = cv2.dnn.blobFromImage(image, 1 / 255.0, (416, 416),swapRB=True, crop=False)
    net_vnm_regular_visa.setInput(blob)
    layerOutputs = net_vnm_regular_visa.forward(ln_vnm_regular_visa)
    # Initializing for getting box coordinates, confidences, classid 
    boxes = []
    confidences = []
    classIDs = []
    threshold = 0.01

    for output in layerOutputs:
        for detection in output:
            scores = detection[5:]
            classID = np.argmax(scores)
            confidence = scores[classID]
            if confidence > threshold:
                
                box = detection[0:4] * np.array([W, H, W, H])

                (centerX, centerY, width, height) = box.astype("int")           
                x = int(centerX - (width / 2))
                y = int(centerY - (height / 2))    
                boxes.append([x, y, int(width), int(height)])

                confidences.append(float(confidence))
                classIDs.append(classID)
    idxs = cv2.dnn.NMSBoxes(boxes, confidences, threshold, 0.1)
    result = {}
    if len(idxs) > 0:
        for i in idxs.flatten():
            (x, y) = (boxes[i][0], boxes[i][1])
            (w, h) = (boxes[i][2], boxes[i][3])

            x = x + w 
            h = int(h*1.5)

            name = LABEL_VNM_REGULAR_VISA[classIDs[i]]
            if name == "country":
                x = x + int(h/2)
                w = h*5
            if name == "dob":
                w = h*5
            if name == "number_of_entries":
                w = h*8
            if name == "name":
                y = y - int(h/4)
                w = h*8
            if name == "passport_number":
                w = h*4
            if name == "entry_date":
                x = x + int(h/2)
                w = h*5
            if name == "visa_expired":
                x = x + int(h/2)
                w = h*5
            
            cv2.rectangle(image, (x, y), (x + w, y + h), (255,0,0), 2)
            crop_img = image[y:y+h, x:x+w]
            _, im_arr = cv2.imencode('.jpg', crop_img) 
            crop_img_base64 = base64.b64encode(im_arr.tobytes())
            result[LABEL_VNM_REGULAR_VISA[classIDs[i]]] = crop_img_base64
    
    if result == {}:
        return {"success":False, "message": "No object detected"}
    # cv2.imwrite("2."+os.path.basename(img), image)

    result = util.image_base_64_to_text(result, "vnm_regular_visa")
   
    fields = {}
    for label, text in result.items():
        if text is not None:
            fields[label] = text
    return {"success":True, "data": fields}