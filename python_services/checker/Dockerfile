FROM python:3.6-slim
LABEL maintainer=ARIADIRECT

RUN apt-get update && \
    apt-get install -y curl unzip chromium && \
    pip install --upgrade --no-cache-dir pip

RUN curl -O https://chromedriver.storage.googleapis.com/83.0.4103.14/chromedriver_linux64.zip &&  \

    unzip chromedriver_linux64.zip && \
    mv chromedriver /usr/bin/chromedriver && \
    chmod +x /usr/bin/chromedriver && \
    rm -rf chromedriver_linux64.zip

COPY src /src

COPY requirements.txt /requirements.txt
RUN pip install -r /requirements.txt

WORKDIR /src

CMD python /src/checker.py
