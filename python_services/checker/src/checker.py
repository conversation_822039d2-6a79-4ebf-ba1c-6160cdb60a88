import argparse
import json
import logging
import os
import psycopg2

from bs4 import BeautifulSoup
from elasticsearch import Elasticsearch, RequestsHttpConnection
from retrying import retry

from selenium import webdriver
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions

ad_es = json.loads(os.environ.get('ad_es', '{}'))
ad_db = json.loads(os.environ.get('ad_db', '{}'))

# ElasticSearch config
es_host = ad_es.get('endpoint', 'vpc-ad-es-upjnyogwuftl2oi2elb7pptnum.us-west-2.es.amazonaws.com')
es_port = ad_es.get('port', 443)

# Database config
dbhost = ad_db.get('write_host', 'localhost')
database = ad_db.get('dbname', 'earth')
port = ad_db.get('port', 5432)
user = ad_db.get('username', 'aria')
password = ad_db.get('password', 'password')
db_table = 'visa_check'

# crawl website url
url_format = 'https://www.passportindex.org/comparebyPassport.php?p1={}&fl=&s=yes'

logger = logging.getLogger(__name__)


IGNORED_COUNTRIES = ('Russian Federation',
                     'Eswatini',
                     'Grenada'
                     )
# "country_name_in_website": ("alpha_3", "country_name_in_ES")
ESPECIAL_COUNTRIES = {'Congo':                          ('COG', 'Republic of the Congo'),
                      'Congo (Dem. Rep.)':              ('COD', 'Democratic Republic of Congo'),
                      'Macao':                          ('MAC', 'Macau'),
                      'Myanmar [Burma]':                ('MMR', 'Myanmar (Burma)'),
                      'North Macedonia':                ('MKD', 'Macedonia'),
                      'Palestinian Territories':        ('PSE', 'Palestinian'),
                      'St. Vincent and the Grenadines': ('VCT', 'Saint Vincent and the Grenadines'),
                      'Timor-Leste':                    ('TLS', 'East Timor'),
                      'Tonga':                          ('TON', 'Tonga'),
                      'Vatican City':                   ('VAT', 'Vatican'),
                      'Viet Nam':                       ('VNM', 'Vietnam'),
                      'United States of America':       ('USA', 'United States')
                      }


class Checker(object):

    def __init__(self):
        """
        """
        self.countries = dict()  # (country_name, iso_alpha2) as dict key/value
        self.countries_not_in_web = []
        self.country_names_not_in_es = []

    def run(self):
        """
        :return:
        """
        if not self.countries:
            self.fetch_countries_from_es()

        for country_name, (country_alpha2, country_alpha3) in self.countries.items():
            self.run_a_country(country_name, country_alpha2, country_alpha3)

        if self.countries_not_in_web:
            logger.info('These countries do not have visa check: %s', self.countries_not_in_web)

        if self.country_names_not_in_es:
            logger.info('These countries do not have records in ElasticSearch: %s', self.country_names_not_in_es)

        logger.info('Completed')

    def run_a_country(self, country_name, country_alpha2, country_alpha3):
        """
        :param country_name:
        :param country_alpha2:
        :param country_alpha3:
        :return:
        """
        logger.info("Checking country %s", country_name)

        visa_requirement = self.fetch_requirement(country_alpha2, country_alpha3)

        if visa_requirement:
            self.update_db(country_name, visa_requirement)
        else:
            logger.warning('No visa requirement info for country %s', country_name)

        self.update_db(country_name, visa_requirement)

    def fetch_countries_from_es(self, index="country"):
        """
        :param index:
        :return:
        """
        es_client = Elasticsearch(
            hosts=[{'host': es_host, 'port': es_port}],
            use_ssl=True,
            verify_certs=True,
            connection_class=RequestsHttpConnection
        )
        res = es_client.search(index=index,
                               body={'query': {'match_all': {}}},
                               size=250)

        total = res['hits']['total']['value']
        for each in res["hits"]["hits"]:
            self.countries[each["_source"]["name"]] = (each["_source"]["iso_alpha2"],each["_source"]["iso_alpha3"])
        logger.info('Get %d country records from ElasticSearch', total)
        logger.info(self.countries)
        return self.countries

    @retry(wait_fixed=500, stop_max_attempt_number=3)  # Retry 3 times with 0.5 second
    def init_db_connection(self):
        """
        :return:
        """
        try:
            conn = psycopg2.connect(user=user,
                                    password=password,
                                    host=dbhost,
                                    port=int(port),
                                    database=database
                                    )

            logger.info("connected")
            cursor = conn.cursor()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to connect to database: %s", error)

        return conn, cursor

    def update_db(self, country_name, visa_requirement):
        """
        :param country_name:
        :param visa_requirement:
        :return:
        """
        sql_select_query = """select * from {} where from_country_alpha3 = %s""".format(db_table)
        sql_update_query = \
            """INSERT INTO visa_check (from_country_alpha3, to_country_alpha3, requirement) \
            VALUES (%(from_country_alpha3)s, %(to_country_alpha3)s, %(requirement)s) \
            ON CONFLICT (from_country_alpha3, to_country_alpha3) \
            DO \
            UPDATE SET requirement = %(requirement)s"""

        from_country_alpha3 = self.countries.get(country_name)[1]

        to_be_updated = []

        connection, cursor = self.init_db_connection()
        try:
            logger.info("Get the current requirement records for country %s", from_country_alpha3)
            cursor.execute(sql_select_query, (from_country_alpha3,))
            records = cursor.fetchall()

            old_requirement_dict = dict()
            for row in records:
                old_requirement_dict[(row[0], row[1])] = row[2]

            for to_country_alpha3, new_requirement in visa_requirement.items():
                if old_requirement_dict.get((from_country_alpha3, to_country_alpha3)) == new_requirement:
                    continue

                # Need update if None, or different
                tmp_dict = {"from_country_alpha3": from_country_alpha3,
                            "to_country_alpha3": to_country_alpha3,
                            "requirement": new_requirement
                            }
                to_be_updated.append(tmp_dict)

            if to_be_updated:
                logger.info("Update database for country %s", country_name)

                # Update multiple records
                cursor.executemany(sql_update_query, to_be_updated)
                connection.commit()
                row_count = cursor.rowcount
                logger.info("%d records updated successfully", row_count)
            else:
                logger.info("No update needed for country %s", country_name)
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to updating records into table: %s", error)
        finally:
            # closing database connection.
            cursor.close()
            connection.close()
            logger.info("PostgreSQL connection is closed")

    def fetch_requirement(self, country_alpha2, country_alpha3):
        """
        :param country_alpha2:
        :param country_alpha3:
        :return:
        """
        def page_is_not_ready(soup):
            """
            :param soup:
            :return:
            """
            visa_count = soup.find('span', {"class": "comp-top-ms ms-2"}).text
            if visa_count and int(visa_count) > 0:
                return False
            else:
                logger.info('Page is not loaded yet...')
                return True

        @retry(wait_fixed=2 * 1000, stop_max_delay=30 * 1000, retry_on_result=page_is_not_ready)
        def load_page():
            """
            :return:
            """
            return BeautifulSoup(driver.page_source, "lxml")

        # Running chrome in container environment
        path_to_chromedriver = '/usr/bin/chromedriver'

        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')

        url = url_format.format(country_alpha2.lower())

        driver = webdriver.Chrome(path_to_chromedriver, chrome_options=chrome_options)
        driver.get(url)

        requirement_dict = dict()

        try:
            soup = load_page()

            for tr in soup.select("table tbody tr"):
                tds = tr.find_all('td')

                to_country_name = tds[0].text.strip()
                req = tds[1].text.strip()

                logger.info('"%s" ---> "%s"', to_country_name, req)

                if to_country_name in IGNORED_COUNTRIES:
                    logger.warning('Skip country %s', to_country_name)
                    continue

                # Handle the special country name
                if to_country_name in ESPECIAL_COUNTRIES:
                    _alpha3 = ESPECIAL_COUNTRIES.get(to_country_name)[0]
                    requirement_dict[_alpha3] = req
                else:
                    _alpha_tuple = self.countries.get(to_country_name)
                    if _alpha_tuple:
                        # country_alpha3
                        requirement_dict[_alpha_tuple[1]] = req
                    else:
                        # We record it if the to_country is not in ES
                        logger.warning('Could NOT find the country in ElasticSearch: "%s"', to_country_name)
                        if to_country_name not in self.country_names_not_in_es:
                            self.country_names_not_in_es.append(to_country_name)

        except Exception:
            logger.warning('Timed out waiting for page to load')
            logger.warning('Return empty. Skip country %s', country_alpha2)
            # We assume this country not in web for visa requirement, record it
            if country_alpha3 not in self.countries_not_in_web:
                self.countries_not_in_web.append(country_alpha3)

        driver.quit()
        logger.info('Updated %d countries', len(requirement_dict.keys()))
        return requirement_dict


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='preview service parameters')
    args = parser.parse_args()
    logger.debug('Parsed arguments: %s', vars(args))

    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.INFO
    )
    checker = Checker()
    checker.run()
