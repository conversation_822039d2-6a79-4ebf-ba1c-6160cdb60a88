import cv2
import mediapipe as mp
import numpy as np
import urllib3
import requests

FACE_MASK_MODEL = "models/face_mask_detection.model"
FACE_MASK_PROTO = "models/face_mask_detection.prototxt"
net = cv2.dnn.readNet(FACE_MASK_MODEL,FACE_MASK_PROTO)

class FaceMaskDetection(object):
    def detect(self, file_path):
        image = cv2.imread(file_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        blob = cv2.dnn.blobFromImage(image, scalefactor=1/255.0, size=(160, 160))
        net.setInput(blob)

        names = [net.getLayerNames()[i[0] - 1] for i in net.getUnconnectedOutLayers()]
        y_bboxes_output, y_cls_output = net.forward(names)
        
        # remove the batch dimension, for batch is always 1 for inference.
        y_cls = y_cls_output[0]

        # To speed up, do single class NMS, not multiple classes NMS.
        bbox_max_scores = np.max(y_cls, axis=1)
        bbox_max_score_classes = np.argmax(y_cls, axis=1)
        keep_idx  = cv2.dnn.NMSBoxes(y_bboxes_output, bbox_max_scores.tolist(), 0.5, 0.4)
        # get len of keep_idx
        if len(keep_idx) == 0:
            return False
        face_mask_idx = bbox_max_score_classes[keep_idx[0]]
        
        if len(face_mask_idx) == 0:
            return False
        # 0: face mask, 1: no face mask
        return face_mask_idx[0] == 0
