import country_converter as coco
import logging
import re

from aws import AWS
from datetime import datetime
from trp import Document


SUCCESS = "SUCCESS"
FAILURE = "FAILURE"
OK = "OK"

MULTIPLE_ENTRIES = 'multiple_entries'
SINGLE_ENTRY = 'single_entry'


# Example:
#
# Key: "NHAP CANH VIET NAM NHIEU LAN DEN NGAY", Value: "23/10/2023"
# Key: "HO CHIEU/GIAY TO CO GIA TRI DI LAI QUOC TE SO", Value: "533740073"
# Key: "CUA NUOC/VUNG LANH THO", Value: "United States"
# Key: "DATE OF BIRTH", Value: "16/11/1986"
# Key: "SEX", Value: "None"
# Key: "CAP CHO", Value: "JONATHANDUY TRAN"
# Key: "GIOI TINH", Value: "NAM"
# Key: "DURATION OF STAY DOES NOT EXCEED 180 DAYS FOR EACH ENTRY", Value: "None"
# # Key: "FOR <PERSON>ULTIPLE ENTRIES INTO VIETNAM ON VISA EXEMPTION UNTIL", Value: "MOI MOILAN LAN NHAP CANH DUOC TAM TRU KHONG QUA 180 NGAY"


visa_mapping = {
    "NHAP CANH VIET NAM NHIEU LAN DEN NGAY":         "visa_expiration",
    "INTO VIETNAM ON VISA EXEMPTION UNTIL":          "visa_expiration",
    "HO CHIEU/GIAY TO CO GIA TRI DI LAI QUOC TE SO": "passport_number",
    "INTERNATIONAL TRAVEL DOCUMENT":                 "passport_number",
    "CUA NUOC/VUNG LANH THO":                        "nationality",
    "COUNTRY":                                       "nationality",
    "TERRITORY":                                     "nationality",
    "DATE OF BIRTH":                                 "date_of_birth",
    "CAP CHO":                                       "name",
    "ISSUED TO":                                     "name",
    "ISSUED":                                        "name",
    "GIOI TINH":                                     "gender",
    "SEX":                                           "gender"
}

COUNTRY_NOT_FOUND = 'COUNTRY NOT FOUND'

TO_VERIFY = (
    'name',
    'gender',
    'passport_number',
    'nationality',
    'date_of_birth',
    'number_of_entries'
    # 'validity'
)

logger = logging.getLogger(__name__)


def run(s3_bucket, s3_key, expected, aws_region):
    """
    :param s3_bucket:
    :param s3_key:
    :param expected:
    :param aws_region:
    :return:
    """
    visa_dict, confidences = docu_analysis(s3_bucket, s3_key, aws_region)

    ret = dict()
    mismatch = list()
    reason_message = ''

    for k in TO_VERIFY:
        # Handle nationality
        if k == 'nationality':
            country_name = coco.convert(expected[0].get(k), src='ISO3', to='name_short', not_found=COUNTRY_NOT_FOUND)
            if country_name == COUNTRY_NOT_FOUND:
                mismatch.append(k)
                reason_message += 'Nationality in application form is not valid'
            elif country_name.lower() != visa_dict.get(k, '').lower():
                mismatch.append(k)
                reason_message += 'Mismatch: {}: in application: {}; in visa: {} \n' \
                    .format(k, expected[0].get(k), visa_dict.get(k))

            expected[0][k] = country_name

        # Handle name
        if k == 'name':
            _surname = expected[0].pop('surname', '').lower()
            _given_name = expected[0].pop('given_name', '').lower()

            ok = visa_dict.get(k, '').lower() == ' '.join([_surname, _given_name]) or \
                 visa_dict.get(k, '').lower() == ' '.join([_given_name, _surname])
            if ok:
                expected[0]['name'] = visa_dict.get(k, '').upper()
            else:
                expected[0]['name'] = ' '.join([_given_name, _surname]).upper()
                mismatch.append(k)
                reason_message += 'Mismatch: {}: in application: {}; in approval letter: {} \n' \
                    .format(k, expected[0]['name'], visa_dict.get(k))

        elif expected[0].get(k, '').lower() != visa_dict.get(k, '').lower():
            mismatch.append(k)
            reason_message += 'Mismatch: {}: in application: {}; in visa: {} \n' \
                .format(k, expected[0].get(k), visa_dict.get(k))

    if mismatch:
        ret['status'] = FAILURE
        ret['reason'] = reason_message
    else:
        ret['status'] = SUCCESS
        ret['reason'] = OK

    ret['confidence'] = sum(confidences) / len(confidences) if confidences else 0
    ret['details'] = {
        'mismatch': mismatch,
        'application_submitted': expected[0],
        'ai_results': visa_dict
    }

    logger.info('Results: %s', ret)
    return ret


def docu_analysis(s3_bucket, s3_key, aws_region):
    """
    :param s3_bucket:
    :param s3_key:
    :param aws_region:
    :return:
    """
    aws_client = AWS(aws_region)

    logger.info('Detect VNM Native VISA %s/%s', s3_bucket, s3_key)
    response = aws_client.run_docu_analysis_job(s3_bucket, s3_key)
    logger.debug('Response: %s', response)

    doc = Document(response)

    visa_dict = dict()
    confidences = list()

    lines = list()
    for page in doc.pages:
        for line in page.lines:
            lines.append(line.text)

    already = list()  # Cache the found keys
    not_checked_fields = list()  # record for the missed field
    for page in doc.pages:
        for field in page.form.fields:
            logger.info('field key: "%s", field value: "%s"', field.key, field.value)

            if not field.value: continue

            found = find_the_key(field.key.text, already)
            if found is None:
                if field.value:
                    not_checked_fields.append((field.key, field.value))
                continue
            else:
                already.append(found)

                visa_dict[found] = field.value.text.strip()
                confidences.append(field.key.confidence)
                confidences.append(field.value.confidence)

    missed_keys = set(visa_mapping.values()) - set(already)
    if missed_keys:
        logger.info('Still missed keys: %s', missed_keys)  # Such as 'name', 'gender'
        logger.info('Unchecked fields: %s', not_checked_fields)

        second_visa_mapping_keys = [(k, v) for k, v in visa_mapping.items() if v in missed_keys]
        logger.info('Second round search for keys: %s', second_visa_mapping_keys)

        for f_key, f_val in not_checked_fields:
            words = f_key.text.split(' ')
            for tar_str, tar_key in second_visa_mapping_keys:
                matches = [1 if w in tar_str else 0 for w in words]
                if sum(matches) / len(matches) >= 0.5:
                    logger.info('Matched! "%s" -- "%s" -->"%s"', f_key, tar_str, tar_key)
                    visa_dict[tar_key] = f_val.text
                    break


    # Handle name format
    if visa_dict.get('name'):
        visa_dict['name'] = visa_dict.get('name', '').upper()

    # Special handle for gender
    gender = visa_dict.get('gender', '').strip()
    if gender:
        if 'NU' in gender:
            visa_dict['gender'] = 'F'
        elif 'NAM' in gender or 'A' in gender or 'M' in gender:
            visa_dict['gender'] = 'M'
        else:
            visa_dict['gender'] = 'M'

    # Checking number_of_entries
    lines_str = ' '.join(lines)
    if 'MULTIPLE ENTRIES' in lines_str:
        visa_dict['number_of_entries'] = MULTIPLE_ENTRIES
    elif 'SINGLE ENTRY' in lines_str:
        visa_dict['number_of_entries'] = SINGLE_ENTRY
    elif 'MULTIPLE' in lines_str:
        visa_dict['number_of_entries'] = MULTIPLE_ENTRIES
    elif 'ENTRIES' in lines_str:
        visa_dict['number_of_entries'] = MULTIPLE_ENTRIES
    elif 'SINGLE' in lines_str:
        visa_dict['number_of_entries'] = SINGLE_ENTRY
    else:
        visa_dict['number_of_entries'] = MULTIPLE_ENTRIES

    # Special handle for date_of_birth
    # It could be 22/04/1983 --> 22104/1983
    dob = visa_dict.get('date_of_birth')
    if dob:
        pattern = re.compile(r"\d{2}/\d{2}/\d{4}")
        if not re.match(pattern, dob):
            dob = dob[:2] + '/' + dob[3:5] + '/' + dob[6:]

        visa_dict['date_of_birth'] = datetime.strftime(datetime.strptime(dob, '%d/%m/%Y'),
                                                       '%Y-%m-%dT%H:%M:%SZ')

    logger.info('The raw data from VNM visa %s', visa_dict)

    return visa_dict, confidences


def find_the_key(src_str, already):
    """
    :param src_str:
    :param already:
    :return:
    """
    for tar_str, tar_val in visa_mapping.items():
        if tar_val in already:
            continue

        if compare(src_str, tar_str):
            logger.info('Matched!  "%s" --> "%s" --> "%s"', src_str, tar_str, tar_val)
            return tar_val
    logger.info('Could not find: "%s"', src_str)
    return None


def compare(src, tar, threshold=0.85):
    """
    :param src:
    :param tar:
    :param threshold:
    :return:
    """
    if len(src) < 3 or len(tar) < 3:
        return False

    logger.info('src: "%s"    tar: "%s"', src, tar)

    # Handle the '/' case
    if '/' in src or '/' in tar:
        if len(src) == len(tar):
            src = src.replace('/', 'I')
            tar = tar.replace('/', 'I')
        else:
            src = src.replace('/', '')
            tar = tar.replace('/', '')

    if len(src) <= len(tar):
        short, long = src, tar
    else:
        short, long = tar, src

    len_diff = len(long) - len(short)

    for d in range(len_diff + 1):
        matches = [1 if short[i] == long[i+d] else 0 for i in range(len(short))]
        similarity = sum(matches) / len(short)

        if similarity > threshold:
            logger.info('Similarity: %s; Threshold: %s', similarity, threshold)
            return True

    return False


def local_test():
    # Note: Need update the aws.py profile_name
    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.INFO
    )
    aws_region = 'us-west-2'

    s3_bucket = 'ad-test-ai-service-us-west-2'
    s3_key = 'Truong.jpg'
    expected = [{}]
    run(s3_bucket, s3_key, expected, aws_region)
