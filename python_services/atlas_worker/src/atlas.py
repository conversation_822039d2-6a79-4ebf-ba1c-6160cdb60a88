import argparse
import boto3
import botocore
import json
import logging
import os

from datetime import datetime
from retrying import retry
from xrequests import XRequests

from worker import Worker


ad_api_token = json.loads(os.environ.get('ad_api_token', '{}'))['token']

ad_aws = json.loads(os.environ.get('ad_aws', '{}'))
ad_sqs = json.loads(os.environ.get('ad_sqs', '{}'))

sqs_queue_url = '/'.join((ad_sqs.get('url_prefix', ''),
                         ad_sqs.get('atlas_sqs_name', '')))

aws_region_name = ad_aws.get('region', 'us-west-2')
session = boto3.Session(region_name=aws_region_name)
sqs_client = session.client("sqs")

DATEFORMAT = "%Y-%m-%dT%H:%M:%SZ"
FAILURE = "FAILURE"

logger = logging.getLogger(__name__)


def run():
    """
    :return:
    """
    while True:
        messages = receive_sqs_messages()
        for msg in messages:
            logger.info('Got the SQS message %s', msg)

            request_id, stype, application_id, inputs, callback, request_ts = parse_message_body(
                msg.get('Body'))

            try:
                # 1. run the worker

                try:
                    wk = Worker(aws_region=aws_region_name)
                    response = wk.run(stype, inputs)

                    result_ts = datetime.strftime(datetime.now(), DATEFORMAT)
                    if type(response) == list:
                        for each_resp in response:
                            cback = dict()

                            if int(application_id) == -1:
                                cback['request_id'] = each_resp.get(
                                    'request_id')
                                cback['application_id'] = each_resp.get(
                                    'application_id')
                            else:
                                cback['request_id'] = request_id
                                cback['application_id'] = application_id

                            cback['request_ts'] = request_ts
                            cback['result_ts'] = result_ts
                            cback['outputs'] = each_resp
                            cback['confidence'] = each_resp.get('confidence')
                            # 2. Update the result
                            update_service_result(callback, cback)
                    else:
                        cback = dict()
                        cback['request_id'] = request_id
                        cback['application_id'] = application_id

                        cback['request_ts'] = request_ts
                        cback['result_ts'] = result_ts
                        cback['outputs'] = response
                        cback['confidence'] = response.get('confidence')
                        # 2. Update the result
                        update_service_result(callback, cback)
                except Exception as exc:
                    logger.error('Error: %s', exc)

                    result_ts = datetime.strftime(datetime.now(), DATEFORMAT)

                    if stype == 'd10001':
                        for each_resp in inputs.get('expected'):
                            cback = dict()

                            if int(application_id) == -1:
                                cback['request_id'] = each_resp.get(
                                    'request_id')
                                cback['application_id'] = each_resp.get(
                                    'application_id')
                            else:
                                cback['request_id'] = request_id
                                cback['application_id'] = application_id

                            cback['request_ts'] = request_ts
                            cback['result_ts'] = result_ts
                            cback['confidence'] = 100
                            cback['outputs'] = {
                                "status": FAILURE,
                                "reason": "Fail to process the request: {}".format(exc)
                            }
                            # 2. Update the result
                            update_service_result(callback, cback)
                    else:
                        cback = dict()
                        cback['request_id'] = request_id
                        cback['application_id'] = application_id
                        cback['request_ts'] = request_ts

                        cback['result_ts'] = result_ts
                        cback['confidence'] = 100
                        cback['outputs'] = {
                            "status": FAILURE,
                            "reason": "Fail to process the request: {}".format(exc)
                        }
                        # 2. Update the result
                        update_service_result(callback, cback)

                # 3. delete the processed image message from queue
                logger.info('Received and deleted message: %s', msg)
                sqs_client.delete_message(
                    QueueUrl=sqs_queue_url,
                    ReceiptHandle=msg['ReceiptHandle']
                )
            except Exception as exc:
                logger.exception('FAILURE, Atlas Service, %s', exc)
                raise


def receive_sqs_messages():
    """
    :return:
    """
    # Receive message from SQS queue
    logger.debug("Listen to SQS: %s", sqs_queue_url)
    response = sqs_client.receive_message(
        QueueUrl=sqs_queue_url,
        AttributeNames=[
            'SentTimestamp'
        ],
        MaxNumberOfMessages=1,
        MessageAttributeNames=[
            'All'
        ],
        VisibilityTimeout=0,
        WaitTimeSeconds=20
    )
    # If there is message in queue
    if response.get('Messages'):
        return response['Messages']
    else:
        logger.debug("No message in queue")
        return []


def parse_message_body(body_str):
    """
    :param body_str:
    :return:
    """
    try:
        msg = json.loads(body_str)

        request_id = msg.get('request_id')
        stype = msg.get('type')
        application_id = msg.get('application_id')
        inputs = msg.get('inputs')
        callback = msg.get('callback')
        request_ts = msg.get('request_ts')

    except Exception as exc:
        logger.exception(
            'Fail to parse the message:\n %s \n %s', body_str, exc)
        raise

    return request_id, stype, application_id, inputs, callback, request_ts


# Retry 3 times with 0.5 second
@retry(wait_fixed=500, stop_max_attempt_number=3)
def update_service_result(callback, data):
    """
    :param callback:
    :param data:
    :return:
    """
    logger.info('Update atlas results to database: %s', data)

    try:
        headers = {'x-ad-token': ad_api_token}
        update_result = XRequests(callback)
        update_result.post(data=data, headers=headers, value_only=False)
    except Exception as exc:
        logger.exception('Callback failure: %s', exc)


def main():
    parser = argparse.ArgumentParser(description='preview service parameters')
    args = parser.parse_args()

    logger.debug('Parsed arguments: %s', vars(args))
    run()


if __name__ == "__main__":
    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.INFO
    )
    # Really don't need to hear about connections being brought up again after server has closed it
    logging.getLogger('requests.packages.urllib3.connectionpool').setLevel(
        logging.WARNING)
    logging.getLogger(
        'botocore.vendored.requests.packages.urllib3.connectionpool').setLevel(logging.WARNING)
    main()
