import boto3
import json
import logging
import os
import time

from retrying import retry


ad_s3 = json.loads(os.environ.get('ad_s3', '{}'))

atlas_cache_s3_bucket = ad_s3.get(
    'ariadirect_prod_atlas_cache_s3_bucket', 'ad-prod-us-atlas-cache-us-west-2')

logger = logging.getLogger(__name__)


class AWS(object):

    def __init__(self, region_name='us-west-2'):
        """
        :param region_name:
        """
        _session = boto3.Session(region_name=region_name)
        self.reko_client = _session.client("rekognition")

        if region_name == 'us-west-2':
            new_session = boto3.Session(region_name='us-west-2')
            self.text_client = _session.client('textract')
            self.s3_client =  new_session.client('s3')
        else:
            new_session = boto3.Session(region_name='us-west-2')
            self.text_client = new_session.client('textract')
            self.s3_client = new_session.client('s3')

    def detect_labels(self, s3_bucket, s3_key, max_labels=20, min_confidence=50):
        """
        :param s3_bucket:
        :param s3_key:
        :param max_labels:
        :param min_confidence:
        :return:
        """
        logger.info('Calling AWS Rekognition detect_labels')
        response = self.reko_client.detect_labels(
            Image={
                'S3Object': {
                    'Bucket': s3_bucket,
                    'Name': s3_key
                }
            },
            MaxLabels=max_labels,
            MinConfidence=min_confidence
        )
        return response

    def detect_faces(self, s3_bucket, s3_key):
        """
        :param s3_bucket:
        :param s3_key:
        :param max_labels:
        :param min_confidence:
        :return:
        """
        supported_image = ['.jpg', '.png', 'jpeg']
        attributes = [
            'AgeRange',
            'Eyeglasses',
            'EyesOpen',
            'Gender',
            'MouthOpen',
            'Sunglasses'
        ]

        logger.info('Calling AWS Rekognition detect_faces')
        response = self.reko_client.detect_faces(
            Image={
                'S3Object': {
                    'Bucket': s3_bucket,
                    'Name': s3_key
                }
            },
            Attributes=[
                'ALL',
            ]
        )
        faces = response['FaceDetails']

        return faces

    def compare_faces(self, src_s3_bucket, src_s3_key, tar_s3_bucket, tar_s3_key,
                      similarity_threshold=80, quality_filter='AUTO'):
        """
        :param src_s3_bucket:
        :param src_s3_key:
        :param tar_s3_bucket:
        :param tar_s3_key:
        :param similarity_threshold:
        :param quality_filter:
        :return:
        """
        logger.info('Calling AWS Rekognition compare_faces')
        response = self.reko_client.compare_faces(
            SourceImage={
                'S3Object': {
                    'Bucket': src_s3_bucket,
                    'Name': src_s3_key
                }
            },
            TargetImage={
                'S3Object': {
                    'Bucket': tar_s3_bucket,
                    'Name': tar_s3_key
                }
            },
            SimilarityThreshold=similarity_threshold,
            QualityFilter=quality_filter
        )

        return response

    def run_docu_analysis_job(self, s3_bucket, s3_key):
        """
        :param s3_bucket:
        :param s3_key:
        :return:
        """
        cache_s3_bucket = atlas_cache_s3_bucket
        if self.s3_client:
            logger.info('Copy S3 key to the cached S3 bucket %s',
                        cache_s3_bucket)
            self.s3_client.copy_object(
                CopySource={'Bucket': s3_bucket, 'Key': s3_key},
                Bucket=cache_s3_bucket,
                Key=s3_key
            )
            s3_bucket = cache_s3_bucket

        job_id = self._start_docu_analysis_job(s3_bucket, s3_key)
        return self._get_docu_analysis_result(job_id)

    def _start_docu_analysis_job(self, s3_bucket, s3_key):
        """
        :param s3_bucket:
        :param s3_key:
        :return:
        """
        response = self.text_client.start_document_analysis(
            DocumentLocation={
                'S3Object': {
                    'Bucket': s3_bucket,
                    'Name': s3_key
                }
            },
            FeatureTypes=[
                'TABLES',
                'FORMS'
            ]
        )
        return response['JobId']

    def _get_docu_analysis_result(self, job_id):
        """
        :param job_id:
        :return:
        """

        def retry_if_not_complete(result):
            """
            :param result:
            :return:
            """
            # 'JobStatus': 'IN_PROGRESS' | 'SUCCEEDED' | 'FAILED' | 'PARTIAL_SUCCESS',
            return result.get('JobStatus') == 'IN_PROGRESS'

        @retry(wait_fixed=2 * 1000, stop_max_delay=300 * 1000, retry_on_result=retry_if_not_complete)
        def is_job_complete(job_id):
            """
            :param job_id:
            :return:
            """
            ret = self.text_client.get_document_analysis(
                JobId=job_id,
            )
            logger.info(ret.get('JobStatus'))
            return ret

        def get_result(job_id, max_results=1000):
            """
            :param job_id:
            :param max_results:
            :return:
            """
            results = list()

            ret = self.text_client.get_document_analysis(
                JobId=job_id,
                MaxResults=max_results
            )

            results.append(ret)
            next_token = ret.get('NextToken')

            while next_token:
                time.sleep(1)
                ret = self.text_client.get_document_analysis(
                    JobId=job_id,
                    MaxResults=max_results,
                    NextToken=next_token
                )
                results.append(ret)
                next_token = ret.get('NextToken')

            return results

        if is_job_complete(job_id):
            return get_result(job_id)
