import country_converter as coco
import logging
import re

from aws import AWS
from datetime import datetime
from trp import Document


SUCCESS = "SUCCESS"
FAILURE = "FAILURE"
NOT_FOUND = "NOT FOUND"
OK = "OK"

COUNTRY_NOT_FOUND = 'COUNTRY NOT FOUND'
TO_VERIFY = (
    'name',
    'gender',
    # 'passport_number',  # TODO: it is the search key
    'nationality',
    'date_of_birth',
    'number_of_entries',
    'validity'
)

logger = logging.getLogger(__name__)


def run(approval_letters, expected, aws_region='us-west-2'):
    """

    :param approval_letters:
    :param expected:
    :param aws_region:
    :return:
    """
    ret_list = list()

    approval_applications = list()
    for s3_bucket, s3_key in approval_letters:
        approval_applications.extend(docu_analysis(s3_bucket, s3_key, aws_region))

    # Example:
    # "expected": [
    #     {
    #         "application_id": 1544,
    #         "nationality": "USA",
    #         "number_of_entries": "multiple_entries",
    #         "request_id": "f0100895-195e-4f53-93e3-689fc9147fd1",
    #         "validity": "1y"
    #     },
    # ]

    for expected_each in expected:
        ret = dict()
        mismatch = list()
        reason_message = ''

        ret['application_id'] = expected_each.get('application_id')
        ret['request_id'] = expected_each.get('request_id')

        logger.info('Checking the expected: %s', expected_each)
        # TODO: to detect better with mismatch passport number
        found = [each for each in approval_applications if each.get('passport_number') == expected_each.get('passport_number')]

        if not found:
            logger.info('Did not find the application in the approval letter!')
            ret['status'] = NOT_FOUND
            ret['reason'] = '404 not found: the passport number not in the approval letter'
            ret['details'] = {
                'mismatch': mismatch,
                'application_submitted': expected_each,
                'ai_results': approval_applications
            }
            ret['confidence'] = 100
        else:
            logger.info('Found the passport_number match: %s', found)

            for k in TO_VERIFY:
                logger.info('verify: %s', k)
                logger.info('expected %s', expected_each)

                # Handle nationality
                if k == 'nationality':
                    country_name = coco.convert(expected_each.get(k), src='ISO3', to='name_short',
                                                not_found=COUNTRY_NOT_FOUND)
                    if country_name == COUNTRY_NOT_FOUND:
                        mismatch.append(k)
                        reason_message += 'Nationality in application form is not valid'
                    elif country_name.lower() != found[0].get(k, '').lower():
                        mismatch.append(k)
                        reason_message += 'Mismatch: {}: in application: {}; in approval letter: {} \n' \
                            .format(k, expected_each.get(k), found[0].get(k))

                        expected_each[k] = country_name
                # Handle name
                elif k == 'name':
                    _surname = expected_each.pop('surname', '').lower()
                    _given_name = expected_each.pop('given_name', '').lower()
                    ok = found[0].get(k, '').lower() == ' '.join([_surname, _given_name]) or \
                         found[0].get(k, '').lower() == ' '.join([_given_name, _surname])
                    if ok:
                        expected_each['name'] = found[0].get(k, '').upper()
                    else:
                        expected_each['name'] = ' '.join([_given_name, _surname])
                        mismatch.append(k)
                        reason_message += 'Mismatch: {}: in application: {}; in approval letter: {} \n' \
                            .format(k, expected_each['name'], found[0].get(k))

                elif expected_each.get(k, '').lower() != found[0].get(k, '').lower():
                    mismatch.append(k)
                    reason_message += 'Mismatch: {}: in application: {}; in approval letter: {} \n'\
                        .format(k, expected_each.get(k), found[0].get(k))

            if mismatch or reason_message:
                ret['status'] = FAILURE
                ret['reason'] = reason_message
                ret['details'] = {
                    'mismatch': mismatch,
                    'application_approved': found[0],
                    'application_submitted': expected_each,
                    'ai_results': approval_applications
                }
                ret['confidence'] = found[0]['confidence']
            else:
                ret['status'] = SUCCESS
                ret['reason'] = OK
                ret['details'] = {
                    'mismatch': mismatch,
                    'application_approved': found[0],
                    'application_submitted': expected_each,
                    'ai_results': approval_applications
                }
                ret['confidence'] = found[0]['confidence']

        ret_list.append(ret)

    logger.info('Approval Letter: Results: %s', ret_list)
    return ret_list


def docu_analysis(s3_bucket, s3_key, aws_region):
    """
    :param s3_bucket:
    :param s3_key:
    :param aws_region:
    :return:
    """
    aws_client = AWS(aws_region)

    logger.info('Detect VNM approval letter: %s/%s', s3_bucket, s3_key )
    response = aws_client.run_docu_analysis_job(s3_bucket, s3_key)
    logger.debug('Response:  %s', response)

    doc = Document(response)

    lines = list()
    rows = list()
    confidences = list()

    for page in doc.pages:
        for line in page.lines:
            confidences.append(line.confidence)
            lines.append(line.text)
        for table in page.tables:
            rows.extend(table.rows)

    # 1. detect how many people
    # 2. detect validity
    # 3. detect number of entries

    # Note: Any position with 'following person' keyword
    locs = list()
    for loc, txt in enumerate(lines):
        if 'following person' in txt:
            locs.append(loc)

    tmp_results = list()
    for loc in locs:
        how_many_person_line = ' '.join(lines[loc:loc + 10])
        logger.info("Got a line: %s", how_many_person_line)

        number_pattern = re.compile(r'\s*(\d{1,4}) following person\.*')
        validity_pattern = re.compile(r'\.*from (\d{1,2}\/\d{1,2}\/\d{4}) to (\d{1,2}\/\d{1,2}\/\d{4})\.*')
        entry_pattern = re.compile(r'\.*granted (.*) Viet\.*')

        found = number_pattern.search(how_many_person_line)
        if found:
            count = int(found.groups()[0])
        else:  # Second try to search "people to enter" keyword
            for loc, txt in enumerate(lines):
                if 'people to enter' in txt:
                    break
            second_how_many_person_line = ' '.join(lines[loc- 2:loc + 2])
            second_number_pattern = re.compile(r'\s*(\d{1,4}) people to enter\.*')
            found = second_number_pattern.search(second_how_many_person_line)
            if found:
                count = int(found.groups()[0])

        found = validity_pattern.search(how_many_person_line)
        if found:
            validity = found.groups()

        found = entry_pattern.search(how_many_person_line)

        if found:
            _number_of_entries = found.groups()[0]
            if 'single entr' in _number_of_entries:
                number_of_entries = 'single_entry'
            elif 'multiple entr' in _number_of_entries:
                number_of_entries = 'multiple_entries'
            elif 'double entr' in _number_of_entries:
                number_of_entries = 'double_entries'
            else:
                number_of_entries = 'UNKNOWN'
        else:
            number_of_entries = 'UNKNOWN'

        tmp_results.append((count, validity, number_of_entries))

    # 3. The list of people
    applications = list()
    if rows:
        # Full name / Gender / Date of birth / Nationality / Passport No
        for row in rows[1:]:
            try:
                person_tuple =[]

                for cell in row.cells:
                    person_tuple.append(cell.text)
                    confidences.append(cell.confidence)

                logger.info('The table has %s columns', len(person_tuple))
                logger.info(person_tuple)
                # Example: ['KEHO PHILIP JAV ', 'Male ', '04/07/1971 United States ']
                person = dict()
                if len(person_tuple) == 3:
                    if '-' in person_tuple[0]:
                        person['name'] = person_tuple[0].split(' ', 1)[-1].strip('-').strip()
                    else:
                        person['name'] = person_tuple[0]
                    logger.info('raw data: "%s"; Name: "%s"', person_tuple[0], person['name'])

                    person['gender'] = person_tuple[1]
                    # Special case:
                    person['date_of_birth'] = person_tuple[2].split(' ', 1)[0].strip()
                    person['nationality'] = person_tuple[2].split(' ', 1)[1].strip()

                    # Special handle for the passport number
                    for loc, txt in enumerate(lines):
                        if person_tuple[2].strip() in txt:
                            person['passport_number'] = lines[loc + 1]
                            break
                    else:
                        person['passport_number'] = 'UNKNOWN'
                elif len(person_tuple) == 4:
                    person['name'] = person_tuple[0].split(' ', 1)[-1].strip('-').strip()
                    logger.info('raw data: "%s"; Name: "%s"', person_tuple[0], person['name'])

                    person['gender'] = person_tuple[1]
                    person['date_of_birth'] = person_tuple[2].split(' ', 1)[0].strip()
                    person['nationality'] = person_tuple[2].split(' ', 1)[-1].strip()
                    person['passport_number'] = person_tuple[3].strip()
                elif len(person_tuple) == 5:
                    person['name'] = person_tuple[0].split(' ', 1)[-1].strip('-').strip()
                    logger.info('raw data: "%s"; Name: "%s"', person_tuple[0], person['name'])

                    person['gender'] = person_tuple[1]
                    person['date_of_birth'] = person_tuple[2]
                    person['nationality'] = person_tuple[3]
                    person['passport_number'] = person_tuple[4].strip()
                elif len(person_tuple) == 6:
                    person['name'] = person_tuple[1].strip('-').strip()
                    logger.info('raw data: "%s"; Name: "%s"', person_tuple[0], person['name'])

                    person['gender'] = person_tuple[2]
                    person['date_of_birth'] = person_tuple[3]
                    person['nationality'] = person_tuple[4]
                    person['passport_number'] = person_tuple[5].strip()

                person['confidence'] = sum(confidences) / len(confidences)
                applications.append(person)
            except Exception as exc:
                logger.info('Error %s', exc)
                continue
    else:
        logger.info('No table detected')
        person = dict()

        for loc, line in enumerate(lines):
            if 'Date of birth' in line:
                break
        try:
            person['name'] = lines[loc - 1].split(')')[-1].strip(':').strip()
            logger.info('raw data: "%s"; Name: "%s"', lines[loc - 1], person['name'])

            person['gender'] = 'Male' if 'Mr' in ' '.join(lines) else 'Female'

            if '(Date of birth)' in lines[loc]:
                person['date_of_birth'] = lines[loc].split(')')[-1].strip(':').strip()

            if '(Nationality)' in lines[loc + 1]:
                person['nationality'] = lines[loc + 1].split(')')[-1].strip(':').strip()

            if '(Passport No)' in lines[loc + 2]:
                person['passport_number'] = lines[loc + 2].split(')')[-1].strip(':').split()[-1]

            person['confidence'] = sum(confidences) / len(confidences)
            applications.append(person)
        except Exception as exc:
            logger.error(exc)

    # attach validity, number_of_entries to each person

    # If no validity and number_of_entries detected
    if not tmp_results:
        for each in applications:
            each['validity'] = None
            each['number_of_entries'] = 'single_entry'  # default
    else:
        first = 0
        count = 0
        for c, validity, number_of_entries in tmp_results:
            count += c
            for each in applications[first:first + c]:
                each['validity'] = validity
                each['number_of_entries'] = number_of_entries
            first = first + c

    logger.info('The raw data detected from the approval letter: \n %s', applications)

    # Example:
    # [{'confidence': 97.00606752053285,
    #   'date_of_birth': '04/07/1971 ',
    #   'gender': 'M',
    #   'nationality': 'United States',
    #   'number_of_entries': 'single entry',
    #   'passport_number': '*********',
    #   'name': 'Jay Keho Philip',
    #   'validity': ('25/12/2019', '25/01/2020')}]

    # Example:
    # {'confidence': 99.23187455397385,
    #  'date_of_birth': '10/08/1989',
    #  'gender': 'M',
    #  'nationality': 'China',
    #  'number_of_entries': 'single_entry',
    #  'passport_number': 'E30479189',
    #  'name': 'LEI DAI',
    #  'validity': '1M'}]

    new_applications = list()
    for each in applications:
        logger.info('Enhance the application %s', each)

        new = dict()

        new['confidence'] = each['confidence']
        new['gender'] = 'F' if 'female' in each.get('gender', '').lower() else 'M'
        new['name'] = each.get('name', '').strip().upper()
        new['passport_number'] = each.get('passport_number', '').strip()
        new['number_of_entries'] = each.get('number_of_entries', '').strip()
        new['nationality'] = each.get('nationality', '').strip()

        # Handle date_of_birth
        d_str = each.get('date_of_birth', '')
        if d_str:
            try:
                new['date_of_birth'] = datetime.strftime(
                    datetime.strptime(d_str.strip(), "%d/%m/%Y"),
                    "%Y-%m-%dT%H:%M:%SZ"
                )
            except Exception as exc:
                logger.warning(exc)
                new['date_of_birth'] = d_str
        else:
            new['date_of_birth'] = d_str

        # Handle validity
        if each.get('validity'):
            try:
                days = (datetime.strptime(each['validity'][1], "%d/%m/%Y") -
                        datetime.strptime(each['validity'][0], "%d/%m/%Y")).days
                if 29 < days < 32:
                    new['validity'] = '1M'
                elif 89 < days < 92:
                    new['validity'] = '3M'
                elif 179 < days < 182:
                    new['validity'] = '6M'
                elif 364 < days < 366:
                    new['validity'] = '1y'
                elif 365 * 5 - 2 < days < 365 * 5 + 2:
                    new['validity'] = '5y'
                else:
                    new['validity'] = '{}-{}'.format(each['validity'][0], each['validity'][1])
            except Exception as exc:
                logger.warning(exc)
                new['validity'] = each['validity']
        else:
            new['validity'] = '1M'  # default value

        # Removing empty entries
        if any([new['name'], new['passport_number'], new['nationality']]):
            new_applications.append(new)

    logger.info('The data after the enhancement: \n %s', new_applications)
    return new_applications


def local_test():
    # Note: Need update the aws.py profile_name
    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.INFO
    )
    aws_region = 'us-west-2'

    s3_bucket = 'ad-test-ai-service-us-west-2'
    s3_key = 'IEP.pdf'
    expected = [{}]
    run([(s3_bucket, s3_key)], expected, aws_region)
