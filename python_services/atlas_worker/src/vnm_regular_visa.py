import country_converter as coco
import logging
import re

from aws import AWS
from datetime import datetime
from trp import Document


SUCCESS = "SUCCESS"
FAILURE = "FAILURE"
OK = "OK"

COUNTRY_NOT_FOUND = 'COUNTRY NOT FOUND'

TO_VERIFY = (
    'name',
    # 'gender',
    'passport_number',
    'nationality',
    'date_of_birth',
    'number_of_entries',
    'validity'
)

logger = logging.getLogger(__name__)


def run(s3_bucket, s3_key, expected, aws_region):
    """
    :param s3_bucket:
    :param s3_key:
    :param expected:
    :param aws_region:
    :return:
    """
    visa_dict, confidences = run_all_analysis(s3_bucket, s3_key, aws_region)

    ret = dict()
    mismatch = list()
    reason_message = ''

    for k in TO_VERIFY:
        # Handle name
        if k == 'name':

            _surname = expected[0].pop('surname', '').lower()
            _given_name = expected[0].pop('given_name', '').lower()
            ok = visa_dict.get(k, '').lower() == ' '.join([_surname, _given_name]) or \
                 visa_dict.get(k, '').lower() == ' '.join([_given_name, _surname])
            if ok:
                expected[0]['name'] = visa_dict.get(k, '').upper()
            else:
                expected[0]['name'] = ' '.join([_given_name, _surname]).upper()
                mismatch.append(k)
                reason_message += 'Mismatch: {}: in application: {}; in approval letter: {} \n' \
                    .format(k, expected[0]['name'], visa_dict.get(k))

        elif expected[0].get(k, '').lower() != visa_dict.get(k, '').lower():
            mismatch.append(k)
            reason_message += 'Mismatch: {}: in application: {}; in visa: {} \n' \
                .format(k, expected[0].get(k), visa_dict.get(k))

    expected[0]['nationality'] = coco.convert(expected[0].get('nationality'), to='name_short',
                                              not_found=COUNTRY_NOT_FOUND)

    if mismatch:
        ret['status'] = FAILURE
        ret['reason'] = reason_message
    else:
        ret['status'] = SUCCESS
        ret['reason'] = OK

    ret['confidence'] = sum(confidences) / len(confidences) if confidences else 0

    ret['details'] = {
        'mismatch': mismatch,
        'application_submitted': expected[0],
        'ai_results': visa_dict
    }

    logger.info('Results: %s', ret)
    return ret


def run_all_analysis(s3_bucket, s3_key, aws_region):
    """
    :param s3_bucket:
    :param s3_key:
    :param aws_region:
    :return:
    """
    lines, form_fields, confidences = docu_analysis(s3_bucket, s3_key, aws_region)
    visa_dict = dict()

    visa_dict['name'] = fetch_name(form_fields)
    visa_dict['date_of_birth'] = fetch_dob(form_fields)
    visa_dict['nationality'], visa_dict['passport_number'] = fetch_passport_and_nationality(lines)
    visa_dict['validity'] = fetch_validity(lines)
    visa_dict['number_of_entries'] = fetch_number_of_entries(lines)

    logger.info('Got the visa info: %s', visa_dict)
    return visa_dict, confidences


def docu_analysis(s3_bucket, s3_key, aws_region):
    """
    :param s3_bucket:
    :param s3_key:
    :param aws_region:
    :return:
    """
    aws_client = AWS(aws_region)

    logger.info('Detect VNM Regular VISA %s/%s', s3_bucket, s3_key)
    response = aws_client.run_docu_analysis_job(s3_bucket, s3_key)
    logger.debug('Response: %s', response)

    lines = list()
    form_fields = list()
    confidences = list()

    doc = Document(response)

    for page in doc.pages:
        for line in page.lines:
            lines.append(line)
            confidences.append(line.confidence)
        for field in page.form.fields:
            form_fields.append(field)
            if field.key and field.value:
                confidences.append(field.key.confidence)
                confidences.append(field.value.confidence)
    return lines, form_fields, confidences


def fetch_name(form_fields):
    """
    :param form_fields:
    :return:
    """
    keys = ['Full name', 'HOTEN', 'TEN', 'Ful']
    for field in form_fields:
        for k in keys:
            s = field.key.text.replace('.', '')
            if k in s:
                logger.info('Found: field key: "%s", field value: "%s"', field.key, field.value)
                return field.value.text.strip().upper()
    return ''


def fetch_dob(form_fields):
    """
    :param form_fields:
    :return:
    """
    keys = ['Date of birth', 'Date', 'birth']
    try:
        for field in form_fields:
            for k in keys:
                s = field.key.text.replace('.', '')
                if k in s:
                    logger.info('Found: field key: "%s", field value: "%s"', field.key, field.value)
                    _dob = field.value.text.strip()
                    return datetime.strftime(datetime.strptime(_dob, "%d %b %Y"), '%Y-%m-%dT%H:%M:%SZ')
    except Exception as exc:
        logger.info('Error: %s', exc)
    return ''


def fetch_passport_and_nationality(lines):
    """
    :param lines:
    :return:
    """
    nationality = ''
    passport_number = ''

    pattern = re.compile(r'^[A-Z]{0,3}[0-9]{3,9}$')
    keys = ['Holding passport', 'Holding', 'country', 'territory']

    for position, line in enumerate(lines):
        got = False
        for k in keys:
            if k in line.text.replace('.', ''):
                got = True
                break
        if got:
            break

    target = lines[max(0, position - 5): position + 5]
    logger.info('Got the passport number and nationality info in the %s', [each.text for each in target])

    for each_line in target:
        new_str = each_line.text.replace('.', '').strip()
        pp = re.match(pattern, new_str)
        if pp:
            logger.info("Got the passport number info: %s", new_str)
            passport_number = pp.group()
            break

    got = False
    for each_line in target:
        if got: break

        new_str = each_line.text.replace('.', '').strip()
        if '/' in new_str:
            logger.info("Got the nationality info: %s", new_str)

            str_list = new_str.split()
            for each_str in str_list:
                if '/' not in each_str:
                    continue

                nationality = each_str.split('/')[0].strip()
                iso3_name = coco.convert(nationality, to='name_short', not_found=COUNTRY_NOT_FOUND)
                if iso3_name == COUNTRY_NOT_FOUND:
                    logger.info('Not the desired nationality: %s', nationality)
                    continue
                else:
                    got = True
                    break

    return nationality, passport_number


def fetch_validity(lines):
    """
    :param lines:
    :return:
    """
    pattern = re.compile(r"\.*([0-9]{2} [A-Z]{1}[a-z]{2} [0-9]{4})\.*")

    keys = ['Valid from', 'Valid', 'until']
    for position, line in enumerate(lines):
        got = False
        for k in keys:
            if k in line.text.replace('.', ''):
                got = True
                break
        if got:
            break

    lines_str = ' '.join([l.text.replace('.', '') for l in lines[max(position - 5, 0): position + 5]])
    logger.info('Got the visa validity info in the "%s"', lines_str)

    found = pattern.findall(lines_str)
    logger.info('Got the visa validation range: "%s"', found)

    if len(found) == 2:
        start, end = found[0].strip(), found[1].strip()
        try:
            days = (datetime.strptime(end, "%d %b %Y") - datetime.strptime(start, "%d %b %Y")).days
            if 29 < days < 32:
                validity = '1M'
            elif 89 < days < 92:
                validity = '3M'
            elif 179 < days < 182:
                validity = '6M'
            elif 364 < days < 366:
                validity = '1y'
            elif 365 * 5 - 2 < days < 365 * 5 + 2:
                validity = '5y'
            else:
                validity = '1y'
        except Exception as exc:
            logger.warning(exc)
            logger.warning('Set the default validity "1y"')
            validity = '1y'
    else:
        logger.warning('Set the default validity "1y"')
        validity = '1y'

    return validity


def fetch_number_of_entries(lines):
    """
    :param lines:
    :return:
    """
    keys = ['Single entry', 'Single', 'entry']

    lines_str = ' '.join([l.text.strip() for l in lines])
    for k in keys:
        if k in lines_str:
            return 'single_entry'

    return 'multiple_entries'


def local_test():
    # Note: Need update the aws.py profile_name
    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.INFO
    )
    aws_region = 'us-west-2'

    s3_bucket = 'ad-test-ai-service-us-west-2'
    s3_key = 'Dawn.jpg'
    expected = [{}]
    run(s3_bucket, s3_key, expected, aws_region)
