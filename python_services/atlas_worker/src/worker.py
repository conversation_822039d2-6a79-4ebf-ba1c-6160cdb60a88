import logging
import os
import tempfile
from re import T

from aws import AWS
from media_pipe import MediaPipe
from face_mask_detection import FaceMaskDetection

from settings import AiLabels, SUPPORTED_IMAGES

SUCCESS = "SUCCESS"
FAILURE = "FAILURE"
OK = "OK"
THREAD_HOLD = 20

logger = logging.getLogger(__name__)


class Worker(object):

    def __init__(self, aws_region='us-west-2'):
        """
        :param aws_region:
        """
        logger.info('AWS Region: %s', aws_region)
        self.aws_region = aws_region
        self.aws = AWS(aws_region)
        self.mp = MediaPipe()
        self.face_mask = FaceMaskDetection()

    def run(self, stype, inputs):
        """
        :param stype:
        :param inputs:
        :return:
        """
        # 901029: is passport
        # 908005: is DL
        # 909001: is photo
        # d909001: person face attributes
        # c10001: passport and photo
        # c10002: visa and photo
        # d10001: vnm voa approval letter
        # d10002: vnm native visa
        # d901029: fetch passport

        logger.info('Running service %s; Inputs: %s', stype, inputs)

        if stype == "901029":
            s3_bucket, s3_key = inputs['images'][0]['bucket'], inputs['images'][0]['key']
            expected = AiLabels.PASSPORT
            logger.info("S3_Bucket: %s; S3_Key: %s; Expected: %s",
                        s3_bucket, s3_key, expected)
            return self.detect_labels(s3_bucket, s3_key, expected)

        elif stype == "908005":
            s3_bucket, s3_key = inputs['images'][0]['bucket'], inputs['images'][0]['key']
            expected = AiLabels.DL
            logger.info("S3_Bucket: %s; S3_Key: %s; Expected: %s",
                        s3_bucket, s3_key, expected)
            return self.detect_labels(s3_bucket, s3_key, expected)

        elif stype == "909001":
            s3_bucket, s3_key = inputs['images'][0]['bucket'], inputs['images'][0]['key']

            logger.info("S3_Bucket: %s; S3_Key: %s", s3_bucket, s3_key)

            labels = self.detect_labels(s3_bucket, s3_key)
            labels_dict = dict((each['Name'], each['Confidence'])
                               for each in labels)
            logger.info('Detected labels: %s', labels_dict)

            min_confidence = 100
            if AiLabels.PERSON not in labels_dict and AiLabels.HUMAN not in labels_dict and AiLabels.FACE not in labels_dict:
                ret = {
                        'status': FAILURE,
                        'reason': 'Not detected as a valid photo',
                        'confidence': 95
                    }
                return ret
                
            ret = {
                'status': SUCCESS,
                'reason': OK,
                'confidence': min_confidence
            }
            return ret

        elif stype == "d909001":
            s3_bucket, s3_key = inputs['images'][0]['bucket'], inputs['images'][0]['key']
            level = inputs.get('level') or 'EASY'
            logger.info("S3_Bucket: %s; S3_Key: %s", s3_bucket, s3_key)
            return self.detect_faces(level, s3_bucket, s3_key)

        elif stype == "c10001":
            src_s3_bucket, src_s3_key = inputs['images'][0]['bucket'], inputs['images'][0]['key']
            tar_s3_bucket, tar_s3_key = inputs['images'][1]['bucket'], inputs['images'][1]['key']
            logger.info("First S3_Bucket: %s; First S3_Key %s",
                        src_s3_bucket, src_s3_key)
            logger.info("Second S3_Bucket: %s; Second S3_Key %s",
                        tar_s3_bucket, tar_s3_key)
            return self.compare_faces(src_s3_bucket, src_s3_key, tar_s3_bucket, tar_s3_key)

        elif stype == "c10002":
            src_s3_bucket, src_s3_key = inputs['images'][0]['bucket'], inputs['images'][0]['key']
            tar_s3_bucket, tar_s3_key = inputs['images'][1]['bucket'], inputs['images'][1]['key']
            logger.info("First S3_Bucket: %s; First S3_Key %s",
                        src_s3_bucket, src_s3_key)
            logger.info("Second S3_Bucket: %s; Second S3_Key %s",
                        tar_s3_bucket, tar_s3_key)
            return self.compare_faces(src_s3_bucket, src_s3_key, tar_s3_bucket, tar_s3_key)

        elif stype == "d10001":  # vnm voa approval letter
            approval_letters = [(each['bucket'], each['key'])
                                for each in inputs['images']]
            expected = inputs.get('expected')
            logger.info('Got %s VNM VOA approval letter(s): %s',
                        len(approval_letters), approval_letters)
            logger.info('%s application(s) is to verify %s',
                        len(expected), expected)

            import vnm_voa_approval_letter
            return vnm_voa_approval_letter.run(approval_letters, expected, self.aws_region)

        elif stype == "d10002":  # vnm native visa
            s3_bucket, s3_key = inputs['images'][0]['bucket'], inputs['images'][0]['key']
            expected = inputs.get('expected')
            logger.info('Got VNM native visa: %s, %s', s3_bucket, s3_key)
            logger.info('Application is to verify %s', expected)

            import vnm_native_visa
            return vnm_native_visa.run(s3_bucket, s3_key, expected, self.aws_region)

        elif stype == "d10003":  # vnm regular visa
            s3_bucket, s3_key = inputs['images'][0]['bucket'], inputs['images'][0]['key']
            expected = inputs.get('expected')
            logger.info('Got VNM regular visa: %s, %s', s3_bucket, s3_key)
            logger.info('Application is to verify %s', expected)

            import vnm_regular_visa
            return vnm_regular_visa.run(s3_bucket, s3_key, expected, self.aws_region)

        elif stype == "d901029":  # passport
            raise NotImplementedError

        else:
            logger.exception('Not supported service type %s', stype)

    def detect_labels(self, s3_bucket, s3_key, expected=None):
        """
        :param s3_bucket:
        :param s3_key:
        :param expected:
        :return:
        """
        ret = dict()

        if os.path.splitext(s3_key)[-1].lower() not in SUPPORTED_IMAGES:
            logger.exception('Not support image')

        response = self.aws.detect_labels(s3_bucket, s3_key)
        logger.info('Response:  %s', response)

        if expected is None:
            return response['Labels']

        else:
            for each in response['Labels']:
                if each.get('Name') == expected:
                    ret['status'] = SUCCESS
                    ret['reason'] = OK
                    ret['confidence'] = float(each.get('Confidence'))
                    break
            else:
                ret['status'] = FAILURE
                ret['reason'] = "Not match as {}".format(expected)
                ret['confidence'] = 100

            logger.info(ret)
            return ret

    def detect_faces(self, level, s3_bucket, s3_key):
        """
        :param s3_bucket:
        :param s3_key:
        :return:
        """
        ret = dict()

        if os.path.splitext(s3_key)[-1] not in SUPPORTED_IMAGES:
            logger.exception('Not Support Image')

        faces = self.aws.detect_faces(s3_bucket, s3_key)
        logger.info('Faces:  %s', faces)

        if len(faces) == 1:
            face = faces[0]

            ret['status'] = SUCCESS
            ret['reason'] = OK
            ret['confidence'] = face['Confidence']
            ret['details'] = dict()
            for attr in AiLabels.FACE_ATTRS:
                ret['details'][attr] = face[attr]

            ret['details']['HeadPose'] = {
                'turn_left': face['Pose']['Yaw'] < -THREAD_HOLD,
                'turn_right': face['Pose']['Yaw'] > THREAD_HOLD,
                'look_down': face['Pose']['Pitch'] < -THREAD_HOLD,
                'look_up':  face['Pose']['Pitch'] > THREAD_HOLD,
                'tilt_left':  face['Pose']['Roll'] < -THREAD_HOLD,
                'tilt_right':  face['Pose']['Roll'] > THREAD_HOLD,
                'eye_closed': not face['EyesOpen']['Value'],
                'mouth_open': face['MouthOpen']['Value'],
            }

            ret['details']['Emotions'] = {}
            for emotion in face['Emotions']:
                if emotion['Type'] == 'HAPPY' or emotion['Type'] == 'SAD':
                    ret['details']['Emotions'][emotion['Type']
                                               ] = emotion['Confidence'] > 90

            temp_file = tempfile.NamedTemporaryFile(delete=True)
            self.aws.s3_client.download_file(s3_bucket, s3_key, temp_file.name)
            human_pose = self.mp.detect_pose(temp_file.name)

            face_mask_result = self.face_mask.detect(temp_file.name)
            # face_mask_result = False
            shoulder_ratio = 0
            if human_pose is not None:
                shoulder_ratio = abs(human_pose["left_shoulder"].y - human_pose["right_shoulder"].y) / abs(
                    human_pose["left_shoulder"].x - human_pose["right_shoulder"].x)

            CONFIG_LEVEL = {
                "EASY": {
                    "TOO_FAR": 0.2,
                    "TOO_CLOSE": 0.8,
                    "TOO_BUR": 50,
                    "TOO_DARK": 75,
                    "SHOULDER_RATIO": 0.2
                },
                "HARD": {
                    "TOO_FAR": 0.3,
                    "TOO_CLOSE": 0.7,
                    "TOO_BUR": 55,
                    "TOO_DARK": 80,
                    "SHOULDER_RATIO": 0.1
                },
            }.get(level)


            ret['details']['HeadFrame'] = {
                'too_far':  face['BoundingBox']['Width'] < CONFIG_LEVEL["TOO_FAR"] or face['BoundingBox']['Height'] < CONFIG_LEVEL["TOO_FAR"],
                'too_close': face['BoundingBox']['Width'] > CONFIG_LEVEL["TOO_CLOSE"]  or face['BoundingBox']['Height'] > CONFIG_LEVEL["TOO_CLOSE"] ,
                'too_blur': face['Quality']['Sharpness'] < CONFIG_LEVEL["TOO_BUR"],
                'too_dark':  face['Quality']['Brightness'] < CONFIG_LEVEL["TOO_DARK"],
                'eye_glass': (face['Eyeglasses']['Value'] and face['Eyeglasses']['Confidence'] > 90) or (face['Sunglasses']['Value'] and face['Sunglasses']['Confidence'] > 90),
                'wear_hat': False,
                'face_mask': bool(face_mask_result),
            }

            aws_label_detect = self.aws.detect_labels(s3_bucket, s3_key)
            for attr in aws_label_detect['Labels']:
                if attr['Name'] in ['Sun Hat' , 'Hat', 'Helmet', 'Cap', 'Hardhat']:  #  'Hoodie' Arab woman skip
                    ret['details']['HeadFrame']['wear_hat'] = True

            ret['details']['BodyFrame'] = {
                'shoulder_tilt':  shoulder_ratio > CONFIG_LEVEL["SHOULDER_RATIO"]
            }

            ret['details']['CheckList'] = {
                'look_straight_ahead': True,
                'no_shadow': True,
                'balance_shoulder': True,
                'face_not_covered': True,
                'face_distance': True,
                'not_blur': True,
                'neutral_face': True
            }

            for attr in ['turn_left', 'turn_right', 'look_down', 'look_up', 'tilt_left', 'tilt_right']:
                if ret['details']['HeadPose'][attr] is True:
                    ret['details']['CheckList']['look_straight_ahead'] = False

            if ret['details']['HeadFrame']['too_far'] or ret['details']['HeadFrame']['too_close']:
                ret['details']['CheckList']['face_distance'] = False

            ret['details']['CheckList']['no_shadow'] = not ret['details']['HeadFrame']['too_dark']

            ret['details']['CheckList']['face_not_covered'] = (
                not ret['details']['HeadFrame']['eye_glass']) and (not ret['details']['HeadFrame']['wear_hat']) and (not ret['details']['HeadFrame']['face_mask']) 

            ret['details']['CheckList']['not_blur'] = not ret['details']['HeadFrame']['too_blur']

            ret['details']['CheckList']['balance_shoulder'] = not ret['details']['BodyFrame']['shoulder_tilt']

            ret['details']['CheckList']['neutral_face'] = ret['details']['Emotions']['HAPPY'] == False and ret['details']['Emotions']['SAD'] == False and ret['details']['HeadPose']['eye_closed'] == False and ret['details']['HeadPose']['mouth_open'] == False

            for attr in ret['details']['CheckList']:
                if not ret['details']['CheckList'][attr]:
                    ret['status'] = FAILURE
                    ret['reason'] = "{} is false".format(attr)
                    ret['confidence'] = 100
        else:
            ret['status'] = FAILURE
            ret['confidence'] = 100

            if len(faces) > 1:
                ret['reason'] = 'More than one human face detected'
            elif len(faces) == 0:
                ret['reason'] = 'No human face detected'

        logger.info(ret)
        return ret

    def compare_faces(self, src_s3_bucket, src_s3_key, tar_s3_bucket, tar_s3_key):
        """
        :param src_s3_bucket:
        :param src_s3_key:
        :param tar_s3_bucket:
        :param tar_s3_key:
        :return:
        """
        ret = dict()

        response = self.aws.compare_faces(
            src_s3_bucket, src_s3_key, tar_s3_bucket, tar_s3_key)
        logger.info('Response:  %s', response)

        matched = response.get('FaceMatches')
        unmatched = response.get('UnmatchedFaces')

        if matched:
            ret['status'] = SUCCESS
            ret['reason'] = OK
            ret['confidence'] = matched[0]['Similarity']
        else:
            ret['status'] = FAILURE
            ret['reason'] = 'Human faces do not match'
            ret['confidence'] = unmatched[0]['Confidence']

        return ret
