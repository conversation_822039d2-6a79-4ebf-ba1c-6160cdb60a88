#!/usr/bin/env python

# -*- coding: utf-8 -*-

import logging
import requests

REQUESTS_GET = 'get'
REQUESTS_POST = 'post'
REQUESTS_PUT = 'put'
REQUESTS_DELETE = 'delete'

logger = logging.getLogger('core.xrequests')


class XRequests(object):
    """Requests Wrapper."""

    def __init__(self, endpoint, timeout=30):
        """
        :param endpoint:
        :param timeout:
        """
        self._endpoint = endpoint
        self._timeout = timeout
        self._session = requests.Session()

    def __enter__(self):
        """
        :return:
        """
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        """
        :param exc_type:
        :param exc_value:
        :param traceback:
        :return:
        """
        self._session.close()

    def _requests(self, method, path=None, params=None, data=None, session=None, timeout=None,
                  headers=None, ignore_not_found=False, value_only=True, raise_exception=True):
        """
        :param method:
        :param path:
        :param params:
        :param data:
        :param session:
        :param timeout:
        :param headers:
        :param ignore_not_found:
        :param value_only:
        :param raise_exception:
        :return:
        """
        session = session if session else self._session
        timeout = timeout if timeout else self._timeout

        session.headers.update({'Content-Type': 'application/json'})
        if headers:
            session.headers.update(headers)

        if path:
            url = "/".join((self._endpoint, path))
        else:
            url = self._endpoint

        logger.debug('Method: %s; URL: %s; Params: %s; Headers: %s; Data: %s',
                     method, url, params, headers, data)

        if method == REQUESTS_GET:
            response = session.get(url, params=params, timeout=timeout)
        elif method == REQUESTS_PUT:
            response = session.put(url, json=data, timeout=timeout)
        elif method == REQUESTS_POST:
            response = session.post(url, json=data, timeout=timeout)
        elif method == REQUESTS_DELETE:
            response = session.delete(url, timeout=timeout)
            if response.status_code is requests.codes.not_found and ignore_not_found:
                response.status_code = requests.codes.no_content
        else:
            raise NotImplementedError

        # Raise exception if status code indicates a failure
        if 400 <= response.status_code <= 500:
            logger.error('Request failed (status: %s, reason: %s)',
                         response.status_code, response.text)
        if raise_exception:
            response.raise_for_status()

        logger.info('Get response %s (%d): %s',
                     'succeeded' if response.ok else 'failed',
                     response.status_code,
                     response.text)

        if value_only:
            return self._parse_response(response)
        else:
            return response

    @staticmethod
    def _parse_response(response):
        """Parse response.
        :param response:
        :return:
        """
        if 'application/json' not in response.headers['Content-Type'].lower():
            return response.text
        else:
            return response.json()

    def get(self, path=None, params=None, **kwargs):
        """Get.

        :param path:
        :param params:
        :param kwargs:
        :return:
        """
        return self._requests(REQUESTS_GET, path=path, params=params, **kwargs)

    def post(self, path=None, data=None, **kwargs):
        """Post.

        :param path:
        :param data:
        :param kwargs:
        :return:
        """
        return self._requests(REQUESTS_POST, path=path, data=data, **kwargs)

    def put(self, path=None, data=None, **kwargs):
        """Put.

        :param path:
        :param data:
        """
        return self._requests(REQUESTS_PUT, path=path, data=data, **kwargs)

    def delete(self, path, **kwargs):
        """Delete.

        :param path:
        :param kwargs:
        :return:
        """
        return self._requests(REQUESTS_DELETE, path=path, **kwargs)
