import cv2
import mediapipe as mp
import numpy as np
import urllib3
import requests

mp_pose = mp.solutions.pose
mpDraw = mp.solutions.drawing_utils

# https://google.github.io/mediapipe/solutions/pose.html
pose = mp_pose.Pose(
            static_image_mode=True,
            # model_complexity=2,
            # enable_segmentation=True,
            min_detection_confidence=0.5)
class MediaPipe(object):
    def detect_pose(self, file_path):
        image = cv2.imread(file_path)
        # Convert the BGR image to RGB before processing.
        results = pose.process(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))

        if not results.pose_landmarks:
            return None
        return {
            "left_shoulder": results.pose_landmarks.landmark[11],
            "right_shoulder": results.pose_landmarks.landmark[12],
        }
