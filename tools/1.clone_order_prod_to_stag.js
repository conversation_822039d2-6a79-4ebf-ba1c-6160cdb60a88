const { Sequelize, DataTypes } = require('sequelize');
const axios = require('axios');

// Initialize Sequelize with PostgreSQL database connection
const sequelize = new Sequelize('ariadirect_stag', 'aria', 'ariadirect2020', {
    host: 'localhost',
    port: '5432',
    dialect: 'postgres',
    logging: false,
});

const get_order_detail = async (order_id) => {
    const resp = await axios({
        method: 'GET',
        url: `https://api.ariadirect.com/v1/pkg/internal/orders/${order_id}`,
        headers: {
            'x-ad-token': "2hNwPul_SppFM_iUlV1Wng",
            'Content-Type': 'application/json',
        },
        data: body
    })
    return resp.data
}

(async () => {
    await sequelize.authenticate();
    const order = await get_order_detail('7339')
    const query = `
    INSERT INTO orders (id, orderDetail, taskDetail)
    VALUES (:id, :orderDetail, :taskDetail)
    ON CONFLICT (id) 
    DO UPDATE SET orderDetail = EXCLUDED.orderDetail, taskDetail = EXCLUDED.taskDetail
    `;

    // Execute the raw SQL insert query
    const order = await sequelize.query(query, {
        replacements: {
            id: order_id,
            orderDetail: JSON.stringify(orderDetail),
            taskDetail: JSON.stringify(taskDetail),
        },
        type: sequelize.QueryTypes.INSERT,
    });
    console.log('DONE')
})();