#!/bin/bash

# Test WhatsApp invite users API
echo "Testing WhatsApp invite users API..."

# Get a group ID first
GROUP_ID=$(curl -s -X GET "http://localhost:8080/api/whatsapp/group/list" | jq -r '.data.groups[0].id')

if [ -z "$GROUP_ID" ]; then
  echo "No groups found. Please create a group first."
  exit 1
fi

echo "Using group ID: $GROUP_ID"

# Test inviting users to the group
curl -X POST "http://localhost:8080/api/whatsapp/group/invite_users" \
  -H "Content-Type: application/json" \
  -d "{
    \"group_id\": \"$GROUP_ID\",
    \"phone_numbers\": [\"+84123456789\"],
    \"send_invite\": true,
    \"invite_message\": \"You are invited to join our WhatsApp group!\"
  }"

echo -e "\n\nTest completed."
