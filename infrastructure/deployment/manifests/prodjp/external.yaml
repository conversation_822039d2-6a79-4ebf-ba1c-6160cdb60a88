---
external_services:
  services:
    ##################
    #    With path   #
    ##################
    atlas:
      desired_count: 1
      image_name: atlas-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./atlas,--account-service-config,ad_user_account_service,--ad_secrets,ad_secrets,--callback-host,api.jp.ariadirect.com
      service_path: atlas
      service_priority: 1

    internals:
      desired_count: 1
      image_name: internals
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./internals,i,--ad_sqs,ad_sqs,--ad_aws,ad_aws
      service_path: int
      service_priority: 2

    package:
      desired_count: 1
      image_name: package-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./packages,svc,--db-config,ad_db,--s3-bucket-config,ad_s3,--sqs-config,ad_sqs,--conf-file,conf/packages.yaml,--localize-file,conf/localize.yaml,--client-secret,aaa,--internal-auth-service-host,bbbb,--service-config,ad_package_service,--account-service-config,ad_user_account_service,--ad_secrets,ad_secrets,--ad_api_token,ad_api_token,--ad_es,ad_es,--ad_onepay,ad_onepay,--ad_zellepay,ad_zellepay,--ad_packer_service,ad_packer_service,--ad_email,ad_email,--ad_endpoint,ad_endpoint,--bypass-auth,true
      service_path: pkg
      service_priority: 3

    packer:
      desired_count: 1
      image_name: packer-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./packer,p,--ad_db,ad_db,--ad_aws,ad_aws,--ad_sqs,ad_sqs,--ad_s3,ad_s3,--ad_packer_service,ad_packer_service,--ad_user_account_service,ad_user_account_service,--ad_secrets,ad_secrets,--ad_api_token,ad_api_token
      service_path: packer
      service_priority: 24

    passport-mrz-parser-service:
      desired_count: 1
      image_name: passport-mrz-parser-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: npm,run,start
      service_path: pp-mrz
      service_priority: 10

    payment:
      desired_count: 1
      image_name: payment-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./payments,svc,--db-config,ad_db,--use-wechat-pay-prod,--conf-file,conf/payments.yaml,--service-config,ad_payment_service,--account-service-config,ad_user_account_service,--ad_secrets,ad_secrets,--ad_website,ad_website,--ad_authorize,ad_authorize,--ad_wechatpay,ad_wechatpay,--ad_onepay,ad_onepay,--ad_paypal,ad_paypal,--ad_zellepay,ad_zellepay,--ad_stripe,ad_stripe,--ad_email,ad_email,--sqs-config,ad_sqs,--s3-region,us-west-2,--version-file-bucket,ad-app-version,--currency-config-file,/app-payment-config/payment-config-by-currency.json,--region-payment-config-file,/app-payment-config/default-curency-by-country.json
      service_path: payment
      service_priority: 5

    search:
      desired_count: 1
      image_name: search-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./search,s,--ad_es,ad_es,--ad_aws,ad_aws,--ad_user_account_service,ad_user_account_service,--ad_secrets,ad_secrets
      service_path: search-svc
      service_priority: 6

    user-management:
      desired_count: 1
      image_name: user-management-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: "./user-authentication"
      service_path: ums
      service_priority: 7

    versions:
      desired_count: 1
      image_name: app-version-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./app_version,svc,--version-file-bucket,ad-app-version,--version-file-key,app-versions/app-versions.json,--s3-region,us-west-2
      service_path: ad-app-versions
      service_priority: 8

    master-data:
      desired_count: 1
      image_name: master-data-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./master_data,svc,--db-config,ad_db,--account-service-config,ad_user_account_service,--ad_secrets,ad_secrets
      service_path: master-data
      service_priority: 9

    shipment:
      desired_count: 1
      image_name: shipment-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./shipments,service,--db-config,ad_db
      service_path: shipment
      service_priority: 12

    mrz-parser:
      desired_count: 1
      image_name: mrz-parser-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./mrz_parser,--db-config,ad_db,--ad_endpoint,ad_endpoint
      service_path: mrz-parser
      health_check_endpoint: /version
      service_priority: 13
    
    passport-photo:
      desired_count: 1
      image_name: passport-photo-service
      cpu_reservation: 0
      memory_reservation: 512
      task_command: npm,run,start
      service_path: passport-photo
      health_check_endpoint: /version
      service_priority: 15

    helper:
      desired_count: 1
      image_name: helper-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: npm,run,start
      service_path: helper-service
      health_check_endpoint: /version
      service_priority: 16
    
    id-reader:
      desired_count: 1
      image_name: id-reader
      cpu_reservation: 0
      memory_reservation: 512
      task_command:  flask,run,-p,3000,-h,0.0.0.0
      service_path: id-reader
      health_check_endpoint: /version
      service_priority: 17

    devices:
      desired_count: 1
      image_name: devices-service
      cpu_reservation: 0
      memory_reservation: 512
      task_command: ./devices,svc,--ad_firebase,ad_firebase,--db-config,ad_db,--ad_secrets,ad_secrets,--account-service-config,ad_user_account_service,--ad_api_token,ad_api_token
      service_path: device_tokens
      health_check_endpoint: /version
      service_priority: 19
    
    album:
      desired_count: 1
      image_name: album-service
      cpu_reservation: 0
      memory_reservation: 512
      task_command: ./album-service,svc,--db-config,ad_db,--account-service-config,ad_user_account_service,--ad_secrets,ad_secrets,--s3-bucket-config,ad_s3,--ad_aws,ad_aws
      service_path: album-photo
      health_check_endpoint: /version
      service_priority: 21
    
    third-party:
      desired_count: 1
      image_name: third-party-service
      cpu_reservation: 0
      memory_reservation: 128
      task_command: npm,run,start
      service_path: third-party-service
      health_check_endpoint: /version
      service_priority: 22

    logger:
      desired_count: 1
      image_name: logger-service
      cpu_reservation: 0
      memory_reservation: 128
      task_command: ./logger,svc,--db-config,ad_db
      service_path: logger
      health_check_endpoint: /version
      service_priority: 23

    ##################
    #  Without path  #
    ##################

    atlas-worker:
      desired_count: 1
      image_name: atlas-worker-service
      cpu_reservation: 0
      memory_reservation: 512
      task_command: python,atlas.py

    carrier:
      desired_count: 1
      image_name: carrier-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./carrier,s,--ad_sqs,ad_sqs,--ad_aws,ad_aws,--ad_s3,ad_s3,--ad_fedex,ad_fedex,--ad_api_token,ad_api_token

    packer-offline:
      desired_count: 1
      image_name: packer-offline-service
      cpu_reservation: 0
      memory_reservation: 256
      task_command: npm,run,start

    packer-online:
      desired_count: 1
      image_name: packer-online-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: npm,run,start

    packer-online-captcha:
      desired_count: 1
      image_name: packer-online-captcha-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: npm,run,start

    notification:
      desired_count: 1
      image_name: notification-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./notification,n,--ad_sqs,ad_sqs,--ad_aws,ad_aws,--ad_db,ad_db,--ad_s3,ad_s3,--ad_ses,ad_ses,--ad_website,ad_website,--ad_endpoint,ad_endpoint,--ad_api_token,ad_api_token

    notification-user:
      desired_count: 1
      image_name: notification-user-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./notification-user,n,--ad_sqs,ad_sqs,--ad_aws,ad_aws,--ad_email,ad_email,--ad_website,ad_website,--ad_api_token,ad_api_token,--localize-file,conf/localize.yaml

    submit-email-worker:
      desired_count: 1
      image_name: submit-email-worker-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./submit-email-worker,s,--ad_sqs,ad_sqs,--ad_aws,ad_aws,--ad_s3,ad_s3,--ad_submit_services,ad_submit_services,--localize-file,conf/localize.yaml,--ad_db,ad_db,--ad_ses,ad_ses,--ad_email,ad_email,--ad_website,ad_website,--ad_api_token,ad_api_token

    watchdog:
      desired_count: 1
      image_name: watchdog-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./watchdog,svc,--db-config,ad_db,--service-config,ad_watchdog_service,--ad_secrets,ad_secrets,--sqs-config,ad_sqs,--ad_website,ad_website,--ad_email,ad_email,--ad-s3,ad_s3,--ad_fedex,ad_fedex

    submit-dispatcher:
      desired_count: 1
      image_name: submit-dispatcher-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./submit-dispatcher,s,--ad_db,ad_db,--ad_sqs,ad_sqs,--ad_aws,ad_aws,--ad_package_service,ad_package_service,--ad_s3,ad_s3,--ad_email,ad_email,--ad_website,ad_website,--localize-file,conf/localize.yaml

    email-hook:
      desired_count: 1
      image_name: email-hook-service
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./email-hook,svc


    #######################
    #  Scheduled service  #
    #######################

    checker:
      state: "absent"
      type: scheduled
      desired_count: 1
      cron_expression: cron(0 23 ? * SUN *)
      image_name: checker
      cpu_reservation: 0
      memory_reservation: 256
      task_command: python,checker.py

    payment-worker:
      type: scheduled
      desired_count: 1
      cron_expression: cron(0/2 * * * ? *)
      image_name: payment-worker
      cpu_reservation: 0
      memory_reservation: 64
      task_command: ./payment_worker,svc,--db-config,ad_db,--use-wechat-pay-prod,--use-authorize-prod,--conf-file,conf/payments.yaml,--ad_authorize,ad_authorize,--ad_wechatpay,ad_wechatpay,--ad_onepay,ad_onepay
