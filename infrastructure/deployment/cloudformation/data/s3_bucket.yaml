AWSTemplateFormatVersion: "2010-09-09"
Description: Generic template for S3 bucket
Parameters:
  Env:
    Description: AWS environment name
    Type: String
    AllowedValues:
      - produs
      - prodjp
      - stagingus
  S3BucketName:
    Description: S3 bucket name
    Type: String
  VersioningEnabled:
    Description: Enable object versioning
    Type: String
    Default: Suspended
    AllowedValues:
      - Enabled
      - Suspended

Resources:
  S3Bucket1:
    Type: "AWS::S3::Bucket"
    Properties:
      AccessControl: "BucketOwnerFullControl"
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      BucketName: !Ref S3BucketName
      VersioningConfiguration:
        Status: !Ref VersioningEnabled
      CorsConfiguration:
        CorsRules:
          - AllowedMethods:
              - GET
              - PUT
              - POST
            AllowedOrigins:
              - "*"
            AllowedHeaders:
              - Authorization
              - Origin
              - Content-Type
            ExposedHeaders:
              - x-amz-server-side-encryption
              - x-amz-request-id
              - x-amz-id-2
            MaxAge: 3000
      LifecycleConfiguration:
        Rules:
        - Id: DeleteMultipartAfter7Days
          AbortIncompleteMultipartUpload:
            DaysAfterInitiation: 7
          Status: Enabled
        - Id: DeleteVersionAfter180Days
          NoncurrentVersionExpirationInDays: 180
          Status: Enabled
        - Id: DeleteContentAfter180Days
          Prefix: ""
          Status: Disabled
          ExpirationInDays: 180
      Tags:
      - Key: Env
        Value: !Ref Env
