AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation Template to create Aurora Postgresql Cluster DB Instance
###############################################################################
# Parameter groups
###############################################################################

Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
      -
        Label:
          default: Environment
        Parameters:
          - AvailabilityZone
          - DBSubnets
          - DNSDomain
          - Env
          - PrivateHostedZone
          - PublicHostedZone
          - VpcCIDR
          - VpcID
      -
        Label:
          default: DB Parameters
        Parameters:
          - DBBackupRetention
          - DBBackupWindow
          - DBEngine
          - DBEngineVersion
          - DBInstanceClass
          - DBName
          - DBPassword
          - DBPort
          - DBMaintenanceWindow
          - DBSnapshotName
          - DBUsername
          - IsReplica

###############################################################################
# Parameters
###############################################################################

Parameters:
  AvailabilityZone:
    Description: AWS AvailabilityZone for RDS instance to work in
    Type: "AWS::EC2::AvailabilityZone::Name"
  DBSubnets:
    Description: Private Subnets in which DB instance will be placed
    Type: 'List<AWS::EC2::Subnet::Id>'
  DNSDomain:
    Description: Domain name
    Type: String
  DNSName:
    Description: DNS Name
    Type: String
  Env:
    Description: The environment tag is used to designate the Environment Stage of the associated AWS resource.
    Type: String
    Default: produs
    AllowedValues:
      - produs
      - prodjp
      - stagingus
  PrivateHostedZone:
    Description: Select private hosted zone
    Type: AWS::Route53::HostedZone::Id
  PublicHostedZone:
    Description: Select public hosted zone
    Type: AWS::Route53::HostedZone::Id
  VpcCIDR:
    Description: Current VPC CIDR
    Type: String
    MinLength: 9
    MaxLength: 18
    AllowedPattern: "(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})/(\\d{1,2})"
  VpcID:
    Description: VPC ID.
    Type: "AWS::EC2::VPC::Id"
###############################################################################
  DBBackupRetention:
    Description: Backup retention policy for Audit instance
    Type: Number
    Default: 7
  DBBackupWindow:
    Description: Prefered backup window
    Type: String
    Default: "03:16-03:46"
  DBEngine:
    Description: The RDS engine to use for earth instance
    Type: String
    Default: aurora-postgresql
  DBEngineVersion:
    Description: Select Database Engine Version
    Type: String
    AllowedValues:
      - 9.6.8
      - 9.6.9
      - 10.4
      - 10.5
  DBInstanceClass:
    Description: Database Instance Class
    Type: String
    AllowedValues:
    - db.m4.large
    - db.m4.xlarge
    - db.r4.large
    - db.r4.xlarge
    - db.r4.2xlarge
    - db.r4.4xlarge
    - db.r4.8xlarge
    - db.r4.16xlarge
  DBName:
    Description: Database Name
    Type: String
    MinLength: '1'
    MaxLength: '64'
    AllowedPattern: '^[a-zA-Z]+[0-9a-zA-Z_]*$'
    ConstraintDescription: Must start with a letter. Only numbers, letters, and _ accepted. max length 64 characters
  DBPassword:
    Description: Password for DB
    Type: String
    NoEcho: True
    MinLength: 8
  DBPort:
    Description: TCP/IP Port for the Database Instance
    Type: Number
    ConstraintDescription: 'Must be in the range [1115-65535]'
    MinValue: 1115
    MaxValue: 65535
  DBMaintenanceWindow:
    Description: Prefered maintenance window
    Type: String
    Default: "fri:07:00-fri:08:00"
  DBSnapshotName:
    Description: Optional. DB Snapshot ID to restore database. Leave this blank if you are not restoring from a snapshot.
    Type: String
    Default: ''
  DBUsername:
    Description: Database master username
    Type: String
    Default: 'aria'
    MinLength: '1'
    MaxLength: '16'
    AllowedPattern: '^[a-zA-Z]+[0-9a-zA-Z_]*$'
    ConstraintDescription: Must start with a letter. Only numbers, letters, and _ accepted. max length 16 characters
  IsReplica:
    Description: whether to enable replica
    Type: String
    Default: false
    AllowedValues:
    - true
    - false

###############################################################################
# Mappings
###############################################################################

Mappings:
  DBFamilyMap:
    '9.6.8':
      'family': 'aurora-postgresql9.6'
    '9.6.9':
      'family': 'aurora-postgresql9.6'
    '10.4':
      'family': 'aurora-postgresql10'
    '10.5':
      'family': 'aurora-postgresql10'

###############################################################################
# Conditions
###############################################################################
Conditions:
  IsUseDBSnapshot: !Not [!Equals [!Ref DBSnapshotName, '']]
  IsNotUseDBSnapshot: !Not [Condition: IsUseDBSnapshot]
  IsReplica: !Equals [!Ref IsReplica, 'true']
  DoEnableIAM: !Not [!Equals [!Ref DBEngineVersion, '9.6.8']]

###############################################################################
# Resources
###############################################################################

Resources:

  DBSubnetGroup:
    Type: 'AWS::RDS::DBSubnetGroup'
    Properties:
      DBSubnetGroupDescription: !Ref AWS::StackName
      SubnetIds: !Ref DBSubnets

  DBParamGroup:
    Type: AWS::RDS::DBParameterGroup
    Properties:
      Description: !Join [ '- ', [ 'Aurora PG Database Instance Parameter Group for Cloudformation Stack ', !Ref DBName ] ]
      Family: !FindInMap [DBFamilyMap, !Ref DBEngineVersion, 'family']
      Parameters:
        shared_preload_libraries: auto_explain,pg_stat_statements,pg_hint_plan,pgaudit
        log_statement: 'ddl'
        log_connections: 1
        log_disconnections: 1
        log_lock_waits: 1
        log_min_duration_statement: 5000
        auto_explain.log_min_duration: 5000
        auto_explain.log_verbose: 1
        log_rotation_age: 1440
        log_rotation_size: 102400
        rds.log_retention_period: 10080
        random_page_cost: 1
        track_activity_query_size: 16384
        idle_in_transaction_session_timeout: 7200000
        statement_timeout: 7200000
        search_path: '"$user",public'

  ClusterSecurityGroup:
    Type: 'AWS::EC2::SecurityGroup'
    Properties:
      GroupDescription: !Ref 'AWS::StackName'
      VpcId: !Ref VpcID

  ClusterSecurityGroupIngress1:
    Type: 'AWS::EC2::SecurityGroupIngress'
    Properties:
      GroupId: !GetAtt 'ClusterSecurityGroup.GroupId'
      IpProtocol: -1
      SourceSecurityGroupId: !GetAtt ClusterSecurityGroup.GroupId
      Description: 'Self Reference'

  ClusterSecurityGroupIngress2:
    Type: 'AWS::EC2::SecurityGroupIngress'
    Properties:
      GroupId: !GetAtt 'ClusterSecurityGroup.GroupId'
      IpProtocol: tcp
      FromPort: !Ref DBPort
      ToPort: !Ref DBPort
      CidrIp: !Ref VpcCIDR
      Description: 'Self VPC CIDR'

  RDSDBClusterParameterGroup:
    Type: AWS::RDS::DBClusterParameterGroup
    Properties:
      Description: !Join [ '- ', [ 'Aurora PG Cluster Parameter Group for Cloudformation Stack ', !Ref DBName ] ]
      Family: !FindInMap [DBFamilyMap, !Ref DBEngineVersion, 'family']
      Parameters:
        rds.force_ssl: 1

  AuroraDBCluster:
    Type: AWS::RDS::DBCluster
    DeletionPolicy: Snapshot
    UpdateReplacePolicy: Snapshot
    Properties:
      BackupRetentionPeriod: !Ref DBBackupRetention
      DatabaseName: !If [IsUseDBSnapshot, !Ref 'AWS::NoValue', !Ref DBName]
      DBClusterParameterGroupName: !Ref RDSDBClusterParameterGroup
      DBSubnetGroupName: !Ref DBSubnetGroup
      Engine: !Ref DBEngine
      EngineVersion: !Ref DBEngineVersion
      MasterUsername:
        !If [IsUseDBSnapshot, !Ref 'AWS::NoValue', !Ref DBUsername]
      MasterUserPassword:
        !If [IsUseDBSnapshot, !Ref 'AWS::NoValue', !Ref DBPassword]
      Port: !Ref DBPort
      PreferredBackupWindow: !Ref DBBackupWindow
      PreferredMaintenanceWindow: !Ref DBMaintenanceWindow
      SnapshotIdentifier: !If [IsUseDBSnapshot, !Ref DBSnapshotName, !Ref 'AWS::NoValue']
      VpcSecurityGroupIds:
      - !Ref ClusterSecurityGroup

  AuroraDBFirstInstance:
    Type: AWS::RDS::DBInstance
    Properties:
      AvailabilityZone: !Ref AvailabilityZone
      CopyTagsToSnapshot: true
      DBInstanceClass:
        Ref: DBInstanceClass
      DBClusterIdentifier: !Ref AuroraDBCluster
      Engine: !Ref DBEngine
      EngineVersion: !Ref DBEngineVersion
      DBParameterGroupName:
        Ref: DBParamGroup
      MonitoringInterval: 1
      MonitoringRoleArn: !GetAtt MonitoringIAMRole.Arn
      AutoMinorVersionUpgrade: false
      DBSubnetGroupName: !Ref DBSubnetGroup
      PubliclyAccessible: false
      EnablePerformanceInsights: true
      PerformanceInsightsRetentionPeriod: 7

  AuroraDBSecondInstance:
    Condition: IsReplica
    Type: AWS::RDS::DBInstance
    DependsOn:
      - AuroraDBFirstInstance
    Properties:
      AvailabilityZone: !Ref AvailabilityZone
      CopyTagsToSnapshot: true
      DBInstanceClass:
        Ref: DBInstanceClass
      DBClusterIdentifier: !Ref AuroraDBCluster
      Engine: !Ref DBEngine
      EngineVersion: !Ref DBEngineVersion
      DBParameterGroupName:
        Ref: DBParamGroup
      MonitoringInterval: 1
      MonitoringRoleArn: !GetAtt MonitoringIAMRole.Arn
      AutoMinorVersionUpgrade: false
      DBSubnetGroupName: !Ref DBSubnetGroup
      PubliclyAccessible: false
      EnablePerformanceInsights: true
      PerformanceInsightsRetentionPeriod: 7
      SourceDBInstanceIdentifier: !Ref AuroraDBFirstInstance

###############################################################################
# Monitor
###############################################################################

  MonitoringIAMRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Principal:
              Service:
                - "monitoring.rds.amazonaws.com"
            Action:
              - "sts:AssumeRole"
      Path: "/"
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole

  CPUUtilizationAlarm1:
    Type: 'AWS::CloudWatch::Alarm'
    Properties:
      ActionsEnabled: true
      AlarmActions:
      - Ref: DBSNSTopic
      AlarmDescription: 'CPU_Utilization'
      Dimensions:
      - Name: DBInstanceIdentifier
        Value:
          Ref: AuroraDBFirstInstance
      MetricName: CPUUtilization
      Statistic: Maximum
      Namespace: 'AWS/RDS'
      Threshold: '80'
      Unit: Percent
      ComparisonOperator: 'GreaterThanOrEqualToThreshold'
      Period: '60'
      EvaluationPeriods: '5'
      TreatMissingData: 'notBreaching'

  CPUUtilizationAlarm2:
    Condition: IsReplica
    Type: 'AWS::CloudWatch::Alarm'
    Properties:
      ActionsEnabled: true
      AlarmActions:
      - Ref: DBSNSTopic
      AlarmDescription: 'CPU_Utilization'
      Dimensions:
      - Name: DBInstanceIdentifier
        Value:
          Ref: AuroraDBSecondInstance
      MetricName: CPUUtilization
      Statistic: Maximum
      Namespace: 'AWS/RDS'
      Threshold: '80'
      Unit: Percent
      ComparisonOperator: 'GreaterThanOrEqualToThreshold'
      Period: '60'
      EvaluationPeriods: '5'
      TreatMissingData: 'notBreaching'

  MaxUsedTxIDsAlarm1:
    Type: 'AWS::CloudWatch::Alarm'
    Properties:
      ActionsEnabled: true
      AlarmActions:
      - Ref: DBSNSTopic
      AlarmDescription: 'Maximum Used Transaction IDs'
      Dimensions:
      - Name: DBInstanceIdentifier
        Value:
          Ref: AuroraDBFirstInstance
      MetricName: 'MaximumUsedTransactionIDs'
      Statistic: Average
      Namespace: 'AWS/RDS'
      Threshold: '600000000'
      Unit: Count
      ComparisonOperator: 'GreaterThanOrEqualToThreshold'
      Period: '60'
      EvaluationPeriods: '5'
      TreatMissingData: 'notBreaching'

  MaxUsedTxIDsAlarm2:
    Condition: IsReplica
    Type: 'AWS::CloudWatch::Alarm'
    Properties:
      ActionsEnabled: true
      AlarmActions:
      - Ref: DBSNSTopic
      AlarmDescription: 'Maximum Used Transaction IDs'
      Dimensions:
      - Name: DBInstanceIdentifier
        Value:
          Ref: AuroraDBSecondInstance
      MetricName: 'MaximumUsedTransactionIDs'
      Statistic: Average
      Namespace: 'AWS/RDS'
      Threshold: '600000000'
      Unit: Count
      ComparisonOperator: 'GreaterThanOrEqualToThreshold'
      Period: '60'
      EvaluationPeriods: '5'
      TreatMissingData: 'notBreaching'

  FreeLocalStorageAlarm1:
    Type: 'AWS::CloudWatch::Alarm'
    Properties:
      ActionsEnabled: true
      AlarmActions:
      - Ref: DBSNSTopic
      AlarmDescription: 'Free Local Storage'
      Dimensions:
      - Name: DBInstanceIdentifier
        Value:
          Ref: AuroraDBFirstInstance
      MetricName: 'FreeLocalStorage'
      Statistic: Average
      Namespace: 'AWS/RDS'
      Threshold: '5368709120'
      Unit: Bytes
      ComparisonOperator: 'LessThanOrEqualToThreshold'
      Period: '60'
      EvaluationPeriods: '5'
      TreatMissingData: 'notBreaching'

  FreeLocalStorageAlarm2:
    Condition: IsReplica
    Type: 'AWS::CloudWatch::Alarm'
    Properties:
      ActionsEnabled: true
      AlarmActions:
      - Ref: DBSNSTopic
      AlarmDescription: 'Free Local Storage'
      Dimensions:
      - Name: DBInstanceIdentifier
        Value:
          Ref: AuroraDBSecondInstance
      MetricName: 'FreeLocalStorage'
      Statistic: Average
      Namespace: 'AWS/RDS'
      Threshold: '5368709120'
      Unit: Bytes
      ComparisonOperator: 'LessThanOrEqualToThreshold'
      Period: '60'
      EvaluationPeriods: '5'
      TreatMissingData: 'notBreaching'

###############################################################################
# Notification
###############################################################################

  DBSNSTopic:
    Type: AWS::SNS::Topic
    Properties:
      Subscription:
      - Endpoint: '<EMAIL>'
        Protocol: email

  DatabaseClusterEventSubscription:
    Type: 'AWS::RDS::EventSubscription'
    Properties:
      EventCategories:
      - failover
      - failure
      - notification
      SnsTopicArn: !Ref DBSNSTopic
      SourceIds: [!Ref AuroraDBCluster]
      SourceType: 'db-cluster'

  DatabaseInstanceEventSubscription:
    Type: 'AWS::RDS::EventSubscription'
    Properties:
      EventCategories:
      - availability
      - configuration change
      - deletion
      - failover
      - failure
      - maintenance
      - notification
      - recovery
      SnsTopicArn: !Ref DBSNSTopic
      SourceIds:
      - !Ref AuroraDBFirstInstance
      - !If [IsReplica, !Ref AuroraDBSecondInstance, !Ref 'AWS::NoValue']
      SourceType: 'db-instance'

  DBParameterGroupEventSubscription:
    Type: 'AWS::RDS::EventSubscription'
    Properties:
      EventCategories:
      - configuration change
      SnsTopicArn: !Ref DBSNSTopic
      SourceIds: [!Ref DBParamGroup]
      SourceType: 'db-parameter-group'

###############################################################################
# DNS Domain Name
###############################################################################

  DBPublicDNS:
    Type: 'AWS::Route53::RecordSetGroup'
    Properties:
      HostedZoneId: !Ref PublicHostedZone
      RecordSets:
      - Name: !Join ['', [!Ref DNSName, '.', !Ref DNSDomain, .]]
        Type: CNAME
        TTL: '300'
        ResourceRecords:
        - !GetAtt AuroraDBCluster.Endpoint.Address

  DBPrivateDNS:
    Type: 'AWS::Route53::RecordSetGroup'
    Properties:
      HostedZoneId: !Ref PrivateHostedZone
      RecordSets:
      - Name: !Join ['', [!Ref DNSName, '.', !Ref DNSDomain, .]]
        Type: CNAME
        TTL: '300'
        ResourceRecords:
        - !GetAtt AuroraDBCluster.Endpoint.Address

  DBReadPublicDNS:
    Type: 'AWS::Route53::RecordSetGroup'
    Properties:
      HostedZoneId: !Ref PublicHostedZone
      RecordSets:
      - Name: !Join ['', [!Ref DNSName, '_read', '.', !Ref DNSDomain, .]]
        Type: CNAME
        TTL: '300'
        ResourceRecords:
        - !GetAtt AuroraDBCluster.ReadEndpoint.Address

  DBReadPrivateDNS:
    Type: 'AWS::Route53::RecordSetGroup'
    Properties:
      HostedZoneId: !Ref 'PrivateHostedZone'
      RecordSets:
      - Name: !Join ['', [!Ref DNSName, '_read', '.', !Ref DNSDomain, .]]
        Type: CNAME
        TTL: '300'
        ResourceRecords:
        - !GetAtt AuroraDBCluster.ReadEndpoint.Address

###############################################################################
# Outputs
###############################################################################
Outputs:
  ClusterEndpoint:
    Description: 'Aurora Cluster/Writer Endpoint'
    Value: !GetAtt 'AuroraDBCluster.Endpoint.Address'
  ReaderEndpoint:
    Description: 'Aurora Reader Endpoint'
    Value: !GetAtt 'AuroraDBCluster.ReadEndpoint.Address'
  Port:
    Description: 'Aurora Endpoint Port'
    Value: !GetAtt 'AuroraDBCluster.Endpoint.Port'
  DBUsername:
    Description: 'Database master username'
    Value: !Ref DBUsername
  DBName:
    Description: 'Database Name'
    Value: !Ref DBName
  PSQLCommandLine:
    Description: PSQL Command Line
    Value: !Join
             - ''
             - - 'psql --host='
               - !GetAtt 'AuroraDBCluster.Endpoint.Address'
               - ' --port='
               - !GetAtt 'AuroraDBCluster.Endpoint.Port'
               - ' --username='
               - !Ref DBUsername
               - ' --dbname='
               - !Ref DBName
