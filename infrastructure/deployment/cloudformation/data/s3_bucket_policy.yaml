AWSTemplateFormatVersion: "2010-09-09"
Description: Generic template for S3 bucket policy
Parameters:
  Env:
    Description: AWS environment name
    Type: String
    Default: produs
    AllowedValues:
      - produs
      - prodjp
      - stagingus
  S3BucketName:
    Description: S3 bucket name
    Type: String
Resources:
  S3BucketPolicy:
    Type: "AWS::S3::BucketPolicy"
    Properties:
      Bucket: !Ref S3BucketName
      PolicyDocument:
        Statement:
        -
          Sid: "Allow full administration of the bucket"
          Action:
          - "s3:DeleteBucket"
          - "s3:DeleteObject"
          Effect: "Allow"
          Principal:
            AWS:
              - !Sub "arn:aws:iam::${AWS::AccountId}:user/michael.mao"
              - !Sub "arn:aws:iam::${AWS::AccountId}:user/lu.min"
          Resource:
            - !Join [ "", [ "arn:aws:s3:::", !Ref S3BucketName, "/*" ] ]
            - !Join [ "", [ "arn:aws:s3:::", !Ref S3BucketName ] ]