AWSTemplateFormatVersion: "2010-09-09"
Description: Elasticsearch cluster
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Default
      Parameters:
      - DNSDomain
      - Env
      - PrivateHostedZone
      - SubnetId
      - VpcId
      - VpcCIDRJP
      - VpcCIDRUS
    - Label:
        default: Elasticsearch Cluster Config
      Parameters:
      - DedicatedMasterCount
      - DedicatedMasterEnabled
      - DedicatedMasterType
      - ElasticsearchDNSName
      - ElasticsearchDomainName
      - InstanceType
      - InstanceCount
      - VolumeSize
      - ZoneAwareness
Parameters:
  DNSDomain:
    Description: DNS Domain name
    Type: String
  Env:
    Description: AWS environment name
    Type: String
    Default: produs
    AllowedValues:
      - produs
      - prodjp
      - stagingus
  PrivateHostedZone:
    Description: Select private hosted zone
    Type: "AWS::Route53::HostedZone::Id"
  SubnetId:
    Description: Please select a private subnet
    Type: "AWS::EC2::Subnet::Id"
  VpcId:
    Description: Select a VPC
    Type: "AWS::EC2::VPC::Id"
  VpcCIDRJP:
    Description: VPC CIDR JP
    Type: String
    MinLength: 9
    MaxLength: 18
    AllowedPattern: "(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})/(\\d{1,2})"
    ConstraintDescription: "CIDR range in the form of x.x.x.x/16"
  VpcCIDRUS:
    Description: VPC CIDR US
    Type: String
    MinLength: 9
    MaxLength: 18
    AllowedPattern: "(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})/(\\d{1,2})"
    ConstraintDescription: "CIDR range in the form of x.x.x.x/16"
  ##############################
  DedicatedMasterCount:
    Description: Dedicated master instance count
    Type: Number
  DedicatedMasterEnabled:
    Description: Dedicated Master Enabled
    Type: String
  DedicatedMasterType:
    Description: EC2 instance type class
    Type: String
    Default: t2.small.elasticsearch
    AllowedValues:
      - t2.small.elasticsearch
      - t2.medium.elasticsearch
      - m5.large.elasticsearch
      - m5.xlarge.elasticsearch
      - r5.large.elasticsearch
      - r5.xlarge.elasticsearch
    ConstraintDescription: "Please choose a valid instance type."
  ElasticsearchDNSName:
    Description: The DNS name for ElasticSearch
    Type: String
  ElasticsearchDomainName:
    Description: ES domain name
    Type: String
  InstanceType:
    Description: EC2 instance type class
    Type: String
    AllowedValues:
      - t2.small.elasticsearch
      - t2.medium.elasticsearch
      - m5.large.elasticsearch
      - m5.xlarge.elasticsearch
      - r5.large.elasticsearch
      - r5.xlarge.elasticsearch]
    ConstraintDescription: "Please choose a valid instance type."
  InstanceCount:
    Description: Instance count
    Type: Number
  VolumeSize:
    Description: Volume size
    Type: String
  ZoneAwareness:
    Description: Zone awareness enabled
    Type: String

Resources:
  ESSecurityGroup:
    Type: "AWS::EC2::SecurityGroup"
    Properties:
      GroupDescription: "ES Security Group"
      Tags:
        -
          Key: "Name"
          Value: "ad elasticsearch Security Group"
        -
          Key: "Env"
          Value:
            Ref: "Env"
      VpcId: !Ref VpcId
  ESHttpInboundVpc:
    Type: "AWS::EC2::SecurityGroupIngress"
    Properties:
      GroupId: !Ref ESSecurityGroup
      IpProtocol: tcp
      FromPort: "80"
      ToPort: "80"
      CidrIp: !Ref VpcCIDRUS
  ESHttpsInbound:
    Type: "AWS::EC2::SecurityGroupIngress"
    Properties:
      GroupId: !Ref ESSecurityGroup
      IpProtocol: tcp
      FromPort: "443"
      ToPort: "443"
      CidrIp: !Ref VpcCIDRUS
  ESHttpInboundVpcJP:
    Type: "AWS::EC2::SecurityGroupIngress"
    Properties:
      GroupId: !Ref ESSecurityGroup
      IpProtocol: tcp
      FromPort: "80"
      ToPort: "80"
      CidrIp: !Ref VpcCIDRJP
  ESHttpsInboundJP:
    Type: "AWS::EC2::SecurityGroupIngress"
    Properties:
      GroupId: !Ref ESSecurityGroup
      IpProtocol: tcp
      FromPort: "443"
      ToPort: "443"
      CidrIp: !Ref VpcCIDRJP
  ElasticsearchDomain:
      Type: "AWS::Elasticsearch::Domain"
      Properties:
        DomainName: !Ref ElasticsearchDomainName
        ElasticsearchClusterConfig:
          DedicatedMasterEnabled: !Ref DedicatedMasterEnabled
          InstanceCount: !Ref InstanceCount
          ZoneAwarenessEnabled: !Ref ZoneAwareness
          InstanceType: !Ref InstanceType
#          DedicatedMasterType: !Ref "DedicatedMasterType"
#          DedicatedMasterCount: !Ref "DedicatedMasterCount"
        EBSOptions:
          EBSEnabled: true
          Iops: 0
          VolumeSize: !Ref VolumeSize
          VolumeType: gp2
        ElasticsearchVersion: 7.1
        SnapshotOptions:
          AutomatedSnapshotStartHour: "0"
        AccessPolicies:
          Version: 2012-10-17
          Statement:
            - Effect: Allow
              Principal:
                AWS: '*'
              Action: 'es:*'
              Resource: !Sub 'arn:aws:es:${AWS::Region}:*:domain/${ElasticsearchDomainName}/*'
        AdvancedOptions:
          rest.action.multi.allow_explicit_index: "true"
        Tags:
            - Key: Env
              Value: !Ref Env
        VPCOptions:
          SubnetIds:
            - !Ref SubnetId
          SecurityGroupIds:
            - !Ref ESSecurityGroup
  ElasticsearchDomainDNS:
    DependsOn:
      - ElasticsearchDomain
    Type: "AWS::Route53::RecordSetGroup"
    Properties:
      HostedZoneId: !Ref PrivateHostedZone
      RecordSets:
      - Name: !Join ['', [!Ref ElasticsearchDNSName,'.', !Ref DNSDomain, .]]
        Type: CNAME
        TTL: '300'
        ResourceRecords:
        - !GetAtt ElasticsearchDomain.DomainEndpoint
Outputs:
  ElasticsearchEndpoint:
    Value: !GetAtt "ElasticsearchDomain.DomainEndpoint"
