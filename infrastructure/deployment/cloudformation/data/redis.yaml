AWSTemplateFormatVersion: "2010-09-09"
Description: Redis cluster
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Default
      Parameters:
      - Env
      - PrivateHostedZone
      - PrivateSubnetA
      - PrivateSubnetB
      - VpcCIDRJP
      - VpcCIDRUS
      - VpcId
    - Label:
        default: Redis
      Parameters:
      - AutoMinorVersionUpgrade
      - ClusterName
      - DNSDomain
      - EngineType
      - EngineVersionCompatibility
      - MaintenanceWindow
      - NodeType
      - NumberOfNodes
      - PortNumber
      - RedisDNSName
      - SnapshotRetention
Parameters:
  Env:
    Description: AWS environment name
    Type: String
    Default: prodjp
    AllowedValues:
      - produs
      - prodjp
      - stagingus
  PrivateHostedZone:
    Description: Select private hosted zone
    Type: "AWS::Route53::HostedZone::Id"
  PrivateSubnetA:
    Description: Select one private subnet
    Type: "AWS::EC2::Subnet::Id"
  PrivateSubnetB:
    Description: Select one private subnet
    Type: "AWS::EC2::Subnet::Id"
  PublicHostedZone:
    Description: Select public hosted zone
    Type: "AWS::Route53::HostedZone::Id"
  VpcCIDRJP:
    Description: Select a VPC CIDR, JP VPC
    Type: String
    MinLength: 9
    MaxLength: 18
    AllowedPattern: "(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})/(\\d{1,2})"
  VpcCIDRUS:
    Description: Select a VPC CIDR, US VPC
    Type: String
    MinLength: 9
    MaxLength: 18
    AllowedPattern: "(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})/(\\d{1,2})"
  VpcId:
    Description: Select a VPC
    Type: "AWS::EC2::VPC::Id"
  ###################################
  AutoMinorVersionUpgrade:
    Description: Auto minor version upgrade
    Type: String
  ClusterName:
    Description: Redis cluster name
    Type: String
  DNSDomain:
    Description: DNS Domain name
    Type: String
  EngineType:
    Description: Engine type - use default value - redis
    Type: String
  EngineVersionCompatibility:
    Description: Engine Version Compatibility
    Type: String
  MaintenanceWindow:
    Description: Preffered maintenance window
    Type: String
  NodeType:
    Description: Redis node type name
    Type: String
    AllowedValues:
      - cache.m3.medium
      - cache.m3.large
      - cache.m3.xlarge
      - cache.m3.2xlarge
      - cache.r3.large
      - cache.r3.xlarge
      - cache.r3.2xlarge
      - cache.r4.large
      - cache.r4.xlarge
      - cache.r4.2xlarge
      - cache.r4.4xlarge
      - cache.r5.large
      - cache.r5.xlarge
      - cache.r5.2xlarge
      - cache.r5.4xlarge
      - cache.r5.12xlarge
  NumberOfNodes:
    Description: Number of nodes
    Type: Number
  PortNumber:
    Description: Port number
    Type: Number
    Default: 6379
  RedisDNSName:
    Description: DNS Name for Redis
    Type: String
  SnapshotRetention:
    Type: Number
    Description: Snapshot retention days
Resources:
  ElasticacheSecurityGroup:
    Type: "AWS::EC2::SecurityGroup"
    Properties:
      GroupDescription: "Redis Security Group"
      Tags:
        -
          Key: "Name"
          Value: "ad redis security group"
        -
          Key: "Env"
          Value:
            Ref: "Env"
      VpcId:
        Ref: "VpcId"
  RedisRuleJP:
    Type: "AWS::EC2::SecurityGroupIngress"
    Properties:
      Description: "JP VPC"
      GroupId: !Ref ElasticacheSecurityGroup
      IpProtocol: tcp
      FromPort: !Ref PortNumber
      ToPort: !Ref PortNumber
      CidrIp: !Ref VpcCIDRJP
  RedisRuleUS:
    Type: "AWS::EC2::SecurityGroupIngress"
    Properties:
      Description: "US VPC"
      GroupId: !Ref ElasticacheSecurityGroup
      IpProtocol: tcp
      FromPort: !Ref PortNumber
      ToPort: !Ref PortNumber
      CidrIp: !Ref VpcCIDRUS
  CacheSubnetGroup:
      Type: "AWS::ElastiCache::SubnetGroup"
      Properties:
        Description: "Cache subnet group"
        SubnetIds:
          -
            Ref: PrivateSubnetA
          -
            Ref: PrivateSubnetB
  DefaultParametersGroup:
      Type: "AWS::ElastiCache::ParameterGroup"
      Properties:
        CacheParameterGroupFamily: "redis5.0"
        Description: "Modifications to support better performance"
        Properties:
          tcp-keepalive: 60
          timeout: 900
  RedisDNS:
    DependsOn:
      - RedisCluster
    Type: "AWS::Route53::RecordSetGroup"
    Properties:
      HostedZoneId: !Ref PrivateHostedZone
      RecordSets:
      - Name: !Join ['', [!Ref RedisDNSName, '.', !Ref DNSDomain, '.']]
        Type: CNAME
        TTL: '300'
        ResourceRecords:
        - !GetAtt RedisCluster.RedisEndpoint.Address
  RedisPublicDNS:
    DependsOn:
      - RedisCluster
    Type: "AWS::Route53::RecordSetGroup"
    Properties:
      HostedZoneId: !Ref PublicHostedZone
      RecordSets:
        - Name: !Join ['', [!Ref RedisDNSName, '.', !Ref DNSDomain, '.']]
          Type: CNAME
          TTL: '300'
          ResourceRecords:
            - !GetAtt RedisCluster.RedisEndpoint.Address
  RedisCluster:
    Type: "AWS::ElastiCache::CacheCluster"
    Properties:
      AutoMinorVersionUpgrade: !Ref AutoMinorVersionUpgrade
      CacheNodeType: !Ref NodeType
      CacheParameterGroupName: !Ref DefaultParametersGroup
      CacheSubnetGroupName: !Ref CacheSubnetGroup
      ClusterName: !Ref ClusterName
      Engine: !Ref EngineType
      EngineVersion: !Ref EngineVersionCompatibility
      NumCacheNodes: !Ref NumberOfNodes
      Port: !Ref PortNumber
      PreferredMaintenanceWindow: !Ref MaintenanceWindow
      SnapshotRetentionLimit: !Ref SnapshotRetention
      Tags:
        -
          Key: "Name"
          Value: "ad redis"
        -
          Key: "Env"
          Value:
            Ref: "Env"
      VpcSecurityGroupIds:
      - !Ref ElasticacheSecurityGroup
