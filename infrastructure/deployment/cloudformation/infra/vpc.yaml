AWSTemplateFormatVersion: "2010-09-09"
Description: Creates AWS VPC
Parameters:
  Env:
    Description: AWS environment name
    Type: String
    Default: produs
    AllowedValues:
      - produs
      - prodjp
      - stagingus
  Public1aCIDR:
    Type: String
    MinLength: 9
    MaxLength: 18
    AllowedPattern: "(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})/(\\d{1,2})"
    ConstraintDescription: "CIDR range in the form of x.x.x.x/24"
    Default: "***********/24"
  Public1bCIDR:
    Type: String
    MinLength: 9
    MaxLength: 18
    AllowedPattern: "(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})/(\\d{1,2})"
    ConstraintDescription: "CIDR range in the form of x.x.x.x/24"
    Default: "***********/24"
  Public1cCIDR:
    Type: String
    MinLength: 9
    MaxLength: 18
    AllowedPattern: "(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})/(\\d{1,2})"
    ConstraintDescription: "CIDR range in the form of x.x.x.x/24"
    Default: "***********/24"
  Private1aCIDR:
    Type: String
    MinLength: 9
    MaxLength: 18
    AllowedPattern: "(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})/(\\d{1,2})"
    ConstraintDescription: "CIDR range in the form of x.x.x.x/24"
    Default: "**********/24"
  Private1bCIDR:
    Type: String
    MinLength: 9
    MaxLength: 18
    AllowedPattern: "(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})/(\\d{1,2})"
    ConstraintDescription: "CIDR range in the form of x.x.x.x/24"
    Default: "**********/24"
  Private1cCIDR:
    Type: String
    MinLength: 9
    MaxLength: 18
    AllowedPattern: "(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})/(\\d{1,2})"
    ConstraintDescription: "CIDR range in the form of x.x.x.x/24"
    Default: "**********/24"
  SubnetAvailabilityZone1:
    Type: String
    Default: "a"
  SubnetAvailabilityZone2:
    Type: String
    Default: "b"
  SubnetAvailabilityZone3:
    Type: String
    Default: "c"
  VpcCIDR:
    Type: String
    MinLength: 9
    MaxLength: 18
    AllowedPattern: "(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})/(\\d{1,2})"
    ConstraintDescription: "CIDR range in the form of x.x.x.x/16"
    Default: "**********/16"
Resources:
    VPC:
      Type: "AWS::EC2::VPC"
      Properties:
        EnableDnsSupport: true
        EnableDnsHostnames: true
        CidrBlock: !Ref VpcCIDR
        Tags:
          -
            Key: "Name"
            Value: !Join [ " ", [!Ref Env,  "VPC with Public and Private"]]
          -
            Key: "Env"
            Value:
              Ref: "Env"
    PublicSubnet1a:
      Type: "AWS::EC2::Subnet"
      Properties:
        VpcId:
          Ref: "VPC"
        CidrBlock:  !Ref "Public1aCIDR"
        MapPublicIpOnLaunch: "false"
        AvailabilityZone: !Join [ "", [!Ref "AWS::Region", !Ref "SubnetAvailabilityZone1"]]
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Network"
            Value: !Join [ "", ["Public1", !Ref "SubnetAvailabilityZone1"]]
          -
            Key: "Name"
            Value: !Join [ "", ["Public1", !Ref "SubnetAvailabilityZone1"]]
          -
            Key: "Env"
            Value:
              Ref: "Env"
    PublicSubnet1b:
      Type: "AWS::EC2::Subnet"
      Properties:
        VpcId:
          Ref: "VPC"
        CidrBlock: !Ref "Public1bCIDR"
        MapPublicIpOnLaunch: "false"
        AvailabilityZone: !Join [ "", [!Ref 'AWS::Region', !Ref "SubnetAvailabilityZone2"]]
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Network"
            Value: !Join [ "", ["Public1", !Ref "SubnetAvailabilityZone2"]]
          -
            Key: "Name"
            Value: !Join [ "", ["Public1", !Ref "SubnetAvailabilityZone2"]]
          -
            Key: "Env"
            Value:
              Ref: "Env"
    PublicSubnet1c:
      Type: "AWS::EC2::Subnet"
      Properties:
        VpcId:
          Ref: "VPC"
        CidrBlock: !Ref "Public1cCIDR"
        MapPublicIpOnLaunch: "false"
        AvailabilityZone: !Join [ "", [!Ref 'AWS::Region', !Ref "SubnetAvailabilityZone3"]]
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Network"
            Value: !Join [ "", ["Public1", !Ref "SubnetAvailabilityZone3"]]
          -
            Key: "Name"
            Value: !Join [ "", ["Public1", !Ref "SubnetAvailabilityZone3"]]
          -
            Key: "Env"
            Value:
              Ref: "Env"
    PrivateSubnet1a:
      Type: "AWS::EC2::Subnet"
      Properties:
        VpcId:
          Ref: "VPC"
        CidrBlock: !Ref "Private1aCIDR"
        MapPublicIpOnLaunch: "false"
        AvailabilityZone: !Join [ "", [!Ref 'AWS::Region', !Ref "SubnetAvailabilityZone1"]]
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Network"
            Value: !Join [ "", ["Private1", !Ref "SubnetAvailabilityZone1"]]
          -
            Key: "Name"
            Value: !Join [ "", ["Private1", !Ref "SubnetAvailabilityZone1"]]
          -
            Key: "Env"
            Value:
              Ref: "Env"
    PrivateSubnet1b:
      Type: "AWS::EC2::Subnet"
      Properties:
        VpcId:
          Ref: "VPC"
        CidrBlock: !Ref "Private1bCIDR"
        MapPublicIpOnLaunch: "false"
        AvailabilityZone: !Join [ "", [!Ref 'AWS::Region', !Ref "SubnetAvailabilityZone2"]]
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Network"
            Value: !Join [ "", ["Private1", !Ref "SubnetAvailabilityZone2"]]
          -
            Key: "Name"
            Value: !Join [ "", ["Private1", !Ref "SubnetAvailabilityZone2"]]
          -
            Key: "Env"
            Value:
              Ref: "Env"
    PrivateSubnet1c:
      Type: "AWS::EC2::Subnet"
      Properties:
        VpcId:
          Ref: "VPC"
        CidrBlock: !Ref "Private1cCIDR"
        MapPublicIpOnLaunch: "false"
        AvailabilityZone: !Join [ "", [!Ref 'AWS::Region', !Ref "SubnetAvailabilityZone3"]]
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Network"
            Value: !Join [ "", ["Private1", !Ref "SubnetAvailabilityZone3"]]
          -
            Key: "Name"
            Value: !Join [ "", ["Private1", !Ref "SubnetAvailabilityZone3"]]
          -
            Key: "Env"
            Value:
              Ref: "Env"
    InternetGateway:
      Type: "AWS::EC2::InternetGateway"
      Properties:
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Network"
            Value: "Public"
          -
            Key: "Name"
            Value: "InternetGateway"
          -
            Key: "Env"
            Value:
              Ref: "Env"
    GatewayToInternet:
      Type: "AWS::EC2::VPCGatewayAttachment"
      Properties:
        VpcId:
          Ref: "VPC"
        InternetGatewayId:
          Ref: "InternetGateway"
    PublicRouteTable:
      Type: "AWS::EC2::RouteTable"
      Properties:
        VpcId:
          Ref: "VPC"
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Network"
            Value: "Public"
          -
            Key: "Name"
            Value: "PublicRouteTable"
          -
            Key: "Env"
            Value:
              Ref: "Env"
    PublicRoute:
      Type: "AWS::EC2::Route"
      DependsOn: "GatewayToInternet"
      Properties:
        RouteTableId:
          Ref: "PublicRouteTable"
        DestinationCidrBlock: "0.0.0.0/0"
        GatewayId:
          Ref: "InternetGateway"
    PublicSubnetRouteTableAssociation1a:
      Type: "AWS::EC2::SubnetRouteTableAssociation"
      Properties:
        SubnetId:
          Ref: "PublicSubnet1a"
        RouteTableId:
          Ref: "PublicRouteTable"
    PublicSubnetRouteTableAssociation1b:
      Type: "AWS::EC2::SubnetRouteTableAssociation"
      Properties:
        SubnetId:
          Ref: "PublicSubnet1b"
        RouteTableId:
          Ref: "PublicRouteTable"
    PublicSubnetRouteTableAssociation1c:
      Type: "AWS::EC2::SubnetRouteTableAssociation"
      Properties:
        SubnetId:
          Ref: "PublicSubnet1c"
        RouteTableId:
          Ref: "PublicRouteTable"
    PrivateRouteTable:
      Type: "AWS::EC2::RouteTable"
      Properties:
        VpcId:
          Ref: "VPC"
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Network"
            Value: "Private"
          -
            Key: "Name"
            Value: "PrivateRouteTable"
          -
            Key: "Env"
            Value:
              Ref: "Env"
    PrivateRoute:
      Type: "AWS::EC2::Route"
      DependsOn: "NatGatewayPublic1a"
      Properties:
        RouteTableId:
          Ref: "PrivateRouteTable"
        DestinationCidrBlock: "0.0.0.0/0"
        NatGatewayId:
          Ref: "NatGatewayPublic1a"
    PrivateSubnetRouteTableAssociation1a:
      Type: "AWS::EC2::SubnetRouteTableAssociation"
      Properties:
        SubnetId:
          Ref: "PrivateSubnet1a"
        RouteTableId:
          Ref: "PrivateRouteTable"
    PrivateSubnetRouteTableAssociation1b:
      Type: "AWS::EC2::SubnetRouteTableAssociation"
      Properties:
        SubnetId:
          Ref: "PrivateSubnet1b"
        RouteTableId:
          Ref: "PrivateRouteTable"
    PrivateSubnetRouteTableAssociation1c:
      Type: "AWS::EC2::SubnetRouteTableAssociation"
      Properties:
        SubnetId:
          Ref: "PrivateSubnet1c"
        RouteTableId:
          Ref: "PrivateRouteTable"
    NatGatewayPublic1a:
      Type: "AWS::EC2::NatGateway"
      Properties:
        AllocationId:
          Fn::GetAtt:
          - EIPNATPub1a
          - AllocationId
        SubnetId: !Ref PublicSubnet1a
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Name"
            Value: !Join [ "", ["NatGatewayPublic1", !Ref "SubnetAvailabilityZone1"]]
          -
            Key: "Env"
            Value:
              Ref: "Env"
    EIPNATPub1a:
      Type: "AWS::EC2::EIP"
      Properties:
        Domain: "vpc"
    NatGatewayPublic1b:
      Type: "AWS::EC2::NatGateway"
      Properties:
        AllocationId:
          Fn::GetAtt:
          - EIPNATPub1b
          - AllocationId
        SubnetId: !Ref PublicSubnet1b
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Name"
            Value: !Join [ "", ["NatGatewayPublic1", !Ref "SubnetAvailabilityZone2"]]
          -
            Key: "Env"
            Value:
              Ref: "Env"
    EIPNATPub1b:
      Type: "AWS::EC2::EIP"
      Properties:
        Domain: "vpc"
    NatGatewayPublic1c:
      Type: "AWS::EC2::NatGateway"
      Properties:
        AllocationId:
          Fn::GetAtt:
          - EIPNATPub1c
          - AllocationId
        SubnetId: !Ref PublicSubnet1c
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Name"
            Value: !Join [ "", ["NatGatewayPublic1", !Ref "SubnetAvailabilityZone3"]]
          -
            Key: "Env"
            Value:
              Ref: "Env"
    EIPNATPub1c:
      Type: "AWS::EC2::EIP"
      Properties:
        Domain: "vpc"
    NetworkAcl:
      Type: "AWS::EC2::NetworkAcl"
      Properties:
        VpcId:
          Ref: "VPC"
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Name"
            Value: "Network ACL"
          -
            Key: "Env"
            Value:
              Ref: "Env"
    InboundHTTPPublicNetworkAclEntry:
      Type: "AWS::EC2::NetworkAclEntry"
      Properties:
        NetworkAclId:
          Ref: "NetworkAcl"
        RuleNumber: "100"
        Protocol: "-1"
        RuleAction: "allow"
        Egress: "false"
        CidrBlock: "0.0.0.0/0"
        PortRange:
          From: "0"
          To: "65535"
    OutboundPublicNetworkAclEntry:
      Type: "AWS::EC2::NetworkAclEntry"
      Properties:
        NetworkAclId:
          Ref: "NetworkAcl"
        RuleNumber: "100"
        Protocol: "-1"
        RuleAction: "allow"
        Egress: "true"
        CidrBlock: "0.0.0.0/0"
        PortRange:
          From: "0"
          To: "65535"
    SubnetNetworkAclAssociationPub1a:
      Type: "AWS::EC2::SubnetNetworkAclAssociation"
      Properties:
        SubnetId: !Ref PublicSubnet1a
        NetworkAclId: !Ref NetworkAcl
    SubnetNetworkAclAssociationPub1b:
      Type: "AWS::EC2::SubnetNetworkAclAssociation"
      Properties:
        SubnetId: !Ref PublicSubnet1b
        NetworkAclId: !Ref NetworkAcl
    SubnetNetworkAclAssociationPub1c:
      Type: "AWS::EC2::SubnetNetworkAclAssociation"
      Properties:
        SubnetId: !Ref PublicSubnet1c
        NetworkAclId: !Ref NetworkAcl
    SubnetNetworkAclAssociationPriv1a:
      Type: "AWS::EC2::SubnetNetworkAclAssociation"
      Properties:
        SubnetId: !Ref PrivateSubnet1a
        NetworkAclId: !Ref NetworkAcl
    SubnetNetworkAclAssociationPriv1b:
      Type: "AWS::EC2::SubnetNetworkAclAssociation"
      Properties:
        SubnetId: !Ref PrivateSubnet1b
        NetworkAclId: !Ref NetworkAcl
    SubnetNetworkAclAssociationPriv1c:
      Type: "AWS::EC2::SubnetNetworkAclAssociation"
      Properties:
        SubnetId: !Ref PrivateSubnet1c
        NetworkAclId: !Ref NetworkAcl
    DefaultSecurityGroup:
      Type: "AWS::EC2::SecurityGroup"
      Properties:
        GroupName: "default basic"
        GroupDescription: "Default Basic Security Group"
        VpcId:
          Ref: "VPC"
        SecurityGroupIngress:
        - IpProtocol: "-1"
          FromPort: "0"
          ToPort: "65535"
          CidrIp: !GetAtt ["VPC", CidrBlock]
        SecurityGroupEgress:
        - IpProtocol: "-1"
          FromPort: "0"
          ToPort: "65535"
          CidrIp: "0.0.0.0/0"
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Name"
            Value: "default"
          -
            Key: "Env"
            Value:
              Ref: "Env"
    ALBExtSecurityGroup:
      Type: "AWS::EC2::SecurityGroup"
      Properties:
        GroupName: "ALBExt"
        GroupDescription: "ALB ext Security Group"
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Name"
            Value: "alb ext sg"
          -
            Key: "Env"
            Value:
              Ref: "Env"
        VpcId: !Ref "VPC"
    ALBSecurityGroupHTTPinbound:
      Type: "AWS::EC2::SecurityGroupIngress"
      Properties:
        GroupId: !Ref "ALBExtSecurityGroup"
        IpProtocol: "tcp"
        FromPort: "80"
        ToPort: "80"
        CidrIp: "0.0.0.0/0"
    ALBSecurityGroupHTTPSinbound:
      Type: "AWS::EC2::SecurityGroupIngress"
      Properties:
        GroupId: !Ref "ALBExtSecurityGroup"
        IpProtocol: "tcp"
        FromPort: "443"
        ToPort: "443"
        CidrIp: "0.0.0.0/0"
    ALBIntSecurityGroup:
      Type: "AWS::EC2::SecurityGroup"
      Properties:
        GroupName: "ALBInt"
        GroupDescription: "ALB int Security Group"
        Tags:
          -
            Key: "Application"
            Value:
              Ref: "AWS::StackName"
          -
            Key: "Name"
            Value: "alb int sg"
          -
            Key: "Env"
            Value:
              Ref: "Env"
        VpcId: !Ref "VPC"
    ALBINTSecurityGroupHTTPInbound:
      Type: "AWS::EC2::SecurityGroupIngress"
      Properties:
        GroupId: !Ref "ALBIntSecurityGroup"
        IpProtocol: "tcp"
        FromPort: "80"
        ToPort: "80"
        CidrIp: !GetAtt ["VPC", "CidrBlock"]
    ALBINTSecurityGroupHTTPSInbound:
      Type: "AWS::EC2::SecurityGroupIngress"
      Properties:
        GroupId: !Ref "ALBIntSecurityGroup"
        IpProtocol: "tcp"
        FromPort: "443"
        ToPort: "443"
        CidrIp: !GetAtt ["VPC", "CidrBlock"]
    VPCEndpointS3:
      Type: "AWS::EC2::VPCEndpoint"
      Properties:
        PolicyDocument:
          Version: 2012-10-17
          Statement:
            - Effect: Allow
              Principal: '*'
              Action:
                - 's3:*'
              Resource:
                - 'arn:aws:s3:::*'
        RouteTableIds:
          - !Ref PublicRouteTable
          - !Ref PrivateRouteTable
        ServiceName: !Join
          - ''
          - - com.amazonaws.
            - !Ref 'AWS::Region'
            - .s3
        VpcId:
          Ref: "VPC"
Outputs:
    VPCId:
      Description: "VPCId of the newly created VPC"
      Value:
        Ref: "VPC"
      Export:
        Name:
          !Join ['-', [!Ref 'AWS::Region', !Sub '${AWS::StackName}', 'VPC']]
    PublicSubnet1a:
      Description: "SubnetId of the public subnet"
      Value:
        Ref: "PublicSubnet1a"
      Export:
        Name:
          !Join ['-', [!Ref 'AWS::Region', !Sub '${AWS::StackName}', !Join [ "", ["PublicSubnet1", !Ref "SubnetAvailabilityZone1"]]]]
    PublicSubnet1b:
      Description: "SubnetId of the public subnet"
      Value:
        Ref: "PublicSubnet1b"
      Export:
        Name:
          !Join ['-', [!Ref 'AWS::Region', !Sub '${AWS::StackName}', !Join [ "", ["PublicSubnet1", !Ref "SubnetAvailabilityZone2"]]]]
    PublicSubnet1c:
      Description: "SubnetId of the public subnet"
      Value:
        Ref: "PublicSubnet1c"
      Export:
        Name:
          !Join ['-', [!Ref 'AWS::Region', !Sub '${AWS::StackName}', !Join [ "", ["PublicSubnet1", !Ref "SubnetAvailabilityZone3"]]]]
    PrivateSubnet1a:
      Description: "SubnetId of the private subnet"
      Value:
        Ref: "PrivateSubnet1a"
      Export:
        Name:
          !Join ['-', [!Ref 'AWS::Region', !Sub '${AWS::StackName}', !Join [ "", ["PrivateSubnet1", !Ref "SubnetAvailabilityZone1"]]]]
    PrivateSubnet1b:
      Description: "SubnetId of the private subnet"
      Value:
        Ref: "PrivateSubnet1b"
      Export:
        Name:
          !Join ['-', [!Ref 'AWS::Region', !Sub '${AWS::StackName}', !Join [ "", ["PrivateSubnet1", !Ref "SubnetAvailabilityZone2"]]]]
    PrivateSubnet1c:
      Description: "SubnetId of the private subnet"
      Value:
        Ref: "PrivateSubnet1c"
      Export:
        Name:
          !Join ['-', [!Ref 'AWS::Region', !Sub '${AWS::StackName}', !Join [ "", ["PrivateSubnet1", !Ref "SubnetAvailabilityZone3"]]]]
    ALBIntSecurityGroup:
      Description: "SecurityGroupId for ALBInt"
      Value: !Ref ALBIntSecurityGroup
      Export:
        Name: !Join ['-', [!Ref 'AWS::Region', !Sub '${AWS::StackName}', 'ALBIntSecurityGroup']]
    ALBExtSecurityGroup:
      Description: "SecurityGroup for ALB"
      Value: !Ref ALBExtSecurityGroup
      Export:
        Name: !Join ['-', [!Ref 'AWS::Region', !Sub '${AWS::StackName}', 'ALBExtSecurityGroup']]
    RouteTablePrivate:
      Value: !Ref PrivateRouteTable
    RouteTablePublic:
      Value: !Ref PublicRouteTable
      