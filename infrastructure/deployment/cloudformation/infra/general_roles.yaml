AWSTemplateFormatVersion: "2010-09-09"
Description: "IAM Global Roles"
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Default
      Parameters:
      - Env
Parameters:
  Env:
    Description: "AWS environment name"
    Type: String
    Default: produs
    AllowedValues:
      - produs
      - prodjp
      - stagingus
Resources:
  ECSInstanceRole:
    Type: "AWS::IAM::Role"
    Properties:
#      RoleName: "ECSInstanceRole"
      AssumeRolePolicyDocument:
        Statement:
        - Effect: "Allow"
          Principal:
            Service: ["ec2.amazonaws.com",
                      "ecs-tasks.amazonaws.com",
                      "ssm.amazonaws.com"
                      ]
          Action: ["sts:AssumeRole"]
      Path: /
      ManagedPolicyArns:
        - "arn:aws:iam::aws:policy/AmazonSQSFullAccess"
        - "arn:aws:iam::aws:policy/AmazonS3FullAccess"
        - "arn:aws:iam::aws:policy/AmazonSSMFullAccess"
        - "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
        - "arn:aws:iam::aws:policy/AmazonSNSFullAccess"
        - "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
        - "arn:aws:iam::aws:policy/CloudWatchFullAccess"
        - "arn:aws:iam::aws:policy/AmazonSESFullAccess"
        - "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryFullAccess"
        - "arn:aws:iam::aws:policy/AmazonEC2ContainerServiceFullAccess"
      Policies:
        -
          PolicyName: "KMSDecryptPolicy"
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              -
                Effect: "Allow"
                Action: [
                    "kms:Decrypt"
                ]
                Resource:
                - "arn:aws:kms:us-west-2:853431205376:key/3512a241-1670-4ec0-94f7-1095d752d43a"
        - PolicyName: "MyAIPolicies"
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: "Allow"
                Action: "rekognition:*"
                Resource: "*"
              - Effect: "Allow"
                Action: "textract:*"
                Resource: "*"
  ECSInstanceProfile:
    Type: "AWS::IAM::InstanceProfile"
    Properties:
      Roles:
        - !Ref ECSInstanceRole
      Path: "/"
      InstanceProfileName: !Ref ECSInstanceRole
  ECSEventsRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Statement:
        - Effect: "Allow"
          Principal:
            Service: ["events.amazonaws.com"]
          Action: ["sts:AssumeRole"]
      Path: /
      ManagedPolicyArns: ["arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceEventsRole"]
Outputs:
  ECSInstanceRoleName:
    Value: !Ref "ECSInstanceRole"
  ECSInstanceRoleArn:
    Value: !GetAtt "ECSInstanceRole.Arn"
  ECSEventsRoleName:
    Value: !Ref "ECSEventsRole"
  ECSEventsRoleArn:
    Value: !GetAtt "ECSEventsRole.Arn"
