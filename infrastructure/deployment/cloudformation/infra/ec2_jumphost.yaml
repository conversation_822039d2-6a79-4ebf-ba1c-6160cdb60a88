AWSTemplateFormatVersion: 2010-09-09
Description: This template is used for setting up a single EC2
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Default
      Parameters:
      - Env
    - Label:
        default: Advanced
      Parameters:
      - HostName
      - InstanceType
      - KeyName
      - SshRuleCIDR
      - SubnetId
      - VpcID
Parameters:
  Env:
    Description: AWS environment name
    Type: String
    Default: produs
    AllowedValues:
      - produs
      - prodjp
      - stagingus
  HostName:
    Description: The name for the EC2
    Type: String
    Default: jumphost
  InstanceType:
    Description: EC2 instance class
    Type: String
    Default: t2.micro
    AllowedValues:
      - t2.micro
      - t2.small
      - t2.medium
      - t2.large
      - m5.large
      - m5.xlarge
    ConstraintDescription: Please choose a valid instance type.
  KeyName:
    Description: Name of an existing EC2 KeyPair to enable SSH access
    Type: AWS::EC2::KeyPair::KeyName
    Default: ad-jumphost
  SshRuleCIDR:
    Description: CIDR block for ssh rule
    Type: String
    Default: '0.0.0.0/0'
  SubnetId:
    Description: Select a public subnet
    Type: 'AWS::EC2::Subnet::Id'
  VpcID:
    Description: Select a VPC that allows instances access to the Internet.
    Type: 'AWS::EC2::VPC::Id'
Mappings:
  AWSAMIRegionMap:
    AMI:
      AMZNLINUX: amzn-ami-hvm-2018.03.0.20190611-x86_64-gp2
    ap-northeast-1:
      AMZNLINUX: ami-04b2d1589ab1d972c
    ap-northeast-2:
      AMZNLINUX: ami-0be3e6f84d3b968cd
    ap-south-1:
      AMZNLINUX: ami-0b99c7725b9484f9e
    ap-southeast-1:
      AMZNLINUX: ami-0fb6b6f9e81056553
    ap-southeast-2:
      AMZNLINUX: ami-075caa3491def750b
    eu-central-1:
      AMZNLINUX: ami-026d3b3672c6e7b66
    eu-west-1:
      AMZNLINUX: ami-0862aabda3fb488b5
    eu-west-2:
      AMZNLINUX: ami-0bdfa1adc3878cd23
    us-east-1:
      AMZNLINUX: ami-035b3c7efe6d061d5
    us-east-2:
      AMZNLINUX: ami-02f706d959cedf892
    us-west-1:
      AMZNLINUX: ami-0fcdcdb074d2bac5f
    us-west-2:
      AMZNLINUX: ami-0f2176987ee50226e
Resources:
  JumphostEC2Instance:
    Type: 'AWS::EC2::Instance'
    Properties:
      Tags:
        -
          Key: 'Name'
          Value: !Ref HostName
        -
          Key: 'Env'
          Value:
            Ref: 'Env'
      KeyName: !Ref KeyName
      InstanceType: !Ref InstanceType
      NetworkInterfaces:
        - AssociatePublicIpAddress: true
          DeviceIndex: 0
          GroupSet:
            - !GetAtt JumphostSecurityGroup.GroupId
          SubnetId: !Ref SubnetId
      ImageId: !FindInMap [AWSAMIRegionMap, !Ref 'AWS::Region', AMZNLINUX]
  JumphostSecurityGroup:
    Type: 'AWS::EC2::SecurityGroup'
    Properties:
      GroupDescription: 'AriaDirect Jumphost Security Group'
      Tags:
        -
          Key: 'Name'
          Value: 'EC2 Jumphost security group'
        -
          Key: 'Env'
          Value: !Ref Env
      VpcId: !Ref VpcID
  JumphostSSHRule:
    Type: 'AWS::EC2::SecurityGroupIngress'
    Properties:
      GroupId: !Ref JumphostSecurityGroup
      IpProtocol: tcp
      FromPort: '22'
      ToPort: '22'
      CidrIp: !Ref SshRuleCIDR
Outputs:
  JumphostEC2InstancePublicIPAddress:
    Value: !GetAtt JumphostEC2Instance.PublicIp
    Description: Public IP address of EC2 Jumphost.
  JumphostEC2InstancePrivateIPAddress:
    Value: !GetAtt JumphostEC2Instance.PrivateIp
    Description: Private IP address of EC2 Jumphost.
