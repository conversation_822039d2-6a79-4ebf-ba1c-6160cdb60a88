AWSTemplateFormatVersion: '2010-09-09'
Description: "AWS CloudFormation Template SQS: with dead letter queue"
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Default
      Parameters:
      - DeadLetterQueueName
      - DelaySeconds
      - MaximumMessageSize
      - FifoQueue
      - MaximumMessageSize
      - MessageRetentionPeriod
      - QueueName
      - ReceiveMessageWaitTimeSeconds
      - UsedeadletterQueue
      - VisibilityTimeout
Parameters:
  DeadLetterQueueName:
    Description: The deadletter queue name.
    Type: String
    MinLength: 1
    MaxLength: 255
    ConstraintDescription: must be less than 255 characters.
  DelaySeconds:
    Description: The time in seconds that the delivery of all messages in the queue
      is delayed. You can specify an integer value of 0 to 900 (15 minutes).
    Type: Number
    Default: '0'
  FifoQueue:
    Description: Whether or not this queue is a FIFO queue.
    Type: String
    Default: false
    AllowedValues:
    - true
    - false
  MaximumMessageSize:
    Description: The limit of how many bytes that a message can contain before Amazon
      SQS rejects it, 1024 bytes (1 KiB) to 262144 bytes (256 KiB)
    Type: Number
    Default: '262144'
  MaxReceiveCount:
    Description: The amount of receives a message should undergo undeleted before being moved to the Dead Letter queue.
    Type: Number
    Default: '5'
  MessageRetentionPeriod:
    Description: The number of seconds that Amazon SQS retains a message. You can
      specify an integer value from 60 seconds (1 minute) to 1209600 seconds (14 days).
    Type: Number
    Default: '1209600'
  QueueName:
    Description: The queue name.
    Type: String
    MinLength: 1
    MaxLength: 255
    ConstraintDescription: must be less than 255 characters.
  ReceiveMessageWaitTimeSeconds:
    Description: Specifies the duration, in seconds, that the ReceiveMessage action
      call waits until a message is in the queue in order to include it in the response,
      as opposed to returning an empty response if a message is not yet available.
      1 to 20
    Type: Number
    Default: '1'
  UsedeadletterQueue:
    Description: A dead-letter queue is a queue that other (source) queues can target
      for messages that can't be processed (consumed) successfully. You can set aside
      and isolate these messages in the dead-letter queue to determine why their processing
      doesn't succeed.
    Type: String
    Default: true
    AllowedValues:
    - true
    - false
  VisibilityTimeout:
    Description: This should be longer than the time it would take to process and
      delete a message, this should not exceed 12 hours.
    Type: Number
    Default: '300'
Conditions:
  CreateDeadLetterQueue:
    Fn::Equals:
    - Ref: UsedeadletterQueue
    - 'true'
  UseFifoQueue:
    Fn::Equals:
    - Ref: FifoQueue
    - 'true'
Resources:
  MySQSQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::If:
        - UseFifoQueue
        - Fn::Join:
          - '.'
          - - Ref: QueueName
            - 'fifo'
        - Ref: QueueName
      DelaySeconds:
        Ref: DelaySeconds
      MaximumMessageSize:
        Ref: MaximumMessageSize
      MessageRetentionPeriod:
        Ref: MessageRetentionPeriod
      ReceiveMessageWaitTimeSeconds:
        Ref: ReceiveMessageWaitTimeSeconds
      RedrivePolicy:
        Fn::If:
        - CreateDeadLetterQueue
        - deadLetterTargetArn:
            Fn::GetAtt:
            - MyDeadLetterQueue
            - Arn
          maxReceiveCount:
            Ref: MaxReceiveCount
        - Ref: AWS::NoValue
      VisibilityTimeout:
        Ref: VisibilityTimeout
      FifoQueue:
        Fn::If:
        - UseFifoQueue
        - Ref: FifoQueue
        - Ref: AWS::NoValue
  MyDeadLetterQueue:
    Condition: CreateDeadLetterQueue
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Ref DeadLetterQueueName
      FifoQueue:
        Fn::If:
        - UseFifoQueue
        - Ref: FifoQueue
        - Ref: AWS::NoValue
      MessageRetentionPeriod:
        Ref: MessageRetentionPeriod

Outputs:
  MyQueueARN:
    Description: ARN of newly created SQS Queue
    Value:
      Fn::GetAtt:
      - MySQSQueue
      - Arn
  MyQueueName:
    Description: Name newly created SQS Queue
    Value:
      Fn::GetAtt:
      - MySQSQueue
      - QueueName
  MyQueueURL:
    Description: URL of newly created SQS Queue
    Value:
      Ref: MySQSQueue
  MyDeadLetterQueueURL:
    Condition: CreateDeadLetterQueue
    Description: URL of the dead letter queue
    Value:
      Ref: MyDeadLetterQueue
  MyDeadLetterQueueARN:
    Condition: CreateDeadLetterQueue
    Description: ARN of the dead letter queue
    Value:
      Fn::GetAtt:
      - MyDeadLetterQueue
      - Arn