AWSTemplateFormatVersion: "2010-09-09"
Description: Kinesis streams
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Default
      Parameters:
      - Env
      - StreamName
      - StreamRetentionPeriod
      - StreamShardCount
Parameters:
  Env:
    Description: AWS environment name
    Type: String
    Default: produs
    AllowedValues:
      - produs
      - prodjp
      - stagingus
  StreamName:
    Description: The Kinesis stream name.
    Type: String
  StreamRetentionPeriod:
    Description: The number of hours for the data records that are stored in kinesis to remain accessible.
    Type: Number
    Default: 24
  StreamShardCount:
    Description: The number of shards.
    Type: Number
    Default: 2
Resources:
  MyStream:
    Type: AWS::Kinesis::Stream
    Properties:
      Name: !Ref StreamName
      Tags:
        - Key: "Name"
          Value: !Ref StreamName
        - Key: "Env"
          Value: !Ref Env
      RetentionPeriodHours: !Ref StreamRetentionPeriod
      ShardCount: !Ref StreamShardCount
