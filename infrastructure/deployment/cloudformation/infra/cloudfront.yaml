AWSTemplateFormatVersion: "2010-09-09"
Description: Create CloudFront CDN
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Default
      Parameters:
      - Env
    - Label:
        default: Advanced
      Parameters:
      - ApplicationName
      - CDNPriceClass
      - CertificateArn
      - CreateDNS
      - DistributionConfigComment
      - Domain
      - ResponsePagePath
      - RootPublicHostedZone
      - S3BucketName
      - S3OriginPath
      - TargetOriginId
Parameters:
  Env:
    Description: AWS environment name
    Type: String
    Default: produs
    AllowedValues:
      - produs
      - prodjp
      - stagingus
  ApplicationName:
    Description: Application name
    Type: String
  CDNPriceClass:
    Description: CloudFront price class
    Type: String
    Default: PriceClass_All
    AllowedValues:
      - PriceClass_100
      - PriceClass_200
      - PriceClass_All
  CertificateArn:
    Description: Certificate ARN, must be in us-east-1 region
    Type: String
  CreateDNS:
    Description: Create dns A record
    Type: String
    AllowedValues:
    - true
    - false
  DistributionConfigComment:
    Description: DistributionConfig Comment
    Type: String
  DNSDomain:
    Description: DNS Domain name
    Type: String
  ResponsePagePath:
    Description: Response page path
    Type: String
  RootPublicHostedZone:
    Description: Select public hosted zone
    Type: "AWS::Route53::HostedZone::Id"
  S3BucketName:
    Description: S3 bucket name
    Type: String
  S3OriginPath:
    Description: S3 origin path
    Type: String
  TargetOriginId:
    Description: S3 target origin id
    Type: String
Conditions:
  CreateDNSRecord:
    Fn::Equals:
    - Ref: CreateDNS
    - true
Resources:
  CDNDistribution:
    Type: AWS::CloudFront::Distribution
    Properties:
      DistributionConfig:
        Aliases:
          - !Join [ ".", [ !Ref ApplicationName, !Ref DNSDomain ]]
        DefaultCacheBehavior:
          Compress: true
          ForwardedValues:
            QueryString: false
          TargetOriginId: !Ref TargetOriginId
          ViewerProtocolPolicy: redirect-to-https
        CustomErrorResponses:
          - ErrorCachingMinTTL: 300
            ErrorCode: 403
            ResponseCode: 200
            ResponsePagePath: !Ref ResponsePagePath
        Comment: !Ref DistributionConfigComment
        Enabled: true
        HttpVersion: http2
        Origins:
          - DomainName: !Ref S3BucketName
            Id: !Ref TargetOriginId
            OriginPath: !Ref S3OriginPath
            S3OriginConfig:
              OriginAccessIdentity: ""
#                !Join [ "", [ "origin-access-identity/cloudfront/", !Ref TheCloudFrontOriginAccessIdentity ]]
        PriceClass: !Ref CDNPriceClass
        ViewerCertificate:
          AcmCertificateArn: !Ref CertificateArn
          SslSupportMethod: "sni-only"
      Tags:
      - Key: Domain
        Value: !Join [ ".", [ !Ref ApplicationName, !Ref DNSDomain ]]
      - Key: Env
        Value: !Ref Env
  PublicDNSARecord:
    DependsOn: CDNDistribution
    Condition: CreateDNSRecord
    Type: "AWS::Route53::RecordSetGroup"
    Properties:
      HostedZoneId: !Ref RootPublicHostedZone
      RecordSets:
      - Name: !Join [ "", [ !Ref ApplicationName, ".", !Ref DNSDomain, "." ]]
        Type: A
        AliasTarget:
          DNSName: !GetAtt CDNDistribution.DomainName
          HostedZoneId: "Z2FDTNDATAQYW2"
  TheCloudFrontOriginAccessIdentity:
    Type: AWS::CloudFront::CloudFrontOriginAccessIdentity
    Properties:
      CloudFrontOriginAccessIdentityConfig:
        Comment: "CloudFrontOriginAccessIdentity"
