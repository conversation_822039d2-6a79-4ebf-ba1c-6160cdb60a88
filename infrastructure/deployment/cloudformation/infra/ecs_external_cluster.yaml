AWSTemplateFormatVersion: "2010-09-09"
Description: "ECS external cluster in PRIVATE subnets"
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Default
      Parameters:
      - Env
    - Label:
        default: Advanced
      Parameters:
      - ALBExtSecurityGroup
      - ALBIntSecurityGroup
      - AutoScalingEnabled
      - CloudwatchLogRetention
      - ClusterDesiredCapacity
      - ClusterMaxSize
      - ClusterMinSize
      - ClusterName
      - ECSInstanceProfileArn
      - InstanceType
      - KeyName
      - SubnetIds
      - VpcCIDR
      - VpcId
Parameters:
  Env:
    Description: AWS environment name
    Type: String
    Default: produs
    AllowedValues:
      - produs
      - prodjp
      - stagingus
  #######################################
  ALBExtSecurityGroup:
    Description: SecurityGroup for ALB External Internet SG
    Type: AWS::EC2::SecurityGroup::Id
  ALBIntSecurityGroup:
    Description: SecurityGroup for Internal SG
    Type: AWS::EC2::SecurityGroup::Id
  AutoScalingEnabled:
    Description: Enable auto-scaling of EC2 instances based on CPU usage
    Type: String
    AllowedValues:
      - true
      - false
    Default: false
  CloudwatchLogRetention:
    Description: Retention days for CloudWatch logs.
    Type: Number
    Default: 3
  ClusterDesiredCapacity:
    Description: Number of instances to launch in your ECS cluster.
    Type: Number
    Default: 1
  ClusterMaxSize:
    Description: Max number of instances that can be launched in this ECS cluster.
    Type: Number
    Default: 3
  ClusterMinSize:
    Description: Min number of instances that can be launched in this ECS cluster.
    Type: Number
    Default: 1
  ClusterName:
    Description: Name of the ECS cluster
    Type: String
    MinLength: 5
    MaxLength: 255
    Default: "ad_<env>_<cluster_name:external>"
  ECSInstanceProfileArn:
    Description: ECS Instance profile name
    Type: String
  InstanceType:
    Description: EC2 instance type class
    Type: String
    Default: t2.large
    AllowedValues: [t2.micro, t2.small, t2.medium, t2.large,
      m4.large, m4.xlarge, m4.2xlarge, m4.4xlarge, m4.10xlarge,
      m5.large, m5.xlarge, m5.2xlarge, m5.4xlarge, m5.12xlarge]
    ConstraintDescription: Please choose a valid instance type.
  KeyName:
    Description: Name of an existing EC2 KeyPair to enable SSH access to the ECS instances.
    Type: "AWS::EC2::KeyPair::KeyName"
    Default: ad-ecs
  SubnetIds:
    Description: Select at least two private subnet in your selected VPC.
    Type: "List<AWS::EC2::Subnet::Id>"
    Default:
      -"Private1a"
      -"Private1b"
      -"Private1c"
  VpcCIDR:
    Description: VPC CIDR
    Type: String
    Default: "**********/16"
  VpcId:
    Description: Select a VPC that allows instances access to the Internet.
    Type: "AWS::EC2::VPC::Id"

Conditions:
  DoesAutoScalingEnabled: !Equals [ !Ref AutoScalingEnabled, ture ]
Mappings:
  AWSRegionToAMI:
    us-east-1:
      AMIID: ami-aff65ad2
    us-west-2:
      AMIID: ami-40ddb938
    ap-northeast-1:
      AMIID: ami-a99d8ad5
Resources:
  ECSCluster:
    Type: "AWS::ECS::Cluster"
    Properties:
      ClusterName: !Ref ClusterName
  ECSClusterSecurityGroup:
    Type: "AWS::EC2::SecurityGroup"
    Properties:
      GroupDescription: "ECS External Cluster Security Group"
      Tags:
        -
          Key: "Name"
          Value: "ECS External Cluster SG"
        -
          Key: "Env"
          Value:
            Ref: "Env"
      VpcId: !Ref VpcId
  ECSClusterSecurityGroupALBPorts:
    Type: "AWS::EC2::SecurityGroupIngress"
    Properties:
      GroupId: !Ref ECSClusterSecurityGroup
      IpProtocol: tcp
      FromPort: "31000"
      ToPort: "61000"
      SourceSecurityGroupId: !Ref ECSClusterSecurityGroup
  ECSClusterSecurityGroupVPC:
    Type: "AWS::EC2::SecurityGroupIngress"
    Properties:
      GroupId: !Ref "ECSClusterSecurityGroup"
      IpProtocol: tcp
      FromPort: "0"
      ToPort: "65535"
      CidrIp: !Ref VpcCIDR
  ECSClusterSecurityGroupALBExt:
    Type: "AWS::EC2::SecurityGroupIngress"
    Properties:
      GroupId: !Ref ECSClusterSecurityGroup
      IpProtocol: tcp
      FromPort: "0"
      ToPort: "65535"
      SourceSecurityGroupId: !Ref "ALBExtSecurityGroup"
  ECSClusterSecurityGroupALBInt:
    Type: "AWS::EC2::SecurityGroupIngress"
    Properties:
      GroupId: !Ref ECSClusterSecurityGroup
      IpProtocol: tcp
      FromPort: "0"
      ToPort: "65535"
      SourceSecurityGroupId: !Ref "ALBIntSecurityGroup"
  CloudwatchLogsGroup:
    Type: "AWS::Logs::LogGroup"
    Properties:
      LogGroupName: !Join ["-", [ECSPuclicClusterLogGroup, !Ref "AWS::StackName"]]
      RetentionInDays: !Ref CloudwatchLogRetention
  ECSAutoScalingGroup:
    Type: "AWS::AutoScaling::AutoScalingGroup"
    Properties:
      VPCZoneIdentifier: !Ref SubnetIds
      MixedInstancesPolicy:
        LaunchTemplate:
          LaunchTemplateSpecification:
            LaunchTemplateId: !Ref ECSLaunchTemplate
            Version:
              Fn::GetAtt:
                - "ECSLaunchTemplate"
                - "LatestVersionNumber"
      MinSize: !Ref ClusterMinSize
      MaxSize: !Ref ClusterMaxSize
      DesiredCapacity: !Ref ClusterDesiredCapacity
      HealthCheckGracePeriod: 90
      HealthCheckType: "EC2"
      Cooldown: "240"
      MetricsCollection:
        -
          Granularity: "1Minute"
          Metrics:
           - "GroupTerminatingInstances"
           - "GroupDesiredCapacity"
           - "GroupInServiceInstances"
           - "GroupMaxSize"
           - "GroupMinSize"
           - "GroupStandbyInstances"
           - "GroupPendingInstances"
           - "GroupTotalInstances"
      Tags:
        -
          Key: "Application"
          Value:
            Ref: "ClusterName"
          PropagateAtLaunch: "true"
        -
          Key: "Name"
          Value: "ECS External Cluster Minion"
          PropagateAtLaunch: "true"
        -
          Key: "Env"
          Value:
            Ref: "Env"
          PropagateAtLaunch: "true"
    CreationPolicy:
      ResourceSignal:
        Timeout: PT15M
    UpdatePolicy:
     AutoScalingRollingUpdate:
        MinSuccessfulInstancesPercent: 100
        MaxBatchSize: 1
        PauseTime: PT15M
        SuspendProcesses:
          - HealthCheck
          - ReplaceUnhealthy
          - AZRebalance
          - AlarmNotification
          - ScheduledActions
        WaitOnResourceSignals: True
  ECSLaunchTemplate:
    Type: "AWS::EC2::LaunchTemplate"
    Properties:
      LaunchTemplateData:
        ImageId: !FindInMap [AWSRegionToAMI, !Ref "AWS::Region", AMIID]
        InstanceType: !Ref InstanceType
        BlockDeviceMappings:
          - DeviceName: "/dev/sdk"
            Ebs:
              VolumeSize: "2"
              VolumeType: "gp2"
          - DeviceName: "/dev/xvda"
            Ebs:
              VolumeSize: "16"
              VolumeType: "gp2"
              DeleteOnTermination: "true"
          - DeviceName: "/dev/xvdcz"
            Ebs:
              VolumeSize: "50"
              VolumeType: "gp2"
              DeleteOnTermination: "true"
        SecurityGroupIds: [!Ref ECSClusterSecurityGroup]
        IamInstanceProfile:
          Arn: !Ref ECSInstanceProfileArn
        KeyName: !Ref KeyName
        UserData:
          Fn::Base64: !Sub |
            #!/bin/bash

            ECS_CLUSTER_NAME="${ECSCluster}"

            MY_IP=$(curl http://***************/latest/meta-data/local-ipv4)
            HOSTNUM=`hostname -I | /bin/awk '{print $1}' | /bin/awk '{split($0,ip,".");print ip[4]}'`

            # when ECS_CLUSTER_NAME is defined join cluster specified via ECS_CLUSTER_NAME
            if [ "$ECS_CLUSTER_NAME" != "" ]
            then
              # stop ECS agent to modify ECS_CLUSTER_NAME
              /sbin/stop ecs
              # set appropiate hostname
              /bin/hostname $ECS_CLUSTER_NAME$HOSTNUM
              # delete cached ECS_CLUSTER_NAME
              /bin/rm /var/lib/ecs/data/*

              # modify ECS config
              /bin/echo ECS_CLUSTER=$ECS_CLUSTER_NAME | /usr/bin/tee /etc/ecs/ecs.config
              /bin/echo ECS_ENABLE_CONTAINER_METADATA=true >> /etc/ecs/ecs.config
              /bin/echo ECS_ENABLE_SPOT_INSTANCE_DRAINING=true >> /etc/ecs/ecs.config

              # pull new ecs-agent image
              docker pull amazon/amazon-ecs-agent:latest
              # init ECS with new config and run docker container
              /sbin/start ecs
            else
              /sbin/stop ecs
            fi

            yum install -y aws-cfn-bootstrap
            /opt/aws/bin/cfn-signal -e $? --stack ${AWS::StackName} --resource ECSAutoScalingGroup --region ${AWS::Region}
  ScaleUpPolicy:
    Type: "AWS::AutoScaling::ScalingPolicy"
    Condition: DoesAutoScalingEnabled
    Properties:
      AdjustmentType: ChangeInCapacity
      AutoScalingGroupName:
        Ref: ECSAutoScalingGroup
      Cooldown: "60"
      ScalingAdjustment: "1"
  CPUAlarmHigh:
    Type: "AWS::CloudWatch::Alarm"
    Condition: DoesAutoScalingEnabled
    Properties:
      EvaluationPeriods: "3"
      Statistic: "Average"
      Threshold: "70"
      AlarmDescription: "Alarm if CPU too high or metric disappears indicating instance is down"
      Period: "60"
      AlarmActions:
      - Ref: ScaleUpPolicy
      Namespace: AWS/EC2
      Dimensions:
      - Name: AutoScalingGroupName
        Value:
          Ref: ECSAutoScalingGroup
      ComparisonOperator: GreaterThanThreshold
      MetricName: CPUUtilization
  ScaleDownPolicy:
    Type: "AWS::AutoScaling::ScalingPolicy"
    Condition: DoesAutoScalingEnabled
    Properties:
      AdjustmentType: ChangeInCapacity
      AutoScalingGroupName:
        Ref: ECSAutoScalingGroup
      Cooldown: "60"
      ScalingAdjustment: "-1"
  CPUAlarmLow:
    Type: "AWS::CloudWatch::Alarm"
    Condition: DoesAutoScalingEnabled
    Properties:
      EvaluationPeriods: "5"
      Statistic: Average
      Threshold: "3"
      AlarmDescription: "Alarm if CPU too low or metric disappears indicating instance is down"
      Period: "60"
      AlarmActions:
      - Ref: ScaleDownPolicy
      Namespace: AWS/EC2
      Dimensions:
      - Name: AutoScalingGroupName
        Value:
          Ref: ECSAutoScalingGroup
      ComparisonOperator: LessThanThreshold
      MetricName: CPUUtilization
  AutoscalingRole:
    Type: "AWS::IAM::Role"
    Condition: DoesAutoScalingEnabled
    Properties:
      AssumeRolePolicyDocument:
        Statement:
        - Effect: Allow
          Principal:
            Service: [application-autoscaling.amazonaws.com]
          Action: ["sts:AssumeRole"]
      Path: /
      Policies:
      - PolicyName: "ecs-service-autoscaling"
        PolicyDocument:
          Statement:
          - Effect: Allow
            Action: ["application-autoscaling:*",
                     "cloudwatch:DescribeAlarms",
                     "cloudwatch:PutMetricAlarm",
                     "ecs:DescribeServices",
                     "ecs:UpdateService"]
            Resource: "*"
Outputs:
  ECSExternalCluster:
    Value: !Ref ECSCluster
  ECSExternalClusterSecurityGroup:
    Value: !Ref ECSClusterSecurityGroup
