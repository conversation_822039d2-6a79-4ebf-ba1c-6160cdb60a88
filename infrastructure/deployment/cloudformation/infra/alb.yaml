AWSTemplateFormatVersion: 2010-09-09
Description: ECS task definitions for services with ALB/DNS in ECS external ECS cluster
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Basic
      Parameters:
      - ALBExtSecurityGroup
      - ALBIntSecurityGroup
      - DNSName
      - Env
      - PrivateHostedZone
      - PrivateSubnetIds
      - PublicHostedZone
      - PublicSubnetIds
      - RootCertificateArn
Parameters:
  ALBExtSecurityGroup:
    Description: ALB ext security group ID
    Type: String
  ALBIntSecurityGroup:
    Description: ALB int security group ID
    Type: String
  CertificateArn:
    Description: certificate arn
    Type: String
  DNSName:
    Description: DNS name
    Type: String
  Env:
    Description: AWS environment name
    Type: String
    Default: produs
    AllowedValues:
      - produs
      - prodjp
      - stagingus
  PrivateHostedZone:
    Description: Select private hosted zone
    Type: AWS::Route53::HostedZone::Id
  PrivateSubnetIds:
    Description: Select private subnets
    Type: List<AWS::EC2::Subnet::Id>
  PublicHostedZone:
    Description: Select public hosted zone
    Type: AWS::Route53::HostedZone::Id
  PublicSubnetIds:
    Description: Select public subnets
    Type: List<AWS::EC2::Subnet::Id>
  RootCertificateArn:
    Description: ARN for root Certificate to be used for HTTPS traffic in Load Balancers
    Type: String
Conditions:
  CreateRootCertificate:
    Fn::Equals:
      - Ref: Env
      - produs
  #################################
Resources:
  APIDNSPrivate:
    Type: AWS::Route53::RecordSetGroup
    Properties:
      HostedZoneId: !Ref PrivateHostedZone
      RecordSets:
      - Name: !Join ['', ['api', '.', !Ref DNSName, .]]
        Type: CNAME
        TTL: 300
        ResourceRecords:
        - !GetAtt APILoadBalancer.DNSName
  APIDNSPublic:
    Type: AWS::Route53::RecordSetGroup
    Properties:
      HostedZoneId: !Ref PublicHostedZone
      RecordSets:
      - Name: !Join ['', ['api', '.', !Ref DNSName, .]]
        Type: CNAME
        TTL: 300
        ResourceRecords:
        - !GetAtt APILoadBalancer.DNSName
  APILoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Scheme: internet-facing
      SecurityGroups:
        - !Ref ALBExtSecurityGroup
      Subnets: !Ref PublicSubnetIds
      IpAddressType: ipv4
      Tags:
        - Key: service
          Value: api
  ServiceListenerExternal:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      Certificates:
        - CertificateArn: !Ref CertificateArn
      DefaultActions:
        - Type: fixed-response
          FixedResponseConfig:
            StatusCode: 404
      LoadBalancerArn:
        Ref: APILoadBalancer
      Port: 443
      Protocol: HTTPS
      SslPolicy: 'ELBSecurityPolicy-TLS-1-2-2017-01'
  ServiceListenerExternalCertificatesList:
    Condition: CreateRootCertificate
    DependsOn:
      - ServiceListenerExternal
    Type: AWS::ElasticLoadBalancingV2::ListenerCertificate
    Properties:
      Certificates:
        - CertificateArn: !Ref RootCertificateArn
      ListenerArn: !Ref ServiceListenerExternal
  ##########################################
  APIIntDNSPrivate:
    Type: AWS::Route53::RecordSetGroup
    Properties:
      HostedZoneId: !Ref PrivateHostedZone
      RecordSets:
      - Name: !Join ['', ['api-int', '.', !Ref DNSName, .]]
        Type: CNAME
        TTL: 300
        ResourceRecords:
        - !GetAtt APIIntLoadBalancer.DNSName
  APIIntDNSPublic:
    Type: AWS::Route53::RecordSetGroup
    Properties:
      HostedZoneId: !Ref PublicHostedZone
      RecordSets:
      - Name: !Join ['', ['api-int', '.', !Ref DNSName, .]]
        Type: CNAME
        TTL: 300
        ResourceRecords:
        - !GetAtt APIIntLoadBalancer.DNSName
  APIIntLoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Scheme: internal
      SecurityGroups:
        - !Ref ALBIntSecurityGroup
      Subnets: !Ref PrivateSubnetIds
      IpAddressType: ipv4
      Tags:
        - Key: service
          Value: api-int
  ServiceListenerInternal:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      Certificates:
        - CertificateArn: !Ref CertificateArn
      DefaultActions:
        - Type: fixed-response
          FixedResponseConfig:
            StatusCode: 404
      LoadBalancerArn:
        Ref: APIIntLoadBalancer
      Port: 443
      Protocol: HTTPS
      SslPolicy: 'ELBSecurityPolicy-TLS-1-2-2017-01'
  ServiceListenerInternalCertificatesList:
    Condition: CreateRootCertificate
    DependsOn:
      - ServiceListenerInternal
    Type: AWS::ElasticLoadBalancingV2::ListenerCertificate
    Properties:
      Certificates:
        - CertificateArn: !Ref RootCertificateArn
      ListenerArn: !Ref ServiceListenerInternal
