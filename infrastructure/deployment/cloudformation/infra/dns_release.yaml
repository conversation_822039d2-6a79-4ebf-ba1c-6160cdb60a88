Description: 'DNS endpoints to enable the production deployment'
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Basic
      Parameters:
      - DeploymentDNSDomain
      - ProductionDNSDomain
      - PrivateHostedZone
      - PublicHostedZone
      - Name
Parameters:
  DeploymentDNSDomain:
    Type: String
    Default: us
    AllowedValues:
      - us
      - jp
      - staging
  ProductionDNSDomain:
    Type: String
    Default: ariadirect.com
  PrivateHostedZone:
    Type: "AWS::Route53::HostedZone::Id"
    Description: Select private hosted zone
  PublicHostedZone:
    Type: "AWS::Route53::HostedZone::Id"
    Description: Select public hosted zone
  Name:
    Type: String
    Default: api
Resources:
  MyDNSPublic:
    Type: "AWS::Route53::RecordSetGroup"
    Properties:
      HostedZoneId: !Ref 'PublicHostedZone'
      RecordSets:
      - Name: !Join ['', [!Ref 'Name', '.', !Ref 'ProductionDNSDomain', .]]
        Type: CNAME
        TTL: '300'
        ResourceRecords:
        - !Join ['', [!Ref 'Name', '.', !Ref 'DeploymentDNSDomain', '.', !Ref 'ProductionDNSDomain', .]]
  MyDNSPrivate:
    Type: "AWS::Route53::RecordSetGroup"
    Properties:
      HostedZoneId: !Ref 'PrivateHostedZone'
      RecordSets:
      - Name: !Join ['', [!Ref 'Name', '.', !Ref 'ProductionDNSDomain', .]]
        Type: CNAME
        TTL: '300'
        ResourceRecords:
        - !Join ['', [!Ref 'Name', '.', !Ref 'DeploymentDNSDomain', '.', !Ref 'ProductionDNSDomain', .]]
