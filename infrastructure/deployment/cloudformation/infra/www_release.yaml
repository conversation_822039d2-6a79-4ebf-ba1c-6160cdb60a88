Description: 'Cloudfront DNS endpoint to www production deployment'
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Basic
      Parameters:
      - PrivateHostedZone
      - PublicHostedZone
      - Version
Parameters:
  PrivateHostedZone:
    Type: "AWS::Route53::HostedZone::Id"
    Description: Select private hosted zone
  PublicHostedZone:
    Type: "AWS::Route53::HostedZone::Id"
    Description: Select public hosted zone
  Version:
    Type: String
Resources:
  MyDNSPublic:
    Type: "AWS::Route53::RecordSetGroup"
    Properties:
      HostedZoneId: !Ref 'PublicHostedZone'
      RecordSets:
      - Name: www.ariadirect.com
        Type: CNAME
        TTL: '300'
        ResourceRecords:
        - !Join ['', [!Ref 'Version', '.ariadirect.com', .]]
  MyDNSPrivate:
    Type: "AWS::Route53::RecordSetGroup"
    Properties:
      HostedZoneId: !Ref 'PrivateHostedZone'
      RecordSets:
      - Name: www.ariadirect.com
        Type: CNAME
        TTL: '300'
        ResourceRecords:
        - !Join ['', [!Ref 'Version', '.ariadirect.com', .]]
