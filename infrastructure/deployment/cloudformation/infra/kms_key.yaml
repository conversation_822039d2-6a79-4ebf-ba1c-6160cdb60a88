AWSTemplateFormatVersion: "2010-09-09"
Description: KMS key for parameter store config
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Default
      Parameters:
      - Env
    - Label:
        default: Advanced
      Parameters:
      - AliasName
      - ECSInstanceRole
Parameters:
  Env:
    Description: AWS environment name
    Type: String
    Default: produs
    AllowedValues:
      - produs
      - prodjp
      - stagingus
  AliasName:
    Description: The KMS key alias name
    Type: String
    Default: ad_kms_key_for_config
  ECSInstanceRole:
    Description: ECSInstanceRole user
    Type: String
    Default: arn:aws:iam::853431205376:role/ad-roles-ECSInstanceRole-SGOTTMRRC6WC
Resources:
  MySecretsKey:
    Type: "AWS::KMS::Key"
    Properties:
      Description: "kms key for the parameters and secrets"
      Enabled: true
      EnableKeyRotation: false
      KeyPolicy:
        Version: "2012-10-17"
        Id: "kms_key_for_config"
        Statement:
          -
            Sid: "Allow full administration of the key"
            Effect: "Allow"
            Principal:
              AWS:
                - !Sub "arn:aws:iam::${AWS::AccountId}:user/michael.mao"
                - !Sub "arn:aws:iam::${AWS::AccountId}:user/lu.min"
                - !Sub "arn:aws:iam::${AWS::AccountId}:user/yingyi.hu"
                - !Ref ECSInstanceRole
            Action:
              - "kms:*"
            Resource: "*"
          -
            Sid: "Allow read only usage"
            Effect: "Allow"
            Principal:
              AWS:
                - !Sub "arn:aws:iam::${AWS::AccountId}:user/lu.min"
                - !Sub "arn:aws:iam::${AWS::AccountId}:user/yingyi.hu"
            Action:
              - "kms:Decrypt"
              - "kms:DescribeKey"
            Resource: "*"
      Tags:
        -
          Key: "Name"
          Value: "AD KMS key for config"
        -
          Key: "Env"
          Value:
            Ref: "Env"
  MySecretsKeyAlias:
    Type: "AWS::KMS::Alias"
    Properties:
      AliasName: !Join [ "/", [ "alias", !Ref AliasName ]]
      TargetKeyId: !Ref MySecretsKey
