AWSTemplateFormatVersion: '2010-09-09'
Description: 'AWS CloudFormation IAM Admins'
Parameters:
  Env:
    Description: "AWS environment name"
    Type: String
    Default: produs
    AllowedValues:
      - produs
      - pordjp
      - stagingus
Resources:
  AdministratorsGroup:
    Type: "AWS::IAM::Group"
    Properties:
      GroupName: "administrators"
      ManagedPolicyArns: [ "arn:aws:iam::aws:policy/AdministratorAccess" ]
      Path: "/"
  PowerUsersGroup:
    Type: "AWS::IAM::Group"
    Properties:
      GroupName: "powerusers"
      ManagedPolicyArns: [ "arn:aws:iam::aws:policy/PowerUserAccess" ]
      Path: "/"
  UserYingyi:
    Type: "AWS::IAM::User"
    Properties:
      Groups:
        - !Ref PowerUsersGroup
      Path: "/"
      UserName: "yingyi.hu"
  UserLu:
    Type: "AWS::IAM::User"
    Properties:
      Groups:
        - !Ref PowerUsersGroup
      Path: "/"
      UserName: "lu.min"
  UserJas:
    Type: "AWS::IAM::User"
    Properties:
      Groups:
        - !Ref PowerUsersGroup
      Path: "/"
      UserName: "jas.shou"