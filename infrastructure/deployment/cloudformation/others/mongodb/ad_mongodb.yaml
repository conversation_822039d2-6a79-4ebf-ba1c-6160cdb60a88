AWSTemplateFormatVersion: 2010-09-09
Description: This template is used for setting up a single mongodb node
Parameters:

  Env:
    Type: String
    Description: The environment tag is used to designate the Environment Stage of the associated AWS resource.
    Default: prod_us
    AllowedValues:
      - prod_us
      - prod_jp
      - staging_us
  VpcID:
    Type: 'AWS::EC2::VPC::Id'
    Description: VPC ID where you want to depoy MongoDB cluster.
    Default: 'vpc-0d9e25bc47eca4a94'
  VpcCIDR:
    Type: String
    Description: Current VPC CIDR
    MinLength: 9
    MaxLength: 18
    AllowedPattern: "(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})/(\\d{1,2})"
    Default: '**********/16'
  SubnetId:
    Type: 'AWS::EC2::Subnet::Id'
    Description: 'Select a private subnet'
    Default: 'subnet-0b2df219748605cc3'
  KeyName:
    Type: 'AWS::EC2::KeyPair::KeyName'
    Default: ad-mongodb
    Description: Name of an existing EC2 KeyPair.

  InstanceType:
    Description: Amazon EC2 instance type for the MongoDB nodes.
    Type: String
    Default: m3.large
    AllowedValues:
      - m5.large
      - m5.xlarge
      - m5.2xlarge
      - m5.4xlarge
      - m5.12xlarge
      - m5.24xlarge
      - c5.large
      - c5.xlarge
      - c5.2xlarge
      - c5.4xlarge
      - c5.9xlarge
      - c5.18xlarge
      - x1.16xlarge
      - x1.32xlarge
      - r4.large
      - r4.xlarge
      - r4.2xlarge
      - r4.4xlarge
      - r4.8xlarge
      - r4.16xlarge
      - i3.large
      - i3.xlarge
      - i3.2xlarge
      - i3.4xlarge
      - i3.8xlarge
      - i3.16xlarge
      - x1e.32xlarge
      - x1e.16xlarge
      - x1e.8xlarge
      - x1e.4xlarge
      - x1e.2xlarge
      - x1e.xlarge
      - m3.medium
      - m3.large
      - m3.xlarge
      - m3.2xlarge
      - m4.large
      - m4.xlarge
      - m4.2xlarge
      - m4.4xlarge
      - m4.10xlarge
      - c3.large
      - c3.xlarge
      - c3.2xlarge
      - c3.4xlarge
      - c3.8xlarge
      - r3.large
      - r3.xlarge
      - r3.2xlarge
      - r3.4xlarge
      - r3.8xlarge
      - i2.xlarge
      - i2.2xlarge
      - i2.4xlarge
      - i2.8xlarge
  VolumeSize:
    Type: String
    Description: EBS Volume Size (data) to be attached to node in GBs
    Default: 100
  VolumeType:
    Type: String
    Description: EBS Volume Type (data) to be attached to node in GBs [io1,gp2]
    Default: gp2
    AllowedValues:
      - gp2
      - io1
  Iops:
    Type: String
    Description: Iops of EBS volume when io1 type is chosen. Otherwise ignored
    Default: '100'

  NodeDnsName:
    Type: String
    Description: 'The DNS name for MongoDB'
    Default: 'mongodb'
  Domain:
    Type: 'String'
    Default: 'ariadirect.com'
  PrivateHostedZone:
    Type: 'AWS::Route53::HostedZone::Id'
    Default: 'Z5RH4IDG4YSU0'
    Description: 'Select private hosted zone'

  MongoDBVersion:
    Description: MongoDB version
    Type: String
    Default: '4.0'
    AllowedValues:
      - '4.0'
      - '3.6'
      - '3.4'
      - '3.2'

Conditions:
  UsePIops: !Equals
    - !Ref VolumeType
    - io1

Mappings:
  AWSAMIRegionMap:
    AMI:
      AMZNLINUX: amzn-ami-hvm-2018.03.0.20190611-x86_64-gp2
    ap-northeast-1:
      AMZNLINUX: ami-04b2d1589ab1d972c
    ap-northeast-2:
      AMZNLINUX: ami-0be3e6f84d3b968cd
    ap-south-1:
      AMZNLINUX: ami-0b99c7725b9484f9e
    ap-southeast-1:
      AMZNLINUX: ami-0fb6b6f9e81056553
    ap-southeast-2:
      AMZNLINUX: ami-075caa3491def750b
    eu-central-1:
      AMZNLINUX: ami-026d3b3672c6e7b66
    eu-west-1:
      AMZNLINUX: ami-0862aabda3fb488b5
    eu-west-2:
      AMZNLINUX: ami-0bdfa1adc3878cd23
    us-east-1:
      AMZNLINUX: ami-035b3c7efe6d061d5
    us-east-2:
      AMZNLINUX: ami-02f706d959cedf892
    us-west-1:
      AMZNLINUX: ami-0fcdcdb074d2bac5f
    us-west-2:
      AMZNLINUX: ami-0f2176987ee50226e

Resources:

###############################################################################
# Role
###############################################################################

  MongoDBNodeIAMRole:
    Type: 'AWS::IAM::Role'
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - ec2.amazonaws.com
            Action:
              - 'sts:AssumeRole'
      Path: /
      Policies:
        - PolicyName: ad-mongodb-policy
          PolicyDocument:
            Statement:
              - Effect: Allow
                Action:
                  - 's3:GetObject'
                  - 's3:GetBucketAcl'
                  - 's3:GetBucketLocation'
                  - 's3:ListBucket'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'ec2:Describe*'
                  - 'ec2:AttachNetworkInterface'
                  - 'ec2:AttachVolume'
                  - 'ec2:CreateTags'
                  - 'ec2:CreateVolume'
                  - 'ec2:RunInstances'
                  - 'ec2:StartInstances'
                  - 'ec2:DeleteVolume'
                  - 'ec2:CreateSecurityGroup'
                  - 'ec2:CreateSnapshot'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'dynamodb:BatchGetItem'
                  - 'dynamodb:CreateTable'
                  - 'dynamodb:DeleteTable'
                  - 'dynamodb:DescribeTable'
                  - 'dynamodb:GetItem'
                  - 'dynamodb:PutItem'
                  - 'dynamodb:Query'
                  - 'dynamodb:Scan'
                  - 'dynamodb:UpdateItem'
                  - 'dynamodb:UpdateTable'
                Resource:
                  - !Sub >-
                    arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*MONGODB_*

  MongoDBNodeIAMProfile:
    Type: 'AWS::IAM::InstanceProfile'
    Properties:
      Path: /
      Roles:
        - !Ref MongoDBNodeIAMRole

###############################################################################
# Security
###############################################################################

  MongoDBServerSecurityGroup:
    Type: 'AWS::EC2::SecurityGroup'
    Properties:
      GroupDescription: MongoDB server management and access ports
      VpcId: !Ref VpcID

  MongoDBServerSecurityGroupIngress1:
    Type: 'AWS::EC2::SecurityGroupIngress'
    Properties:
      GroupId: !GetAtt 'MongoDBServerSecurityGroup.GroupId'
      IpProtocol: -1
      SourceSecurityGroupId: !GetAtt MongoDBServerSecurityGroup.GroupId
      Description: 'Self Reference'

  MongoDBServerSecurityGroupIngress2:
    Type: 'AWS::EC2::SecurityGroupIngress'
    Properties:
      GroupId: !GetAtt 'MongoDBServerSecurityGroup.GroupId'
      IpProtocol: tcp
      FromPort: 28017
      ToPort: 28017
      CidrIp: !Ref VpcCIDR
      Description: 'Self VPC CIDR'

  MongoDBServerSecurityGroupIngress3:
    Type: 'AWS::EC2::SecurityGroupIngress'
    Properties:
      GroupId: !GetAtt 'MongoDBServerSecurityGroup.GroupId'
      IpProtocol: tcp
      FromPort: 27017
      ToPort: 27030
      CidrIp: !Ref VpcCIDR
      Description: 'Self VPC CIDR'

  MongoDBServerSecurityGroupIngress4:
    Type: 'AWS::EC2::SecurityGroupIngress'
    Properties:
      GroupId: !GetAtt 'MongoDBServerSecurityGroup.GroupId'
      IpProtocol: tcp
      FromPort: 22
      ToPort: 22
      CidrIp: !Ref VpcCIDR
      Description: 'Self VPC CIDR'

  MongoDBNameRecord:
    DependsOn: NodeInstance
    Type: AWS::Route53::RecordSet
    Properties:
      HostedZoneId: !Ref PrivateHostedZone
      Comment: DNS name for my instance.
      Name: !Join ['', [!Ref 'NodeDnsName', '.', !Ref 'Domain', .]]
      Type: A
      TTL: '900'
      ResourceRecords:
      - !GetAtt NodeInstance.PrivateIp

  NodeInstance:
    Type: 'AWS::EC2::Instance'
    Properties:
      DisableApiTermination:  true
      ImageId: !FindInMap
        - AWSAMIRegionMap
        - !Ref 'AWS::Region'
        - AMZNLINUX
      InstanceType: !Ref InstanceType
      IamInstanceProfile: !Ref MongoDBNodeIAMProfile
      KeyName: !Ref KeyName
      Monitoring: true
      NetworkInterfaces:
        - DeviceIndex: '0'
          Description: Network Interface for MongoDB Node
          SubnetId: !Ref SubnetId
          GroupSet:
            - !Ref MongoDBServerSecurityGroup
          AssociatePublicIpAddress: false
          DeleteOnTermination: true
      BlockDeviceMappings:
        - DeviceName: /dev/xvdg
          Ebs:
            VolumeType: io1
            DeleteOnTermination: true
            VolumeSize: 25
            Iops: 250
            Encrypted: true
        - DeviceName: /dev/xvdh
          Ebs:
            VolumeType: io1
            DeleteOnTermination: true
            VolumeSize: 25
            Iops: 200
            Encrypted: true
        - DeviceName: /dev/xvdf
          Ebs:
            VolumeSize: !Ref VolumeSize
            VolumeType: !Ref VolumeType
            Iops: !If
              - UsePIops
              - !Ref Iops
              - !Ref 'AWS::NoValue'
            DeleteOnTermination: true
            Encrypted: true
      Tags:
        -
          Key: 'Name'
          Value: 'ad mongodb 4.0'
        -
          Key: 'Env'
          Value:
            Ref: 'Env'
      UserData:
        Fn::Base64: !Sub |
          #!/usr/bin/env bash

          /opt/aws/bin/cfn-init -v --stack ${AWS::StackName} --resource NodeInstance --region ${AWS::Region}

          sudo /bin/mkdir -p /home/<USER>/mongodb
          cd /home/<USER>/mongodb

          aws s3 cp s3://ariadirect-prod-infrastructure/disable-transparent-hugepages disable-transparent-hugepages
          aws s3 cp s3://ariadirect-prod-infrastructure/init_mangodb.sh init_mangodb.sh

          sudo cp disable-transparent-hugepages /etc/init.d/disable-transparent-hugepages

          sudo chmod +x /etc/init.d/disable-transparent-hugepages
          sudo chkconfig --add disable-transparent-hugepages

          sudo chmod +x init_mangodb.sh

          sudo ./init_mangodb.sh > /tmp/install.log 2>&1 \n

          chown -R ec2-user:ec2-user /home/<USER>/
          /opt/aws/bin/cfn-signal -e 0 --stack ${AWS::StackName} --resource NodeInstance --region ${AWS::Region}

Outputs:
  NodeDnsName:
    Description: MongoDB dnsname
    Value: !Join ['', [!Ref 'NodeDnsName', '.', !Ref 'Domain', .]]
  NodePrivateIp:
    Description: Private IP Address of Node Instance
    Value: !GetAtt
      - NodeInstance
      - PrivateIp
  NodeInstanceID:
    Description: The Instance ID
    Value: !Ref NodeInstance

