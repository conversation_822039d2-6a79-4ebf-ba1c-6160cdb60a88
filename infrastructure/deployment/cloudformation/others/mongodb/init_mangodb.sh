#!/usr/bin/env bash

MongoDBVersion="4.0"

/bin/mkdir -p /home/<USER>/mongodb
cd /home/<USER>/mongodb

export MongoDBVersion=${MongoDBVersion} >> config.sh

#################################################################
# Update the OS, install packages, initialize environment vars,
# and get the instance tags
#################################################################
yum -y update
yum install -y jq
yum install -y xfsprogs

# MongoDBVersion set inside config.sh
version=${MongoDBVersion}

if [ -z "$version" ] ; then
  version="3.6"
fi

echo "[mongodb-org-${version}]
name=MongoDB Repository
baseurl=http://repo.mongodb.org/yum/amazon/2013.03/mongodb-org/${version}/x86_64/
gpgcheck=0
enabled=1" > /etc/yum.repos.d/mongodb-org-${version}.repo

# To be safe, wait a bit for flush
sleep 5

yum --enablerepo=epel install node npm -y

yum install -y mongodb-org
yum install -y munin-node
yum install -y libcgroup
yum -y install mongo-10gen-server mongodb-org-shell
yum -y install sysstat

#################################################################
# Make filesystems, set ulimits and block read ahead on ALL nodes
#################################################################
mkfs.xfs -f /dev/xvdf
echo "/dev/xvdf /data xfs defaults,auto,noatime,noexec 0 0" | tee -a /etc/fstab
mkdir -p /data
mount /data
chown -R mongod:mongod /data
blockdev --setra 32 /dev/xvdf
rm -rf /etc/udev/rules.d/85-ebs.rules
touch /etc/udev/rules.d/85-ebs.rules
echo 'ACTION=="add", KERNEL=="'$1'", ATTR{bdi/read_ahead_kb}="16"' | tee -a /etc/udev/rules.d/85-ebs.rules
echo "* soft nofile 64000
* hard nofile 64000
* soft nproc 32000
* hard nproc 32000" > /etc/limits.conf
#################################################################
# End All Nodes
#################################################################


#################################################################
# Listen to all interfaces, not just local
#################################################################

enable_all_listen() {
  for f in /etc/mongo*.conf
  do
    sed -e '/bindIp/s/^/#/g' -i ${f}
    sed -e '/bind_ip/s/^/#/g' -i ${f}
    echo " Set listen to all interfaces : ${f}"
  done
}

#################################################################
# Setup MongoDB servers and config nodes
#################################################################
mkdir /var/run/mongod
chown mongod:mongod /var/run/mongod

echo "net:" > mongod.conf
echo "  port:" >> mongod.conf
if [ "$version" == "3.6" ] || [ "$version" == "4.0" ]; then
    echo "  bindIpAll: true" >> mongod.conf
fi
echo "" >> mongod.conf
echo "systemLog:" >> mongod.conf
echo "  destination: file" >> mongod.conf
echo "  logAppend: true" >> mongod.conf
echo "  path: /log/mongod.log" >> mongod.conf
echo "" >> mongod.conf
echo "storage:" >> mongod.conf
echo "  dbPath: /data" >> mongod.conf
echo "  journal:" >> mongod.conf
echo "    enabled: true" >> mongod.conf
echo "" >> mongod.conf
echo "processManagement:" >> mongod.conf
echo "  fork: true" >> mongod.conf
echo "  pidFilePath: /var/run/mongod/mongod.pid" >> mongod.conf


#################################################################
#  Enable munin plugins for iostat and iostat_ios
#################################################################
ln -s /usr/share/munin/plugins/iostat /etc/munin/plugins/iostat
ln -s /usr/share/munin/plugins/iostat_ios /etc/munin/plugins/iostat_ios
touch /var/lib/munin/plugin-state/iostat-ios.state
chown munin:munin /var/lib/munin/plugin-state/iostat-ios.state

#################################################################
# Make the filesystems, add persistent mounts
#################################################################
mkfs.xfs -f /dev/xvdg
mkfs.xfs -f /dev/xvdh

echo "/dev/xvdg /journal xfs defaults,auto,noatime,noexec 0 0" | tee -a /etc/fstab
echo "/dev/xvdh /log xfs defaults,auto,noatime,noexec 0 0" | tee -a /etc/fstab

#################################################################
# Make directories for data, journal, and logs
#################################################################
mkdir -p /journal
mount /journal

#################################################################
#  Figure out how much RAM we have and how to slice it up
#################################################################
memory=$(vmstat -s | grep "total memory" | sed -e 's/ total.*//g' | sed -e 's/[ ]//g' | tr -d '\n')
memory=$(printf %.0f $(echo "${memory} / 1024 / 1 * .9 / 1024" | bc))

if [ ${memory} -lt 1 ]; then
    memory=1
fi

#################################################################
#  Make data directories and add symbolic links for journal files
#################################################################

mkdir -p /data/
mkdir -p /journal/

  # Add links for journal to data directory
ln -s /journal/ /data/journal

mkdir -p /log
mount /log

#################################################################
# Change permissions to the directories
#################################################################
chown -R mongod:mongod /journal
chown -R mongod:mongod /log
chown -R mongod:mongod /data

#################################################################
# Clone the mongod config file and create cgroups for mongod
#################################################################
c=0
port=27017

cp mongod.conf /etc/mongod.conf
sed -i "s/.*port:.*/  port: ${port}/g" /etc/mongod.conf

echo CGROUP_DAEMON="memory:mongod" > /etc/sysconfig/mongod

echo "mount {
    cpuset  = /cgroup/cpuset;
    cpu     = /cgroup/cpu;
    cpuacct = /cgroup/cpuacct;
    memory  = /cgroup/memory;
    devices = /cgroup/devices;
  }

  group mongod {
    perm {
      admin {
        uid = mongod;
        gid = mongod;
      }
      task {
        uid = mongod;
        gid = mongod;
      }
    }
    memory {
      memory.limit_in_bytes = ${memory}G;
      }
  }" > /etc/cgconfig.conf

#################################################################
#  Start cgconfig, munin-node, and all mongod processes
#################################################################
chkconfig cgconfig on
service cgconfig start

chkconfig munin-node on
service munin-node start

chkconfig mongod on
if [ "$version" != "3.6" ] && [ "$version" != "4.0" ];  then
    enable_all_listen
fi

service mongod start