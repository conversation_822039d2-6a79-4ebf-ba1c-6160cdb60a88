AWSTemplateFormatVersion: 2010-09-09
Description: EC2 for Redmine
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Default
      Parameters:
      - InstanceType
      - KeyName
      - PostgresPassword
      - PostgresUser
      - PublicHostedZone
      - SubnetId
      - SSHRuleCIDR
      - VpcID
Parameters:
  InstanceType:
    Type: String
    Description: EC2 instance class
    Default: m5.large
    AllowedValues:
      - t2.medium
      - t2.large
      - m5.large
      - m5.xlarge
    ConstraintDescription: Please choose a valid instance type.
  KeyName:
    Type: AWS::EC2::KeyPair::KeyName
    Description: Name of an existing EC2 KeyPair to enable SSH access to the ECS instances.
    Default: ad-redmine
  PostgresPassword:
    Type: String
    Description: Postgres database password
    Default: postgres
    NoEcho: true
  PostgresUser:
    Type: String
    Description: Postgres database username
    Default: postgres
  PublicHostedZone:
    Type: AWS::Route53::HostedZone::Id
    Description: Select public hosted zone of ariadirect.com
    Default: Z3O7TBLZGRNSLJ
  SubnetId:
    Type: AWS::EC2::Subnet::Id
    Description: Select a public subnet
    Default: subnet-0389b08815a580651
  SSHRuleCIDR:
    Type: String
    Description: CIDR block for ssh rule
    Default: "**********/16"
  VpcID:
    Type: AWS::EC2::VPC::Id
    Description: Select a VPC that allows instances access to the Internet.
    Default: vpc-0d9e25bc47eca4a94
Mappings:
  AWSRegionToAMI:
    us-east-1:
      AMIID: ami-0732fb78fd652fa82
    us-west-2:
      AMIID: ami-a0cfeed8
    ap-northeast-1:
      AMIID: ami-0dbb0434b193a58db
Resources:
  MyEC2Instance:
    Type: AWS::EC2::Instance
    Properties:
      KeyName: !Ref KeyName
      InstanceType: !Ref InstanceType
      DisableApiTermination: true
      BlockDeviceMappings:
        - DeviceName: /dev/sda1
          Ebs:
            VolumeType: gp2
            VolumeSize: 50
            DeleteOnTermination: false
      NetworkInterfaces:
        - AssociatePublicIpAddress: true
          DeviceIndex: "0"
          GroupSet:
            - !GetAtt MySecurityGroup.GroupId
          SubnetId: !Ref SubnetId
      ImageId: !FindInMap [AWSRegionToAMI, !Ref "AWS::Region", AMIID]
      Tags:
        -
          Key: "Name"
          Value: "Redmine"

      UserData:
        Fn::Base64: !Sub |
          #!/usr/bin/env bash

      UserData:
        Fn::Base64: !Sub |
          #!/usr/bin/env bash

          # https://hub.docker.com/_/postgres
          # https://hub.docker.com/_/redmine
          # https://www.redmine.org/projects/redmine/wiki/Plugins
          # Redcase https://www.youtube.com/watch?v=VmI93EreB_w

          POSTGRES_PASSWORD="${PostgresPassword}"
          POSTGRES_USER="${PostgresUser}"

          sudo yum update -y
          sudo yum install -y git aws-cli docker && sudo yum -y update --exclude=kernel*

          sudo yum update -y aws-cfn-bootstrap
          /opt/aws/bin/cfn-init -v --stack ${AWS::StackName} --resource MyEC2Instance --region ${AWS::Region}

          sudo service docker start

          sudo mkdir -p /data/postgres
          sudo mkdir -p /data/redmine_files
          sudo mkdir -p /data/redmine_plugins
          sudo chmod -R 777 /data/

          cd /data/redmine_plugins && sudo git clone https://github.com/bugzinga/redcase

          echo "Start postgres container"
          sudo docker run -d \
            --restart=unless-stopped \
            --name=postgres \
            --ulimit nofile=1048576:1048576 \
            -p 5432:5432 \
            -v /data/postgres:/var/lib/postgresql/data \
            -e POSTGRES_USER=$POSTGRES_USER \
            -e POSTGRES_PASSWORD=$POSTGRES_PASSWORD \
            postgres:9.6.17

          echo "Start redmine container"
          sudo docker run -d \
            --restart=unless-stopped \
            --name=redmine \
            --ulimit nofile=1048576:1048576 \
            -p 3000:3000 \
            -v /data/redmine_files:/usr/src/redmine/files \
            -v /data/redmine_plugins:/usr/src/redmine/plugins \
            -e REDMINE_DB_USERNAME=$POSTGRES_USER \
            -e REDMINE_DB_PASSWORD=$POSTGRES_PASSWORD \
            -e REDMINE_DB_POSTGRES=postgres \
            --link postgres:postgres \
            redmine:3.4

          /opt/aws/bin/cfn-signal -e $? --stack ${AWS::StackName} --resource MyEC2Instance --region ${AWS::Region}

  MyEC2InstanceDNS:
    Type: AWS::Route53::RecordSet
    Properties:
      HostedZoneId: !Ref PublicHostedZone
      Name: redmine.ariadirect.com
      Type: A
      TTL: 900
      ResourceRecords:
      - !GetAtt MyEC2Instance.PublicIp
  MyHTTPRule:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !Ref MySecurityGroup
      IpProtocol: tcp
      FromPort: 3000
      ToPort: 3000
      CidrIp: "0.0.0.0/0"
  MySecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security Group
      VpcId: !Ref VpcID
      Tags:
        -
          Key: Name
          Value: Redmine Security Group
  MySSHRule:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !Ref MySecurityGroup
      IpProtocol: tcp
      FromPort: 22
      ToPort: 22
      CidrIp: !Ref SSHRuleCIDR
Outputs:
  MyEC2InstancePublicIPAddress:
    Value: !GetAtt MyEC2Instance.PublicIp
    Description: Public IP address of EC2.
  MyEC2InstancePrivateIPAddress:
    Value: !GetAtt MyEC2Instance.PrivateIp
    Description: Private IP address of EC2.
