AWSTemplateFormatVersion: "2010-09-09"
Description: "DNS health check"
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Default
      Parameters:
        - AlarmSNSTopicArn
        - DNSDomain
        - Env
        - ServiceHealthCheckEndpoint
        - ServiceName
        - ServicePath
        - ServiceVersion
Parameters:
  AlarmSNSTopicArn:
    Description: DNS health check SNS topic arn
    Type: String
  DNSDomain:
    Description: DNS domain name
    Type: String
  Env:
    Description: "AWS environment name"
    Type: String
    Default: produs
    AllowedValues:
      - produs
      - prodjp
      - stagingus
  ServiceHealthCheckEndpoint:
    Description: Service Health check endpoint
    Type: String
    Default: "/status"
  ServiceName:
    Description: Service Name
    Type: String
  ServicePath:
    Description: The API url path for the service
    Type: String
  ServiceVersion:
    Description: The current API version
    Type: String
    Default: v1
Resources:
  HealthCheck:
    Type: 'AWS::Route53::HealthCheck'
    Properties:
      HealthCheckConfig:
        Port: 443
        Type: HTTPS
        ResourcePath: !Join ['', ['/', !Ref ServiceVersion, '/', !Ref ServicePath, !Ref ServiceHealthCheckEndpoint] ]
        FullyQualifiedDomainName: !Join [ '.', ['api', !Ref DNSDomain] ]
        RequestInterval: 30
        FailureThreshold: 3
      HealthCheckTags:
        - Key: Name
          Value: !Join [' ', [!Ref ServiceName, "health check"]]
        - Key: Service
          Value: !Ref ServiceName
  HealthCheckAlarm:
    Type: "AWS::CloudWatch::Alarm"
    Properties:
      AlarmName: !Join [ '-', ['HealthCheckAlarm', !Ref ServiceName, !Ref Env] ]
      ActionsEnabled: true
      Namespace: "AWS/Route53"
      MetricName: HealthCheckStatus
      ComparisonOperator: LessThanThreshold
      Period: 60
      EvaluationPeriods: 1
      Statistic: Minimum
      Threshold: 1.0
      AlarmActions:
        - !Ref AlarmSNSTopicArn
      Dimensions:
        - Name: HealthCheckId
          Value: !Ref HealthCheck
