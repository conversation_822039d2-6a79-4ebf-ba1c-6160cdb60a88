AWSTemplateFormatVersion: 2010-09-09
Description: ECS task definitions for services without path in ECS external ECS cluster
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
    - Label:
        default: Service
      Parameters:
      - CPUReservation
      - MemoryReservation
      - ServiceDesiredCount
      - ServiceHealthCheckEndpoint
      - ServiceImageName
      - ServiceImageTag
      - ServiceName
      - TaskCommand
    - Label:
        default: Basic
      Parameters:
      - CloudwatchLogRetention
      - ClusterName
      - CreateCloudWatch
      - ECRURL
      - ECSExecutionRoleArn
      - Env
      - SSMArn
    - Label:
        default: Default
      Parameters:
      - ContainerPortMapping
      - DeploymentMaximumPercent
      - DeploymentMinimumPercent
      - PlacementStrategyType
      - PlacementStrategyField

Parameters:
  CPUReservation:
    Description: Cpu reservation
    Type: String
    Default: 0
  MemoryReservation:
    Description: Memory reservation
    Type: String
    Default: 64
  ServiceDesiredCount:
    Description: The desired count for the Service
    Type: Number
    Default: 1
  ServiceHealthCheckEndpoint:
    Description: Service Health check endpoint
    Type: String
    Default: "/status"
  ServiceImageName:
    Description: The docker image for the service
    Type: String
  ServiceImageTag:
    Description: The Docker image tag for the service
    Type: String
  ServiceName:
    Description: Service Name
    Type: String
  TaskCommand:
    Type: String
    Description: Comma separated, no space, string of commands passed to the task
  #################################
  CloudwatchLogRetention:
    Description: cloudwatch log retention
    Type: Number
    Default: 7
  ClusterName:
    Description: The cluster name
    Type: String
  CreateCloudWatch:
    Description: Create cloudwatch log group for a task definition
    Type: String
    AllowedValues:
    - true
    - false
    Default: true
  ECRURL:
    Description: The default ECR location
    Type: String
  ECSExecutionRoleArn:
    Description: An IAM Role that allows ECS run task
    Type: String
  Env:
    Description: AWS environment name
    Type: String
    Default: produs
    AllowedValues:
      - produs
      - prodjp
      - stagingus
  SSMArn:
    Description: An ARN for SSM parameters store
    Type: String
  #################################
  ContainerPortMapping:
    Description: Container port
    Type: Number
    Default: 3000
  DeploymentMaximumPercent:
    Description: Deployment configuration maximum percent
    Type: Number
    Default: 200
  DeploymentMinimumPercent:
    Description: Deployment configuration minimum percent
    Type: Number
    Default: 100
  PlacementStrategyType:
    Description: Placement strategy type
    Type: String
    Default: spread
    AllowedValues:
    - spread
    - binpack
    - random
  PlacementStrategyField:
    Description: Placement strategy field
    Type: String
    Default: host

Conditions:
  CreateCloudWatchLG:
    Fn::Equals:
    - Ref: CreateCloudWatch
    - true

Resources:
  CloudwatchLogGroupsService:
    Condition: CreateCloudWatchLG
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Join ['-', ['ad', !Ref ServiceName]]
      RetentionInDays: !Ref CloudwatchLogRetention
  Service:
    Type: AWS::ECS::Service
    Properties:
      ServiceName: !Join ['-', [!Ref ServiceName, 'service']]
      Cluster: !Ref ClusterName
      DesiredCount: !Ref ServiceDesiredCount
      LaunchType: EC2
      TaskDefinition: !Ref ServiceTask
      DeploymentConfiguration:
        MaximumPercent: !Ref DeploymentMaximumPercent
        MinimumHealthyPercent: !Ref DeploymentMinimumPercent
      PlacementStrategies:
        - Type: !Ref PlacementStrategyType
          Field: !Ref PlacementStrategyField
  ServiceTask:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Ref ServiceName
      ExecutionRoleArn: !Ref ECSExecutionRoleArn
      ContainerDefinitions:
        - Name: !Ref ServiceName
          Image: !Join ['', [!Ref ECRURL, '/', !Ref ServiceImageName, ':', !Ref ServiceImageTag]]
          Cpu: !Ref CPUReservation
          MemoryReservation: !Ref MemoryReservation
          Essential: True
          Ulimits:
          - Name: nofile
            SoftLimit: 1048576
            HardLimit: 1048576
          PortMappings:
          - HostPort: 0
            ContainerPort: !Ref ContainerPortMapping
            Protocol: tcp
          Command: !Split [",", !Ref TaskCommand]
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Join ['-', ['ad', !Ref ServiceName]]
              awslogs-region: !Ref 'AWS::Region'
              awslogs-stream-prefix: !Ref ServiceImageTag
          Secrets:
          - Name: "ad_aws"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_aws']]
          - Name: "ad_api_token"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_api_token']]
          - Name: "ad_authorize"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_authorize']]
          - Name: "ad_db"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_db']]
          - Name: "ad_email"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_email']]
          - Name: "ad_es"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_es']]
          - Name: "ad_fedex"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_fedex']]
          - Name: "ad_onepay"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_onepay']]
          - Name: "ad_package_service"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_package_service']]
          - Name: "ad_payment_service"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_payment_service']]
          - Name: "ad_s3"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_s3']]
          - Name: "ad_secrets"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_secrets']]
          - Name: "ad_ses"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_ses']]
          - Name: "ad_sqs"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_sqs']]
          - Name: "ad_submit_services"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_submit_services']]
          - Name: "ad_user_account_service"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_user_account_service']]
          - Name: "ad_watchdog_service"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_watchdog_service']]
          - Name: "ad_website"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_website']]
          - Name: "ad_wechatpay"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_wechatpay']]
          - Name: "ad_packer_service"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_packer_service']]
          - Name: "ad_paypal"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_paypal']]
          - Name: "ad_zellepay"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_zellepay']]
          - Name: "ad_apple"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_apple']]
          - Name: "ad_stripe"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_stripe']]
          - Name: "ad_document_template"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_document_template']]
          - Name: "ad_endpoint"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_endpoint']]
          - Name: "ad_walgreen"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_walgreen']]
          - Name: "ad_firebase"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_firebase']]
          - Name: "ad_meilisearch"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_meilisearch']]
          - Name: "ad_env"
            ValueFrom: !Join ['', [!Ref SSMArn, '/', !Ref Env, '/', 'ad_env']]
    
