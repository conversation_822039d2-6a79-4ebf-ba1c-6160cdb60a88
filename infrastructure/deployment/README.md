# Ansible Infrastructure and Service Deployment

AriaDirect cloud product deployment process follows the principle of **`infrastructure as code`**, where we heavily
leverage descriptive tools, such as **`ansible`** and **`cloudformation`** to code more versatile and adaptive
provisioning and deployment processes. With `infrastructure as code` in mind and in code, we are going to gain the
following benefits:

- Improve the velocity of deployment for **`infrastructure`**, **`data layer`** and **`services`**
- Allow to deploy any individual AWS cloud resource
- Allow to deploy individual AriaDirect `service` by
    - sha
    - desired count
    - CPU
    - Mem
    - ... almost all `parameters`


## Directory layout

The top level of the **infrastructure/ansible** directory contain files and directories like so:

```
inventory/                  # directory containing inventory file per environment
    prodjp
        group_vars/         # directory containing variables per environment
    produs
        group_vars/         # directory containing variables per environment


roles/
    data/                   # this represents a custom role hierarchy
        tasks/              # contains data tasks
    infra/  
        tasks/              # contains infra tasks
    service/
        tasks/              # contains service tasks

prodjp_data.yml             # playbook files containing mapping between inventory files and roles to apply onto this host
prodjp_infra.yml
prodjp_service.yml
produs_data.yml
produs_infra.yml
produs_service.yml

ansible.cfg                 # specific Ansible configuration file
```

## Playbooks

Per each environment, we define three roles:
* **infra**:    AWS native resources, stateless
  - VPC
  - Load Balancers
  - SQS
  - ECS cluster
  - IAM
  ...
* **data**:  AWS native resources, stateful
  - RDS
  - Redis
  - S3
  - ElasticSearch
  ...

* **service**:  AriaDirect services

In order to deploy playbook one has to have api access to AWS specific environment. All the environment specific
variables are kept in `inventory/<env_name>/group_vars/<env_name>`, if you want to apply new configuration just
change those parameters.

#### Running infra playbook

```markdown
cd ~/infrastructure/deployment/ansible
AWS_PROFILE=jp ansible-playbook -i inventory/prodjp/prodjp prodjp_infra.yml
```

#### Running data playbook

```markdown
cd ~/infrastructure/deployment/ansible
AWS_PROFILE=jp ansible-playbook -i inventory/prodjp/prodjp prodjp_data.yml
```

#### Running service playbook

```markdown
cd ~/infrastructure/deployment/ansible
AWS_PROFILE=jp ansible-playbook -i inventory/prodjp/prodjp prodjp_service.yml
```
**Note**: `AWS_PROFILE` is your local aws profile

**WARNING**: Please be very careful when you are running `infra` and `data` playbooks. Always pull the latest code!


## Service Manifests


The `manifests` folder includes each individual folder per environment
```markdown
$ tree -L 2
.
├── prodjp
│   ├── external.yaml                       <--- each manifest groups services
├── produs
    └── external.yaml
```

Each yaml file represents a application group, which is very flexible to be organized by developers. The yaml file
contains the repeated blocks for each single service:

```markdown
external_services:
  services:
    atlas:
      tag: "abcdefgh"                   <--- to overwrite the default ECR tag
      desired_count: 1
      image_name: atlas
      cpu_reservation: 0
      memory_reservation: 128
      task_command: ./atlas,--account-service-config,ad_user_account_service,--ad_secrets,ad_secrets
                                        <--- Comma separated, no space, string of commands passed to the task
      service_path: atlas               <--- URL path, api.jp.ariadirect.com/api
    atlas-worker:
      desired_count: 1                  <--- use the default ECR tag
      image_name: package
      cpu_reservation: 0
      memory_reservation: 256
      task_command: python,atlas.py
```

Few other parameters are also supported, with the default values:
* entry_point_name
* health_check_endpoint
* service_version






