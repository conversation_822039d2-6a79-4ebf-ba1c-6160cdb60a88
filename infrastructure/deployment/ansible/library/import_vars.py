#!/usr/bin/env python3

import os
import yaml
from ansible.module_utils.basic import *

ALLOWED_EXTENSIONS = ["yaml", "yml"]


def merge_dicts(a, b, path=None):
    "merges b into a"
    if path is None:
        path = []
    for key in b:
        if key in a:
            if isinstance(a[key], dict) and isinstance(b[key], dict):
                merge_dicts(a[key], b[key], path + [str(key)])
            elif a[key] == b[key]:
                pass  # same leaf value
            else:
                raise Exception('Conflict at %s' % '.'.join(path + [str(key)]))
        else:
            a[key] = b[key]
    return a


def main():
    module = AnsibleModule(
        argument_spec={
            "dir": {
                "type": "str",
                "required": True
            }
        }
    )

    directory = module.params['dir']

    # make some validation
    if not os.path.exists(directory):
        module.fail_json(msg="Directory {} doesn't exist".format(directory))

    if not os.path.isdir(directory):
        module.fail_json(msg="Directory {} is not a directory".format(directory))

    if len(os.listdir(directory)) == 0:
        module.exit_json(changed=False)

    result = {}
    for file in os.listdir(directory):
        # skip hidden files
        if file[0] == ".":
            continue

        if file.split(".")[-1] not in ALLOWED_EXTENSIONS:
            module.fail_json(msg="File {} has wrong extension".format(file))

        with open(os.path.join(directory, file)) as f:
            var_file = yaml.load(f.read(), Loader=yaml.SafeLoader)
            merge_dicts(result, var_file)

    module.exit_json(ansible_facts={'manifest_vars': result}, changed=True)


if __name__ == '__main__':
    main()
