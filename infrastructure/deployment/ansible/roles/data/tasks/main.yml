---
- name: validate require vars to include vars
  fail:
    msg: "Variable {{ item.key }} is not defined or empty"
  when: item.value|length == 0 or item.value is undefined
  with_dict:
    env: "{{ env }}"
    action: "{{ action }}"
    aws_region: "{{ aws_region }}"

- name: deploy stack
  import_tasks: deploy.yaml
  when:
  - action == "deploy"

- name: purge stack
  import_tasks: purge.yaml
  when:
  - action == "purge"