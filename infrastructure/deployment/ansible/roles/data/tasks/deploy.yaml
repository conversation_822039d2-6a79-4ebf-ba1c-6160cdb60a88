---
- name: collect SSM secrets
  import_tasks: ssm.yaml
  tags:
    - ssm

- name: ssm ansible mapping
  set_fact:
    aurora_earth_username: "{{ ssm['db_username'] }}"
    aurora_earth_password: "{{ ssm['db_password'] }}"

- name: check ssm mapping
  debug: msg="{{ aurora_earth_username }}"

- name: run rds aurora
  cloudformation:
    stack_name: "ad-aurora-earth-{{ env | lower }}"
    state: present
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/data/aurora.yaml"
    template_parameters:
      AvailabilityZone: "{{ aurora_earth_availability_zone }}"
      DBSubnets: "{{ private_subnets | join(',') }}"
      DNSDomain: "{{ dns_domain }}"
      DNSName: "{{ aurora_earth_db_dns_name }}"
      Env: "{{ env }}"
      PrivateHostedZone: "{{ private_hosted_zone }}"
      PublicHostedZone: "{{ public_hosted_zone }}"
      VpcCIDR: "{{ vpc_cidr }}"
      VpcID: "{{ vpc_id }}"
      DBBackupRetention: "{{ aurora_earth_backup_retention }}"
      DBBackupWindow: "{{ aurora_earth_backup_window }}"
      DBEngine: "{{ aurora_earth_engine }}"
      DBEngineVersion: "{{ aurora_earth_engine_version }}"
      DBInstanceClass: "{{ aurora_earth_instance_type }}"
      DBName: "{{ aurora_earth_db_name }}"
      DBPassword: "{{ aurora_earth_password }}"
      DBPort: "{{ aurora_earth_port }}"
      DBMaintenanceWindow: "{{ aurora_earth_maintenance_window }}"
      DBSnapshotName: "{{ aurora_earth_snapshot_identifier }}"
      DBUsername: "{{ aurora_earth_username }}"
      IsReplica: "false"
  vars:
    private_subnets:
      - "{{ private_subnet_1a }}"
      - "{{ private_subnet_1b }}"
      - "{{ private_subnet_1c }}"
  register: rds
  tags:
    - rds

- name: run general s3 buckets
  cloudformation:
    state: present
    region: "{{ aws_region }}"
    stack_name: "ad-s3-{{ item.key | lower }}-{{ env | lower }}"
    template: "{{ cf_directory }}/data/s3_bucket.yaml"
    template_parameters:
      Env: "{{ env }}"
      S3BucketName: "{{ item.value }}"
      VersioningEnabled: "Suspended"
  with_dict:
    passport-images: "ad-{{ env | lower }}-passport-images-{{ aws_region | lower }}"
    applications:  "ad-{{ env | lower }}-applications-{{ aws_region | lower }}"
    notification-events: "ad-{{ env | lower }}-notification-events-{{ aws_region | lower }}"
    visa-forms: "ad-{{ env | lower }}-visa-forms-{{ aws_region | lower }}"
    document-template: "ad-{{ env | lower }}-document-template-{{ aws_region | lower }}"
    ai-service: "ad-{{ env | lower }}-ai-service-{{ aws_region | lower }}"
    corporation-invoices: "ad-{{ env | lower }}-corporation-invoices-{{ aws_region | lower }}"
    corporation-logo: "ad-{{ env | lower }}-corporation-logo-{{ aws_region | lower }}"
    travel-profile: "ad-{{ env | lower }}-traveler-profile-{{ aws_region | lower }}"
    loyalty-club: "ad-{{ env | lower }}-loyalty-club-{{ aws_region | lower }}"
    promotions: "ad-{{ env | lower }}-promotions-{{ aws_region | lower }}"
  register: s3
  tags:
    - s3

- name: run general s3 buckets policy
  cloudformation:
    state: present
    region: "{{ aws_region }}"
    stack_name: "ad-s3-policy-{{ item.key | lower }}-{{ env | lower }}"
    template: "{{ cf_directory }}/data/s3_bucket_policy.yaml"
    template_parameters:
      Env: "{{ env }}"
      S3BucketName: "{{ item.value }}"
  with_dict:
    notification-events: "ad-{{ env | lower }}-notification-events-{{ aws_region | lower }}"
  register: s3_policy
  tags:
    - s3_policy

- name: run us only s3 buckets
  cloudformation:
    state: present
    region: "{{ aws_region }}"
    stack_name: "ad-s3-{{ item.key | lower }}-{{ env | lower }}"
    template: "{{ cf_directory }}/data/s3_bucket.yaml"
    template_parameters:
      Env: "{{ env }}"
      S3BucketName: "{{ item.value }}"
      VersioningEnabled: "Suspended"
  with_dict:
    atlas-cache: "ad-{{ env | lower }}-atlas-cache-{{ aws_region | lower }}"
  register: s3
  tags:
    - s3
  when:
    - env == "produs"

- name: run release s3 buckets
  cloudformation:
    state: present
    region: "{{ aws_region }}"
    stack_name: "ad-s3-{{ item.key | lower }}-{{ env | lower }}"
    template: "{{ cf_directory }}/data/s3_bucket.yaml"
    template_parameters:
      Env: "{{ env }}"
      S3BucketName: "{{ item.value }}"
      VersioningEnabled: "Suspended"
  with_dict:
    webapps: "ad-webapps"
    app-version: "ad-app-version"
  register: s3
  tags:
    - s3
  when:
    - env == "produs"

- name: run redis stack
  cloudformation:
    stack_name: "ad-redis-{{ env | lower }}"
    state: present
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/data/redis.yaml"
    template_parameters:
      Env: "{{ env }}"
      PrivateHostedZone: "{{ private_hosted_zone }}"
      PrivateSubnetA: "{{ private_subnet_1a }}"
      PrivateSubnetB: "{{ private_subnet_1b }}"
      PublicHostedZone: "{{ public_hosted_zone }}"
      VpcCIDRJP: "{{ jp_vpc_cidr }}"
      VpcCIDRUS: "{{ us_vpc_cidr }}"
      VpcId: "{{ vpc_id }}"
      ###################################
      AutoMinorVersionUpgrade: "{{ redis_auto_minor_version_upgrade }}"
      ClusterName: "{{ redis_cluster_name }}-{{ env | lower }}"
      DNSDomain: "{{ dns_domain }}"
      EngineType: "{{ redis_engine_type }}"
      EngineVersionCompatibility: "{{ redis_engine_version_compatibility }}"
      MaintenanceWindow: "{{ redis_maintenance_window }}"
      NodeType: "{{ redis_node_type }}"
      NumberOfNodes: "{{ redis_number_of_nodes }}"
      PortNumber: "{{ redis_port }}"
      RedisDNSName: "{{ redis_dns_name }}"
      SnapshotRetention: "{{ redis_snapshot_retention }}"
  tags:
    - redis

- name: run elasticsearch stack
  cloudformation:
    stack_name: "ad-elasticsearch-{{ env | lower }}"
    state: present
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/data/elasticsearch.yaml"
    template_parameters:
      DNSDomain: "{{ dns_domain }}"
      Env: "{{ env }}"
      PrivateHostedZone: "{{ private_hosted_zone }}"
      SubnetId: "{{ private_subnet_1a }}"
      VpcId: "{{ vpc_id }}"
      VpcCIDRJP: "{{ jp_vpc_cidr }}"
      VpcCIDRUS: "{{ us_vpc_cidr }}"
      ##########################################
      DedicatedMasterCount: "{{ elasticsearch_dedicated_master_count }}"
      DedicatedMasterEnabled: "{{ elasticsearch_dedicated_master_enabled }}"
      DedicatedMasterType: "{{ elasticsearch_dedicated_master_type }}"
      ElasticsearchDNSName: "{{ elasticsearch_dns_name }}"
      ElasticsearchDomainName: "{{ elasticsearch_domain_name }}-{{ env | lower }}"
      InstanceType: "{{ elasticsearch_instance_type }}"
      InstanceCount: "{{ elasticsearch_instance_count }}"
      VolumeSize: "{{ elasticsearch_volume_size }}"
      ZoneAwareness: "{{ elasticsearch_zone_awareness }}"
  register: es
  tags:
    - es
  when:
    - env == "produs"

- name: check service-linked role
  command: "aws iam get-role --role-name AWSServiceRoleForAmazonElasticsearchService"
  register: es_role
  changed_when: False
  tags:
    - es

- name: enable service-linked role
  command: "aws --region {{ aws_region }} iam create-service-linked-role --aws-service-name es.amazonaws.com"
  when: es_role is failed
  tags:
    - es
