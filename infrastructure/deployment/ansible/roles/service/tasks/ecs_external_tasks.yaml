---
- name: run ecs services with path
  cloudformation:
    stack_name: "ad-ecs-external-{{ item }}-{{ env | lower }}"
    state: "{{ manifest_vars['external-services'].services[item].state | default('present') }}"
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/ecs_services/ecs_services_with_path.yaml"
    template_parameters:
      CloudwatchLogRetention: "{{ cloudwatch_log_retention }}"
      ClusterName: "{{ external_cluster_name }}"
      CreateCloudWatch: "{{ create_cloudwatch }}"
      ECRURL: "{{ ecr_url }}"
      ECSExecutionRoleArn: "{{ ecs_execution_role_arn}}"
      Env: "{{ env | lower }}"
      ServiceListener: "{{ service_listener_arn }}"
      SSMArn: "{{ ssm_arn }}"
      VpcId: "{{ vpc_id }}"
      ###############################################
      CPUReservation: "{{ manifest_vars['external_services'].services[item].cpu_reservation | default('0') }}"
      MemoryReservation: "{{ manifest_vars['external_services'].services[item].memory_reservation | default('64') }}"
      ServiceDesiredCount: "{{ manifest_vars['external_services'].services[item].desired_count }}"
      ServiceHealthCheckEndpoint: "{{ manifest_vars['external_services'].services[item].health_check_endpoint | default('/status') }}"
      ServiceImageName: "{{ manifest_vars['external_services'].services[item].image_name }}"
      ServiceImageTag:  "{{ IMAGE_TAG }}"
      ServiceName: "{{ item }}"
      ServicePath: "{{ manifest_vars['external_services'].services[item].service_path | default(item) }}"
      ServicePriority: "{{ manifest_vars['external_services'].services[item].service_priority}}"
      ServiceVersion: "v1"
      TaskCommand: "{{ manifest_vars['external_services'].services[item].task_command }}"
  when: item in service.with_path
  register: services_with_path
  vars:
    IMAGE_TAG: "{{ lookup('env', 'IMAGE_TAG') }}"
    SERVICE_LIST: "{{ lookup('env', 'SERVICE_LIST') }}"
  with_items:  "{{ SERVICE_LIST.split(',') if SERVICE_LIST else service.with_path }}"

- name: run DNS health check
  cloudformation:
    stack_name: "ad-dns-health-check-{{ item }}-{{ env | lower }}"
    state: "{{ manifest_vars['external-services'].services[item].state | default('present') }}"
    region: "{{ health_check_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/ecs_services/health_check.yaml"
    template_parameters:
      AlarmSNSTopicArn: "{{ health_check_sns_topic_arn }}"
      DNSDomain: "{{ dns_domain }}"
      Env: "{{ env | lower }}"
      ServiceHealthCheckEndpoint: "{{ manifest_vars['external_services'].services[item].health_check_endpoint | default('/status') }}"
      ServiceName: "{{ item }}"
      ServicePath: "{{ manifest_vars['external_services'].services[item].service_path | default(item) }}"
      ServiceVersion: "v1"
  register: dns_health_check
  when: item in service.with_path
  vars:
    SERVICE_LIST: "{{ lookup('env', 'SERVICE_LIST') }}"
  with_items:  "{{ SERVICE_LIST.split(',') if SERVICE_LIST else service.with_path }}"

- name: run ecs services without path
  cloudformation:
    stack_name: "ad-ecs-external-{{ item }}-{{ env | lower }}"
    state: "{{ manifest_vars['external-services'].services[item].state | default('present') }}"
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/ecs_services/ecs_services_wo_path.yaml"
    template_parameters:
      CloudwatchLogRetention: "{{ cloudwatch_log_retention }}"
      ClusterName: "{{ external_cluster_name }}"
      CreateCloudWatch: "{{ create_cloudwatch }}"
      ECRURL: "{{ ecr_url }}"
      ECSExecutionRoleArn: "{{ ecs_execution_role_arn}}"
      Env: "{{ env | lower }}"
      SSMArn: "{{ ssm_arn }}"
      ###############################################
      CPUReservation: "{{ manifest_vars['external_services'].services[item].cpu_reservation | default('0') }}"
      MemoryReservation: "{{ manifest_vars['external_services'].services[item].memory_reservation | default('64') }}"
      ServiceDesiredCount: "{{ manifest_vars['external_services'].services[item].desired_count }}"
      ServiceHealthCheckEndpoint: "{{ manifest_vars['external_services'].services[item].health_check_endpoint | default('/status') }}"
      ServiceImageName: "{{ manifest_vars['external_services'].services[item].image_name }}"
      ServiceImageTag:  "{{ IMAGE_TAG }}"
      ServiceName: "{{ item }}"
      TaskCommand: "{{ manifest_vars['external_services'].services[item].task_command }}"
  register: services_without_path
  when: item in service.without_path
  vars:
    IMAGE_TAG: "{{ lookup('env', 'IMAGE_TAG') }}"
    SERVICE_LIST: "{{ lookup('env', 'SERVICE_LIST') }}"
  with_items:  "{{ SERVICE_LIST.split(',') if SERVICE_LIST else service.without_path }}"

- name: run ecs scheduled services
  cloudformation:
    stack_name: "ad-ecs-external-{{ item }}-{{ env | lower }}"
    state: "{{ manifest_vars['external-services'].services[item].state | default('present') }}"
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/ecs_services/ecs_services_scheduled.yaml"
    template_parameters:
      CloudwatchLogRetention: "{{ cloudwatch_log_retention }}"
      ClusterArn: "{{ external_cluster_arn }}"
      CreateCloudWatch: "{{ create_cloudwatch }}"
      ECRURL: "{{ ecr_url }}"
      ECSEventsRoleArn: "{{ ecs_events_role_arn}}"
      ECSExecutionRoleArn: "{{ ecs_execution_role_arn}}"
      Env: "{{ env | lower }}"
      SSMArn: "{{ ssm_arn }}"
      ###############################################
      CPUReservation: "{{ manifest_vars['external_services'].services[item].cpu_reservation | default('0') }}"
      CronExpression: "{{ manifest_vars['external_services'].services[item].cron_expression }}"
      MemoryReservation: "{{ manifest_vars['external_services'].services[item].memory_reservation | default('64') }}"
      ServiceDesiredCount: "{{ manifest_vars['external_services'].services[item].desired_count }}"
      ServiceImageName: "{{ manifest_vars['external_services'].services[item].image_name }}"
      ServiceImageTag:  "{{ IMAGE_TAG }}"
      ServiceName: "{{ item }}"
      TaskCommand: "{{ manifest_vars['external_services'].services[item].task_command }}"
  register: services_scheduled
  when: item in service.schedule
  vars:
    IMAGE_TAG: "{{ lookup('env', 'IMAGE_TAG') }}"
    SERVICE_LIST: "{{ lookup('env', 'SERVICE_LIST') }}"
  with_items:  "{{ SERVICE_LIST.split(',') if SERVICE_LIST else service.schedule }}"

# - name: wait for tasks to finish
#  async_status:
#    jid: "{{ item.ansible_job_id }}"
#  loop: "{{ services_with_path.results + services_without_path.results + services_scheduled.results }}"
#  register: async_poll_results_services
#  until: async_poll_results_services.finished
#  retries: 300
#  delay: 6
#  when:
#    - env == "produs" or env == "prodjp"
