---
- name: run general iam roles
  cloudformation:
    stack_name: "ad-iam-{{ env | lower }}"
    state: present
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/infra/general_roles.yaml"
    template_parameters:
      Env: "{{ env }}"
  register: role
  tags:
    - role

- name: run vpc cloudformation
  cloudformation:
    stack_name: "ad-vpc-{{ env | lower }}"
    state: present
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/infra/vpc.yaml"
    template_parameters:
      Env: "{{ env }}"
      Private1aCIDR: "{{ private_1a_cidr }}"
      Private1bCIDR: "{{ private_1b_cidr }}"
      Private1cCIDR: "{{ private_1c_cidr }}"
      Public1aCIDR: "{{ public_1a_cidr }}"
      Public1bCIDR: "{{ public_1b_cidr }}"
      Public1cCIDR: "{{ public_1c_cidr }}"
      SubnetAvailabilityZone1: "{{ subnet_az_1 }}"
      SubnetAvailabilityZone2: "{{ subnet_az_2 }}"
      SubnetAvailabilityZone3: "{{ subnet_az_3 }}"
      VpcCIDR: "{{ vpc_cidr }}"
  register: vpc
  tags:
    - vpc

- name: run api load balancers
  cloudformation:
    stack_name: "ad-alb-{{ env | lower }}"
    state: present
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/infra/alb.yaml"
    template_parameters:
      ALBExtSecurityGroup: "{{ alb_ext_sg }}"
      ALBIntSecurityGroup: "{{ alb_int_sg }}"
      CertificateArn: "{{ certificate_arn }}"
      DNSName: "{{ dns_domain }}"
      Env: "{{ env }}"
      PrivateHostedZone: "{{ private_hosted_zone }}"
      PrivateSubnetIds: "{{ private_subnets | join(',') }}"
      PublicHostedZone: "{{ public_hosted_zone }}"
      PublicSubnetIds: "{{ public_subnets | join(',') }}"
      RootCertificateArn: "{{ root_certificate_arn }}"
  vars:
    private_subnets:
      - "{{ private_subnet_1a }}"
      - "{{ private_subnet_1b }}"
      - "{{ private_subnet_1c }}"
    public_subnets:
      - "{{ public_subnet_1a }}"
      - "{{ public_subnet_1b }}"
      - "{{ public_subnet_1c }}"
  register: alb
  tags:
    - alb

- name: run ecs clusters
  import_tasks: ecs_clusters.yaml
  vars:
    private_subnets:
      - "{{ private_subnet_1a }}"
      - "{{ private_subnet_1b }}"
      - "{{ private_subnet_1c }}"
    public_subnets:
      - "{{ public_subnet_1a }}"
      - "{{ public_subnet_1b }}"
      - "{{ public_subnet_1c }}"
  register: ecs
  tags:
    - ecs

- name: run sqs
  cloudformation:
    stack_name: "ad-sqs-{{ item.key | lower }}-{{ env | lower }}"
    state: present
    region: "{{ aws_region }}"
    template: "{{ cf_directory }}/infra/sqs.yaml"
    template_parameters:
      QueueName: "{{ item.value }}"
      DeadLetterQueueName: "{{ item.value }}-dead-letter"
  with_dict:
    atlas: "ad-{{ env | lower }}-atlas"
    notification: "ad-{{ env | lower }}-notification"
    notification-user: "ad-{{ env | lower }}-notification-user"
    packer-offline: "ad-{{ env | lower }}-packer-offline"
    packer-online: "ad-{{ env | lower }}-packer-online"
    packer-online-captcha: "ad-{{ env | lower }}-packer-online-captcha"
    shipment: "ad-{{ env | lower }}-shipment"
    submit: "ad-{{ env | lower }}-submit"
  register: sqs
  tags:
    - sqs

- name: run sns for health check
  cloudformation:
    stack_name: "ad-sns-{{ item.key | lower }}-{{ env | lower }}"
    state: present
    region: "{{ health_check_region }}"
    template: "{{ cf_directory }}/infra/sns.yaml"
    template_parameters:
      Env: "{{ env }}"
      SNSTopicName: "{{ item.value }}"
  with_dict:
    dns-health-check: "ad-{{ env | lower }}-dns-health-check"
  register: sns
  tags:
    - sns

- name: run kinesis
  cloudformation:
    stack_name: "ad-kinesis-{{ env | lower }}"
    state: absent
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/infra/kinesis.yaml"
    template_parameters:
      Env: "{{ env }}"
      StreamName: "ad-{{ env | lower }}-smarttrip"
  register: kinesis
  tags:
    - kinesis

- name: run kms key
  cloudformation:
    stack_name: "ad-kms-key-{{ env | lower }}"
    state: present
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/infra/kms_key.yaml"
    template_parameters:
      Env: "{{ env }}"
      AliasName: "ad_kms_key_for_config_{{ env | lower }}"
      ECSInstanceRole: "{{ ecs_instance_arn }}"
  register: kms
  tags:
    - kms

- name: run ec2
  import_tasks: ec2.yaml
  register: ec2
  tags:
    - jumphost
  when:
    - env == "produs"

- name: run cloudfront
  cloudformation:
    stack_name: "ad-cloudfront-{{ item.key | lower }}-{{ env | lower }}"
    state: present
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/infra/cloudfront.yaml"
    template_parameters:
      Env: "{{ env }}"
      ApplicationName: "{{ item.key | lower }}"
      CDNPriceClass: "PriceClass_All"
      CertificateArn: "{{ cloudfront_certificate_arn }}"
      CreateDNS: "true"
      DistributionConfigComment: "{{ item.value }}"
      DNSDomain: "{{ root_dns_domain }}"
      ResponsePagePath: "/index.html"
      RootPublicHostedZone: "{{ root_public_hosted_zone }}"
      S3BucketName: "{{ cloudfront_s3_bucket }}"
      S3OriginPath: "/{{ item.key | lower }}"
      TargetOriginId: "{{ cloudfront_s3_bucket }}/{{ item.key | lower }}"
  with_dict:
    dev: "dev staging release"
    v270: "v270 release"
    v301: "v301 release"
    v302: "v302 release"
  register: cloudfront
  tags:
    - cloudfront
  when:
    - env == "produs"

- name: run cloudfront www release
  cloudformation:
    stack_name: "ad-cloudfront-www-release-{{ env | lower }}"
    state: present
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/infra/www_release.yaml"
    template_parameters:
      PrivateHostedZone: "{{ root_private_hosted_zone }}"
      PublicHostedZone: "{{ root_public_hosted_zone }}"
      Version: "v302"
  register: www
  tags:
    - www
  when:
    - env == "produs"

- name: run dns release
  cloudformation:
    stack_name: "ad-dns-release-{{ item }}-{{ env | lower }}"
    state: present
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/infra/dns_release.yaml"
    template_parameters:
      DeploymentDNSDomain: "us"
      ProductionDNSDomain: "{{ root_dns_domain }}"
      PrivateHostedZone: "{{ root_private_hosted_zone }}"
      PublicHostedZone: "{{ root_public_hosted_zone }}"
  loop:
    - api
    - earth
    # - redis
    - es
  register: release
  tags:
    - release
  when:
    - env == "produs"
