---
- name: run EC2 jumphost cloudformation
  cloudformation:
    stack_name: "ad-ec2-jumphost-{{ env | lower }}"
    state: present
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/infra/ec2_jumphost.yaml"
    template_parameters:
      Env: "{{ env | lower }}"
      HostName: "{{ ec2_jumphost_name }}"
      InstanceType: "{{ ec2_jumphost_instance_type }}"
      KeyName: "{{ ec2_jumphost_key_name }}"
      SshRuleCIDR: "0.0.0.0/0"
      SubnetId: "{{ public_subnet_1a }}"
      VpcID: "{{ vpc_id }}"
  register: ec2_jumphost
  tags:
    - ec2_jumphost
