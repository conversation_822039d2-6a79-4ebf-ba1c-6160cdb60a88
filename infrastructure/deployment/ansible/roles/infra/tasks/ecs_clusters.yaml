---
- name: run ECS external cloudformation
  cloudformation:
    stack_name: "ad-ecs-cluster-external-{{ env | lower }}"
    state: present
    region: "{{ aws_region }}"
    disable_rollback: False
    template: "{{ cf_directory }}/infra/ecs_external_cluster.yaml"
    template_parameters:
      Env: "{{ env }}"
      ALBExtSecurityGroup: "{{ alb_ext_sg }}"
      ALBIntSecurityGroup: "{{ alb_int_sg }}"
      AutoScalingEnabled: "{{ external_cluster_autoscaling }}"
      CloudwatchLogRetention: "{{ cloudwatch_log_retention }}"
      ClusterDesiredCapacity: "{{ external_cluster_desired_capacity }}"
      ClusterMaxSize: "{{ external_cluster_max_size }}"
      ClusterMinSize: "{{ external_cluster_min_size }}"
      ClusterName: "{{ external_cluster_name }}"
      ECSInstanceProfileArn: "{{ ecs_instance_profile_arn }}"
      InstanceType: "{{ external_cluster_instance_type }}"
      KeyName: "{{ ecs_key_name }}"
      SubnetIds: "{{ private_subnets | join(',') }}"
      VpcCIDR: "{{ vpc_cidr }}"
      VpcId: "{{ vpc_id }}"
  register: ecs_external_cluster
  tags:
    - ecs_external_cluster
