################
#   General    #
################

cf_directory: "../cloudformation"              # absolute path to directory where cloudformation is stored
action: "deploy"                               # which action to perform valid choices: [ deploy, purge ]
env: "prodjp"                                  # choose one of: [ produs, prodjp, stagingus ]
aws_region: "ap-northeast-1"                   # aws region where cloudformation will be deployed
health_check_region: "us-east-1"
ecr_url: "853431205376.dkr.ecr.us-west-2.amazonaws.com"
ssm_arn: "arn:aws:ssm:ap-northeast-1:853431205376:parameter"
ssm_prefix: "secrets/"
init_tag: "NOT_WORK"


############################
#   Domain,  Certificate   #
############################

root_dns_domain: "ariadirect.com"
dns_domain: "jp.ariadirect.com"
private_hosted_zone: "Z104176956MNC8AZQAS5"
public_hosted_zone: "ZX3YERD4RPJ3J"
certificate_arn: "arn:aws:acm:ap-northeast-1:853431205376:certificate/194fc90d-9b85-4eb4-ad2f-8bde57d62500"
root_certificate_arn: "arn:aws:acm:us-west-2:853431205376:certificate/7554701e-94db-440a-b280-e254f1d5fda9"
cloudfront_certificate_arn: "arn:aws:acm:us-east-1:853431205376:certificate/f28e62af-0ceb-45e4-9cb5-70c919cfac5d"


############################
#  VPC,  Networking, SG    #
############################

vpc_id: "vpc-0dcc6bdac368265f3"
vpc_cidr: "**********/16"

jp_vpc_cidr: "**********/16"
us_vpc_cidr: "**********/16"

private_1a_cidr: "**********/24"
private_1b_cidr: "**********/24"
private_1c_cidr: "**********/24"
public_1a_cidr: "***********/24"
public_1b_cidr: "***********/24"
public_1c_cidr: "***********/24"

subnet_az_1: "a"
subnet_az_2: "c"
subnet_az_3: "d"

private_subnet_1a: "subnet-02dbfa3b3aac6bbaf"
private_subnet_1b: "subnet-0cb29d4172667044a"
private_subnet_1c: "subnet-058bfda854cdc9f19"
public_subnet_1a: "subnet-07b28c6cd06020627"
public_subnet_1b: "subnet-00f6b10ec6451c347"
public_subnet_1c: "subnet-0b62f4bfd1883b130"
alb_ext_sg: "sg-0ff73acd8f6d77c28"
alb_int_sg: "sg-06686d60fc6be0ebc"


##########
#  ECS   #
##########

ecs_events_role_arn: "arn:aws:iam::853431205376:role/ad-iam-prodjp-ECSEventsRole-1SCU09MCF7228"
ecs_execution_role_arn: "arn:aws:iam::853431205376:role/ad-iam-prodjp-ECSInstanceRole-RK6ZLTB8O6BQ"
cloudwatch_log_retention: 7
create_cloudwatch: "true"
ecs_key_name: "ad-jp-ecs"
ecs_instance_profile_arn: "arn:aws:iam::853431205376:instance-profile/ad-iam-prodjp-ECSInstanceRole-RK6ZLTB8O6BQ"
ecs_instance_arn: "arn:aws:iam::853431205376:role/ad-iam-prodjp-ECSInstanceRole-RK6ZLTB8O6BQ"

service_listener_arn: "arn:aws:elasticloadbalancing:ap-northeast-1:853431205376:listener/app/ad-al-APILo-1DZTXBJ6C4MAJ/4c02f8a6fbbe6cbb/8788d6931cb5c9e5"
internal_service_listener_arn: "arn:aws:elasticloadbalancing:ap-northeast-1:853431205376:listener/app/ad-al-APIIn-13F5D9KS2DPUG/b5550c705ac6c77b/789a00da55b84998"


#################
#  ECS Cluster  #
#################

external_cluster_name: ad_prodjp_external
external_cluster_arn: "arn:aws:ecs:ap-northeast-1:853431205376:cluster/ad_prodjp_external"
external_cluster_autoscaling: "false"
external_cluster_desired_capacity: 1
external_cluster_max_size: 3
external_cluster_min_size: 1
external_cluster_instance_type: "t2.large"


#####################
#   Aurora earth    #
#####################

aurora_earth_availability_zone: "ap-northeast-1a"
aurora_earth_backup_retention: 3
aurora_earth_backup_window: "03:16-03:46"
aurora_earth_engine: "aurora-postgresql"
aurora_earth_engine_version: "9.6.8"
aurora_earth_instance_type: "db.r4.large"
aurora_earth_db_name: "earth"
aurora_earth_db_dns_name: "earth"
aurora_earth_port: 5432
aurora_earth_maintenance_window: "fri:07:00-fri:08:00"
aurora_earth_snapshot_identifier: ""


############
#  Redis   #
############
redis_auto_minor_version_upgrade: "true"
redis_cluster_name: "ad-redis"
redis_engine_type: "redis"
redis_engine_version_compatibility: "5.0.0"
redis_maintenance_window: "fri:07:00-fri:08:00"
redis_node_type: "cache.r4.xlarge"
redis_number_of_nodes: 1
redis_port: 6379
redis_dns_name: "redis"
redis_snapshot_retention: 1


#########
#  SNS  #
#########
health_check_sns_topic_arn: "arn:aws:sns:us-east-1:853431205376:ad-prodjp-dns-health-check"


################
#  Deployment  #
################
service:
  with_path:
    - atlas
    - internals
    - package
    - packer
    - payment
    - search
    - user-management
    - versions
    - master-data
    - shipment
    - mrz-parser
    - passport-photo
    - helper
    - id-reader
    - devices
    - album
    - logger
    - third-party
  without_path:
    - atlas-worker
    - carrier
    - packer-offline
    - packer-online
    - packer-online-captcha
    - submit-email-worker
    - notification
    - notification-user
    - watchdog
    - submit-dispatcher
    - email-hook
  schedule:
    - checker
    - payment-worker
    