################
#   General    #
################

cf_directory: "../cloudformation" # absolute path to directory where cloudformation is stored
action: "deploy" # which action to perform valid choices: [ deploy, purge ]
env: "produs" # choose one of: [ produs, prodjp, stagingus ]
aws_region: "us-west-2" # aws region where cloudformation will be deployed
health_check_region: "us-east-1"
ecr_url: "853431205376.dkr.ecr.us-west-2.amazonaws.com"
ssm_arn: "arn:aws:ssm:us-west-2:853431205376:parameter"
ssm_prefix: "secrets/"
init_tag: "NOT_WORK"

############################
#   Domain,  Certificate   #
############################

root_dns_domain: "ariadirect.com"
dns_domain: "us.ariadirect.com"
private_hosted_zone: "Z05331973H0QUMYSKD429"
public_hosted_zone: "Z2H373P6WGT6Y8"
root_public_hosted_zone: "Z3O7TBLZGRNSLJ"
root_private_hosted_zone: "Z5RH4IDG4YSU0"

certificate_arn: "arn:aws:acm:us-west-2:853431205376:certificate/03b6984f-bfff-4c7d-8809-fc3c37dbd472"
root_certificate_arn: "arn:aws:acm:us-west-2:853431205376:certificate/7554701e-94db-440a-b280-e254f1d5fda9"
cloudfront_certificate_arn: "arn:aws:acm:us-east-1:853431205376:certificate/497fc30b-6bcf-4d83-a659-63ad767b12cc"

############################
#  VPC,  Networking, SG    #
############################

vpc_id: vpc-09c68e8b2a108c58f
vpc_cidr: "**********/16"

jp_vpc_cidr: "**********/16"
us_vpc_cidr: "**********/16"

private_1a_cidr: "**********/24"
private_1b_cidr: "**********/24"
private_1c_cidr: "**********/24"
public_1a_cidr: "***********/24"
public_1b_cidr: "***********/24"
public_1c_cidr: "***********/24"

subnet_az_1: "a"
subnet_az_2: "b"
subnet_az_3: "c"

private_subnet_1a: subnet-0c112c48a4073455e
private_subnet_1b: subnet-031ff75acc7e1c0a3
private_subnet_1c: subnet-0cf84bf0219e14037
public_subnet_1a: subnet-0f70ad4b23c411ba1
public_subnet_1b: subnet-0be4b6a1481318eb0
public_subnet_1c: subnet-0155e80d282a99e80
alb_ext_sg: sg-0cc87cb9fdf18b350
alb_int_sg: sg-050d5966fdaf0e89d

##########
#  ECS   #
##########

ecs_events_role_arn: "arn:aws:iam::853431205376:role/ad-iam-produs-ECSEventsRole-CBOUQD09REWD"
ecs_execution_role_arn: "arn:aws:iam::853431205376:role/ad-iam-produs-ECSInstanceRole-44WK11XJ6XZJ"
cloudwatch_log_retention: 14
create_cloudwatch: "true"
ecs_key_name: "ad-us-ecs"
ecs_instance_profile_arn: "arn:aws:iam::853431205376:instance-profile/ad-iam-produs-ECSInstanceRole-44WK11XJ6XZJ"
ecs_instance_arn: "arn:aws:iam::853431205376:role/ad-iam-produs-ECSInstanceRole-44WK11XJ6XZJ"

service_listener_arn: "arn:aws:elasticloadbalancing:us-west-2:853431205376:listener/app/ad-al-APILo-160YKGAVW0IB/893065113afc7764/5b71fc42d7b14d3d"
internal_service_listener_arn: "arn:aws:elasticloadbalancing:us-west-2:853431205376:listener/app/ad-al-APIIn-IOS2KXCB482A/756922e8795df2f8/032d755485899059"

#################
#  ECS Cluster  #
#################

external_cluster_name: ad-produs-external
external_cluster_arn: arn:aws:ecs:us-west-2:853431205376:cluster/ad-produs-external
external_cluster_autoscaling: "false"
external_cluster_desired_capacity: 1
external_cluster_max_size: 3
external_cluster_min_size: 1
external_cluster_instance_type: "t2.large"

###################
#  EC2 Jumphost   #
###################

ec2_jumphost_name: "jumphost"
ec2_jumphost_instance_type: "t2.micro"
ec2_jumphost_key_name: "ad-jumphost"

###################
#   Cloudfront    #
###################

cloudfront_s3_bucket: "ad-webapps.s3.amazonaws.com"

#####################
#   Aurora earth    #
#####################
aurora_earth_availability_zone: "us-west-2c"
aurora_earth_backup_retention: 3
aurora_earth_backup_window: "03:16-03:46"
aurora_earth_engine: "aurora-postgresql"
aurora_earth_engine_version: "9.6.8"
aurora_earth_instance_type: "db.r4.large"
aurora_earth_db_name: "earth"
aurora_earth_db_dns_name: "earth"
aurora_earth_port: 5432
aurora_earth_maintenance_window: "fri:07:00-fri:08:00"
aurora_earth_snapshot_identifier: ""

############
#  Redis   #
############
redis_auto_minor_version_upgrade: "true"
redis_cluster_name: "ad-redis"
redis_engine_type: "redis"
redis_engine_version_compatibility: "5.0.0"
redis_maintenance_window: "fri:07:00-fri:08:00"
redis_node_type: "cache.r4.xlarge"
redis_number_of_nodes: 1
redis_port: 6379
redis_dns_name: "redis"
redis_snapshot_retention: 1

####################
#  Elasticsearch   #
####################

elasticsearch_dedicated_master_count: 0
elasticsearch_dedicated_master_enabled: false
elasticsearch_dedicated_master_type: "t2.small.elasticsearch"
elasticsearch_dns_name: "es"
elasticsearch_domain_name: "ad-es"
elasticsearch_instance_type: "t2.small.elasticsearch"
elasticsearch_instance_count: 1
elasticsearch_volume_size: 20
elasticsearch_zone_awareness: false

#########
#  SNS  #
#########
health_check_sns_topic_arn: "arn:aws:sns:us-east-1:853431205376:ad-produs-dns-health-check"

################
#  Deployment  #
################
service:
  with_path:
    - atlas
    - internals
    - package
    - packer
    - payment
    - search
    - user-management
    - master-data
    - shipment
    - mrz-parser
    - passport-photo
    - helper
    - id-reader
    - devices
    - album
    - logger
    - third-party
    - python-tool
  without_path:
    - atlas-worker
    - carrier
    - packer-offline
    - packer-online
    - packer-online-captcha
    - submit-email-worker
    - notification
    - notification-user
    - watchdog
    - submit-dispatcher
    - payment-worker
    - email-hook
  schedule:
    - checker
