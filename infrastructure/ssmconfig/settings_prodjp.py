ad_aws = {
    "region": "ap-northeast-1",
    "account": "************",
    "ecr": "************.dkr.ecr.us-west-2.amazonaws.com"
}


ad_api_token = {
    "token": "{{internal_api_token}}"
}


ad_secrets = {
    "jwt": "{{jwt_secret}}"
}


ad_db = {
    "username": "aria",
    "password": "{{db_password}}",
    "port": 5432,
    "dbname": "jp_earth3.1.1",
    "read_host": "earth_read.jp.ariadirect.com",
    "write_host": "earth.jp.ariadirect.com"
}


ad_ses = {
    "region": "us-west-2"
}


ad_es = {
    "region": "us-west-2",
    "endpoint": "vpc-ad-elasticsearch-produs-6ajlxbv4ed4e7sjp5t5gvurmfq.us-west-2.es.amazonaws.com",
    "port": 443
}


ad_sqs = {
    "url_prefix": "https://sqs.ap-northeast-1.amazonaws.com/************",
    "dead_letter_suffix": "-dead-letter",
    "atlas_sqs_name": "ad-prodjp-atlas",
    "notification_sqs_name": "ad-prodjp-notification",
    "notification_user_sqs_name": "ad-prodjp-notification-user",
    "packer_offline_sqs_name": "ad-prodjp-packer-offline",
    "packer_online_sqs_name": "ad-prodjp-packer-online",
    "packer_online_captcha_sqs_name": "ad-prodjp-packer-online-captcha",
    "shipment_sqs_name": "ad-prodjp-shipment",
    "submit_sqs_name": "ad-prodjp-submit"
}


ad_s3 = {
    "ariadirect_prod_passport_images": "ad-prodjp-passport-images-ap-northeast-1",
    "ariadirect_prod_applications": "ad-prodjp-applications-ap-northeast-1",
    "ariadirect_prod_notification-events": "ad-prodjp-notification-events-ap-northeast-1",
    "ariadirect_prod_atlas_cache_s3_bucket": "ad-produs-atlas-cache-us-west-2",
    "ariadirect_prod_document_templates": "ad-prodjp-document-template-ap-northeast-1",
    "ariadirect_prod_ai_service": "ad-prodjp-ai-service-ap-northeast-1",
    "ariadirect_prod_corporation_invoices": "ad-prodjp-corporation-invoices-ap-northeast-1",
    "ariadirect_prod_corporation_logo": "ad-prodjp-corporation-logo-ap-northeast-1",
    "ariadirect_prod_traveler_profile": "ad-prodjp-traveler-profile-ap-northeast-1",
    "ariadirect_prod_album": "ad-prodjp-album-ap-northeast-1",
}


ad_package_service = {
    "host_name": "api.jp.ariadirect.com/v1/pkg"
}


ad_packer_service = {
    "search_url": "https://api.jp.ariadirect.com/v1/search-svc",
    "host_name": "api.jp.ariadirect.com/v1/packer"
}


ad_submit_services = {
    "env": "staging"
}


ad_user_account_service = {
    "host_name": "api.jp.ariadirect.com/v1/ums",
    "url": "https://dev.ariadirect.com/",
    "googlecid": "{{google_cid}}",
    "googlecsecret": "{{google_csecret}}",
    "second_clientid": "{{android_cid}}",
    "ios_clientid": "{{ios_cid}}",
    "linkedin_request_uri": "https://dev.ariadirect.com/oauth/linkedin",
    "linkedin_client_id": "{{linkedin_client_id}}",
    "linkedin_client_secret": "{{linkedin_client_secret}}",
    "okta_cid": "{{okta_cid}}",
    "okta_csecret": "{{okta_csecret}}",
    "okta_app_url": "https://dev-********.okta.com/oauth2/default"
}

ad_apple = {
    "apple_oauth_url": "https://appleid.apple.com/auth/token",
    "apple_client_id": "io.travesalllc.traversal",
    "apple_team_id": "86T4CX296D",
    "apple_key_id": "MRBTFRYDK6",
    "apple_private_key": "{{apple_private_key}}"
}


ad_fedex = {
    "env": "sandbox",
    "key": "9BDH9o9APvHngAHj",
    "password": "{{fedex_password}}",
    "account": "*********",
    "meter": "*********",
    "url": "https://wsbeta.fedex.com:443/web-services"
}

ad_walgreen = {
    "service_url": "https://services-qa.walgreens.com",
    "api_key": "d12ddc87a36f1cfb422dccb4ff0a7184",
    "print_photo_product_id": "8770001",
    "email": "*******",
    "first_name": "Cindy",
    "last_name": "Ngo",
    "phone": "**********"
}


ad_payment_service = {
    "host_name": "api.jp.ariadirect.com/v1/payment"
}


ad_wechatpay = {
    "payment_url_wechatpay": "https://api.jp.ariadirect.com/v1/payment/payments/callback/wechatpay",
    "wechat_pay_api_key": "{{wechat_pay_api_key}}",
    "wechat_pay_account_id": "**********",
    "wechat_pay_app_id": "wx80a3055e2a3b98d2"
}


ad_authorize = {
    "authorize_api_token": "{{payment_authorize_api_token}}",
    "authorize_key": "{{payment_authorize_key}}",
    "authorize_mode": "sandbox",
    "authorize_endpoint": "https://apitest.authorize.net/xml/v1/request.api",
    "authorize_form_post_url": "https://test.authorize.net/payment/payment",
    "authorize_webhook_url": "https://apitest.authorize.net/rest/v1",
    "authorize_webhook_id": "9104bfbe-0bb7-4928-9170-e5be03ec912b",
    "authorize_webhook_sign": "{{payment_authorize_webhook_sign}}",
    "authorize_webhook_callback": "https://api.jp.ariadirect.com/v1/payment/payments/callback/AUTHORIZENET",
    "authorize_redirect_url": "https://dev.ariadirect.com/dashboard/payment/payment-result/authorizenet",
    "authorize_redirect_cancel_url": "https://dev.ariadirect.com/dashboard/my-cart?tab=my_cart"
}


ad_onepay = {
    "onepay_merchant_id": "TESTONEPAY",
    "onepay_merchant_order": "JSECURETEST01",
    "onepay_user": "op01",
    "onepay_password": "{{onepay_password}}",
    "onepay_api_key": "{{onepay_api_key}}",
    "onepay_api_secret": "{{onepay_api_secret}}",
    "onepay_endpoint": "https://mtf.onepay.vn/paygate/vpcpay.op",
    "onepay_callback": "https://dev.ariadirect.com/dashboard/payment/payment-result/onepay",
    "onepay_query_dc": "https://mtf.onepay.vn/msp/api/v1/vpc/invoices/queries",
    "vpbank_exchange_rate": "https://portal.vietcombank.com.vn/Usercontrols/TVPortal.TyGia/pXML.aspx",
    "onepay_enabled": True
}

ad_paypal = {
    "paypal_api_key": "{{paypal_api_key}}",
    "paypal_api_secret": "{{paypal_api_secret}}",
    "paypal_endpoint": "https://api.sandbox.paypal.com",
    "paypal_return": "https://dev.ariadirect.com/dashboard/payment/payment-result/paypal",
    "paypal_cancel": "https://dev.ariadirect.com/dashboard/my-cart?tab=my_cart",
    "paypal_callback": "https://api.jp.ariadirect.com/v1/payment/payments/callback/paypal",
    "paypal_webhook_id": "9CN10343J3906151E"
}

ad_zellepay = {
    "zellepay_contact_email": "*******",
    "zellepay_contact_phone": "+1**********",
    "bank_1_name": "Ngân hàng thương mại cổ phần Á Châu (ACB)",
    "bank_1_owner": "Công ty TNHH AriaDirect",
    "bank_1_number": "*********"
}

ad_stripe = {
    "secret": "{{stripe_secret}}"
}

ad_email = {
    "info": "*******",
    "finance": "*******",
    "visas": "*******",
    "services": "*******",
    "support": "*******",
    "admin": "*******",
    "supervisor": "*******",
    "env": "staging"
}

# TODO: move to ad_endpoint
ad_website = {
    "host_name": "https://dev.ariadirect.com"
}

ad_watchdog_service = {
    "package_check_frequency": "0 * * * *",
    "package_action_frequency": "0 * * * *",
    "package_complete_days": 30,
    "package_incomplete_days": 20,
    "package_process_days": 10,
    "application_check_frequency": "0 0 * * *",
    "application_action_frequency": "0 0 * * *",
    "application_review_hours": 1,
    "slack_api_key": "********************************************************",
    "slack_channel_id": "CV7BCN5A7",
    "remind_passport_url": "https://ariadirect.page.link/passport-service-staging",
    "remind_visa_url": "https://ariadirect.page.link/visa-service-staging"
}

ad_document_template = {
    "emails": "email_template.sql"
}

ad_endpoint = {
    "api_base_url": "https://api.jp.ariadirect.com",
    "web_base_url": "https://dev.ariadirect.com"
}

ad_passport_photo_service = {
    "background_token": "eab4a8e27c8b4b4e95e71f85e50e1548"
}

ad_env = "stag"

ad_firebase = {
   "certification":{
      "type":"service_account",
      "project_id":"traversal-206105",
      "private_key_id":"829410a0cdbecba3ccfd91d7cbe679d285efb537",
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      "client_email":"*******",
      "client_id":"117881579531569568032",
      "auth_uri":"https://accounts.google.com/o/oauth2/auth",
      "token_uri":"https://oauth2.googleapis.com/token",
      "auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs",
      "client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-9oc1o%40traversal-206105.iam.gserviceaccount.com"
   },
   "project_id":"traversal-206105"
}