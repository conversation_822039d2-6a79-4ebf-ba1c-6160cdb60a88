#!/usr/bin/env python3

import argparse
import boto3
import json
import logging
import os
import pyaml
import re
import retrying
import sys


PROD_US = {
    'aws_profile':   'my',  # replace with your own local profile
    'aws_region':    'us-west-2',
    'kms_key_id':    'cf013ad5-95f0-4450-b4c9-2ca1b458c657',
    'config':        'settings_produs.py',
    'ssm_path':      '/produs'
}

PROD_JP = {
    'aws_profile':   'my',  # replace with your own local profile
    'aws_region':    'ap-northeast-1',
    'kms_key_id':    '6f8c741e-6823-4613-9ccc-670d0223273a',
    'config':        'settings_prodjp.py',
    'ssm_path':      '/prodjp'
}

local_yaml = 'local.yaml'

logger = logging.getLogger(__name__)


class SSMDeploy(object):

    def __init__(self, env):
        """
        :param env:
        """
        if env.lower() == 'produs':
            profile = PROD_US
        elif env.lower() == 'prodjp':
            profile = PROD_JP
        else:
            raise Exception('Not supported')

        logger.info('Working on the environment: %s', env.upper())

        self.kms_key_id = profile['kms_key_id']
        self.config_file = profile['config']
        self.ssm_path = profile['ssm_path']

        self.ssm_client = boto3.Session(profile_name=profile['aws_profile'],
                                        region_name=profile['aws_region']).client('ssm')

    def get_local_parameters(self):
        """
        :return:
        """

        config_obj = __import__(os.path.splitext(self.config_file)[0])
        local_para = [(each, getattr(config_obj, each)) for each in dir(config_obj)
                      if not each.startswith('__') and not each.endswith('__')]
        logger.info('Got %s local parameters', len(local_para))

        ret_dict = dict()
        for key, value in local_para:
            logger.info('checking %s', value)
            if type(value) == int:
                ret_dict[key] = value
            elif type(value) == str:
                ret_dict[key] = self.match_and_replace(value)
            # Second level is a dict
            elif type(value) == dict:
                each_dict = dict()
                for k, v in value.items():
                    if type(v) == int:
                        each_dict[k] = v
                    elif type(v) == str:
                        each_dict[k] = self.match_and_replace(v)
                    # TODO: Do nothing if it is a nested dict
                    else:
                        each_dict[k] = v
                ret_dict[key] = each_dict

        return ret_dict

    def match_and_replace(self, ssm_str):
        """
        :param ssm_str:
        :return:
        """
        pattern = re.compile(r'\{\{(.*)\}\}')
        found = pattern.search(ssm_str)

        if found:
            ssm_key = found.groups()[0]
            ret = self.ssm_get_parameter(self.ssm_client, '/secrets/{}'.format(ssm_key))
            if ret:
                ret_value = ssm_str.replace('{{%s}}' % ssm_key, ret.get('Value'))
            else:
                logger.warning('WARNING! There is no secret for %s', ssm_key)
                ret_value = ssm_str
        else:
            ret_value = ssm_str
        return ret_value

    def test(self):
        """Local test to verify yaml format
        :return:
        """
        local_parameters = self.get_local_parameters()

        cloud_parameters = self.ssm_get_parameters_by_path(self.ssm_client, self.ssm_path)

        all_keys = sorted(set(list(local_parameters.keys()) + list(cloud_parameters.keys())))

        ret_msg = []
        same_msg = []
        for k in all_keys:
            local_v = local_parameters.get(k, None)
            cloud_v = cloud_parameters.get(k, {}).get('Value', None)
            try:
                cloud_v = json.loads(cloud_v)
            except Exception as exc:
                logger.warning('Cannot go with json parser: %s', exc)

            if local_v == cloud_v:
                msg = "%s:  SAME" % k
                same_msg.append(msg)
            else:
                msg = "%s:\nLocal value: %s\nCloud value: %s" % (k, local_v,  cloud_v)
                ret_msg.append(msg)

        logger.info('\n\n%s values are same\n', str(len(same_msg)))
        logger.info('\n\n' + '\n\n'.join(same_msg))
        logger.info('\n\n%s values are different\n', str(len(ret_msg)))
        logger.info('\n\n' +'\n\n'.join(ret_msg))

    def deploy_secrets(self):
        """
        :return:
        """
        with open(local_yaml, 'rt') as f:
            content = f.read()
        secrets = pyaml.yaml.load(content)

        for key, val in secrets.items():
            key = '/secrets/{}'.format(key)
            ssm_type = 'SecureString'
            key_id = self.kms_key_id
            logger.info('Deploy secret "%s" "%s"', key, val)
            self.ssm_put_parameter(self.ssm_client, key, json.dumps(val), ssm_type=ssm_type, key_id=key_id)

    def deploy_config(self):
        """
        :return:
        """
        local_parameters = self.get_local_parameters()
        for key, val in local_parameters.items():
            # Add prefix path by convention
            key = '{}/{}'.format(self.ssm_path, key)
            logger.info('Deploy "%s"  "%s"', key, json.dumps(val))
            self.ssm_put_parameter(self.ssm_client, key, json.dumps(val))

    @retrying.retry(wait_exponential_multiplier=1000, wait_exponential_max=10000, stop_max_attempt_number=8)
    def ssm_put_parameter(self, ssm_client, key, value, ssm_type="String", key_id=None):
        """
        :param self:
        :param ssm_client:
        :param key:
        :param value:
        :param ssm_type:
        :param key_id:
        :return:
        """
        if ssm_type == 'SecureString' and key_id:
            ssm_client.put_parameter(
                Name=str(key),
                Value=str(value),
                Type=ssm_type,
                Overwrite=True,
                KeyId=key_id
            )
        else:
            ssm_client.put_parameter(
                Name=str(key),
                Value=str(value),
                Type=ssm_type,
                Overwrite=True,
            )

    @staticmethod
    def ssm_get_parameters_by_path(ssm_client, ssm_path):
        max_number = 10
        ret = ssm_client.get_parameters_by_path(
            Path=ssm_path,
            Recursive=True,
            WithDecryption=True,
            MaxResults=max_number
        )
        parameters = ret.get('Parameters')
        next_token = ret.get('NextToken', None)
        while next_token:
            ret = ssm_client.get_parameters_by_path(
                Path=ssm_path,
                Recursive=True,
                WithDecryption=True,
                MaxResults=max_number,
                NextToken=next_token
            )
            parameters += ret.get('Parameters')
            next_token = ret.get('NextToken', None)

        # Convert it to a dict with the name as key, and remove the ssm_path
        para_dict = dict()
        for each_para in parameters:
            k = each_para['Name'].split('{}/'.format(ssm_path))[-1]
            para_dict[k] = each_para

        return para_dict

    @staticmethod
    def ssm_get_parameter(ssm_client, name):
        """
        :param ssm_client:
        :param name:
        :return:
        """
        try:
            ret = ssm_client.get_parameter(Name=name, WithDecryption=True)
        except Exception as exc:
            logger.error(exc)
            return {}
        else:
            return ret.get('Parameter')


if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        description='Config SSM parameters')
    parser.add_argument('--env', default='prodjp', choices=['produs', 'prodjp'],
                        help='Specify the environment')
    parser.add_argument('--test', action='store_true',
                        help='Run local test for the value check')
    parser.add_argument('--deploy_secrets', action='store_true',
                        help='Add secrets from local file to the SSM')
    parser.add_argument('--deploy_config', action='store_true',
                        help='Run the deployment')

    parser.add_argument('--log-level', default='INFO', help='Logging level')
    args = parser.parse_args()

    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.getLevelName(args.log_level.upper())
    )
    logging.getLogger('botocore.credentials').setLevel(logging.DEBUG)

    # Really don't need to hear about connections being brought up again after server has closed it
    logging.getLogger('botocore.vendored.requests.packages.urllib3.connectionpool').setLevel(logging.WARNING)
    logging.getLogger('botocore.credentials').setLevel(logging.WARNING)

    rc = 0
    try:
        ssm = SSMDeploy(args.env)
        if args.test:
            ssm.test()
        elif args.deploy_secrets:
            ssm.deploy_secrets()
        elif args.deploy_config:
            ssm.deploy_config()
        else:
            # ssm.test()
            # ssm.deploy_config()
            # ssm.deploy_secrets()
            pass
    except Exception as exc:
        logger.exception('Unhandled exception: {}'.format(exc))
        sys.exit(1)

    sys.exit(rc)
