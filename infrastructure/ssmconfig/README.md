# SSM Config Management

 
## Synopsis
This document describes how we store the config parameters and secrets, and how to use those in our services. We could 
leverage it for:

 - feature on/off flag
 - secret management
 - AWS resource parameters
 - service parameters
 - API URL endpoints store

## Convention
 - All the secrets are stored in AWS SSM under path **/secrets/**;
 - All the config are stored in AWS SSM under path **/<env\>/**, such as **/produs/**, **/prodjp/** ...
 - The secrets in **settings** files are referenced to the SSM secrets, the format is "**_{{<secret_key>}}_**", which is unique in each environment

## WARNING
**PLEASE DO NOT SHARE ANY SECRET ON THE PULL REQUEST AT ANY TIME**

## Overview

We are using AWS **System Manager/Parameter Store** to keep all the secrets and config parameters. And we leverage the 
`secrets field` under **ContainerDefinitions** in ECS task definition to convert those secrets and parameters to the 
environment variables in the services containers.
 
The secrets and variable are a `string` or a JSON-like `string`.

```buildoutcfg
      ContainerDefinitions:
        - Name: my-service
          Secrets:
          - Name: "variable1"
            ValueFrom: "arn:aws:ssm:us-west-2:853431205376:parameter/<env>/variable1"
          - Name: "secret1"
            ValueFrom: "arn:aws:ssm:us-west-2:853431205376:parameter/<env>/secret1"
```

For any parameters and secrets you plan to use in your services, please add it in a **config** file, which will be 
updated to AWS **System Manager/Parameter Store**.


## Makefile commands

Local test, it will check your local config, and compare with parameters from the existing cloud config
```buildoutcfg
$ make test
```
or you can run on the specific environment

```buildoutcfg
$ make test-prodjp
```

To deploy your new parameters to the cloud, simply run the following command after you double check the changes
by running the test
 
```buildoutcfg
$ make deploy-config-prodjp
```
or 
```buildoutcfg
$ make deploy-config-produs
```

## How to add the secrets to SSM
We add the automation to store and fetch the **secrets** from SSM. Edit the **local.yaml** file under **config** folder;
then run the command

```buildoutcfg
$ make deploy-secrets-prodjp
```
or

```buildoutcfg
$ make deploy-secrets-produs
```

**WARNING**: Please do NOT checkin this **local.yaml** file.
