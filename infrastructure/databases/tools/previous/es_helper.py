#!/usr/bin/env python3

"""
It is a one time tool to load data from the files to ElasticSearch
"""
import json
import os
import pandas as pd
from elasticsearch6 import Elasticsearch, RequestsHttpConnection


es_host = 'localhost'
es_port = 9200

es_client = Elasticsearch(
    hosts=[{'host': es_host, 'port': es_port}],
    use_ssl=True,
    verify_certs=False,
    connection_class=RequestsHttpConnection
)

def load_data_to_es(fname, index):
    dir_name, _ = os.path.split(os.path.abspath(__file__))

    with open(os.path.join(dir_name, fname)) as fp:
        data = json.load(fp)

    data = data['data']

    es_client.indices.create(index=index, ignore=400)
    total = len(data)
    for i, d in enumerate(data):
        print('Loading {} out of {}', i, total)
        es_client.index(index=index, doc_type='_doc', body=d)

def load_airline_to_es(fname, index):
    dir_name, _ = os.path.split(os.path.abspath(__file__))

    with open(os.path.join(dir_name, fname)) as fp:
        data = json.load(fp)

    es_client.indices.create(index=index, ignore=400)
    total = len(data)
    for i, d in enumerate(data):
        d['active'] = 'TRUE' if d['active'] else 'FALSE'
        print('Loading {} out of {}', i, total)
        es_client.index(index=index, doc_type='_doc', body=d)


def run():
    load_data_to_es('countryAndState.json', 'country')
    # load_data_to_es('airports-DB.json', 'airport')
    # load_airline_to_es('airlines.json', 'airline')


run()