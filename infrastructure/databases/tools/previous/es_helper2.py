#!/usr/bin/env python3

"""
It is a one time tool to load data from the ElasticSearch to ElasticSearch
"""
import json
import os
from elasticsearch6 import Elasticsearch, RequestsHttpConnection


es_host = 'localhost'
es_port = 9200

es_client = Elasticsearch(
    hosts=[{'host': es_host, 'port': es_port}],
    use_ssl=True,
    verify_certs=False,
    connection_class=RequestsHttpConnection
)


def download(index):
    """
    :param index:
    :return:
    """
    data = list()
    res = es_client.search(index=index,
                           body={'size': 2000,
                                 'query': {'match_all': {}}},
                           scroll = '5s'
                           )
    print('%s documents found' % res['hits']['total']['value'])

    # Get the scroll ID
    sid = res['_scroll_id']
    scroll_size = len(res['hits']['hits'])

    while scroll_size > 0:
        "Scrolling..."
        data += [each['_source'] for each in res['hits']['hits']]
        res = es_client.scroll(scroll_id=sid, scroll='2s')
        sid = res['_scroll_id']
        scroll_size = len(res['hits']['hits'])

    print(len(data))

    dir_name, _ = os.path.split(os.path.abspath(__file__))

    file_name = dir_name + '/tmp/' + index
    with open(file_name, 'w') as fp:
        json.dump(data, fp)


def upload(index):
    """
    :param index:
    :return:
    """

    dir_name, _ = os.path.split(os.path.abspath(__file__))

    file_name = dir_name + '/tmp/' + index
    with open(file_name, 'r') as fp:
        data = json.load(fp)

    es_client.indices.create(index=index, ignore=400)
    for d in data:
        es_client.index(index=index, doc_type='_doc', body=d)


for index in ['aircraft', 'airline', 'country', 'currency', 'state_region', 'airport']:
    download(index)
    upload(index)
