#!/usr/bin/env python

import argparse
import copy
import json
import logging
import os
import psycopg2
import pyaml
import sys

from itertools import product

db_local_profile = {'dbhost':   'localhost',
                    'database': 'earth',
                    'user':     'aria',
                    'password': 'password'
                    }

db_cloud_profile = {'dbhost':   'localhost',
                    'database': 'earth',
                    'user':     'aria',
                    'password': '<password>'
                    }

visa_product = {
    'table':        'visa_product',
    'file_name':    'visa_product.yaml',
    'column':       ("id", "country", "purpose", "category", "issue_method", "attributes",
                     "form", "schema", "constraint", "additional_questions",
                     "additional_requirements", "submit_method", "services", "status")
}

logger = logging.getLogger(__name__)


class Loader(object):

    def __init__(self, dbhost, database, user, password, port=5432):
        """
        :param dbhost:
        :param database:
        :param user:
        :param password:
        :param port:
        """
        self.dbhost = dbhost
        self.database = database
        self.user = user
        self.password = password
        self.port = port

    def run(self, table_profile):
        """
        :param table_profile:
        :return:
        """
        table = table_profile.get('table')
        file_name = table_profile.get('file_name')
        column = table_profile.get('column')
        data_list = self.parse_yaml_file(file_name, column)

        self.insert(table, data_list)

    def insert(self, table, values):
        """
        :param table:
        :param values:
        :return:
        """
        try:
            connection = psycopg2.connect(user=self.user,
                                          password=self.password,
                                          host=self.dbhost,
                                          port=self.port,
                                          database=self.database
                                          )
            cursor = connection.cursor()
            sql = "INSERT INTO {} VALUES ({})".format(table, ','.join(["%s"] * len(values[0])))
            logger.info('SQL: %s', sql)

            cursor.executemany(sql, values)
            connection.commit()
            count = cursor.rowcount
            logger.info("%s records inserted successfully into %s table", count, table)
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            connection.close()
            logger.info("PostgreSQL connection is closed")

    def parse_yaml_file(self, data_file_name, column):
        """
        :param data_file_name:
        :param column:
        :return:
        """
        dirname, _ = os.path.split(os.path.abspath(__file__))
        try:
            with open(os.path.join(dirname, data_file_name), 'rt') as f:
                data_obj = pyaml.yaml.load_all(f.read())
        except Exception as exc:
            logger.exception('Fail to parse YAML file: %s', exc)
            raise

        # Special handler
        if 'visa_product' in data_file_name:
            data_obj = self.visa_product_handler(data_obj)

        data_list = [tuple(item[k] for k in column) for item in data_obj]

        return data_list

    def visa_product_handler(self, data_list):
        """
        :param self:
        :param data_list:
        :return:
        """
        # Handle the following keys
        _product_keys = ["category", "validity", "number_of_entries", "processing_time"]
        # The start DB ID
        start_db_id = 1

        results = []

        logger.info('Handle the visa product data')

        for each in data_list:
            # Step 1: handle schema
            schema_file = each.get('schema')
            if schema_file:
                dirname, _ = os.path.split(os.path.abspath(__file__))
                with open(os.path.join(dirname, schema_file)) as f:
                    each["schema"] = f.read()

            # Step 2: handle category, attributes
            category_list = each["category"].split(",")
            validity_list = each["attributes"]["validity"].split(",")
            number_of_entries_list = each["attributes"]["number_of_entries"].split(",")
            processing_time_list = each["attributes"]["processing_time"].split(",")

            product_list = product(category_list, validity_list,
                                   number_of_entries_list, processing_time_list
                                   )

            for cat, val, num, pro in product_list:
                each_copy = copy.deepcopy(each)
                each_copy["category"] = cat.strip()

                if each_copy["attributes"]:
                    each_copy["attributes"]["validity"] = val.strip()
                    each_copy["attributes"]["number_of_entries"] = num.strip()
                    each_copy["attributes"]["processing_time"] = pro.strip()
                    each_copy["attributes"] = json.dumps(each_copy["attributes"])

                if each_copy["constraint"]:
                    each_copy["constraint"] = json.dumps(each_copy["constraint"])

                each_copy['id'] = start_db_id
                start_db_id += 1

                results.append(each_copy)

        return results


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Helper to insert data to Postgresql')
    parser.add_argument('--test', dest='test', action='store_true',
                        help='Run local database, if True')
    parser.add_argument('--password', dest='password', default=None,
                        help='Must have password, if it runs on cloud database')
    parser.add_argument('--table', dest='table', choices=['visa_product',],
                        help='Insert data for different table, please choose one')
    parser.add_argument('--log-level', default='INFO', help='Logging level')

    args = parser.parse_args()

    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.getLevelName(args.log_level.upper())
    )

    rc = 0
    try:
        if not args.test:
            logger.info('WARNING: Running on the cloud database')
            dbhost = db_local_profile.get('dbhost')
            database =db_local_profile.get('database')
            user = db_local_profile.get('user')
            password = args.password
        else:
            logger.info('Running on the local database')
            dbhost = db_local_profile.get('dbhost')
            database =db_local_profile.get('database')
            user = db_local_profile.get('user')
            password = db_local_profile.get('password')

        loader = Loader(dbhost, database, user, password)

        if args.table == 'visa_product':
            table_profile = visa_product
        else:
            logger.warning('No table to load')

        logger.info('Running for the table %s', table_profile)
        loader.run(table_profile)

    except Exception as exc:
        logger.exception('Unhandled exception: %s', exc)
        sys.exit(1)

    sys.exit(rc)
