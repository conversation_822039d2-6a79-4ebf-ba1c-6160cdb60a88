#!/usr/bin/env python
"""
The source xlsx file:
https://docs.google.com/spreadsheets/d/1gusbUIyr4hbm8BHo3p97O5EXMYSuuXyVXKYfmpFWQUA/edit#gid=0
"""

import argparse
import logging
import os
import pandas as pd
import psycopg2
import sys
import uuid
import json

db_local_profile = {'dbhost':   'localhost',
                    'database': 'earth',
                    'user':     'aria',
                    'password': 'password'
                    }

db_cloud_profile = {'dbhost':   'localhost',
                    'database': 'earth2.1.0',
                    'user':     'aria',
                    'password': '<password>'
                    }

logger = logging.getLogger(__name__)


class Loader(object):

    def __init__(self, dbhost, database, user, password, port=5432):
        """
        :param dbhost:
        :param database:
        :param user:
        :param password:
        :param port:
        """
        self.dbhost = dbhost
        self.database = database
        self.user = user
        self.password = password
        self.port = port
        self.xlsx_name = '2.1.0.xlsx'

    def insert_visa_product_and_pricing(self, vp_sheet_name='visa_product', vp_table='visa_product',
                                        pricing_sheet_name='pricing', pricing_table='visa_customer_price'):
        """
        :param vp_sheet_name:
        :param vp_table:
        :param pricing_sheet_name:
        :param pricing_table:
        :return:
        """
        self.vp_index_id_mapping = dict()

        vp_data = self.parse_xlsx_file(vp_sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            for col in vp_data:
                # Insert VP
                logger.info('insert to %s table', vp_table)

                this_id = str(col['id'])

                this_attr = {
                    'validity':          col['validity'],
                    'number_of_entries': col['number_of_entries'],
                    'processing_time':   col['processing_time']
                }

                schema_file = col.get('schema')

                if col['country'] == 'CHN':
                    dirname, _ = os.path.split(os.path.abspath(__file__))
                    dirname = dirname + '/../' + 'visa_schema/china'
                    with open(os.path.join(dirname, schema_file)) as f:
                        this_schema = f.read()
                elif col['country'] == 'VNM':
                    dirname, _ = os.path.split(os.path.abspath(__file__))
                    dirname = dirname + '/../' + 'visa_schema/vietnam'
                    with open(os.path.join(dirname, schema_file)) as f:
                        this_schema = f.read()
                else:
                    this_schema = 'NULL'

                this_extended_services = []
                # query extended service id

                if col.get('extended_services'):
                    _es_str = col['extended_services'].strip()
                    _es_list = _es_str.split(',') if _es_str else []

                    for es in _es_list:
                        sql_select_query = \
                            """SELECT id FROM extended_service \
                            WHERE name='%s' AND currency='%s' \
                            """ % (es, col.get('currency', 'USD'))

                        logger.info("Get the extended service ID by name %s", es)
                        cursor.execute(sql_select_query)
                        this_extended_services.append(str(cursor.fetchone()[0]))

                _additional_requirements = col.get('additional_requirements')
                if _additional_requirements:
                    req_list = _additional_requirements.split(',')
                    req_list = [each.strip() for each in req_list]
                    this_additional_requirements = {
                        'documents': req_list
                    }
                else:
                    this_additional_requirements = {}

                vp_sql = \
                    """INSERT INTO %s \
                    ("id", "country", "purpose", "category", "issue_method", "attributes", "form", "schema",\
                     "constraint", "submit_method", "receive_method", "region_of_residence", "currency",\
                     "extended_services", "additional_requirements") \
                    VALUES \
                    (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
                    """ % (
                        vp_table,
                        "'" + this_id + "'",
                        "'" + col['country'] + "'",
                        "'" + col['purpose'] + "'",
                        "'" + col['category'] + "'",
                        "'" + col['issue_method'] + "'",
                        "'" + json.dumps(this_attr) + "'",
                        "'" + col['form'] + "'" if col.get('form') else 'null',
                        "'" + this_schema + "'",
                        "'" + json.dumps(json.loads(col['constraint'])) + "'",
                        "'" + col['submit_method'] + "'" if col.get('submit_method') else 'null',
                        "'" + col['receive_method'] + "'" if col.get('receive_method') else 'null',
                        "'" + col['region_of_residence'] + "'",
                        "'" + col['currency'] + "'",
                        "'" + '{' + ','.join(this_extended_services) + '}' + "'" if this_extended_services else 'null',
                        "'" + json.dumps(this_additional_requirements) + "'" if this_additional_requirements else 'null'
                    )

                logger.info('SQL: %s', vp_sql)
                cursor.execute(cursor.mogrify(vp_sql))
                conn.commit()
                self.vp_index_id_mapping[col['ad_product_index']] = this_id

        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

        # Insert pricing table
        pricing_data = self.parse_xlsx_file(pricing_sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            for col in pricing_data:
                # Insert pricing
                logger.info('insert to %s table', pricing_table)

                # VP id
                this_visa_product_id = self.vp_index_id_mapping.get(col['ad_product_index'])

                # consulate id
                sql_select_query = """SELECT id FROM consulate WHERE name='%s'""" % (col['consulate'])
                logger.info("Get the consulate id by name: %s", col['consulate'])
                cursor.execute(sql_select_query)
                consulates = cursor.fetchone()
                this_consulate_id = consulates[0]

                # additional_fee
                this_additional_fee = dict()
                keys = ['voa_visa_fastlane', 'government_stamp_fee']
                for k in keys:
                    if col.get(k):
                        if type(col[k]) == str:
                            if col[k].startswith('¥'):
                                p = col[k][1:].strip()
                            else:
                                p = col[k].strip()
                            p = float(p.strip('\\u00a'))
                        else:
                            p = col[k]

                        this_additional_fee[k] = p

                # shipments
                _shipments = col.get('shipments', '')
                if _shipments:
                    this_shipments = json.loads(_shipments.strip())
                else:
                    this_shipments = {}

                _price = col.get('price')
                if _price:
                    if type(_price) == str and _price.startswith('¥'):
                        this_price = float(_price[1:])
                    else:
                        this_price = float(_price)
                else:
                    this_price = 'null'

                pricing_sql = \
                    """INSERT INTO %s \
                    (visa_product_id, consulate_id, currency, price, additional_fee, shipments, discount) \
                    VALUES \
                    (%s, %s, %s, %f, %s, %s, %s);
                    """ % (
                        pricing_table,
                        this_visa_product_id,
                        "'" + this_consulate_id + "'",
                        "'" + col['currency'] + "'",
                        this_price,
                        "'" + json.dumps(this_additional_fee) + "'" if this_additional_fee else 'null' ,
                        "'" + json.dumps(this_shipments) + "'" if this_shipments else 'null',
                        float(col['discount']) if col.get('discount') else 'null',
                    )
                logger.info('SQL: %s', pricing_sql)
                cursor.execute(pricing_sql)
                conn.commit()

        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def insert_consulate(self, sheet_name="consulate", table="consulate", prefix="cons"):
        """
        :param sheet_name:
        :param table:
        :param prefix:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            for col in data:

                has_address = all([col.get('address'), col.get('city'), col.get('zip_code'), col.get('state'), col.get('country')])
                has_contact = all([col.get('given_name'), col.get('surname'), col.get('phone')])

                # Insert address
                if has_address:
                    logger.info('insert to address table')
                    address_sql = \
                        """INSERT INTO address \
                        (address, address_in_native, city, zip_code, state, country) \
                        VALUES \
                        (%s, %s, %s, %s, %s, %s) \
                        RETURNING id;
                        """ % (
                            "'" + col['address'] + "'" if col.get('address') else 'null',
                            "'" + col['address_in_native'] + "'" if col.get('address_in_native') else 'null',
                            "'" + col['city'] + "'" if col.get('city') else 'null',
                            int(col['zip_code']) if col.get('zip_code') else 'null',
                            "'" + col['state'] + "'" if col.get('state') else 'null',
                            "'" + col['country'] + "'" if col.get('country') else 'null'
                        )
                    logger.info('SQL: %s', address_sql)
                    cursor.execute(address_sql)
                    address_id = cursor.fetchone()[0]
                else:
                    address_id = None

                # Insert contact
                if has_contact:
                    logger.info('insert to contact table')
                    contact_sql = \
                        """INSERT INTO contact \
                        (given_name, surname, phone, email, secondary_phone, secondary_email) \
                        VALUES \
                        (%s, %s, %s, %s, %s, %s) \
                        RETURNING id;
                        """ % (
                            "'" + col['given_name'] + "'" if col.get('given_name') else 'null',
                            "'" + col['surname'] + "'" if col.get('surname') else 'null',
                            int(col['phone']) if col.get('phone') else 'null',
                            "'" + col['email'] + "'" if col.get('email') else 'null',
                            int(col['secondary_phone']) if col.get('secondary_phone') else 'null',
                            "'" + col['secondary_email'] + "'" if col.get('secondary_email') else 'null'
                        )

                    logger.info('SQL: %s', contact_sql)
                    cursor.execute(contact_sql)
                    contact_id = cursor.fetchone()[0]
                else:
                    contact_id = None

                # Insert consulate
                logger.info('insert to %s table', table)
                _id = '%s-%s' % (prefix, uuid.uuid1().hex)

                _sql = \
                    """INSERT INTO %s \
                    (id, name, country, email, secondary_email, address, contact, timezone_name) \
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s);
                    """ % (table,
                           "'" + _id + "'",
                           "'" + col['name'] + "'",
                           "'" + col['country'] + "'",
                           "'" + col['email'] + "'",
                           "'" + col['secondary_email'] + "'" if col.get('secondary_email') else 'null',
                           address_id if address_id else 'null',
                           contact_id if contact_id else 'null',
                           "'" + col['timezone_name'] + "'"
                           )
                logger.info('SQL: %s', _sql)
                cursor.execute(_sql)

                # Insert organization
                logger.info('insert to organization table')
                organization_sql = \
                    """INSERT INTO organization \
                    (type, profile) \
                    VALUES ('%s', '%s');
                    """ % (table, _id)
                logger.info('SQL: %s', _sql)
                cursor.execute(organization_sql)

                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def insert_agency(self, sheet_name="agency", table="agency", prefix="agen"):
        """
        :param sheet_name:
        :param table:
        :param prefix:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            for col in data:

                has_address = all([col.get('address'), col.get('city'), col.get('zip_code'), col.get('state'), col.get('country')])
                has_contact = all([col.get('given_name'), col.get('surname'), col.get('phone')])

                # Insert address
                if has_address:
                    logger.info('insert to address table')
                    address_sql = \
                        """INSERT INTO address \
                        (address, address_in_native, city, zip_code, state, country) \
                        VALUES \
                        (%s, %s, %s, %s, %s, %s) \
                        RETURNING id;
                        """ % (
                            "'" + col['address'] + "'" if col.get('address') else 'null',
                            "'" + col['address_in_native'] + "'" if col.get('address_in_native') else 'null',
                            "'" + col['city'] + "'" if col.get('city') else 'null',
                            int(col['zip_code']) if col.get('zip_code') else 'null',
                            "'" + col['state'] + "'" if col.get('state') else 'null',
                            "'" + col['country'] + "'" if col.get('country') else 'null'
                        )
                    logger.info('SQL: %s', address_sql)
                    cursor.execute(address_sql)
                    address_id = cursor.fetchone()[0]
                else:
                    address_id = None

                # Insert contact
                if has_contact:
                    logger.info('insert to contact table')
                    contact_sql = \
                        """INSERT INTO contact \
                        (given_name, surname, phone, email, secondary_phone, secondary_email) \
                        VALUES \
                        (%s, %s, %s, %s, %s, %s) \
                        RETURNING id;
                        """ % (
                            "'" + col['given_name'] + "'" if col.get('given_name') else 'null',
                            "'" + col['surname'] + "'" if col.get('surname') else 'null',
                            "'" + str(col['phone']) + "'" if col.get('phone') else 'null',
                            "'" + col['email'] + "'" if col.get('email') else 'null',
                            "'" + col['secondary_phone'] + "'" if col.get('secondary_phone') else 'null',
                            "'" + col['secondary_email'] + "'" if col.get('secondary_email') else 'null'
                        )

                    logger.info('SQL: %s', contact_sql)
                    cursor.execute(contact_sql)
                    contact_id = cursor.fetchone()[0]
                else:
                    contact_id = None

                # Insert agency
                logger.info('insert to %s table', table)

                _id = '%s-%s' % (prefix, uuid.uuid1().hex)
                _sql = \
                    """INSERT INTO %s \
                    (id, name, domain, code, country, address, contact, timezone_name) \
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s);
                    """ % (table,
                           "'" + _id + "'",
                           "'" + col['name'] + "'",
                           "'" + col.get('domain') + "'" if col.get('domain') else 'null',
                           "'" + col['code'] + "'",
                           "'" + col['country'] + "'",
                           address_id if address_id else 'null',
                           contact_id if contact_id else 'null',
                           "'" + col['timezone_name'] + "'"
                           )
                logger.info('SQL: %s', _sql)
                cursor.execute(_sql)

                # Insert organization
                logger.info('insert to organization table')
                organization_sql = \
                    """INSERT INTO organization \
                    (type, profile) \
                    VALUES ('%s', '%s');
                    """ % (table, _id)
                logger.info('SQL: %s', _sql)
                cursor.execute(organization_sql)

                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def insert_corporation(self, sheet_name="corporation", table="corporation", prefix="corp"):
        """
        :param sheet_name:
        :param table:
        :param prefix:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            for col in data:

                has_address = all([col.get('address'), col.get('city'), col.get('zip_code'), col.get('state'), col.get('country')])
                has_contact = all([col.get('given_name'), col.get('surname'), col.get('phone')])

                # Insert address
                if has_address:
                    logger.info('insert to address table')
                    address_sql = \
                        """INSERT INTO address \
                        (address, address_in_native, city, zip_code, state, country) \
                        VALUES \
                        (%s, %s, %s, %s, %s, %s) \
                        RETURNING id;
                        """ % (
                            "'" + col['address'] + "'" if col.get('address') else 'null',
                            "'" + col['address_in_native'] + "'" if col.get('address_in_native') else 'null',
                            "'" + col['city'] + "'" if col.get('city') else 'null',
                            int(col['zip_code']) if col.get('zip_code') else 'null',
                            "'" + col['state'] + "'" if col.get('state') else 'null',
                            "'" + col['country'] + "'" if col.get('country') else 'null'
                        )
                    logger.info('SQL: %s', address_sql)
                    cursor.execute(address_sql)
                    address_id = cursor.fetchone()[0]
                else:
                    address_id = None

                # Insert contact
                if has_contact:
                    logger.info('insert to contact table')
                    contact_sql = \
                        """INSERT INTO contact \
                        (given_name, surname, phone, email, secondary_phone, secondary_email) \
                        VALUES \
                        (%s, %s, %s, %s, %s, %s) \
                        RETURNING id;
                        """ % (
                            "'" + col['given_name'] + "'" if col.get('given_name') else 'null',
                            "'" + col['surname'] + "'" if col.get('surname') else 'null',
                            "'" + col['phone'] + "'" if col.get('phone') else 'null',
                            "'" + col['email'] + "'" if col.get('email') else 'null',
                            "'" + col['secondary_phone'] + "'" if col.get('secondary_phone') else 'null',
                            "'" + col['secondary_email'] + "'" if col.get('secondary_email') else 'null'
                        )

                    logger.info('SQL: %s', contact_sql)
                    cursor.execute(contact_sql)
                    contact_id = cursor.fetchone()[0]
                else:
                    contact_id = None

                # Insert corporation
                logger.info('insert to %s table', table)

                _id = '%s-%s' % (prefix, uuid.uuid1().hex)
                _sql = \
                    """INSERT INTO %s \
                    (id, name, domain, code, country, address, contact, timezone_name) \
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s);
                    """ % (table,
                           "'" + _id + "'",
                           "'" + col['name'] + "'",
                           "'" + col['domain'] + "'",
                           "'" + col['code'] + "'",
                           "'" + col['country'] + "'",
                           address_id if address_id else 'null',
                           contact_id if contact_id else 'null',
                           "'" + col['timezone_name'] + "'"
                           )
                logger.info('SQL: %s', _sql)
                cursor.execute(_sql)

                # Insert organization
                logger.info('insert to organization table')
                organization_sql = \
                    """INSERT INTO organization \
                    (type, profile) \
                    VALUES ('%s', '%s');
                    """ % (table, _id)
                logger.info('SQL: %s', _sql)
                cursor.execute(organization_sql)

                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def insert_shipping_service(self, sheet_name="shipping_service", table="shipping_service"):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            for col in data:

                # Insert shipping_service
                logger.info('insert to %s table', table)
                _sql = \
                    """INSERT INTO %s \
                    (carrier, service, price, currency) \
                    VALUES \
                    (%s, %s, %f, %s);
                    """ % (table,
                           "'" + col['carrier'] + "'",
                           "'" + col['service'] + "'",
                           float(col['price']) if col.get('price') else 0,
                           "'" + col['currency'] + "'"
                           )
                logger.info('SQL: %s', _sql)
                cursor.execute(_sql)

                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def insert_extended_service(self, sheet_name="extended_service", table="extended_service"):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            for col in data:

                # Insert extended_service
                logger.info('insert to %s table', table)

                _sql = \
                    """INSERT INTO %s \
                    (name, currency, price, discount, description) \
                    VALUES \
                    (%s, %s, %f, %f, %s);
                    """ % (table,
                           "'" + col['name'] + "'",
                           "'" + col['currency'] + "'",
                           float(col['price']) if col.get('price') else 0,
                           float(col['discount']) if col.get('discount') else 0,
                           "'" + col['description'] + "'" if col.get('description') else 'null'
                           )

                logger.info('SQL: %s', _sql)
                cursor.execute(_sql)

                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def init_db_connection(self):
        """
        :return:
        """
        try:
            conn = psycopg2.connect(user=self.user,
                                    password=self.password,
                                    host=self.dbhost,
                                    port=self.port,
                                    database=self.database
                                    )

            logger.info("connected")
            cursor = conn.cursor()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to connect to database: %s", error)

        return conn, cursor

    def parse_xlsx_file(self, sheet_name, xlsx_name=None):
        """
        :param xlsx_name:
        :param sheet_name:
        :return:
        """
        if xlsx_name is None:
            xlsx_name = self.xlsx_name

        dirname, _ = os.path.split(os.path.abspath(__file__))
        dfs = pd.read_excel(os.path.join(dirname, xlsx_name), sheet_name=sheet_name)
        dfs = dfs.replace({pd.np.nan: None})

        header = dfs.columns.tolist()
        values = dfs.values.tolist()

        new_values = []
        for val in values:
            val = [v.strip() if type == str else v for v in val]
            new_values.append(val)

        return [dict(zip(header, val)) for val in new_values]


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Helper to insert data to Postgresql')
    parser.add_argument('--test', dest='test', action='store_true',
                        default=True, help='Run local database, if True')
    parser.add_argument('--password', dest='password', default=None,
                        help='Must have password, if it runs on cloud database')
    parser.add_argument('--log-level', default='INFO', help='Logging level')

    args = parser.parse_args()

    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.getLevelName(args.log_level.upper())
    )

    rc = 0
    try:
        if not args.test:
            logger.info('WARNING: Running on the cloud database')
            dbhost = db_cloud_profile.get('dbhost')
            database = db_cloud_profile.get('database')
            user = db_cloud_profile.get('user')
            password = args.password
        else:
            logger.info('Running on the local database')
            dbhost = db_local_profile.get('dbhost')
            database = db_local_profile.get('database')
            user = db_local_profile.get('user')
            password = db_local_profile.get('password')

        loader = Loader(dbhost, database, user, password)

        # loader.insert_consulate()

        # loader.insert_corporation()

        # loader.insert_agency()

        # loader.insert_shipping_service()

        # loader.insert_extended_service()

        loader.insert_visa_product_and_pricing()

    except Exception as exc:
        logger.exception('Unhandled exception: %s', exc)
        sys.exit(1)

    sys.exit(rc)
