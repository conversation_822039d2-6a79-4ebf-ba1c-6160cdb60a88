#!/usr/bin/env python

import argparse
import glob
import json
import logging
import os
import psycopg2
import sys


db_cloud_profile = {'dbhost':   'localhost',
                    'database': 'jp_earth_2.1.0_2',
                    'user':     'aria',
                    'password': '<password>'
                    }

logger = logging.getLogger(__name__)


class Loader(object):

    def __init__(self, dbhost, database, user, password, port=5432):
        """
        :param dbhost:
        :param database:
        :param user:
        :param password:
        :param port:
        """
        self.dbhost = dbhost
        self.database = database
        self.user = user
        self.password = password
        self.port = port

    def insert_visa_pods(self, table="visa_pods"):
        """
        :param table:
        :return:
        """
        data = self.parse_visa_pods()

        try:
            conn, cursor = self.init_db_connection()

            for this_id, this_name, this_category, this_sub_category, this_body in data:
                logger.info('insert to %s table', table)

                _sql = \
                    """INSERT INTO %s \
                    (id, name, category, sub_category, body) \
                    VALUES \
                    (%s, %s, %s, %s, %s);
                    """ % (table,
                           "'" + this_id + "'" if this_id else 'null',
                           "'" + this_name + "'" if this_name else 'null',
                           "'" + this_category + "'" if this_category else 'null',
                           "'" + this_sub_category + "'" if this_sub_category else 'null',
                           "'" + json.dumps(this_body) + "'" if this_body else 'null'
                           )

                logger.info('SQL: %s', _sql)
                cursor.execute(_sql)
                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def init_db_connection(self):
        """
        :return:
        """
        try:
            conn = psycopg2.connect(user=self.user,
                                    password=self.password,
                                    host=self.dbhost,
                                    port=self.port,
                                    database=self.database
                                    )

            logger.info("connected")
            cursor = conn.cursor()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to connect to database: %s", error)

        return conn, cursor

    def parse_visa_pods(self, folder='visa_pods'):
        """
        :param folder:
        :return:
        """
        ret_list = list()

        dirname, _ = os.path.split(os.path.abspath(__file__))
        abs_dirname = os.path.join(dirname, '../', folder)

        json_files = glob.glob("%s/*.json" % abs_dirname)
        for each_file in json_files:
            file_name = os.path.splitext(os.path.basename(each_file))[0]
            _id, _name = file_name.split('_', 1)
            with open(each_file, 'r') as fp:
                _body = json.load(fp)

            all_category = [(each.get('category'), each.get('sub_category')) for each in _body]
            assert len(set(all_category)) == 1, 'Only one category in one file'
            _category, _sub_category = all_category[0]

            ret_list.append((_id, _name, _category, _sub_category, _body))

        return ret_list


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Helper to insert data to Postgresql')
    parser.add_argument('--password', dest='password', default=None,
                        help='Must have password, if it runs on cloud database')
    parser.add_argument('--log-level', default='INFO', help='Logging level')

    args = parser.parse_args()

    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.getLevelName(args.log_level.upper())
    )

    rc = 0
    try:
        logger.info('WARNING: Running on the cloud database')
        dbhost = db_cloud_profile.get('dbhost')
        database = db_cloud_profile.get('database')
        user = db_cloud_profile.get('user')
        password = args.password

        loader = Loader(dbhost, database, user, password)
        loader.insert_visa_pods()

    except Exception as exc:
        logger.exception('Unhandled exception: %s', exc)
        sys.exit(1)

    sys.exit(rc)
