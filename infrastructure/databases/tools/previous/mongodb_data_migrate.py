#!/usr/bin/env python3

"""
It is a one time tool to load data collections from existing MongoDB to ElasticSearch
"""
from elasticsearch import Elasticsearch, RequestsHttpConnection
from pymongo import MongoClient


mongodb_host = '**************'
es_host = 'vpc-ad-es-upjnyogwuftl2oi2elb7pptnum.us-west-2.es.amazonaws.com'
username = 'admin'
password = '<password>'

db = 'traversal'

# Create a client instance of MongoClient
mongodb_client = MongoClient(
    'mongodb://{}:27017/'.format(mongodb_host),
    username=username,
    password=password
)


es_client = Elasticsearch(
    hosts=[{'host': es_host, 'port': 443}],
    use_ssl=True,
    verify_certs=True,
    connection_class=RequestsHttpConnection
)


# Get the database object
db = mongodb_client[db]


def _load(collection, keys):
    """
    :param collection:
    :param keys:
    :return:
    """
    print('Loading %s' % collection)
    es_client.indices.create(index=collection, ignore=400)
    col = db[collection]
    for doc in col.find():
        # parse each document
        each_dict = {}

        for k in keys:
            if collection == 'country' and k == 'stateList':
                each_dict['states'] = [_each['stateName'] for _each in doc.get(k, {})]
            else:
                each_dict[k] = doc.get(k, '')

        es_client.index(index=collection, doc_type='_doc', body=each_dict)


def load_country():
    """
    :return:
    """
    collection = 'country'
    keys = ['name', 'iso_alpha2', 'iso_alpha3', 'continent', 'national',
            'currency', 'language', 'stateList']
    _load(collection, keys)


def load_airport():
    """
    :return:
    """
    collection = 'airport'
    keys = ['name', 'city', 'city_code', 'country_code', 'country_name',
            'region', 'iata', 'timezone', 'fs', 'icao', 'active']
    _load(collection, keys)


def load_airline():
    """
    :return:
    """
    collection = 'airline'
    keys = ['iata', 'icao', 'fs', 'name', 'active']
    _load(collection, keys)


def load_aircraft():
    """
    :return:
    """
    collection = 'aircraft'
    keys = ['name', 'iata']
    _load(collection, keys)


def load_currency():
    """
    :return:
    """
    collection = 'currency'
    keys = ['currency', 'code', 'symbol', 'rate']
    _load(collection, keys)


load_country()

load_airline()

load_airport()

load_currency()

load_aircraft()

