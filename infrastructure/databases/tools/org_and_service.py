#!/usr/bin/env python
"""
The source xlsx file:
https://docs.google.com/spreadsheets/d/1fkFQYka6fxUL6FyOIFId0Sc_zjMQ72a7szf8_qXZ5fQ/edit#gid=1811532211

"""

import argparse
import logging
import os
import pandas as pd
import psycopg2
import sys
import uuid
import json

ENV = 'stag'
CURRENT_RELEASE = '3.2.0'

# Database config
DB_HOST = os.getenv('DB_HOST') or 'localhost'
DB_PASSWORD = os.getenv('DB_PASSWORD') or 'ariadirect2020'
DATABASE = 'ariadirect_stag' if ENV == 'stag' else 'ariadirect_prod'

# Google Excel config
VISA_XLSX_NAME = '{} visa.xlsx'.format(CURRENT_RELEASE)
ETS_XLSX_NAME = '{} ets.xlsx'.format(CURRENT_RELEASE)

db_cloud_profile = {
    'dbhost':   DB_HOST,
    'database': DATABASE,
    'user':     'aria',
    'password': DB_PASSWORD,
}

# Consulate/ETS Provider will be sent <NAME_EMAIL> on STAGING environment
STAGING_EMAIL = "<EMAIL>"

logger = logging.getLogger(__name__)


class Loader(object):

    def __init__(self, dbhost, database, user, password, port=5432):
        """
        :param dbhost:
        :param database:
        :param user:
        :param password:
        :param port:
        """
        self.dbhost = dbhost
        self.database = database
        self.user = user
        self.password = password
        self.port = port
        self.xlsx_name = VISA_XLSX_NAME

    def run(self):
        """
        :return:
        """
        # self.update_consulate()

        # self.update_corporation()

        self.update_ets_provider()

        # self.update_shipping_service()

    def update_consulate(self, sheet_name="consulate", table="consulate", prefix="cons"):
        """
        :param sheet_name:
        :param table:
        :param prefix:
        :return:
        """
        REPLACE_EMAIL = ENV == 'jp'

        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            for col in data:
                this_address = {
                    'address':            col.get('address'),
                    'address_in_native':  col.get('address_in_native'),
                    'city':               col.get('city'),
                    'zip_code':           str(int(col['zip_code'])) if col.get('zip_code') else None,
                    'state':              col.get('state'),
                    'country':            col.get('country')
                }

                this_contact = {
                    'given_name':         col.get('given_name'),
                    'surname':            col.get('surname'),
                    'phone':              str(int(col.get('phone'))) if col.get('phone') else None,
                    'email':              STAGING_EMAIL if REPLACE_EMAIL else col.get('email'),
                    'secondary_phone':    col.get('secondary_phone'),
                    'secondary_email':    col.get('secondary_email')
                }

                this_org_id = col.get('org_id')
                this_id = col.get('id')
                if this_id is None:
                    this_id = '%s-%s' % (prefix, uuid.uuid1().hex)

                # Insert organization, if it is a new one
                if this_org_id is None:
                    logger.info('insert to organization table')
                    cursor.execute(
                        """INSERT INTO organization DEFAULT VALUES RETURNING id;""")
                    this_org_id = cursor.fetchone()[0]

                # Insert consulate
                logger.info('insert to %s table', table)

                _sql = \
                    """INSERT INTO {0}
                    (id, name, country, served_countries, served_area, website, email,
                     secondary_email, address, contact, timezone_name, org_id)
                    VALUES 
                    ({1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10}, {11}, {12})
                    ON CONFLICT(name) DO UPDATE
                      SET country={3},
                          served_countries={4},
                          served_area={5},
                          website={6},
                          email={7},
                          secondary_email={8},
                          address={9},
                          contact={10},
                          timezone_name={11},
                          org_id={12};
                    """.format(table,
                               "'" + this_id + "'",
                               "'" + col['name'] + "'",
                               "'" + col['country'] + "'",
                               "'" + col['served_countries'] +
                               "'" if col.get('served_countries') else 'null',
                               "'" + col['served_area'] +
                               "'" if col.get('served_area') else 'null',
                               "'" + col['website'] +
                               "'" if col.get('website') else 'null',
                               "'" + col['email'] +
                               "'" if col.get('email') else 'null',
                               "'" + col['secondary_email'] +
                               "'" if col.get(
                                   'secondary_email') else 'null',
                               "'" + json.dumps(this_address) +
                               "'" if this_address else 'null',
                               "'" + json.dumps(this_contact) +
                               "'" if this_contact else 'null',
                               "'" + col['timezone_name'] +
                               "'" if col.get('timezone_name') else 'null',
                               int(this_org_id)
                               )
                logger.info('SQL: %s', _sql)
                cursor.execute(_sql)

                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def update_corporation(self, sheet_name="corporation", table="corporation", prefix="corp"):
        """
        :param sheet_name:
        :param table:
        :param prefix:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            for col in data:

                this_address = {
                    'address':            col.get('address'),
                    'address_in_native':  col.get('address_in_native'),
                    'city':               col.get('city'),
                    'zip_code':           str(int(col['zip_code'])) if col.get('zip_code') else None,
                    'state':              col.get('state'),
                    'country':            col.get('country')
                }

                this_contact = {
                    'given_name':         col.get('given_name'),
                    'surname':            col.get('surname'),
                    'phone':              str(int(col.get('phone'))) if col.get('phone') else None,
                    'email':              col.get('email'),
                    'secondary_phone':    col.get('secondary_phone'),
                    'secondary_email':    col.get('secondary_email')
                }

                this_org_id = col.get('org_id')
                this_id = col.get('id')
                if this_id is None:
                    this_id = '%s-%s' % (prefix, uuid.uuid1().hex)

                # Insert organization, if it is a new one
                if this_org_id is None:
                    logger.info('insert to organization table')
                    cursor.execute(
                        """INSERT INTO organization DEFAULT VALUES RETURNING id;""")

                    this_org_id = cursor.fetchone()[0]

                # Insert corporation
                logger.info('insert to %s table', table)

                _sql = \
                    """INSERT INTO {0}
                    (id, name, code, domain, country, discount, address, contact, timezone_name, org_id)
                    VALUES 
                    ({1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10})
                    ON CONFLICT(name, code) DO UPDATE
                      SET domain={4},                          
                          country={5},
                          discount={6},
                          address={7},
                          contact={8},
                          timezone_name={9},
                          org_id={10};
                    """.format(table,
                               "'" + this_id + "'",
                               "'" + col['name'] + "'",
                               "'" + col['code'] +
                               "'" if col.get('code') else 'null',
                               "'" + col['domain'] +
                               "'" if col.get('domain') else 'null',
                               "'" + col['country'] +
                               "'" if col.get('country') else 'null',
                               col['discount'] if col.get(
                                   'discount') else 'null',
                               "'" + json.dumps(this_address) +
                               "'" if this_address else 'null',
                               "'" + json.dumps(this_contact) +
                               "'" if this_contact else 'null',
                               "'" + col['timezone_name'] +
                               "'" if col.get('timezone_name') else 'null',
                               int(this_org_id)
                               )

                logger.info('SQL: %s', _sql)
                cursor.execute(_sql)

                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    # def update_agency(self, sheet_name="agency", table="agency", prefix="agen"):
    #     """
    #     :param sheet_name:
    #     :param table:
    #     :param prefix:
    #     :return:
    #     """
    #     self.update_corporation(sheet_name=sheet_name, table="agency", prefix=prefix)

    def update_ets_provider(self, sheet_name="ets_provider", table="ets_provider", prefix="etsp"):
        """
        :param sheet_name:
        :param table:
        :param prefix:
        :return:
        """
        REPLACE_EMAIL = ENV == 'jp'

        data = self.parse_xlsx_file(sheet_name, xlsx_name=ETS_XLSX_NAME)
        try:
            conn, cursor = self.init_db_connection()

            for col in data:

                this_address = {
                    'address':            col.get('address'),
                    'address_in_native':  col.get('address_in_native'),
                    'city':               col.get('city'),
                    'zip_code':           str(int(col['zip_code'])) if col.get('zip_code') else None,
                    'state':              col.get('state'),
                    'country':            col.get('country')
                }

                this_contact = {
                    'given_name':         col.get('given_name'),
                    'surname':            col.get('surname'),
                    'phone':              str(int(col.get('phone'))) if col.get('phone') else None,
                    'email':              STAGING_EMAIL if REPLACE_EMAIL else col.get('email'),
                    'secondary_phone':    col.get('secondary_phone'),
                    'secondary_email':    col.get('secondary_email')
                }

                this_org_id = col.get('org_id')
                this_id = col.get('id')
                if this_id is None:
                    this_id = '%s-%s' % (prefix, uuid.uuid1().hex)

                # Insert organization, if it is a new one
                if this_org_id is None:
                    logger.info('insert to organization table')
                    cursor.execute(
                        """INSERT INTO organization DEFAULT VALUES RETURNING id;""")

                    this_org_id = cursor.fetchone()[0]

                logger.info('insert to %s table', table)

                _sql = \
                    """INSERT INTO {0}
                    (id, name, country, address, contact, served_countries,
                     served_area, served_services, website, timezone_name, org_id)
                    VALUES
                    ({1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10}, {11})
                    ON CONFLICT(name) DO UPDATE
                      SET country={3},
                          address={4},
                          contact={5},
                          served_countries={6},
                          served_area={7},
                          served_services={8},
                          website={9},
                          timezone_name={10},
                          org_id={11};
                    """.format(table,
                               "'" + this_id + "'",
                               "'" + col['name'] + "'",
                               "'" + col['country'] +
                               "'" if col.get('country') else 'null',
                               "'" + json.dumps(this_address) +
                               "'" if this_address else 'null',
                               "'" + json.dumps(this_contact) +
                               "'" if this_contact else 'null',
                               "'" + col['served_countries'] +
                               "'" if col.get(
                                   'served_countries') else 'null',
                               "'" + col['served_area'] +
                               "'" if col.get('served_area') else 'null',
                               "'{" + json.dumps(col['served_services']) + "}'" if col.get(
                                   'served_services') else 'null',
                               "'" + col['website'] +
                               "'" if col.get('website') else 'null',
                               "'" +
                               col['timezone_name'] + "'" if col.get(
                                   'timezone_name') else "'America/Los_Angeles'",
                               int(this_org_id)
                               )
                logger.info('SQL: %s', _sql)
                cursor.execute(_sql)

                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def update_shipping_service(self, sheet_name="shipping_service", table="shipping_service"):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()
            for col in data:

                # Insert shipping_service
                logger.info('Update to %s table', table)

                _sql = \
                    """INSERT INTO {0}
                    (id, carrier, service, shipping_time, price, currency)
                    VALUES
                    ({1}, {2}, {3}, {4}, {5}, {6})
                    ON CONFLICT(id) DO UPDATE
                      SET carrier={2},
                          service={3}, 
                          shipping_time={4},
                          price={5},
                          currency={6};
                    """.format(table,
                               col['id'],
                               "'" + col['carrier'] + "'",
                               "'" + col['service'] + "'",
                               "'" + str(int(col['shipping_time'])) + "'",
                               float(col['price']) if col.get('price') else 0,
                               "'" + col['currency'] + "'"
                               )
                logger.info('SQL: %s', _sql)
                cursor.execute(_sql)

                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def init_db_connection(self):
        """
        :return:
        """
        try:
            conn = psycopg2.connect(user=self.user,
                                    password=self.password,
                                    host=self.dbhost,
                                    port=self.port,
                                    database=self.database
                                    )

            logger.info("connected")
            cursor = conn.cursor()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to connect to database: %s", error)

        return conn, cursor

    def parse_xlsx_file(self, sheet_name, xlsx_name=None):
        """
        :param xlsx_name:
        :param sheet_name:
        :return:
        """
        if xlsx_name is None:
            xlsx_name = self.xlsx_name

        dirname, _ = os.path.split(os.path.abspath(__file__))
        dfs = pd.read_excel(os.path.join(
            dirname, xlsx_name), sheet_name=sheet_name)
        dfs = dfs.replace({pd.np.nan: None})

        header = dfs.columns.tolist()
        values = dfs.values.tolist()

        new_values = []
        for val in values:
            if all([v is None for v in val]):
                continue
            val = [v.strip() if type == str else v for v in val]
            new_values.append(val)

        return [dict(zip(header, val)) for val in new_values]


if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        description='Helper to insert data to Postgresql')
    parser.add_argument('--log-level', default='INFO', help='Logging level')

    args = parser.parse_args()

    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.getLevelName(args.log_level.upper())
    )

    rc = 0
    try:
        logger.info('WARNING: Running on the cloud database')
        dbhost = db_cloud_profile.get('dbhost')
        database = db_cloud_profile.get('database')
        user = db_cloud_profile.get('user')
        password = db_cloud_profile.get('password')

        loader = Loader(dbhost, database, user, password)

        loader.run()

    except Exception as exc:
        logger.exception('Unhandled exception: %s', exc)
        sys.exit(1)

    sys.exit(rc)
