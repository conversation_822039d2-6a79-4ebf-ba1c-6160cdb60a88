#!/usr/bin/env python

"""
1. Generate the pods json files, which are going to be checked in git (optional)
2. Insert/update the visa pods column to the DB "pods" table
3. Based on the query, scan all visa product to update the "schema pods"
4. For each product, update the "schema" from the schema pods", replaced with reference
"""

import argparse
import copy
import json
import logging
import os
import pandas as pd
import psycopg2
import re
import sys
import gspread

ENV = os.getenv('ENV') or 'stag'
if ENV == 'prod':
    GOOGLE_SHEET_ID = '1QOLUqHy_lbsUItUDedDbj8UVxr1ysybjbAD2Z6DWuUM'
    DB_HOST = os.getenv('DB_HOST') or 'localhost'
    DB_PASSWORD = os.getenv('DB_PASSWORD') or 'ariadirect2020'
    DATABASE = 'ariadirect_prod'
else:
    GOOGLE_SHEET_ID = '1QOLUqHy_lbsUItUDedDbj8UVxr1ysybjbAD2Z6DWuUM'
    DB_HOST = os.getenv('DB_HOST') or 'localhost'
    DB_PASSWORD = os.getenv('DB_PASSWORD') or 'ariadirect2020'
    DATABASE = 'ariadirect_stag'

# https://docs.google.com/spreadsheets/d/1jENpAlO-m455DFhtHtCYAOyXPgOHx-ToU3rsfVo3Wtg/edit#gid=1579956526


# Database config

db_cloud_profile = {
    'dbhost':   DB_HOST,
    'database': DATABASE,
    'user':     'aria',
    'password': DB_PASSWORD,
    'port': 5432
}

PRODUCT_COLUMN_INDEX = 2

VISA = os.getenv('VISA') == 'TRUE'
FASTLANE = os.getenv('FASTLANE') == 'TRUE'
PASSPORT = os.getenv('PASSPORT') == 'TRUE'
GLOBAL_ENTRY = os.getenv('GLOBAL_ENTRY') == 'TRUE'
ID_PHOTO = os.getenv('ID_PHOTO') == 'TRUE'
TSA_PRECHECK = os.getenv('TSA_PRECHECK') == 'TRUE'
SENTRI = os.getenv('SENTRI') == 'TRUE'
NEXUS = os.getenv('NEXUS') == 'TRUE'
FAST_TRUCK = os.getenv('FAST_TRUCK') == 'TRUE'
COVID_TEST = os.getenv('COVID_TEST') == 'TRUE'
COUNTRY_TOURIST = os.getenv('COUNTRY_TOURIST') == 'TRUE'

assert [VISA, FASTLANE, PASSPORT, GLOBAL_ENTRY, ID_PHOTO, TSA_PRECHECK, SENTRI, NEXUS,
        FAST_TRUCK, COVID_TEST, COUNTRY_TOURIST].count(True) <= 1, "Only one at a time"

if VISA:
    sheet_name = 'new_visa'
    THIS_SERVICE_TYPE = 'new_visa'

if FASTLANE:
    sheet_name = 'fastlane'
    THIS_SERVICE_TYPE = 'fastlane'

if PASSPORT:
    sheet_name = 'passport'
    THIS_SERVICE_TYPE = 'passport'

if GLOBAL_ENTRY:
    sheet_name = 'global_entry'
    THIS_SERVICE_TYPE = 'global_entry'

if ID_PHOTO:
    sheet_name = 'id_photo'
    THIS_SERVICE_TYPE = 'id_photo'

if TSA_PRECHECK:
    sheet_name = 'tsa_precheck'
    THIS_SERVICE_TYPE = 'tsa_precheck'

if SENTRI:
    sheet_name = 'sentri'
    THIS_SERVICE_TYPE = 'sentri'

if NEXUS:
    sheet_name = 'nexus'
    THIS_SERVICE_TYPE = 'nexus'

if FAST_TRUCK:
    sheet_name = 'fast_truck'
    THIS_SERVICE_TYPE = 'fast_truck'

if COUNTRY_TOURIST:
    sheet_name = 'country_tourist'
    THIS_SERVICE_TYPE = 'country_tourist'

if COVID_TEST:
    sheet_name = 'covid_test'
    THIS_SERVICE_TYPE = 'covid_test'

# if VISA:
#     PRODUCT_DB_TABLE = 'visa_product'
#     PODS_DB_TABLE = 'visa_pods'
# else:
PRODUCT_DB_TABLE = 'ets'
PODS_DB_TABLE = 'ets_pods'
QUERY_PODS_DB_TABLE = 'ets_query_pods'

logger = logging.getLogger(__name__)


class Loader(object):

    def __init__(self, dbhost, database, user, password, port=5432):
        """
        :param dbhost:
        :param database:
        :param user:
        :param password:
        :param port:
        """
        self.dbhost = dbhost
        self.database = database
        self.user = user
        self.password = password
        self.port = port

    def run(self):
        """
        :return:
        """
        # 1. parse xlsx file
        logger.info('Step 1')
        product_pods, pods_amendment = self.parse_xlsx_file()

        # 2. Verify all visa/ets per country are good, no visa/ets id missing and no visa/ets id duplicated
        # [([etsid, ...], {pod_id1: True, pod_id2: {amendment}})...]
        logger.info('Step 2')
        product_group_pods_pair = self.check2(pods_amendment)

        # 3. update ets_pods to DB
        logger.info('Step 3')
        self.update_pods_table(product_pods)

        # 4. insert schema_pods and pods_amendment to visa/ets
        logger.info('Step 4')
        self.update_product_table_with_amendment(product_group_pods_pair)

        # 5. merge schema_pods and pods_amendment to schema
        # Note: it could be run independently, handle the nested here
        logger.info('Step 5')
        self.update_product_table_with_schema()

    def check(self, pods_amendment):
        """
        :param pods_amendment:
        :return:
        """

        # {
        #   "VNM": {
        #    "all": [...],
        #    "group": [...]
        # }
        country_product = dict()

        # [
        #   ([etsid, ...], {pod_id1: True, pod_id2: {amendment}})
        #   ...
        # ]
        product_group_pods_pair = list()

        for (name, query), pods_mapping in pods_amendment.items():
            try:
                query_dict = json.loads(query)
            except Exception:
                logger.exception('%s; %s', name, query)
                raise

            country = query_dict.get('country')

            if country not in country_product:
                country_product[country] = dict()
                country_product[country]['group'] = list()
                # if VISA:
                #     country_product[country]['all'] = self.query_vp_ids(
                #         country)
                # else:
                service_type = THIS_SERVICE_TYPE
                country_product[country]['all'] = self.query_ets_ids(
                    country, service_type)

            logger.info('Querying AD Product group %s', name)

            # if VISA:
            #     issue_method = query_dict.get('issue_method')
            #     validity = query_dict.get('validity')
            #     region_of_residence = query_dict.get('region_of_residence')
            #     purpose = query_dict.get('purpose')
            #     nationality = query_dict.get('nationality')
            #     apply_for = query_dict.get('apply_for')
            #     tag = query_dict.get('tag')
            #     user_age = query_dict.get('user_age')

            #     product_ids = self.query_vp_ids(country, issue_method=issue_method,
            #                                     validity=validity, ror=region_of_residence,
            #                                     purpose=purpose, nationality=nationality,
            #                                     apply_for=apply_for,
            #                                     tag=tag,
            #                                     user_age=user_age
            #                                     )

            # else:
            service_type = THIS_SERVICE_TYPE
            airport = query_dict.get('airport')
            tasks = query_dict.get('tasks')
            terminal = query_dict.get('terminal')
            region_of_residence = query_dict.get('region_of_residence')
            user_age = query_dict.get('user_age')
            condition = query_dict.get('condition')
            document_type = query_dict.get('document_type')
            processing_time = query_dict.get('processing_time')
            apply_for = query_dict.get('apply_for')
            tag = query_dict.get('tag')
            purpose = query_dict.get('purpose')
            validity = query_dict.get('validity')

            product_ids = self.query_ets_ids(country, service_type=service_type, airport=airport, tasks=tasks,
                                             terminal=terminal, region_of_residence=region_of_residence,
                                             user_age=user_age, condition=condition, document_type=document_type,
                                             processing_time=processing_time, apply_for=apply_for, tag=tag, purpose=purpose,
                                             validity=validity
                                             )
            if not product_ids:
                logger.error(
                    'The row "%s" does not have any AD products', name)

            country_product[country]['group'].extend(product_ids)
            product_group_pods_pair.append((product_ids, pods_mapping))

        # for country, product_ids in country_product.items():
        #     logger.info('Check country %s', country)

        #     if sorted(product_ids['all']) != sorted(product_ids['group']):
        #         logger.info(query_dict)
        #         logger.info('AD Product list from DB:  %s \n', sorted(product_ids['all']))
        #         logger.info('AD Product list queried from product pods:  %s\n', sorted(product_ids['group']))
        #         raise Exception('Some AD Product IDs are missing or duplicated')

        return product_group_pods_pair

    def check2(self, pods_amendment):
        """
        :param pods_amendment:
        :return:
        """

        product_group_pods_pair = list()

        for (name, query), pods_mapping in pods_amendment.items():
            product_group_pods_pair.append((name, pods_mapping))

        return product_group_pods_pair

    def update_product_table_with_schema(self):
        """
        :return:
        """
        product_list = self.query_product()
        pods = dict(self.query_pods())

        tt = len(product_list)
        for i, (product_id, schema_pods, amendment) in enumerate(product_list):
            logger.info("Checking product %s", str(product_id))
            schema_list = []

            try:
                if amendment:
                    clean_amendment = self.remove_amendment_pod_reference(
                        amendment)
                else:
                    clean_amendment = None
            except Exception as exc:
                logger.exception(exc)
                raise

            for sp in schema_pods:
                sp_body = copy.deepcopy(pods.get(sp))

                # Update with the amendment
                if clean_amendment and str(sp) in clean_amendment:
                    sp_body.update(clean_amendment.get(str(sp)))

                schema_list.append(sp_body)

            logger.info('Step 5: Update table %s: %d / %d',
                        PRODUCT_DB_TABLE, i + 1, tt)

            self.sql_update_schema_in_product_table(product_id, schema_list)

    def query_product(self):
        """
        :return:
        """
        try:
            conn, cursor = self.init_db_connection()

            _sql = "SELECT id, schema_pods, pods_amendment from {} where service_type='{}' ;".\
                format(PRODUCT_DB_TABLE, THIS_SERVICE_TYPE)

            logger.info('SQL: %s', _sql)
            cursor.execute(_sql)
            result = cursor.fetchall()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to query the records", error)
            raise
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

        return result

    def query_pods(self):
        """
        :return:
        """
        db_table = PODS_DB_TABLE
        try:
            conn, cursor = self.init_db_connection()

            _sql = "SELECT id, body from {};".format(db_table)

            logger.info('SQL: %s', _sql)
            cursor.execute(_sql)
            result = cursor.fetchall()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to query the records", error)
            raise
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

        return result

    def remove_amendment_pod_reference(self, amendment):
        """
        :param amendment:
        :return:
        """
        clean_pods_dict = dict(self.query_pods())

        clean_amendment_dict = dict()
        new_tmp_amendment = copy.deepcopy(amendment)

        for _ in range(3):  # go through 3 times to remove the nested ones

            tmp_amendment = copy.deepcopy(new_tmp_amendment)
            new_tmp_amendment = dict()

            for pid, pbody in tmp_amendment.items():
                findall = self._match_pattern(json.dumps(pbody))
                findall = list(set(findall))

                # findall example: ['{{123456789012}}', '{{1234567890}}']
                # Case 1: no pods reference
                if not findall:
                    clean_pods_dict[pid].update(pbody)
                    clean_amendment_dict[pid] = pbody
                    continue

                # Case 2: has pods reference
                for each in findall:
                    each_array = each.split("|")
                    each_pid = each_array[0].strip("{{")  if len(each_array)>1 else each_array[0].strip("{{").strip("}}")
                    additional_atributes = each_array[1] if len(each_array)>1 else None
                    additional_atributes = additional_atributes[:-2].replace('\\"', '"') if additional_atributes else None
                    additional_atributes_dict = json.loads(additional_atributes) if additional_atributes else {}
                    # if there is a pod reference, which is amendment. It is nested, skip
                    if each_pid in tmp_amendment.keys():
                        new_tmp_amendment[pid] = {**pbody, **additional_atributes_dict}
                        continue

                clean_body = self._magic_replace(pbody, clean_pods_dict)
                clean_pods_dict[pid] = clean_body
                clean_amendment_dict[pid] = clean_body
        assert len(new_tmp_amendment) == 0, 'No more pods reference'
        return clean_amendment_dict

    def remove_pod_reference(self, master_pod_values_pair, pods):
        """
        :param pods:
        :return:
        """
        clean_pods_dict = dict()

        new_tmp_pods = copy.deepcopy(pods)

        for _ in range(4):  # go through 3 times to remove the nested ones

            tmp_pods = copy.deepcopy(new_tmp_pods)
            new_tmp_pods = dict()

            for pid, pbody in tmp_pods.items():
                not_clean = False

                findall = self._match_pattern(json.dumps(pbody))
                findall = list(set(findall))

                # findall example: ['{{123456789012}}', '{{1234567890}}']
                # Case 1: no pods reference
                if not findall:
                    clean_pods_dict[pid] = pbody
                    continue

                # Case 2: has pods reference
                for each in findall:
                    each_pid = each.strip("{{").strip("}}")
                    # if there is a pod reference, which is not clean yet, skip
                    if each_pid not in clean_pods_dict:

                        new_tmp_pods[pid] = pbody
                        not_clean = True
                        break

                if not_clean:
                    continue

                clean_body = self._magic_replace(pbody, clean_pods_dict)
                clean_pods_dict[pid] = clean_body

        print('new_tmp_pods', new_tmp_pods)
        assert len(new_tmp_pods) == 0, 'No more pods reference'

        return clean_pods_dict

    def _magic_replace(self, body_dict, clean_pods, obj=True):
        """
        :param body_dict:
        :param clean_pods:
        :param obj:
        :return:
        """
        reference_allowed = ['initial_value', 'range_from',
                             'range_to', 'option_choice', 'object']

        new_dict = copy.deepcopy(body_dict)

        for k, v in body_dict.items():
            # Nothing update
            if k not in reference_allowed:
                continue

            if k in ['initial_value', 'range_from', 'range_to']:
                if v.get('field'):
                    findall = self._match_pattern(v.get('field'))
                    if findall:
                        _pid = findall[0].strip('{{}}')
                        new_dict[k]['field'] = clean_pods.get(_pid)

            elif k in ['option_choice']:
                new_v = copy.deepcopy(v)
                for kk, vv in v.items():
                    # Only scan when it is a list
                    if type(vv) == list:
                        new_list = list()
                        for each in vv:
                            findall = self._match_pattern(each)
                            if findall:
                                array = findall[0].split("|")
                                _pid = array[0].strip("{{")  if len(array)>1 else array[0].strip("{{").strip("}}")
                                additional_atributes = array[1] if len(array)>1 else None
                                additional_atributes = additional_atributes[:-2].replace('\\"', '"') if additional_atributes else None
                                additional_atributes_dict = json.loads(additional_atributes) if additional_atributes else {}
                                pod = {**clean_pods.get(_pid),**additional_atributes_dict}
                                new_list.append(pod)
                            else:
                                new_list.append(each)
                        new_v[kk] = new_list

                new_dict[k] = new_v

            elif k in ['object'] and obj:
                new_list = list()
                vv = copy.deepcopy(v)

                if type(vv) == list:
                    for each in vv:
                        findall = self._match_pattern(each)
                        if findall:
                            array = findall[0].split("|")
                            _pid = array[0].strip("{{")  if len(array)>1 else array[0].strip("{{").strip("}}")
                            if clean_pods.get(_pid):
                                additional_atributes = array[1] if len(array)>1 else None
                                additional_atributes = additional_atributes[:-2].replace('\\"', '"') if additional_atributes else None
                                additional_atributes_dict = json.loads(additional_atributes) if additional_atributes else {}
                                pod = {**clean_pods.get(_pid),**additional_atributes_dict}
                                pod={**clean_pods.get(_pid), **additional_atributes_dict}
                                new_list.append(pod)
                        else:
                            new_list.append(each)

                    new_dict[k] = new_list

        return new_dict

    def _match_pattern(self, s):
        """Check the replacement regular expression
        :param s:
        :return:
        """
        if type(s) != str:
            return None

        pattern = re.compile(r'\{\{[a-zA-Z0-9_-|,\"\\:\}]+\}\}')

        return pattern.findall(s)

    def sql_update_schema_in_product_table(self, product_id, schema):
        """
        :param product_id:
        :param schema:
        :return:
        """
        db_table = PRODUCT_DB_TABLE

        # Update DB
        try:
            conn, cursor = self.init_db_connection()

            _sql = "UPDATE {0} SET schema = {1} where id={2};".\
                format(db_table,
                       "$$" + json.dumps(schema) + "$$" if schema else 'null',
                       product_id
                       )
            logger.info('Update product %s', str(product_id))
            logger.info('SQL: %s', _sql)

            cursor.execute(_sql)
            conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to update records", error)
            raise
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def update_product_table_with_amendment(self, product_group_pods_pair):
        """
        :param product_group_pods_pair:
        :return:
        """
        db_table = PRODUCT_DB_TABLE

        try:
            conn, cursor = self.init_db_connection()

            tt = len(product_group_pods_pair)
            for i, (product_name, pods_mapping) in enumerate(product_group_pods_pair):

                logger.info('Step 4: Update table %s: %d / %d',
                            db_table, i + 1, tt)

                schema_pods = [str(each)
                               for each in pods_mapping['schema_pods']]
                pods_amendment = pods_mapping['pods_amendment']

                _sql = \
                    """UPDATE {0} SET
                       schema_pods=%s, 
                       pods_amendment=%s
                       where name=%s
                    """.format(db_table)
                logger.info('SQL: %s', _sql)

                cursor.execute(_sql, (
                    json.dumps(schema_pods).replace(
                        '[', '{').replace(']', '}'),
                    json.dumps(pods_amendment),
                    product_name
                ))
                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.exception("Failed to update records", error)
            raise
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def update_pods_table(self, pods_list):
        """
        :param pods_list:
        :return:
        """
        try:
            conn, cursor = self.init_db_connection()

            tt = len(pods_list)
            for i, pod in enumerate(pods_list):
                logger.info('Step 3: Update table %s: %d / %d',
                            PODS_DB_TABLE, i + 1, tt)

                args = {
                    'id': pod['id'],
                    'name': pod['name'],
                    'title': pod['title'],
                    'category': pod['category'],
                    'sub_category': pod['sub_category'],
                    'order': pod['order'] or -1,
                    'service': [pod['service']],
                    'pod_type': pod['pod_type'],
                    'body': json.dumps(pod['body']),
                    'tags': pod['tags'],
                }
                _sql = \
                    """INSERT INTO {0} 
                    (id, name, title, category, sub_category, "order", body, services, pod_type, tags)
                    VALUES 
                    (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT(id) DO UPDATE 
                       SET name=EXCLUDED.name, 
                           title=EXCLUDED.title, 
                           category=EXCLUDED.category,
                           sub_category=EXCLUDED.sub_category,  
                           "order"=EXCLUDED.order, 
                           body=EXCLUDED.body, 
                           services=array(SELECT DISTINCT unnest(array_append({0}.services, %s))), 
                           pod_type=EXCLUDED.pod_type, 
                           tags=EXCLUDED.tags;
                    """.format(PODS_DB_TABLE)
                logger.info('SQL: %s', _sql)
                cursor.execute(_sql, (
                    args['id'],
                    args['name'],
                    args['title'],
                    args['category'],
                    args['sub_category'],
                    args['order'],
                    args['body'],
                    args['service'],
                    args['pod_type'],
                    args['tags'],
                    pod['service']
                ))
                conn.commit()

        except (Exception, psycopg2.Error) as error:
            logger.exception("Failed to insert record into table", error)
            raise
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def query_ets_ids(self, country, service_type=None, airport=None, tasks=None,
                      terminal=None, region_of_residence=None, user_age=None, condition=None,
                      document_type=None, processing_time=None, apply_for=None, tag=None, purpose=None, validity=None):
        """
        :param country:
        :param service_type:
        :param airport:
        :param tasks:
        :param terminal:
        :param region_of_residence:
        :param user_age:
        :param condition:
        :param document_type:
        :param processing_time:
        :return:
        """
        # Example:
        # select * from ets where country='CHN' AND service_type='fastline' AND airport='ABC' \
        #     AND (attributes->>'validity'='1y' OR attributes->>'validity'='3M');
        #
        try:
            conn, cursor = self.init_db_connection()

            _filter = "country='%s'" % country
            if service_type:
                service_type_filter = "service_type='%s'" % service_type
                _filter = _filter + ' AND ' + service_type_filter
            if airport:
                airport_list = airport.split(',')
                airport_filter = ' OR '.join(
                    ["airport='%s'" % ap.strip() for ap in airport_list])
                airport_filter = '(%s)' % airport_filter
                _filter = _filter + ' AND ' + airport_filter
            if tasks:
                tasks_filter = "tasks='%s'" % tasks
                _filter = _filter + ' AND ' + tasks_filter
            if terminal:
                terminal_filter = "attributes->>'terminal'='%s'" % terminal
                _filter = _filter + ' AND ' + terminal_filter
            if region_of_residence:
                ror_filter = "attributes->>'region_of_residence'='%s'" % region_of_residence
                _filter = _filter + ' AND ' + ror_filter
            if user_age:
                ua_filter = "attributes->>'user_age'='%s'" % user_age
                _filter = _filter + ' AND ' + ua_filter
            if condition:
                condition_filter = "attributes->>'condition'='%s'" % condition
                _filter = _filter + ' AND ' + condition_filter
            if document_type:
                document_type_filter = "attributes->>'document_type'='%s'" % document_type
                _filter = _filter + ' AND ' + document_type_filter
            if processing_time:
                processing_time_list = processing_time.split(',')
                processing_time_filter = ' OR '.join(
                    ["attributes->>'processing_time'='%s'" % pt.strip() for pt in processing_time_list])
                processing_time_filter = '(%s)' % processing_time_filter
                _filter = _filter + ' AND ' + processing_time_filter
            if apply_for:
                _filter = _filter + ''' AND attributes->>'apply_for'='%s' ''' % apply_for

            if tag:
                tag_filter = "tag='%s'" % tag
                _filter = _filter + ' AND ' + tag_filter

            if validity:
                validity_filter = "attributes->>'validity'='%s'" % validity
                _filter = _filter + ' AND ' + validity_filter

            if purpose:
                purpose_filter = "attributes->>'purpose'='%s'" % purpose
                _filter = _filter + ' AND ' + purpose_filter

            _sql = "SELECT id from ets where %s ;" % _filter

            logger.info('SQL: %s', _sql)
            cursor.execute(_sql)
            result = cursor.fetchall()
            if result:
                result = [each[0] for each in result]
        except (Exception, psycopg2.Error) as error:
            logger.exception("Failed to query the ETS product records", error)
            raise
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")
        logger.info('ETS Product IDs are %s' % result)
        return result

    def query_vp_ids(self, country, issue_method=None, validity=None, ror=None, purpose=None, nationality=None, apply_for=None, tag=None, user_age=None):
        """
        :param country:
        :param issue_method:
        :param validity:
        :param ror:
        :param purpose:
        :param nationality:
        :return:
        """
        # Example:
        # select * from visa_product where country='CHN' AND issue_method='regular' \
        #     AND (attributes->>'validity'='1y' OR attributes->>'validity'='3M');
        #
        try:
            conn, cursor = self.init_db_connection()

            country_list = country.split(',')
            country_filter = ' OR '.join(
                ["country='%s'" % _c.strip() for _c in country_list])
            _filter = '(%s)' % country_filter

            if issue_method:
                issue_method_filter = "issue_method='%s'" % issue_method
                _filter = _filter + ' AND ' + issue_method_filter
            if validity:
                validity_list = validity.split(',')
                validity_filter = ' OR '.join(
                    ["attributes->>'validity'='%s'" % vf.strip() for vf in validity_list])
                validity_filter = '(%s)' % validity_filter
                _filter = _filter + ' AND ' + validity_filter
            if apply_for:
                _filter = _filter + ''' AND attributes->>'apply_for'='%s' ''' % apply_for
            if ror:
                ror_filter = "region_of_residence='%s'" % ror
                _filter = _filter + ' AND ' + ror_filter
            if purpose:
                purpose_filter = "purpose='%s'" % purpose
                _filter = _filter + ' AND ' + purpose_filter
            if nationality:
                nationality_filter = "nationality='%s'" % nationality
                _filter = _filter + ' AND ' + nationality_filter
            if tag:
                tag_filter = "tag='%s'" % tag
                _filter = _filter + ' AND ' + tag_filter
            if user_age:
                user_age_filter = "attributes->>'user_age'='%s'" % user_age
                _filter = _filter + ' AND ' + user_age_filter

            _sql = "SELECT id from visa_product where %s AND status='active';" % _filter

            logger.info('SQL: %s', _sql)
            cursor.execute(_sql)
            result = cursor.fetchall()
            if result:
                result = [each[0] for each in result]
        except (Exception, psycopg2.Error) as error:
            logger.exception("Failed to query the visa product records", error)
            raise
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")
        logger.info('Visa Product IDs are %s' % result)
        return result

    def init_db_connection(self):
        """
        :return:
        """
        try:
            conn = psycopg2.connect(user=self.user,
                                    password=self.password,
                                    host=self.dbhost,
                                    port=self.port,
                                    database=self.database
                                    )

            logger.info("connected")
            cursor = conn.cursor()
        except (Exception, psycopg2.Error) as error:
            logger.exception("Failed to connect to database: %s", error)
            raise

        return conn, cursor

    def parse_xlsx_file(self):
        """
        :return:
        """
       # Authenticate using your Google Sheets API credentials
        # Replace with your JSON file
        gc = gspread.service_account(
            filename='credentials.json')

        # Open the spreadsheet using its key
        worksheets = gc.open_by_key(GOOGLE_SHEET_ID)

        # Get all the data from the worksheet
        worksheet_data = worksheets.worksheet(sheet_name).get_all_values()

        header = worksheet_data[0]
        values = worksheet_data[1:]

        master_pod_values = worksheets.worksheet(
            'master_pods').get_all_values()[1:]

        master_pod_values_pair = {}
        for row in master_pod_values:
            master_pod_values_pair[row[0]] = {
                'id': row[0],
                'pod_type': row[1],
                'category': row[2],
                'sub_category': row[3],
                'title': row[4],
                'name': row[5],
                'body': {},
            }

            master_pod_values_pair[row[0]]['body'] = {
                **json.loads(row[6]),
                **master_pod_values_pair[row[0]],
            }

        query_pod_values = worksheets.worksheet(
            'query_pods').get_all_values()

        ad_pods = self.fetch_pods(master_pod_values_pair, values)

        query_pods = self.update_query_pods(
            query_pod_values, master_pod_values_pair)

        pods_amendment = self.fetch_pods_amendment(
            master_pod_values_pair, header, values)

        # Remove the reference in ets_pods
        _id_body_dict = dict([(each['id'], each['body']) for each in ad_pods])
        _clean_id_body_dict = self.remove_pod_reference(
            master_pod_values_pair, _id_body_dict)

        clean_pods = list()

        for each in ad_pods:
            each['body'] = _clean_id_body_dict.get(each['id'])
            clean_pods.append(each)

        return clean_pods, pods_amendment

    def fetch_pods(self, master_pod_values_pair, values):
        """
        :param values:
        :return:
        """
        # header:
        # excel: 'id', 'service', 'category', 'sub_category', 'title', 'name', 'tags', 'order', 'body'
        # db:    'id', 'service', 'category', 'sub_category', 'title', 'name', 'tags' 'order', 'body'
        start = 1
        pods_list = list()
        ids = []

        for val in values[start:]:
            try:
                pod_in_master = master_pod_values_pair.get(val[0])
                if not pod_in_master:
                    raise Exception(
                        'Pod ID ' + val[0] + 'not found in master_pods sheet')

                new_body = pod_in_master['body']
                new_body['order'] = int(val[1]) if val[1] else -1

                each_pod = {
                    'id':           new_body['id'],
                    'service':      sheet_name,
                    'pod_type':     new_body['pod_type'],
                    'category':     new_body['category'],
                    'sub_category': new_body['sub_category'],
                    'title':        new_body['title'],
                    'name':         new_body['name'],
                    'tags':         '',
                    'order':        new_body['order'],
                    'body':         new_body
                }
                if each_pod['id'] in ids:
                    print(f"Duplicate pod ID: {each_pod['id']}")
                ids.append(each_pod['id'])
                pods_list.append(each_pod)
            except Exception as _:
                logger.info('Failed to parse: %s', pod_in_master[:9])
                raise

        # Make sure the ids are unique
        ids = sorted(list(ids))
        ids_set = sorted(list(set(ids)))
        if ids != ids_set:
            logger.info('Some Pod IDs are missing or duplicated')
            diff1 = set(ids) - set(ids_set)
            diff2 = set(ids_set) - set(ids)
            logger.info('%s', set(ids) - set(ids_set))
            logger.info('%s', set(ids_set) - set(ids))
            raise Exception('Some Pod IDs are missing or duplicated')

        return pods_list

    def update_query_pods(self, query_pod_values, master_pod_values_pair):
        """
        :param values:
        :return:
        """
        # query_pod_values:
        # service	pod_id	body	order
        # passport	service_core_info_country	T	1																						        start = 1
        try:
            conn, cursor = self.init_db_connection()

            cursor.execute("delete from ets_query_pods")
            conn.commit()
            for i, pod in enumerate(query_pod_values):
                if i == 0:
                    continue

                pod_in_master = master_pod_values_pair.get(pod[1])
                if not pod_in_master:
                    raise Exception(
                        'Pod ID ' + pod[1] + ' not found in master_pods sheet')

                args = {
                    'pod_id': pod[1],
                    'service': pod[0],
                    'body': json.dumps(pod_in_master['body']),
                    'order': pod[3],
                }

                if pod[2] != 'T':
                    args['body'] = json.dumps({
                        **pod_in_master['body'], **json.loads(pod[2])}
                    )

                _sql = \
                    """INSERT INTO ets_query_pods
                    (pod_id,service,body,"order")
                    VALUES 
                    (%s, %s, %s, %s)
                    ON CONFLICT(pod_id,service) DO UPDATE 
                       SET body=EXCLUDED.body, 
                           "order"=EXCLUDED.order;
                    """
                logger.info('SQL: %s', _sql)
                cursor.execute(_sql, (
                    args['pod_id'],
                    args['service'],
                    args['body'],
                    args['order'],
                ))
                conn.commit()

        except (Exception, psycopg2.Error) as error:
            logger.exception("Failed to insert record into table", error)
            raise
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def fetch_pods_amendment(self, master_pod_values_pair, header, values):
        """
        :param header:
        :param values:
        :return:
        """
        ACTIVE_RANGE = range(PRODUCT_COLUMN_INDEX, len(header))

        start = 1

        # key: (name, query)
        pods_amendment_per_group = dict()

        for product_column in ACTIVE_RANGE:
            product_dict = dict()

            product_dict['schema_pods'] = list()
            product_dict['pods_amendment'] = dict()

            for val in values[start:]:
                try:
                    if val[product_column] != 'FALSE' and val[product_column] != False:
                        # Note: add to both
                        product_dict['schema_pods'].append(val[0])

                        # print( val[product_column])
                        if not val[product_column].strip() == 'T':
                            print(val[0])
                            print(val[product_column])
                            product_dict['pods_amendment'][val[0]] = json.loads(
                                val[product_column].strip())

                except Exception as exc:
                    logger.error((exc))
                    logger.exception(
                        'Failed to parse row: %s; column: %s', val[0], product_column)
                    raise

            pods_amendment_per_group[(
                header[product_column], values[0][product_column])] = product_dict

        return pods_amendment_per_group


if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        description='Helper to insert data to Postgresql')
    parser.add_argument('--log-level', default='INFO', help='Logging level')

    args = parser.parse_args()

    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.getLevelName(args.log_level.upper())
    )

    rc = 0
    try:
        logger.info('WARNING: Running on the cloud database')
        dbhost = db_cloud_profile.get('dbhost')
        database = db_cloud_profile.get('database')
        user = db_cloud_profile.get('user')
        password = db_cloud_profile.get('password')
        port = db_cloud_profile.get('port')

        loader = Loader(dbhost, database, user, password, port)
        loader.run()

    except Exception as exc:
        logger.exception('Unhandled exception: %s', exc)
        sys.exit(1)

    sys.exit(rc)
