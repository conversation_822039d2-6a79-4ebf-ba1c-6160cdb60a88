#!/usr/bin/env python
"""
The source xlsx file:
https://docs.google.com/spreadsheets/d/13aFJ958uPnuVChIzP-eotpwmQN2xqcM-OUWfVrNZI4g/edit#gid=1855128023

"""

import argparse
import logging
import os
import sys

import pandas as pd
import psycopg2

ENV = 'stag'
CURRENT_RELEASE = '3.1.5'

# Database config
DB_HOST = os.getenv('DB_HOST') or 'localhost'
DB_PASSWORD = os.getenv('DB_PASSWORD') or ''
DATABASE = 'ariadirect_stag' if ENV == 'stag' else 'ariadirect_prod'

# Google Excel config
XLSX_NAME = '{} email templates.xlsx'.format(CURRENT_RELEASE)

db_cloud_profile = {
    'dbhost':   DB_HOST,
    'database': DATABASE,
    'user':     'aria',
    'password': DB_PASSWORD,
}

logger = logging.getLogger(__name__)


class Loader(object):

    def __init__(self, dbhost, database, user, password, port=5432):
        """
        :param dbhost:
        :param database:
        :param user:
        :param password:
        :param port:
        """
        self.dbhost = dbhost
        self.database = database
        self.user = user
        self.password = password
        self.port = port
        self.xlsx_name = XLSX_NAME

    def run(self):
        """
        :return:
        """

        self.update_email_templates()

    def update_email_templates(self, sheet_name="email_templates", table="email_template"):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            for col in data:

                email_template = {
                    'name':            col.get('Name') or '',
                    'from':            col.get('From') or '',
                    'email_type':      col.get('Email_Type') or 'system',
                    'subject_en':      col.get('Subject_EN') or '',
                    'subject_vi':      col.get('Subject_VI') or '',
                    'subject_zh':      col.get('Subject_ZH') or '',
                    'save_as_notification': col.get('Save_As_Notification') == 'T',
                    'body_en':         col.get('Body_EN') or '',
                    'body_vi':         col.get('Body_VI') or '',
                    'body_zh':         col.get('Body_ZH') or '',
                    'active':         col.get('Active')  == 'T',
                }

                # Insert email template
                logger.info('insert to %s table', table)
                for lang in ['EN', 'VI', 'ZH']:
                    logger.info('Language: %s', lang)
                    _sql = \
                        """INSERT INTO {0}
                        (name, "from", email_type, title, htmlbody, language, textbody, parameters, save_as_notification, active)
                        VALUES 
                        (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT(name, language) DO UPDATE
                        SET "from" = EXCLUDED.from,
                            title = EXCLUDED.title,
                            email_type = EXCLUDED.email_type,
                            htmlbody = EXCLUDED.htmlbody,
                            save_as_notification = EXCLUDED.save_as_notification,
                            active = EXCLUDED.active,
                            updated_at = current_timestamp;
                        """.format(table)
                    logger.info('SQL: %s', _sql)

                    cursor.execute(_sql, (
                        email_template['name'],
                        email_template['from'],
                        email_template['email_type'],
                        email_template['subject_'+lang.lower()],
                        email_template['body_'+lang.lower()],
                        lang,
                        '',
                        '{}',
                        email_template['save_as_notification'],
                        email_template['active'],
                    )
                    )
                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def init_db_connection(self):
        """
        :return:
        """
        try:
            conn = psycopg2.connect(user=self.user,
                                    password=self.password,
                                    host=self.dbhost,
                                    port=self.port,
                                    database=self.database
                                    )

            logger.info("connected")
            cursor = conn.cursor()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to connect to database: %s", error)

        return conn, cursor

    def parse_xlsx_file(self, sheet_name, xlsx_name=None):
        """
        :param xlsx_name:
        :param sheet_name:
        :return:
        """
        if xlsx_name is None:
            xlsx_name = self.xlsx_name

        dirname, _ = os.path.split(os.path.abspath(__file__))
        dfs = pd.read_excel(os.path.join(
            dirname, xlsx_name), sheet_name=sheet_name)
        dfs = dfs.replace({pd.np.nan: None})

        header = dfs.columns.tolist()
        values = dfs.values.tolist()

        new_values = []
        for val in values:
            if all([v is None for v in val]):
                continue
            val = [v.strip() if type == str else v for v in val]
            new_values.append(val)

        return [dict(zip(header, val)) for val in new_values]


if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        description='Helper to insert data to Postgresql')
    parser.add_argument('--log-level', default='INFO', help='Logging level')

    args = parser.parse_args()

    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.getLevelName(args.log_level.upper())
    )

    rc = 0
    try:
        logger.info('WARNING: Running on the cloud database')
        dbhost = db_cloud_profile.get('dbhost')
        database = db_cloud_profile.get('database')
        user = db_cloud_profile.get('user')
        password = db_cloud_profile.get('password')

        loader = Loader(dbhost, database, user, password)

        loader.run()

    except Exception as exc:
        logger.exception('Unhandled exception: %s', exc)
        sys.exit(1)

    sys.exit(rc)
