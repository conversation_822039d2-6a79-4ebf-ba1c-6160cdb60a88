import argparse
import logging
import os
import pandas as pd
import psycopg2
import sys
import json
import os
import gspread
import boto3

import psycopg2.extras

ENV = os.getenv('ENV') or 'stag'

# https://docs.google.com/spreadsheets/d/1Z6dRDPGwFhXk8dH8cUFckigHYl1e0UbU4GON__pKi8c/edit#gid=*********
GOOGLE_SHEET_ID = '1Z6dRDPGwFhXk8dH8cUFckigHYl1e0UbU4GON__pKi8c'
LOCALIZATION_SHEET_NAME = 'localization'
APP_VERSIONS_SHEET_NAME = 'app_versions'


AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
S3_BUCKET_NAME = 'ad-app-version'

logger = logging.getLogger(__name__)


class Loader(object):
    def parse_xlsx_file(self, sheet_name):
        """
        :param sheet_name:
        :return:
        """
        gc = gspread.service_account(filename='credentials.json')

        # Open the spreadsheet using its key
        worksheets = gc.open_by_key(GOOGLE_SHEET_ID)

        # Get all the data from the worksheet
        worksheet_data = worksheets.worksheet(sheet_name).get_all_values()

        header = worksheet_data[0]
        values = worksheet_data[1:]

        new_values = []
        for val in values:
            if all([v is None for v in val]):
                continue
            val = [v.strip() if type == str else v for v in val]
            new_values.append(val)

        return [dict(zip(header, val)) for val in new_values]

    def create_json_file(self, localization_data, app_versions_data):
        json_data_en = {item['key']: item['en'] for item in localization_data}
        with open('en.json', 'w', encoding='utf-8') as file_en:
            json.dump(json_data_en, file_en, indent=4, ensure_ascii=False)

        json_data_vi = {item['key']: item['vi'] for item in localization_data}
        with open('vi.json', 'w', encoding='utf-8') as file_vi:
            json.dump(json_data_vi, file_vi, indent=4, ensure_ascii=False)

        with open('app_versions.json', 'w', encoding='utf-8') as file_vi:
            json.dump(app_versions_data, file_vi, indent=4, ensure_ascii=False)

    def upload_to_s3(self, file_path, bucket_name, key_name):
        s3 = boto3.client('s3')
        s3.upload_file(file_path, bucket_name, key_name,
                       ExtraArgs={'ACL': 'public-read', 'ContentType': 'application/json', 'ContentDisposition': 'inline'})

    def run(self):
        localization_data = self.parse_xlsx_file(LOCALIZATION_SHEET_NAME)
        app_versions_data = self.parse_xlsx_file(APP_VERSIONS_SHEET_NAME)
        self.create_json_file(localization_data, app_versions_data)
        self.upload_to_s3('en.json', S3_BUCKET_NAME,
                          "app-languages/localization_en.json")

        self.upload_to_s3('vi.json', S3_BUCKET_NAME,
                          "app-languages/localization_vi.json")

        self.upload_to_s3('app_versions.json', S3_BUCKET_NAME,
                          "app-versions/app-versions.json")
        logger.info('DONE')


if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        description='Helper to insert data to Postgresql')
    parser.add_argument('--log-level', default='INFO', help='Logging level')

    args = parser.parse_args()

    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.getLevelName(args.log_level.upper())
    )

    rc = 0
    try:
        loader = Loader()
        loader.run()
    except Exception as exc:
        logger.exception('Unhandled exception: %s', exc)
        sys.exit(1)

    sys.exit(rc)
