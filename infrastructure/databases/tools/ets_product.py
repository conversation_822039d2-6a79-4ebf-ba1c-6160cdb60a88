#!/usr/bin/env python
import argparse
import logging
import os
import pandas as pd
import psycopg2
import sys
import json
import gspread

import psycopg2.extras

ENV = os.getenv('ENV') or 'stag'

# https://docs.google.com/spreadsheets/d/1bqW7FbPvJZqp3zh-eZ4tX30cUsCeJPkW8ORlYsHb1uc/edit#gid=473968508
GOOGLE_SHEET_ID = '1bqW7FbPvJZqp3zh-eZ4tX30cUsCeJPkW8ORlYsHb1uc'

# Database config
DB_HOST = os.getenv('DB_HOST') or 'localhost'
DB_PASSWORD = os.getenv('DB_PASSWORD') or 'ariadirect2020'
DATABASE = 'ariadirect_stag' if ENV == 'stag' else 'ariadirect_prod'


db_cloud_profile = {
    'dbhost':   DB_HOST,
    'database': DATABASE,
    'user':     'aria',
    'password': DB_PASSWORD,
    'port':     5432,
}

logger = logging.getLogger(__name__)


class Loader(object):

    def __init__(self, dbhost, database, user, password, port=5432):
        """
        :param dbhost:
        :param database:
        :param user:
        :param password:
        :param port:
        """
        self.dbhost = dbhost
        self.database = database
        self.user = user
        self.password = password
        self.port = port

    def run(self):
        """
        :return:
        """
        self.check_ets()

        self.check_ets_pricing()

        self.update_ets()

        self.update_ets_pricing()

        self.update_promotion()

        self.update_special_working_times()

    # Note: schema_pods, pod_amendment, schema will be input from the visa_pods
    def update_ets(self, sheet_name='ets', table='ets'):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            tt = len(data)
            for i, col in enumerate(data):

                logger.info('Insert to %s table: %d / %d', table, i + 1, tt)
                attributes = json.loads(col['attributes'])
                if col['shipping_labels'] != '':
                    attributes["shipping_labels"] = json.loads(
                        col['shipping_labels'])

                sql_args = {
                    'id':            col.get('id'),
                    'name':          col.get('name'),
                    'service_type':  col.get('service_type'),
                    'country':       col.get('country'),
                    'airport':       col.get('airport'),
                    'tasks':         col.get('tasks'),
                    'attributes':    json.dumps(attributes),
                    'currency':      col.get('currency'),
                    'tag':           col.get('tag'),
                    'status':        col.get('status') or 'active',
                    'working_times':        col.get('working_times') or 'DEFAULT',
                }

                _sql = \
                    """INSERT INTO {0} 
                    (id, name, service_type, country, airport, tasks, attributes, currency, tag, status, working_times)
                    VALUES 
                    (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT(id) DO UPDATE
                      SET name=EXCLUDED.name,
                          service_type=EXCLUDED.service_type,
                          country=EXCLUDED.country,
                          airport=EXCLUDED.airport,
                          tasks=EXCLUDED.tasks,
                          attributes=EXCLUDED.attributes, 
                          currency=EXCLUDED.currency,
                          tag=EXCLUDED.tag,
                          status=EXCLUDED.status,
                          working_times=EXCLUDED.working_times;
                    """.format(table)

                logger.info('SQL: %s', _sql)

                cursor.execute(_sql, (
                    sql_args['id'],
                    sql_args['name'],
                    sql_args['service_type'],
                    sql_args['country'],
                    sql_args['airport'],
                    sql_args['tasks'],
                    sql_args['attributes'],
                    sql_args['currency'],
                    sql_args['tag'],
                    sql_args['status'],
                    sql_args['working_times'],
                ))
                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def update_ets_pricing(self, sheet_name='pricing', table='ets_price'):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            tt = len(data)
            for i, col in enumerate(data):
                logger.info('Insert to %s table: %d / %d', table, i + 1, tt)

                _sql = """INSERT INTO ets_price
                    (ets_id, provider_id, currency, price, additional_fee, shipments, price_modified_rules, additional_services, discount)
                    VALUES 
                    (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT(ets_id) DO UPDATE
                      SET provider_id=EXCLUDED.provider_id,
                          currency=EXCLUDED.currency,
                          price=EXCLUDED.price,
                          additional_fee=EXCLUDED.additional_fee,
                          shipments=EXCLUDED.shipments,
                          price_modified_rules=EXCLUDED.price_modified_rules,
                          additional_services=EXCLUDED.additional_services,
                          discount=EXCLUDED.discount;
                    """

                logger.info('SQL: %s', _sql)
                cursor.execute(_sql, (
                    col['ets_id'],
                    col['provider_id'],
                    col['currency'],
                    int(col['price']) if col['price'] != '' else 0,
                    '{}' if col['additional_fee'] == '' else col['additional_fee'],
                    '{}' if col['shipments'] == '' else col['shipments'],
                    '[]' if col['price_modified_rules'] == '' else col['price_modified_rules'],
                    '[]' if col['additional_services'] == '' else col['additional_services'],
                    int(col['discount']) if col['discount'] != '' else 0,
                ))

                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def update_promotion(self, sheet_name='promotion', table='promotion_codes'):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            tt = len(data)
            for i, col in enumerate(data):
                logger.info('Insert to %s table: %d / %d', table, i + 1, tt)

                _sql = \
                    """INSERT INTO {0} 
                    (promotion_code, promotion_type, discount_rules, currency, quantity, remain, active, start_date, end_date)
                    VALUES 
                    (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT(promotion_code, promotion_type) DO UPDATE
                      SET discount_rules = EXCLUDED.discount_rules,
                          currency = EXCLUDED.currency,
                          quantity = EXCLUDED.quantity,
                          remain = EXCLUDED.remain,
                          active = EXCLUDED.active,
                          start_date = EXCLUDED.start_date,
                          end_date = EXCLUDED.end_date;
                    """.format(table)

                logger.info('SQL: %s', _sql)
                cursor.execute(_sql, (
                    col['promotion_code'],
                    col['promotion_type'],
                    col['discount_rules'],
                    col['currency'],
                    int(col['quantity']),
                    9999 if col['quantity'] == '' else int(col['quantity']),
                    col['active'],
                    col['start_date'],
                    col['end_date'],
                ))
                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def update_special_working_times(self, sheet_name='special_working_times', table='special_working_times'):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            tt = len(data)
            for i, col in enumerate(data):
                logger.info('Insert to %s table: %d / %d', table, i + 1, tt)

                _sql = \
                    """INSERT INTO {0} 
                    (name, location, working_hours, special_off_days, query_time_setting, buffer)
                    VALUES 
                    (%s, %s, %s, %s, %s, %s)
                    ON CONFLICT(name) DO UPDATE
                      SET location = EXCLUDED.location,
                          working_hours = EXCLUDED.working_hours,
                          special_off_days = EXCLUDED.special_off_days,
                          query_time_setting = EXCLUDED.query_time_setting,
                          buffer = EXCLUDED.buffer;
                    """.format(table)

                logger.info('SQL: %s', _sql)
                cursor.execute(_sql, (
                    col['name'],
                    col['location'],
                    col['working_hours'],
                    col['special_off_days'],
                    col['query_time_setting'],
                    col['buffer'],
                ))
                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")


    def check_ets(self, sheet_name='ets', table='ets'):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        column_to_be_check = [
            "service_type",
            "country",
            "airport",
            "tasks",
            "attributes",
            "currency",
            "tag",
            "status"
        ]

        excel_data = self.parse_xlsx_file(sheet_name)  # a list
        db_data = self._query_db_table(table)  # a list

        excel_data_dict = dict([(int(each['id']), each)
                               for each in excel_data])
        db_data_dict = dict([(int(each['id']), each) for each in db_data])
        # 1. Check ets ID
        excel_ids = sorted(list(excel_data_dict.keys()))
        db_ids = sorted(list(db_data_dict.keys()))

        id_not_in_db = [each for each in excel_ids if each not in db_ids]
        id_not_in_excel = [each for each in db_ids if each not in excel_ids]

        print('\n ID not in DB:\n {0} \n ID not in Excel:\n {1}'.format(
            id_not_in_db, id_not_in_excel))

        # 2. Check columns
        column_msg = []
        for etsid, body in db_data_dict.items():

            for col in column_to_be_check:
                db_val, excel_val = body[col], excel_data_dict[etsid].get(col)

                if excel_val:
                    try:
                        if col in ['attributes',]:
                            excel_val = json.loads(excel_val)
                    except Exception:
                        logger.error(
                            'ETS Product: %s; Value in excel: %s', etsid, excel_val)

                if db_val != excel_val:
                    msg = 'ID:{0}; {1}; in DB: {2}; in Excel: {3}'.format(
                        etsid, col, db_val, excel_val)
                    column_msg.append(msg)
        if column_msg:
            print('\n'.join(column_msg))
        else:
            print('NO difference!')

    def check_ets_pricing(self, sheet_name='pricing', table='ets_price'):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        column_to_be_check = [
            "ets_id",
            "provider_id",
            "currency",
            "price",
            "additional_fee",
            "shipments",
            "discount"
        ]

        excel_data = self.parse_xlsx_file(sheet_name)  # a list
        db_data = self._query_db_table(table)  # a list

        excel_data_dict = dict([(int(each['ets_id']), each)
                               for each in excel_data if each['ets_id']])
        db_data_dict = dict([(int(each['ets_id']), each) for each in db_data])

        # 1. Check no duplicate ETS product ID
        excel_ids = sorted(list(excel_data_dict.keys()))
        db_ids = sorted(list(db_data_dict.keys()))

        if len(excel_ids) != len(set(excel_ids)):
            print('Duplicate ETS product id in Excel')
        else:
            print('ETS id in Excel: Checked')

        if len(db_ids) != len(set(db_ids)):
            print('Duplicate ETS product id in DB')
        else:
            print('ETS id in DB: Checked')

        # 2. Check columns
        column_msg = []
        for etsid, body in db_data_dict.items():

            for col in column_to_be_check:
                if excel_data_dict is None or excel_data_dict[etsid] is None:
                    print('ETS id: {0} is None'.format(etsid))
                db_val, excel_val = body[col], excel_data_dict[etsid].get(col)

                if excel_val:
                    if col in ['additional_fee', 'shipments']:
                        excel_val = json.loads(excel_val)

                if db_val != excel_val:
                    if col == 'shipments':
                        if excel_val in [None, {}] and db_val in [None, {}]:
                            continue

                    msg = 'ID:{0}; {1}; in DB: {2}; in Excel: {3}'.format(
                        etsid, col, db_val, excel_val)
                    column_msg.append(msg)

        if column_msg:
            print('\n'.join(column_msg))
        else:
            print('NO difference!')

    def _query_db_table(self, table):
        """
        :param table:
        :return:
        """
        try:
            conn = psycopg2.connect(
                user=self.user,
                password=self.password,
                host=self.dbhost,
                port=self.port,
                database=self.database
            )

            logger.info("connected")
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            _sql = "SELECT * from %s;" % table
            logger.info('SQL: %s', _sql)
            cursor.execute(_sql)
            result = [dict(each) for each in cursor.fetchall()]
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to query the ETS product records", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")
        return result

    def init_db_connection(self):
        """
        :return:
        """
        try:
            conn = psycopg2.connect(user=self.user,
                                    password=self.password,
                                    host=self.dbhost,
                                    port=self.port,
                                    database=self.database
                                    )

            logger.info("connected")
            cursor = conn.cursor()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to connect to database: %s", error)

        return conn, cursor

    def parse_xlsx_file(self, sheet_name):
        """
        :param sheet_name:
        :return:
        """
        gc = gspread.service_account(filename='credentials.json')

        # Open the spreadsheet using its key
        worksheets = gc.open_by_key(GOOGLE_SHEET_ID)

        # Get all the data from the worksheet
        worksheet_data = worksheets.worksheet(sheet_name).get_all_values()

        header = worksheet_data[0]
        values = worksheet_data[1:]

        new_values = []
        for val in values:
            if all([v is None for v in val]):
                continue
            val = [v.strip() if type == str else v for v in val]
            new_values.append(val)

        return [dict(zip(header, val)) for val in new_values]


if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        description='Helper to insert data to Postgresql')
    parser.add_argument('--log-level', default='INFO', help='Logging level')

    args = parser.parse_args()

    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.getLevelName(args.log_level.upper())
    )

    rc = 0
    try:
        logger.info('WARNING: Running on the cloud database')
        dbhost = db_cloud_profile.get('dbhost')
        database = db_cloud_profile.get('database')
        user = db_cloud_profile.get('user')
        password = db_cloud_profile.get('password')
        port = db_cloud_profile.get('port')

        loader = Loader(dbhost, database, user, password, port)

        loader.run()

    except Exception as exc:
        logger.exception('Unhandled exception: %s', exc)
        sys.exit(1)

    sys.exit(rc)
