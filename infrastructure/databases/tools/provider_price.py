#!/usr/bin/env python
"""
The source xlsx file:
https://docs.google.com/spreadsheets/d/1vlRxfNhrot43LBvxbRx_xCXRg5ukbAGw/edit#gid=90298632
file_name: <version> provider_price
"""

import argparse
import logging
import os
import pandas as pd
import psycopg2
import sys

ENV = 'stag'
CURRENT_RELEASE = '3.1.5'

# Database config
DB_HOST = os.getenv('DB_HOST') or 'localhost'
DB_PASSWORD = os.getenv('DB_PASSWORD') or ''
DATABASE = 'ariadirect_stag' if ENV == 'stag' else 'ariadirect_prod'

# Google Excel config
XLSX_NAME = '{} provider_price.xlsx'.format(CURRENT_RELEASE)

db_cloud_profile = {
    'dbhost':   DB_HOST,
    'database': DATABASE,
    'user':     'aria',
    'password': DB_PASSWORD,
}

logger = logging.getLogger(__name__)


class Loader(object):

    def __init__(self, dbhost, database, user, password, port=5432):
        """
        :param dbhost:
        :param database:
        :param user:
        :param password:
        :param port:
        """
        self.dbhost = dbhost
        self.database = database
        self.user = user
        self.password = password
        self.port = port
        self.xlsx_name = XLSX_NAME

    def run(self):
        """
        :return:
        """
        logger.info('Inserting VISA provider price...')
        self.update_provider_price(sheet_name="visa_price")

        logger.info('Inserting ETS provider price...')
        self.update_provider_price(sheet_name="ets_price")


    def update_provider_price(self, sheet_name="visa_price", table="provider_price"):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            for col in data:
                logger.info('insert to %s table', table)

                _sql = \
                    """INSERT INTO {0}
                    (product_id, provider_id, service, status, currency, price, additional_fee)
                    VALUES 
                    ({1}, {2}, {3}, {4}, {5}, {6}, {7})
                    ON CONFLICT(product_id, provider_id) DO UPDATE
                      SET service={3},
                          status={4},
                          currency={5},
                          price={6},
                          additional_fee={7};
                    """.format(table,
                               col['product_id'],
                               "'" + col['provider_id'] + "'" if col.get('provider_id') else 'null',
                               "'" + col['service'] + "'",
                               "'" + col['status'] + "'" if col.get('status') else "'active'",
                               "'" + col['currency'] + "'" if col.get('currency') else 'null',
                               float(col['price']) if col.get('price') else 'null',
                               "'" + col['additional_fee'] + "'" if col.get('additional_fee') else 'null'
                               )
                logger.info('SQL: %s', _sql)
                cursor.execute(_sql)

                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
            raise
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def init_db_connection(self):
        """
        :return:
        """
        try:
            conn = psycopg2.connect(user=self.user,
                                    password=self.password,
                                    host=self.dbhost,
                                    port=self.port,
                                    database=self.database
                                    )

            logger.info("connected")
            cursor = conn.cursor()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to connect to database: %s", error)
            raise

        return conn, cursor

    def parse_xlsx_file(self, sheet_name, xlsx_name=None):
        """
        :param xlsx_name:
        :param sheet_name:
        :return:
        """
        if xlsx_name is None:
            xlsx_name = self.xlsx_name

        dirname, _ = os.path.split(os.path.abspath(__file__))
        dfs = pd.read_excel(os.path.join(dirname, xlsx_name), sheet_name=sheet_name)
        dfs = dfs.replace({pd.np.nan: None})

        header = dfs.columns.tolist()
        values = dfs.values.tolist()

        new_values = []
        for val in values:
            if all([v is None for v in val]):
                continue
            val = [v.strip() if type == str else v for v in val]
            new_values.append(val)

        return [dict(zip(header, val)) for val in new_values]


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Helper to insert data to Postgresql')
    parser.add_argument('--log-level', default='INFO', help='Logging level')

    args = parser.parse_args()

    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.getLevelName(args.log_level.upper())
    )

    rc = 0
    try:
        logger.info('WARNING: Running on the cloud database')
        dbhost = db_cloud_profile.get('dbhost')
        database = db_cloud_profile.get('database')
        user = db_cloud_profile.get('user')
        password = db_cloud_profile.get('password')

        loader = Loader(dbhost, database, user, password)

        loader.run()

    except Exception as exc:
        logger.exception('Unhandled exception: %s', exc)
        sys.exit(1)

    sys.exit(rc)
