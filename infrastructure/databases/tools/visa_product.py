#!/usr/bin/env python
"""
The source xlsx file:
https://docs.google.com/spreadsheets/d/1fkFQYka6fxUL6FyOIFId0Sc_zjMQ72a7szf8_qXZ5fQ/edit#gid=1811532211

"""

import argparse
import logging
import os
import pandas as pd
import psycopg2
import sys
import json

import psycopg2.extras

ENV = 'stag'
CURRENT_RELEASE = '3.1.5'

# Database config
DB_HOST = os.getenv('DB_HOST') or 'localhost'
DB_PASSWORD = os.getenv('DB_PASSWORD') or ''
DATABASE = 'ariadirect_stag' if ENV == 'stag' else 'ariadirect_prod'

# Google Excel config
XLSX_NAME = '{} visa.xlsx'.format(CURRENT_RELEASE)

db_cloud_profile = {
    'dbhost':   DB_HOST,
    'database': DATABASE,
    'user':     'aria',
    'password': DB_PASSWORD,
}

logger = logging.getLogger(__name__)


class Loader(object):

    def __init__(self, dbhost, database, user, password, port=5432):
        """
        :param dbhost:
        :param database:
        :param user:
        :param password:
        :param port:
        """
        self.dbhost = dbhost
        self.database = database
        self.user = user
        self.password = password
        self.port = port
        self.xlsx_name = XLSX_NAME

    def run(self):
        """
        :return:
        """
        self.check_visa_product()

        self.check_visa_product_pricing()

        self.update_visa_product()

        self.update_visa_product_pricing()

    # Note: schema_pods, pod_amendment, schema will be input from the visa_pods
    def update_visa_product(self, sheet_name='visa_product', table='visa_product'):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            tt = len(data)
            for i, col in enumerate(data):

                logger.info('Insert to %s table: %d / %d', table, i+1, tt)

                attributes = json.loads(col['attributes'])
                if col['shipping_labels'] is not None:
                    attributes["shipping_labels"] =json.loads(col['shipping_labels'])

                data = {
                    'id':            col.get('id') or None,
                    'country':            col.get('country') or None,
                    'purpose':            col.get('purpose') or None,
                    'category':           col.get('category') or None,
                    'issue_method':       col.get('issue_method') or None,
                    'attributes':         json.dumps(attributes),
                    'form':               col.get('form') or None,
                    'constraint':         col.get('constraint') or None,
                    'submit_method':      col.get('submit_method') or None,
                    'receive_method':     col.get('receive_method') or None,
                    'nationality':        col.get('nationality') or None,
                    'region_of_residence':col.get('region_of_residence') or None,
                    'process_type':       col.get('process_type') or None,
                    'currency':           col.get('currency') or None,
                    'additional_questions':    col.get('additional_questions') or None,
                    'additional_requirements': col.get('additional_requirements') or None,
                    'tag':                     col.get('tag') or None,
                    'status':                  col.get('status') or 'active',
                }


                _sql = \
                    """INSERT INTO {0} 
                    (id, country, purpose, category, issue_method, attributes, form, "constraint",
                     submit_method, receive_method, nationality, region_of_residence, process_type,
                     currency, additional_questions, additional_requirements, tag, status)
                    VALUES 
                    (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT(id) DO UPDATE
                      SET country=EXCLUDED.country,
                          purpose=EXCLUDED.purpose,
                          category=EXCLUDED.category,
                          issue_method=EXCLUDED.issue_method,
                          attributes=EXCLUDED.attributes,
                          form=EXCLUDED.form,
                          "constraint"=EXCLUDED.constraint,
                          submit_method=EXCLUDED.submit_method,
                          receive_method=EXCLUDED.receive_method,
                          nationality=EXCLUDED.nationality,
                          region_of_residence=EXCLUDED.region_of_residence,
                          process_type=EXCLUDED.process_type,
                          currency=EXCLUDED.currency,
                          additional_questions=EXCLUDED.additional_questions,
                          additional_requirements=EXCLUDED.additional_requirements,
                          tag=EXCLUDED.tag,
                          status=EXCLUDED.status;
                    """.format(table)

                logger.info('SQL: %s', _sql)

                cursor.execute(_sql, (
                    data['id'],
                    data['country'],
                    data['purpose'],
                    data['category'],
                    data['issue_method'],
                    data['attributes'],
                    data['form'],
                    data['constraint'],
                    data['submit_method'],
                    data['receive_method'],
                    data['nationality'],
                    data['region_of_residence'],
                    data['process_type'],
                    data['currency'],
                    data['additional_questions'],
                    data['additional_requirements'],
                    data['tag'],
                    data['status'],

                ))
                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def update_visa_product_pricing(self, sheet_name='pricing', table='visa_customer_price'):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        data = self.parse_xlsx_file(sheet_name)

        try:
            conn, cursor = self.init_db_connection()

            tt = len(data)
            for i, col in enumerate(data):

                logger.info('Insert to %s table: %d / %d', table, i + 1, tt)

                _sql = \
                    """INSERT INTO {0} 
                    (visa_product_id, organization_id, consulate_id, currency, price, additional_fee, shipments, discount)
                    VALUES 
                    ({1}, {2}, {3}, {4}, {5}, {6}, {7}, {8})
                    ON CONFLICT(visa_product_id) DO UPDATE
                      SET organization_id={2},
                          consulate_id={3}, 
                          currency={4}, 
                          price={5},
                          additional_fee={6}, 
                          shipments={7},
                          discount={8};
                    """.format(table,
                               col['visa_product_id'],
                               "'" + col['organization_id'] + "'" if col.get('organization_id') else 'null',
                               "'" + col['consulate_id'] + "'" if col.get('consulate_id') else 'null',
                               "'" + col['currency'] + "'" if col.get('currency') else 'null',
                               float(col['price']) if col.get('price') else 'null',
                               "'" + col['additional_fee'] + "'" if col.get('additional_fee') else 'null',
                               "'" + col['shipments'] + "'" if col.get('shipments') else 'null',
                               "'" + col['discount'] + "'" if col.get('discount') else 'null'
                               )

                logger.info('SQL: %s', _sql)
                cursor.execute(_sql)
                conn.commit()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to insert record into table", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")

    def check_visa_product(self, sheet_name='visa_product', table='visa_product'):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        column_to_be_check = [
            "country", "purpose", "category", "issue_method", "attributes",
            "form", "constraint", "submit_method", "receive_method", "nationality",
            "region_of_residence", "process_type", "currency", "additional_questions",
            "additional_requirements", "tag", "status"
        ]

        excel_data = self.parse_xlsx_file(sheet_name)  # a list
        db_data = self._query_db_table(table)  # a list

        excel_data_dict = dict([(each['id'], each) for each in excel_data])
        db_data_dict = dict([(each['id'], each) for each in db_data])

        # 1. Check visa product ID
        excel_ids = sorted(list(excel_data_dict.keys()))
        db_ids = sorted(list(db_data_dict.keys()))

        id_not_in_db = [each for each in excel_ids if each not in db_ids]
        id_not_in_excel = [each for each in db_ids if each not in excel_ids]

        print('\n ID not in DB:\n {0} \n ID not in Excel:\n {1}'.format(id_not_in_db, id_not_in_excel))

        # 2. Check columns
        column_msg = []
        for vpid, body in db_data_dict.items():
            if vpid < 100000:
                continue

            for col in column_to_be_check:
                db_val, excel_val = body[col], excel_data_dict[vpid].get(col)

                if excel_val:
                    try:
                        if col in ['attributes', 'form', 'constraint', 'additional_requirements']:
                            excel_val = json.loads(excel_val)
                    except Exception:
                        logger.error('Visa Product: %s; Value in excel: %s', vpid, excel_val)

                if db_val != excel_val:
                    msg = 'ID:{0}; {1}; in DB: {2}; in Excel: {3}'.format(vpid, col, db_val, excel_val)
                    column_msg.append(msg)
        if column_msg:
            print('\n'.join(column_msg))
        else:
            print('NO difference!')

    def check_visa_product_pricing(self, sheet_name='pricing', table='visa_customer_price'):
        """
        :param sheet_name:
        :param table:
        :return:
        """
        column_to_be_check = [
            "visa_product_id", "organization_id",
            "consulate_id", "currency",
            "price",
            "additional_fee",
            "shipments",
            "discount", "labeling"
        ]

        excel_data = self.parse_xlsx_file(sheet_name)  # a list
        db_data = self._query_db_table(table)  # a list

        excel_data_dict = dict([(each['visa_product_id'], each) for each in excel_data if each['visa_product_id']])
        db_data_dict = dict([(each['visa_product_id'], each) for each in db_data])

        # 1. Check no duplicate visa product ID
        excel_ids = sorted(list(excel_data_dict.keys()))
        db_ids = sorted(list(db_data_dict.keys()))

        if len(excel_ids) != len(set(excel_ids)):
            print('Duplicate visa product id in Excel')
        else:
            print('Visa product id in Excel: Checked')

        if len(db_ids) != len(set(db_ids)):
            print('Duplicate visa product id in DB')
        else:
            print('Visa product id in DB: Checked')

        # 2. Check columns
        column_msg = []
        for vpid, body in db_data_dict.items():
            if vpid < 100000:
                continue

            for col in column_to_be_check:
                db_val, excel_val = body[col], excel_data_dict[vpid].get(col)

                if excel_val:
                    if col in ['additional_fee', 'shipments']:
                        excel_val = json.loads(excel_val)

                if db_val != excel_val:
                    if col == 'shipments':
                        if excel_val in [None, {}] and db_val in [None, {}]:
                            continue

                    msg = 'ID:{0}; {1}; in DB: {2}; in Excel: {3}'.format(vpid, col, db_val, excel_val)
                    column_msg.append(msg)
        if column_msg:
            print('\n'.join(column_msg))
        else:
            print('NO difference!')

    def _query_db_table(self, table):
        """
        :param table:
        :return:
        """
        try:
            conn = psycopg2.connect(
                user=self.user,
                password=self.password,
                host=self.dbhost,
                port=self.port,
                database=self.database
            )

            logger.info("connected")
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            _sql = "SELECT * from %s;" % table
            logger.info('SQL: %s', _sql)
            cursor.execute(_sql)
            result = [dict(each) for each in cursor.fetchall()]
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to query the visa product records", error)
        finally:
            # closing database connection.
            cursor.close()
            conn.close()
            logger.info("PostgreSQL connection is closed")
        return result

    def init_db_connection(self):
        """
        :return:
        """
        try:
            conn = psycopg2.connect(user=self.user,
                                    password=self.password,
                                    host=self.dbhost,
                                    port=self.port,
                                    database=self.database
                                    )

            logger.info("connected")
            cursor = conn.cursor()
        except (Exception, psycopg2.Error) as error:
            logger.error("Failed to connect to database: %s", error)

        return conn, cursor

    def parse_xlsx_file(self, sheet_name, xlsx_name=None):
        """
        :param xlsx_name:
        :param sheet_name:
        :return:
        """
        if xlsx_name is None:
            xlsx_name = self.xlsx_name

        dirname, _ = os.path.split(os.path.abspath(__file__))
        dfs = pd.read_excel(os.path.join(dirname, xlsx_name), sheet_name=sheet_name)
        dfs = dfs.replace({pd.np.nan: None})

        header = dfs.columns.tolist()
        values = dfs.values.tolist()

        new_values = []
        for val in values:
            if all([v is None for v in val]):
                continue
            val = [v.strip() if type == str else v for v in val]
            new_values.append(val)

        return [dict(zip(header, val)) for val in new_values]


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Helper to insert data to Postgresql')
    parser.add_argument('--log-level', default='INFO', help='Logging level')

    args = parser.parse_args()

    DEFAULT_LOG_FMT = '%(asctime)s.%(msecs)s:%(name)s:%(thread)d:%(levelname)s:%(process)d:%(message)s'
    logging.basicConfig(
        format=DEFAULT_LOG_FMT,
        level=logging.getLevelName(args.log_level.upper())
    )

    rc = 0
    try:
        logger.info('WARNING: Running on the cloud database')
        dbhost = db_cloud_profile.get('dbhost')
        database = db_cloud_profile.get('database')
        user = db_cloud_profile.get('user')
        password = db_cloud_profile.get('password')

        loader = Loader(dbhost, database, user, password)

        loader.run()

    except Exception as exc:
        logger.exception('Unhandled exception: %s', exc)
        sys.exit(1)

    sys.exit(rc)
