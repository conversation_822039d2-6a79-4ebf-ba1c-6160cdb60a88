# Data to Database


## How to setup local PostgreSQL container

- Pull the image, with version 9.6.8
```buildoutcfg
docker pull postgres:9.6.8
```

- Run postgres container locally
```buildoutcfg
docker run --name localpostgres --rm -itd -p 5432:5432 \
  -e POSTGRES_USER=aria \
  -e POSTGRES_DB=earth \
  -e POSTGRES_PASSWORD=password \
  postgres:9.6.8
```
> Note: Here, we set the postgres user and database name, same as our production database


- Connect to Postgres database "earth"

```buildoutcfg
psql -h localhost -d earth -U aria
```

- Login to the postgres container, for debug purpose
```buildoutcfg
docker exec -it localpostgres bash
```

- Close the postgres container
```buildoutcfg
docker stop localpostgres
```

## How to load data to PostgreSQL database

- Test your loader to the local PostgreSQL, for table **visa_product**
```buildoutcfg
(michael)~:python3 loader.py  --test --table visa_product
```

- Load your local data to the production PostgreSQL, for table **visa_product**

```buildoutcfg
(michael)~:python3 aurora_loader.py --table visa_product --password <db passpowrd>
```


## How to open Kibana 

You need setup SSH tunnel to connect to Kibana

- Add the following config to the `~/.ssh/config`
```buildoutcfg
# Elasticsearch Tunnel
Host estunnel
HostName ************
User ec2-user
IdentitiesOnly yes
IdentityFile ~/.ssh/ad-jumphost.pem
LocalForward 9200 vpc-ad-es-upjnyogwuftl2oi2elb7pptnum.us-west-2.es.amazonaws.com:443
```
- The run the command
```buildoutcfg
ssh estunnel -N
```

- Launch Kibana from a browser
```buildoutcfg
# Search: 
https://localhost:9200
# Kibana: 
https://localhost:9200/_plugin/kibana
```
