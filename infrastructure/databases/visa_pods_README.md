
# Visa Pods

### Purpose

For each visa application, it requires dozens or up to a hundred questions, and or requirement by a consulate;
the Frontend, includes Webapp and Mobile APP, will collect these information and send to the Backend. To keep
each question with a standard name, format across the Frondend and Backend, we break down each question and requirement
of a visa product to the small piece of data structure, called **VISA POD**.


### Category


In the high level, we define the **category** for each pod, there are two type of category, in general,
and a country specific one.

**Generic categories**

It has **6-digit** in IDs:
* the 1st digit:9
* the 2nd-3th digits: a different category
* the 4th-6th digits: a sub-category

The pre-defined categories:

* **10100**  additional_question
* **901000** passport
* **902000** visa_info
* **903000** travel
* **904000** personal
* **905000** family
* **906000** employee
* **907000** immigration_status
* **908000** support_document
* **909000** photo
* **910000** TBD
* **911000** TBD
* ...

**Country specific categories**

All country specific pods are in a same category **additonal_question**, and the country alpha3 code is the
 **sub_category**

It has **5-digit** in IDs:
* the 1st-3rd digits: country, which is same as numbers in **_Visa Product_** per country
* the 4th-5th digits: any pod id

The pre-defined sub_categories:

* **10000** VNM
* **10100** CHN
* **10200** IND
* **10300**
* **10400**
* ...


### Visa Pods in Database


All **visa pods** are in a table called **visa_pods**

```markdown
                                 Table "public.visa_pods"
    Column    |            Type             |                   Modifiers
--------------+-----------------------------+-----------------------------------------------
 id           | integer                     | not null
 category     | character varying(64)       | not null
 sub_category | character varying(64)       |
 title        | character varying(512)      |
 name         | character varying(128)      |
 body         | json                        |
 order        | integer                     | not null default '-1'::integer
 status       | character varying(16)       | default 'active'::character varying
```
**Note**: `body` includes all the information, such as id, category, sub_category, title, name, order

#### order
The `order` column is used to sort the pods across the category, and sub_category. The default value is -1, which means
no knowledge to sort this pod on web/app, or the pod is not shown on web/app. The `order` is a 6-digit integer, the
first 3-digit is for the category, starting with 101, the latter 3-digit is for inside the category, starting
with 000.


Each visa product contains a bunch of visa pods:

```markdown
                                      Table "public.visa_product"
         Column          |            Type             |                   Modifiers
-------------------------+-----------------------------+-----------------------------------------------
 ...
 schema                  | json                        |
 schema_pods             | integer[]                   |
 pods_amendment          | json                        |
 ...
```

* **schema**: a array of self contained visa pods, which extracts the reference and replaces with the field details
* **schema_pods**: a array of visa pods ID
* **pods_amendment**:  a dict JSON for the amendments



### Pod Structure



```json
  {
    "id":             REQUIRED,
    "name":           REQUIRED,
    "title":          REQUIRED,
    "type":           REQUIRED,
    "type_fe":        REQUIRED,
    "category":       REQUIRED,
    "sub_category":   REQUIRED,
    "constraint":     OPTIONAL,
    "default":        OPTIONAL,
    "min_length":     OPTIONAL,
    "max_length":     OPTIONAL,
    "pattern":        OPTIONAL,
    "ui":             OPTIONAL,
    "range_from":     OPTIONAL,
    "range_to":       OPTIONAL,
    "option_list":    OPTIONAL,
    "option_choice":  OPTIONAL,
    "description":    OPTIONAL,
    "document_type":  OPTIONAL
  }
  
```

#### type
It is generally used for Backend, the supported values for now are:

* boolean
* object
* string
         
#### type_fe
It is generally used for Frontend, the supported values for now are:

* input_alphabet_number - Only allow to input by number and alphabet characters
* input_number - Only allow to input by number
* input_alphabet - Only allow to input by alphabet characters
* input - Allow to input anything ( characters, number, special chacaters)
* picker_date - Show date picker for select
* picker_datetime - Show date picker and time picker for select
* select_country - Search and select country 
* select_countries - Search and select multiple countries
* select_airport - Search and select airport
* select_airline - Search and select airline
* select_address - Select address from address list
* warning - Show warning message
* list - Select item from list using option list
* switch - Switch on/off - also check option choice if have
* boolean - Yes / No question - also check option choice if have
* upload_file - upload file (could be image or document pdf)

#### type_fe: ui
* **show** default value, means FE needs to show the pod;
* **noshow** FE does not show the pod, BE will fill the value for this pod;
* **auto** will get the pod's value from FE, but it may not be shown on the ui.
 For example, the nationality pod, the value comes from the first ui page in `find_visa` logic

### Constraint vs. Validation

Since version 2.1.0, we introduce the concept of visa **validation** and **constraint**. Sometimes, it may cause
some confusion that what is the difference between two.

In theory, there is no technical difference between **validation** and **constraint**, those both define the
requirement and/or condition of the input values for the visa application. The reason for us to use the constraint
is to make the priority of the required information.

* All visa pods constraint will be asked right after the first **find visa** query, before customer fill out the
details to apply an application;
* We use the **`constraint: true/false`** in visa pod to indicate whether this visa pod is a constraint or not for this
visa product;
* Any visa pods could be flexibly tuned to a constraint per visa product;
