package main

import (
	"log"
	"os"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	"github.com/urfave/cli"
)

func main() {
	app := cli.NewApp()
	app.Name = "aria-devices"
	app.Usage = "Devices service for Aria"
	app.Commands = []cli.Command{
		serviceCmd(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}
}

func serviceCmd() cli.Command {
	return cli.Command{
		Name:      "service",
		ShortName: "svc",
		Usage:     "Start devices service for Aria",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:   "port",
				Usage:  "Port the server listens to",
				EnvVar: "PORT",
				Value:  "3000",
			},
			cli.StringFlag{
				Name: "pg-conn-string",
				Usage: "Postgres connection string, including username password. If not provided, server will try to " +
					"get connection string aws instance role",
				EnvVar: "PG_CONN_STR",
			},
			cli.StringFlag{
				Name:  "db-config",
				Usage: "Env for db config",
			},
			cli.StringFlag{
				Name:     "ad_secrets",
				Usage:    "Env for secret configuration",
				EnvVar:   "ad_secrets",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad-aws",
				Usage:    "AWS config",
				EnvVar:   "ad_aws",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_firebase",
				Usage:    "firebase config credentials",
				EnvVar:   "ad_firebase",
				Required: true,
			},
			cli.StringFlag{
				Name:   "account-service-config",
				Usage:  "account service config",
				EnvVar: "account_service_config",
			},
			cli.StringFlag{
				Name:     "ad_api_token",
				Usage:    "Env for api token configuration",
				EnvVar:   "ad_api_token",
				Required: true,
			},
			flags.LogLevelFlag,
		},
		Action: startDevicesService(),
	}
}
