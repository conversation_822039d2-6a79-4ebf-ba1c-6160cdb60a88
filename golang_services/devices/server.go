package main

import (
	"os"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	"bitbucket.org/persistence17/aria/golang_services/handlers/device"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/tidwall/gjson"
	"github.com/urfave/cli"
)

func startDevicesService() cli.ActionFunc {
	return func(c *cli.Context) error {
		port := c.String("port")

		e, err := newDevicesServer(c)
		if err != nil {
			return err
		}

		return e.Run(":" + port)
	}
}

func newDevicesServer(c *cli.Context) (*gin.Engine, error) {
	logger.SetJsonZeroLoggerWithLevel(c.String(flags.LogLevelFlag.Name))
	e := gin.New()
	e.Use(logger.LoggerMW(), logger.CustomLoggerMW(), gzip.Gzip(gzip.DefaultCompression))
	_, conf, err := middlewares.LoadCommonMWAndConf(c, e)
	log.Info().Interface("conf", conf).Msg("config from conf file")
	if err != nil {
		return nil, err
	}

	// aws related
	awsConf := utils.ParseJsonObjStr(c.String("ad-aws"))
	//awsConfig := aws.NewConfig().WithRegion(awsConf["region"].(string)).WithLogLevel(aws.LogOff)
	//
	//sess, err := session.NewSession(awsConfig)
	//if err != nil {
	//	return nil, err
	//}

	// db
	var conn *db.AuroraDB
	dbConfigMap := utils.GetMapEnv("ad_db")
	if host := dbConfigMap["write_host"]; host != nil {
		log.Info().Interface("write_host", dbConfigMap["write_host"]).Msg("Loading DB config from ENV")
		conn, err = db.NewAuroraDBFromConfigMap(dbConfigMap)
		if err != nil {
			return nil, err
		}
	}

	if err := middlewares.InitSecretMap(c.String("ad_secrets")); err != nil {
		return nil, err
	}

	firebaseConf := gjson.Parse(os.Getenv("ad_firebase"))
	firebaseProjectID, firebaseCreds := firebaseConf.Get("project_id").String(), firebaseConf.Get("certification").String()

	device.InitDeviceHandlers(conn, firebaseProjectID, firebaseCreds)
	userAccountServiceConfigMap := utils.GetMapEnv(c.String("account-service-config"))
	// api token
	apiTokenMap := utils.ParseJsonObjStr(os.Getenv("ad_api_token"))
	log.Info().Int("api_token_map_size", len(apiTokenMap)).Msg("api token from env")
	e.Use(
		middlewares.AWSRegionMW(awsConf["region"].(string)),
		middlewares.AccountServiceConfigMW(userAccountServiceConfigMap),
		middlewares.APITokenConfigMW(apiTokenMap),
	)
	_ = device.LoadDevicesV1(e)
	return e, nil
}
