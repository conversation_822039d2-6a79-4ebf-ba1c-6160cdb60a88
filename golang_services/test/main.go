package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net"
	"os"
	"time"
)

type GraylogClient struct {
	conn     net.Conn
	hostname string
	address  string
}

// Initialize the Graylog client
func NewGraylogClient(address string) (*GraylogClient, error) {
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknown"
	}

	client := &GraylogClient{
		address:  address,
		hostname: hostname,
	}

	err = client.connect()
	if err != nil {
		return nil, fmt.Errorf("failed to connect: %v", err)
	}

	return client, nil
}

// connect establishes a TCP connection
func (g *GraylogClient) connect() error {
	if g.conn != nil {
		g.conn.Close()
	}

	conn, err := net.DialTimeout("tcp", g.address, 5*time.Second)
	if err != nil {
		return fmt.Errorf("failed to connect to %s: %v", g.address, err)
	}

	g.conn = conn
	return nil
}

// SendLog sends a log message to Graylog
func (g *GraylogClient) SendLog(level int, shortMessage string, fullMessage string, extra map[string]any) error {
	// Create GELF message
	gelfMessage := map[string]any{
		"version":       "1.1",
		"host":          g.hostname,
		"short_message": shortMessage,
		"level":         level,
		"timestamp":     time.Now().Unix(),
	}

	if fullMessage != "" {
		gelfMessage["full_message"] = fullMessage
	}

	// Add extra fields with underscore prefix
	for k, v := range extra {
		if k != "" {
			gelfMessage["_"+k] = v
		}
	}

	// Marshal to JSON
	messageBytes, err := json.Marshal(gelfMessage)
	if err != nil {
		return fmt.Errorf("failed to marshal GELF message: %v", err)
	}

	// Add newline character for TCP GELF
	messageBytes = append(messageBytes, '\n')

	// Set write deadline
	err = g.conn.SetWriteDeadline(time.Now().Add(5 * time.Second))
	if err != nil {
		return fmt.Errorf("failed to set write deadline: %v", err)
	}

	// Send to Graylog with retry logic
	for retry := 0; retry < 3; retry++ {
		_, err = g.conn.Write(messageBytes)
		if err == nil {
			return nil
		}

		log.Printf("Failed to send message (attempt %d/3): %v", retry+1, err)

		if retry < 2 {
			time.Sleep(time.Second)
			if err := g.connect(); err != nil {
				log.Printf("Failed to reconnect: %v", err)
			}
		}
	}

	return fmt.Errorf("failed to send message after 3 attempts: %v", err)
}

// Close closes the TCP connection
func (g *GraylogClient) Close() error {
	if g.conn != nil {
		return g.conn.Close()
	}
	return nil
}

func main() {
	// Initialize client
	client, err := NewGraylogClient("203.161.49.115:12201")
	if err != nil {
		log.Fatalf("Failed to initialize Graylog client: %v", err)
	}
	defer client.Close()

	// Example of sending a log
	extraFields := map[string]any{
		"service": "api-service",
		"method":  "POST",
		"status":  200,
	}

	// Send test message
	err = client.SendLog(
		6, // INFO level
		"API Request Processed",
		"Successfully processed POST request",
		extraFields,
	)

	if err != nil {
		log.Printf("Error sending log: %v", err)
	} else {
		fmt.Println("Log message sent successfully")
	}

	// Keep connection open briefly to ensure message is sent
	time.Sleep(time.Second)
}
