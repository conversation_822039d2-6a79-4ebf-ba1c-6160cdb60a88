package main

import (
	"log"
	"os"

	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
)

func main() {
	app := cli.NewApp()
	app.Name = "aria-logger"
	app.Usage = "Logger Service"
	app.Commands = []cli.Command{
		serviceCmd(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}
}

func serviceCmd() cli.Command {
	return cli.Command{
		Name:      "service",
		ShortName: "svc",
		Usage:     "Start service",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:   "port",
				Usage:  "Port the server listens to",
				EnvVar: "PORT",
				Value:  "3000",
			},
			cli.StringFlag{
				Name: "pg-conn-string",
				Usage: "Postgres connection string, including username password. If not provided, server will try to " +
					"get connection string aws instance role",
				EnvVar: "PG_CONN_STR",
			},
			cli.StringFlag{
				Name:  "db-config",
				Usage: "Env for db config",
			},
			flags.LogLevelFlag,
		},
		Action: startService(),
	}
}
