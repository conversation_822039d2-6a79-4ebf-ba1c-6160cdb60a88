package main

import (
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	handler "bitbucket.org/persistence17/aria/golang_services/handlers/logger"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func startService() cli.ActionFunc {
	return func(c *cli.Context) error {
		port := c.String("port")

		e, err := newService(c)
		if err != nil {
			return err
		}

		return e.Run(":" + port)
	}
}

func newService(c *cli.Context) (*gin.Engine, error) {
	logger.SetJsonZeroLoggerWithLevel(c.String(flags.LogLevelFlag.Name))
	e := gin.New()
	e.Use(logger.LoggerMW(), logger.CustomLoggerMW(), gzip.Gzip(gzip.DefaultCompression))
	_, conf, err := middlewares.LoadCommonMWAndConf(c, e)
	log.Info().Interface("conf", conf).Msg("config from conf file")

	// db connection
	var conn *db.AuroraDB
	dbConfigMap := utils.GetMapEnv("ad_db")
	if host := dbConfigMap["write_host"]; host != nil {
		log.Info().Interface("write_host", dbConfigMap["write_host"]).Msg("Loading DB config from ENV")
		conn, err = db.NewAuroraDBFromConfigMap(dbConfigMap)
		if err != nil {
			return nil, err
		}
	}

	e.Use(
		middlewares.ORMDaoMW(conn),
	)
	handler.LoadHandlers(e)
	return e, nil
}
