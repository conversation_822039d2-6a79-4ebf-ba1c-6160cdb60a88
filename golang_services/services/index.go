package services

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"os"
	"time"

	"github.com/rs/zerolog/log"
)

type ServiceClient struct {
	Client *http.Client
}

func NewServiceClient() *ServiceClient {
	return &ServiceClient{
		Client: &http.Client{
			Timeout: 1 * time.Minute,
		},
	}
}

type ValidationTokenRequest struct {
	Token string   `json:"token"`
	Roles []string `json:"roles"`
}

func (c *ServiceClient) ValidationToken(token string, roles []string) (int, any, error) {
	action := ValidationTokenRequest{
		Token: token,
		Roles: roles,
	}
	jsoned, err := json.Marshal(action)
	if err != nil {
		return 0, nil, err
	}

	log.Debug().Str("json", string(jsoned)).Msg("validation token request")

	statusCode, body, err := c.SendRequest(jsoned, os.Getenv("INTERNAL_AUTH_SERVICE_HOST")+"/private/validationToken")
	if err != nil {
		return 0, nil, err
	}
	var bodyResp any
	err = json.Unmarshal(body, &bodyResp)
	if err != nil {
		return 0, nil, err
	}
	return statusCode, bodyResp, err
}

func (c *ServiceClient) SendRequest(input []byte, endpoint string) (int, []byte, error) {
	req, err := http.NewRequest("POST", endpoint, bytes.NewBuffer(input))
	req.Header.Set("Content-Type", "application/json")
	client := c.Client
	resp, err := client.Do(req)
	if err != nil {
		return 0, nil, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return 0, nil, err
	}
	body = bytes.TrimPrefix(body, []byte("\xef\xbb\xbf"))

	return resp.StatusCode, body, err
}

func (c *ServiceClient) SendInternalRequest(input []byte, endpoint string, token string) (int, []byte, error) {
	req, err := http.NewRequest("POST", endpoint, bytes.NewBuffer(input))

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-ad-token", token)

	client := c.Client
	resp, err := client.Do(req)
	if err != nil {
		return 0, nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return 0, nil, err
	}
	body = bytes.TrimPrefix(body, []byte("\xef\xbb\xbf"))

	return resp.StatusCode, body, err
}

func (c *ServiceClient) GetUserInfo(token string, url string) (int, []byte, error) {
	req, err := http.NewRequest("GET", url, nil)
	req.Header.Set("x-access-token", token)

	client := c.Client
	resp, err := client.Do(req)
	if err != nil {
		return 0, nil, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return 0, nil, err
	}

	return resp.StatusCode, body, err
}
