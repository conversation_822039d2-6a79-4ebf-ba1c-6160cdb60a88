package response

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/errors"
)

func HandleErrResponseWithCode(c *gin.Context, statusCode int, err error) {
	c.<PERSON>SON(statusCode, gin.H{
		"success": false,
		"error":   err.<PERSON>rror(),
	})
}

func HandleResponse(c *gin.Context, data any, err error) {
	if err != nil {
		switch err {
		case errors.BadPassword, errors.DuplicateEmailErr, errors.ExpiredPromoCode, errors.InvalidCorpCodeErr,
			errors.InvalidEtsIDErr, errors.NoDataWithEntryDate, errors.NotDataWithNationalityOrResidence,
			errors.ProcessingTimeEntryDateErr, errors.ExitTimeErr:
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err,
			})
			return
		case errors.NotFoundErr, errors.NoAvailableVisaProductErr, errors.NationalityNotSupportedErr, errors.NoAvailableVisaProductEntryDateErr, errors.SystemBusyErr:
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   err,
			})
			return
		case errors.InvalidTokenErr, errors.InvalidEmailOrPasswordErr, errors.NotMatchWithOldPasswordErr:
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   err,
			})
			return
		case errors.CannotSubmitStateErr:
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		case errors.AppUpdatedByOthersErr:
			c.JSON(http.StatusConflict, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		default:
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}
