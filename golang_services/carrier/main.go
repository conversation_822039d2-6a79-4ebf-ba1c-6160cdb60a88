package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/urfave/cli"

	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/fedex"
	"bitbucket.org/persistence17/aria/golang_services/sdk/queue"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func main() {
	app := cli.NewApp()
	app.Name = "aria-carrier"
	app.Usage = "carrier service for Aria"
	app.Commands = []cli.Command{
		carrierCmd(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("carrier failed with error %v", err)
	}
}

func carrierCmd() cli.Command {
	return cli.Command{
		Name:      "carrier",
		ShortName: "s",
		Usage:     "Start carrier single thread",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:     "ad_sqs",
				Usage:    "Env for sqs config",
				EnvVar:   "ad_sqs",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_aws",
				Usage:    "Env for aws config",
				EnvVar:   "ad_aws",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_s3",
				Usage:    "Env for s3 config",
				EnvVar:   "ad_s3",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_fedex",
				Usage:    "API key for calling fedex service",
				EnvVar:   "ad_fedex",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_api_token",
				Usage:    "API key for api token config",
				EnvVar:   "ad_api_token",
				Required: true,
			},
		},
		Action: runCarrier(),
	}
}

func runCarrier() cli.ActionFunc {
	return func(c *cli.Context) error {

		sqsConfigMap := utils.GetMapEnv("ad_sqs")
		if sqsConfigMap["url_prefix"].(string) == "" {
			return fmt.Errorf("missing url_prefix in ad_sqs")
		}
		if sqsConfigMap["shipment_sqs_name"].(string) == "" {
			return fmt.Errorf("missing shipment_sqs_name in ad_sqs")
		}

		s3ConfigMap := utils.GetMapEnv("ad_s3")
		if s3ConfigMap["ariadirect_prod_applications"].(string) == "" {
			return fmt.Errorf("missing ariadirect_prod_applications in ad_s3")
		}

		apiTokenConfigMap := utils.GetMapEnv("ad_api_token")
		if apiTokenConfigMap["token"].(string) == "" {
			return fmt.Errorf("missing token in ad_api_token")
		}

		sqsQueueURL := sqsConfigMap["url_prefix"].(string) + "/" + sqsConfigMap["shipment_sqs_name"].(string)

		awsUSConfig := utils.GetMapEnv("ad_aws")
		if awsUSConfig["region"].(string) == "" {
			return fmt.Errorf("missing region in ad_aws")
		}

		awsConfig := aws.NewConfig().WithRegion(awsUSConfig["region"].(string)).WithLogLevel(aws.LogOff)

		sess, err := session.NewSession(awsConfig)
		if err != nil {
			return err
		}

		s3Presigner := awslib.NewS3Svc(sess)

		fedexConfig := utils.GetMapEnv("ad_fedex")
		fdx, err := fedex.NewFedex(fedexConfig)
		if err != nil {
			return err
		}

		ca := NewCarrierProcessor(s3ConfigMap, s3Presigner, fdx, apiTokenConfigMap)

		s := queue.NewSQSProcessor(sess, sqsQueueURL, ca.Carrier)

		s.Run()
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGHUP, syscall.SIGTERM)

	loop:
		for {
			select {
			case <-sigCh:
				break loop
			}
		}

		return nil
	}
}
