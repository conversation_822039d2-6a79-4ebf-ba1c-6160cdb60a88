package main

import (
	"fmt"
	"image"
	"image/jpeg"
	"os"
	"path/filepath"

	"github.com/disintegration/gift"
	"github.com/karmdip-mi/go-fitz"
	"github.com/signintech/gopdf"
)

func EditFedexLabelPDFFile(filePath string) ([]string, error) {
	doc, err := fitz.New(filePath)
	if err != nil {
		panic(err)
	}
	// folder := strings.TrimSuffix(path.Base(filePath), filepath.Ext(path.Base(filePath)))
	images := []string{}

	os.Mkdir("img", os.ModeDevice)
	for n := 0; n < doc.NumPage(); n++ {
		img, err := doc.Image(n)
		if err != nil {
			return nil, err
		}

		g := gift.New(
			gift.Rotate90(),
			gift.Resize(int(gopdf.PageSizeA4.W), 0, gift.LanczosResampling),
		)
		dst := image.NewNRGBA(image.Rect(0, 0, int(gopdf.PageSizeA4.W), int(gopdf.PageSizeA4.H)))
		for y := 0; y < int(gopdf.PageSizeA4.H); y++ {
			for x := 0; x < int(gopdf.PageSizeA4.W); x++ {
				dst.Set(x, y, image.White)
			}
		}

		g.DrawAt(dst, img, image.Pt(0, 100), gift.OverOperator)
		imagePath := filepath.Join("img/", fmt.Sprintf("image-%05d.jpg", n))
		f, err := os.Create(imagePath)
		if err != nil {
			fmt.Println(err)
			return nil, err
		}

		if err = jpeg.Encode(f, dst, nil); err != nil {
			return nil, err
		}
		images = append(images, imagePath)

	}

	os.Remove(filePath)
	pdf := gopdf.GoPdf{}
	pdf.Start(gopdf.Config{PageSize: *gopdf.PageSizeA4, TrimBox: gopdf.Box{Left: 0, Top: 0, Right: 0, Bottom: 0}})
	for _, image := range images {
		pdf.AddPage()
		pdf.Image(image, 50, 0, nil) //print image

	}
	pdf.WritePdf(filePath)
	// if err := api.ImportImagesFile(images, filePath, &pdfcpu.Import{ScaleAbs: true, PageDim: pdfcpu.PaperSize["A4"]}, nil); err != nil {
	// 	return nil, err
	// }

	return images, nil
}
