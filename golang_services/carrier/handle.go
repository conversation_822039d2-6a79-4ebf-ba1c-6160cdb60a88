package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"

	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/fedex"
	"bitbucket.org/persistence17/aria/golang_services/sdk/file"
	"bitbucket.org/persistence17/aria/golang_services/sdk/shipment"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/services"
)

const (
	FedExServiceCarrier = "FedEx"
	ContentTypePDF      = "application/pdf"
)

type CarrierProcessor struct {
	s3Config          map[string]any
	s3Presigner       *awslib.S3Svc
	fedex             *fedex.FedexClient
	serviceClient     *services.ServiceClient
	apiTokenConfigMap map[string]any
}

func NewCarrierProcessor(s3Config map[string]any, s3Presigner *awslib.S3Svc, fedex *fedex.FedexClient, apiTokenConfigMap map[string]any) CarrierProcessor {
	service := services.NewServiceClient()
	return CarrierProcessor{s3Config: s3Config, s3Presigner: s3Presigner, fedex: fedex, serviceClient: service, apiTokenConfigMap: apiTokenConfigMap}
}

func (s *CarrierProcessor) Carrier(message *sqs.Message) error {
	fmt.Println(*message.Body)
	var msg shipment.ShippingLabelInfo
	err := json.Unmarshal([]byte(*message.Body), &msg)
	if err != nil {
		return err
	}

	emailJ := gjson.Parse(os.Getenv("ad_email"))
	endpointJ := gjson.Parse(os.Getenv("ad_endpoint"))

	switch msg.Data.ShippingCarrier {
	case FedExServiceCarrier:
		{
			// 1. create shipping label. The Fedex service returns the base64 pdf label.
			msg.Data.ServiceType = GetShippingTypeCode(msg.Data.ServiceType)
			msg.Data.Description = fmt.Sprintf("Order #%d", msg.PackageID)

			result, err := s.fedex.CreateShippingLabel(msg.Data)

			// Callback data
			updateData := map[string]any{
				"package_id":       msg.PackageID,
				"status":           msg.Status,
				"ship_date":        msg.Data.ShipDate,
				"shipment_content": msg.Data.ShippingContent,
			}

			if err != nil {
				utils.LogOrderEvent(utils.ERROR, msg.ServiceType, msg.PackageID, "FEDEX_CREATE_LABEL", err.Error())

				utils.SendEmail(utils.SendEmailRequest{
					TemplateName: "failed_shipping_label_notification",
					To:           emailJ.Get("support").String(),
					Parameters: map[string]any{
						"OrderID":       msg.PackageID,
						"RecipientName": msg.Data.Recipient.Name,
						"FullAddress":   fmt.Sprintf("%s %s, %s %s, %s", msg.Data.Recipient.Address, msg.Data.Recipient.City, msg.Data.Recipient.State, msg.Data.Recipient.ZipCode, msg.Data.Recipient.Country),
						"PhoneNumber":   msg.Data.Recipient.Phone,
						"URL":           fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=%s", endpointJ.Get("web_base_url").String(), msg.PackageID, msg.ServiceType),
						"Reason":        err.Error(),
					},
				})
			} else {
				utils.LogOrderEvent(utils.SUCCESS, msg.ServiceType, msg.PackageID, "FEDEX_CREATE_LABEL", "success")

				// 2. convert base64 pdf to pdf file and save to local.
				dec, err := base64.StdEncoding.DecodeString(result.Label)
				if err != nil {
					return err
				}
				fileName := fmt.Sprintf("AriaDirect-Regular-Order-Number-%d-FedEx-label-%s.pdf", msg.PackageID, result.TrackingNumber)
				localPath, err := filepath.Abs(filepath.Dir(os.Args[0]))
				if err != nil {
					return err
				}
				fileDir := localPath + "/" + fileName
				f, err := os.Create(fileDir)
				if err != nil {
					return err
				}
				defer f.Close()
				if _, err := f.Write(dec); err != nil {
					return err
				}
				if err := f.Sync(); err != nil {
					return err
				}
				if err != nil {
					return err
				}

				defer utils.RemoveFile(fileDir)
				// 3. add label to s3
				bucket := s.s3Config["ariadirect_prod_applications"].(string)
				key := file.BuildPkgInputS3Key(msg.UserID, msg.PackageID, fileName)
				err = s.s3Presigner.UploadFile(bucket, key, fileDir, ContentTypePDF)
				if err != nil {
					return err
				}
				updateData["label"] = fmt.Sprintf("%s/%s", bucket, key)

				if result != nil {
					updateData["currency"] = result.Currency
					updateData["price"] = result.Price
					updateData["tracking_number"] = result.TrackingNumber
					updateData["tracking_url"] = "https://www.fedex.com/fedextrack/?trknbr=" + result.TrackingNumber

					notificationEmails := funk.FilterString(funk.UniqString([]string{
						emailJ.Get("support").String(),
						msg.UserEmail,
						msg.Data.Recipient.Email,
					}), func(email string) bool { return email != "" })

					defer func() {
						RETRY := 5
					R:
						for i := 0; i < RETRY; i++ {
							time.Sleep(time.Duration(i+1) * time.Minute)

							if err := s.fedex.RequestNotification(result.TrackingNumber, notificationEmails, emailJ.Get("support").String(), fmt.Sprintf("AriaDirect %s Order #%d", msg.ServiceType, msg.PackageID)); err != nil {
								fmt.Println(err)
								utils.LogOrderEvent(utils.ERROR, msg.ServiceType, msg.PackageID, "FEDEX_REQUEST_NOTIFICATION", err.Error())
							} else {
								utils.LogOrderEvent(utils.SUCCESS, msg.ServiceType, msg.PackageID, "FEDEX_REQUEST_NOTIFICATION", "success")
								break R
							}
						}
					}()

				}
			}

			updateDataJSON, err := json.Marshal(updateData)
			if err != nil {
				return err
			}
			// Callback: /update-shipment-status
			fmt.Println("Callback: ", msg.Callback)
			fmt.Println(string(updateDataJSON))
			statusCode, _, err := s.serviceClient.SendInternalRequest(updateDataJSON, msg.Callback, s.apiTokenConfigMap["token"].(string))
			if err != nil {
				utils.LogOrderEvent(utils.ERROR, msg.ServiceType, msg.PackageID, "FEDEX_CALLBACK_REQUEST", err.Error())
				return err
			} else {
				utils.LogOrderEvent(utils.SUCCESS, msg.ServiceType, msg.PackageID, "FEDEX_CALLBACK_REQUEST", "success")
			}
			if statusCode >= 400 {
				return fmt.Errorf("unable to send the package data update request")
			}

		}
	default:
		{
			return fmt.Errorf(fmt.Sprintf("not found: %s.\n", msg.Data.ShippingCarrier))
		}
	}
	return nil
}

func GetShippingTypeCode(key string) string {
	var shippingType = func() map[string]string {
		return map[string]string{
			"FedEx Next day":               "STANDARD_OVERNIGHT",
			"FedEx 3 days":                 "FEDEX_2_DAY",
			"FedEx International 3 days":   "INTERNATIONAL_PRIORITY",
			"FedEx International 5 days":   "INTERNATIONAL_PRIORITY",
			"FedEx International 6 days":   "INTERNATIONAL_PRIORITY",
			"FedEx International Priority": "INTERNATIONAL_PRIORITY",
		}
	}
	return shippingType()[key]
}
