package main

import (
	"fmt"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	"bitbucket.org/persistence17/aria/golang_services/handlers/atlas"
	atlas_v1 "bitbucket.org/persistence17/aria/golang_services/handlers/atlas/v1"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func startAtlasService(c *cli.Context) error {
	port := c.String("port")

	e, err := newAtlasServer(c)
	if err != nil {
		return err
	}

	return e.Run(":" + port)
}

func newAtlasServer(c *cli.Context) (*gin.Engine, error) {
	logger.SetJsonZeroLoggerWithLevel(c.String(flags.LogLevelFlag.Name))
	e := gin.New()
	e.Use(logger.LoggerMW(), logger.CustomLoggerMW(), gzip.Gzip(gzip.DefaultCompression))

	// db
	if err := initDb(c, e); err != nil {
		return nil, err
	}
	// aws related
	awsConf := utils.ParseJsonObjStr(c.String("ad-aws"))
	awsConfig := aws.NewConfig().WithRegion(awsConf["region"].(string)).WithLogLevel(aws.LogOff)

	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return nil, err
	}
	// sqs
	if err := initSqs(c, e, sess); err != nil {
		return nil, err
	}
	// secrets
	if err := middlewares.InitSecretMap(c.String("ad_secrets")); err != nil {
		return nil, err
	}
	// account service config
	userAccountServiceConfigMap := utils.GetMapEnv(c.String("account-service-config"))
	log.Info().Interface("account-service-config", userAccountServiceConfigMap).Msg("Account Service config from ENV")
	// api token
	apiTokenMap := utils.ParseJsonObjStr(c.String("ad-api-token"))
	// cors
	corsConf := cors.DefaultConfig()
	corsConf.AddAllowHeaders("x-access-token", "Language")
	corsConf.AllowAllOrigins = true

	e.Use(
		cors.New(corsConf),
		middlewares.AuthorizationMW(),
		middlewares.AccountServiceConfigMW(userAccountServiceConfigMap),
		middlewares.APITokenConfigMW(apiTokenMap),
		middlewares.AWSRegionMW(awsConf["region"].(string)),
	)

	// handlers
	atlas_v1.InitAtlasHandlers(c.String("callback-host"))
	_ = atlas.LoadV1AtlasHandlers(e)
	return e, nil
}

func initDb(c *cli.Context, r gin.IRouter) error {
	// db
	var conn *db.AuroraDB
	var err error
	dbConfigMap := utils.GetMapEnv("ad_db")
	if host := dbConfigMap["write_host"]; host != nil {
		log.Info().Interface("write_host", dbConfigMap["write_host"]).Msg("Loading DB config from ENV")
		conn, err = db.NewAuroraDBFromConfigMap(dbConfigMap)
	}
	if err != nil {
		return err
	}
	r.Use(
		middlewares.VisaDaoMW(conn),
		middlewares.AtlasDaoMW(conn),
	)
	return nil
}

func initSqs(c *cli.Context, r gin.IRouter, sess *session.Session) error {
	sqsConfig := utils.ParseJsonObjStr(c.String("sqs-config"))
	log.Info().Interface("sqs_config", sqsConfig).Msg("SQS config from env")
	prefix := sqsConfig["url_prefix"].(string)
	atlasQueueName := prefix + "/" + sqsConfig["atlas_sqs_name"].(string)
	if atlasQueueName == "" {
		return fmt.Errorf("missing atlas_sqs_name")
	}
	// sync queue for preview to
	atlasQueue := awslib.NewSQSClient(sess, atlasQueueName)
	r.Use(middlewares.SimpleQueueMW(atlasQueue, "atlas"))
	return nil
}
