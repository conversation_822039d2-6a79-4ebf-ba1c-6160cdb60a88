package main

import (
	"log"
	"os"

	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
)

func main() {
	app := cli.NewApp()
	app.Name = "atlas"
	app.Usage = "Atlas Service for Aria"
	app.Flags = []cli.Flag{
		cli.StringFlag{
			Name:   "port",
			Usage:  "Port the server listens to",
			EnvVar: "PORT",
			Value:  "3000",
		},
		cli.StringFlag{
			Name: "pg-conn-string",
			Usage: "Postgres connection string, including username password. If not provided, server will try to " +
				"get connection string aws instance role",
			EnvVar: "PG_CONN_STR",
		},
		cli.StringFlag{
			Name:   "db-config",
			Usage:  "Env for db config",
			EnvVar: "ad_db",
		},
		cli.StringFlag{
			Name:     "ad-aws",
			Usage:    "AWS config",
			EnvVar:   "ad_aws",
			Required: true,
		},
		cli.StringFlag{
			Name:     "sqs-config",
			Usage:    "Env name for sqs config",
			Required: true,
			EnvVar:   "ad_sqs",
		},
		cli.StringFlag{
			Name:     "account-service-config",
			Usage:    "Env for user account configuration",
			Required: true,
		},
		cli.StringFlag{
			Name:     "ad_secrets",
			Usage:    "Env for secret configuration",
			EnvVar:   "ad_secrets",
			Required: true,
		},
		cli.StringFlag{
			Name:     "ad-api-token",
			Usage:    "Env for api token configuration",
			EnvVar:   "ad_api_token",
			Required: true,
		},
		cli.StringFlag{
			Name:     "callback-host",
			Usage:    "Callback host for atlas service",
			EnvVar:   "CALLBACK_HOST",
			Required: true,
		},
		flags.LogLevelFlag,
	}
	app.Action = startAtlasService

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}
}
