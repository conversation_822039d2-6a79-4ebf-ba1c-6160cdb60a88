package main

import (
	"fmt"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/rs/zerolog/log"
	"github.com/urfave/cli"
)

func startPaymentWorkerService() cli.ActionFunc {
	return func(c *cli.Context) error {
		var err = newPaymentWorkerServer(c)
		if err != nil {
			log.Error().Str("error", err.Error()).Msg("payment worker run error")
		}
		return err
	}
}

func newPaymentWorkerServer(c *cli.Context) error {
	logger.SetJsonZeroLoggerWithLevel(c.String(flags.LogLevelFlag.Name))

	dbConfigMap := utils.GetMapEnv("ad_db")
	if dbConfigMap["write_host"].(string) == "" {
		return fmt.Errorf("missing write_host in ad_db")
	}
	if dbConfigMap["dbname"].(string) == "" {
		return fmt.Errorf("missing dbname in ad_db")
	}
	if dbConfigMap["username"].(string) == "" {
		return fmt.Errorf("missing username in ad_db")
	}
	if dbConfigMap["password"].(string) == "" {
		return fmt.Errorf("missing password in ad_db")
	}

	conn, err := db.NewAuroraDBFromConfigMap(dbConfigMap)
	if err != nil {
		return err
	}

	// Connect database
	var instant = PaymentWorkerInstant{
		dao:        db.NewPaymentDaoWithDb(conn),
		packageDao: db.NewDaoWithDb(conn),
	}

	// instant.MigrationDataOnly()
	// return nil
	// load payment vendors
	if err := instant.loadPaymentVendor(c); err != nil {
		fmt.Printf("loadPaymentVendor error: %v", err)
	}

	// verify the payment notification
	if err := instant.RunningJob(); err != nil {
		fmt.Printf("VerifyTheNotification error: %v", err)
	}

	return nil
}

func (instant *PaymentWorkerInstant) loadPaymentVendor(c *cli.Context) error {
	confFile := c.String("conf-file")
	if confFile == "" {
		return fmt.Errorf("config file is require")
	}

	// init wechat pay client
	wechatpayConfigMap := utils.GetMapEnv(c.String("ad_wechatpay"))
	log.Info().Interface("ad_wechatpay", wechatpayConfigMap).Msg("Wechatpay config from ENV")

	useWechatPayProd := c.Bool("use-wechat-pay-prod")
	wechatPayAPIKey := wechatpayConfigMap["wechat_pay_api_key"].(string)
	if err := instant.loadWechatPayClient(wechatpayConfigMap, wechatPayAPIKey, useWechatPayProd); err != nil {
		return err
	}

	// init authorize client
	authorizeConfigMap := utils.GetMapEnv(c.String("ad_authorize"))
	log.Info().Interface("ad_authorize", authorizeConfigMap).Msg("Authorize.net config from ENV")

	if err := instant.loadAuthorizeClient(authorizeConfigMap); err != nil {
		return err
	}

	// init onepay client
	onepayConfigMap := utils.GetMapEnv(c.String("ad_onepay"))
	log.Info().Interface("ad_onepay", onepayConfigMap).Msg("Onepay config from ENV")

	if err := instant.loadOnePayClient(onepayConfigMap); err != nil {
		return err
	}

	return nil
}
