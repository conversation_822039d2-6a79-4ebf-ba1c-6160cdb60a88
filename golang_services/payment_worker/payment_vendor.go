package main

import (
	"fmt"
	"net/http"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/payments"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/robfig/cron"
	"github.com/rs/zerolog/log"
)

// PaymentWorkerInstant payment worker install
type PaymentWorkerInstant struct {
	dao             *db.PaymentsDao
	packageDao      *db.Dao
	WechatClient    *payments.WechatPayClient
	AuthorizeClient *payments.AuthorizeClient
	OnePayClient    *payments.OnePayClient
}

// RunningJob verify the notification
func (instant *PaymentWorkerInstant) RunningJob() error {
	tasks := []*utils.Task{}

	tasks = append(tasks, utils.NewTask(func() error {
		select {} // Wait forever
	}))

	tasks = append(tasks, utils.NewTask(func() error {
		var cr = cron.New()
		running := false
		if err := cr.AddFunc("@every 5m", func() {
			if running {
				return
			}
			running = true
			if err := instant.JobVerifyAuthorizeWebhook(); err != nil {
				fmt.Printf("JobVerifyAuthorizeWebhook error: %v", err)
			}
			if err := instant.JobVerifyAuthorizeNet(); err != nil {
				fmt.Printf("JobVerifyAuthorizeNet error: %v", err)
			}
			if err := instant.JobVerifyWechatPay(); err != nil {
				fmt.Printf("JobVerifyWechatPay error: %v", err)
			}
			if err := instant.JobVerifyOnePay(); err != nil {
				fmt.Printf("JobVerifyOnePay error: %v", err)
			}
			running = false
		}); err != nil {
			return err
		}

		cr.Start()
		return nil
	}))

	var p = utils.NewPool(tasks, 10) // Set max concurrency is 10
	p.Run()

	if p.HasErrors() {
		for _, task := range p.Tasks {
			if task.Err != nil {
				log.Error().Msgf("Task return error %v", task.Err)
			}
		}
		return fmt.Errorf("Payment worker run error, please check log! ")
	}
	return nil
}

// loadWechatPayClient load Wechat client
func (instant *PaymentWorkerInstant) loadWechatPayClient(conf map[string]any, apiKey string, isProd bool) error {
	appID, ok := conf["wechat_pay_app_id"].(string)
	if !ok {
		return fmt.Errorf("missing wechat_pay_app_id in config file")
	}
	mchID, ok := conf["wechat_pay_account_id"].(string)
	if !ok {
		return fmt.Errorf("missing wechat_pay_account_id in config file")
	}

	instant.WechatClient = payments.NewWechatPayClient(appID, apiKey, mchID, isProd)
	return nil
}

// loadAuthorizeClient load Authorize client
func (instant *PaymentWorkerInstant) loadAuthorizeClient(config map[string]any) error {
	appToken, ok := config["authorize_api_token"].(string)
	if !ok {
		return fmt.Errorf("missing authorize_api_token in svcConfigMap")
	}
	appKey, ok := config["authorize_key"].(string)
	if !ok {
		return fmt.Errorf("missing authorize_key in svcConfigMap")
	}
	appMode, ok := config["authorize_mode"].(string)
	if !ok {
		return fmt.Errorf("missing authorize_mode in svcConfigMap")
	}
	appEndpoint, ok := config["authorize_endpoint"].(string)
	if !ok {
		return fmt.Errorf("missing authorize_endpoint in svcConfigMap")
	}
	formPost, ok := config["authorize_form_post_url"].(string)
	if !ok {
		return fmt.Errorf("missing authorize_form_post_url in svcConfigMap")
	}
	webhookURL, ok := config["authorize_webhook_url"].(string)
	if !ok {
		return fmt.Errorf("missing authorize_webhook_url in svcConfigMap")
	}
	webhookID, ok := config["authorize_webhook_id"].(string)
	if !ok {
		return fmt.Errorf("missing authorize_webhook_id in svcConfigMap")
	}
	webhookSign, ok := config["authorize_webhook_sign"].(string)
	if !ok {
		return fmt.Errorf("missing authorize_webhook_sign in svcConfigMap")
	}
	urlCallback, ok := config["authorize_webhook_callback"].(string)
	if !ok {
		return fmt.Errorf("missing authorize_webhook_callback in svcConfigMap")
	}
	urlRedirect, ok := config["authorize_redirect_url"].(string)
	if !ok {
		return fmt.Errorf("missing authorize_redirect_url in svcConfigMap")
	}
	instant.AuthorizeClient = &payments.AuthorizeClient{
		APIName:     appToken,
		APIKey:      appKey,
		Mode:        appMode,
		Endpoint:    appEndpoint,
		FormPost:    formPost,
		WebhookURL:  webhookURL,
		WebhookID:   webhookID,
		WebhookSign: webhookSign,
		URLCallback: urlCallback,
		URLRedirect: urlRedirect,
		Client:      &http.Client{},
	}
	return nil
}

// loadOnePayClient load OnePay client
func (instant *PaymentWorkerInstant) loadOnePayClient(config map[string]any) error {
	enabled, ok := config["onepay_enabled"].(bool)
	if !ok {
		return fmt.Errorf("missing onepay_enabled in config file")
	}
	if !enabled {
		return nil
	}

	merchantID, ok := config["onepay_merchant_id"].(string)
	if !ok {
		return fmt.Errorf("missing onepay_merchant_id in config file")
	}

	merchantOrder, ok := config["onepay_merchant_order"].(string)
	if !ok {
		return fmt.Errorf("missing onepay_merchant_order in config file")
	}

	onepayUser, ok := config["onepay_user"].(string)
	if !ok {
		return fmt.Errorf("missing onepay_user in config file")
	}

	onepayPassword, ok := config["onepay_password"].(string)
	if !ok {
		return fmt.Errorf("missing onepay_password in config file")
	}

	apiKey, ok := config["onepay_api_key"].(string)
	if !ok {
		return fmt.Errorf("missing onepay_api_key in config file")
	}

	apiSecret, ok := config["onepay_api_secret"].(string)
	if !ok {
		return fmt.Errorf("missing onepay_api_secret in config file")
	}

	endpoint, ok := config["onepay_endpoint"].(string)
	if !ok {
		return fmt.Errorf("missing onepay_endpoint in config file")
	}

	callback, ok := config["onepay_callback"].(string)
	if !ok {
		return fmt.Errorf("missing onepay_callback in config file")
	}

	queryDC, ok := config["onepay_query_dc"].(string)
	if !ok {
		return fmt.Errorf("missing onepay_query_dc in config file")
	}

	instant.OnePayClient = &payments.OnePayClient{
		MerchantID:    merchantID,
		MerchantOrder: merchantOrder,
		User:          onepayUser,
		Password:      onepayPassword,
		APIKey:        apiKey,
		APISecret:     apiSecret,
		Endpoint:      endpoint,
		CallbackURL:   callback,
		QueryDCURL:    queryDC,
		Client:        payments.NewOnePayHTTPClient(true),
	}

	return nil
}

// UpdateCartStatusByPayment update cart status by payment status
func (instant *PaymentWorkerInstant) UpdateCartStatusByPayment(paymentID, paymentStatus string) error {
	if paymentStatus == models.PaymentStatusSuccess {
		if _, err := instant.packageDao.Db.PSQL.Update("cart").
			Set("status", models.CartStatus.Completed).
			Set("is_current", false).
			Set("updated_at", time.Now()).
			Where("payment_id = ?", paymentID).
			RunWith(instant.dao.Db.Db).Exec(); err != nil {
			return err
		}

		if _, err := instant.packageDao.Db.PSQL.Update("service_orders").
			Set("status", models.EtsOrderStatusPaid).
			Set("order_time", time.Now()).
			Where("payment_id = ?", paymentID).RunWith(instant.dao.Db.Db).Exec(); err != nil {
			return err
		}
	}

	if paymentStatus == models.PaymentStatusClosed {
		if _, err := instant.packageDao.Db.PSQL.Update("cart").
			Set("status", models.CartStatus.Completed).
			Set("is_current", false).
			Set("updated_at", time.Now()).
			Where("payment_id = ?", paymentID).
			RunWith(instant.dao.Db.Db).Exec(); err != nil {
			return err
		}

		if _, err := instant.packageDao.Db.PSQL.Update("service_orders").
			Set("status", models.EtsOrderStatusOpen).
			Set("order_time", time.Now()).
			Where("payment_id = ?", paymentID).RunWith(instant.dao.Db.Db).Exec(); err != nil {
			return err
		}
	}

	return nil
}
