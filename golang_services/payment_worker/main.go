package main

import (
	"log"
	"os"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	"github.com/urfave/cli"
)

func main() {
	app := cli.NewApp()
	app.Name = "aria-payment-worker"
	app.Usage = "Payment worker service for Aria"
	app.Commands = []cli.Command{
		serviceCmd(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}
}

func serviceCmd() cli.Command {
	return cli.Command{
		Name:      "service",
		ShortName: "svc",
		Usage:     "Start search service for Aria",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:     "conf-file",
				Usage:    "Configuration file for payment service",
				EnvVar:   "CONF_FILE",
				Required: true,
				Value:    "conf/payments.yaml",
			},
			cli.BoolFlag{
				Name:   "use-wechat-pay-prod",
				Usage:  "Use WechatPay prod API for payment service, default to false",
				EnvVar: "USE_WECHAT_PAY_PROD",
			},
			cli.Bool<PERSON>lag{
				Name:   "use-authorize-prod",
				Usage:  "Use Authorize prod API for payment service, default to false",
				EnvVar: "USE_AUTHORIZE_PROD",
			},
			cli.StringFlag{
				Name:  "db-config",
				Usage: "Env for db config",
			},
			cli.StringFlag{
				Name:     "ad_wechatpay",
				Usage:    "Env for wechatpay config",
				EnvVar:   "ad_wechatpay",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_authorize",
				Usage:    "Env for authorize config",
				EnvVar:   "ad_authorize",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_onepay",
				Usage:    "Env for onepay config",
				EnvVar:   "ad_onepay",
				Required: true,
			},
			flags.LogLevelFlag,
		},
		Action: startPaymentWorkerService(),
	}
}
