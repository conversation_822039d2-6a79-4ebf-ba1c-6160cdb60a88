package main

import (
	"time"

	"bitbucket.org/persistence17/aria/golang_services/sdk/time_util"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/payments"
	"github.com/aws/aws-sdk-go/aws"
)

// JobVerifyAuthorizeWebhook job verify the authorize webhook alway active
func (instant *PaymentWorkerInstant) JobVerifyAuthorizeWebhook() error {
	return instant.AuthorizeClient.FindAndActiveWebhook()
}

// JobVerifyAuthorizeNet job verify authorize net
func (instant *PaymentWorkerInstant) JobVerifyAuthorizeNet() error {
	// Get newest batch : 1 batch max range 1 day
	batchList, err := instant.AuthorizeClient.GetSettledBatchList(payments.GetSettledBatchListRequest{
		GetSettledBatchList: payments.GetSettledBatchList{
			MerchantAuthentication: instant.AuthorizeClient.GetAuthentication(),
			IncludeStatistics:      "true",
			FirstSettlementDate:    time.Now().UTC().AddDate(0, 0, -1),
			LastSettlementDate:     time.Now().UTC(),
		},
	})
	if err != nil {
		return err
	}

	var newestBatchID string
	if index := len(batchList.BatchList); index > 0 {
		newestBatchID = batchList.BatchList[index-1].BatchID
	}

	transationList, err := instant.AuthorizeClient.GetAllTransactionList(payments.GetTransactionListRequest{
		GetTransactionList: payments.GetTransactionList{
			MerchantAuthentication: instant.AuthorizeClient.GetAuthentication(),
			BatchID:                newestBatchID,
			Sorting: &payments.Sorting{
				OrderBy:         "submitTimeUTC",
				OrderDescending: "true",
			},
		},
	})

	if err != nil {
		return err
	}

	for _, transaction := range transationList.Transactions {
		transactionFullDetail, err := instant.AuthorizeClient.GetTransactionFullDetail(payments.GetTransaction{
			GetTransactionDetailsRequest: payments.GetTransactionDetailsRequest{
				MerchantAuthentication: instant.AuthorizeClient.GetAuthentication(),
				TransId:                transaction.TransID,
			},
		})
		if err != nil {
			return err
		}

		payment, err := instant.dao.GetPayment(transaction.Invoice)
		if err != nil {
			return err
		}

		if payment == nil {
			continue
		}

		if !utils.Contain(payment.Status, []string{models.PaymentStatusOpen, models.PaymentStatusPending}) {
			continue
		}

		var paymentStatus string
		switch transactionFullDetail.TransactionStatus {
		case "settledSuccessfully", "capturedPendingSettlement":
			paymentStatus = models.PaymentStatusSuccess
		case "failedReview", "settlementError":
			paymentStatus = models.PaymentStatusFailed
		default:
			paymentStatus = models.PaymentStatusPending
		}

		if paymentStatus != payment.Status {
			payment.Status = paymentStatus
			payment.ExternalTransactionID = transaction.TransID
			if err := instant.dao.UpdatePayment(payment); err != nil {
				return err
			}

			if err := instant.UpdateCartStatusByPayment(payment.ID, paymentStatus); err != nil {
				return err
			}
		}

	}
	return nil
}

// JobVerifyWechatPay job verify wechat pay
func (instant *PaymentWorkerInstant) JobVerifyWechatPay() error {
	// Ensure the wechat client transaction
	paymentList, err := instant.dao.GetPaymentList(models.PaymentListRequest{
		PaymentMethod: []string{payments.WechatPay},
		Status:        []string{models.PaymentStatusPending, models.PaymentStatusOpen},
		FromDate:      aws.Time(time.Now().AddDate(0, 0, -5)), // 5 days nearest
		ToDate:        aws.Time(time.Now()),
		GetAllItems:   true,
	})

	if err != nil {
		return err
	}

	for _, ariaOrder := range paymentList {
		vendorOrder, err := instant.WechatClient.GetOrderInfo("", ariaOrder.ID)
		if err != nil {
			return err
		}

		if vendorOrder == nil {
			continue
		}

		if !utils.Contain(vendorOrder.Status, []string{models.PaymentStatusSuccess, models.PaymentStatusFailed, models.PaymentStatusClosed}) {
			continue
		}

		if vendorOrder.Status != ariaOrder.Status {
			ariaOrder.Status = vendorOrder.Status
			if err := instant.dao.UpdatePayment(&ariaOrder); err != nil {
				return err
			}

			if err := instant.UpdateCartStatusByPayment(ariaOrder.ID, vendorOrder.Status); err != nil {
				return err
			}
		}
	}
	return nil
}

// JobVerifyOnePay job verify onepay
func (instant *PaymentWorkerInstant) JobVerifyOnePay() error {
	// Ensure the onepay client transaction
	paymentList, err := instant.dao.GetPaymentList(models.PaymentListRequest{
		PaymentMethod: []string{payments.OnePay},
		Status:        []string{models.PaymentStatusPending, models.PaymentStatusOpen},
		FromDate:      aws.Time(time.Now().AddDate(0, 0, -5)), // 5 days nearest
		ToDate:        aws.Time(time.Now()),
		GetAllItems:   true,
	})

	if err != nil {
		return err
	}

	for _, ariaOrder := range paymentList {
		vendorOrder, err := instant.OnePayClient.GetOrderInfo("", ariaOrder.ID)
		if err != nil {
			return err
		}

		if vendorOrder == nil {
			continue
		}

		if !utils.Contain(vendorOrder.Status, []string{models.PaymentStatusSuccess, models.PaymentStatusFailed, models.PaymentStatusClosed}) {
			continue
		}

		if vendorOrder.Status != ariaOrder.Status {
			ariaOrder.Status = vendorOrder.Status
			if err := instant.dao.UpdatePayment(&ariaOrder); err != nil {
				return err
			}

			if err := instant.UpdateCartStatusByPayment(ariaOrder.ID, vendorOrder.Status); err != nil {
				return err
			}
		}
	}
	return nil
}

// MigrationDataOnly update cart status by payment status
func (instant *PaymentWorkerInstant) MigrationDataOnly() error {
	{
		rows, err := instant.packageDao.Db.PSQL.Select(`p.id, order_time, COALESCE(vp.attributes->>'processing_time','')`).From("package p").
			LeftJoin("visa_product vp ON vp.id = p.visa_product_id").
			Where("p.payment_info IS NOT NULL").
			Where("p.order_time IS NOT NULL").
			RunWith(instant.dao.Db.Db).Query()
		if err != nil {
			return err
		}
		defer rows.Close()

		for rows.Next() {
			var (
				packageID      int
				orderTime      time.Time
				processingTime string
			)
			if err := rows.Scan(&packageID, &orderTime, &processingTime); err != nil {
				return err
			}

			process, err := time_util.CalculateProcessingTimeWorkingDays(orderTime, processingTime, "")
			if err != nil {
				return err
			}

			if _, err := instant.packageDao.Db.PSQL.Update("package").
				Set("processing_time_expired_at", process).
				Where("id = ?", packageID).RunWith(instant.dao.Db.Db).Exec(); err != nil {
				return err
			}
		}
	}

	{
		rows, err := instant.packageDao.Db.PSQL.Select(`so.id, so.order_time, COALESCE(ets.attributes->>'processing_time','')`).From("service_orders so").
			LeftJoin("ets ON ets.id = so.service_id").
			Where("so.payment_id IS NOT NULL").
			Where("so.order_time IS NOT NULL").
			RunWith(instant.dao.Db.Db).Query()
		if err != nil {
			return err
		}
		defer rows.Close()

		for rows.Next() {
			var (
				orderID        int
				orderTime      time.Time
				processingTime string
			)
			if err := rows.Scan(&orderID, &orderTime, &processingTime); err != nil {
				return err
			}

			process, err := time_util.CalculateProcessingTimeWorkingDays(orderTime, processingTime, "")
			if err != nil {
				return err
			}

			if _, err := instant.packageDao.Db.PSQL.Update("service_orders").
				Set("processing_time_expired_at", process).
				Where("id = ?", orderID).RunWith(instant.dao.Db.Db).Exec(); err != nil {
				return err
			}
		}
	}

	return nil
}
