package main

import (
	"errors"
	"fmt"

	"bitbucket.org/persistence17/aria/golang_services/handlers/internals"
	internal_v1 "bitbucket.org/persistence17/aria/golang_services/handlers/internals/v1"
	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/urfave/cli"
)

func startInternalService() cli.ActionFunc {
	return func(c *cli.Context) error {
		port := c.String("port")

		e, err := newInternalServer(c)
		if err != nil {
			return err
		}

		return e.Run(":" + port)
	}
}

func newInternalServer(c *cli.Context) (*gin.Engine, error) {
	e := gin.New()
	e.Use(logger.LoggerMW(), logger.CustomLoggerMW(), gzip.Gzip(gzip.DefaultCompression))
	logger.InitGraylogLogger()
	e.Use(logger.GraylogLoggerMiddleware(logger.DefaultRequestLoggerConfig()))

	awsUSConfig := utils.GetMapEnv("ad_aws")
	region := awsUSConfig["region"].(string)
	if region == "" {
		return nil, fmt.Errorf("missing region in ad_aws")
	}

	awsConfig := aws.NewConfig().WithRegion(region).WithLogLevel(aws.LogOff)

	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return nil, err
	}

	// sqs
	_, err = loadInternalRelatedQueues(utils.GetMapEnv("ad_sqs"), e, sess)
	if err != nil {
		return nil, err
	}

	// cors
	corsConf := cors.DefaultConfig()
	corsConf.AddAllowHeaders("x-access-token", "Language")
	corsConf.AllowAllOrigins = true

	e.Use(
		cors.New(corsConf),
	)
	internals.LoadV1InternalsHandlers(e)
	return e, nil
}

func loadInternalRelatedQueues(conf map[string]any, r gin.IRouter, sess *session.Session) (gin.IRouter, error) {
	prefix := conf["url_prefix"].(string)
	notificationQueue := prefix + "/" + conf["notification_sqs_name"].(string)
	if notificationQueue == "" {
		return nil, errors.New("missing notification-queue")
	}
	notificationQ := awslib.NewSQSClient(sess, notificationQueue)

	r.Use(middlewares.SimpleQueueMW(notificationQ, internal_v1.NotificationQueue))
	return r, nil
}
