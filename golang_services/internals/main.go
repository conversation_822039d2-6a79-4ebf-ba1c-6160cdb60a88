package main

import (
	"log"
	"os"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	"github.com/urfave/cli"
)

func main() {
	app := cli.NewApp()
	app.Name = "aria-internal"
	app.Usage = "internal service for Aria"
	app.Commands = []cli.Command{
		serviceCmd(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}
}

func serviceCmd() cli.Command {
	return cli.Command{
		Name:      "service",
		ShortName: "i",
		Usage:     "Start internal service for Aria",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:   "port",
				Usage:  "Port the server listens to",
				EnvVar: "PORT",
				Value:  "3000",
			},
			cli.StringFlag{
				Name:     "ad_sqs",
				Usage:    "Env for s3 config",
				EnvVar:   "ad_sqs",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_aws",
				Usage:    "Env for aws config",
				EnvVar:   "ad_aws",
				Required: true,
			},
			flags.LogLevelFlag,
		},
		Action: startInternalService(),
	}
}
