package sign

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"encoding/base64"
)

func Sha256WithRsa(content string, key *rsa.PrivateKey) (string, error) {
	h := sha256.New()
	_, err := h.Write([]byte(content))
	if err != nil {
		return "", err
	}
	d := h.Sum(nil)
	byt, err := rsa.SignPKCS1v15(rand.Reader, key, crypto.SHA256, d)
	if err != nil {
		return "", nil
	}
	encodedSig := base64.StdEncoding.EncodeToString(byt)
	return encodedSig, nil
}

func VerifyBySha256WithRsa(signature, content string, key *rsa.PublicKey) (bool, error) {
	sigByt, err := base64.StdEncoding.DecodeString(signature)
	if err != nil {
		return false, err
	}
	h := sha256.New()
	h.Write([]byte(content))
	d := h.Sum(nil)
	err = rsa.VerifyPKCS1v15(key, crypto.SHA256, d, sigByt)
	if err != nil {
		return true, nil
	} else {
		return false, err
	}
}
