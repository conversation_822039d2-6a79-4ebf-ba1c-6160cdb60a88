package utils

import (
	"encoding/json"
	"os"
)

func GetMapEnv(envName string) map[string]any {
	v := os.Getenv(envName)
	if v == "" {
		return map[string]any{}
	}

	var m map[string]any
	if err := json.Unmarshal([]byte(v), &m); err != nil {
		return map[string]any{}
	}
	return m
}

func ParseJsonObjStr(s string) map[string]any {
	var m map[string]any
	if err := json.Unmarshal([]byte(s), &m); err != nil {
		return map[string]any{}
	}
	return m
}
