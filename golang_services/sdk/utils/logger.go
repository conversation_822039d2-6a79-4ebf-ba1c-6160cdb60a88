package utils

import (
	"fmt"
	"os"

	"github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
)

type LOG_INFO string

const (
	SUCCESS LOG_INFO = "SUCCESS"
	ERROR   LOG_INFO = "ERROR"
	WARNING LOG_INFO = "WARNING"
)

func LogOrderEvent(info LOG_INFO, serviceType string, orderID any, event string, message any) {
	fmt.Printf("%s: %s: %s: %s: %v\n", info, serviceType, orderID, event, message)
	baseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("api_base_url").String()

	// TODO: Send SQS or HTTP request to log server
	if _, err := resty.New().R().SetBody(map[string]any{
		"log_type":     string(info),
		"service_type": serviceType,
		"order_id":     orderID,
		"event":        event,
		"message":      message,
	}).Post(baseURL + "/v1/logger/order_events"); err != nil {
		fmt.Println(err)
	}
}
