package utils

import (
	"strings"

	"golang.org/x/text/language"
	"golang.org/x/text/message"
)

func ParseStringToMap(s string) map[string]bool {
	if s == "" {
		return map[string]bool{}
	}
	parts := strings.Split(s, ",")
	res := map[string]bool{}
	for _, p := range parts {
		val := strings.TrimSpace(p)
		res[val] = true
	}
	return res
}

func ParseFloatToCurrency(f float64) string {
	p := message.NewPrinter(language.English)
	currency := p.Sprintf("%.2f", f)
	return currency
}
