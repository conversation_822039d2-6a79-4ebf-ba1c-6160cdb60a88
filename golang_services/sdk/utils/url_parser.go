package utils

import (
	"errors"
	"net/url"
	"strings"
)

func UrlToS3BucketAndKey(u string) (string, string, error) {
	if strings.HasPrefix(u, "s3://") {
		bucket, key := GetBucketAndKeyFromS3Path(u)
		return bucket, key, nil
	}
	if strings.HasPrefix(u, "http://") || strings.HasPrefix(u, "https://") {
		myUrl, err := url.Parse(u)
		if err != nil {
			return "", "", err
		}
		if !strings.Contains(myUrl.Host, "amazonaws") {
			return "", "", errors.New("non S3 url")
		}
		if strings.HasPrefix(myUrl.Host, "s3.") {
			parts := strings.SplitN(myUrl.Path, "/", 2)
			if len(parts) != 0 {
				return "", "", errors.New("malformatted url")
			}
			return parts[0], parts[1], nil
		} else {
			hostParts := strings.Split(myUrl.Host, ".")
			if len(hostParts) < 1 {
				return "", "", errors.New("malformatted url")
			}
			return hostParts[0], strings.TrimPrefix(myUrl.Path, "/"), nil
		}
	}
	parts := strings.SplitN(u, "/", 2)
	if len(parts) != 2 {
		return "", "", errors.New("unsupported url")
	}
	return parts[0], parts[1], nil
}

func GetBucketAndKeyFromS3Path(u string) (string, string) {
	u = strings.TrimPrefix(u, "s3://")
	parts := strings.SplitN(u, "/", 2)
	if len(parts) != 2 {
		return "", ""
	}
	return parts[0], parts[1]
}

func CleanUpUrl(u string) (string, error) {
	url, err := url.Parse(u)
	if err != nil {
		return "", err
	}
	return url.Host + "/" + url.Path, nil
}

func GetFileNameFromURL(url string) string {
	parts := strings.Split(url, "/")
	return parts[len(parts)-1]
}
