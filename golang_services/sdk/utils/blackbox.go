package utils

import (
	"bytes"
	"fmt"
	"log"

	"github.com/go-resty/resty/v2"
	uuid "github.com/satori/go.uuid"
	"golang.org/x/exp/rand"
)

func UrlToBuff(url string) ([]byte, error) {
	resp, err := resty.New().R().Get(url)
	if err != nil {
		return nil, err
	}
	return resp.Body(), nil
}

func PdfToText(buff []byte) string {
	url := "https://www.blackbox.ai/api/upload"

	userID := uuid.NewV4().String()

	resp, err := resty.New().R().
		SetFileReader("image", uuid.NewV4().String()+".pdf", bytes.NewReader(buff)).
		SetFormData(map[string]string{
			"fileName": uuid.NewV4().String() + ".pdf",
			"userId":   userID,
		}).
		Post(url)
	if err != nil {
		log.Fatal(err)
	}
	result := string(resp.Body())
	fmt.Println(result)
	return result
}

func ImageToText(buff []byte) string {
	url := "https://www.blackbox.ai/api/upload"

	userID := uuid.NewV4().String()

	resp, err := resty.New().R().
		SetFileReader("image", uuid.NewV4().String()+".jpg", bytes.NewReader(buff)).
		SetFormData(map[string]string{
			"fileName": uuid.NewV4().String() + ".jpg",
			"userId":   userID,
		}).
		Post(url)
	if err != nil {
		log.Fatal(err)
	}
	result := string(resp.Body())
	fmt.Println(result)
	return result
}

func RandomPassword() string {
	var letters = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")
	b := make([]rune, 8)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}
