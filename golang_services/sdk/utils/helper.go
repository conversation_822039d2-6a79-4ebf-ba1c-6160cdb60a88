package utils

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"reflect"
	"sort"
	"strings"

	"github.com/landoop/tableprinter"
)

// IsZero check zero value
func IsZero(x any) bool {
	return x == reflect.Zero(reflect.TypeOf(x)).Interface()
}

// Equal check equal value of 2 elements
func Equal(src, desc any) bool {
	if src == nil || desc == nil {
		return src == desc
	}
	return reflect.DeepEqual(src, desc)

}

// Contain check contain
// ex: .Contain("a", []string{"a","b"}) => true
func Contain(str any, list any) bool {
	inValue := reflect.ValueOf(list)
	elemValue := reflect.ValueOf(str)
	inType := inValue.Type()

	switch inType.Kind() {
	case reflect.String:
		return strings.Contains(inValue.String(), elemValue.String())
	case reflect.Map:
		for _, key := range inValue.MapKeys() {
			if Equal(key.Interface(), str) {
				return true
			}
		}
	case reflect.Slice, reflect.Array:
		for i := 0; i < inValue.Len(); i++ {
			if Equal(inValue.Index(i).Interface(), str) {
				return true
			}
		}
	}

	return false
}

// IfThenElse evaluates a condition, if true returns the first parameter otherwise the second
func IfThenElse[T comparable](condition bool, a T, b T) T {
	if condition {
		return a
	}
	return b
}

// StructToTableString struct with table, need header flag
func StructToTableString(list any) string {
	var buff = bytes.Buffer{}
	printer := tableprinter.New(&buff)

	printer.Print(list)

	return string(buff.Bytes())
}

// GetJSONKeys get keys of a json
func GetJSONKeys(b []byte) ([]string, error) {
	dec := json.NewDecoder(bytes.NewBuffer(b))
	kmap := make(map[string]struct{})
	var key bool
	parents := make([]bool, 0, 10)
	for {
		t, err := dec.Token()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, err
		}
		del, ok := t.(json.Delim)
		if ok {
			if del == '{' {
				parents = append(parents, true)
			}
			if del == '[' {
				parents = append(parents, false)
			}
			if del == '}' || del == ']' {
				if len(parents) == 0 {
					return nil, errors.New("bad json: unexpected } or ] delim")
				}
				parents = parents[:len(parents)-1]
			}
			if len(parents) > 0 && parents[len(parents)-1] {
				key = true
			} else {
				key = false
			}
			continue
		}
		if key {
			str, ok := t.(string)
			if !ok {
				return nil, errors.New("bad json: keys must be strings")
			}
			kmap[str] = struct{}{}
			key = false
		} else if len(parents) > 0 && parents[len(parents)-1] {
			key = true
		}
	}
	ret := make([]string, len(kmap))
	var i int
	for k := range kmap {
		ret[i] = k
		i++
	}
	sort.Strings(ret)
	return ret, nil
}

func ContainsAllString(s []string, v ...string) bool {
	if len(v) == 0 {
		return true
	}
	if len(s) == 0 && len(v) != 0 {
		return false
	}
	m := map[string]bool{}
	for _, ss := range s {
		m[ss] = true
	}
	for _, vv := range v {
		if !m[vv] {
			return false
		}
	}
	return true
}
