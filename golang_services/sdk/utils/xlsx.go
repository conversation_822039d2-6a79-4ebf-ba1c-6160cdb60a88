package utils

import (
	"encoding/csv"
	"fmt"
	"io"
	"mime/multipart"

	"github.com/extrame/xls"
	"github.com/tealeg/xlsx/v3"
)

func ReadSheetRecords(file multipart.File, header *multipart.FileHeader) ([][]string, error) {
	var (
		records [][]string
		err     error
	)

	// Reference: https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types

	switch header.Header.Get("Content-Type") {
	case "text/csv": // Support .csv extension
		reader := csv.NewReader(file)
		records, err = reader.ReadAll()
		if err != nil && err != io.EOF {
			return records, err
		}
	case "application/vnd.ms-excel": // Suport .xls extension
		wb, err := xls.OpenReader(file, "utf-8")
		if err != nil {
			return nil, err
		}
		sh := wb.GetSheet(0)
		for i := 0; i < int(sh.MaxRow); i++ {
			row := sh.Row(i)
			record := []string{}
			for j := row.FirstCol(); j < row.LastCol(); j++ {
				record = append(record, row.Col(j))
			}
			records = append(records, record)
		}

	case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": // Suport .xlsx extension
		wb, err := xlsx.OpenReaderAt(file, header.Size)
		if err != nil {
			return nil, err
		}
		sh := wb.Sheets[0]

		fmt.Println("Max row is", sh.MaxRow)
		_ = sh.ForEachRow(func(r *xlsx.Row) error {
			var row []string
			err := r.ForEachCell(func(c *xlsx.Cell) error {
				value, _ := c.FormattedValue()
				row = append(row, value)
				return err
			})
			if row != nil {
				records = append(records, row)
			}
			return err
		})
	default:
		return nil, fmt.Errorf("Unsupported file format")
	}

	return records, nil
}
