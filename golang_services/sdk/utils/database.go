package utils

import (
	"fmt"
	"regexp"
	"strings"
)

// QueryCountItems get total item without limit and offset
func QueryCountItems(query string, syntax ...string) string {
	query = regexp.MustCompile(`^SELECT(.|\s)+FROM`).ReplaceAllString(query, fmt.Sprintf("SELECT 1 %s FROM", strings.Join(syntax, " ")))
	query = fmt.Sprintf("SELECT COUNT(*) FROM ( %s ) result", regexp.MustCompile(`((LIMIT)|(OFFSET))(.|\s)+`).ReplaceAllString(query, ""))
	return query
}

// QueryCountItemV2 get total item without limit and offset
// Use this when have multi SELECT statement in query
func QueryCountItemV2(query string) string {
	query = fmt.Sprintf("SELECT COUNT(*) FROM ( %s ) result", regexp.MustCompile(`((LIMIT)|(OFFSET))(.|\s)+`).ReplaceAllString(query, ""))
	return query
}
