package utils

import (
	"errors"
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
)

const (
	UserIDKey = "user-id"
)

// GetOrgIDAndUserID returns (orgID, userID, hasOrgID)
// orgID comes from request path. If orgID does not exist in the path, this func returns a 0 orgID and hasOrgID=false
// userID comes with the token
func GetOrgIDAndUserID(c *gin.Context) (int, string, bool) {
	hasOrgID := true
	orgID, err := GetIntPathParam(c, "org-id")
	if err != nil {
		hasOrgID = false
	}
	v := c.Value(UserIDKey)
	if v == nil {
		return orgID, "", hasOrgID
	}
	userID, ok := v.(string)
	if !ok {
		return orgID, "", hasOrgID
	}
	return orgID, userID, hasOrgID
}

func GetIntPathParam(c *gin.Context, param string) (int, error) {
	v := c.Param(param)
	if v == "" {
		return 0, errors.New(fmt.Sprintf("missing path param %s", param))
	}
	n, err := strconv.ParseInt(v, 10, 64)
	if err != nil {
		return 0, err
	}
	return int(n), nil
}

func GetStringPathParam(c *gin.Context, param string) (string, error) {
	v := c.Param(param)
	if v == "" {
		return "", fmt.Errorf("missing path param %s", param)
	}
	return v, nil
}
