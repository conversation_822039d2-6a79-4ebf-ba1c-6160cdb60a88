package utils

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"time"
)

var MAX_TRY = 5

func GenerateXlsxFromTemplate(baseAPI, template string, data []byte) ([]byte, error) {
	fmt.Println(string(data))
	req := &bytes.Buffer{}
	req.Write(data)
	for i := 0; i < MAX_TRY; i++ {
		response, err := http.Post(baseAPI+"/v1/helper-service/xlsx?template="+template, "application/json", req)
		if err != nil {
			return nil, err
		}
		if response.StatusCode == 200 {
			return io.ReadAll(response.Body)
		}
		time.Sleep(1 * time.Second)
	}
	return nil, fmt.Errorf("GENERATE_XLSX_SERVER_FAIL")
}
