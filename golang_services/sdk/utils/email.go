package utils

import (
	"fmt"
	"os"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/tidwall/gjson"
)

type SendEmailRequest struct {
	TemplateName string           `json:"template_name"`
	To           string           `json:"to"`
	CC           []string         `json:"cc"`
	BCC          []string         `json:"bcc"`
	Parameters   map[string]any   `json:"parameters"`
	Attachments  []map[string]any `json:"attachments"`
}

func SendEmail(req SendEmailRequest) error {
	awsJ := gjson.ParseBytes([]byte(os.Getenv("ad_aws")))
	sqsJ := gjson.ParseBytes([]byte(os.Getenv("ad_sqs")))

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(awsJ.Get("region").String())},
	)
	if err != nil {
		return err
	}
	svc := sqs.New(sess)

	message := StructToJSON(req).String()

	qURL := sqsJ.Get("url_prefix").String() + "/" + sqsJ.Get("notification_sqs_name").String()
	result, err := svc.SendMessage(&sqs.SendMessageInput{
		MessageBody: &message,
		QueueUrl:    &qURL,
	})

	fmt.Println(result)
	return err
}
