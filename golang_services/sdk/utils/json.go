package utils

import (
	"encoding/json"
	"reflect"
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/sdk/types"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

func StructToJSON(src any) gjson.Result {
	buff, err := json.Marshal(src)
	if err != nil {
		return gjson.Parse("null")
	}
	return gjson.ParseBytes(buff)
}

func SetJSONPath(src gjson.Result, path string, value any) gjson.Result {
	newValue, _ := sjson.Set(src.String(), path, value)
	return gjson.Parse(newValue)
}

func GetJSONPathDiff(src, desc string) ([]string, []string) {
	var srcAdds, srcRevs []string

	var srcPaths = types.GetJSONPaths(src)
	var descPaths = types.GetJSONPaths(desc)

	for _, path := range descPaths {
		if !Contain(path, srcPaths) || gjson.Get(src, path).Type != gjson.Get(desc, path).Type {
			srcAdds = append(srcAdds, path)
			continue
		}
		if (gjson.Get(src, path).IsObject() && gjson.Get(desc, path).IsArray()) || (gjson.Get(src, path).IsArray() && gjson.Get(desc, path).IsObject()) {
			srcAdds = append(srcAdds, path)
		}
	}

	for _, path := range srcPaths {
		if !Contain(path, descPaths) || gjson.Get(src, path).Type != gjson.Get(desc, path).Type {
			srcRevs = append(srcRevs, path)
		}
		if (gjson.Get(src, path).IsObject() && gjson.Get(desc, path).IsArray()) || (gjson.Get(src, path).IsArray() && gjson.Get(desc, path).IsObject()) {
			srcRevs = append(srcRevs, path)
		}
	}

	return srcAdds, srcRevs
}

func MergeJSON(defaultJSON, changedJSON json.RawMessage) string {
	var defaultJSONDecoded map[string]any
	defaultJSONUnmarshalErr := json.Unmarshal(defaultJSON, &defaultJSONDecoded)
	if defaultJSONUnmarshalErr != nil {
		panic("Error unmarshalling first JSON")
	}
	var changedJSONDecoded map[string]any
	changedJSONUnmarshalErr := json.Unmarshal(changedJSON, &changedJSONDecoded)
	if changedJSONUnmarshalErr != nil {
		panic("Error unmarshalling second JSON")
	}
	for key := range defaultJSONDecoded {
		checkKeyBeforeMerging(key, defaultJSONDecoded[key], changedJSONDecoded[key], changedJSONDecoded)
	}
	mergedJSON, mergedJSONErr := json.Marshal(changedJSONDecoded)
	if mergedJSONErr != nil {
		panic("Error marshalling merging JSON")
	}
	return string(mergedJSON)
}

func checkKeyBeforeMerging(key string, defaultMap any, changedMap any, finalMap map[string]any) {
	if !reflect.DeepEqual(defaultMap, changedMap) {
		switch defaultMap.(type) {
		case map[string]any:
			if changedMap == nil {
				finalMap[key] = defaultMap
			} else if _, ok := changedMap.(map[string]any); ok {
				defaultMapRef := defaultMap.(map[string]any)
				changedMapRef := changedMap.(map[string]any)
				for newKey := range defaultMapRef {
					checkKeyBeforeMerging(newKey, defaultMapRef[newKey], changedMapRef[newKey], finalMap)
				}
			}
		default:
			//Check if the value was set, otherwise set it
			if changedMap == nil {
				finalMap[key] = defaultMap
			}
		}
	}
}

func queryValueInJSONArray(src json.RawMessage, path []string, val string, limit, offset int, exactQuery bool) json.RawMessage {
	srcJ := gjson.ParseBytes(src)
	result := []string{}

	for _, item := range srcJ.Array() {
	P:
		for _, pathItem := range path {
			if strings.Contains(ParseVietnameseText(strings.ToLower(item.Get(pathItem).String())), ParseVietnameseText(strings.ToLower(val))) {
				result = append(result, item.Raw)
				break P
			}
		}
	}

	if offset+limit > len(result) {
		result = result[offset:]
	} else {
		result = result[offset : offset+limit]
	}

	return json.RawMessage("[" + strings.Join(result, ",") + "]")
}

func QueryValueInJSONArray(src json.RawMessage, path []string, limit, offset int, val string) json.RawMessage {
	return queryValueInJSONArray(src, path, val, limit, offset, false)
}

func ParseVietnameseText(src string) string {
	return strings.NewReplacer("À", "A", "Á", "A", "Â", "A", "Ã", "A", "È", "E", "É", "E", "Ê", "E", "Ì", "I", "Í", "I", "Ò", "O", "Ó", "O", "Ô", "O", "Õ", "O", "Ù", "U", "Ú", "U", "Ý", "Y", "à", "a", "á", "a", "â", "a", "ã", "a", "è", "e", "é", "e", "ê", "e", "ì", "i", "í", "i", "ò", "o", "ó", "o", "ô", "o", "õ", "o", "ù", "u", "ú", "u", "ý", "y", "Ă", "A", "ă", "a", "Đ", "D", "đ", "d", "Ĩ", "I", "ĩ", "i", "Ũ", "U", "ũ", "u", "Ơ", "O", "ơ", "o", "Ư", "U", "ư", "u", "Ạ", "A", "ạ", "a", "Ả", "A", "ả", "a", "Ấ", "A", "ấ", "a", "Ầ", "A", "ầ", "a", "Ẩ", "A", "ẩ", "a", "Ẫ", "A", "ẫ", "a", "Ậ", "A", "ậ", "a", "Ắ", "A", "ắ", "a", "Ằ", "A", "ằ", "a", "Ẳ", "A", "ẳ", "a", "Ẵ", "A", "ẵ", "a", "Ặ", "A", "ặ", "a", "Ẹ", "E", "ẹ", "e", "Ẻ", "E", "ẻ", "e", "Ẽ", "E", "ẽ", "e", "Ế", "E", "ế", "e", "Ề", "E", "ề", "e", "Ể", "E", "ể", "e", "Ễ", "E", "ễ", "e", "Ệ", "E", "ệ", "e", "Ỉ", "I", "ỉ", "i", "Ị", "I", "ị", "i", "Ọ", "O", "ọ", "o", "Ỏ", "O", "ỏ", "o", "Ố", "O", "ố", "o", "Ồ", "O", "ồ", "o", "Ổ", "O", "ổ", "o", "Ỗ", "O", "ỗ", "o", "Ộ", "O", "ộ", "o", "Ớ", "O", "ớ", "o", "Ờ", "O", "ờ", "o", "Ở", "O", "ở", "o", "Ỡ", "O", "ỡ", "o", "Ợ", "O", "ợ", "o", "Ụ", "U", "ụ", "u", "Ủ", "U", "ủ", "u", "Ứ", "U", "ứ", "u", "Ừ", "U", "ừ", "u", "Ử", "U", "ử", "u", "Ữ", "U", "ữ", "u", "Ự", "U", "ự", "u", "Ỳ", "Y", "ỳ", "y", "Ỵ", "Y", "ỵ", "y", "Ỷ", "Y", "ỷ", "y", "Ỹ", "Y", "ỹ", "y").Replace(src)
}

func GetStringOrValue(src any) string {
	if val := StructToJSON(src).Get("value").String(); val != "" {
		return val
	}
	return cast.ToString(src)
}

func GetStringOrText(src any) string {
	if val := StructToJSON(src).Get("text").String(); val != "" {
		return val
	}
	return cast.ToString(src)
}
