package photo

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"time"

	"github.com/go-resty/resty/v2"
)

func RemoveImageBackground(buff []byte, imageSize string) ([]byte, error) {
	document := map[string]string{
		"2x2": "804",
		"4x6": "726",
		"3x4": "485",
	}

	client := resty.New()

	// Create form data
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	documentID := document[imageSize]
	if documentID == "" {
		documentID = "726"
	}

	_ = writer.WriteField("UploadForm[documentId]", documentID)
	_ = writer.WriteField("UploadForm[language]", "vi-VN")
	_ = writer.WriteField("UploadForm[appName]", "2")

	part, err := writer.CreateFormFile("UploadForm[imageFile]", "file.png")
	if err != nil {
		return nil, err
	}
	_, err = io.Copy(part, bytes.NewReader(buff))
	if err != nil {
		return nil, err
	}
	err = writer.Close()
	if err != nil {
		return nil, err
	}

	fmt.Println("Start", time.Now())

	var result map[string]any
	// Step 1: Upload image
	resp, err := client.R().
		SetHeader("Content-Type", writer.FormDataContentType()).
		SetHeader("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36").
		SetBody(body).
		SetResult(&result).
		Post("https://api.photoaid.com/api/upload?web=1")

	if err != nil {
		return nil, err
	}

	photoKey, ok := result["photoKey"].(string)
	if !ok {
		return nil, fmt.Errorf("photoKey not found in response")
	}

	// Step 2: Get processed image
	step1URL := "https://photoaid.com/get-photo/result?photoKey=" + photoKey
	resp, err = client.R().
		SetDoNotParseResponse(true).
		Get(step1URL)

	if err != nil {
		return nil, err
	}

	return io.ReadAll(resp.RawBody())
}
