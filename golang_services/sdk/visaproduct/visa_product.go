package visaproduct

import (
	"sort"
)

const (
	FormOfflineType       = "offline"
	FormOnlineType        = "online"
	FormOnlineCaptchaType = "online-captcha"
	FormWebsite           = "website"
)

func SortStayDuration(sd []string) []string {
	orderMap := map[string]int{
		"1M": 1,
		"2M": 2,
		"3M": 3,
		"4M": 4,
		"5M": 5,
		"6M": 6,
		"1y": 12,
		"2y": 24,
		"5y": 60,
	}
	sort.SliceStable(sd, func(i, j int) bool {
		s1, s2 := sd[i], sd[j]
		if len(s1) == 0 {
			return true
		}
		if len(s2) == 0 {
			return false
		}
		unit1, ok1 := orderMap[s1]
		unit2, ok2 := orderMap[s2]
		if !ok1 && ok2 {
			return false
		}
		if !ok2 && ok2 {
			return true
		}
		if !ok1 && !ok2 {
			return true
		}
		return unit1 < unit2
	})
	return sd
}

func SortProcessTime(pt []string) []string {
	orderMap := map[string]int{
		"3d":              1,
		"1d":              2,
		"4h":              3,
		"weekend&holiday": 4,
	}
	sort.SliceStable(pt, func(i, j int) bool {
		s1, s2 := pt[i], pt[j]
		if len(s1) == 0 {
			return true
		}
		if len(s2) == 0 {
			return false
		}
		unit1, ok1 := orderMap[s1]
		unit2, ok2 := orderMap[s2]
		if !ok1 && ok2 {
			return false
		}
		if !ok2 && ok2 {
			return true
		}
		if !ok1 && !ok2 {
			return true
		}
		return unit1 < unit2
	})
	return pt
}

func SortNumberOfEntries(noe []string) []string {
	orderMap := map[string]int{
		"single_entry":   1,
		"multiple_entry": 2,
	}
	sort.SliceStable(noe, func(i, j int) bool {
		s1, s2 := noe[i], noe[j]
		if len(s1) == 0 {
			return true
		}
		if len(s2) == 0 {
			return false
		}
		unit1, ok1 := orderMap[s1]
		unit2, ok2 := orderMap[s2]
		if !ok1 && ok2 {
			return false
		}
		if !ok2 && ok2 {
			return true
		}
		if !ok1 && !ok2 {
			return true
		}
		return unit1 < unit2
	})
	return noe
}
