package visaproduct

import (
	"sort"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

func FilterAndSortVisaProductPods(pods []*models.Pod, cats, subCats, ids, names []string) []*models.Pod {
	catMap := map[string]bool{}
	hasCatFilter := false
	for _, c := range cats {
		catMap[c] = true
		hasCatFilter = true
	}
	subCatMap := map[string]bool{}
	hasSubCatFilter := false
	for _, c := range subCats {
		subCatMap[c] = true
		hasSubCatFilter = true
	}
	idMap := map[string]bool{}
	hasIdFilter := false
	for _, s := range ids {
		idMap[s] = true
		hasIdFilter = true
	}
	nameMap := map[string]bool{}
	hasNameFilter := false
	for _, n := range names {
		nameMap[n] = true
		hasNameFilter = true
	}

	var l []*models.Pod
	for _, p := range pods {
		if (hasCatFilter && !catMap[p.Category]) ||
			(hasSubCatFilter && !subCatMap[p.SubCategory]) ||
			(hasIdFilter && !idMap[p.ID]) ||
			(hasNameFilter && !nameMap[p.Name]) {
			continue
		}
		l = append(l, p)
	}

	sort.SliceStable(l, func(i, j int) bool {
		return l[i].Order < l[j].Order
	})
	return l
}
