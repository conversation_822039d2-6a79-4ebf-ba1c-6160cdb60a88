package fedex

import (
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

type AddressValidationReply struct {
	HighestSeverity string
	Notifications   []Notification
	Version         Version
	AddressResults  AddressResults
}

type Version struct {
	ServiceId    string
	Major        int
	Intermediate int
	Minor        int
}

type Notification struct {
	Severity         string
	Source           string
	Code             int
	Message          string
	LocalizedMessage string
}

type AddressResults struct {
	State            string
	Classification   string
	EffectiveAddress EffectiveAddress
}
type EffectiveAddress struct {
	StreetLines         []string
	City                string
	StateOrProvinceCode string
	PostalCode          string
	CountryCode         string
}

type ShipmentContact struct {
	City    string `json:"city"`
	Name    string `json:"name"`
	State   string `json:"state"`
	Phone   string `json:"phone"`
	Email   string `json:"email"`
	Address string `json:"address"`
	ZipCode string `json:"zip_code"`
	Country string `json:"country"`
}

type ProcessShipmentRequest struct {
	ShippingCarrier string          `json:"shipping_carrier"`
	ShipDate        time.Time       `json:"ship_date"`
	ServiceType     string          `json:"service_type"`
	Shipper         ShipmentContact `json:"shipper"`
	Recipient       ShipmentContact `json:"recipient"`
	Description     string          `json:"description"`
	PaymentType     string          `json:"payment_type"`
	ShippingContent string          `json:"shipping_content"`
}

type ProcessShipmentResponse struct {
	TrackingNumber string  `json:"tracking_number"`
	Currency       string  `json:"currency"`
	Price          float32 `json:"price"`
	Label          string  `json:"label"`
}

type Contact struct {
	PersonName   string         `json:"person_name"`
	CompanyName  string         `json:"company_name"`
	PhoneNumber  string         `json:"phone_number"`
	EMailAddress string         `json:"email_address"`
	Address      models.Address `json:"address"`
}

type ProcessShipmentReply struct {
	HighestSeverity         string
	Notifications           []Notification
	Version                 Version
	CompletedShipmentDetail CompletedShipmentDetail
}

type ShipmentReply struct {
	HighestSeverity string
	Notifications   []Notification
	Version         Version
}

type TrackReply struct {
	HighestSeverity       string
	Notifications         []Notification
	Version               Version
	CompletedTrackDetails CompletedTrackDetail
}
type CompletedShipmentDetail struct {
	CompletedPackageDetails CompletedPackageDetails
	ShipmentRating          ShipmentRating
}

type CompletedPackageDetails struct {
	Label       Label
	TrackingIds TrackingIds
}

type Label struct {
	Parts Parts
}

type Parts struct {
	Image string
}

type TrackingIds struct {
	TrackingNumber string
}

type ShipmentRating struct {
	ShipmentRateDetails ShipmentRateDetails
}

type ShipmentRateDetails struct {
	TotalNetCharge Charge
}

type Charge struct {
	Currency string
	Amount   float32
}

type CompletedTrackDetail struct {
	HighestSeverity  string
	Notifications    Notification
	DuplicateWaybill bool
	MoreData         bool
	TrackDetails     TrackDetail
}

type TrackDetail struct {
	TrackingNumber                         string
	TrackingNumberUniqueIdentifier         string
	Notification                           Notification
	StatusDetail                           StatusDetail
	CarrierCode                            string
	OperatingCompanyOrCarrierDescription   string
	Packaging                              string
	PackagingType                          string
	PackageSequenceNumber                  int
	PackageCount                           int
	ShipTimestamp                          string
	ActualDeliveryTimestamp                string
	DeliveryLocationType                   string
	DeliveryLocationDescription            string
	DeliveryAttempts                       int
	DeliverySignatureName                  string
	TotalUniqueAddressCountInConsolidation int
	NotificationEventsAvailable            string
	RedirectToHoldEligibility              string
	DatesOrTimes                           []DatesOrTime
	Service                                Service
}

type Service struct {
	Type        string
	Description string
}
type DatesOrTime struct {
	Type            string // SHIP	ACTUAL_TENDER ACTUAL_PICKUP ACTUAL_DELIVERY
	DateOrTimestamp string
}

type StatusDetail struct {
	CreationTime string
	Code         string
	Description  string
}

type SendNotificationsReply struct {
	HighestSeverity string
}
