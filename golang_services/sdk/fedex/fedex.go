package fedex

import (
	"bytes"
	"encoding/xml"
	"fmt"
	"html/template"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"gopkg.in/guregu/null.v3"
)

const (
	FormatDateTime = "2006-01-02T15:04:05"
	ISO2OfUS       = "US"
)

type FedexClient struct {
	URL      string
	Key      string
	Password string
	Account  string
	Meter    string
}

func NewFedex(conf map[string]any) (*FedexClient, error) {
	url, ok := conf["url"].(string)
	if !ok {
		return nil, fmt.Errorf("missing url in ad_fedex file")
	}
	key, ok := conf["key"].(string)
	if !ok {
		return nil, fmt.Errorf("missing key in ad_fedex file")
	}
	password, ok := conf["password"].(string)
	if !ok {
		return nil, fmt.Errorf("missing password in ad_fedex file")
	}
	account, ok := conf["account"].(string)
	if !ok {
		return nil, fmt.Errorf("missing account in ad_fedex file")
	}
	meter, ok := conf["meter"].(string)
	if !ok {
		return nil, fmt.Errorf("missing meter in ad_fedex file")
	}
	return NewFedexClient(url, key, password, account, meter), nil
}

func NewFedexClient(url, key, password, account, meter string) *FedexClient {
	return &FedexClient{URL: url, Key: key, Password: password, Account: account, Meter: meter}
}

func (f *FedexClient) DeleteShipment(trackingNumber string) error {
	data := map[string]string{
		"Key":            f.Key,
		"Password":       f.Password,
		"AccountNumber":  f.Account,
		"MeterNumber":    f.Meter,
		"ShipTimestamp":  time.Now().Format(FormatDateTime),
		"TrackingNumber": trackingNumber,
		"TrackingIdType": "FEDEX",
		"FormId":         "0201",
	}
	template, err := template.New("InputRequest").Parse(getDeleteShipTemplate())
	if err != nil {
		return err
	}
	// 2. send request to FedEx service by XML
	reqXML := &bytes.Buffer{}
	err = template.Execute(reqXML, data)
	if err != nil {
		return err
	}
	fmt.Println(reqXML.String())
	content, err := postXML(f.URL, reqXML.String())
	if err != nil {
		return err
	}
	fmt.Println(string(content))
	res, err := ParseShipmentReply(content)
	if err != nil {
		return err
	}

	if res.HighestSeverity == "FAILURE" || res.HighestSeverity == "ERROR" {
		return fmt.Errorf(res.Notifications[0].Message)
	}
	return nil
}

func (f *FedexClient) CreateShippingLabel(rep ProcessShipmentRequest) (*ProcessShipmentResponse, error) {
	// 1. prepare soap data
	data := map[string]string{
		"Key":                          f.Key,
		"Password":                     f.Password,
		"AccountNumber":                f.Account,
		"MeterNumber":                  f.Meter,
		"ShipTimestamp":                rep.ShipDate.Format(FormatDateTime),
		"ServiceType":                  rep.ServiceType,
		"ShipperPersonName":            rep.Shipper.Name,
		"ShipperCompanyName":           "",
		"ShipperPhoneNumber":           rep.Shipper.Phone,
		"ShipperEMailAddress":          rep.Shipper.Email,
		"ShipperStreetLines":           rep.Shipper.Address,
		"ShipperCity":                  rep.Shipper.City,
		"ShipperStateOrProvinceCode":   rep.Shipper.State,
		"ShipperPostalCode":            rep.Shipper.ZipCode,
		"ShipperCountryCode":           rep.Shipper.Country,
		"RecipientPersonName":          rep.Recipient.Name,
		"RecipientCompanyName":         "",
		"RecipientPhoneNumber":         rep.Recipient.Phone,
		"RecipientEMailAddress":        rep.Recipient.Email,
		"RecipientStreetLines":         rep.Recipient.Address,
		"RecipientCity":                rep.Recipient.City,
		"RecipientStateOrProvinceCode": rep.Recipient.State,
		"RecipientPostalCode":          rep.Recipient.ZipCode,
		"RecipientCountryCode":         rep.Recipient.Country,
		"Description":                  rep.Description,
		"PaymentType":                  rep.PaymentType,
	}
	template, err := template.New("InputRequest").Parse(getShipTemplate())
	if err != nil {
		return nil, err
	}
	// 2. send request to FedEx service by XML
	reqXML := &bytes.Buffer{}
	err = template.Execute(reqXML, data)
	if err != nil {
		return nil, err
	}
	fmt.Println(reqXML.String())
	content, err := postXML(f.URL, reqXML.String())
	if err != nil {
		return nil, err
	}
	fmt.Println(string(content))
	res, err := ParseProcessShipmentReply(content)
	if err != nil {
		return nil, err
	}

	if res.HighestSeverity == "FAILURE" || res.HighestSeverity == "ERROR" {
		return nil, fmt.Errorf(res.Notifications[0].Message)
	}
	// 3. return base64 pdf label
	result := ProcessShipmentResponse{
		Label:          res.CompletedShipmentDetail.CompletedPackageDetails.Label.Parts.Image,
		Price:          res.CompletedShipmentDetail.ShipmentRating.ShipmentRateDetails.TotalNetCharge.Amount,
		Currency:       res.CompletedShipmentDetail.ShipmentRating.ShipmentRateDetails.TotalNetCharge.Currency,
		TrackingNumber: res.CompletedShipmentDetail.CompletedPackageDetails.TrackingIds.TrackingNumber,
	}
	return &result, nil
}

func (f *FedexClient) ValidationAddress(ad models.Address) (*models.Address, error) {
	// 1. prepare soap data
	isResidential := false
	if ad.Type == models.AddressTypeResidential {
		isResidential = true
	}

	data := map[string]string{
		"Key":                 f.Key,
		"Password":            f.Password,
		"AccountNumber":       f.Account,
		"MeterNumber":         f.Meter,
		"StreetLines":         ad.Address,
		"City":                ad.City,
		"StateOrProvinceCode": ad.State,
		"PostalCode":          ad.ZipCode.String,
		"CountryCode":         ad.Country,
		"Residential":         strconv.FormatBool(isResidential),
	}
	template, err := template.New("InputRequest").Parse(getValidationAddressTemplate())
	if err != nil {
		return nil, err
	}
	// 2. send request to FedEx service by XML
	reqXML := &bytes.Buffer{}
	err = template.Execute(reqXML, data)
	if err != nil {
		return nil, err
	}

	fmt.Println(reqXML.String())
	content, err := postXML(f.URL, reqXML.String())
	if err != nil {
		return nil, err
	}
	fmt.Println(string(content))
	res, err := ParseAddressValidationReply(content)
	if err != nil {
		return nil, err
	}

	if res.HighestSeverity != "SUCCESS" {
		if res.AddressResults.Classification == "UNKNOWN" {
			return nil, errors.InvalidAddress
		} else {
			return nil, errors.CantValidateAddress
		}
	}

	effectiveAddress := models.Address{
		Address: strings.Join(res.AddressResults.EffectiveAddress.StreetLines, " "),
		City:    res.AddressResults.EffectiveAddress.City,
		State:   res.AddressResults.EffectiveAddress.StateOrProvinceCode,
		ZipCode: null.NewString(res.AddressResults.EffectiveAddress.PostalCode, true),
		Country: res.AddressResults.EffectiveAddress.CountryCode,
	}

	return &effectiveAddress, nil
}

func (f *FedexClient) TrackByNumber(trackingNumber string) (*TrackReply, error) {
	// 1. prepare soap data
	data := map[string]string{
		"Key":            f.Key,
		"Password":       f.Password,
		"AccountNumber":  f.Account,
		"MeterNumber":    f.Meter,
		"TrackingNumber": trackingNumber,
	}
	template, err := template.New("InputRequest").Parse(trackByNumberTemplate())
	if err != nil {
		return nil, err
	}
	// 2. send request to FedEx service by XML
	reqXML := &bytes.Buffer{}
	err = template.Execute(reqXML, data)
	if err != nil {
		return nil, err
	}
	content, err := postXML(f.URL, reqXML.String())
	if err != nil {
		return nil, err
	}
	res, err := ParseTrackReply(content)
	if err != nil {
		return nil, err
	}

	if res.HighestSeverity == "FAILURE" || res.HighestSeverity == "ERROR" {
		return nil, fmt.Errorf(res.Notifications[0].Message)
	}
	return &res, nil
}

func (f *FedexClient) RequestNotification(trackingNumber string, emails []string, senderEmail, message string) error {
	data := map[string]any{
		"Key":                f.Key,
		"Password":           f.Password,
		"AccountNumber":      f.Account,
		"MeterNumber":        f.Meter,
		"NotificationEmails": emails,
		"PersonalMessage":    message,
		"SenderEmail":        senderEmail,
		"TrackingNumber":     trackingNumber,
	}

	template, err := template.New("InputRequest").Parse(getRequestNotificationTemplate())
	if err != nil {
		return err
	}

	reqXML := &bytes.Buffer{}
	if err := template.Execute(reqXML, data); err != nil {
		return err
	}

	fmt.Println(reqXML.String())

	content, err := postXML(f.URL, reqXML.String())
	if err != nil {
		return err
	}
	fmt.Println(string(content))
	res, err := ParseNotificationRequest(content)
	if err != nil {
		return err
	}

	if res.HighestSeverity != "SUCCESS" {
		return fmt.Errorf("RequestNotification failure")
	}

	return nil
}

func ParseAddressValidationReply(xmlResp []byte) (reply AddressValidationReply, err error) {
	data := struct {
		Reply AddressValidationReply `xml:"Body>AddressValidationReply"`
	}{}
	err = xml.Unmarshal(xmlResp, &data)
	return data.Reply, err
}

func ParseProcessShipmentReply(xmlResp []byte) (reply ProcessShipmentReply, err error) {
	data := struct {
		Reply ProcessShipmentReply `xml:"Body>ProcessShipmentReply"`
	}{}
	err = xml.Unmarshal(xmlResp, &data)
	return data.Reply, err
}

func ParseShipmentReply(xmlResp []byte) (reply ShipmentReply, err error) {
	data := struct {
		Reply ShipmentReply `xml:"Body>ShipmentReply"`
	}{}
	err = xml.Unmarshal(xmlResp, &data)
	return data.Reply, err
}

func ParseTrackReply(xmlResp []byte) (reply TrackReply, err error) {
	data := struct {
		Reply TrackReply `xml:"Body>TrackReply"`
	}{}
	err = xml.Unmarshal(xmlResp, &data)
	return data.Reply, err
}

func ParseNotificationRequest(xmlResp []byte) (reply SendNotificationsReply, err error) {
	data := struct {
		SendNotificationsReply SendNotificationsReply `xml:"Body>SendNotificationsReply"`
	}{}
	err = xml.Unmarshal(xmlResp, &data)
	return data.SendNotificationsReply, err
}

func postXML(url string, xml string) (content []byte, err error) {
	resp, err := http.Post(url, "text/xml", strings.NewReader(xml))
	if err != nil {
		return content, err
	}
	defer resp.Body.Close()
	return ioutil.ReadAll(resp.Body)
}

func getValidationAddressTemplate() string {
	return `		
	<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:v4="http://fedex.com/ws/addressvalidation/v4">
		<soapenv:Header/>
		<soapenv:Body>
		   <v4:AddressValidationRequest>
			  <v4:WebAuthenticationDetail>
				 <v4:UserCredential>
					<v4:Key>{{.Key}}</v4:Key>
					<v4:Password>{{.Password}}</v4:Password>
				 </v4:UserCredential>
			  </v4:WebAuthenticationDetail>
			  <v4:ClientDetail>
				 <v4:AccountNumber>{{.AccountNumber}}</v4:AccountNumber>
				 <v4:MeterNumber>{{.MeterNumber}}</v4:MeterNumber>
				 <v4:Localization>
					<v4:LanguageCode>EN</v4:LanguageCode>
					<v4:LocaleCode>US</v4:LocaleCode>
				 </v4:Localization>
			  </v4:ClientDetail>
			  <v4:TransactionDetail>
				 <v4:CustomerTransactionId>AddressValidationRequest_v4</v4:CustomerTransactionId>
				 <v4:Localization>
					<v4:LanguageCode>EN</v4:LanguageCode>
					<v4:LocaleCode>US</v4:LocaleCode>
				 </v4:Localization>
			  </v4:TransactionDetail>
			  <v4:Version>
				 <v4:ServiceId>aval</v4:ServiceId>
				 <v4:Major>4</v4:Major>
				 <v4:Intermediate>0</v4:Intermediate>
				 <v4:Minor>0</v4:Minor>
			  </v4:Version>
			  <v4:InEffectAsOfTimestamp>2015-03-09T01:21:14+05:30</v4:InEffectAsOfTimestamp>
			  <v4:AddressesToValidate>
				 <v4:Address>
					<v4:StreetLines>{{.StreetLines}}</v4:StreetLines>
					<v4:City>{{.City}}</v4:City>
					<v4:StateOrProvinceCode>{{.StateOrProvinceCode}}</v4:StateOrProvinceCode>
					<v4:PostalCode>{{.PostalCode}}</v4:PostalCode>
					<v4:CountryCode>{{.CountryCode}}</v4:CountryCode>
				 </v4:Address>
			  </v4:AddressesToValidate>
		   </v4:AddressValidationRequest>
		</soapenv:Body>
	 </soapenv:Envelope>`
}

func getShipTemplate() string {
	return `
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns="http://fedex.com/ws/ship/v26">
   <soapenv:Header/>
   <soapenv:Body>
      <ProcessShipmentRequest>
         <WebAuthenticationDetail>
            <UserCredential>
               <Key>{{.Key}}</Key>
               <Password>{{.Password}}</Password>
            </UserCredential>
         </WebAuthenticationDetail>
         <ClientDetail>
            <AccountNumber>{{.AccountNumber}}</AccountNumber>
            <MeterNumber>{{.MeterNumber}}</MeterNumber>
         </ClientDetail>
         <TransactionDetail>
			<CustomerTransactionId>Ship_International_basic</CustomerTransactionId>
			<Localization>
				<LanguageCode>EN</LanguageCode>
				<LocaleCode>US</LocaleCode>
		 	</Localization>
         </TransactionDetail>
         <Version>
            <ServiceId>ship</ServiceId>
            <Major>26</Major>
            <Intermediate>0</Intermediate>
            <Minor>0</Minor>
         </Version>
         <RequestedShipment>
            <ShipTimestamp>{{.ShipTimestamp}}</ShipTimestamp>
            <DropoffType>REGULAR_PICKUP</DropoffType>
            <ServiceType>{{.ServiceType}}</ServiceType>
            <PackagingType>FEDEX_ENVELOPE</PackagingType>
            <Shipper>
               <Contact>
                  <PersonName>{{.ShipperPersonName}}</PersonName>
                  <CompanyName>{{.ShipperCompanyName}}</CompanyName>
                  <PhoneNumber>{{.ShipperPhoneNumber}}</PhoneNumber>
                  <EMailAddress>{{.ShipperEMailAddress}}</EMailAddress>
               </Contact>
               <Address>
                  <StreetLines>{{.ShipperStreetLines}}</StreetLines>
                  <City>{{.ShipperCity}}</City>
                  <StateOrProvinceCode>{{.ShipperStateOrProvinceCode}}</StateOrProvinceCode>
                  <PostalCode>{{.ShipperPostalCode}}</PostalCode>
                  <CountryCode>{{.ShipperCountryCode}}</CountryCode>
               </Address>
            </Shipper>
            <Recipient>
               <Contact>
                  <PersonName>{{.RecipientPersonName}}</PersonName>
                  <CompanyName>{{.RecipientCompanyName}}</CompanyName>
                  <PhoneNumber>{{.RecipientPhoneNumber}}</PhoneNumber>
                  <EMailAddress>{{.RecipientEMailAddress}}</EMailAddress>
               </Contact>
               <Address>
                  <StreetLines>{{.RecipientStreetLines}}</StreetLines>
                  <City>{{.RecipientCity}}</City>
                  <StateOrProvinceCode>{{.RecipientStateOrProvinceCode}}</StateOrProvinceCode>
                  <PostalCode>{{.RecipientPostalCode}}</PostalCode>
                  <CountryCode>{{.RecipientCountryCode}}</CountryCode>
               </Address>
            </Recipient>
            <ShippingChargesPayment>
               <PaymentType>{{.PaymentType}}</PaymentType>
               <Payor>
                  <ResponsibleParty>
                     <AccountNumber>{{.AccountNumber}}</AccountNumber>
                  </ResponsibleParty>
               </Payor>
			</ShippingChargesPayment>

			<CustomsClearanceDetail>
				<DutiesPayment>
					<PaymentType>{{.PaymentType}}</PaymentType>
					<Payor>
						<ResponsibleParty>
							<AccountNumber>{{.AccountNumber}}</AccountNumber>
						</ResponsibleParty>
					</Payor>
				</DutiesPayment>
				<DocumentContent>DOCUMENTS_ONLY</DocumentContent>
				<CustomsValue>
					<Currency>USD</Currency>
					<Amount>50.00</Amount>
				</CustomsValue>
				<Commodities>
					<NumberOfPieces>1</NumberOfPieces>
					<Description>DOCUMENT</Description>
					<CountryOfManufacture>US</CountryOfManufacture>
					<Weight>
						<Units>LB</Units>
						<Value>1.0</Value>
					</Weight>
					<Quantity>1</Quantity>
					<QuantityUnits>cm</QuantityUnits>
					<UnitPrice>
						<Currency>USD</Currency>
						<Amount>50.000000</Amount>
					</UnitPrice>
					<CustomsValue>
						<Currency>USD</Currency>
						<Amount>50.000000</Amount>
					</CustomsValue>
				</Commodities>
        	</CustomsClearanceDetail>

            <LabelSpecification>
               <LabelFormatType>COMMON2D</LabelFormatType>
			   <ImageType>PDF</ImageType>
			   <LabelStockType>PAPER_8.5X11_TOP_HALF_LABEL</LabelStockType>
            </LabelSpecification>
            <PackageCount>1</PackageCount>
            <RequestedPackageLineItems>
			   <SequenceNumber>1</SequenceNumber>
			   <GroupPackageCount>1</GroupPackageCount>
               <Weight>
                  <Units>LB</Units>
                  <Value>1</Value>
			   </Weight>
			   <ItemDescription>{{.Description}}</ItemDescription>
			   <CustomerReferences>
				  <CustomerReferenceType>CUSTOMER_REFERENCE</CustomerReferenceType>
				  <Value>{{.Description}}</Value>
			   </CustomerReferences>
            </RequestedPackageLineItems>
         </RequestedShipment>
      </ProcessShipmentRequest>
   </soapenv:Body>
</soapenv:Envelope>`
}

func getDeleteShipTemplate() string {
	return `<soapenv:Envelope
	xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
	xmlns:v23="http://fedex.com/ws/ship/v23">
		<soapenv:Header/>
		<soapenv:Body>
			<v23:DeleteShipmentRequest>
				<v23:WebAuthenticationDetail>
					<v23:UserCredential>
						<v23:Key>{{.Key}}</v23:Key>
						<v23:Password>{{.Password}}</v23:Password>
					</v23:UserCredential>
				</v23:WebAuthenticationDetail>
				<v23:ClientDetail>
					<v23:AccountNumber>{{.AccountNumber}}</v23:AccountNumber>
					<v23:MeterNumber>{{.MeterNumber}}</v23:MeterNumber>
				</v23:ClientDetail>
				<v23:TransactionDetail>
					<v23:CustomerTransactionId>DeleteShipmentRequest_v23.1</v23:CustomerTransactionId>
				</v23:TransactionDetail>
				<v23:Version>
					<v23:ServiceId>ship</v23:ServiceId>
					<v23:Major>23</v23:Major>
					<v23:Intermediate>0</v23:Intermediate>
					<v23:Minor>0</v23:Minor>
				</v23:Version>
				<v23:ShipTimestamp>{{.ShipTimestamp}}</v23:ShipTimestamp>
				<v23:TrackingId>
					<v23:TrackingIdType>{{.TrackingIdType}}</v23:TrackingIdType>
					<v23:FormId>{{.FormId}}</v23:FormId>
					<v23:TrackingNumber>{{.TrackingNumber}}</v23:TrackingNumber>
				</v23:TrackingId>
				<v23:DeletionControl>DELETE_ALL_PACKAGES</v23:DeletionControl>
			</v23:DeleteShipmentRequest>
		</soapenv:Body>
	</soapenv:Envelope>`
}

func trackByNumberTemplate() string {
	return `
	<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:v16="http://fedex.com/ws/track/v16">
		<soapenv:Header/>
		<soapenv:Body>
		<v16:TrackRequest>
			<v16:WebAuthenticationDetail>
				<v16:UserCredential>
					<v16:Key>{{.Key}}</v16:Key>
					<v16:Password>{{.Password}}</v16:Password>
				</v16:UserCredential>
			</v16:WebAuthenticationDetail>
			<v16:ClientDetail>
				<v16:AccountNumber>{{.AccountNumber}}</v16:AccountNumber>
				<v16:MeterNumber>{{.MeterNumber}}</v16:MeterNumber>
			</v16:ClientDetail>
			<v16:Version>
				<v16:ServiceId>trck</v16:ServiceId>
				<v16:Major>16</v16:Major>
				<v16:Intermediate>0</v16:Intermediate>
				<v16:Minor>0</v16:Minor>
			</v16:Version>
			<v16:SelectionDetails>
				<v16:PackageIdentifier>
					<v16:Type>TRACKING_NUMBER_OR_DOORTAG</v16:Type>
					<v16:Value>{{.TrackingNumber}}</v16:Value>
				</v16:PackageIdentifier>
			</v16:SelectionDetails>
		</v16:TrackRequest>
		</soapenv:Body>
	</soapenv:Envelope>`
}

func getRequestNotificationTemplate() string {
	return `
	<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:v16="http://fedex.com/ws/track/v16">
    <soapenv:Header/>
    <soapenv:Body>
        <v16:SendNotificationsRequest>
            <v16:WebAuthenticationDetail>
                <v16:UserCredential>
                    <v16:Key>{{.Key}}</v16:Key>
                    <v16:Password>{{.Password}}</v16:Password>
                </v16:UserCredential>
            </v16:WebAuthenticationDetail>
            <v16:ClientDetail>
                <v16:AccountNumber>{{.AccountNumber}}</v16:AccountNumber>
                <v16:MeterNumber>{{.MeterNumber}}</v16:MeterNumber>
                <v16:Localization>
                    <v16:LanguageCode>EN</v16:LanguageCode>
                </v16:Localization>
            </v16:ClientDetail>
            <v16:TransactionDetail>
                <v16:CustomerTransactionId>Ship_International_basic</v16:CustomerTransactionId>
                <v16:Localization>
                    <v16:LanguageCode>EN</v16:LanguageCode>
                </v16:Localization>
            </v16:TransactionDetail>
            <v16:Version>
                <v16:ServiceId>trck</v16:ServiceId>
                <v16:Major>16</v16:Major>
                <v16:Intermediate>0</v16:Intermediate>
                <v16:Minor>0</v16:Minor>
            </v16:Version>
            <v16:TrackingNumber>{{.TrackingNumber}}</v16:TrackingNumber>
            <v16:SenderEMailAddress>{{.SenderEmail}}</v16:SenderEMailAddress>
            <v16:SenderContactName>AriaDirect Corporation</v16:SenderContactName>
            <v16:EventNotificationDetail>
				<v16:AggregationType>PER_PACKAGE</v16:AggregationType>
				<v16:PersonalMessage>{{.PersonalMessage}}</v16:PersonalMessage>
				{{range .NotificationEmails}}
                <v16:EventNotifications>
                    <v16:Events>ON_DELIVERY</v16:Events>
                    <v16:Events>ON_ESTIMATED_DELIVERY</v16:Events>
                    <v16:Events>ON_EXCEPTION</v16:Events>
                    <v16:Events>ON_SHIPMENT</v16:Events>
                    <v16:Events>ON_TENDER</v16:Events>
                    <v16:NotificationDetail>
                        <v16:NotificationType>EMAIL</v16:NotificationType>
                        <v16:EmailDetail>
                            <v16:EmailAddress>{{.}}</v16:EmailAddress>
                            <v16:Name>AriaDirect Corporation</v16:Name>
                        </v16:EmailDetail>
                        <v16:Localization>
                            <v16:LanguageCode>EN</v16:LanguageCode>
                            <v16:LocaleCode>USA</v16:LocaleCode>
                        </v16:Localization>
                    </v16:NotificationDetail>
                    <v16:FormatSpecification>
                        <v16:Type>HTML</v16:Type>
                    </v16:FormatSpecification>
                </v16:EventNotifications>
				{{end}}
            </v16:EventNotificationDetail>
        </v16:SendNotificationsRequest>
    </soapenv:Body>
</soapenv:Envelope>`
}
