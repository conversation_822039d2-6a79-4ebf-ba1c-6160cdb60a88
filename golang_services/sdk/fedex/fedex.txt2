package fedex

import (
	"fmt"

	"github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
)

const (
	FormatDateTime = "2006-01-02T15:04:05"
	ISO2OfUS       = "US"
)

type FedexClient struct {
	BaseURL       string
	ClientID      string
	ClientSecret  string
	AccountNumber string
	client        *resty.Client
}

func NewFedex(conf map[string]any) (*FedexClient, error) {
	baseURL, _ := conf["base_url"].(string)
	clientID, _ := conf["client_id"].(string)
	clientSecret, _ := conf["client_secret"].(string)
	accountNumber, _ := conf["account_number"].(string)

	if baseURL == "" || clientID == "" || clientSecret == "" || accountNumber == "" {
		return nil, fmt.Errorf("missing required configuration")
	}

	client := resty.New()
	client.SetBaseURL(baseURL)

	return &FedexClient{
		BaseURL:       baseURL,
		ClientID:      clientID,
		ClientSecret:  clientSecret,
		AccountNumber: accountNumber,
		client:        client,
	}, nil
}

func (f *FedexClient) getAccessToken() (string, error) {
	resp, err := f.client.R().
		SetFormData(map[string]string{
			"grant_type":    "client_credentials",
			"client_id":     f.ClientID,
			"client_secret": f.ClientSecret,
		}).
		Post(f.BaseURL + "/oauth/token")

	if err != nil {
		return "", err
	}

	return gjson.GetBytes(resp.Body(), "access_token").String(), nil
}

func (f *FedexClient) DeleteShipment(trackingNumber string) error {
	accessToken, err := f.getAccessToken()
	if err != nil {
		return err
	}

	resp, err := f.client.R().
		SetAuthToken(accessToken).
		SetBody(map[string]any{
			"trackingNumber":  trackingNumber,
			"deletionControl": "DELETE_ALL_PACKAGES",
		}).
		Post("/ship/v1/deletions")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("failed to delete shipment: %s", resp.String())
	}

	return nil
}

func (f *FedexClient) CreateShippingLabel(rep ProcessShipmentRequest) (*ProcessShipmentResponse, error) {
	accessToken, err := f.getAccessToken()
	if err != nil {
		return nil, err
	}

	requestBody := map[string]any{
		"requestedShipment": map[string]any{
			"shipper":       rep.Shipper,
			"recipients":    []Address{rep.Recipient},
			"shipDatestamp": rep.ShipDate.Format("2006-01-02"),
			"serviceType":   rep.ServiceType,
			"packagingType": "FEDEX_ENVELOPE",
			"pickupType":    "REGULAR_PICKUP",
			"shippingChargesPayment": map[string]any{
				"paymentType": rep.PaymentType,
				"payor": map[string]any{
					"responsibleParty": map[string]any{
						"accountNumber": f.AccountNumber,
					},
				},
			},
			"labelSpecification": map[string]any{
				"labelFormatType": "COMMON2D",
				"imageType":       "PDF",
				"labelStockType":  "PAPER_8.5X11_TOP_HALF_LABEL",
			},
			"requestedPackageLineItems": []map[string]any{
				{
					"weight": map[string]any{
						"units": "LB",
						"value": 1,
					},
					"itemDescription": rep.Description,
					"customerReferences": []map[string]any{
						{
							"customerReferenceType": "CUSTOMER_REFERENCE",
							"value":                 rep.Description,
						},
					},
				},
			},
		},
	}

	resp, err := f.client.R().
		SetAuthToken(accessToken).
		SetBody(requestBody).
		Post("/ship/v1/shipments")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("failed to create shipping label: %s", resp.String())
	}

	result := gjson.ParseBytes(resp.Body())
	shipment := result.Get("output.transactionShipments.0")

	response := &ProcessShipmentResponse{
		Label:          shipment.Get("completedShipmentDetail.shipmentDocuments.0.url").String(),
		Price:          shipment.Get("shipmentRating.shipmentRateDetails.0.totalNetCharge.amount").String(),
		Currency:       shipment.Get("shipmentRating.shipmentRateDetails.0.totalNetCharge.currency").String(),
		TrackingNumber: shipment.Get("completedShipmentDetail.completedPackageDetails.0.trackingIds.0.trackingNumber").String(),
	}

	return response, nil
}
