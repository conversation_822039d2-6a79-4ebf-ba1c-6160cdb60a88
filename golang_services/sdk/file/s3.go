package file

import (
	"fmt"
	"time"
)

func BuildPkgInputS3Key(ownerID string, pkgID int, fileName string) string {
	return fmt.Sprintf("%s/%d/outputs/%s", ownerID, pkgID, fileName)
}

func BuildPkgConsulateInputS3Key(ownerID string, pkgID int, fileName string) string {
	return fmt.Sprintf("%s/%d/consulate-inputs/%s", ownerID, pkgID, fileName)
}

func BuildETSConsulateInputS3Key(ownerID string, orderID int, fileName string) string {
	return fmt.Sprintf("%s/ets-order-%d/consulate-inputs/%s", ownerID, orderID, fileName)
}

func BuildAppInputS3Key(fileName, ownerID string, pkgID, appID int) string {
	return fmt.Sprintf("%s/%d/%d/inputs/%s", ownerID, pkgID, appID, fileName)
}

func BuildAppOutputS3Key(fileName, ownerID string, pkgID, appID int) string {
	return fmt.Sprintf("%s/%d/%d/outputs/%s", ownerID, pkgID, appID, fileName)
}

func BuildBackupS3Key(fileName string) string {
	now := time.Now().UTC()
	return fmt.Sprintf("%s/%s", now.Format("20060102"), fileName)
}

func BuildDocumentTemplateS3Key(fileName string, templateType string) string {
	return fmt.Sprintf("%s/%s", templateType, fileName)
}

func BuildValidationOrderInputS3Key(orgID, orderID int, filename string) string {
	return fmt.Sprintf("%d/%d/%s", orgID, orderID, filename)
}

func BuildPaymentOutputS3Key(ownerID string, paymentID string, fileName string) string {
	return fmt.Sprintf("%s/%s/outputs/%s", ownerID, paymentID, fileName)
}

func BuildTaskInputS3Key(fileName, ownerID string, pkgID int, taskID string) string {
	return fmt.Sprintf("%s/%d/%s/inputs/%s", ownerID, pkgID, taskID, fileName)
}

func BuildBatchUploadKey(fileName, ownerID, batchID string) string {
	return fmt.Sprintf("%s/batch-inputs/%s/%s", ownerID, batchID, fileName)
}

func BuildBatchUploadPrefix(ownerID, batchID string) string {
	return fmt.Sprintf("%s/batch-inputs/%s", ownerID, batchID)
}
