package summary

import (
	"fmt"
	"strconv"
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/tidwall/gjson"
)

func CalculateETSServiceSummary(dao db.IEtsDao, order *models.ServiceOrderDetail, cartSelection models.CartSelection, update bool) (*models.ServiceSummary, error) {
	switch order.Service.ServiceType {
	case "id_photo":
		return CalculateIDPhotoSummary(dao, order, cartSelection, update)
	default:
		return CalculateGeneralETSSummary(dao, order, cartSelection, update)
	}
}

func CalculateGeneralETSSummary(dao db.IEtsDao, order *models.ServiceOrderDetail, cartSelection models.CartSelection, update bool) (*models.ServiceSummary, error) {
	if order.SmallestPrice == nil {
		return nil, fmt.Errorf("Service: %d is missing price configuration", order.ServiceID)
	}
	var quantity float64 = 0
	var unitPrice float64 = order.SmallestPrice.UnitPrice
	var discountPrice float64 = 0
	promotionCode := strings.ToUpper(cartSelection.PromotionCode)

	if promotionCode != "" {
		promotionCodes, err := dao.VisaDB().GetPromotionCodes()
		if err != nil {
			return nil, err
		}

		user, err := dao.VisaDB().GetUserByID(order.UserID)
		if err != nil {
			return nil, err
		}

		if _, discount, ok := promotionCodes.ApplyCode(promotionCode, int64(order.ServiceID), user); ok {
			if discount != 0 {
				discountPrice = discount
			}
		}
	}

	priceModifiedRuleJ := utils.StructToJSON(order.SmallestPrice.PriceModifiedRules)

	additionalServiceJ := utils.StructToJSON(order.SmallestPrice.AdditionalServices)
	additionalServiceMap := map[string]gjson.Result{}
	for _, additionalService := range additionalServiceJ.Array() {
		additionalServiceMap[additionalService.Get("id").String()] = additionalService
	}

	for _, rule := range priceModifiedRuleJ.Array() {
		if rule.Get("rule").String() == "QUANTITY" {
			from, _ := strconv.ParseFloat(strings.Split(rule.Get("condition").String(), "-")[0], 10)
			to, _ := strconv.ParseFloat(strings.Split(rule.Get("condition").String(), "-")[1], 10)

			if quantity >= from && quantity <= to {
				unitPrice = rule.Get("price").Float()
			}
		}

		if rule.Get("rule").String() == "ADDITIONAL_FEE" {
			from, _ := strconv.ParseFloat(strings.Split(rule.Get("condition").String(), "-")[0], 10)
			to, _ := strconv.ParseFloat(strings.Split(rule.Get("condition").String(), "-")[1], 10)

			if quantity >= from && quantity <= to {
				unitPrice = rule.Get("price").Float()
			}
		}
	}

	var appServices = map[int64]map[string]float64{}
	var appServicePrice float64 = 0
	for _, task := range order.Tasks {
		taskKeyPair := task.InputPods.ToMapKeyValueV2(order.InputPods)
		if order.Service.ServiceType == "fastlane" || order.Service.ServiceType == "country_tourist" {
			// Get number of traveler enter
			if val := taskKeyPair["travel_passenger_info_no_of_traveler"]; val != nil {
				if val, ok := val.(float64); ok {
					quantity += val
				} else {
					quantity++
				}
			} else {
				quantity++
			}
		} else {
			quantity = float64(len(order.Tasks))
		}

		if val := taskKeyPair["travel_additional_information_additional_services"]; val != nil {
			appServices[task.ID] = map[string]float64{}
			for _, selectedAdditionalService := range utils.StructToJSON(val).Array() {
				selectedServiceID := selectedAdditionalService.Get("id").String()
				appServices[task.ID][selectedServiceID] = additionalServiceMap[selectedServiceID].Get("price").Float() * selectedAdditionalService.Get("quantity").Float()
				appServicePrice += appServices[task.ID][selectedServiceID]
			}
		}
	}

	if quantity <= 0 {
		quantity = 1
	}

	subTotal := unitPrice
	utils.StructToJSON(order.SmallestPrice.AdditionalFee).ForEach(func(key, value gjson.Result) bool {
		subTotal += value.Float()
		return true
	})

	shippingPrice := utils.StructToJSON(order.SmallestPrice.Shipments).Get("shipping_fee").Float()

	if utils.StructToJSON(order.Service.Attributes).Get("separate_shipment").Bool() {
		shippingPrice = shippingPrice * quantity
	}

	if order.ShipmentOrder != nil {
		shippingPrice = 0
	}

	summary := &models.ServiceSummary{
		UnitPrice:           unitPrice,
		UnitPriceWithFees:   subTotal,
		ShippingPrice:       shippingPrice,
		Currency:            order.SmallestPrice.Currency,
		Total:               subTotal*quantity + shippingPrice + appServicePrice,
		AdditionalFee:       order.SmallestPrice.AdditionalFee,
		AdditionAppServices: appServices,
		Quantity:            int(quantity),
		SubTotal:            subTotal*quantity + appServicePrice,
	}
	if order.Summary == nil {
		summary.TotalActual = summary.Total
		summary.TotalActualNote = ""
	} else {
		summary.TotalActual = order.Summary.TotalActual
		summary.TotalActualNote = order.Summary.TotalActualNote

	}

	if discountPrice != 0 {
		summary.PromotionCode = promotionCode
		summary.Discount = discountPrice * quantity
		summary.Total = summary.Total - summary.Discount
	}

	// if val, ok := payments.TransactionFee[cartSelection.Method]; ok {
	// 	summary.TransactionFee = math.Ceil(summary.Total * val) // Buffer 3% for transaction fee
	// 	summary.Total += summary.TransactionFee
	// }

	// if !order.IsPaid() {
	if err := dao.UpdateServiceOrder(map[string]any{"summary": summary}, order.ID); err != nil {
		return nil, err
	}
	// }

	return summary, nil
}

func CalculateIDPhotoSummary(dao db.IEtsDao, order *models.ServiceOrderDetail, cartSelection models.CartSelection, update bool) (*models.ServiceSummary, error) {
	if order.SmallestPrice == nil {
		return nil, fmt.Errorf("Service: %d is missing price configuration", order.ServiceID)
	}
	var quantity float64 = 0
	var unitPrice float64 = order.SmallestPrice.UnitPrice
	var shippingFee float64 = 0

	subTotal := unitPrice
	utils.StructToJSON(order.SmallestPrice.AdditionalFee).ForEach(func(key, value gjson.Result) bool {
		subTotal += value.Float()
		return true
	})

	shippingFee += utils.StructToJSON(order.SmallestPrice.Shipments).Get("shipping_fee").Float()

	for _, task := range order.Tasks {
		// Get number of Print Photo
		if val := task.InputPods.ToMapKeyValueV2(order.InputPods)["service_core_info_number_of_set"]; val != nil {
			if val.(float64) > 0 {
				quantity += val.(float64)
			} else {
				quantity += 1
			}
		}
	}

	if quantity < 1 {
		quantity = 1
	}

	summary := &models.ServiceSummary{
		UnitPrice:         unitPrice,
		UnitPriceWithFees: subTotal,
		ShippingPrice:     shippingFee,
		Currency:          order.SmallestPrice.Currency,
		Total:             subTotal*quantity + shippingFee,
		AdditionalFee:     order.SmallestPrice.AdditionalFee,
		Quantity:          int(quantity),
		SubTotal:          subTotal * quantity,
	}

	if update {
		if err := dao.UpdateServiceOrder(map[string]any{"summary": summary}, order.ID); err != nil {
			return nil, err
		}
	}

	return summary, nil
}
