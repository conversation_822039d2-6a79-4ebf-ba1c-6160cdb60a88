package queue

import (
	"context"
	"os"
	"os/signal"
	"syscall"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/rs/zerolog/log"
)

type MsgProcessor func(message *sqs.Message) error

type SQSProcessor struct {
	svc        *sqs.SQS
	url        string
	processor  MsgProcessor
	numWorkers int
	msgChan    chan *sqs.Message
	errChan    chan error
}

func NewSQSProcessor(sess *session.Session, url string, processor MsgProcessor) *SQSProcessor {
	svc := sqs.New(sess)
	msgChan := make(chan *sqs.Message, 20)
	errChan := make(chan error)
	return &SQSProcessor{
		svc:        svc,
		url:        url,
		processor:  processor,
		numWorkers: 10,
		msg<PERSON>han:    msg<PERSON>han,
		errChan:    err<PERSON><PERSON>,
	}
}

func (p *SQSProcessor) Run() {
	ctx, cancel := context.WithCancel(context.Background())

	go p.consume(ctx)

	for i := 0; i < p.numWorkers; i++ {
		go p.process(ctx)
	}

	go func(c context.CancelFunc) {
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGHUP, syscall.SIGTERM)

	loop:
		for {
			select {
			case <-sigCh:
				break loop
			}
		}

		log.Info().Msg("Terminating SQS processor...")
		c()
	}(cancel)
}

func (p *SQSProcessor) consume(c context.Context) {
	input := &sqs.ReceiveMessageInput{
		AttributeNames:        aws.StringSlice([]string{"SentTimestamp"}),
		MaxNumberOfMessages:   aws.Int64(10),
		MessageAttributeNames: aws.StringSlice([]string{"All"}),
		QueueUrl:              aws.String(p.url),
		VisibilityTimeout:     aws.Int64(70),
		WaitTimeSeconds:       aws.Int64(20),
	}

loop:
	for {
		select {
		case <-c.Done():
			break loop
		default:
			resp, err := p.svc.ReceiveMessage(input)
			if err != nil {
				p.errChan <- err
				continue
			}
			for _, msg := range resp.Messages {
				p.msgChan <- msg
			}
		}
	}
}

func (p *SQSProcessor) process(c context.Context) {
loop:
	for {
		select {
		case <-c.Done():
			break loop
		case msg := <-p.msgChan:
			err := p.processOne(msg)
			if err != nil {
				log.Error().Str("error", err.Error()).Msg("SQSProcessor: failed to process msg")
				continue
			}
			input := &sqs.DeleteMessageInput{
				QueueUrl:      aws.String(p.url),
				ReceiptHandle: msg.ReceiptHandle,
			}
			_, err = p.svc.DeleteMessage(input)
			if err != nil {
				log.Error().Str("error", err.Error()).Msg("SQSProcessor: failed to delete msg")
			}
		case err := <-p.errChan:
			log.Error().Str("error", err.Error()).Msg("SQSProcessor: consumer error")
		}
	}
}

func (p *SQSProcessor) processOne(msg *sqs.Message) error {
	err := p.processor(msg)
	if err != nil {
		return err
	}
	input := &sqs.DeleteMessageInput{
		QueueUrl:      aws.String(p.url),
		ReceiptHandle: msg.ReceiptHandle,
	}
	_, err = p.svc.DeleteMessage(input)
	if err != nil {
		return err
	}
	return nil
}
