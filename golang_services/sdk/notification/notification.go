package notification

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/sdk/whatsapp"
	"bitbucket.org/persistence17/aria/golang_services/sdk/zalo"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/gin-gonic/gin"
	"github.com/metal3d/go-slugify"
	"github.com/pariz/gountries"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
)

const (
	NotifyInternalCreateOrerTemplateName   = "notify_internal_create_order"
	NotifyInternalCheckoutOrerTemplateName = "notify_internal_checkout_order"
	NotifyReviewPassportTemplateName       = "noti_fill_missing_ets_to_ad"
	NotifyNeedMoreDocumentTemplateName     = "reject_status_notify_to_ad"
	NotifyCancelTemplateName               = "cancel_ets_to_user"
	NotiQueue                              = "notification"
	NotifyVisaNeedMoreDocumentTemplateName = "visa_need_more_document_to_user"
)

func NotifyInternalCreateOrder(c *gin.Context, orderID int, userID, orderType string) error {
	config := middlewares.GetServiceConfigMap(c)

	if config["env"] != "prod" {
		return nil
	}

	sender := middlewares.GetSimpleQueueSender(c, NotiQueue)
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}
	user, err := dao.GetUserByID(userID)
	if err != nil {
		return err
	}
	message := map[string]any{
		"template_name": NotifyInternalCreateOrerTemplateName,
		"to":            config["support_email"].(string),
		"bcc":           []string{config["supervisor_email"].(string)},
		"parameters": map[string]any{
			"OrderID":  fmt.Sprintf("%d", orderID),
			"FullName": fmt.Sprintf("%s %s", user.Surname, user.GivenName),
			"Email":    user.Email,
			"Type":     orderType,
		},
	}

	return sender.Send(message)
}

func NotifyInternalCheckedOutOrder(c *gin.Context, orderID, userID, orderType string) error {
	config := middlewares.GetServiceConfigMap(c)

	if config["env"] != "prod" {
		return nil
	}

	sender := middlewares.GetSimpleQueueSender(c, NotiQueue)
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}
	user, err := dao.GetUserByID(userID)
	if err != nil {
		return err
	}
	message := map[string]any{
		"template_name": NotifyInternalCheckoutOrerTemplateName,
		"to":            config["support_email"].(string),
		"bcc":           []string{config["supervisor_email"].(string)},
		"parameters": map[string]any{
			"OrderID":  orderID,
			"FullName": fmt.Sprintf("%s %s", user.Surname, user.GivenName),
			"Email":    user.Email,
			"Type":     orderType,
		},
	}
	return sender.Send(message)
}

func NotifyReviewPassport(c *gin.Context, orderID int, userID string) error {
	config := middlewares.GetServiceConfigMap(c)
	sender := middlewares.GetSimpleQueueSender(c, NotiQueue)
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}
	user, err := dao.GetUserByID(userID)
	if err != nil {
		return err
	}
	message := map[string]any{
		"template_name": NotifyReviewPassportTemplateName,
		"to":            config["admin_email"].(string),
		"parameters": map[string]any{
			"ID":    orderID,
			"Email": user.Email,
		},
	}
	return sender.Send(message)
}

func NotifyNeedMoreDocument(c *gin.Context, order *models.ServiceOrderDetail) error {
	config := middlewares.GetServiceConfigMap(c)
	sender := middlewares.GetSimpleQueueSender(c, NotiQueue)

	message := map[string]any{
		"template_name": NotifyNeedMoreDocumentTemplateName,
		"to":            config["support_email"].(string),
		"parameters": map[string]any{
			"OrderID": order.ID,
			"URL":     fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", config["web_base_url"].(string), order.ID),
		},
	}
	return sender.Send(message)
}

func NotifyCancelETSToUser(c *gin.Context, order *models.ServiceOrderDetail, taskInfo *models.ServiceTask) error {
	config := middlewares.GetServiceConfigMap(c)
	sender := middlewares.GetSimpleQueueSender(c, NotiQueue)
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}
	user, err := dao.GetUserByID(order.UserID)
	if err != nil {
		return err
	}
	// find application name
	var surname, givenName, middleName string
	for _, v := range taskInfo.InputPods {
		if v.Category == "passport" && v.SubCategory == "core_info" {
			switch v.Name {
			case "surname":
				surname = v.Value.FE.(string)
			case "given_name":
				givenName = v.Value.FE.(string)
			case "middle_name":
				middleName = v.Value.FE.(string)
			}
		}
	}
	message := map[string]any{
		"template_name": NotifyCancelTemplateName,
		"to":            user.Email,
		"bcc":           []string{config["support_email"].(string)},
		"parameters": map[string]any{
			"OrderID":         order.ID,
			"FullName":        strings.ToUpper(user.GivenName + " " + user.Surname),
			"ApplicationName": strings.ToUpper(fmt.Sprintf("%s, %s %s", surname, givenName, middleName)),
			"URL":             fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", config["web_base_url"].(string), order.ID),
		},
	}
	return sender.Send(message)
}

func NotifyCancelETSToProvider(c *gin.Context, order *models.ServiceOrderDetail, cancelReason string) error {
	config := middlewares.GetServiceConfigMap(c)
	sender := middlewares.GetSimpleQueueSender(c, NotiQueue)
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}

	etsDao := db.NewEtsDao(dao.DB().Db)

	provider, err := etsDao.GetEtsProviderByID(order.ProviderID)
	if err != nil {
		return err
	}

	applications := []map[string]any{}
	for _, task := range order.Tasks {
		applications = append(applications, map[string]any{
			"FullName": task.GetAppName(order.Service.ServiceType),
		})
	}

	var gocountry = gountries.New()
	localize := middlewares.GetLocalize(c)

	country, _ := gocountry.FindCountryByAlpha(order.Service.Country)
	message := map[string]any{
		"template_name": "cancel_ets_to_provider",
		"to":            provider.Contact.Email,
		"bcc":           []string{config["support_email"].(string)},
		"parameters": map[string]any{
			"Name":         provider.Name,
			"OrderID":      order.ID,
			"CountryName":  country.Name.Common,
			"ServiceType":  localize.EN.ServiceType[order.Service.ServiceType],
			"Task":         localize.EN.Tasks[strings.Join(order.Service.Tasks, ", ")],
			"Applications": applications,
			"URL":          fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", config["web_base_url"].(string), order.ID),
		},
	}
	if err := sender.Send(message); err != nil {
		return err
	}

	if order.Service.ServiceType == "fastlane" {
		task := order.Tasks[0]
		inputPods := task.InputPods.ToMapKeyValueV2(order.InputPods)
		webBaseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("web_base_url").String()

		// Build Zalo message with addField
		var parts []string
		addField := func(label string, value interface{}) {
			if v := fmt.Sprintf("%v", value); v != "" && v != "0" {
				parts = append(parts, fmt.Sprintf("%s: <b>%v</b>", label, value))
			}
		}

		isDeparture := order.Service.Tasks[0] == "departure"
		serviceType := funk.ShortIf(cast.ToString((*order.Service.Attributes)["terminal"]) == "international", "International", "Domestic")
		serviceDirection := funk.ShortIf(isDeparture, "Departure", "Arrival")

		parts = append(parts, fmt.Sprintf(`[<b><span style="color:#f27806">CANCELLED</span></b>] Order# <b>%d</b> - Request for Fastlane <b>%s %s</b> service as below:`,
			order.ID, serviceType, serviceDirection))

		contactMethod := ""
		if method := cast.ToString(inputPods["travel_traveler_contact_info_social_network_channel"]); method != "" {
			contactMethod = method + " - " + utils.OneOf(
				utils.StructToJSON(inputPods["travel_traveler_contact_info_social_network_id"]).Get("phone").String(),
				utils.StructToJSON(inputPods["travel_traveler_contact_info_social_network_id"]).String(),
			)
		}

		if isDeparture {
			addField("Airport", cast.ToString(inputPods["travel_exit_flight_exit_airport"]))
			addField("Flight#", utils.GetStringOrText(inputPods["travel_exit_flight_exit_flight"]))
			addField("Date Time", cast.ToTime(inputPods["travel_exit_flight_exit_timestamp"]).Format("Jan 02, 2006 - 15:04"))
		} else {
			addField("Airport", cast.ToString(inputPods["travel_enter_flight_enter_airport"]))
			addField("Flight#", utils.GetStringOrText(inputPods["travel_enter_flight_enter_flight"]))
			addField("Date Time", cast.ToTime(inputPods["travel_enter_flight_enter_timestamp"]).Format("Jan 02, 2006 - 15:04"))
		}

		addField("Welcome Name", cast.ToString(inputPods["travel_passenger_info_welcome_name"]))
		addField("Contact", contactMethod)
		addField("Number of travelers", cast.ToInt(inputPods["travel_passenger_info_no_of_traveler"]))
		addField("Note", cast.ToString(inputPods["travel_additional_information_note"]))
		addField("Cancel reason", cancelReason)
		parts = append(parts, fmt.Sprintf("To view order details, please click the below button:\n%s/dashboard/orders/detail?order_id=%d",
			webBaseURL, order.ID))

		zaloMessage := strings.Join(parts, "\n")

		// Send SMS
		zaloGroup := provider.Contact.ZaloGroup
		if zaloGroup != "" {
			if err := zalo.SendZaloSMS(zaloGroup, zaloMessage); err != nil {
				return err
			}
		}
		attJ := utils.StructToJSON(order.Service.Attributes)
		whatsAppGroup := attJ.Get("notifications.whatsapp").String()
		if whatsAppGroup != "" && cast.ToString(inputPods["travel_traveler_contact_info_social_network_channel"]) == "whatsapp" {
			userPhone := strings.ReplaceAll(utils.OneOf(
				utils.StructToJSON(inputPods["travel_traveler_contact_info_social_network_id"]).Get("phone").String(),
				utils.StructToJSON(inputPods["travel_traveler_contact_info_social_network_id"]).String(),
			), "+", "")
			if err := etsDao.CreateWatchdogJob(models.WatchDogJob{
				JobName: models.JobNameDelayBeforeRemoveUserFromWhatsApp,
				JobData: []byte(utils.StructToJSON(map[string]any{
					"group": whatsAppGroup,
					"phone": userPhone,
				}).Raw),
				ScheduleAt: time.Now().Add(5 * time.Minute),
			}); err != nil {
				fmt.Println(err)
			}
			if err := whatsapp.SendMessageToGroup(whatsAppGroup, fmt.Sprintf(`Thanks @%s for choosing AriaDirect Fastlane service. We will remove you from the group.`, userPhone)); err != nil {
				fmt.Println(err)
			}
		}

	}
	return nil
}

func NotifyCompletedOrderETSToProvider(c *gin.Context, order *models.ServiceOrderDetail) error {
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}
	etsDao := db.EtsDao{Db: dao.DB().Db}

	if order.Service.ServiceType == "fastlane" {
		task := order.Tasks[0]
		inputPods := task.InputPods.ToMapKeyValueV2(order.InputPods)

		attJ := utils.StructToJSON(order.Service.Attributes)
		whatsAppGroup := attJ.Get("notifications.whatsapp").String()

		if whatsAppGroup != "" && cast.ToString(inputPods["travel_traveler_contact_info_social_network_channel"]) == "whatsapp" {
			userPhone := strings.ReplaceAll(utils.OneOf(
				utils.StructToJSON(inputPods["travel_traveler_contact_info_social_network_id"]).Get("phone").String(),
				utils.StructToJSON(inputPods["travel_traveler_contact_info_social_network_id"]).String(),
			), "+", "")
			if err := etsDao.CreateWatchdogJob(models.WatchDogJob{
				JobName: models.JobNameDelayBeforeRemoveUserFromWhatsApp,
				JobData: []byte(utils.StructToJSON(map[string]any{
					"group": whatsAppGroup,
					"phone": userPhone,
				}).Raw),
				ScheduleAt: time.Now().Add(5 * time.Minute),
			}); err != nil {
				fmt.Println(err)
			}
			if err := whatsapp.SendMessageToGroup(whatsAppGroup, fmt.Sprintf(`Thanks @%s for choosing AriaDirect Fastlane service. We will remove you from the group.`, userPhone)); err != nil {
				fmt.Println(err)
			}
		}

	}
	return nil
}

type PodDataChange struct {
	Title, OldValue, NewValue string
}

func NotifyUpdateOutputPods(c *gin.Context, orderID, taskID int64) error {
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}
	localize := middlewares.GetLocalize(c)
	etsDao := db.NewEtsDao(dao.DB().Db)
	orderResp, err := etsDao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:             []string{strconv.Itoa(int(orderID))},
		IncludeTasks:   true,
		IncludeService: true,
		Limit:          1,
	})
	if err != nil {
		return err
	}
	order := orderResp.Data[0]
	var task *models.ServiceTask
	for _, t := range order.Tasks {
		if t.ID == taskID {
			task = t
			break
		}
	}
	if task == nil {
		return nil
	}

	inputPodValues := task.InputPods.ToMapKeyValueV2(order.InputPods)
	outputPodValues := task.OutputPods.ToMapKeyValue()
	userInputEmail := cast.ToString(inputPodValues["travel_visa_info_registration_email"])
	if userInputEmail == "" {
		userInputEmail = cast.ToString(outputPodValues["application_application_info_registration_email"])
	}

	for _, pod := range task.OutputPods {
		if pod.Value == nil || pod.Value.FE == nil {
			continue
		}
		switch pod.ID {
		case "application_application_info_copy_of_new_visa":
			visaFiles := cast.ToStringSlice(pod.GetFEValue())
			if len(visaFiles) == 0 {
				continue
			}
			attachments := []map[string]any{}

			for i, visaItem := range visaFiles {
				bucket, key, _ := utils.UrlToS3BucketAndKey(visaItem)
				ext := filepath.Ext(key)
				name := fmt.Sprintf("%s%s", slugify.Marshal(task.GetAppName(order.Service.ServiceType)), ext)
				if i > 0 {
					name = fmt.Sprintf("%s-%d%s", slugify.Marshal(task.GetAppName(order.Service.ServiceType)), i+1, ext)
				}
				attachments = append(attachments, map[string]any{
					"bucket":    bucket,
					"key":       key,
					"file_name": name,
				})
			}

			user, err := dao.GetUserByID(order.UserID)
			if err != nil {
				return err
			}

			totalAppGrantedVisa := 0
			for _, task := range order.Tasks {
				pair := task.OutputPods.ToMapKeyValue()
				if len(cast.ToStringSlice(pair["application_application_info_copy_of_new_visa"])) > 0 {
					totalAppGrantedVisa++
				}
			}

			shipment, err := dao.GetVisaShipment(order.ShipmentInfo.String)
			if err != nil {
				return err
			}
			shipmentJ := utils.StructToJSON(shipment).Get("shipping_contact")

			fullName := "SIR/MADAM"
			if user.GivenName != "" {
				fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(user.GivenName), strings.TrimSpace(user.Surname)))
			} else if shipmentJ.Get("given_name").String() != "" {
				fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(shipmentJ.Get("given_name").String()), strings.TrimSpace(shipmentJ.Get("surname").String())))
			}

			parameters := map[string]any{
				"PackageID":        order.ID,
				"FullName":         fullName,
				"IssueMethod":      localize.EN.ServiceType[order.Service.ServiceType],
				"VisaCountry":      localize.EN.Country[order.Service.Country],
				"TotalGrantedVisa": totalAppGrantedVisa,
				"TotalApps":        len(order.Tasks),
			}
			emailConfigMap := utils.StructToJSON(utils.GetMapEnv("ad_email"))

			ccEmails := []string{shipmentJ.Get("email").String()}
			if userInputEmail != "" {
				ccEmails = append(ccEmails, userInputEmail)
			}

			bccEmails := []string{emailConfigMap.Get("visas").String()}

			sendVisaToTemplate := utils.StructToJSON(order.Service.Attributes).Get("email_template.send_visa_to_user").String() // Get email template from attributes

			message := map[string]any{
				"template_name": sendVisaToTemplate,
				"to":            user.Email,
				"cc":            ccEmails,
				"bcc":           bccEmails,
				"parameters":    parameters,
				"attachments":   attachments,
			}

			if len(attachments) > 0 {
				if sendVisaToTemplate != "" && order.Status != models.EtsOrderStatusCompleted {
					if err := sendEmail(c, utils.StructToJSON(message).Raw); err != nil {
						return err
					}
				}
				if cast.ToString(outputPodValues["application_application_info_application_status"]) != "granted" {
					task.OutputPods.SetFEValue("application_application_info_application_status", "granted")
					if note := cast.ToString(task.OutputPods.ToMapKeyValue()["application_application_info_note"]); note != "" {
						task.OutputPods.SetFEValue("application_application_info_note", note+"\n"+"Granted at: "+time.Now().Format("02/01/2006"))
					}
				}
			}

			if err := etsDao.UpdateServiceTask(map[string]any{
				"output_files": task.OutputFiles,
				"output_pods":  task.OutputPods,
				"status":       models.EtsTaskStatusApproved,
			}, task.ID); err != nil {
				return err
			}

			if totalAppGrantedVisa == len(order.Tasks) && order.Status != models.EtsOrderStatusCompleted {
				if err := etsDao.UpdateServiceOrder(map[string]any{
					"status":         models.EtsOrderStatusCompleted,
					"completed_time": time.Now(),
					"updated_at":     time.Now(),
				}, order.ID); err != nil {
					return err
				}

				if err := NotifyFeedbackToUser(order.ID); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func sendEmail(c *gin.Context, data string) error {
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(middlewares.GetAWSRegion(c))},
	)
	if err != nil {
		return err
	}
	svc := sqs.New(sess)
	sqsConf := gjson.Parse(os.Getenv("ad_sqs"))
	qURL := sqsConf.Get("url_prefix").String() + "/" + sqsConf.Get("notification_sqs_name").String()

	if _, err := svc.SendMessage(&sqs.SendMessageInput{
		MessageBody: aws.String(data),
		QueueUrl:    &qURL,
	}); err != nil {
		return err
	}
	return nil
}

func NotifyVisaNeedMoreDocumentToUser(c *gin.Context, fullname, remark, email string, packageID int, bcc []string) error {
	config := middlewares.GetServiceConfigMap(c)
	sender := middlewares.GetSimpleQueueSender(c, NotiQueue)

	message := map[string]any{
		"template_name": NotifyVisaNeedMoreDocumentTemplateName,
		"to":            email,
		"bcc":           bcc,
		"parameters": map[string]any{
			"PackageID": packageID,
			"FullName":  fullname,
			"Remark":    remark,
			"URL":       fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=visa", config["web_base_url"].(string), packageID),
		},
	}
	return sender.Send(message)
}
func NotifyNeedMoreDocumentToUser(c *gin.Context, order *models.ServiceOrderDetail, task *models.ServiceTask) error {
	config := middlewares.GetServiceConfigMap(c)
	sender := middlewares.GetSimpleQueueSender(c, NotiQueue)
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}
	user, err := dao.GetUserByID(order.UserID)
	if err != nil {
		return err
	}

	notes := utils.StructToJSON(task.Note).Get("need_more_document").Array()
	if len(notes) == 0 {
		return fmt.Errorf("Missing note need_more_document message for task %d", task.ID)
	}

	remark := notes[len(notes)-1].Get("message").String()
	if remark == "" {
		return fmt.Errorf("Missing note need_more_document message for task %d", task.ID)
	}
	appName := task.GetAppName(order.Service.ServiceType)
	message := map[string]any{
		"template_name": "ets_need_more_document_to_user",
		"to":            user.Email,
		"bcc":           []string{config["support_email"].(string)},
		"parameters": map[string]any{
			"OrderID":  order.ID,
			"FullName": appName,
			"Remark":   remark,
			"URL":      fmt.Sprintf("%s/dashboard/customer-orders/detail?order_id=%d&service=ets", config["web_base_url"].(string), order.ID),
		},
	}
	return sender.Send(message)
}

func NotifyUploadMissingDocumentToConsulate(c *gin.Context, dao db.IEtsDao, task *models.ServiceTask) error {
	config := middlewares.GetServiceConfigMap(c)
	sender := middlewares.GetSimpleQueueSender(c, NotiQueue)
	orders, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:    []string{strconv.Itoa(task.OrderID)},
		Limit: 1,
	})
	if err != nil {
		return err
	}
	order := orders.Data[0]

	notes := utils.StructToJSON(task.Note).Get("need_more_document").Array()
	if len(notes) == 0 {
		return fmt.Errorf("Missing note.need_more_document.message")
	}

	remark := notes[len(notes)-1].Get("message").String()

	if remark == "" {
		return fmt.Errorf("Missing note.need_more_document.message")
	}

	provider, err := dao.GetEtsProviderByID(order.ProviderID)
	if err != nil {
		return err
	}

	taskName := task.GetAppName(order.Service.ServiceType)

	message := map[string]any{
		"template_name": "notify_uploaded_missing_document_to_provider",
		"bcc":           []string{config["support_email"].(string)},
		"parameters": map[string]any{
			"ConsulateName": provider.Name,
			"OrderID":       order.ID,
			"FullName":      taskName,
			"Remark":        remark,
			"URL":           fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", config["web_base_url"].(string), order.ID),
		},
	}

	if config["env"].(string) == "prod" {
		message["to"] = provider.Contact.Email
	} else {
		message["to"] = config["support_email"].(string)
	}

	return sender.Send(message)
}

func NotifyFeedbackToUser(orderID int) error {
	sender := middlewares.GetSQS()
	dao := middlewares.GetDB()
	etsDao := db.NewEtsDao(dao.Db)

	resp, err := etsDao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:              []string{strconv.Itoa(orderID)},
		IncludeTasks:    true,
		IncludeShipment: true,
		Limit:           1,
	})

	for _, task := range resp.Data[0].Tasks {
		if task.Status == models.EtsTaskStatusDenied {
			return nil // Skip if any task is denied
		}
	}

	if err != nil {
		return err
	}
	user, err := dao.GetUserByID(resp.Data[0].UserID)
	if err != nil {
		return err
	}

	cc := []string{}

	shipmentJ := utils.StructToJSON(resp.Data[0]).Get("shipment.shipping_contact")
	shipmentEmail := shipmentJ.Get("email").String()
	if shipmentEmail != "" && shipmentEmail != user.Email {
		cc = append(cc, shipmentEmail)
	}

	fullName := "SIR/MADAM"
	if user.GivenName != "" {
		fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(user.GivenName), strings.TrimSpace(user.Surname)))
	} else if shipmentJ.Get("given_name").String() != "" {
		fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(shipmentJ.Get("given_name").String()), strings.TrimSpace(shipmentJ.Get("surname").String())))
	}

	//  Send Feedback Email
	webBaseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("web_base_url").String()
	bcc := []string{gjson.Parse(os.Getenv("ad_email")).Get("support").String()}

	message := map[string]any{
		"template_name": "send_feedback_to_user",
		"to":            user.Email,
		"cc":            cc,
		"bcc":           bcc,
		"parameters": map[string]any{
			"OrderID":  orderID,
			"FullName": fullName,
			"URL":      fmt.Sprintf("%s/feedback?order_id=%d&service=ets", webBaseURL, orderID),
		},
		"attachments": []map[string]any{},
	}

	return sender.Send(message)
}
