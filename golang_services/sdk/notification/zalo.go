package notification

import (
	"fmt"
	"os"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/time_util"
	"bitbucket.org/persistence17/aria/golang_services/sdk/zalo"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
)

var REASON_MAPPING = map[string]string{
	"date_of_birth":    "Date of birth",
	"entry_valid_date": "Entry valid date",
	"gender":           "Gender",
	"incorrect_name":   "Incorrect name",
	// "expiration_date":   "Passport expired date",
	"passport_number":   "Passport number",
	"visa_expired_date": "Visa expired date",
	"expiration_date":   "Expiration date",
}

var URGENT_PRODUCT_NAMES = []string{
	"VNM eVisa Urgent New",
	"VNM eVisa Urgent New Boarding",
	"VNM eVisa Urgent Expedite",
	"VNM eVisa Urgent Expedite Boarding",
	"VNM eVisa Urgent Fix",
	"VNM eVisa Urgent Fix Boarding",
}

func NotifyViaZaloForUrgentVisa(dao db.IEtsDao, orderID int64) error {
	orders, err := dao.GetServiceOrders(map[string][]any{"id": {orderID}})
	if err != nil {
		return err
	}
	order := orders[0]

	tasks, _, err := dao.QueryServiceTasks(map[string]any{"order_id": order.ID}, 0, 0)
	if err != nil {
		return err
	}

	svcs, err := dao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return err
	}
	svc := svcs[0]
	// attrJ := utils.StructToJSON(svc.Attributes)
	servicePrices, err := dao.GetServicePrice([]int{order.ServiceID})
	if err != nil {
		return err
	}
	servicePrice := servicePrices[order.ServiceID][0]
	provider, err := dao.GetEtsProviderByID(servicePrice.ProviderID)
	if err != nil {
		return err
	}

	if funk.ContainsString(URGENT_PRODUCT_NAMES, cast.ToString(svc.Name)) {
		zaloGroup := provider.Contact.ZaloGroup
		if zaloGroup != "" {
			message := []string{}

			if cast.ToString(svc.Name) == "VNM eVisa Urgent New" || cast.ToString(svc.Name) == "VNM eVisa Urgent New Boarding" {
				message = append(message, fmt.Sprintf(`Order#<b>%d</b> - Request for <b>NEW eVisa</b> service as below:`, order.ID))
			}
			if cast.ToString(svc.Name) == "VNM eVisa Urgent Expedite" || cast.ToString(svc.Name) == "VNM eVisa Urgent Expedite Boarding" {
				message = append(message, fmt.Sprintf(`Order#<b>%d</b> - Request for  <b>EXPEDITE eVisa</b> service as below:`, order.ID))
			}
			if cast.ToString(svc.Name) == "VNM eVisa Urgent Fix" || cast.ToString(svc.Name) == "VNM eVisa Urgent Fix Boarding" {
				message = append(message, fmt.Sprintf(`Order#<b>%d</b> - Request for <b>Fix eVisa</b> service as below:`, order.ID))
			}

			if order.SubmittedTime == nil {
				order.SubmittedTime = aws.Time(time.Now())
			}
			newBoardingTime, _ := dao.VisaDB().DB().GetProcessingTimeWithInput(svc.ID, *order.SubmittedTime, "2h")

			if err := dao.UpdateServiceOrder(map[string]any{"expect_issue_visa_at": newBoardingTime}, order.ID); err != nil {
				return err
			}

			// message = append(message, fmt.Sprintf(`Issues visa by: <b>%s</b>`, localBoardingTime.Format("Jan 02, 2006 - 15:04")))
			message = append(message, fmt.Sprintf(`Issues visa by: <b><span style="color:#ff0000">%s</span></b>`, newBoardingTime.Format("Jan 02, 2006 - 15:04")))

			orderInputPodValues := order.InputPods.ToMapKeyValue()
			if val := cast.ToString(orderInputPodValues["travel_enter_flight_enter_timestamp"]); val != "" {
				message = append(message, fmt.Sprintf(`Arrival date: <b>%s</b>`, time_util.LocalTimeAsUTC(cast.ToTime(val)).Format("Jan 02, 2006 - 15:04")))
			}

			webBaseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("web_base_url").String()
			message = append(message, fmt.Sprintf("Update Visa here: %s/upload-visa", webBaseURL))

			files := []string{}
			for i, task := range tasks {
				inputPodValues := task.InputPods.ToMapKeyValueV2(order.InputPods)
				outputPodValues := task.OutputPods.ToMapKeyValue()

				if val := outputPodValues["application_application_info_registration_code"]; val != "" {
					message = append(message, fmt.Sprintf(`Registration code #%d: <b>%s</b>`, i+1, cast.ToString(val)))
				}

				if val := cast.ToStringSlice(inputPodValues["travel_visa_info_evisa_error"]); len(val) > 0 {
					reasons := []string{}
					for _, reason := range val {
						if reasonL, ok := REASON_MAPPING[reason]; ok {
							reasons = append(reasons, reasonL)
						} else {
							reasons = append(reasons, reason)
						}
					}
					message = append(message, fmt.Sprintf(`Problem #%d: <b><span style="color:#ff0000">%s</span></b>`, i+1, strings.Join(reasons, ", ")))
				}
				if val := cast.ToStringSlice(inputPodValues["travel_visa_info_proof_of_evisa_issued"]); len(val) > 0 {
					files = append(files, val...)
				}

				if val := cast.ToStringSlice(inputPodValues["document_copy_of_passport_copy_of_passport_main_page"]); len(val) > 0 {
					files = append(files, val...)
				}
			}
			zalo.SendZaloSMS(zaloGroup, strings.Join(message, "\n"))
			zalo.SendZaloSMS(zalo.GetGroupID("INTERNAL_NOTIFICATION"), strings.Join(message, "\n"))
			for _, file := range files {
				zalo.SendZaloFile(zaloGroup, file)
				zalo.SendZaloFile(zalo.GetGroupID("INTERNAL_NOTIFICATION"), file)
			}
		}
	}
	return nil
}

func NotifyViaZaloForRemindUrgentVisa(dao db.IEtsDao, orderID int64) error {
	orders, err := dao.GetServiceOrders(map[string][]any{"id": {orderID}})
	if err != nil {
		return err
	}
	order := orders[0]

	tasks, _, err := dao.QueryServiceTasks(map[string]any{"order_id": order.ID}, 0, 0)
	if err != nil {
		return err
	}

	svcs, err := dao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return err
	}
	svc := svcs[0]
	// attrJ := utils.StructToJSON(svc.Attributes)
	servicePrices, err := dao.GetServicePrice([]int{order.ServiceID})
	if err != nil {
		return err
	}
	servicePrice := servicePrices[order.ServiceID][0]
	provider, err := dao.GetEtsProviderByID(servicePrice.ProviderID)
	if err != nil {
		return err
	}

	if funk.ContainsString(URGENT_PRODUCT_NAMES, cast.ToString(svc.Name)) {
		zaloGroup := provider.Contact.ZaloGroup
		if zaloGroup != "" {
			message := []string{}

			if cast.ToString(svc.Name) == "VNM eVisa Urgent New" || cast.ToString(svc.Name) == "VNM eVisa Urgent New Boarding" {
				message = append(message, fmt.Sprintf(`[<b><span style="color:#f27806">REMINDER</span></b>] Order#<b>%d</b> - Request for <b>NEW eVisa</b> service as below:`, order.ID))
			}
			if cast.ToString(svc.Name) == "VNM eVisa Urgent Expedite" || cast.ToString(svc.Name) == "VNM eVisa Urgent Expedite Boarding" {
				message = append(message, fmt.Sprintf(`[<b><span style="color:#f27806">REMINDER</span></b>] Order#<b>%d</b> - Request for  <b>EXPEDITE eVisa</b> service as below:`, order.ID))
			}
			if cast.ToString(svc.Name) == "VNM eVisa Urgent Fix" || cast.ToString(svc.Name) == "VNM eVisa Urgent Fix Boarding" {
				message = append(message, fmt.Sprintf(`[<b><span style="color:#f27806">REMINDER</span></b>] Order#<b>%d</b> - Request for <b>Fix eVisa</b> service as below:`, order.ID))
			}

			if order.ExpectIssueVisaAt == nil {
				order.ExpectIssueVisaAt = aws.Time(time.Now())
			}

			message = append(message, fmt.Sprintf(`Issues visa by: <b><span style="color:#ff0000">%s</span></b>`, order.ExpectIssueVisaAt.Format("Jan 02, 2006 - 15:04")))

			orderInputPodValues := order.InputPods.ToMapKeyValue()
			if val := cast.ToString(orderInputPodValues["travel_enter_flight_enter_timestamp"]); val != "" {
				message = append(message, fmt.Sprintf(`Arrival date: <b>%s</b>`, time_util.LocalTimeAsUTC(cast.ToTime(val)).Format("Jan 02, 2006 - 15:04")))
			}

			webBaseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("web_base_url").String()
			message = append(message, fmt.Sprintf("Update Visa here: %s/upload-visa", webBaseURL))

			for i, task := range tasks {
				inputPodValues := task.InputPods.ToMapKeyValueV2(order.InputPods)
				outputPodValues := task.OutputPods.ToMapKeyValue()

				if val := outputPodValues["application_application_info_registration_code"]; val != "" {
					message = append(message, fmt.Sprintf(`Registration code #%d: <b>%s</b>`, i+1, cast.ToString(val)))
				}

				if val := cast.ToStringSlice(inputPodValues["travel_visa_info_evisa_error"]); len(val) > 0 {
					reasons := []string{}
					for _, reason := range val {
						if reasonL, ok := REASON_MAPPING[reason]; ok {
							reasons = append(reasons, reasonL)
						} else {
							reasons = append(reasons, reason)
						}
					}
					message = append(message, fmt.Sprintf(`Problem #%d: <b><span style="color:#ff0000">%s</span></b>`, i+1, strings.Join(reasons, ", ")))
				}
			}
			zalo.SendZaloSMS(zaloGroup, strings.Join(message, "\n"))
			zalo.SendZaloSMS(zalo.GetGroupID("INTERNAL_NOTIFICATION"), strings.Join(message, "\n"))
		}
	}
	return nil
}

func NotifyViaZaloForCancelUrgentVisa(dao db.IEtsDao, orderID int64, taskIDs []int64) error {
	orders, err := dao.GetServiceOrders(map[string][]any{"id": {orderID}})
	if err != nil {
		return err
	}
	order := orders[0]

	tasks, _, err := dao.QueryServiceTasks(map[string]any{"order_id": order.ID}, 0, 0)
	if err != nil {
		return err
	}

	svcs, err := dao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return err
	}
	svc := svcs[0]
	// attrJ := utils.StructToJSON(svc.Attributes)
	servicePrices, err := dao.GetServicePrice([]int{order.ServiceID})
	if err != nil {
		return err
	}
	servicePrice := servicePrices[order.ServiceID][0]
	provider, err := dao.GetEtsProviderByID(servicePrice.ProviderID)
	if err != nil {
		return err
	}

	if funk.ContainsString(URGENT_PRODUCT_NAMES, cast.ToString(svc.Name)) {
		zaloGroup := provider.Contact.ZaloGroup
		if zaloGroup != "" {
			message := []string{}

			if cast.ToString(svc.Name) == "VNM eVisa Urgent New" || cast.ToString(svc.Name) == "VNM eVisa Urgent New Boarding" {
				message = append(message, fmt.Sprintf(`<b><span style="color:#ff0000">[CANCELLED]</span></b> Order#<b>%d</b> - Request for <b>NEW eVisa</b> service as below:`, order.ID))
			}
			if cast.ToString(svc.Name) == "VNM eVisa Urgent Expedite" || cast.ToString(svc.Name) == "VNM eVisa Urgent Expedite Boarding" {
				message = append(message, fmt.Sprintf(`<b><span style="color:#ff0000">[CANCELLED]</span></b> Order#<b>%d</b> - Request for  <b>EXPEDITE eVisa</b> service as below:`, order.ID))
			}
			if cast.ToString(svc.Name) == "VNM eVisa Urgent Fix" || cast.ToString(svc.Name) == "VNM eVisa Urgent Fix Boarding" {
				message = append(message, fmt.Sprintf(`<b><span style="color:#ff0000">[CANCELLED]</span></b> Order#<b>%d</b> - Request for <b>Fix eVisa</b> service as below:`, order.ID))
			}

			for i, task := range tasks {
				inputPodValues := task.InputPods.ToMapKeyValueV2(order.InputPods)
				outputPodValues := task.OutputPods.ToMapKeyValueV2(order.InputPods)
				if i == 0 {
					reason := gjson.ParseBytes(order.Note).Get("cancelled.0.message").String()
					if reason == "" {
						reason = gjson.ParseBytes(task.Note).Get("cancelled.0.message").String()
					}
					message = append(message, fmt.Sprintf(`Reason: <b>%s</b>`, reason))

					if order.SubmittedTime == nil {
						order.SubmittedTime = aws.Time(time.Now())
					}
					newBoardingTime, _ := dao.VisaDB().DB().GetProcessingTimeWithInput(svc.ID, *order.SubmittedTime, "2h")
					message = append(message, fmt.Sprintf(`Issues visa by: <b><span style="color:#ff0000">%s</span></b>`, newBoardingTime.Format("Jan 02, 2006 - 15:04")))

				}
				if val := outputPodValues["application_application_info_registration_code"]; val != "" {
					message = append(message, fmt.Sprintf(`Registration code #%d: <b>%s</b>`, i+1, cast.ToString(val)))
				}

				if val := cast.ToStringSlice(inputPodValues["travel_visa_info_evisa_error"]); len(val) > 0 {
					reasons := []string{}
					for _, reason := range val {
						if reasonL, ok := REASON_MAPPING[reason]; ok {
							reasons = append(reasons, reasonL)
						} else {
							reasons = append(reasons, reason)
						}
					}

					message = append(message, fmt.Sprintf(`Problem #%d: <b><span style="color:#ff0000">%s</span></b>`, i+1, strings.Join(reasons, ", ")))
				}
				message = append(message, fmt.Sprintf(`Arrival date: <b>%s</b>`, time_util.LocalTimeAsUTC(cast.ToTime(inputPodValues["travel_enter_flight_enter_timestamp"])).Format("Jan 02, 2006 - 15:04")))
			}
			zalo.SendZaloSMS(zaloGroup, strings.Join(message, "\n"))
			zalo.SendZaloSMS(zalo.GetGroupID("INTERNAL_NOTIFICATION"), strings.Join(message, "\n"))
		}
	}
	return nil
}

func NotifyViaZaloForUpdateUrgentVisa(dao db.IEtsDao, orderID, taskID int64, firstNotificatin bool) error {
	orders, err := dao.GetServiceOrders(map[string][]any{"id": {orderID}})
	if err != nil {
		return err
	}
	order := orders[0]

	tasks, _, err := dao.QueryServiceTasks(map[string]any{"order_id": order.ID}, 0, 0)
	if err != nil {
		return err
	}

	task := funk.Find(tasks, func(task *models.ServiceTask) bool {
		return task.ID == taskID
	}).(*models.ServiceTask)

	svcs, err := dao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return err
	}
	svc := svcs[0]
	// attrJ := utils.StructToJSON(svc.Attributes)
	servicePrices, err := dao.GetServicePrice([]int{order.ServiceID})
	if err != nil {
		return err
	}
	servicePrice := servicePrices[order.ServiceID][0]
	provider, err := dao.GetEtsProviderByID(servicePrice.ProviderID)
	if err != nil {
		return err
	}

	if funk.ContainsString(URGENT_PRODUCT_NAMES, cast.ToString(svc.Name)) {
		zaloGroup := provider.Contact.ZaloGroup
		if zaloGroup != "" {
			message := []string{}
			prefix := ""
			if !firstNotificatin {
				prefix = `<b><span style="color:#ff0000">[UPDATE REGISTRATION CODE]</span></b> `
			}
			if cast.ToString(svc.Name) == "VNM eVisa Urgent New" || cast.ToString(svc.Name) == "VNM eVisa Urgent New Boarding" {
				message = append(message, fmt.Sprintf(`%sOrder#<b>%d</b> - Request for <b>NEW eVisa</b> service as below:`, prefix, order.ID))
			}
			if cast.ToString(svc.Name) == "VNM eVisa Urgent Expedite" || cast.ToString(svc.Name) == "VNM eVisa Urgent Expedite Boarding" {
				message = append(message, fmt.Sprintf(`%sOrder#<b>%d</b> - Request for  <b>EXPEDITE eVisa</b> service as below:`, prefix, order.ID))
			}
			if cast.ToString(svc.Name) == "VNM eVisa Urgent Fix" || cast.ToString(svc.Name) == "VNM eVisa Urgent Fix Boarding" {
				message = append(message, fmt.Sprintf(`%sOrder#<b>%d</b> - Request for <b>Fix eVisa</b> service as below:`, prefix, order.ID))
			}

			inputPodValues := task.InputPods.ToMapKeyValueV2(order.InputPods)
			outputPodValues := task.OutputPods.ToMapKeyValueV2(order.InputPods)

			newBoardingTime, _ := dao.VisaDB().DB().GetProcessingTimeWithInput(svc.ID, time.Now(), "2h")
			message = append(message, fmt.Sprintf(`Issues visa by: <b><span style="color:#ff0000">%s</span></b>`, newBoardingTime.Format("Jan 02, 2006 - 15:04")))

			if val := outputPodValues["application_application_info_registration_code"]; val != "" {
				message = append(message, fmt.Sprintf(`Registration code #%d: <b>%s</b>`, 1, cast.ToString(val)))
			}

			if val := cast.ToStringSlice(inputPodValues["travel_visa_info_evisa_error"]); len(val) > 0 {
				reasons := []string{}
				for _, reason := range val {
					if reasonL, ok := REASON_MAPPING[reason]; ok {
						reasons = append(reasons, reasonL)
					} else {
						reasons = append(reasons, reason)
					}
				}

				message = append(message, fmt.Sprintf(`Problem #%d: <b><span style="color:#ff0000">%s</span></b>`, 1, strings.Join(reasons, ", ")))
			}

			webBaseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("web_base_url").String()
			message = append(message, fmt.Sprintf(`Arrival date: <b>%s</b>`, time_util.LocalTimeAsUTC(cast.ToTime(inputPodValues["travel_enter_flight_enter_timestamp"])).Format("Jan 02, 2006 - 15:04")))
			message = append(message, fmt.Sprintf("Update Visa here: %s/upload-visa", webBaseURL))

			zalo.SendZaloSMS(zaloGroup, strings.Join(message, "\n"))
			zalo.SendZaloSMS(zalo.GetGroupID("INTERNAL_NOTIFICATION"), strings.Join(message, "\n"))
		}
	}
	return nil
}

func NotifyViaZaloForUpdateEVisaProofFile(dao db.IEtsDao, orderID, taskID int64) error {
	orders, err := dao.GetServiceOrders(map[string][]any{"id": {orderID}})
	if err != nil {
		return err
	}
	order := orders[0]

	tasks, _, err := dao.QueryServiceTasks(map[string]any{"order_id": order.ID}, 0, 0)
	if err != nil {
		return err
	}

	task := funk.Find(tasks, func(task *models.ServiceTask) bool {
		return task.ID == taskID
	}).(*models.ServiceTask)

	svcs, err := dao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return err
	}
	svc := svcs[0]
	// attrJ := utils.StructToJSON(svc.Attributes)
	servicePrices, err := dao.GetServicePrice([]int{order.ServiceID})
	if err != nil {
		return err
	}
	servicePrice := servicePrices[order.ServiceID][0]
	provider, err := dao.GetEtsProviderByID(servicePrice.ProviderID)
	if err != nil {
		return err
	}

	if funk.ContainsString(URGENT_PRODUCT_NAMES, cast.ToString(svc.Name)) {
		zaloGroup := provider.Contact.ZaloGroup
		if zaloGroup != "" {

			inputPodValues := task.InputPods.ToMapKeyValueV2(order.InputPods)
			files := []string{}
			if val := cast.ToStringSlice(inputPodValues["travel_visa_info_proof_of_evisa_issued"]); len(val) > 0 {
				files = append(files, val...)
			}
			for _, file := range files {
				zalo.SendZaloFile(zaloGroup, file)
				zalo.SendZaloFile(zalo.GetGroupID("INTERNAL_NOTIFICATION"), file)
			}
		}
	}
	return nil
}
