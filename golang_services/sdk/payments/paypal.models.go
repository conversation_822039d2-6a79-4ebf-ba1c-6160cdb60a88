package payments

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
	"time"
)

// PayPalClient paypal client
type PayPalClient struct {
	APIKey      string
	APISecret   string
	APIBase     string
	ReturnURL   string
	CancelURL   string
	WebhookID   string
	CallbackURL string
	Client      *http.Client
	Token       *PayPalToken
}

// PayPalToken Paypal token
type PayPalToken struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
	ExpiredAt   time.Time
}

type (
	// PayPalTransaction paypal transaction
	PayPalTransaction struct {
		ID                string                  `json:"id,omitempty"`
		State             string                  `json:"state,omitempty"`
		Payer             PayPalPayer             `json:"payer"`
		Intent            string                  `json:"intent"`
		Transactions      []PayPalTransactionUnit `json:"transactions"`
		PayPalRedirectURL PayPalRedirectURL       `json:"redirect_urls"`
		Links             []PaypalLink            `json:"links,omitempty"`
	}
	// PayPalTransactionUnit purchase_units
	PayPalTransactionUnit struct {
		Amount        *PurchaseUnitAmount `json:"amount"`
		InvoiceNumber string              `json:"invoice_number,omitempty"`
		Description   string              `json:"description,omitempty"`
	}
	// PurchaseUnitAmount amount
	PurchaseUnitAmount struct {
		Currency string `json:"currency"`
		Total    string `json:"total"`
	}
	// PayPalPayer payer
	PayPalPayer struct {
		PaymentMethod string `json:"payment_method"`
	}
	// PayPalRedirectURL redirect url
	PayPalRedirectURL struct {
		ReturnURL string `json:"return_url"`
		CancelURL string `json:"cancel_url"`
	}
	// PaypalLink link
	PaypalLink struct {
		Href string `json:"href"`
		Rel  string `json:"rel"`
	}
)

// Send makes a request using the basic auth or bearer token
func (p *PayPalClient) Send(req *http.Request, withToken bool, v any) error {
	// Set default headers
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Accept-Language", "en_US")

	// Default values for headers
	if req.Header.Get("Content-type") == "" {
		req.Header.Set("Content-type", "application/json")
	}

	// Set token
	if withToken {
		if p.Token == nil || p.Token.ExpiredAt.Before(time.Now()) {
			if err := p.GetAccessToken(); err != nil {
				return err
			}
		}
		req.Header.Set("Authorization", "Bearer "+p.Token.AccessToken)
	} else {
		req.SetBasicAuth(p.APIKey, p.APISecret)
	}

	resp, err := p.Client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	buff, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	return json.Unmarshal(buff, v)
}
