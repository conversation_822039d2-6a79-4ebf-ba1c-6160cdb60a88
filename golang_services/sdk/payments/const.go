package payments

import "time"

const (
	Alipay            = "ALIPAY"
	WechatPay         = "WECHATPAY"
	AuthorizePay      = "AUTHORIZENET"
	AuthorizeInDirect = "AUTHORIZENET_INDIRECT"
	OnePay            = "ONEPAY"
	PayPal            = "PAYPAL"
	PayPalInDirect    = "PAYPAL_INDIRECT"
	ZellePay          = "ZELLEPAY"
	BankPay           = "BANKPAY"
	CorpPay           = "CORPPAY"
	Stripe            = "STRIPE"

	alipaySubject = "Aria Visa Service"

	//authorize.net
	defaultHTTPTimeout = 80 * time.Second
)

var TransactionFee = map[string]float64{
	AuthorizePay:      0.0625,
	AuthorizeInDirect: 0.0625,
	OnePay:            0.00,
	PayPal:            0.0625,
	PayPalInDirect:    0.0625,
	Stripe:            0.00,
}
