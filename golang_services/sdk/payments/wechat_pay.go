package payments

import (
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/skip2/go-qrcode"
	"github.com/smartwalle/wxpay"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

const (
	// dummy IP here. if we use VPC and NAT gateway, outgoing traffic should be one IP,
	// we can hard code it or set when start application
	hostIP = "************"
)

type WechatPayClient struct {
	client      *wxpay.Client
	location    *time.Location
	apiKey      string
	AppID       string
	MerChantID  string
	Description string
}

func NewWechatPayClient(appID, apiKey, mchID string, isProd bool) *WechatPayClient {
	c := wxpay.New(appID, apiKey, mchID, isProd)
	loc := time.FixedZone("Asia/Shanghai", 8*24*3600)
	return &WechatPayClient{client: c, location: loc, apiKey: apiKey, AppID: appID, MerChantID: mchID,
		Description: "aria direct wechat order"}
}

func (c *WechatPayClient) PlaceOrder(req *PlaceOrderRequest) (*PlaceOrderResponse, error) {
	if req == nil {
		return nil, nil
	}
	typ := ""
	switch strings.ToUpper(req.Type) {
	case "NATIVE", "APP":
		typ = strings.ToUpper(req.Type)
	case "":
		typ = "APP"
	default:
		return nil, fmt.Errorf("unsupported wechat payment type, %s", req.Type)
	}

	timeStart := time.Now().In(c.location)
	timeExp := timeStart.Add(24 * time.Hour)
	param := wxpay.UnifiedOrderParam{
		AppId:          c.AppID,
		Body:           fmt.Sprintf("%s %s", c.Description, req.Description),
		NotifyURL:      req.NotifyURL,
		OutTradeNo:     req.AriaTransactionID,
		TotalFee:       int(req.TotalAmount * 100), // wechat needs amount in cents, do conversion here
		SpbillCreateIP: hostIP,
		TradeType:      typ,
		SignType:       "MD5",
		FeeType:        "CNY",
		TimeStart:      timeStart.Format("**************"),
		TimeExpire:     timeExp.Format("**************"),
	}
	log.Info().Interface("order_param", param).Msg("wechat preorder")
	res, err := c.client.UnifiedOrder(param)
	if err != nil {
		return nil, err
	}

	if res.ResultCode != wxpay.K_RETURN_CODE_SUCCESS {
		return nil, fmt.Errorf("wechat prepay failed, result: %v", res)
	}

	ret := &PlaceOrderResponse{
		AppID:             res.AppId,
		AccountID:         res.MCHId,
		AriaTransactionID: req.AriaTransactionID,
		OrderID:           res.PrepayId,
		TotalAmount:       req.TotalAmount,
	}

	switch strings.ToUpper(req.Type) {
	case "NATIVE":
		return c.genQRCode(ret, res.CodeURL)
	default:
		ret = c.signResponse(ret, timeStart)
	}
	return ret, nil
}

func (c WechatPayClient) genQRCode(resp *PlaceOrderResponse, codeURL string) (*PlaceOrderResponse, error) {
	if codeURL == "" {
		return resp, fmt.Errorf("code url returned from wechat is empty")
	}
	var png []byte
	png, err := qrcode.Encode(codeURL, qrcode.Medium, 512)
	if err != nil {
		return resp, err
	}

	encodedPng := base64.StdEncoding.EncodeToString(png)
	if len(resp.Extra) == 0 {
		resp.Extra = map[string]any{
			"code_url": codeURL,
			"qr_code":  encodedPng,
		}
	} else {
		resp.Extra["code_url"] = codeURL
		resp.Extra["qr_code"] = encodedPng
	}
	return resp, nil
}

func (c *WechatPayClient) signResponse(resp *PlaceOrderResponse, t time.Time) *PlaceOrderResponse {
	randomStr := wxpay.GetNonceStr()
	val := url.Values{}
	val.Add("appid", resp.AppID)
	val.Add("partnerid", resp.AccountID)
	val.Add("prepayid", resp.OrderID)
	val.Add("package", "Sign=WXPay")
	val.Add("noncestr", randomStr)
	val.Add("timestamp", fmt.Sprintf("%d", t.Unix()))

	signature := c.client.SignMD5(val)
	resp.Signature = signature
	resp.Extra = map[string]any{
		"noncestr":  randomStr,
		"timestamp": t.Unix(),
	}
	return resp
}

func (c *WechatPayClient) GetOrderInfo(orderID, ariaTransactionID string) (*OrderInfo, error) {
	param := wxpay.OrderQueryParam{
		TransactionId: orderID,
		OutTradeNo:    ariaTransactionID,
	}

	res, err := c.client.OrderQuery(param)
	if err != nil {
		return nil, err
	}
	if res.ReturnCode != wxpay.K_RETURN_CODE_SUCCESS {
		return nil, fmt.Errorf("failed to query wechat order, response %v", res)
	}
	return &OrderInfo{
		TransactionID: res.TransactionId,
		Status:        convertWXStatus(res.TradeState),
		TotalAmount:   float32(res.TotalFee) / 100.0, // wechat sends amount in cents
		Currency:      res.FeeType,
	}, nil
}

func convertWXStatus(tradeState string) string {
	switch tradeState {
	case wxpay.K_TRADE_STATE_SUCCESS:
		return models.PaymentStatusSuccess
	case wxpay.K_TRADE_STATE_USERPAYING, wxpay.K_TRADE_STATE_NOTPAY:
		return models.PaymentStatusPending
	case wxpay.K_TRADE_STATE_CLOSED, wxpay.K_TRADE_STATE_REFUND:
		return models.PaymentStatusClosed
	default:
		return models.PaymentStatusFailed
	}
}

func (c *WechatPayClient) GetType() string {
	return WechatPay
}

func (c *WechatPayClient) ReadNotification(req *http.Request) (*models.Payment, error) {
	notif, err := c.client.GetTradeNotification(req)
	if err != nil {
		return nil, err
	}
	status := models.PaymentStatusFailed
	if notif.ResultCode == wxpay.K_RETURN_CODE_SUCCESS {
		status = models.PaymentStatusSuccess
	}

	p := &models.Payment{
		ID:                    notif.OutTradeNo,
		Status:                status,
		ExternalTransactionID: notif.TransactionId,
		Total:                 float64(notif.TotalFee) / 100.0,
		Properties: &models.PropertyMap{
			"notification": notif,
		},
	}
	return p, nil
}

func (c *WechatPayClient) AckNotification(resp http.ResponseWriter) {
	c.client.AckNotification(resp)
}

// GetOrderList get order list
func (c *WechatPayClient) GetOrderList(req OrderListRequest) ([]OrderInfo, error) {
	// TODO: not implement
	return nil, nil
}
