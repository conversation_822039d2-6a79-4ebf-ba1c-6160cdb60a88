package payments

import (
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

type BankPayClient struct {
	ReceiverInfo map[string]any
}

func NewBankPayClient() *BankPayClient {
	return &BankPayClient{}
}

// PlaceOrder place order
func (z *BankPayClient) PlaceOrder(req *PlaceOrderRequest) (*PlaceOrderResponse, error) {
	return &PlaceOrderResponse{
		AriaTransactionID: req.AriaTransactionID,
		TotalAmount:       req.TotalAmount,
		OrderID:           req.AriaTransactionID,
		Extra: map[string]any{
			"receiver": z.ReceiverInfo,
		},
	}, nil
}

// GetOrderInfo get order info
func (z *BankPayClient) GetOrderInfo(orderID, ariaTransactionID string) (*OrderInfo, error) {
	return nil, nil
}

// GetType get type
func (z *BankPayClient) GetType() string {
	return BankPay
}

// ReadNotification read notification
func (z *BankPayClient) ReadNotification(req *http.Request) (*models.Payment, error) {
	return nil, nil
}

// AckNotification ack notification
func (z *BankPayClient) AckNotification(resp http.ResponseWriter) {
	resp.WriteHeader(http.StatusOK)
}

// GetOrderList get order list
func (z *BankPayClient) GetOrderList(req OrderListRequest) ([]OrderInfo, error) {
	return nil, nil
}
