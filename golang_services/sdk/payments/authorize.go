package payments

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/rs/zerolog/log"
)

func NewAuthorizeClient(apiName, apiKey, apiMode, apiEndpoint, formPost, webhookID, webhookSign, urlCallback, urlRedirect, cancelRedirect string) *AuthorizeClient {
	return &AuthorizeClient{
		APIKey:         apiKey,
		APIName:        apiName,
		Endpoint:       apiEndpoint,
		Mode:           apiMode,
		Client:         &http.Client{Timeout: defaultHTTPTimeout},
		FormPost:       formPost,
		WebhookID:      webhookID,
		WebhookSign:    webhookSign,
		URLCallback:    urlCallback,
		URLRedirect:    urlRedirect,
		CancelRedirect: cancelRedirect,
	}
}
func (c *AuthorizeClient) GetAuthentication() MerchantAuthentication {
	auth := MerchantAuthentication{
		Name:           c.<PERSON>,
		TransactionKey: c.<PERSON>,
	}
	return auth
}
func (c *AuthorizeClient) PlaceOrder(req *PlaceOrderRequest) (*PlaceOrderResponse, error) {
	if req == nil {
		return nil, nil
	}
	if req.Description == "" {
		if req.CartID > 0 {
			req.Description = fmt.Sprintf("AriaDirect payment for payment #%d", req.CartID)
		} else {
			req.Description = "AriaDirect payment"
		}
	}
	action := GetHosted{
		GetHostedPaymentPageRequest: GetHostedPaymentPageRequest{
			MerchantAuthentication: c.GetAuthentication(),
			TransactionRequest: TransactionRequest{
				TransactionType: "authCaptureTransaction",
				Amount:          fmt.Sprintf("%.2f", req.TotalAmount),
				Order: Order{
					InvoiceNumber: req.AriaTransactionID,
					Description:   req.Description,
				},
				Customer: Customer{
					Email: req.Email,
				},
			},
			HostedPaymentSettings: HostedPaymentSettings{
				Setting: []Setting{
					{
						SettingName:  "hostedPaymentReturnOptions",
						SettingValue: fmt.Sprintf("{\"showReceipt\": true, \"url\":\"%s\", \"cancelUrl\":\"%s\"}", c.URLRedirect, c.CancelRedirect),
					},
					{
						SettingName:  "hostedPaymentButtonOptions",
						SettingValue: "{\"text\": \"Pay\"}",
					},
					{
						SettingName:  "hostedPaymentOrderOptions",
						SettingValue: "{\"show\": true, \"merchantName\": \"AriaDirect\"}",
					},
					{
						SettingName:  "hostedPaymentBillingAddressOptions",
						SettingValue: "{\"show\": true, \"required\": true}",
					},
					{
						SettingName:  "hostedPaymentPaymentOptions",
						SettingValue: "{\"showBankAccount\": false}",
					},
				},
			},
		},
	}
	jsoned, err := json.Marshal(action)
	if err != nil {
		return nil, err
	}
	fmt.Print(string(jsoned))
	statusCode, body, err := c.SendRequest(jsoned)
	if statusCode >= 400 {
		return nil, fmt.Errorf("authorized failed request")
	}
	fmt.Print(string(body))
	var dat GetHostedResponse
	err = json.Unmarshal(body, &dat)
	if err != nil {
		return nil, err
	}
	if dat.Messages.ResultCode != "Ok" {
		return nil, fmt.Errorf(dat.Messages.Message[0].Text)
	}
	r := &PlaceOrderResponse{
		TotalAmount: req.TotalAmount,
		OrderID:     req.AriaTransactionID,
		Signature:   dat.Token,
		Extra: map[string]any{
			"paymentUrl": c.FormPost,
		},
	}
	return r, nil
}
func (c *AuthorizeClient) SendRequest(input []byte) (int, []byte, error) {
	req, err := http.NewRequest("POST", c.Endpoint, bytes.NewBuffer(input))
	req.Header.Set("Content-Type", "application/json")
	client := c.Client
	resp, err := client.Do(req)
	if err != nil {
		return 0, nil, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return 0, nil, err
	}
	body = bytes.TrimPrefix(body, []byte("\xef\xbb\xbf"))

	return resp.StatusCode, body, err
}

// SendWebhookRequest send webhook notification
func (c *AuthorizeClient) SendWebhookRequest(prefix, method string, input []byte) ([]byte, error) {
	req, err := http.NewRequest(method, fmt.Sprintf("%s/%s", c.WebhookURL, prefix), bytes.NewBuffer(input))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")

	token := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", c.APIName, c.APIKey)))

	req.Header.Set("Authorization", "Basic "+token)
	client := c.Client
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Do webhook request fail! ")
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	body = bytes.TrimPrefix(body, []byte("\xef\xbb\xbf"))

	return body, nil
}

func (c *AuthorizeClient) GetType() string {
	return AuthorizePay
}

func convertAuthorizeStatus(tradeStatus int) string {
	switch tradeStatus {
	case AUTHORIZE_STATUS_APPROVED:
		return models.PaymentStatusSuccess
	case AUTHORIZE_STATUS_HELD_FOR_REVIEW:
		return models.PaymentStatusPending
	default:
		return models.PaymentStatusFailed
	}
}

func (c *AuthorizeClient) ReadNotification(req *http.Request) (*models.Payment, error) {
	// verify info that webhook return
	notif, err := ioutil.ReadAll(req.Body)
	if err != nil {
		return nil, err
	}
	fmt.Print(string(notif))
	log.Debug().Str("json", string(notif)).Msg("AuthorizeClient.ReadNotification notification request")

	var bodyNotif WebhookNotifRequest
	err = json.Unmarshal(notif, &bodyNotif)
	if err != nil {
		return nil, err
	}
	resp, err := c.GetTransactionDetails(bodyNotif.Payload.Id)
	if err != nil {
		return nil, err
	}

	var notification map[string]any
	json.Unmarshal(notif, &notification)

	p := &models.Payment{
		ID:                    resp.Transaction.Order.InvoiceNumber,
		Status:                convertAuthorizeStatus(resp.Transaction.ResponseCode),
		ExternalTransactionID: bodyNotif.Payload.Id,
		Total:                 bodyNotif.Payload.AuthAmount,
		Properties: &models.PropertyMap{
			"notification": notification,
		},
	}
	return p, nil
}

func (c *AuthorizeClient) GetTransactionDetails(transactionId string) (*GetTransactionDetailsResponse, error) {
	action := GetTransaction{
		GetTransactionDetailsRequest{
			MerchantAuthentication: c.GetAuthentication(),
			TransId:                transactionId,
		},
	}
	jsoned, err := json.Marshal(action)
	if err != nil {
		return nil, err
	}

	log.Debug().Str("json", string(jsoned)).Msg("AuthorizeClient.GetTransactionDetails request")

	statusCode, body, err := c.SendRequest(jsoned)
	if statusCode >= 400 {
		return nil, fmt.Errorf("authorized failed request")
	}
	fmt.Print(string(body))
	var dat GetTransactionDetailsResponse
	err = json.Unmarshal(body, &dat)
	if err != nil {
		return nil, err
	}
	if dat.Messages.ResultCode == "Error" {
		detailError, err := json.Marshal(dat.Messages)
		if err != nil {
			return nil, err
		}
		return nil, fmt.Errorf(string(detailError))
	}
	return &dat, err
}

func (c *AuthorizeClient) AckNotification(w http.ResponseWriter) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("success"))
}

func (c *AuthorizeClient) GetOrderInfo(orderID, ariaTransactionID string) (*OrderInfo, error) {
	request := GetTransaction{
		GetTransactionDetailsRequest{
			MerchantAuthentication: c.GetAuthentication(),
			TransId:                orderID,
		},
	}

	jsoned, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	statusCode, body, err := c.SendRequest(jsoned)
	if statusCode >= 400 {
		return nil, fmt.Errorf("authorized failed request")
	}

	var dat TransactionDetailsResponse
	err = json.Unmarshal(body, &dat)
	if err != nil {
		return nil, err
	}

	var order = &OrderInfo{
		TransactionID: dat.Transaction.TransID,
		TotalAmount:   float32(dat.Transaction.AuthAmount),
		Currency:      "USD",
	}

	switch dat.Transaction.TransactionStatus {
	case "settledSuccessfully":
		order.Status = models.PaymentStatusSuccess
	case "failedReview", "settlementError":
		order.Status = models.PaymentStatusFailed
	default:
		order.Status = models.PaymentStatusPending
	}
	return order, nil
}

// GetOrderList get order list
func (c *AuthorizeClient) GetOrderList(req OrderListRequest) ([]OrderInfo, error) {
	var notificationList, err = c.getNotificationList(req)
	if err != nil {
		return nil, err
	}
	var orderList = []OrderInfo{}
	for _, notification := range notificationList {
		var detail, err = c.getNotificationDetail(notification.NotificationID)
		if err != nil {
			return nil, err
		}
		var order = OrderInfo{
			TransactionID: detail.NotificationPayload.ID,
			RefID:         detail.NotificationPayload.InvoiceNumber,
			TotalAmount:   detail.NotificationPayload.AuthAmount,
			Currency:      "USD",
		}
		switch detail.DeliveryStatus {
		case "Delivered":
			order.Status = "success"
		case "Failed":
			order.Status = "fail"
		default:
			order.Status = "pending"
		}
		orderList = append(orderList, order)
	}

	return orderList, nil
}

func (c *AuthorizeClient) getNotificationList(req OrderListRequest) ([]Notification, error) {
	var data NotificationListResponse
	var url = fmt.Sprintf("notifications?limit=%d&offset=%d", req.Limit, req.Offset)

	if req.FromDate != nil {
		url += fmt.Sprintf("&from_date=%s", req.FromDate.Format("2006-01-02"))
	}
	if req.ToDate != nil {
		url += fmt.Sprintf("&to_date=%s", req.ToDate.Format("2006-01-02"))
	}
	if req.Status != "" {
		url += fmt.Sprintf("&deliveryStatus=%s", req.Status)
	}

	body, err := c.SendWebhookRequest(url, http.MethodGet, nil)
	if err != nil {
		return nil, err
	}
	if string(body) == "[]" {
		return []Notification{}, nil
	}
	err = json.Unmarshal(body, &data)
	if err != nil {
		return nil, err
	}
	return data.Notifications, nil
}

func (c *AuthorizeClient) getNotificationDetail(notificationID string) (*Notification, error) {
	var data Notification
	body, err := c.SendWebhookRequest(fmt.Sprintf("notifications/%s", notificationID), http.MethodGet, nil)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(body, &data)
	if err != nil {
		return nil, err
	}
	return &data, nil
}

// FindAndActiveWebhook Make sure webhook is enabled
func (c *AuthorizeClient) FindAndActiveWebhook() error {
	var webhook WebhookDetail
	body, err := c.SendWebhookRequest(fmt.Sprintf("webhooks/%s", c.WebhookID), http.MethodGet, nil)
	if err != nil {
		return err
	}

	if err := webhook.Unmarshal(body); err != nil {
		return err
	}

	var update = webhook

	// Update webhook when status is inactive
	if webhook.Status == "inactive" {
		update.Status = "active"
	}

	// Ensure callback is outdated
	if webhook.URLCallback != c.URLCallback {
		update.URLCallback = c.URLCallback
	}

	if update != webhook {
		if _, err := c.SendWebhookRequest(fmt.Sprintf("webhooks/%s", c.WebhookID), http.MethodPut, update.Marshal()); err != nil {
			return err
		}
	}

	return nil
}

// GetSettledBatchList get settled batch list
func (c *AuthorizeClient) GetSettledBatchList(req GetSettledBatchListRequest) (*BatchListResponse, error) {
	status, body, err := c.SendRequest(req.Marshal())
	if err != nil || status != http.StatusOK {
		return nil, fmt.Errorf("Get settled batch list fail")
	}

	var res BatchListResponse
	if err := res.Unmarshal(body); err != nil {
		return nil, err
	}

	return &res, nil
}

// GetTransactionList get transaction list
func (c *AuthorizeClient) GetTransactionList(req GetTransactionListRequest) (*GetTransactionListResponse, error) {
	var res GetTransactionListResponse
	// Settle
	if req.GetTransactionList.BatchID != "" {
		status, body, err := c.SendRequest(req.Marshal())
		if err != nil || status != http.StatusOK {
			return nil, fmt.Errorf("Get settle transaction list fail")
		}

		if err := res.Unmarshal(body); err != nil {
			return nil, err
		}
	}

	// UnSettle
	var res2 GetTransactionListResponse
	var req2 = GetUnsettleTransactionListRequest{
		GetTransactionList: req.GetTransactionList,
	}

	req2.GetTransactionList.BatchID = ""
	status, body, err := c.SendRequest(req2.Marshal())
	if err != nil || status != http.StatusOK {
		return nil, fmt.Errorf("Get unsettle transaction list fail")
	}
	if err := res2.Unmarshal(body); err != nil {
		return nil, err
	}
	res.Transactions = append(res.Transactions, res2.Transactions...)
	res.TotalNumInResultSet += res2.TotalNumInResultSet

	return &res, nil
}

// GetAllTransactionList get all transaction list
func (c *AuthorizeClient) GetAllTransactionList(req GetTransactionListRequest) (*GetTransactionListResponse, error) {
	var res GetTransactionListResponse
	var limit, offset = 100, 1
LOOP:

	req.GetTransactionList.Paging = &Paging{
		Limit:  strconv.Itoa(limit),
		Offset: strconv.Itoa(offset),
	}
	items, err := c.GetTransactionList(req)
	if err != nil {
		return nil, err
	}
	if len(items.Transactions) > 0 {
		res.Transactions = append(res.Transactions, items.Transactions...)
		res.TotalNumInResultSet += items.TotalNumInResultSet
		offset++
		goto LOOP
	}
	return &res, nil
}

// GetTransactionFullDetail get transaction with full detail
func (c *AuthorizeClient) GetTransactionFullDetail(req GetTransaction) (*FullTransaction, error) {
	status, body, err := c.SendRequest(req.Marshal())
	if err != nil || status != http.StatusOK {
		return nil, fmt.Errorf("Get transaction list fail")
	}

	var res TransactionDetailsResponse
	if err := res.Unmarshal(body); err != nil {
		return nil, err
	}

	return &res.Transaction, nil
}
