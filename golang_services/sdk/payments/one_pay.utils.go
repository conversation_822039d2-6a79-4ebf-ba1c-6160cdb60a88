package payments

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strings"
)

// cryptoRequest crypto request with SHA256
func cryptoRequest(params []string, hashKey string) (string, error) {
	decodedKey, err := hex.DecodeString(hashKey)
	if err != nil {
		return "", err
	}

	hash := hmac.New(sha256.New, decodedKey)

	io.WriteString(hash, strings.Join(params, "&"))
	return fmt.Sprintf("%X", (hash.Sum(nil))), nil
}

// GetCheckOutURL get checkout url
func (c *OnePayClient) GetCheckOutURL(invoiceID, amount string) (string, error) {
	params, err := c.GetCheckOutParam(invoiceID, amount)
	if err != nil {
		return "", err
	}
	var redirectURL = fmt.Sprintf("%s?%s", c.Endpoint, toRawURL(params))
	return redirectURL, nil
}

// QueryInvoice query invoice
func (c *OnePayClient) QueryInvoice(invoiceID string) (*OnePayInvoiceDetail, error) {
	params, err := c.GetInvoiceParam(invoiceID)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest(http.MethodPost, c.QueryDCURL, strings.NewReader(toRawURL(params)))

	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	res, err := c.Client.Do(req)
	if err != nil {
		return nil, err
	}

	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	var invoice = new(OnePayInvoiceDetail)
	if err := invoice.Parse(string(body)); err != nil {
		return nil, err
	}

	return invoice, nil
}
