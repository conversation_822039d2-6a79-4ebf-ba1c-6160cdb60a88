package payments

import (
	"net/http"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

type IPaymentClient interface {
	PlaceOrder(req *PlaceOrderRequest) (*PlaceOrderResponse, error)
	GetOrderInfo(orderID, ariaTransactionID string) (*OrderInfo, error)
	GetOrderList(req OrderListRequest) ([]OrderInfo, error)
	GetType() string
}

type IPaymentCallbackHandler interface {
	ReadNotification(req *http.Request) (*models.Payment, error)
	AckNotification(resp http.ResponseWriter)
}

type PlaceOrderRequest struct {
	CartID            int
	AriaTransactionID string
	NotifyURL         string
	TotalAmount       float64
	Email             string
	Description       string
	Type              string
	Currency          string
}

type PlaceOrderResponse struct {
	AppID             string         `json:"appID"`
	AccountID         string         `json:"accountID"`
	AriaTransactionID string         `json:"ariaTransactionID"`
	OrderID           string         `json:"orderID"`
	TotalAmount       float64        `json:"totalAmount"`
	Signature         string         `json:"signature"`
	Extra             map[string]any `json:"extra"`
}

type OrderInfo struct {
	TransactionID string
	RefID         string
	Status        string
	TotalAmount   float32
	Currency      string
}

type deliveryStatus string

// Declare webhook status
const (
	RetryPending = deliveryStatus("RetryPending")
	Failed       = deliveryStatus("Failed")
	Delivered    = deliveryStatus("Delivered")
)

// OrderListRequest request get list
type OrderListRequest struct {
	FromDate *time.Time
	ToDate   *time.Time
	Status   deliveryStatus
	Limit    uint64
	Offset   uint64
}
