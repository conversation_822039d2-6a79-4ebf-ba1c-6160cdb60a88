package payments

import (
	"encoding/json"
	"net/http"
	"time"
)

const AUTHORIZE_STATUS_APPROVED = 1
const AUTHORIZE_STATUS_DELINED = 2
const AUTHORIZE_STATUS_ERROR = 3
const AUTHORIZE_STATUS_HELD_FOR_REVIEW = 4

type AuthorizeClient struct {
	APIName        string
	APIKey         string
	Endpoint       string
	Mode           string
	Client         *http.Client
	FormPost       string
	WebhookURL     string
	WebhookID      string
	WebhookSign    string
	URLCallback    string
	URLRedirect    string
	CancelRedirect string
}

type GetHosted struct {
	GetHostedPaymentPageRequest GetHostedPaymentPageRequest `json:"getHostedPaymentPageRequest"`
}

type GetHostedPaymentPageRequest struct {
	MerchantAuthentication MerchantAuthentication `json:"merchantAuthentication"`
	TransactionRequest     TransactionRequest     `json:"transactionRequest"`
	HostedPaymentSettings  HostedPaymentSettings  `json:"hostedPaymentSettings"`
}

type MerchantAuthentication struct {
	Name           string `json:"name"`
	TransactionKey string `json:"transactionKey"`
}

type TransactionRequest struct {
	TransactionType string   `json:"transactionType"`
	Amount          string   `json:"amount"`
	Order           Order    `json:"order"`
	Customer        Customer `json:"customer"`
}

type Customer struct {
	Email string `json:"email"`
}

type Order struct {
	InvoiceNumber string `json:"invoiceNumber"`
	Description   string `json:"description"`
}

type HostedPaymentSettings struct {
	Setting []Setting `json:"setting"`
}

type Setting struct {
	SettingName  string `json:"settingName"`
	SettingValue string `json:"settingValue"`
}

type GetHostedResponse struct {
	Token    string   `json:"token"`
	Messages Messages `json:"messages"`
}

type Messages struct {
	ResultCode string    `json:"resultCode"`
	Message    []Message `json:"message"`
}

type Message struct {
	Code string `json:"code"`
	Text string `json:"text"`
}

type MessagesResponse struct {
	Messages struct {
		ResultCode string `json:"resultCode"`
		Message    []struct {
			Code string `json:"code"`
			Text string `json:"text"`
		} `json:"message"`
	} `json:"messages"`
}

type WebhookNotifRequest struct {
	WebhookId string  `json:"webhookId"`
	Payload   Payload `json:"payload"`
}

type Payload struct {
	ResponseCode int     `json:"responseCode"`
	AuthCode     string  `json:"authCode"`
	AvsResponse  string  `json:"avsResponse"`
	AuthAmount   float64 `json:"authAmount"`
	EntityName   string  `json:"entityName"`
	Id           string  `json:"id"`
}

type GetTransaction struct {
	GetTransactionDetailsRequest GetTransactionDetailsRequest `json:"getTransactionDetailsRequest"`
}

// Marshal return byte array
func (t *GetTransaction) Marshal() []byte {
	buff, _ := json.Marshal(t)
	return buff
}

type GetTransactionDetailsRequest struct {
	MerchantAuthentication MerchantAuthentication `json:"merchantAuthentication"`
	TransId                string                 `json:"transId"`
}

type GetTransactionDetailsResponse struct {
	Messages    Messages    `json:"messages"`
	Transaction Transaction `json:"transaction"`
}

type Transaction struct {
	ResponseCode int   `json:"responseCode"`
	Order        Order `json:"order"`
}

type TransactionDetailsResponse struct {
	Transaction FullTransaction `json:"transaction"`
	MessagesResponse
}

// Unmarshal parse data from byte array
func (t *TransactionDetailsResponse) Unmarshal(b []byte) error {
	return json.Unmarshal(b, &t)
}

type FullTransaction struct {
	TransID                   string    `json:"transId"`
	SubmitTimeUTC             time.Time `json:"submitTimeUTC"`
	SubmitTimeLocal           string    `json:"submitTimeLocal"`
	TransactionType           string    `json:"transactionType"`
	TransactionStatus         string    `json:"transactionStatus"`
	ResponseCode              int       `json:"resCode"`
	ResponseReasonCode        int       `json:"resReasonCode"`
	ResponseReasonDescription string    `json:"resReasonDescription"`
	AVSResponse               string    `json:"AVSResponse"`
	Batch                     struct {
		BatchID                      string    `json:"batchId"`
		SettlementTimeUTC            time.Time `json:"settlementTimeUTC"`
		SettlementTimeUTCSpecified   bool      `json:"settlementTimeUTCSpecified"`
		SettlementTimeLocal          string    `json:"settlementTimeLocal"`
		SettlementTimeLocalSpecified bool      `json:"settlementTimeLocalSpecified"`
		SettlementState              string    `json:"settlementState"`
	} `json:"batch"`
	Order struct {
		InvoiceNumber string `json:"invoiceNumber"`
	} `json:"order"`
	RequestedAmountSpecified         bool    `json:"requestedAmountSpecified"`
	AuthAmount                       float64 `json:"authAmount"`
	SettleAmount                     float64 `json:"settleAmount"`
	PrepaidBalanceRemainingSpecified bool    `json:"prepaidBalanceRemainingSpecified"`
	TaxExempt                        bool    `json:"taxExempt"`
	TaxExemptSpecified               bool    `json:"taxExemptSpecified"`
	Payment                          struct {
		BankAccount struct {
			AccountType          int    `json:"accountType"`
			AccountTypeSpecified bool   `json:"accountTypeSpecified"`
			RoutingNumber        string `json:"routingNumber"`
			AccountNumber        string `json:"accountNumber"`
			NameOnAccount        string `json:"nameOnAccount"`
			EcheckType           int    `json:"echeckType"`
			EcheckTypeSpecified  bool   `json:"echeckTypeSpecified"`
			BankName             any    `json:"bankName"`
		} `json:"bankAccount"`
	} `json:"payment"`
	RecurringBilling          bool `json:"recurringBilling"`
	RecurringBillingSpecified bool `json:"recurringBillingSpecified"`
	ReturnedItems             []struct {
		ID          string    `json:"id"`
		DateUTC     time.Time `json:"dateUTC"`
		DateLocal   string    `json:"dateLocal"`
		Code        string    `json:"code"`
		Description string    `json:"description"`
	} `json:"returnedItems"`
}

// Notification webhook notification model
type Notification struct {
	NotificationID      string               `json:"notificationId"`
	DeliveryStatus      string               `json:"deliveryStatus"`
	EventType           string               `json:"eventType"`
	EventDate           string               `json:"eventDate"`
	WebhookID           string               `json:"webhookId"`
	NotificationDate    string               `json:"notificationDate"`
	NotificationPayload *NotificationPayload `json:"payload,omitempty"`
}

// NotificationPayload payload
type NotificationPayload struct {
	ID            string  `json:"id"`
	ResponseCode  int     `json:"responseCode"`
	AuthCode      string  `json:"authCode"`
	AvsResponse   string  `json:"avsResponse"`
	AuthAmount    float32 `json:"authAmount"`
	InvoiceNumber string  `json:"invoiceNumber"`
	EntityName    string  `json:"entityName"`
}

// NotificationListResponse notification list response model
type NotificationListResponse struct {
	Notifications []Notification `json:"notifications"`
}

// WebhookDetail webhook detail model
type WebhookDetail struct {
	Name        string           `json:"name"`
	Status      string           `json:"status"` //active || inactive
	URLCallback string           `json:"url"`
	EventTypes  *json.RawMessage `json:"eventTypes"`
}

// Unmarshal parse data from byte array
func (w *WebhookDetail) Unmarshal(b []byte) error {
	return json.Unmarshal(b, &w)
}

// Marshal return byte array
func (w *WebhookDetail) Marshal() []byte {
	buff, _ := json.Marshal(w)
	return buff
}

// GetSettledBatchListRequest get batch list request
type GetSettledBatchListRequest struct {
	GetSettledBatchList GetSettledBatchList `json:"getSettledBatchListRequest"`
}

// Unmarshal parse data from byte array
func (s *GetSettledBatchListRequest) Unmarshal(b []byte) error {
	return json.Unmarshal(b, &s)
}

// Marshal return byte array
func (s *GetSettledBatchListRequest) Marshal() []byte {
	buff, _ := json.Marshal(s)
	return buff
}

// GetSettledBatchList get batch list
type GetSettledBatchList struct {
	MerchantAuthentication MerchantAuthentication `json:"merchantAuthentication"`
	IncludeStatistics      string                 `json:"includeStatistics"`
	FirstSettlementDate    time.Time              `json:"firstSettlementDate"`
	LastSettlementDate     time.Time              `json:"lastSettlementDate"`
}

// BatchListResponse batch list response
type BatchListResponse struct {
	MessagesResponse
	BatchList []BatchList `json:"batchList,omitempty"`
}

// Unmarshal parse data from byte array
func (l *BatchListResponse) Unmarshal(b []byte) error {
	return json.Unmarshal(b, &l)
}

// BatchList batch list
type BatchList struct {
	BatchID                      string    `json:"batchId"`
	SettlementTimeUTC            time.Time `json:"settlementTimeUTC"`
	SettlementTimeUTCSpecified   bool      `json:"settlementTimeUTCSpecified"`
	SettlementTimeLocal          string    `json:"settlementTimeLocal"`
	SettlementTimeLocalSpecified bool      `json:"settlementTimeLocalSpecified"`
	SettlementState              string    `json:"settlementState"`
	PaymentMethod                string    `json:"paymentMethod"`
}

// GetTransactionListRequest get transaction list request
type GetTransactionListRequest struct {
	GetTransactionList GetTransactionList `json:"getTransactionListRequest"`
}

// Marshal return byte array
func (s *GetTransactionListRequest) Marshal() []byte {
	buff, _ := json.Marshal(s)
	return buff
}

// GetUnsettleTransactionListRequest get unsettle transaction list request
type GetUnsettleTransactionListRequest struct {
	GetTransactionList GetTransactionList `json:"getUnsettledTransactionListRequest"`
}

// Marshal return byte array
func (s *GetUnsettleTransactionListRequest) Marshal() []byte {
	buff, _ := json.Marshal(s)
	return buff
}

// GetTransactionList get transaction list
type GetTransactionList struct {
	MerchantAuthentication MerchantAuthentication `json:"merchantAuthentication"`
	BatchID                string                 `json:"batchId,omitempty"`
	Sorting                *Sorting               `json:"sorting,omitempty"`
	Paging                 *Paging                `json:"paging,omitempty"`
}

// GetTransactionListResponse get transaction list response
type GetTransactionListResponse struct {
	MessagesResponse
	Transactions []struct {
		TransID           string  `json:"transId"`
		SubmitTimeUTC     string  `json:"submitTimeUTC"`
		SubmitTimeLocal   string  `json:"submitTimeLocal"`
		TransactionStatus string  `json:"transactionStatus"`
		Invoice           string  `json:"invoiceNumber,omitempty"`
		FirstName         string  `json:"firstName,omitempty"`
		LastName          string  `json:"lastName,omitempty"`
		Amount            string  `json:"amount,omitempty"`
		AccountType       string  `json:"accountType,omitempty"`
		AccountNumber     string  `json:"accountNumber,omitempty"`
		SettleAmount      float64 `json:"settleAmount,omitempty"`
		Subscription      struct {
			ID     int `json:"id"`
			PayNum int `json:"payNum,omitempty"`
		} `json:"subscription,omitempty"`
		MarketType     string `json:"marketType,omitempty"`
		Product        string `json:"product,omitempty"`
		MobileDeviceID string `json:"mobileDeviceId,omitempty"`
	} `json:"transactions"`
	TotalNumInResultSet int `json:"totalNumInResultSet"`
}

// Unmarshal parse data from byte array
func (t *GetTransactionListResponse) Unmarshal(b []byte) error {
	return json.Unmarshal(b, &t)
}

// Sorting sorting
type Sorting struct {
	OrderBy         string `json:"orderBy"`
	OrderDescending string `json:"orderDescending"`
}

// Paging paging
type Paging struct {
	Limit  string `json:"limit"`
	Offset string `json:"offset"`
}
