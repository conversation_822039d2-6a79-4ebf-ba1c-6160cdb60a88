package payments

// type AliPayClient struct {
// 	client    *alipayClient.Client
// 	AppID     string
// 	AccountID string
// }

// func NewAliPayClient(appID, accountID, privateKey, pubKey string, isProd bool) (*AliPayClient, error) {
// 	c, err := alipayClient.New(appID, pubKey, privateKey, isProd)
// 	if err != nil {
// 		return nil, err
// 	}
// 	return &AliPayClient{client: c, AppID: appID, AccountID: accountID}, nil
// }

// // Alipay does not need to make calls to their server to do pre order, just return
// func (c *AliPayClient) PlaceOrder(req *PlaceOrderRequest) (*PlaceOrderResponse, error) {
// 	if req == nil {
// 		return nil, nil
// 	}
// 	pay := alipayClient.TradeAppPay{
// 		Trade: alipayClient.Trade{
// 			NotifyURL:   req.NotifyURL,
// 			OutTradeNo:  req.AriaTransactionID,
// 			TotalAmount: strconv.FormatFloat(float64(req.TotalAmount), 'f', 2, 32),
// 			Subject:     alipaySubject,
// 		},
// 		TimeExpire: "",
// 	}

// 	requestParams, err := c.client.TradeAppPay(pay)
// 	if err != nil {
// 		return nil, err
// 	}
// 	r := &PlaceOrderResponse{
// 		AppID:             c.AppID,
// 		AccountID:         c.AccountID,
// 		AriaTransactionID: req.AriaTransactionID,
// 		TotalAmount:       req.TotalAmount,
// 		Extra: map[string]any{
// 			"request_params": requestParams,
// 		},
// 	}
// 	return r, nil
// }

// func (c *AliPayClient) GetOrderInfo(orderID, ariaTransactionID string) (*OrderInfo, error) {
// 	param := alipayClient.TradeQuery{
// 		AppAuthToken: "",
// 		OutTradeNo:   ariaTransactionID,
// 		TradeNo:      orderID,
// 	}
// 	resp, err := c.client.TradeQuery(param)
// 	if err != nil {
// 		return nil, err
// 	}
// 	if resp == nil {
// 		return nil, nil
// 	}

// 	if resp.Content.Code != alipayClient.K_SUCCESS_CODE {
// 		return nil, fmt.Errorf("query Alipay order failed, response: %v", resp)
// 	}
// 	amount, err := strconv.ParseFloat(resp.Content.TotalAmount, 32)
// 	if err != nil {
// 		return nil, err
// 	}
// 	return &OrderInfo{
// 		TransactionID: resp.Content.TradeNo,
// 		Status:        convertAlipayStatus(resp.Content.TradeStatus),
// 		TotalAmount:   float32(amount),
// 		Currency:      resp.Content.TransCurrency,
// 	}, nil
// }

// func convertAlipayStatus(tradeStatus string) string {
// 	switch tradeStatus {
// 	case alipayClient.K_TRADE_STATUS_TRADE_SUCCESS, alipayClient.K_TRADE_STATUS_TRADE_FINISHED:
// 		return models.PaymentStatusSuccess
// 	case alipayClient.K_TRADE_STATUS_WAIT_BUYER_PAY:
// 		return models.PaymentStatusPending
// 	case alipayClient.K_TRADE_STATUS_TRADE_CLOSED:
// 		return models.PaymentStatusClosed
// 	default:
// 		return models.PaymentStatusFailed
// 	}
// }

// func (c *AliPayClient) GetType() string {
// 	return Alipay
// }

// func (c *AliPayClient) ReadNotification(req *http.Request) (*models.Payment, error) {
// 	notif, err := c.client.GetTradeNotification(req)
// 	if err != nil {
// 		return nil, err
// 	}
// 	total, err := strconv.ParseFloat(notif.TotalAmount, 32)
// 	if err != nil {
// 		return nil, err
// 	}
// 	p := &models.Payment{
// 		ID:                    notif.OutTradeNo,
// 		Status:                convertAlipayStatus(notif.TradeStatus),
// 		ExternalTransactionID: notif.TradeNo,
// 		Total:                 total,
// 		Properties: &models.PropertyMap{
// 			"notification": notif,
// 		},
// 	}
// 	return p, nil
// }

// func (c *AliPayClient) AckNotification(resp http.ResponseWriter) {
// 	c.client.AckNotification(resp)
// }

// func (c *AliPayClient) GetOrderList(req OrderListRequest) ([]OrderInfo, error) {
// 	return nil, nil
// }
