package payments

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/rs/zerolog/log"
	"github.com/stripe/stripe-go"
	"github.com/stripe/stripe-go/paymentintent"
)

type StripeClient struct {
	secret string
}

func NewStripeClient(secret string) *StripeClient {
	stripe.Key = secret
	return &StripeClient{secret: secret}
}

func (s *StripeClient) ReadNotification(req *http.Request) (*models.Payment, error) {
	event, err := s.readWebhook(req)
	if err != nil {
		return nil, err
	}

	switch event.Type {
	case "payment_intent.succeeded":
		var pi stripe.PaymentIntent
		if err := json.Unmarshal(event.Data.Raw, &pi); err != nil {
			return nil, err
		}
		res := &models.Payment{
			ID:                    pi.Metadata["ad_transaction_id"],
			Status:                models.PaymentStatusSuccess,
			ExternalTransactionID: pi.ID,
			Total:                 float64(pi.Amount) / 100,
		}
		return res, nil
	case "payment_intent.payment_failed":
		var pi stripe.PaymentIntent
		if err := json.Unmarshal(event.Data.Raw, &pi); err != nil {
			return nil, err
		}
		res := &models.Payment{
			ID:                    pi.Metadata["ad_transaction_id"],
			Status:                models.PaymentStatusFailed,
			ExternalTransactionID: pi.ID,
			Total:                 float64(pi.Amount) / 100,
		}
		return res, nil
	default:
		log.Info().Str("stripe_event_type", event.Type).Msg("unhandled event")
	}
	return nil, fmt.Errorf("unknown event from Strip, %s", event.Type)
}

func (s *StripeClient) AckNotification(resp http.ResponseWriter) {
	resp.WriteHeader(http.StatusOK)
}

func (s *StripeClient) PlaceOrder(req *PlaceOrderRequest) (*PlaceOrderResponse, error) {
	params := &stripe.PaymentIntentParams{
		Amount:      stripe.Int64(int64(req.TotalAmount * 100)),
		Currency:    stripe.String(string(strings.ToLower(req.Currency))),
		Description: stripe.String(req.Description),
	}
	params.AddMetadata("ad_transaction_id", req.AriaTransactionID)
	params.AddMetadata("email", req.Email)
	params.SetIdempotencyKey(req.AriaTransactionID)
	pi, err := paymentintent.New(params)
	if err != nil {
		return nil, err
	}
	resp := &PlaceOrderResponse{
		AriaTransactionID: req.AriaTransactionID,
		OrderID:           pi.ID,
		TotalAmount:       req.TotalAmount,
		Signature:         pi.ClientSecret,
		Extra: map[string]any{
			"client_secret": pi.ClientSecret,
		},
	}
	return resp, nil
}

func (s *StripeClient) GetOrderInfo(orderID, ariaTransactionID string) (*OrderInfo, error) {
	panic("implement me")
}

func (s *StripeClient) GetOrderList(req OrderListRequest) ([]OrderInfo, error) {
	panic("implement me")
}

func (s *StripeClient) GetType() string {
	return Stripe
}

func (s *StripeClient) readWebhook(req *http.Request) (*stripe.Event, error) {
	byt, err := ioutil.ReadAll(req.Body)
	if err != nil {
		return nil, err
	}
	var e stripe.Event
	if err := json.Unmarshal(byt, &e); err != nil {
		return nil, err
	}
	return &e, nil
}
