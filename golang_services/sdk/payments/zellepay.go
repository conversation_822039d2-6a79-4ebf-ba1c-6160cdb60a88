package payments

import (
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

type ZellePayClient struct {
	ReceiverInfo map[string]any
}

func NewZellePayClient() *ZellePayClient {
	return &ZellePayClient{}
}

// PlaceOrder place order
func (z *ZellePayClient) PlaceOrder(req *PlaceOrderRequest) (*PlaceOrderResponse, error) {
	return &PlaceOrderResponse{
		AriaTransactionID: req.AriaTransactionID,
		TotalAmount:       req.TotalAmount,
		OrderID:           req.AriaTransactionID,
		Extra: map[string]any{
			"receiver": z.ReceiverInfo,
		},
	}, nil
}

// GetOrderInfo get order info
func (z *ZellePayClient) GetOrderInfo(orderID, ariaTransactionID string) (*OrderInfo, error) {
	return nil, nil
}

// GetType get type
func (z *ZellePayClient) GetType() string {
	return ZellePay
}

// ReadNotification read notification
func (z *ZellePayClient) ReadNotification(req *http.Request) (*models.Payment, error) {
	return nil, nil
}

// AckNotification ack notification
func (z *ZellePayClient) AckNotification(resp http.ResponseWriter) {
	resp.WriteHeader(http.StatusOK)
}

// GetOrderList get order list
func (z *ZellePayClient) GetOrderList(req OrderListRequest) ([]OrderInfo, error) {
	return nil, nil
}
