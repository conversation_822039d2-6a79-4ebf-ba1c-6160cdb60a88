package payments

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/rs/zerolog/log"
	"github.com/tidwall/gjson"
)

func NewPayPalClient(apiKey, apiSecret, apiBase, returnURL, cancelURL, callbackURL, webhookID string) *PayPalClient {
	return &PayPalClient{
		APIKey:      apiKey,
		APISecret:   apiSecret,
		APIBase:     apiBase,
		ReturnURL:   returnURL,
		CancelURL:   cancelURL,
		CallbackURL: callbackURL,
		WebhookID:   webhookID,
		Client:      &http.Client{Timeout: 120 * time.Second},
	}
}

// GetAccessToken get access token from access key and secret
func (p *PayPalClient) GetAccessToken() error {
	buf := bytes.NewBuffer([]byte("grant_type=client_credentials"))
	req, err := http.NewRequest("POST", fmt.Sprintf("%s%s", p.APIBase, "/v1/oauth2/token"), buf)
	if err != nil {
		return err
	}

	req.Header.Set("Content-type", "application/x-www-form-urlencoded")

	p.Token = &PayPalToken{}
	if err = p.Send(req, false, p.Token); err != nil {
		return err
	}

	p.Token.ExpiredAt = time.Now().Add(time.Second * time.Duration(p.Token.ExpiresIn))
	return nil
}

// PlaceOrder place order
func (p *PayPalClient) PlaceOrder(req *PlaceOrderRequest) (*PlaceOrderResponse, error) {
	var (
		buff []byte
		err  error
	)
	var transaction = &PayPalTransaction{
		Intent: "sale",
		Payer: PayPalPayer{
			PaymentMethod: "paypal",
		},
		Transactions: []PayPalTransactionUnit{
			{
				Amount: &PurchaseUnitAmount{
					Total:    fmt.Sprintf("%0.0f", req.TotalAmount),
					Currency: "USD",
				},
				InvoiceNumber: req.AriaTransactionID,
			},
		},
		PayPalRedirectURL: PayPalRedirectURL{
			ReturnURL: p.ReturnURL,
			CancelURL: p.CancelURL,
		},
	}

	if buff, err = json.Marshal(transaction); err != nil {
		return nil, err
	}

	paypalReq, err := http.NewRequest("POST", fmt.Sprintf("%s%s", p.APIBase, "/v1/payments/payment"), bytes.NewBuffer(buff))
	if err != nil {
		return nil, err
	}

	if err = p.Send(paypalReq, true, transaction); err != nil {
		return nil, err
	}

	var paymentURL = ""
	for _, item := range transaction.Links {
		if item.Rel == "approval_url" {
			paymentURL = item.Href
		}
	}
	return &PlaceOrderResponse{
		AriaTransactionID: req.AriaTransactionID,
		TotalAmount:       req.TotalAmount,
		OrderID:           transaction.ID,
		Extra: map[string]any{
			"paymentUrl": paymentURL,
		},
	}, nil
}

// GetOrderInfo get order info
func (p *PayPalClient) GetOrderInfo(orderID, ariaTransactionID string) (*OrderInfo, error) {
	return nil, nil
}

// GetType get type
func (p *PayPalClient) GetType() string {
	return PayPal
}

// ReadNotification read notification
func (p *PayPalClient) ReadNotification(req *http.Request) (*models.Payment, error) {
	buff, err := ioutil.ReadAll(req.Body)
	if err != nil {
		return nil, err
	}

	var notification map[string]any
	if err = json.Unmarshal(buff, &notification); err != nil {
		return nil, err
	}

	var (
		callbackJSON = gjson.ParseBytes(buff)
		transaction  = &PayPalTransaction{}
	)

	// Only get payment that created and approved by buyer
	if callbackJSON.Get("event_type").String() != "PAYMENTS.PAYMENT.CREATED" {
		log.Info().Interface("Body", callbackJSON.Raw).Msg("Ignore callback message")
		return nil, nil
	}

	reqBuff, err := json.Marshal(map[string]any{
		"payer_id": callbackJSON.Get("resource.payer.payer_info.payer_id").String(),
	})

	if err != nil {
		return nil, err
	}

	// Make sure the notification request come from paypal
	if err = p.verifyNotificationRequest(req.Header, buff); err != nil {
		return nil, err
	}

	// Confirm the payment by system
	paypalReq, err := http.NewRequest("POST", fmt.Sprintf("%s/v1/payments/payment/%s/execute", p.APIBase, callbackJSON.Get("resource.id").String()), bytes.NewBuffer(reqBuff))
	if err != nil {
		return nil, err
	}

	if err = p.Send(paypalReq, true, transaction); err != nil {
		return nil, err
	}

	if len(transaction.Transactions) == 0 {
		return nil, nil
	}

	payment := &models.Payment{
		ID:                    transaction.Transactions[0].InvoiceNumber,
		ExternalTransactionID: callbackJSON.Get("resource.id").String(),
		Properties: &models.PropertyMap{
			"notification": notification,
		},
	}

	// Map status
	switch transaction.State {
	case "approved":
		payment.Status = models.PaymentStatusSuccess
	case "failed":
		payment.Status = models.PaymentStatusFailed
	default:
		payment.Status = models.PaymentStatusPending
	}

	if val, err := strconv.ParseFloat(transaction.Transactions[0].Amount.Total, 10); err == nil {
		payment.Total = val
	}

	return payment, nil
}

// verifyNotificationRequest verify notification request
func (p *PayPalClient) verifyNotificationRequest(header http.Header, body []byte) error {
	var playload = map[string]any{
		"auth_algo":         header.Get("paypal-auth-algo"),
		"cert_url":          header.Get("paypal-cert-url"),
		"transmission_id":   header.Get("paypal-transmission-id"),
		"transmission_sig":  header.Get("paypal-transmission-sig"),
		"transmission_time": header.Get("paypal-transmission-time"),
		"webhook_id":        p.WebhookID,
		"webhook_event":     json.RawMessage(body),
	}
	var reader = &bytes.Buffer{}
	if err := json.NewEncoder(reader).Encode(playload); err != nil {
		return err
	}

	verifyReq, err := http.NewRequest("POST", fmt.Sprintf("%s%s", p.APIBase, "/v1/notifications/verify-webhook-signature"), reader)
	if err != nil {
		return err
	}

	var verifyRes = json.RawMessage{}
	if err = p.Send(verifyReq, true, &verifyRes); err != nil {
		return err
	}
	if gjson.ParseBytes(verifyRes).Get("verification_status").String() != "SUCCESS" {
		log.Error().Interface("response verify", verifyRes)
		return fmt.Errorf("Webhook notification is invalid")
	}

	return nil
}

// AckNotification ack notification
func (p *PayPalClient) AckNotification(resp http.ResponseWriter) {
	resp.WriteHeader(http.StatusOK)
}

// GetOrderList get order list
func (p *PayPalClient) GetOrderList(req OrderListRequest) ([]OrderInfo, error) {
	return nil, nil
}
