package payments

import (
	"crypto/tls"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"net/http"
	"net/url"
	"sort"
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/bluele/gcache"
)

// OnePayClient one pay client
type OnePayClient struct {
	MerchantID    string
	MerchantOrder string
	User          string
	Password      string
	APIKey        string
	APISecret     string
	Endpoint      string
	CallbackURL   string
	QueryDCURL    string
	ReturnURL     string
	Client        *OnePayHTTPClient
	Cache         gcache.Cache
}

// GetErrorByCode get error code detail
func (o *OnePayClient) GetErrorByCode(code int) Localize {
	val, ok := localizeDict[code]
	if ok {
		return val
	}
	// Unknown error
	return Localize{
		VI: "Giao dịch thất bại",
		EN: "Unknown Failure",
	}
}

// GetCheckOutParam get checkout params
func (o *OnePayClient) GetCheckOutParam(invoiceID string, amount string) ([]PairKeyValue, error) {
	return OnePayParams{
		AccessCode:  o.APIKey,
		Amount:      amount + "00", // 1VND = 100 Onepay cents
		Command:     "pay",
		MerchTxnRef: invoiceID,
		Merchant:    o.MerchantID,
		OrderInfo:   o.MerchantOrder,
		ReturnURL:   o.CallbackURL,
		Version:     "2",
	}.ToMap(o.APISecret)
}

// GetInvoiceParam get invoice params
func (o *OnePayClient) GetInvoiceParam(invoiceID string) ([]PairKeyValue, error) {
	return OnePayParams{
		AccessCode:  o.APIKey,
		Command:     "queryDR",
		MerchTxnRef: invoiceID,
		Merchant:    o.MerchantID,
		User:        o.User,
		Password:    o.Password,
		Version:     "2",
	}.ToMap(o.APISecret)
}

// Localize localize
type Localize struct {
	EN string
	VI string
}

// OnePayParams onepay params
type OnePayParams struct {
	// Vendor params [Required]
	Version    string `json:"vpc_Version,omitempty"`
	Command    string `json:"vpc_Command,omitempty"`
	AccessCode string `json:"vpc_AccessCode,omitempty"`
	Merchant   string `json:"vpc_Merchant,omitempty"`
	User       string `json:"vpc_User,omitempty"`
	Password   string `json:"vpc_Password,omitempty"`
	Locale     string `json:"vpc_Locale,omitempty"`
	ReturnURL  string `json:"vpc_ReturnURL,omitempty"`
	Currency   string `json:"vpc_Currency,omitempty"`

	// Merchant params [Not required]
	MerchTxnRef string `json:"vpc_MerchTxnRef,omitempty"`
	OrderInfo   string `json:"vpc_OrderInfo,omitempty"`
	Amount      string `json:"vpc_Amount,omitempty"`
	TicketNo    string `json:"vpc_TicketNo,omitempty"` //IP address
	AgainLink   string `json:"AgainLink,omitempty"`
	Title       string `json:"Title,omitempty"`
	SecureHash  string `json:"vpc_SecureHash,omitempty"`

	// Customer params [Not required]
	SHIPStreet01  string `json:"vpc_SHIP_Street01,omitempty"`
	SHIPProvice   string `json:"vpc_SHIP_Provice,omitempty"`
	SHIPCity      string `json:"vpc_SHIP_City,omitempty"`
	SHIPCountry   string `json:"vpc_SHIP_Country,omitempty"`
	CustomerPhone string `json:"vpc_Customer_Phone,omitempty"`
	CustomerEmail string `json:"vpc_Customer_Email,omitempty"`
	CustomerID    string `json:"vpc_Customer_Id,omitempty"`
}

// Parse parse data
func (o *OnePayParams) Parse(v map[string]string) error {
	buff, err := json.Marshal(v)
	if err != nil {
		return err
	}

	return json.Unmarshal(buff, &o)
}

// ToMap data to map
func (o OnePayParams) ToMap(hashKey string) ([]PairKeyValue, error) {
	buff, err := json.Marshal(o)
	if err != nil {
		return nil, err
	}
	var payload = map[string]string{}
	if err := json.Unmarshal(buff, &payload); err != nil {
		return nil, err
	}

	var storedPayload = []PairKeyValue{}

	for key, val := range payload {
		storedPayload = append(storedPayload, PairKeyValue{key, val})
	}

	// Sort by key in alphabet order
	sort.Slice(storedPayload, func(i, j int) bool {
		return storedPayload[i].Key < storedPayload[j].Key
	})

	var secureParams = []string{}

	for _, pair := range storedPayload {
		if len(pair.Key) > 4 && pair.Key[:4] == "vpc_" || len(pair.Key) > 5 && pair.Key[:5] == "user_" {
			secureParams = append(secureParams, fmt.Sprintf("%s=%s", pair.Key, pair.Value))
		}
	}

	// calculate the param checksum
	signature, err := cryptoRequest(secureParams, hashKey)
	if err != nil {
		return nil, err
	}
	storedPayload = append(storedPayload, PairKeyValue{"vpc_SecureHash", signature})

	return storedPayload, nil
}

// OnePayCallback onepay callback
type OnePayCallback struct {
	MerchTxnRef     string `json:"vpc_MerchTxnRef"`
	Amount          string `json:"vpc_Amount"`
	Message         string `json:"vpc_Message"`
	TransactionNo   string `json:"vpc_TransactionNo"`
	TxnResponseCode string `json:"vpc_TxnResponseCode"`
}

// ParseFromMap parse from map
func (o *OnePayCallback) ParseFromMap(v map[string]any) error {
	buff, err := json.Marshal(v)
	if err != nil {
		return err
	}
	return json.Unmarshal(buff, &o)
}

// GetPaymentStatus get payment status
func (o *OnePayCallback) GetPaymentStatus() string {
	switch o.TxnResponseCode {
	case "0": // Approved
		return models.PaymentStatusSuccess
	case "99", "253": // User canceled, Transaction timeout
		return models.PaymentStatusClosed
	default:
		return models.PaymentStatusFailed
	}
}

// OnePayInvoiceDetail onepay callback
type OnePayInvoiceDetail struct {
	DRExist         string `json:"vpc_DRExists"` // Y or N
	TransactionNo   string `json:"vpc_TransactionNo"`
	TxnResponseCode string `json:"vpc_TxnResponseCode"`
	Message         string `json:"vpc_Message"`
	Amount          string `json:"vpc_Amount"`
}

// Parse parse
func (o *OnePayInvoiceDetail) Parse(urlEncode string) error {
	parsedQuery, err := url.ParseQuery(urlEncode)
	if err != nil {
		return err
	}
	if item, ok := parsedQuery["vpc_DRExists"]; ok && len(item) > 0 {
		o.DRExist = item[0]
	}
	if item, ok := parsedQuery["vpc_TransactionNo"]; ok && len(item) > 0 {
		o.TransactionNo = item[0]
	}
	if item, ok := parsedQuery["vpc_TxnResponseCode"]; ok && len(item) > 0 {
		o.TxnResponseCode = item[0]
	}
	if item, ok := parsedQuery["vpc_Message"]; ok && len(item) > 0 {
		o.Message = item[0]
	}
	if item, ok := parsedQuery["vpc_Amount"]; ok && len(item) > 0 {
		o.Amount = item[0]
	}
	return nil
}

// PairKeyValue key value
type PairKeyValue struct {
	Key   string
	Value string
}

func toRawURL(list []PairKeyValue) string {
	var temp = []string{}
	for _, item := range list {
		temp = append(temp, fmt.Sprintf("%s=%s", item.Key, item.Value))
	}
	return strings.Join(temp, "&")
}

// Cache name
const (
	CacheOnePayExchangeRate = "onepay_exchange_rate"
)

var localizeDict = map[int]Localize{
	0: Localize{
		VI: "Giao dịch thành công",
		EN: "Approved",
	},
	1: {
		VI: "Ngân hàng từ chối giao dịch",
		EN: "Bank Declined",
	},
	3: {
		VI: "Mã đơn vị không tồn tại",
		EN: "Merchant not exist",
	},
	4: {
		VI: "Không đúng access code",
		EN: "Invalid access code",
	},
	5: {
		VI: "Số tiền không hợp lệ",
		EN: "Invalid amount",
	},
	6: {
		VI: "Mã tiền tệ không tồn tại",
		EN: "Invalid currency code",
	},
	7: {
		VI: "Lỗi không xác định",
		EN: "Unspecified Failure ",
	},
	8: {
		VI: "Số thẻ không đúng",
		EN: "Invalid card Number",
	},
	9: {
		VI: "Tên chủ thẻ không đúng",
		EN: "Invalid card name",
	},
	10: {
		VI: "Thẻ hết hạn/Thẻ bị khóa",
		EN: "Expired Card",
	},
	11: {
		VI: "Thẻ chưa đăng ký sử dụng dịch vụ",
		EN: "Card Not Registed Service(internet banking)",
	},
	12: {
		VI: "Ngày phát hành/Hết hạn không đúng",
		EN: "Invalid card date",
	},
	13: {
		VI: "Vượt quá hạn mức thanh toán",
		EN: "Exist Amount",
	},
	21: {
		VI: "Số tiền không đủ để thanh toán",
		EN: "Insufficient fund",
	},
	22: {
		VI: "Thông tin tài khoản không đúng",
		EN: "Invalid Account",
	},
	23: {
		VI: "Tài khoản bị khóa",
		EN: "Account Locked",
	},
	24: {
		VI: "Thông tin thẻ không đúng",
		EN: "Invalid Card Info",
	},
	25: {
		VI: "OTP không đúng",
		EN: "Invalid OTP",
	},
	253: {
		VI: "Quá thời gian thanh toán",
		EN: "Transaction timeout",
	},
	99: {
		VI: "Người sử dụng hủy giao dịch",
		EN: "User cancel",
	},
}

// ExchangeRateList exchange rate list
type ExchangeRateList struct {
	XMLName xml.Name `xml:"ExrateList"`
	ExRate  []struct {
		CurrencyCode string `xml:"CurrencyCode,attr"`
		CurrencyName string `xml:"CurrencyName,attr"`
		Buy          string `xml:"Buy,attr"`
		Transfer     string `xml:"Transfer,attr"`
		Sell         string `xml:"Sell,attr"`
	} `xml:"Exrate"`
	Source string `xml:"Source"`
}

// OnePayHTTPClient onepay http client
type OnePayHTTPClient struct {
	SkipVerify bool
	*http.Client
}

// NewOnePayHTTPClient make new http client for one pay
func NewOnePayHTTPClient(skipVerify bool) *OnePayHTTPClient {
	client := &http.Client{Transport: &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: skipVerify},
	}}
	return &OnePayHTTPClient{
		SkipVerify: skipVerify,
		Client:     client,
	}
}
