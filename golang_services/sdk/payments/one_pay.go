package payments

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

// PlaceOrder place order
func (c *OnePayClient) PlaceOrder(req *PlaceOrderRequest) (*PlaceOrderResponse, error) {
	var url, err = c.GetCheckOutURL(req.AriaTransactionID, fmt.Sprintf("%0.0f", req.TotalAmount))
	if err != nil {
		return nil, err
	}

	return &PlaceOrderResponse{
		AriaTransactionID: req.AriaTransactionID,
		TotalAmount:       req.TotalAmount,
		OrderID:           req.AriaTransactionID,
		Extra: map[string]any{
			"paymentUrl": url,
		},
	}, nil
}

// GetOrderInfo get order info
func (c *OnePayClient) GetOrderInfo(orderID, ariaTransactionID string) (*OrderInfo, error) {
	if ariaTransactionID == "" {
		return nil, nil
	}
	invoice, err := c.QueryInvoice(ariaTransactionID)
	if err != nil {
		return nil, err
	}

	if invoice.DRExist == "N" { // Not exist
		return nil, nil
	}

	var order = &OrderInfo{
		Currency: "VND", // Onepay only support VND currency
	}
	order.TransactionID = invoice.TransactionNo
	order.RefID = ariaTransactionID

	switch invoice.TxnResponseCode {
	case "100", "300":
		order.Status = models.PaymentStatusPending
	case "0":
		order.Status = models.PaymentStatusSuccess
	default:
		order.Status = models.PaymentStatusFailed
	}

	//Parse amount
	if amount, err := strconv.ParseFloat(invoice.Amount, 10); err == nil {
		order.TotalAmount = float32(amount / 100) // 1VND = 100 x rate Onepay cent unit
	}

	return order, nil
}

// GetType get type
func (c *OnePayClient) GetType() string {
	return OnePay
}

// ReadNotification read notification
func (c *OnePayClient) ReadNotification(req *http.Request) (*models.Payment, error) {
	buff, err := ioutil.ReadAll(req.Body)
	if err != nil {
		return nil, err
	}

	var callback = new(OnePayCallback)
	if err := json.Unmarshal(buff, &callback); err != nil {
		return nil, err
	}

	var notification map[string]any
	if err = json.Unmarshal(buff, &notification); err != nil {
		return nil, err
	}

	p := &models.Payment{
		ID:                    callback.MerchTxnRef,
		Status:                callback.GetPaymentStatus(),
		ExternalTransactionID: callback.TransactionNo,
		Properties: &models.PropertyMap{
			"notification": notification,
		},
	}
	if val, err := strconv.ParseFloat(callback.Amount, 10); err == nil {
		p.Total = val / 100 // 1VND = 100 Onepay cents
	}

	return p, nil
}

// AckNotification ack notification
func (c *OnePayClient) AckNotification(resp http.ResponseWriter) {
	// resp.WriteHeader(http.StatusOK)
}

// GetOrderList get order list
func (c *OnePayClient) GetOrderList(req OrderListRequest) ([]OrderInfo, error) {
	// TODO: not implement
	return nil, nil
}
