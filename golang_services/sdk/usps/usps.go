package usps

import (
	"encoding/json"
	"fmt"

	aderr "bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/services"
	"github.com/gin-gonic/gin"
)

const (
	baseURL       = "https://tools.usps.com/UspsToolsRestServices/rest/v2"
	productType   = "PASSPORT"
	radiusDefault = "20"
)

type Response struct {
	Result Result `json:"result"`
}
type Result struct {
	FieldErrors           string              `json:"fieldErrors"`
	Success               bool                `json:"success"`
	ResultCodeMessageList []ResultCodeMessage `json:"resultCodeMessageList"`
}
type ResultCodeMessage struct {
	Message string `json:"message"`
}

func FacilityScheduleSearch(c *gin.Context, zipCode, radius, date string, isAdult bool) (map[string]any, error) {
	var numberOfAdults, numberOfMinors = 0, 0
	if isAdult {
		numberOfAdults = 1
	} else {
		numberOfMinors = 1
	}

	// get data from config
	v := c.Value("service_client")
	isl, _ := v.(*services.ServiceClient)
	url := fmt.Sprintf("%s/%s", baseURL, "facilityScheduleSearch")

	// prepare body of request
	data := map[string]any{
		"poScheduleType": productType,
		"date":           date,
		"numberOfAdults": numberOfAdults,
		"numberOfMinors": numberOfMinors,
		"radius":         radius,
		"zip5":           zipCode,
	}
	jData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	statusCode, body, err := isl.SendRequest(jData, url)
	if err != nil {
		return nil, err
	}
	if statusCode >= 400 {
		return nil, fmt.Errorf("unable to send request")
	}

	var resp Response
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, aderr.SystemBusyErr
	}
	if !resp.Result.Success {
		return nil, fmt.Errorf(resp.Result.ResultCodeMessageList[0].Message)
	}

	var bodyResp map[string]any
	err = json.Unmarshal(body, &bodyResp)
	if err != nil {
		return nil, err
	}
	return bodyResp, nil
}

func AppointmentDateSearch(c *gin.Context, fdbId string, isAdult bool) (map[string]any, error) {
	var numberOfAdults, numberOfMinors = 0, 0
	if isAdult {
		numberOfAdults = 1
	} else {
		numberOfMinors = 1
	}
	// get data from config
	v := c.Value("service_client")
	isl, _ := v.(*services.ServiceClient)
	url := fmt.Sprintf("%s/%s", baseURL, "appointmentDateSearch")

	// prepare body of request
	data := map[string]any{
		"numberOfAdults": numberOfAdults,
		"numberOfMinors": numberOfMinors,
		"fdbId":          fdbId,
		"productType":    productType,
	}

	jData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	statusCode, body, err := isl.SendRequest(jData, url)
	if err != nil {
		return nil, err
	}
	if statusCode >= 400 {
		return nil, fmt.Errorf("unable to send request")
	}

	var resp Response
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, aderr.SystemBusyErr
	}
	if !resp.Result.Success {
		return nil, fmt.Errorf(resp.Result.ResultCodeMessageList[0].Message)
	}

	var bodyResp map[string]any
	err = json.Unmarshal(body, &bodyResp)
	if err != nil {
		return nil, err
	}
	return bodyResp, nil
}

func AppointmentTimeSearch(c *gin.Context, fdbId string, date string, isAdult bool) (map[string]any, error) {
	var numberOfAdults, numberOfMinors = 0, 0
	if isAdult {
		numberOfAdults = 1
	} else {
		numberOfMinors = 1
	}
	// get data from config
	v := c.Value("service_client")
	isl, _ := v.(*services.ServiceClient)
	url := fmt.Sprintf("%s/%s", baseURL, "appointmentTimeSearch")

	// prepare body of request
	data := map[string]any{
		"numberOfAdults":             numberOfAdults,
		"numberOfMinors":             numberOfMinors,
		"fdbId":                      []string{fdbId},
		"productType":                productType,
		"date":                       date,
		"skipEndOfDayRecord":         true,
		"excludedConfirmationNumber": []string{},
	}

	jData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	statusCode, body, err := isl.SendRequest(jData, url)
	if err != nil {
		return nil, err
	}
	if statusCode >= 400 {
		return nil, fmt.Errorf("unable to send request")
	}

	var resp Response
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, aderr.SystemBusyErr
	}

	var bodyResp map[string]any
	err = json.Unmarshal(body, &bodyResp)
	if err != nil {
		return nil, err
	}
	return bodyResp, nil
}

func CreateAppointment(c *gin.Context, ba models.BookingAppointmentInfo, isAdult bool) (map[string]any, error) {
	var numberOfAdults, numberOfMinors = 0, 0
	if isAdult {
		numberOfAdults = 1
	} else {
		numberOfMinors = 1
	}

	// get data from config
	v := c.Value("service_client")
	isl, _ := v.(*services.ServiceClient)
	url := fmt.Sprintf("%s/%s", baseURL, "createAppointment")

	// prepare body of request
	data := map[string]any{
		"customer": map[string]any{
			"firstName": ba.FirstName,
			"lastName":  ba.LastName,
			"regId":     "",
		},
		"customerEmailAddress": ba.Email,
		"customerPhone": map[string]any{
			"areaCode": ba.Phone[len(ba.Phone)-10 : len(ba.Phone)-7],
			"exchange": ba.Phone[len(ba.Phone)-7 : len(ba.Phone)-4],
			"line":     ba.Phone[len(ba.Phone)-4 : len(ba.Phone)-0],
			"textable": false,
		},
		"numberOfAdults":         numberOfAdults,
		"numberOfMinors":         numberOfMinors,
		"fdbId":                  ba.FdbID,
		"schedulingType":         productType,
		"date":                   ba.Date,
		"time":                   ba.Time,
		"passportPhotoIndicator": 0,
		"serviceCenter":          "Web Service Center",
	}

	jData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	statusCode, body, err := isl.SendRequest(jData, url)
	if err != nil {
		return nil, err
	}
	if statusCode >= 400 {
		return nil, fmt.Errorf("unable to send request")
	}

	var resp Response
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, aderr.SystemBusyErr
	}
	if !resp.Result.Success {
		return nil, fmt.Errorf(resp.Result.ResultCodeMessageList[0].Message)
	}

	var bodyResp map[string]any
	err = json.Unmarshal(body, &bodyResp)
	if err != nil {
		return nil, err
	}
	return bodyResp, nil
}

func PostalBusinessDays(c *gin.Context) ([]string, error) {
	// get data from config
	v := c.Value("service_client")
	isl, _ := v.(*services.ServiceClient)
	url := fmt.Sprintf("%s/%s", baseURL, "postalBusinessDays")

	// prepare body of request
	data := map[string]any{}

	jData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	statusCode, body, err := isl.SendRequest(jData, url)
	if err != nil {
		return nil, err
	}
	if statusCode >= 400 {
		return nil, fmt.Errorf("unable to send request")
	}

	var resp []string
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, aderr.SystemBusyErr
	}

	return resp, nil
}

func GetAppointmentByConfirmation(c *gin.Context, code string) (map[string]any, error) {
	// get data from config
	v := c.Value("service_client")
	isl, _ := v.(*services.ServiceClient)
	url := fmt.Sprintf("%s/%s", baseURL, "appointmentByConfirmation")

	// prepare body of request
	data := map[string]any{
		"confirmationNumber": code,
	}

	jData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	statusCode, body, err := isl.SendRequest(jData, url)
	if err != nil {
		return nil, err
	}
	if statusCode >= 400 {
		return nil, fmt.Errorf("unable to send request")
	}

	var resp Response
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, aderr.SystemBusyErr
	}
	if !resp.Result.Success {
		return nil, fmt.Errorf(resp.Result.ResultCodeMessageList[0].Message)
	}

	var bodyResp map[string]any
	err = json.Unmarshal(body, &bodyResp)
	if err != nil {
		return nil, err
	}
	return bodyResp, nil
}

func CancelAppointmentByConfirmation(c *gin.Context, code string) error {
	// get data from config
	v := c.Value("service_client")
	isl, _ := v.(*services.ServiceClient)
	url := fmt.Sprintf("%s/%s", baseURL, "cancelAppointment")

	// prepare body of request
	data := map[string]any{"confirmationNumber": code, "cancelReason": "CHANGEFACILITY"}

	jData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	statusCode, body, err := isl.SendRequest(jData, url)
	if err != nil {
		return err
	}
	if statusCode >= 400 {
		return fmt.Errorf("unable to send request")
	}

	var resp Response
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return aderr.SystemBusyErr
	}
	if !resp.Result.Success {
		return fmt.Errorf(resp.Result.ResultCodeMessageList[0].Message)
	}

	return nil
}
