package shipment

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/sdk/product"

	"bitbucket.org/persistence17/aria/golang_services/models"
	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/fedex"
)

const (
	GenerateShipmentEmailTemplateName = "admin_generate_shipping_label"
	DateFormat                        = "2006-01-02T15:04:05"
)

// func QueryShippingRequirements(shipDao db.ShippingData, q *ShippingRequirementsQuery) ([]*models.ShippingRequirement, error) {
// 	r, err := shipDao.QueryShippingRequirements(q.ProductType, q.ProductID)
// 	var res []*models.ShippingRequirement
// 	if err != nil {
// 		return res, err
// 	}
// 	if r == nil {
// 		return res, nil
// 	}
// 	for _, requirement := range r.Requirements {
// 		matchContent, matchRegion, matchState, matchCity := false, false, false, false
// 		if q.ShippingContent == "" || strings.EqualFold(q.ShippingContent, requirement.ShippingContent) {
// 			matchContent = true
// 		}
// 		if q.ResidenceRegion == "" || strings.EqualFold(q.ResidenceRegion, requirement.CoveringRegionOfResidence) {
// 			matchRegion = true
// 		}

// 		if q.ResidenceState == "" || len(requirement.CoveringStateOfResidence) == 0 {
// 			matchState = true
// 		} else {
// 			for _, state := range requirement.CoveringStateOfResidence {
// 				if strings.EqualFold(state, q.ResidenceState) {
// 					matchState = true
// 					break
// 				}
// 			}
// 		}

// 		if q.ResidenceCity == "" || len(requirement.CoveringCityOfResidence) == 0 {
// 			matchCity = true
// 		} else {
// 			for _, city := range requirement.CoveringCityOfResidence {
// 				if strings.EqualFold(city, q.ResidenceCity) {
// 					matchCity = true
// 					break
// 				}
// 			}
// 		}
// 		if matchContent && matchRegion && matchState && matchCity {
// 			res = append(res, requirement)
// 		}
// 	}

// 	return res, nil
// }

type Contact struct {
	PersonName   string          `json:"person_name"`
	CompanyName  string          `json:"company_name"`
	PhoneNumber  string          `json:"phone_number"`
	EmailAddress string          `json:"email_address"`
	Address      *models.Address `json:"address"`
}

type ShippingLabelInfo struct {
	PackageID   int                          `json:"package_id"` // same with orderId
	ServiceType string                       `json:"service_type"`
	UserID      string                       `json:"user_id"`
	UserEmail   string                       `json:"user_email"`
	Status      string                       `json:"status"`
	Callback    string                       `json:"callback"`
	Data        fedex.ProcessShipmentRequest `json:"data"`
}

func GetContactOfUser(dao db.IDao, ShipmentInfo string) (*fedex.ShipmentContact, error) {
	shipment, err := dao.GetVisaShipment(ShipmentInfo)
	if err != nil {
		return nil, err
	}

	data := &fedex.ShipmentContact{
		City:    shipment.ShippingAddress.City,
		Name:    fmt.Sprintf("%s %s", shipment.ShippingContact.GivenName, shipment.ShippingContact.Surname),
		State:   shipment.ShippingAddress.State,
		Phone:   shipment.ShippingContact.Phone,
		Email:   shipment.ShippingContact.Email,
		Address: shipment.ShippingAddress.Address,
		ZipCode: shipment.ShippingAddress.ZipCode.String,
		Country: shipment.ShippingAddress.Country,
	}
	return data, nil
}

func replaceUserContact(requirements []*models.ShippingRequirement, userContact *Contact) ([]*models.ShippingRequirement, error) {
	// convert struct to string
	js, err := json.Marshal(requirements)
	if err != nil {
		return nil, err
	}
	str := string(js)

	// replace user info
	str = strings.ReplaceAll(str, "$customer_city", userContact.Address.City)
	str = strings.ReplaceAll(str, "$customer_name", userContact.PersonName)
	str = strings.ReplaceAll(str, "$customer_zipcode", userContact.Address.ZipCode.String)
	str = strings.ReplaceAll(str, "$customer_state", userContact.Address.State)
	str = strings.ReplaceAll(str, "$customer_phone", userContact.PhoneNumber)
	str = strings.ReplaceAll(str, "$customer_address", userContact.Address.Address)
	str = strings.ReplaceAll(str, "$customer_region", userContact.Address.Country)
	str = strings.ReplaceAll(str, "$customer_email", userContact.EmailAddress)
	// convert string to struct
	var data []*models.ShippingRequirement
	if err := json.Unmarshal([]byte(str), &data); err != nil {
		return nil, err
	}
	return data, nil
}

type CreateShipmentData struct {
	UserID              string
	UserEmail           string
	SqsShip             *awslib.SQSClient
	SqsNotification     *awslib.SQSClient
	ProductType         string
	OrderID             int
	ProductID           int
	From                *fedex.ShipmentContact
	To                  *fedex.ShipmentContact
	ShippingContent     string
	Config              map[string]any
	ShipmentProcessedBy string
	ShipmentCarrier     string
	Status              string
	ServiceType         string
	OrderServiceType    string
}

func getTypeOfProcessShipment(carrier string) string {
	var processBy string
	switch carrier {
	case "FedEx":
		processBy = "system"
	case "USPS":
		processBy = "admin"
	}
	return processBy
}
func getCallbackType(productType string) string {
	var callbackType string
	switch productType {
	case product.VisaProductType:
		callbackType = "packages"
	case product.ETSProductType:
		callbackType = "ets"
	}
	return callbackType
}

func getPaymentType(input string) string {
	var paymentType string
	switch input {
	case "user_to_consulate":
		paymentType = "RECIPIENT"
	case "consulate_to_user":
		paymentType = "SENDER"
	}
	return paymentType
}
func CreateShipment(data CreateShipmentData) error {
	callbackType := getCallbackType(data.ProductType) // package / ets

	shipDate := time.Now().AddDate(0, 0, 1)
	switch data.ShipmentCarrier {
	case "FedEx":
		{
			msgContent := map[string]any{
				"package_id":   data.OrderID,
				"user_id":      data.UserID,
				"user_email":   data.UserEmail,
				"status":       data.Status,
				"service_type": data.OrderServiceType,
				"callback":     fmt.Sprintf("https://%s/%s-internal/%d/update-shipment-status", data.Config["package_host_name"].(string), callbackType, data.OrderID),
				"data": map[string]any{
					"ship_date":        shipDate,
					"shipper":          data.From,
					"recipient":        data.To,
					"service_type":     data.ServiceType,
					"shipping_carrier": data.ShipmentCarrier,
					"payment_type":     getPaymentType(data.ShippingContent), // SENDER / RECIPIENT
					"shipping_content": data.ShippingContent,
				},
			}

			// Queue to func (s *CarrierProcessor) Carrier(message *sqs.Message)
			if err := data.SqsShip.Send(msgContent); err != nil {
				return err
			}

		}
	case "USPS":
		{
			emailContent := map[string]any{
				"ShippingCarrier": data.ShipmentCarrier,
				"ShipDate":        shipDate.Format(DateFormat),
				"ShippingService": data.ServiceType,
				"Shipper":         []any{data.From},
				"Recipient":       []any{data.To},
				"PackageID":       strconv.Itoa(data.OrderID),
			}
			if data.ProductType == "visa" {
				emailContent["URL"] = fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=visa", data.Config["website_host_name"].(string), data.OrderID)
			} else {
				emailContent["URL"] = fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", data.Config["website_host_name"].(string), data.OrderID)
			}
			if err := notificationGenerateLabelToAdmin(data.SqsNotification, data.Config["admin_email"].(string), emailContent); err != nil {
				return err
			}
		}

	}
	return nil
}
func notificationGenerateLabelToAdmin(sqsNotification *awslib.SQSClient, to string, parameters map[string]any) error {
	// notification review package to AD (if have)
	message := map[string]any{
		"template_name": GenerateShipmentEmailTemplateName,
		"to":            to,
		"parameters":    parameters,
	}
	// send message to notification queue
	err := sqsNotification.Send(message)
	if err != nil {
		return err
	}
	return nil
}
