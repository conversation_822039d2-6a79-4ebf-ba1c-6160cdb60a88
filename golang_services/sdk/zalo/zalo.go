package zalo

import (
	"bytes"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"

	"github.com/go-resty/resty/v2"
)

func GetGroupID(groupName string) string {
	if os.Getenv("ad_env") == "prod" {
		switch groupName {
		case "INTERNAL_NOTIFICATION":
			return "https://zalo.me/g/ltphvo214"
		}
	} else {
		switch groupName {
		case "INTERNAL_NOTIFICATION":
			return "https://zalo.me/g/hlkdtr874"
		}
	}
	return ""
}

func SendZaloSMS(shareLink, message string) error {
	resp, err := resty.New().R().SetFormData(map[string]string{
		"shareLink": shareLink,
		"message":   message,
	}).Post(`https://zalo.ariadirectcorp.com/api/zalo_chat/group/send_message`)
	if err != nil {
		return err
	}
	fmt.Println(resp.String())
	return nil
}

func SendZaloFile(shareLink, fileURL string) {
	client := resty.New()

	resp, err := client.R().
		SetDoNotParseResponse(true).
		Get(fileURL)

	if err != nil {
		log.Fatalf("Failed to fetch file: %v", err)
	}
	defer resp.RawBody().Close()

	var buffer bytes.Buffer
	writer := multipart.NewWriter(&buffer)

	err = writer.WriteField("shareLink", shareLink)
	if err != nil {
		log.Fatalf("Failed to write shareLink field: %v", err)
	}

	part, err := writer.CreateFormFile("file", strings.Split(filepath.Base(fileURL), "?")[0])
	if err != nil {
		log.Fatalf("Failed to create form file: %v", err)
	}
	_, err = io.Copy(part, resp.RawBody())
	if err != nil {
		log.Fatalf("Failed to copy file content: %v", err)
	}

	err = writer.Close()
	if err != nil {
		log.Fatalf("Failed to close writer: %v", err)
	}

	resp, err = client.R().
		SetHeader("Content-Type", writer.FormDataContentType()).
		SetBody(&buffer).
		Post("https://zalo.ariadirectcorp.com/api/zalo_chat/group/send_file")

	if err != nil {
		log.Fatalf("Failed to send POST request: %v", err)
	}

	fmt.Println(resp)
}
