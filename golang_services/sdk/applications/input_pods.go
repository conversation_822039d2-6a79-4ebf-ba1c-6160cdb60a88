package applications

import (
	"fmt"
	"path"
	"sort"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

// return input pods grouped by category and sorted within eatch category
func InputPodsMapToSlice(inputPods *models.InputPods) []*models.InputPod {
	if inputPods == nil {
		return nil
	}
	var res []*models.InputPod
	for _, v := range *inputPods {
		res = append(res, v)
	}
	res = sortByOrder(res)
	return res
}

func sortByOrder(pods []*models.InputPod) []*models.InputPod {
	sort.SliceStable(pods, func(i, j int) bool {
		return pods[i].Order < pods[j].Order
	})
	return pods
}

func ParseUrlToS3(pod *models.InputPod) *models.InputPod {
	if pod.TypeFE == "upload_file" && pod.Value != nil && pod.Value.FE != nil {
		s, ok := pod.Value.FE.(string)
		if !ok {
			log.Warn().Interface("fe_value", pod.Value.FE).Msg("input value fe is not a string for upload file")
		} else {
			bucket, key, err := utils.UrlToS3BucketAndKey(s)
			if err != nil {
				log.Warn().Str("fe_value", s).
					Str("err", err.Error()).
					Msg("failed to parse url to s3")
			} else {
				pod.Value.FE = path.Join(bucket, key)
			}
		}
		return pod
	}
	if len(pod.OptionChoice) > 0 {
		for _, v := range pod.OptionChoice {
			for _, op := range v {
				if op.TypeFE == "upload_file" && op.Value != nil && op.Value.FE != nil {
					s, ok := op.Value.FE.(string)
					if !ok {
						log.Warn().Interface("fe_value", op.Value.FE).Msg("input value fe is not a string for upload file")
					} else {
						bucket, key, err := utils.UrlToS3BucketAndKey(s)
						if err != nil {
							log.Warn().Str("fe_value", s).
								Str("err", err.Error()).
								Msg("failed to parse url to s3")
						} else {
							op.Value.FE = fmt.Sprintf("%s/%s", bucket, key)
						}
					}
				}
			}
		}
	}
	return pod
}

func PresignInputPods(ps *aws.S3Svc, pods []*models.InputPod, exp time.Duration) error {
	for _, pod := range pods {
		// if err := PresignInputPod(ps, pod, exp); err != nil {
		// 	return err
		// }
		recursionPod(ps, pod, nil)
	}
	return nil
}

func PresignInputPod(ps *aws.S3Svc, pod *models.InputPod, exp time.Duration) error {
	if funk.ContainsString([]string{"upload_file", "upload_files", "scan_upload_file"}, pod.TypeFE) && pod.Value != nil && pod.Value.FE != nil {
		s, ok := pod.Value.FE.(string)
		if !ok {
			log.Warn().Interface("fe_value", pod.Value.FE).Msg("input value fe is not a string for upload file")
		} else {
			bucket, key, err := utils.UrlToS3BucketAndKey(s)
			if err != nil {
				return err
			}
			url, err := ps.PresignUrl(bucket, key, exp)
			if err != nil {
				return err
			}
			pod.Value.FE = url
		}
		return nil
	}
	if pod.TypeFE == "upload_files" && pod.Value != nil && pod.Value.FE != nil {
		urls, ok := pod.Value.FE.([]any)
		if !ok {
			log.Warn().Interface("fe_value", pod.Value.FE).Msg("input value fe is not a string array for upload file")
		} else {
			sPreSignedURLs := []string{}
			for _, url := range urls {
				bucket, key, _ := utils.UrlToS3BucketAndKey(url.(string))
				urlPreSigned, err := ps.PresignUrl(bucket, key, exp)
				if err != nil {
					return err
				}
				sPreSignedURLs = append(sPreSignedURLs, urlPreSigned)
			}

			pod.Value.FE = sPreSignedURLs
		}
		return nil
	}
	if len(pod.OptionChoice) > 0 {
		for _, v := range pod.OptionChoice {
			for _, op := range v {
				if funk.ContainsString([]string{"upload_file", "upload_files", "scan_upload_file"}, op.TypeFE) && op.Value != nil && op.Value.FE != nil {
					s, ok := op.Value.FE.(string)
					if !ok {
						log.Warn().Interface("fe_value", op.Value.FE).Msg("input value fe is not a string for upload file")
					} else {
						bucket, key, err := utils.UrlToS3BucketAndKey(s)
						if err != nil {
							return err
						}
						url, err := ps.PresignUrl(bucket, key, 15*time.Minute)
						if err != nil {
							return err
						}
						op.Value.FE = url
					}
				}
			}
		}
	}
	return nil
}

func recursionPod(ps *aws.S3Svc, pod *models.InputPod, opPod *models.InputPod) {
	if pod != nil {
		podJ := utils.StructToJSON(pod)
		if funk.ContainsString([]string{"upload_file", "upload_files", "scan_upload_file"}, pod.TypeFE) {
			if pod.Value == nil {
				pod.Value = &models.InputValue{}
			}
			pod.Value.FE = extendExpiredS3URL(ps, podJ.Get("value.fe"))
		}

		if len(pod.OptionChoice) > 0 {
			for k := range pod.OptionChoice {
				for i := range pod.OptionChoice[k] {
					recursionPod(ps, nil, pod.OptionChoice[k][i])
				}

			}
		}
	}

	if opPod != nil {
		opPodJ := utils.StructToJSON(opPod)
		if funk.ContainsString([]string{"upload_file", "upload_files", "scan_upload_file"}, opPod.TypeFE) {
			if opPod.Value == nil {
				opPod.Value = &models.InputValue{}
			}
			opPod.Value.FE = extendExpiredS3URL(ps, opPodJ.Get("value.fe"))
		}

		if len(opPod.OptionChoice) > 0 {
			for k := range opPod.OptionChoice {
				for i := range opPod.OptionChoice[k] {
					recursionPod(ps, nil, opPod.OptionChoice[k][i])
				}

			}
		}
	}

}

func extendExpiredS3URL(ps *aws.S3Svc, value gjson.Result) any {
	if value.IsArray() {
		result := []string{}
		for _, item := range value.Array() {
			if val := item.String(); val != "" {
				bucket, key, err := utils.UrlToS3BucketAndKey(val)
				if err != nil {
					fmt.Printf("extendExpiredS3URL error: %v", err)
				}
				url, err := ps.PresignUrl(bucket, key, 24*time.Hour)
				if err != nil {
					fmt.Printf("extendExpiredS3URL error: %v", err)
				}
				result = append(result, url)
			}
		}
		return result
	}

	if value.String() != "" {
		bucket, key, err := utils.UrlToS3BucketAndKey(value.String())
		if err != nil {
			fmt.Printf("extendExpiredS3URL error: %v", err)
		}
		url, err := ps.PresignUrl(bucket, key, 24*time.Hour)
		if err != nil {
			fmt.Printf("extendExpiredS3URL error: %v", err)
		}
		return url
	}
	return nil
}
