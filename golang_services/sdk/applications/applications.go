package applications

import (
	"fmt"
	"net/http"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/pariz/gountries"
	"github.com/tidwall/gjson"

	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

var countryQuery = gountries.New()

func CallToGenerateETSForm(orderID int, orderForms []string, taskForms map[int64][]string) error {
	baseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("api_base_url").String()
	token := gjson.Parse(os.Getenv("ad_api_token")).Get("token").String()

	url := fmt.Sprintf("%s/v1/packer/internals-packers/%v/generate-ets-form", baseURL, orderID)
	data := utils.StructToJSON(gin.H{"order_forms": orderForms, "task_forms": taskForms}).Raw

	fmt.Println(url)
	fmt.Println(token)
	fmt.Println(data)

	// Create a new HTTP request
	req, err := http.NewRequest("POST", url, strings.NewReader(data))
	if err != nil {
		return err
	}

	// Set the request headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-ad-token", token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Check the response status code
	if resp.StatusCode >= 400 {
		return fmt.Errorf("unable to send the package data update request")
	}

	return nil
}
