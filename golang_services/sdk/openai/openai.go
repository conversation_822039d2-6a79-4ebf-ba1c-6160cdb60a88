package openai

import (
	"context"
	"fmt"
	"time"

	gpt3 "github.com/PullRequestInc/go-gpt3"
	"github.com/go-resty/resty/v2"
)

func GetGPTChatResponse(src string) string {
	apiKey := "***************************************************"
	client := gpt3.NewClient(apiKey)
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Hour)
	defer cancel()
	fmt.Println(src)
	result := ""
	err := client.CompletionStreamWithEngine(ctx, "gpt-3.5-turbo", gpt3.CompletionRequest{
		Prompt: []string{
			src,
		},
		MaxTokens:   gpt3.IntPtr(3000),
		Temperature: gpt3.Float32Ptr(0),
	}, func(resp *gpt3.CompletionResponse) {
		// fmt.Print(resp.Choices[0].Text)
		result += resp.Choices[0].Text
	})

	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(result)
	return result
}

func GetGPTChatResponseV2(message string) string {
	url := "https://anika-api-dev.anika-ai.tech/social/zalo-mini-app/chat"

	client := resty.New()
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Host", "anika-api-dev.anika-ai.tech").
		SetHeader("Origin", "https://h5.zdn.vn").
		SetHeader("Referer", "https://h5.zdn.vn/").
		SetHeader("Sec-Ch-Ua", "\"Android WebView\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"").
		SetHeader("User-Agent", "Mozilla/5.0 (Linux; Android 10; SM-N960N Build/QP1A.190711.020;) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.99 Mobile Safari/537.36 Zalo android/12100737 ZaloTheme/light ZaloLanguage/vi").
		SetHeader("X-Requested-With", "com.zing.zalo").
		SetBody(map[string]string{
			"user_id":    "5167139517885006103",
			"history_id": "0dfd9554-2f6e-4eb2-b6cc-8a909b3627e1",
			"question":   message,
		}).
		Post(url)
	if err != nil {
		fmt.Println(err)
		return ""
	}
	fmt.Println(resp.String())
	return resp.String()
}
