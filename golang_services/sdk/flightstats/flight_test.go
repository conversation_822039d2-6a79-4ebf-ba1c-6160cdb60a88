package flightstats

import (
	"fmt"
	"testing"
	"time"
)

func TestGetFlightDataV2(t *testing.T) {
	// Test with a real flight number and date
	flightNumber := "CX-764"
	date := time.Date(2025, 4, 16, 0, 0, 0, 0, time.UTC)

	result, err := GetFlightDataV2(flightNumber, date)
	if err != nil {
		t.Fatalf("Error getting flight data: %v", err)
	}

	// Print the results for inspection
	fmt.Println("Flight Status:", result["flightStatus"])
	fmt.Println("Departure Airport:", result["departureAirport"])
	fmt.Println("Arrival Airport:", result["arrivalAirport"])

	// Print the UTC times (main fields for compatibility with v1)
	fmt.Println("Scheduled Departure UTC:", result["scheduledDepartureUTC"])
	fmt.Println("Actual Departure UTC:", result["actualDepartureUTC"])
	fmt.Println("Scheduled Arrival UTC:", result["scheduledArrivalUTC"])
	fmt.Println("Actual Arrival UTC:", result["actualArrivalUTC"])

	// Print additional information
	fmt.Println("Departure Terminal/Gate:", result["departureTerminalGate"])
	fmt.Println("Arrival Terminal/Gate:", result["arrivalTerminalGate"])
	fmt.Println("Aircraft Type:", result["aircraftType"])

	// Verify that we got the required data (v1 compatibility fields)
	if result["scheduledDepartureUTC"] == "" {
		t.Error("Expected scheduledDepartureUTC, got empty string")
	}
	if result["actualDepartureUTC"] == "" {
		t.Error("Expected actualDepartureUTC, got empty string")
	}
	if result["scheduledArrivalUTC"] == "" {
		t.Error("Expected scheduledArrivalUTC, got empty string")
	}
	if result["actualArrivalUTC"] == "" {
		t.Error("Expected actualArrivalUTC, got empty string")
	}

	// Verify the format of the UTC times (should be ISO 8601 format)
	for _, field := range []string{"scheduledDepartureUTC", "actualDepartureUTC", "scheduledArrivalUTC", "actualArrivalUTC"} {
		if result[field] != "" {
			_, err := time.Parse(time.RFC3339, result[field])
			if err != nil {
				t.Errorf("Field %s is not in ISO 8601 format: %s", field, result[field])
			}
		}
	}
}
