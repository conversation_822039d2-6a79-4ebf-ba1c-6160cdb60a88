package flightstats

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
)

func GetFlightData(flightNumber string, date time.Time) (map[string]string, error) {
	client := resty.New()

	parts := strings.SplitN(flightNumber, "-", 2)
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid flight number format")
	}
	airlineCode, flightNum := parts[0], parts[1]

	url := fmt.Sprintf("https://www.flightstats.com/v2/flight-tracker/%s/%s?year=%d&month=%d&date=%d", airlineCode, flightNum, date.Year(), date.Month(), date.Day())

	resp, err := client.R().Get(url)
	if err != nil {
		return nil, err
	}
	// Extract __NEXT_DATA__ from HTML
	re := regexp.MustCompile(`__NEXT_DATA__ = (.+?);`)
	matches := re.FindStringSubmatch(string(resp.Body()))
	if len(matches) < 2 {
		return nil, fmt.Errorf("__NEXT_DATA__ not found in response")
	}
	nextData := strings.ReplaceAll(matches[1], `\"`, "")
	fmt.Println(nextData)

	flightJ := gjson.Parse(nextData).Get("props.initialState.flightTracker.flight")
	return map[string]string{
		"scheduledDepartureUTC": flightJ.Get("schedule.scheduledDepartureUTC").String(),
		"actualDepartureUTC":    flightJ.Get("schedule.estimatedActualDepartureUTC").String(),
		"scheduledArrivalUTC":   flightJ.Get("schedule.scheduledArrivalUTC").String(),
		"actualArrivalUTC":      flightJ.Get("schedule.estimatedActualArrivalUTC").String(),
	}, nil
}

func GetFlightDataV2(flightNumber string, date time.Time) (map[string]string, error) {
	client := resty.New()

	parts := strings.SplitN(flightNumber, "-", 2)
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid flight number format")
	}
	airlineCode, flightNum := parts[0], strings.Split(parts[1], " ")[0]

	url := "https://www.flightview.com/TravelTools/FlightTrackerQueryResults.asp"

	formData := map[string]string{
		"qtype":      "sfi",
		"sfw":        "/FV/TravelTools/Main",
		"whenArrDep": "dep",
		"namal":      airlineCode,
		"al":         airlineCode,
		"fn":         flightNum,
		"whenDate":   date.Format("20060102"),
		"input":      "Track Flight",
	}

	resp, err := client.R().
		SetHeaders(map[string]string{
			"Accept":                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
			"Accept-Language":           "en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7",
			"Content-Type":              "application/x-www-form-urlencoded",
			"Origin":                    "https://www.flightview.com",
			"Referer":                   "https://www.flightview.com/TravelTools/default.asp",
			"User-Agent":                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
			"Upgrade-Insecure-Requests": "1",
		}).
		SetFormData(formData).
		Post(url)

	if err != nil {
		return nil, err
	}

	htmlContent := string(resp.Body())

	// Extract departure information
	scheduledDepartureTime := extractDataWithRegex(htmlContent, `Scheduled Time:</td>[\s\S]*?<td class="search-results-table-data FlightTrackerData">([^<]+)`)
	estimatedDepartureTime := extractDataWithRegex(htmlContent, `Estimated Time:</td>[\s\S]*?<td class="search-results-table-data FlightTrackerData">([^<]+)`)

	// Extract arrival information
	scheduledArrivalTime := extractDataWithRegex(htmlContent, `Scheduled Time:</td>[\s\S]*?<td class="search-results-table-data FlightTrackerData">([^<]+)`, 2)
	estimatedArrivalTime := extractDataWithRegex(htmlContent, `Estimated Time:</td>[\s\S]*?<td class="search-results-table-data FlightTrackerData">([^<]+)`, 2)

	// Parse times to UTC ISO format
	scheduledDepartureUTC := parseFlightViewTime(scheduledDepartureTime, date)
	actualDepartureUTC := parseFlightViewTime(estimatedDepartureTime, date)
	scheduledArrivalUTC := parseFlightViewTime(scheduledArrivalTime, date)
	actualArrivalUTC := parseFlightViewTime(estimatedArrivalTime, date)

	// Return the same fields as v1 for compatibility
	return map[string]string{
		"scheduledDepartureUTC": scheduledDepartureUTC,
		"actualDepartureUTC":    actualDepartureUTC,
		"scheduledArrivalUTC":   scheduledArrivalUTC,
		"actualArrivalUTC":      actualArrivalUTC,
	}, nil
}

// Helper function to extract data from HTML content using regex
// occurrence parameter is used to find the nth occurrence of the pattern (default is 1)
func extractDataWithRegex(htmlContent, pattern string, occurrence ...int) string {
	re := regexp.MustCompile(pattern)
	matches := re.FindAllStringSubmatch(htmlContent, -1)

	occurrenceIndex := 0
	if len(occurrence) > 0 && occurrence[0] > 1 && len(matches) >= occurrence[0] {
		occurrenceIndex = occurrence[0] - 1
	}

	if len(matches) > occurrenceIndex && len(matches[occurrenceIndex]) > 1 {
		return strings.TrimSpace(matches[occurrenceIndex][1])
	}
	return ""
}

// parseFlightViewTime converts a time string from FlightView format to UTC ISO format
// Example input: "6:20 PM,&nbsp;Apr&nbsp;16"
// Example output: "2025-04-16T18:20:00Z"
func parseFlightViewTime(timeStr string, referenceDate time.Time) string {
	if timeStr == "" {
		return ""
	}

	// Clean up HTML entities and extra spaces
	timeStr = strings.ReplaceAll(timeStr, "&nbsp;", " ")
	timeStr = regexp.MustCompile(`\s+`).ReplaceAllString(timeStr, " ")
	timeStr = strings.TrimSpace(timeStr)

	// Extract time components
	// Format example: "6:20 PM, Apr 16"
	parts := strings.Split(timeStr, ",")
	if len(parts) < 2 {
		return ""
	}

	timePart := strings.TrimSpace(parts[0]) // "6:20 PM"
	datePart := strings.TrimSpace(parts[1]) // "Apr 16"

	// Parse time
	timeLayout := "3:04 PM"
	timeParsed, err := time.Parse(timeLayout, timePart)
	if err != nil {
		return ""
	}

	// Parse date
	dateLayout := "Jan 2"
	dateParsed, err := time.Parse(dateLayout, datePart)
	if err != nil {
		return ""
	}

	// Combine date and time with the reference year
	result := time.Date(
		referenceDate.Year(),
		dateParsed.Month(),
		dateParsed.Day(),
		timeParsed.Hour(),
		timeParsed.Minute(),
		0, 0,
		time.UTC,
	)

	// Return in ISO format
	return result.Format(time.RFC3339)
}
