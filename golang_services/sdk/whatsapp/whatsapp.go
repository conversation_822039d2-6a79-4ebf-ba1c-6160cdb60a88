package whatsapp

import (
	"fmt"

	"github.com/go-resty/resty/v2"
)

func AddUsersToGroup(group_id, message string, phoneNumbers []string) error {
	resp, err := resty.New().R().SetBody(map[string]any{
		"group_id":       group_id,
		"phone_numbers":  phoneNumbers,
		"send_invite":    true,
		"invite_message": message,
	}).Post(`https://zalo.ariadirectcorp.com/api/whatsapp/group/invite_users`)
	if err != nil {
		return err
	}
	fmt.Println(resp.String())
	return nil
}

func RemoveUsersFromGroup(group_id string, phoneNumbers []string) error {
	resp, err := resty.New().R().SetBody(map[string]any{
		"group_id":      group_id,
		"phone_numbers": phoneNumbers,
	}).Post(`https://zalo.ariadirectcorp.com/api/whatsapp/group/remove_users`)
	if err != nil {
		return err
	}
	fmt.Println(resp.String())
	return nil
}

func SendMessageToGroup(group_id string, message string) error {
	resp, err := resty.New().R().SetBody(map[string]any{
		"group_id": group_id,
		"message":  message,
	}).Post(`https://zalo.ariadirectcorp.com/api/whatsapp/group/send_message`)
	if err != nil {
		return err
	}
	fmt.Println(resp.String())
	return nil
}

type Participant struct {
	Admin bool   `json:"admin"`
	JID   string `json:"jid"`
}

type Group struct {
	ID                string        `json:"id"`
	Name              string        `json:"name"`
	Owner             string        `json:"owner"`
	Participants      []Participant `json:"participants"`
	ParticipantsCount int           `json:"participants_count"`
}

type GroupListResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Groups      []Group `json:"groups"`
		GroupsCount int     `json:"groups_count"`
	} `json:"data"`
}

func GetGroupList() ([]Group, error) {
	resp, err := resty.New().R().
		SetResult(&GroupListResponse{}).
		Get(`https://zalo.ariadirectcorp.com/api/whatsapp/group/list`)
	if err != nil {
		return nil, err
	}
	result := resp.Result().(*GroupListResponse)
	if !result.Success {
		return nil, fmt.Errorf("API request failed: %s", result.Message)
	}
	return result.Data.Groups, nil
}
