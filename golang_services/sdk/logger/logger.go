package logger

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/gin-contrib/logger"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

func SetJsonZeroLoggerWithLevel(level string) {
	switch strings.ToLower(level) {
	case "info":
		zerolog.SetGlobalLevel(zerolog.InfoLevel)
	case "warn", "warning":
		zerolog.SetGlobalLevel(zerolog.WarnLevel)
	case "error":
		zerolog.SetGlobalLevel(zerolog.ErrorLevel)
	case "trace", "debug":
		zerolog.SetGlobalLevel(zerolog.DebugLevel)
	default:
		zerolog.SetGlobalLevel(zerolog.InfoLevel)
	}
}

func LoggerMW() gin.HandlerFunc {
	conf := logger.Config{
		UTC:            true,
		SkipPathRegexp: regexp.MustCompile(`^\/v\d/[^\/]+\/(:?status|version)`),
	}
	return logger.SetLogger(conf)
}

func CustomLoggerMW() gin.HandlerFunc {
	return func(c *gin.Context) {
		if regexp.MustCompile(`^\/v\d/[^\/]+\/(:?status|version)`).MatchString(c.Request.URL.Path) {
			c.Next()
			return
		}
		buf, _ := ioutil.ReadAll(c.Request.Body)
		rdr1 := ioutil.NopCloser(bytes.NewBuffer(buf))
		rdr2 := ioutil.NopCloser(bytes.NewBuffer(buf))

		buffer := &bytes.Buffer{}
		buffer.ReadFrom(rdr1)
		if c.Request.Header.Get("Content-Type") == "application/json" {
			fmt.Println(c.Request.Method, c.Request.URL.Path, "\n", buffer.String())
		}
		if val, ok := c.Request.Header["X-Access-Token"]; ok && len(val) > 0 {
			fmt.Println("x-access-token:", val[0])
		}
		c.Request.Body = rdr2
		c.Next()
	}
}

func InfoToGraylog(short string, full string, extra map[string]any) {
	LogToGraylog(6, short, full, extra)
}

func WarnToGraylog(short string, full string, extra map[string]any) {
	LogToGraylog(4, short, full, extra)
}

func ErrorToGraylog(short string, full string, extra map[string]any) {
	LogToGraylog(3, short, full, extra)
}

// RequestLoggerConfig holds configuration for the middleware
type RequestLoggerConfig struct {
	// SkipPaths defines which paths to skip logging
	SkipPaths []string
	// CustomFields allows adding additional static fields to all logs
	CustomFields map[string]any
	// Environment for conditional logging (e.g., "prod", "dev")
	Environment string
}

// DefaultRequestLoggerConfig returns a default configuration
func DefaultRequestLoggerConfig() RequestLoggerConfig {
	return RequestLoggerConfig{
		SkipPaths:    []string{"/v1/pkg/status"},
		CustomFields: make(map[string]any),
		Environment:  "development",
	}
}

type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// GraylogLoggerMiddleware creates a gin middleware for request logging
func GraylogLoggerMiddleware(config RequestLoggerConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip logging for specified paths
		if regexp.MustCompile(`^\/v\d/[^\/]+\/(:?status|version)`).MatchString(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Start timer
		start := time.Now()

		// Read request body first, before any processing
		var requestBody string
		if c.Request.Method == "PUT" || c.Request.Method == "POST" {
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err == nil {
				requestBody = string(bodyBytes)
				// Restore the request body
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			}
		}

		// Create a custom response writer to capture the response body
		bodyWriter := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = bodyWriter

		// Process request
		c.Next()

		// Stop timer
		duration := time.Since(start)

		// Prepare log data with standard fields
		logData := map[string]any{
			"client_ip":     c.ClientIP(),
			"method":        c.Request.Method,
			"host":          c.Request.Host,
			"path":          c.Request.URL.Path,
			"status_code":   c.Writer.Status(),
			"latency_ms":    duration.Milliseconds(),
			"latency_human": duration.String(),
			"user_agent":    c.Request.UserAgent(),
			"request_id":    c.GetString("RequestID"),
			"bytes_out":     c.Writer.Size(),
			"route":         c.FullPath(),
			"error_count":   len(c.Errors),
		}

		// Add custom fields
		for k, v := range config.CustomFields {
			logData[k] = v
		}

		logData["headers"] = c.Request.Header

		responseBody := bodyWriter.body.String()
		logData["response_body"] = responseBody

		// Check for x-access-token and parse username
		if accessToken := c.GetHeader("x-access-token"); accessToken != "" {
			if username, err := parseUsernameFromToken(accessToken); err == nil {
				logData["username"] = username
			}
		}

		// Add error details if any
		if len(c.Errors) > 0 {
			logData["errors"] = c.Errors.String()
		}

		// Log request body for PUT and POST methods
		if requestBody != "" {
			// const maxBodyLength = 1000
			// if len(requestBody) > maxBodyLength {
			// 	requestBody = requestBody[:maxBodyLength] + "... (truncated)"
			// }
			logData["request_body"] = requestBody
		}

		// Rest of the code remains the same...

		// Determine log level based on status code
		var logLevel int32
		statusCode := c.Writer.Status()
		switch {
		case statusCode >= 500:
			logLevel = 3 // Error
		case statusCode >= 400:
			logLevel = 4 // Warning
		case statusCode >= 300:
			logLevel = 5 // Notice
		default:
			logLevel = 6 // Info
		}

		// Create messages
		shortMessage := fmt.Sprintf("%s %s %d", c.Request.Method, c.Request.URL.Path, statusCode)
		fullMessage := fmt.Sprintf("Request processed in %v with status %d", duration, statusCode)

		// Log to Graylog in production
		if os.Getenv("ad_env") == "prod" {
			LogToGraylog(logLevel, shortMessage, fullMessage, logData)
		}

		// Always log to console using zerolog
		event := log.Info()
		if statusCode >= 400 {
			event = log.Error()
		}

		event.
			Int("status", statusCode).
			Str("method", c.Request.Method).
			Str("path", c.Request.URL.Path).
			Dur("latency", duration).
			Int("bytes", c.Writer.Size()).
			Str("ip", c.ClientIP())

		if len(c.Errors) > 0 {
			event.Str("errors", c.Errors.String())
		}

		// Add request body to zerolog output for PUT and POST
		if requestBody != "" {
			event.Str("request_body", requestBody)
		}

		event.Msg(shortMessage)
	}
}

// Add this new function to parse the username from the token
func parseUsernameFromToken(token string) (string, error) {
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return "", fmt.Errorf("invalid token format")
	}

	payload, err := base64.RawURLEncoding.DecodeString(parts[1])
	if err != nil {
		return "", fmt.Errorf("failed to decode token payload: %v", err)
	}

	var claims map[string]any
	if err := json.Unmarshal(payload, &claims); err != nil {
		return "", fmt.Errorf("failed to parse token claims: %v", err)
	}

	username, ok := claims["username"].(string)
	if !ok {
		return "", fmt.Errorf("username not found in token claims")
	}

	return username, nil
}
