package logger

import (
	"encoding/json"
	"fmt"
	"net"
	"os"
	"sync"
	"time"
)

var (
	graylogClient *Client
	hostname      string
	mu            sync.RWMutex
)

type Client struct {
	conn     net.Conn
	address  string
	hostname string
}

// InitGraylogLogger initializes the Graylog logger
func InitGraylogLogger() error {
	mu.Lock()
	defer mu.Unlock()

	var err error
	hostname, err = os.Hostname()
	if err != nil {
		hostname = "unknown"
	}

	client, err := connect("**************:12201")
	if err != nil {
		return fmt.Errorf("failed to initialize graylog client: %w", err)
	}

	graylogClient = client
	return nil
}

func connect(address string) (*Client, error) {
	conn, err := net.DialTimeout("tcp", address, 5*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %w", address, err)
	}

	return &Client{
		conn:     conn,
		address:  address,
		hostname: hostname,
	}, nil
}

// LogToGraylog sends a message to Graylog with the specified level and additional fields
func LogToGraylog(level int32, short string, full string, extra map[string]any) error {
	mu.RLock()
	if graylogClient == nil {
		mu.RUnlock()
		return fmt.Errorf("graylog logger not initialized")
	}
	mu.RUnlock()

	msg := map[string]any{
		"version":       "1.1",
		"host":          hostname,
		"short_message": short,
		"level":         level,
		"timestamp":     time.Now().Unix(),
	}

	if full != "" {
		msg["full_message"] = full
	}

	for k, v := range extra {
		if k != "" {
			msg["_"+k] = v
		}
	}

	jsonMsg, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to marshal JSON: %w", err)
	}

	// Add newline character for TCP GELF
	jsonMsg = append(jsonMsg, '\n')

	// Set write deadline and implement retry logic
	for retry := 0; retry < 3; retry++ {
		mu.RLock()
		if graylogClient.conn != nil {
			err = graylogClient.conn.SetWriteDeadline(time.Now().Add(5 * time.Second))
			if err == nil {
				_, err = graylogClient.conn.Write(jsonMsg)
			}
		} else {
			err = fmt.Errorf("connection is nil")
		}
		mu.RUnlock()

		if err == nil {
			return nil
		}

		if retry < 2 {
			time.Sleep(time.Second)
			mu.Lock()
			newClient, connectErr := connect(graylogClient.address)
			if connectErr == nil {
				if graylogClient.conn != nil {
					graylogClient.conn.Close()
				}
				graylogClient = newClient
			}
			mu.Unlock()
		}
	}

	return fmt.Errorf("failed to send message after 3 attempts: %w", err)
}
