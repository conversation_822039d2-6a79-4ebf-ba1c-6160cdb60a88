package idgenerator

import (
	"fmt"

	uuid "github.com/satori/go.uuid"
)

const (
	maxLength = 20
)

type AriaPaymentTransactionIDGenerator struct {
	namespace uuid.UUID
}

func NewAriaPaymentTransactionIDGenerator(ns uuid.UUID) *AriaPaymentTransactionIDGenerator {
	return &AriaPaymentTransactionIDGenerator{namespace: ns}
}

func (g *AriaPaymentTransactionIDGenerator) GenIDByPkgID(pkgID int) string {
	name := fmt.Sprintf("%d%s", pkgID, uuid.NewV4().String())
	return uuid.NewV5(g.namespace, name).String()[:maxLength]
}
