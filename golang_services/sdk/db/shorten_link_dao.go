package db

import (
	"fmt"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

func (dao *Dao) CreateShortenLink(item *models.ShortenLink) error {
	if item == nil {
		return nil
	}

	newRow := map[string]any{
		"shorten_code": item.ShortenCode,
		"url":          item.URL,
		"expired_at":   item.ExpiredAt,
		"created_at":   time.Now(),
		"updated_at":   time.Now(),
	}

	_, err := dao.Db.PSQL.Insert("shorten_links").SetMap(newRow).RunWith(dao.Db.Db).Exec()
	return err
}

func (dao *Dao) GetShortenLink(code string) (*models.ShortenLink, error) {
	rows, err := dao.Db.PSQL.Select(" id, shorten_code, url, expired_at, created_at, updated_at").
		From("shorten_links").Where("shorten_code = ?", code).
		RunWith(dao.Db.Db).Query()
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	if rows.Next() {
		item := new(models.ShortenLink)
		rows.Scan(
			&item.ID,
			&item.ShortenCode,
			&item.URL,
			&item.ExpiredAt,
			&item.CreatedAt,
			&item.UpdatedAt,
		)
		return item, nil
	}
	return nil, fmt.Errorf("not found")
}
