package db

import (
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/Masterminds/squirrel"
	"github.com/thoas/go-funk"
)

func (dao *Dao) GetInvoiceList(req models.InvoiceQueryReq) (*models.InvoiceQueryRes, error) {
	query := dao.Db.PSQL.Select("id, invoice_from, invoice_to, org_id, org_type, org_name, invoice_number, po_number, sub_total, total, items, invoice_file, report_file, status, created_at, updated_at, deleted_at").
		From("corporation_invoices").
		Where("deleted_at IS NULL")
	if req.Limit > 0 {
		query = query.Limit(req.Limit)
	}
	if req.Offset > 0 {
		query = query.Offset(req.Offset)
	}
	if req.Query != "" {
		query = query.Where("(invoice_number ILIKE ? OR org_name ILIKE ?)", "%"+req.Query+"%", "%"+req.Query+"%")
	}
	if len(req.OrgType) > 0 {
		query = query.Where(squirrel.Eq{"org_type ": req.OrgType})
	}
	if req.OrgID > 0 {
		query = query.Where("org_id = ?", req.OrgID)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	if funk.ContainsString([]string{"invoice_number", "org_type", "org_name", "invoice_from", "invoice_to", "created_at", "updated_at"}, req.SortField) {
		if strings.ToLower(req.SortOrder) != "desc" {
			req.SortOrder = "asc"
		}
		query = query.OrderBy(req.SortField + " " + req.SortOrder)
	} else {
		query = query.OrderBy("id DESC") // default
	}
	sql, args, _ := query.ToSql()
	rows, err := dao.Db.Db.Queryx(sql, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	result := &models.InvoiceQueryRes{
		Data:    []models.CorporationInvoice{},
		Success: true,
		Offset:  req.Offset,
		Limit:   req.Limit,
	}
	for rows.Next() {
		var item models.CorporationInvoice
		if err = rows.Scan(
			&item.ID,
			&item.From,
			&item.To,
			&item.OrgID,
			&item.OrgType,
			&item.OrgName,
			&item.InvoiceNumber,
			&item.PONumber,
			&item.SubTotal,
			&item.Total,
			&item.Items,
			&item.InvoiceFile,
			&item.ReportFile,
			&item.Status,
			&item.CreatedAt,
			&item.UpdatedAt,
			&item.DeletedAt,
		); err != nil {
			return nil, err
		}
		result.Data = append(result.Data, item)
	}

	sqlCount := utils.QueryCountItems(sql)
	if err := dao.Db.Db.Get(&result.Total, sqlCount, args...); err != nil {
		return nil, err
	}
	return result, nil
}

func (dao *Dao) GetInvoice(id int) (*models.CorporationInvoice, error) {
	result := &models.CorporationInvoice{}
	err := dao.Db.PSQL.Select("id, invoice_from, invoice_to, org_id, org_type, org_name, invoice_number, po_number, sub_total, total, items, invoice_file, report_file, status, created_at, updated_at, deleted_at").From("corporation_invoices").Where("id = ?", id).RunWith(dao.Db.Db).
		QueryRow().Scan(&result.ID, &result.From, &result.To, &result.OrgID, &result.OrgType, &result.OrgName, &result.InvoiceNumber, &result.PONumber, &result.SubTotal, &result.Total, &result.Items, &result.InvoiceFile, &result.ReportFile, &result.Status, &result.CreatedAt, &result.UpdatedAt, &result.DeletedAt)

	return result, err
}

func (dao *Dao) UpdateInvoice(req models.CorporationInvoice) error {
	query := dao.Db.PSQL.Update("corporation_invoices")
	if req.Status != "" {
		query = query.Set("status", req.Status)
	}

	if req.InvoiceFile != "" {
		query = query.Set("invoice_file", req.InvoiceFile)
	}

	if req.ReportFile != "" {
		query = query.Set("report_file", req.ReportFile)
	}

	if req.OrgID > 0 {
		query = query.Where("org_id = ?", req.OrgID)
	}

	query = query.Where("id = ?", req.ID)

	_, err := query.RunWith(dao.Db.Db).Exec()
	return err
}

func (dao *Dao) DeleteInvoice(id int) error {
	query := dao.Db.PSQL.Update("corporation_invoices").Set("deleted_at", time.Now()).Where("id = ?", id)
	_, err := query.RunWith(dao.Db.Db).Exec()
	return err
}
