package db

import (
	sqllib "database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

const (
	visaBookTable = "visa_book"
)

var (
	visaBookCols = []string{
		"id",
		"user_id",
		"traveler_id",
		"country",
		"issue_date",
		"expiration_date",
		"number_of_entries",
		"visa_type",
		"remind_renew_enabled",
		"remind_renew_email",
		"created_at",
		"updated_at",
		"visa_image",
	}
)

func (dao *Dao) GetVisaBookByID(id int) (*models.VisaBook, error) {
	if id <= 0 {
		return nil, nil
	}
	adds, err := queryVisaBooks(dao.getQueryer(), dao.Db.PSQL, map[string]any{"id": id})
	if err != nil {
		return nil, err
	}
	if len(adds) < 1 {
		return nil, nil
	}
	return adds[0], nil
}

func (dao *Dao) QueryVisaBook(q map[string]any) ([]*models.VisaBook, error) {
	queryer := dao.getQueryer()
	return queryVisaBooks(queryer, dao.Db.PSQL, q)
}

func queryVisaBooks(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string]any) ([]*models.VisaBook, error) {
	sql := sb.Select(visaBookCols...).From(visaBookTable)
	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}
	sql = sql.OrderBy("updated_at DESC")
	sqlstr, args, err := sql.ToSql()
	if err != nil && err != sqllib.ErrNoRows {
		return nil, err
	}
	fmt.Printf(sqlstr)
	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var res []*models.VisaBook
	for rows.Next() {
		var item models.VisaBook
		if err = rows.StructScan(&item); err != nil {
			return nil, err
		}
		res = append(res, &item)
	}
	return res, nil
}

func (dao *Dao) InsertVisaBook(item *models.VisaBook) (int, error) {
	if item == nil {
		return 0, nil
	}
	if item.UserID == "" {
		return 0, fmt.Errorf("invalid user ID")
	}
	now := time.Now()
	m := map[string]any{
		"user_id":              item.UserID,
		"traveler_id":          item.TravelerID,
		"country":              item.Country,
		"issue_date":           item.IssueDate,
		"expiration_date":      item.ExpirationDate,
		"number_of_entries":    item.NumberOfEntries,
		"visa_type":            item.VisaType,
		"remind_renew_enabled": item.RemindRenewEnabled,
		"remind_renew_email":   item.RemindRenewEmail,
		"created_at":           now,
		"updated_at":           now,
		"visa_image":           item.VisaImage,
	}
	return insertVisaBook(dao.getQueryer(), dao.Db.PSQL, m)
}

func insertVisaBook(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, newVisaBook map[string]any) (int, error) {
	sql := sb.Insert(visaBookTable).SetMap(newVisaBook).Suffix("RETURNING id")
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return 0, err
	}
	row := queryer.QueryRowx(sqlstr, args...)
	if row.Err() != nil {
		return 0, row.Err()
	}
	var id int
	if err := row.Scan(&id); err != nil {
		return 0, err
	}
	return id, nil
}

func (dao *Dao) UpdateVisaBook(id int, update *models.VisaBook) error {
	if update == nil || id <= 0 {
		return nil
	}
	return updateVisaBook(dao.getExecer(), dao.Db.PSQL, id, update)
}

func updateVisaBook(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id int, update *models.VisaBook) error {
	now := time.Now()
	sql := sb.Update(visaBookTable).SetMap(map[string]any{
		"country":              update.Country,
		"issue_date":           update.IssueDate,
		"expiration_date":      update.ExpirationDate,
		"number_of_entries":    update.NumberOfEntries,
		"visa_type":            update.VisaType,
		"remind_renew_enabled": update.RemindRenewEnabled,
		"last_remind":          update.LastRemind,
		"remind_renew_email":   update.RemindRenewEmail,
		"updated_at":           &now,
		"visa_image":           update.VisaImage,
	}).Where(squirrel.Eq{"id": id})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *Dao) DeleteVisaBook(id int) error {
	if id <= 0 {
		return nil
	}
	return deleteVisaBook(dao.getExecer(), dao.Db.PSQL, id)
}

func deleteVisaBook(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id int) error {
	sqlstr, args, err := sb.Delete(visaBookTable).Where(squirrel.Eq{"id": id}).ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}
