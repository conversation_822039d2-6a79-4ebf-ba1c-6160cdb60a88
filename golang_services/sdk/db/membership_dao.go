package db

import (
	sqllib "database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

const (
	membershipTable         = "membership"
	membershipProviderTable = "membership_provider"
)

var (
	membershipCols = []string{
		"id",
		"user_id",
		"traveler_id",
		"membership_number",
		"name",
		"type",
		"expiration_date",
		"status",
		"note",
		"images",
		"logo",
		"description",
		"created_at",
		"updated_at",
	}
	membershipProviderCols = []string{
		"type",
		"name",
		"logo",
		"source",
		"description",
	}
)

func (dao *Dao) GetMembershipByID(id int) (*models.Membership, error) {
	if id <= 0 {
		return nil, nil
	}
	adds, err := queryMemberships(dao.getQueryer(), dao.Db.PSQL, map[string]any{"id": id})
	if err != nil {
		return nil, err
	}
	if len(adds) < 1 {
		return nil, nil
	}
	return adds[0], nil
}

func (dao *Dao) QueryMembership(q map[string]any) ([]*models.Membership, error) {
	queryer := dao.getQueryer()
	return queryMemberships(queryer, dao.Db.PSQL, q)
}

func queryMemberships(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string]any) ([]*models.Membership, error) {
	sql := sb.Select(membershipCols...).From(membershipTable)
	for k, v := range q {
		switch k {
		case "traveler_id", "user_id", "membership_number":
			sql = sql.Where(squirrel.Like{k: v})
		default:
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}
	sql = sql.OrderBy("name ASC")
	sqlstr, args, err := sql.ToSql()
	if err != nil && err != sqllib.ErrNoRows {
		return nil, err
	}
	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var res []*models.Membership
	for rows.Next() {
		var item models.Membership
		if err = rows.StructScan(&item); err != nil {
			return nil, err
		}
		res = append(res, &item)
	}
	return res, nil
}

func (dao *Dao) InsertMembership(item *models.Membership) (int, error) {
	if item == nil {
		return 0, nil
	}
	if item.UserID == "" {
		return 0, fmt.Errorf("invalid user ID")
	}
	now := time.Now()
	m := map[string]any{
		"user_id":           item.UserID,
		"traveler_id":       item.TravelerID,
		"membership_number": item.MembershipNumber,
		"name":              item.Name,
		"type":              item.Type,
		"status":            "active",
		"note":              item.Note,
		"logo":              item.Logo,
		"description":       item.Description,
		"created_at":        now,
		"updated_at":        now,
	}
	if item.Images != nil {
		m["images"] = item.Images
	}
	if item.ExpirationDate != nil {
		m["expiration_date"] = item.ExpirationDate
	}
	return insertMembership(dao.getQueryer(), dao.Db.PSQL, m)
}

func insertMembership(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, newMembership map[string]any) (int, error) {
	sql := sb.Insert(membershipTable).SetMap(newMembership).Suffix("RETURNING id")
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return 0, err
	}
	row := queryer.QueryRowx(sqlstr, args...)
	if row.Err() != nil {
		return 0, row.Err()
	}
	var id int
	if err := row.Scan(&id); err != nil {
		return 0, err
	}
	return id, nil
}

func (dao *Dao) UpdateMembership(id int, update *models.Membership) error {
	if update == nil || id <= 0 {
		return nil
	}
	return updateMembership(dao.getExecer(), dao.Db.PSQL, id, update)
}

func updateMembership(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id int, update *models.Membership) error {
	now := time.Now()
	sql := sb.Update(membershipTable).SetMap(map[string]any{
		"membership_number": update.MembershipNumber,
		"name":              update.Name,
		"type":              update.Type,
		"expiration_date":   update.ExpirationDate,
		"note":              update.Note,
		"images":            update.Images,
		"logo":              update.Logo,
		"description":       update.Description,
		"updated_at":        &now,
	}).Where(squirrel.Eq{"id": id})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *Dao) DeactivateMembership(id int) error {
	if id <= 0 {
		return nil
	}
	return deactivateMembership(dao.getExecer(), dao.Db.PSQL, id)
}

func deactivateMembership(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id int) error {
	sqlstr, args, err := sb.Update(membershipTable).Set("status", "inactive").Where(squirrel.Eq{"id": id}).ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *Dao) QueryMembershipProvider(q map[string]any) ([]*models.MembershipProvider, error) {
	queryer := dao.getQueryer()
	sb := dao.Db.PSQL
	sql := sb.Select(membershipProviderCols...).From(membershipProviderTable)
	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}
	sql = sql.OrderBy("name ASC")
	sqlstr, args, err := sql.ToSql()
	if err != nil && err != sqllib.ErrNoRows {
		return nil, err
	}
	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var res []*models.MembershipProvider
	for rows.Next() {
		var item models.MembershipProvider
		if err = rows.StructScan(&item); err != nil {
			return nil, err
		}
		res = append(res, &item)
	}
	return res, nil
}
