package db

import (
	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
)

const (
	corporationTable = "Corporation"
)

var (
	corporationColumns = []string{
		"id",
		"status",
		"name",
		"code",
		"domain",
		"country",
		"address",
		"contact",
		"secondary_contact",
		"timezone_name",
		"org_id",
		"created_at",
		"updated_at",
	}
)

func (dao *Dao) GetCorporationByID(id string) (*models.Corporation, error) {
	if id == "" {
		return nil, nil
	}
	q := map[string][]any{"id": {id}}

	res, err := queryCorporations(dao.getQueryer(), dao.Db.PSQL, q)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

func (dao *Dao) GetCorporationByOrgID(id int64) (*models.Corporation, error) {
	if id <= 0 {
		return nil, nil
	}
	q := map[string][]any{"org_id": {id}}

	res, err := queryCorporations(dao.getQueryer(), dao.Db.PSQL, q)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

func (dao *Dao) GetCorporations(q map[string][]any) ([]*models.Corporation, error) {
	return queryCorporations(dao.getQueryer(), dao.Db.PSQL, q)
}

func queryCorporations(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any) ([]*models.Corporation, error) {
	sql := sb.Select(corporationColumns...).From(corporationTable)

	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}
	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := queryer.Queryx(sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.Corporation
	for rows.Next() {
		var p models.Corporation
		if err = rows.StructScan(&p); err != nil {
			return nil, err
		}
		res = append(res, &p)
	}
	return res, nil
}
