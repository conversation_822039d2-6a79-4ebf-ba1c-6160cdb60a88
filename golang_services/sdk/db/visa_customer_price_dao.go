package db

import (
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

const (
	visaCustomerPriceTable = "visa_customer_price"
)

var (
	visaCustomerPriceColmns = []string{
		"id",
		"status",
		"visa_product_id",
		"organization_id",
		"consulate_id",
		"currency",
		"price",
		"additional_fee",
		"discount",
		"shipments",
		"created_at",
		"updated_at",
	}
)

func (dao *Dao) QueryVisaCustomerPrice(q map[string][]any) ([]*models.VisaCustomerPrice, error) {
	return queryVisaCustomerPrice(dao.getQueryer(), dao.Db.PSQL, q)
}

func queryVisaCustomerPrice(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any) ([]*models.VisaCustomerPrice, error) {
	sql := sb.Select(visaCustomerPriceColmns...).From(visaCustomerPriceTable)
	for k, v := range q {
		if isValidVCPColumn(k) {
			if k == "organization_id" {
				if len(v) > 0 {
					sql = sql.Where(squirrel.Or{squirrel.Eq{"organization_id": v}, squirrel.Eq{"organization_id": nil}})
				} else {
					sql = sql.Where(squirrel.Eq{"organization_id": nil})
				}
				continue
			}
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.VisaCustomerPrice
	for rows.Next() {
		var vcp models.VisaCustomerPrice
		if err = rows.StructScan(&vcp); err != nil {
			return nil, err
		}
		res = append(res, &vcp)
	}
	return res, nil
}

func isValidVCPColumn(col string) bool {
	switch col {
	case "id", "status", "visa_product_id", "organization_id", "consulate_id", "currency", "price", "additional_fee",
		"discount", "shipments", "created_at", "updated_at":
		return true
	default:
		return false
	}
}

func (dao *Dao) QueryVisaCustomerPriceByCountry(country string, q map[string][]any) ([]*models.VisaCustomerPrice, error) {
	if country == "" {
		return nil, nil
	}

	sql := dao.Db.PSQL.Select(
		"visa_customer_price.id",
		"visa_customer_price.status",
		"visa_customer_price.visa_product_id",
		"visa_customer_price.organization_id",
		"visa_customer_price.consulate_id",
		"visa_customer_price.currency",
		"visa_customer_price.price",
		"visa_customer_price.additional_fee",
		"visa_customer_price.discount",
		"visa_customer_price.shipments",
		"visa_customer_price.created_at",
		"visa_customer_price.updated_at",
	).From(visaCustomerPriceTable).
		Join("consulate on visa_customer_price.consulate_id=consulate.id").
		Where(squirrel.Eq{"consulate.country": country})
	for k, v := range q {
		if isValidVCPColumn(k) {
			if k == "organization_id" && len(v) == 0 {
				sql = sql.Where(squirrel.Eq{"visa_customer_price." + "organization_id": nil})
				continue
			}
			sql = sql.Where(squirrel.Eq{"visa_customer_price." + k: v})
		}
	}

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.VisaCustomerPrice
	if rows.Next() {
		var vcp models.VisaCustomerPrice
		if err = rows.StructScan(&vcp); err != nil {
			return nil, err
		}
		res = append(res, &vcp)
	}
	return res, nil
}
