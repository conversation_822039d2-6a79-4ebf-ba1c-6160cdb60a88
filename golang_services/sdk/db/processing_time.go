package db

import (
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/Masterminds/squirrel"
	"github.com/spf13/cast"
	"gorm.io/datatypes"
)

func (dao *Dao) CalculateServiceProcessingTime(etsID int) (*time.Time, *models.WorkingTimeConfig, error) {
	// Query to get attributes and working times
	query := squirrel.Select("attributes, working_times").
		From("ets").
		Where(squirrel.Eq{"id": etsID}).
		Limit(1).
		PlaceholderFormat(squirrel.Dollar)

	var attributes datatypes.JSONMap
	var workingTime string
	sqlStr, sqlArgs, _ := query.ToSql()
	err := dao.Db.Db.QueryRow(sqlStr, sqlArgs...).Scan(&attributes, &workingTime)
	if err != nil {
		return nil, nil, err
	}

	// Query to get special working times
	query2 := squirrel.Select("jsonb_build_object('name', name, 'location', location, 'working_hours', working_hours, 'special_off_days', special_off_days, 'buffer', buffer, 'query_time_setting', query_time_setting) AS result").
		From("special_working_times").
		Where(squirrel.Eq{"name": workingTime}).
		Limit(1).
		PlaceholderFormat(squirrel.Dollar)

	var specialWorkingTimeConfig string
	sqlStr2, sqlArgs2, _ := query2.ToSql()
	if err := dao.Db.Db.QueryRow(sqlStr2, sqlArgs2...).Scan(&specialWorkingTimeConfig); err != nil {
		return nil, nil, err
	}

	// Calculate the end date
	endDate, config, err := calculateSpecialWorkingTime(specialWorkingTimeConfig, time.Now(), cast.ToString(attributes["processing_time"]))
	return endDate, config, err
}

func (dao *Dao) GetProcessingTimeWithInput(etsID int, input time.Time, processingTime string) (*time.Time, error) {
	_, config, err := dao.CalculateServiceProcessingTime(etsID)
	if err != nil {
		return nil, err
	}
	// Calculate the end date
	endDate, _, err := calculateSpecialWorkingTime(utils.StructToJSON(config).Raw, input, processingTime)
	return endDate, err
}

func calculateSpecialWorkingTime(configJ string, now time.Time, durationStr string) (*time.Time, *models.WorkingTimeConfig, error) {
	config := models.WorkingTimeConfig{}
	if err := json.Unmarshal([]byte(configJ), &config); err != nil {
		fmt.Println(err)
		return nil, nil, err
	}

	duration, err := convertToDuration(config, durationStr)
	if err != nil {
		fmt.Println(err)
		return nil, nil, err
	}

	toDateTime := calculateEndTime(config, now, duration)
	return &toDateTime, &config, nil
}

func convertToDuration(config models.WorkingTimeConfig, input string) (time.Duration, error) {
	if len(input) < 2 {
		return 0, errors.New("invalid duration string")
	}

	numPart := regexp.MustCompile(`^(\d+)`).FindString(input)
	unitPart := input[len(input)-1:]

	value, err := strconv.Atoi(numPart)
	if err != nil {
		return 0, fmt.Errorf("invalid number in duration string: %v", err)
	}

	medianWorkingHours := calculateMedianWorkingHours(config.WorkingHours)
	weeklyWorkingHours := totalWeeklyHours(config.WorkingHours)

	switch unitPart {
	case "h":
		return time.Duration(value) * time.Hour, nil
	case "d":
		return time.Duration(float64(value)*medianWorkingHours) * time.Hour, nil
	case "w":
		return time.Duration(value) * time.Duration(weeklyWorkingHours) * time.Hour, nil
	case "m", "M":
		return time.Duration(value) * 4 * time.Duration(weeklyWorkingHours) * time.Hour, nil
	default:
		return 0, fmt.Errorf("invalid unit in duration string: %s", unitPart)
	}
}
func calculateMedianWorkingHours(workingHours map[string][]string) float64 {
	var dailyHours []int

	for _, hours := range workingHours {
		dailyHours = append(dailyHours, totalDailyHours(hours))
	}

	if len(dailyHours) == 0 {
		return 0
	}

	sort.Ints(dailyHours)

	medianIndex := len(dailyHours) / 2
	if len(dailyHours)%2 == 0 {
		return float64(dailyHours[medianIndex-1]+dailyHours[medianIndex]) / 2.0
	}
	return float64(dailyHours[medianIndex])
}

func totalDailyHours(times []string) int {
	totalHours := 0.0
	currentTime := time.Now()
	for i := 0; i < len(times); i += 2 {
		startHour := parseTime(currentTime, times[i])
		endHour := parseTime(currentTime, times[i+1])
		totalHours += endHour.Sub(startHour).Hours()
	}
	return int(totalHours)
}
func totalWeeklyHours(workingHours map[string][]string) int {
	totalHours := 0
	for _, times := range workingHours {
		totalHours += totalDailyHours(times)
	}
	return int(totalHours)
}

func calculateEndTime(config models.WorkingTimeConfig, now time.Time, duration time.Duration) time.Time {
	buffer, err := convertToDuration(config, config.Buffer)
	if err != nil {
		buffer = 0
	}
	duration += buffer

	location, err := time.LoadLocation(config.Location)
	if err != nil {
		panic(err)
	}

	workingHours := parseWorkingHours(config.WorkingHours)
	specialOffDays := parseSpecialOffDays(config.SpecialOffDays)

	remainingDuration := duration
	currentTime := now.In(location)
	fmt.Println(currentTime)
	for remainingDuration > 0 {
		dayOfWeek := currentTime.Weekday().String()[:3] // "Mon", "Tue", etc.
		dayWorkingHours, found := workingHours[strings.ToLower(dayOfWeek)]

		if found && len(dayWorkingHours) > 0 && !isSpecialOffDay(currentTime, specialOffDays) {
			for i := 0; i < len(dayWorkingHours); i += 2 {
				startHour := parseTime(currentTime, dayWorkingHours[i])
				endHour := parseTime(currentTime, dayWorkingHours[i+1])

				if currentTime.Before(startHour) {
					currentTime = startHour
				}

				if currentTime.After(endHour) {
					continue
				}

				workingPeriod := endHour.Sub(currentTime)
				if workingPeriod > remainingDuration {
					workingPeriod = remainingDuration
				}

				currentTime = currentTime.Add(workingPeriod)
				remainingDuration -= workingPeriod

				if remainingDuration == 0 {
					return currentTime
				}
			}
		}

		// Move to the next day at 00:00
		currentTime = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()+1, 0, 0, 0, 0, location)
	}

	return currentTime
}

func parseWorkingHours(hoursMap map[string][]string) map[string][]string {
	workingHours := make(map[string][]string)
	for day, hours := range hoursMap {
		workingHours[strings.ToLower(day[:3])] = hours // "Mon", "Tue", etc.
	}
	return workingHours
}

func parseSpecialOffDays(offDaysMap map[string][]string) map[string]struct{} {
	specialOffDays := make(map[string]struct{})
	for _, dates := range offDaysMap {
		for _, date := range dates {
			specialOffDays[date] = struct{}{}
		}
	}
	return specialOffDays
}

func isSpecialOffDay(currentTime time.Time, specialOffDays map[string]struct{}) bool {
	dateStr := currentTime.Format("2006-01-02")
	_, exists := specialOffDays[dateStr]
	return exists
}

func parseTime(baseTime time.Time, timeStr string) time.Time {
	parsedTime, _ := time.ParseInLocation("15:04", timeStr, baseTime.Location())
	return time.Date(baseTime.Year(), baseTime.Month(), baseTime.Day(), parsedTime.Hour(), parsedTime.Minute(), 0, 0, baseTime.Location())
}
