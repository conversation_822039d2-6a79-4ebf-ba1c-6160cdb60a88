package db

//
//import (
//	"github.com/Masterminds/squirrel"
//
//	"bitbucket.org/persistence17/aria/golang_services/models"
//)
//
//const (
//	addressTable = "address"
//)
//
//var (
//	addressColmns = []string{
//		"id",
//		"address",
//		"address_in_native",
//		"city",
//		"zip_code",
//		"state",
//		"country",
//		"is_resident",
//		"is_po_box",
//		"company_name",
//	}
//)
//
//func (dao *Dao) InsertAddress(add *models.Address) (int, error) {
//	if add == nil {
//		return 0, nil
//	}
//	sql := dao.Db.PSQL.Insert(addressTable).
//		Columns("address", "address_in_native", "city", "zip_code", "state", "country", "is_resident",
//			"is_po_box", "company_name").
//		Values(add.Address, add.AddressInNative, add.City, add.ZipCode, add.State, add.Country, add.IsResident,
//			add.IsPoBox, add.CompanyName.String).Suffix("RETURNING id")
//	sqlstr, args, err := sql.ToSql()
//	if err != nil {
//		return 0, err
//	}
//	row := dao.getQueryer().QueryRowx(sqlstr, args...)
//	if row.Err() != nil {
//		return 0, row.Err()
//	}
//	var id int
//	if err = row.Scan(&id); err != nil {
//		return 0, err
//	}
//	return id, nil
//}
//
//func (dao *Dao) GetAddressByID(id int) (*models.Address, error) {
//	if id <= 0 {
//		return nil, nil
//	}
//	sql := dao.Db.PSQL.Select(addressColmns...).From(addressTable).Where(squirrel.Eq{"id": id})
//	sqlstr, args, err := sql.ToSql()
//	if err != nil {
//		return nil, err
//	}
//	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
//	if err != nil {
//		return nil, err
//	}
//	defer rows.Close()
//	var res models.Address
//	if rows.Next() {
//		if err = rows.StructScan(&res); err != nil {
//			return nil, err
//		}
//	}
//	return &res, nil
//}
//
//func (dao *Dao) UpdateAddress(id int, update *models.Address) error {
//	if update == nil {
//		return nil
//	}
//	sql := dao.Db.PSQL.Update(addressTable).SetMap(map[string]any{
//		"address":           update.Address,
//		"address_in_native": update.AddressInNative,
//		"city":              update.City,
//		"zip_code":          update.ZipCode,
//		"state":             update.State,
//		"country":           update.Country,
//		"is_resident":       update.IsResident,
//		"is_po_box":         update.IsPoBox,
//		"company_name":      update.CompanyName,
//	}).Where(squirrel.Eq{"id": id})
//	sqlstr, args, err := sql.ToSql()
//	if err != nil {
//		return err
//	}
//	if _, err = dao.getExecer().Exec(sqlstr, args...); err != nil {
//		return err
//	}
//	return nil
//}
