package db

import (
	"encoding/json"
)

func (dao *Dao) GetMasterDataValueByName(name string) (json.RawMessage, error) {
	rows, err := dao.getQueryer().Query("SELECT value FROM master_data WHERE name = $1", name)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var value json.RawMessage
	if rows.Next() {
		if err = rows.Scan(&value); err != nil {
			return nil, err
		}
		return value, nil
	}
	return nil, nil
}
