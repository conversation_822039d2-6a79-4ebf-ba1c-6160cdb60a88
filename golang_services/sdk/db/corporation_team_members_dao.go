package db

import (
	"errors"

	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
)

const (
	corpTeamMembersTable = "corporation_team_members"
)

var (
	corpTeamMembersColumns = []string{
		"id",
		"team_id",
		"user_id",
		"role",
		"require_approval",
		"approvers",
		"status",
		"created_at",
		"updated_at",
	}
)

func (dao *Dao) GetTeamMemberByUserID(userID string) (*models.CorporationTeamMember, error) {
	if userID == "" {
		return nil, nil
	}
	q := map[string][]any{"user_id": {userID}}

	res, err := queryTeamMembers(dao.getQueryer(), dao.Db.PSQL, q, 0, 0, true)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

func queryTeamMembers(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any, offset, limit uint64, desc bool) ([]*models.CorporationTeamMember, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := sb.Select(corpTeamMembersColumns...).
		From(corpTeamMembersTable)
	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}
	if offset >= 0 {
		sql = sql.Offset(offset)
	}
	if limit > 0 {
		sql = sql.Limit(limit)
	}

	if desc {
		sql = sql.OrderBy("created_at DESC")
	} else {
		sql = sql.OrderBy("created_at ASC")
	}
	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	var res []*models.CorporationTeamMember
	if err := sqlx.Select(queryer, &res, sqlStr, args...); err != nil {
		return nil, err
	}
	return res, nil
}

func queryTeamIDByUserID(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, userID string) (int, error) {
	sql := sb.Select("team_id").From(corpTeamMembersTable)
	sql = sql.Where(squirrel.Eq{"user_id": userID})

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return 0, err
	}
	res := queryer.QueryRowx(sqlstr, args...)
	if res == nil {
		return 0, errors.New("Cannot fetch team id")
	}
	var id int
	if err = res.Scan(&id); err != nil {
		return 0, err
	}
	return id, nil
}

func queryTeamMemberIDsByManager(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, managerID string) ([]string, error) {
	teamID, err := queryTeamIDByUserID(queryer, sb, managerID)
	if err != nil {
		return nil, err
	}
	sql := sb.Select("user_id").From(corpTeamMembersTable)
	sql = sql.Where(squirrel.Eq{"team_id": teamID})
	sql = sql.Where(squirrel.NotEq{"role": "manager"})
	sql = sql.Where(squirrel.Eq{"require_approval": true})

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []string
	for rows.Next() {
		var teamMemberID string
		if err = rows.Scan(&teamMemberID); err != nil {
			return nil, err
		}
		res = append(res, teamMemberID)
	}
	return res, nil
}

func (dao *Dao) GetTeamMemberIDsByManager(managerID string) ([]string, error) {
	teamMemberIDs, err := queryTeamMemberIDsByManager(dao.getQueryer(), dao.Db.PSQL, managerID)
	if err != nil {
		return nil, err
	}
	return teamMemberIDs, nil
}

func (dao *Dao) GetTeamRoleByUserID(userID string) (string, error) {
	teamRole, err := getTeamRoleByUserID(dao.getQueryer(), dao.Db.PSQL, userID)
	if err != nil {
		return "", err
	}
	return teamRole, nil
}

func getTeamRoleByUserID(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, userID string) (string, error) {
	sql := sb.Select("role").From(corpTeamMembersTable)
	sql = sql.Where(squirrel.Eq{"user_id": userID})

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return "", err
	}
	res := queryer.QueryRowx(sqlstr, args...)
	if res == nil {
		return "", errors.New("Cannot fetch team role, or not belong to any team")
	}
	var role string
	if err = res.Scan(&role); err != nil {
		return "", err
	}
	return role, nil
}
