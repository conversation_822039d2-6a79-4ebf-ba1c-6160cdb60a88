package db

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

const (
	etsTable         = "ets"
	ordersTable      = "service_orders"
	tasksTable       = "service_tasks"
	priceTable       = "ets_price"
	etsProviderTable = "ets_provider"
	etsPodTable      = "ets_pods"
)

var (
	etsCols      = []string{"id", "name", "service_type", "country", "airport", "currency", "schema", "order_schema", "schema_pods", "tag", "tasks", "working_times", "status", "attributes", "created_at", "updated_at"}
	orderCols    = []string{"id", "status", "user_id", "org_id", "service_id", "payment_id", "query_pod_values", "summary", "output_files", "config", "shipment_info", "provider_id", "auto_generated_form_at", "form_callback", "input_pods", "input_pod_values", "created_at", "updated_at", "is_send_feedback_to_user", "expect_issue_visa_at", "note", "tracking_times"}
	priceCols    = []string{"id", "status", "ets_id", "provider_id", "price", "currency", "additional_fee", "discount", "shipments", "price_modified_rules", "additional_services", "created_at", "updated_at"}
	providerCols = []string{"id", "status", "name", "country", "timezone_name", "address", "contact", "secondary_contact", "website", "served_services", "served_countries", "served_area", "org_id", "created_at", "updated_at"}
)

type EtsDao struct {
	Db *AuroraDB
	tx *sqlx.Tx
}

func NewEtsDao(db *AuroraDB) *EtsDao {
	return &EtsDao{Db: db}
}

type availableSvcProvider struct {
	ServiceID int `db:"service_id"`
	*models.ServiceProvider
}

func (dao *EtsDao) getQueryer() sqlx.Queryer {
	if dao.tx != nil {
		return dao.tx
	}
	return dao.Db.Db
}

func (dao *EtsDao) getExecer() sqlx.Execer {
	if dao.tx != nil {
		return dao.tx
	}
	return dao.Db.Db
}

func (dao *EtsDao) VisaDB() *Dao {
	return &Dao{Db: dao.Db, Tx: dao.tx}
}

func (dao *EtsDao) Begin() error {
	if dao.tx != nil {
		return fmt.Errorf("already in a transaction")
	}
	t, err := dao.Db.Db.Beginx()
	if err != nil {
		return err
	}
	dao.tx = t
	return nil
}

func (dao *EtsDao) Commit() error {
	if dao.tx == nil {
		return nil
	}
	if err := dao.tx.Commit(); err != nil {
		return err
	}
	dao.tx = nil
	return nil
}

func (dao *EtsDao) Rollback() error {
	if dao.tx == nil {
		return nil
	}
	if err := dao.tx.Rollback(); err != nil {
		return err
	}
	dao.tx = nil
	return nil
}

func (dao *EtsDao) QueryExtendedTravelServices(q map[string]any, offset, limit uint) ([]*models.ExtendedTravelService, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := dao.Db.PSQL.Select(etsCols...).From(etsTable).OrderBy("id")
	for k, v := range q {
		switch k {
		case "id", "service_type", "currency", "status":
			sql = sql.Where(squirrel.Eq{k: v})
		case "number_of_entries":
			sql = sql.Where(squirrel.Eq{fmt.Sprintf("attributes->>'%s'", k): v})
		case "country":
			sql = sql.Where(squirrel.Like{k: fmt.Sprintf("%%%s%%", v)})
		case "tag":
			switch vv := v.(type) {
			case string:
				sql = sql.Where(squirrel.Eq{k: vv})
			case []string:
				for _, s := range vv {
					sql = sql.Where(squirrel.Eq{k: s})
				}
			}
		case "airport":
			switch vv := v.(type) {
			case string:
				sql = sql.Where(squirrel.Like{k: fmt.Sprintf("%%%s%%", vv)})
			case []string:
				for _, s := range vv {
					sql = sql.Where(squirrel.Like{k: fmt.Sprintf("%%%s%%", s)})
				}
			}
		case "tasks":
			switch vv := v.(type) {
			case string:
				sql = sql.Where(squirrel.Like{k: fmt.Sprintf("%%%s%%", vv)})
			case []string:
				for _, s := range vv {
					sql = sql.Where(squirrel.Like{k: fmt.Sprintf("%s", s)})
				}
			}
		case "region_of_residence", "terminal", "nationality":
			k = fmt.Sprintf("%s->>'%s'", "attributes", k)
			sql = sql.Where(squirrel.Like{k: fmt.Sprintf("%%%s%%", v)})
		case "user_age", "condition":
			k = fmt.Sprintf("%s->>'%s'", "attributes", k)
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}
	if offset > 0 {
		sql = sql.Offset(uint64(offset))
	}
	if limit > 0 {
		sql = sql.Limit(uint64(limit))
	}
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.ExtendedTravelService
	for rows.Next() {
		var s models.ExtendedTravelService
		if err := rows.StructScan(&s); err != nil {
			return nil, err
		}
		fmt.Println(s.ID)
		res = append(res, &s)
	}
	return res, nil
}

func (dao *EtsDao) QueryExtendedTravelServicesByOrderIDs(soIDs []int) (map[int]*models.ExtendedTravelService, error) {
	if len(soIDs) == 0 {
		return nil, nil
	}
	sql := dao.Db.PSQL.Select(`so.id, e.id, e.service_type, e.country, e.airport, e.tasks, e."attributes", e.currency,
			e."schema", e.schema_pods, e.tag, e.status, e.created_at, e.updated_at`).
		From("ets e").
		LeftJoin("service_orders so ON so.service_id = e.id").
		Where(squirrel.Eq{"so.id": soIDs})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	res := map[int]*models.ExtendedTravelService{}
	for rows.Next() {
		var orderID int
		item := models.ExtendedTravelService{}
		if err := rows.Scan(&orderID, &item.ID, &item.ServiceType, &item.Country, &item.Airport, &item.Tasks, &item.Attributes, &item.Currency,
			&item.Schema, &item.SchemaPods, &item.Tag, &item.Status, &item.CreatedAt, &item.UpdatedAt); err != nil {
			return nil, err
		}
		res[orderID] = &item
	}
	return res, nil
}

func (dao *EtsDao) GetServiceProviders(svcIDs []int) (map[int][]*models.ServiceProvider, error) {
	if len(svcIDs) == 0 {
		return map[int][]*models.ServiceProvider{}, nil
	}
	sql := dao.Db.PSQL.Select("p.id", "p.org_id", "p.contact_info", "p.meta_info", "p.created_at", "p.updated_at", "sp.service_id").
		From("service_providers p").Join("available_service_provider sp ON p.id=sp.provider_id").
		Where(squirrel.Eq{"sp.service_id": svcIDs})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	res := map[int][]*models.ServiceProvider{}
	for rows.Next() {
		var item availableSvcProvider
		if err := rows.StructScan(&item); err != nil {
			return nil, err
		}
		res[item.ServiceID] = append(res[item.ServiceID], item.ServiceProvider)
	}
	return res, nil
}

func (dao *EtsDao) QueryServiceOrders(req models.ServiceOrderFilter) (*models.ServiceOrderResponse, error) {
	sql := dao.Db.PSQL.Select(`so.id,so.status,so.user_id,so.org_id,so.shipment_info, so.shipment_order,so.service_id,so.payment_id,so.provider_id,so.query_pod_values,so.summary,so.input_pods,so.input_pod_values,so.expect_issue_visa_at,
	so.config,so.order_time,so.submitted_time,so.completed_time,so.note,so.created_at,so.updated_at,so.deleted_at,to_jsonb(e) service, so.output_files, so.processing_time_expired_at, so.tracking_times, so.form_callback,
	(select (md.value->vea.action)::jsonb || jsonb_build_object('name', vea.action, 'apps', vea.apps) from master_data md where name = 'order_actions') action_data,
	(SELECT to_jsonb(uf) FROM user_feedbacks uf WHERE uf.service_type != 'visa' AND uf.order_id = so.id ORDER BY created_at DESC fetch first 1 rows only) user_feedback`).
		From("service_orders so").
		LeftJoin("ets e ON e.id = so.service_id").
		LeftJoin("service_tasks t ON t.order_id = so.id").
		LeftJoin("ets_provider ep ON ep.id = so.provider_id").
		LeftJoin("v_ets_actions vea ON vea.order_id = so.id").
		LeftJoin("v_fastlane_orders vfo ON vfo.id = so.id").
		Where("so.deleted_at IS NULL").
		GroupBy("so.id, e.id, vea.action, vea.apps")

	sql = sql.Column(`(SELECT jsonb_agg(jsonb_build_object(
		'staff_id', u.id, 
		'staff_name', CONCAT_WS('_', u.given_name, u.surname), 
		'staff_confirm_status', sos.staff_confirm_status,
		'staff_confirm_code', sos.staff_confirm_code,
		'staff_confirm_at', sos.staff_confirm_at,
		'note', sos.note,
		'created_at', sos.created_at,
		'staff_contact', u.staff_contact
	)) FROM service_order_staffs sos LEFT JOIN users u ON u.id = sos.staff_id
	WHERE sos.order_id = so.id) order_staffs`)

	if req.IncludeTasks {
		sql = sql.Column("COALESCE(jsonb_agg(to_jsonb(t)) FILTER (WHERE t.id IS NOT NULL), '[]') AS tasks")
	}

	if req.IncludePrice {
		sql = sql.Column("jsonb_agg(to_jsonb(eps) ORDER BY eps.price) prices").
			LeftJoin("ets_price eps ON eps.ets_id = e.id")
	}

	if req.IncludePayment {
		sql = sql.Column("to_jsonb(p) payment, to_jsonb(c) cart").
			LeftJoin("payment p ON p.id = so.payment_id").
			LeftJoin("cart c on c.payment_id = p.id").
			GroupBy("p.id,c.id")
	}

	if req.IncludeShipment {
		sql = sql.Column(`to_jsonb(vs)  as shipment`).
			LeftJoin("visa_shipment vs ON vs.id = so.shipment_info").
			GroupBy("vs.*")
	}

	if req.IncludeCreator {
		sql = sql.Column(`jsonb_build_object(
			'name',CONCAT_WS(' ',u.given_name, u.surname),
			'email',u.email,
			'org_name',MAX(o.name)
			) as creator`).
			LeftJoin("users u ON u.id = so.user_id").
			LeftJoin("organization o on o.id = u.organization_id").
			GroupBy("u.given_name,u.surname,u.email")
	}

	if true {
		sql = sql.Column(`(SELECT 
			jsonb_build_object(
				'cart',to_jsonb(c),
				'cart_item',to_jsonb(ci)
			) current_cart
			FROM cart_item ci 
			LEFT JOIN cart c ON c.id = ci.cart_id
			WHERE ci.product_id = so.id::text AND so.deleted_at IS NULL AND so.status IN ('open','pending-payment') AND ci.id IS NOT NULL AND ci.product_type = 'AIRPORT_SERVICES' AND is_current is true
			) as current_cart`)
	}

	if req.IncludeProvider {
		sql = sql.Column(`jsonb_build_object(
			'name',ep.name,
			'address',ep.address,
			'contact',ep.contact,
			'accounts', (select jsonb_agg(email) from users where organization_id = ep.org_id)
			) as provider`).
			GroupBy("ep.name,ep.address,ep.contact,ep.org_id")
	}

	// https://traversal17.atlassian.net/browse/AD-5434
	// search by order ID (Both)
	// search by traveler name (first, last, middle name) (Both)
	// search by task (ETS: renew, new)  (Both)
	// search by visa type (VISA: native, regular, voa..)  (Both)
	// search by organization name (AD admin)
	// search by Consulate / Provider name (AD admin)

	if len(req.Query) > 0 {
		query := "%" + req.Query + "%"
		queryConditions := []squirrel.Sqlizer{}
		queryConditions = append(queryConditions, squirrel.ILike{"so.id::text": query})
		queryConditions = append(queryConditions, squirrel.Expr("EXISTS (SELECT * FROM users u WHERE CONCAT_WS(' ', u.given_name, u.surname, u.email) ILIKE ? AND u.id = so.user_id)", query))
		queryConditions = append(queryConditions, squirrel.Expr(`t.id IN (select st.id from service_tasks st,jsonb_to_recordset(st.input_pods) as items(name text, value jsonb)
		where items.name = ANY(ARRAY['task','passenger_name_list','given_name','surname','middle_name']) and items.value->>'fe' ILIKE ?)`, query))

		// Admin only can query by org name
		if req.ADAdmin {
			queryConditions = append(queryConditions, squirrel.Expr(`so.org_id IN (select id from v_organizations where name ILIKE ?)`, query))
		}

		sql = sql.Where(squirrel.Or(queryConditions))
	}

	if req.IDs != nil {
		sql = sql.Where(squirrel.Eq{"so.id": req.IDs})
	}

	if len(req.ID) > 0 {
		sql = sql.Where(squirrel.Eq{"so.id": req.ID})
	}

	if len(req.Status) > 0 {
		sql = sql.Where(squirrel.Eq{"so.status": req.Status})
	}

	if len(req.UserID) > 0 {
		sql = sql.Where(squirrel.Eq{"so.user_id": req.UserID})
	}

	if req.OrgID > 0 {
		sql = sql.Where(squirrel.Eq{"so.org_id": req.OrgID})
	}

	if req.ProviderOrgID > 0 {
		sql = sql.Where("ep.org_id = ?", req.ProviderOrgID)
	}

	if len(req.ServiceID) > 0 {
		sql = sql.Where(squirrel.Eq{"so.service_id": req.ServiceID})
	}

	if len(req.ServiceType) > 0 {
		sql = sql.Where(squirrel.Eq{"e.service_type": req.ServiceType})
	}

	if len(req.PaymentID) > 0 {
		sql = sql.Where(squirrel.Eq{"so.payment_id": req.PaymentID})
	}

	if val, err := time.Parse(time.RFC3339, req.CreatedAtGte); err == nil {
		sql = sql.Where(squirrel.GtOrEq{"so.created_at": val})
	}

	if val, err := time.Parse(time.RFC3339, req.CreatedAtLte); err == nil {
		sql = sql.Where(squirrel.LtOrEq{"so.created_at": val})
	}

	var sortFields []string
	var sortOrder string

	switch req.SortField {
	case "id", "status", "submitted_time", "processing_time_expired_at":
		sortFields = append(sortFields, "so."+req.SortField)
	case "updated_at":
		sortFields = append(sortFields, "COALESCE(so.updated_at,so.created_at)")
	case "country", "service_type", "tasks":
		sortFields = append(sortFields, "e."+req.SortField)
	case "quantity":
		sortFields = append(sortFields, "so.summary->>'quantity'")
	case "ets_tasks":
		sortFields = append(sortFields, "e.tasks")
	case "org_name":
		sortFields = append(sortFields, "MAX(vo.org_name)")
	case "processing_time":
		sortFields = append(sortFields, `ARRAY_POSITION(array['h','d','w'], SUBSTRING (e.attributes->>'processing_time' , '\d+(h|d|w)'))`)
		sortFields = append(sortFields, `SUBSTRING (e.attributes->>'processing_time','(\d+)')::int`)
	case "approved_time":
		sortFields = append(sortFields, "so.tracking_times->>'in_delivery'")
	case "completed_time":
		sortFields = append(sortFields, "so.tracking_times->>'completed'")
	case "service_date":
		sortFields = append(sortFields, "vfo.service_date")
		sql = sql.GroupBy("vfo.service_date")
	default:
		sortFields = append(sortFields, "so.id")
	}

	if strings.ToUpper(req.SortOrder) == "ASC" {
		sortOrder = "ASC"
	} else {
		sortOrder = "DESC"
	}

	for _, sortField := range sortFields {
		sql = sql.OrderBy(sortField + " " + sortOrder)
	}

	sql = sql.Limit(req.Limit).Offset(req.Offset)

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// Get data
	var res models.ServiceOrderResponse
	for rows.Next() {
		var o models.ServiceOrderDetail
		if err := rows.StructScan(&o); err != nil {
			return nil, err
		}

		if o.TasksRaw != nil {
			if err = json.Unmarshal(o.TasksRaw, &o.Tasks); err != nil {
				return nil, err
			}
		}

		if o.PricesRaw != nil {
			var prices []*models.EtsPrice
			if err = json.Unmarshal(o.PricesRaw, &prices); err != nil {
				return nil, err
			}

			o.SmallestPrice = prices[0].GetCalculatePrice()
		}

		//get service_date from input_pod (only fastlane)
		if o.Service.ServiceType == "fastlane" {
			for _, application := range gjson.ParseBytes(o.TasksRaw).Array() {
				appMap := map[string]gjson.Result{}
				application.Get("input_pods").ForEach(func(_, value gjson.Result) bool {
					appMap[value.Get("id").String()] = value
					return true
				})

				if val := appMap["travel_enter_flight_enter_timestamp"].Get("value.fe").String(); val != "" {
					o.Tasks[0].ServiceDate = val
				}

				if val := appMap["travel_exit_flight_exit_timestamp"].Get("value.fe").String(); val != "" {
					o.Tasks[0].ServiceDate = val
				}
			}
		}

		if !req.IncludeOutputFileBeforePaid && (o.ServiceOrder.Status == models.EtsOrderStatusOpen || o.ServiceOrder.Status == models.EtsOrderStatusPendingPayment || o.ServiceOrder.Status == models.EtsOrderStatusWaitingPayment) {
			o.ServiceOrder.OutputFiles = nil
		}

		for i := range o.Tasks {
			o.Tasks[i].InputPodValues = o.Tasks[i].InputPods.ToMapKeyValue()
			o.Tasks[i].OutPodValues = o.Tasks[i].OutputPods.ToMapKeyValue()
		}

		res.Data = append(res.Data, &o)
	}

	// Get total count
	if !req.SkipPagination {
		sqlCount := utils.QueryCountItemV2(sqlstr)
		rowCount, err := dao.getQueryer().Queryx(sqlCount, args...)
		if err != nil {
			return nil, err
		}
		defer rowCount.Close()

		if rowCount.Next() {
			if err := rowCount.Scan(&res.Total); err != nil {
				return nil, err
			}
		}
	}

	res.Success = true
	return &res, nil
}

func (dao *EtsDao) CreateServiceOrder(o *models.ServiceOrder) error {
	return dao.Db.PSQL.Insert(ordersTable).
		Columns("status", "user_id", "org_id", "service_id", "payment_id", "query_pod_values", "config", "provider_id", "summary", "input_pods", "output_pods", "input_pod_values", "output_pod_values").
		Values(o.Status, o.UserID, o.OrgID, o.ServiceID, o.PaymentID, o.QueryPodValues, o.Config, o.ProviderID, o.Summary, o.InputPods, o.OutputPods, o.InputPods.ToMapKeyValue(), o.OutputPods.ToMapKeyValue()).
		Suffix("RETURNING id").RunWith(dao.Db.Db).QueryRow().Scan(&o.ID)
}

func (dao *EtsDao) UpdateServiceOrder(update map[string]any, id int) error {
	if len(update) == 0 {
		return nil
	}
	sb := dao.Db.PSQL.Update(ordersTable).Set("updated_at", time.Now()).Where(squirrel.Eq{"id": id})
	updateJ := utils.StructToJSON(update)
	for k, v := range update {
		switch k {
		case "status":
			status := updateJ.Get("status").String()
			trackingTimeMap := map[string]any{status: time.Now()}
			if status == "completed" {
				if tasks, _, err := dao.QueryServiceTasks(map[string]any{"order_id": id}, 0, 0); err == nil {
					allApproved := true
					for _, task := range tasks {
						if task.Status != models.EtsTaskStatusApproved {
							allApproved = false
						}
					}
					if allApproved {
						trackingTimeMap["in_delivery"] = time.Now()
					}
				}
				sb = sb.Set("completed_time", time.Now())
			}
			trackingStatusJ, _ := json.Marshal(trackingTimeMap)

			sb = sb.
				Set("status", status).
				Set("tracking_times", squirrel.Expr(`tracking_times || ?`, trackingStatusJ))
		case "payment_id", "config", "submitted_time", "shipment_order", "provider_id", "auto_generated_form_at", "is_send_feedback_to_user", "shipment_info", "expect_issue_visa_at":
			sb = sb.Set(k, v)
		case "input_pods":
			sb = sb.Set("input_pods", v)
		case "input_pod_values":
			sb = sb.Set("input_pod_values", squirrel.Expr(`coalesce(input_pod_values,'{}')::jsonb || ?`, v))
		case "summary":
			sb = sb.Set("summary", squirrel.Expr(`coalesce(summary,'{}')::jsonb || ?`, v))
		case "note":
			sb = sb.Set("note", squirrel.Expr(`coalesce(note,'{}')::jsonb || ?`, v))
		case "query_pod_values":
			sb = sb.Set("query_pod_values", squirrel.Expr(`coalesce(query_pod_values,'{}')::jsonb || ?`, v))
		case "form_callback":
			sb = sb.Set("form_callback", squirrel.Expr(`coalesce(form_callback,'{}')::jsonb || ?`, v))
		}
	}

	sql, args, err := sb.ToSql()
	if err != nil {
		return err
	}
	res, err := dao.getExecer().Exec(sql, args...)
	if err != nil {
		return err
	}
	r, err := res.RowsAffected()
	if err != nil {
		return err
	}
	if r == 0 {
		return errors.NotFoundErr
	}

	return nil
}

func (dao *EtsDao) DeleteServiceOrder(id []string) error {
	if len(id) <= 0 {
		return nil
	}

	var runner squirrel.BaseRunner
	if dao.tx != nil {
		runner = dao.tx.Tx
	} else {
		runner = dao.Db.Db
	}

	if _, err := dao.Db.PSQL.Delete("cart_item").Where(squirrel.Eq{
		"product_id":   id,
		"product_type": models.ProductType.AirPortService,
	}).RunWith(runner).Exec(); err != nil {
		return err
	}

	if _, err := dao.Db.PSQL.Update("service_orders").Set("deleted_at", time.Now()).Where(squirrel.Eq{
		"id": id,
	}).RunWith(runner).Exec(); err != nil {
		return err
	}

	return nil
}

func (dao *EtsDao) QueryServiceTasks(q map[string]any, offset, limit uint) ([]*models.ServiceTask, int, error) {
	if len(q) == 0 {
		return nil, 0, nil
	}
	// sql := dao.Db.PSQL.Select("t.id, t.type, t.status, t.order_id, t.provider_id, t.input_pod_values, t.input_pods,(case when o.status NOT IN ('open','pending-payment','manual_payment') then t.output_pods else '[]'::jsonb end ) as output_pods,(case when o.status NOT IN ('open','pending-payment','manual_payment') then t.output_files else '{}'::jsonb end ) as output_files, t.note, t.created_at, t.updated_at, t.form_callback").
	// 	From("service_tasks t").
	// 	LeftJoin("service_orders o ON o.id = t.order_id")
	sql := dao.Db.PSQL.Select("t.id, t.type, t.status, t.order_id, t.provider_id, t.input_pod_values, t.input_pods,t.output_pods, t.output_pod_values, t.output_files, t.note, t.created_at, t.updated_at, t.form_callback").
		From("service_tasks t").
		LeftJoin("service_orders o ON o.id = t.order_id")
	for k, v := range q {
		switch k {
		case "id", "type", "status", "order_id", "task_time", "provider_id":
			sql = sql.Where(squirrel.Eq{"t." + k: v})
		case "user_id":
			sql = sql.Where(squirrel.Eq{"o." + k: v})
		}
	}

	if offset > 0 {
		sql = sql.Offset(uint64(offset))
	}
	if limit > 0 {
		sql = sql.Limit(uint64(limit))
	}
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, 0, err
	}
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()
	var res []*models.ServiceTask
	for rows.Next() {
		var task models.ServiceTask
		if err := rows.StructScan(&task); err != nil {
			return nil, 0, err
		}
		res = append(res, &task)
	}
	total := 0
	if limit > 0 {
		sqlCount := utils.QueryCountItems(sqlstr)
		rowCount, err := dao.getQueryer().Queryx(sqlCount, args...)
		if err != nil {
			return nil, 0, err
		}
		defer rowCount.Close()

		if rowCount.Next() {
			if err := rowCount.Scan(&total); err != nil {
				return nil, 0, err
			}
		}
	} else {
		total = len(res)
	}
	return res, total, nil
}

func (dao *EtsDao) QueryServiceProviderTasks(req models.ServiceTaskWithETSFilter) (*models.ServiceTaskWithETSData, error) {
	sql := dao.Db.PSQL.Select("st.id,st.type, st.status, st.order_id, st.provider_id, st.input_pods,so.output_files,st.created_at,st.updated_at,row_to_json(ets) ets, row_to_json(vs) shipment, so.status order_status, so.submitted_time, so.completed_time").
		From("service_tasks st").
		LeftJoin("service_orders so ON so.id = st.order_id").
		LeftJoin("ets ON ets.id = so.service_id").
		LeftJoin("visa_shipment vs ON so.shipment_info = vs.id").
		Where(squirrel.Eq{
			"so.status": []string{models.EtsOrderStatusDispatched, models.EtsOrderStatusUnderReview, models.EtsOrderStatusInDelivery, models.EtsOrderStatusSubmitted, models.EtsOrderStatusCompleted},
		})

	if req.ID != "" {
		sql = sql.Where(squirrel.Eq{"st.id": req.ID})
	}
	if len(req.Status) > 0 {
		sql = sql.Where(squirrel.Eq{"st.status": req.Status})
	}
	if req.ProviderID != "" {
		sql = sql.Where(squirrel.Eq{"so.provider_id": req.ProviderID})
	}

	if req.ServiceType != "" {
		sql = sql.Where(squirrel.Eq{"ets.service_type": req.ServiceType})
	}

	if len(req.Query) > 0 {
		query := "%" + req.Query + "%"
		sql = sql.Where(squirrel.Or([]squirrel.Sqlizer{
			squirrel.ILike{"st.order_id::text": query},
			squirrel.Expr(`st.id IN (select st.id from service_tasks st,jsonb_to_recordset(st.input_pods) as items(name text, value jsonb)
			where items.name = ANY(ARRAY['passenger_name_list','given_name','surname','enter_flight','exit_flight']) 
			and items.value->>'fe' ILIKE ?)`, query),
		}))
	}

	var sortFields []string
	var sortOrder string

	switch req.SortField {
	case "order_id", "status", "type", "updated_at":
		sortFields = append(sortFields, "st."+req.SortField)
	case "service_type", "country":
		sortFields = append(sortFields, "ets."+req.SortField)
	case "processing_time":
		sortFields = append(sortFields, `ARRAY_POSITION(array['h','d','w'], SUBSTRING (ets.attributes->>'processing_time' , '\d+(h|d|w)'))`)
		sortFields = append(sortFields, `SUBSTRING (ets.attributes->>'processing_time','(\d+)')::int`)
	default:
		sortFields = append(sortFields, "st.id")
	}

	if strings.ToUpper(req.SortOrder) == "ASC" {
		sortOrder = "ASC"
	} else {
		sortOrder = "DESC"
	}

	for _, sortField := range sortFields {
		sql = sql.OrderBy(sortField + " " + sortOrder)
	}

	if req.Limit <= 0 {
		req.Limit = 10
	}

	sql = sql.Offset(req.Offset).Limit(req.Limit)

	sqlstr, args, err := sql.ToSql()

	if err != nil {
		return nil, err
	}

	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	res := &models.ServiceTaskWithETSData{
		Data: []models.ServiceTaskWithETS{},
	}

	for rows.Next() {
		task := models.ServiceTaskWithETS{}
		if err := rows.Scan(
			&task.ID, &task.Type, &task.Status, &task.OrderID, &task.ProviderID, &task.InputPods, &task.OutputFiles, &task.CreatedAt, &task.UpdatedAt, &task.ETS, &task.Shipment, &task.OrderStatus, &task.SubmittedTime, &task.CompletedTime,
		); err != nil {
			return nil, err
		}
		res.Data = append(res.Data, task)
	}

	// Get total count
	sqlCount := utils.QueryCountItems(sqlstr)
	rowCount, err := dao.getQueryer().Queryx(sqlCount, args...)
	if err != nil {
		return nil, err
	}
	defer rowCount.Close()

	if rowCount.Next() {
		if err := rowCount.Scan(&res.Total); err != nil {
			return nil, err
		}
	}
	res.Success = true

	return res, nil
}

func (dao *EtsDao) QueryServiceRelateTasks(ids []int64) ([]*models.ServiceTask, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	sql := dao.Db.PSQL.Select("st.*").
		From("service_tasks st").Join("service_tasks st2 ON st2.order_id = st.order_id").
		Where(squirrel.Eq{"st2.id": ids}).
		GroupBy("st.id")

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.ServiceTask
	for rows.Next() {
		var task models.ServiceTask
		if err := rows.StructScan(&task); err != nil {
			return nil, err
		}
		res = append(res, &task)
	}
	return res, nil
}

func (dao *EtsDao) CreateServiceTask(task *models.ServiceTask) error {
	if task.OrderID <= 0 {
		return fmt.Errorf("ID/order ID cannot be empty")
	}
	sql, args, err := dao.Db.PSQL.Insert(tasksTable).
		Columns("\"type\"", "status", "order_id", "provider_id", "input_pods", "input_pod_values", "output_pods", "output_pod_values").
		Values(task.Type, task.Status, task.OrderID, task.ProviderID, task.InputPods, utils.StructToJSON(task.InputPods.ToMapKeyValue()).Raw, task.OutputPods, utils.StructToJSON(task.OutputPods.ToMapKeyValue()).Raw).
		Suffix("RETURNING id").
		ToSql()
	if err != nil {
		return err
	}

	if err := dao.getQueryer().QueryRowx(sql, args...).Scan(&task.ID); err != nil {
		return err
	}
	return nil
}

func (dao *EtsDao) UpdateServiceTask(update map[string]any, id int64) error {
	if id <= 0 {
		return fmt.Errorf("ID cannot be empty")
	}
	if len(update) == 0 {
		return nil
	}
	sb := dao.Db.PSQL.Update(tasksTable).Set("updated_at", time.Now()).Where(squirrel.Eq{"id": id})
	for k, v := range update {
		switch k {
		case "status", "provider_id":
			sb = sb.Set(k, v)
		case "input_pods":
			sb = sb.Set("input_pods", squirrel.Expr(`json_merge_array(input_pods, ?)`, v))
			inputPods := v.(models.InputPodsArray)
			sb = sb.Set("input_pod_values", utils.StructToJSON(inputPods.ToMapKeyValue()).Raw)
		case "output_pods":
			sb = sb.Set("output_pods", squirrel.Expr(`json_merge_array(output_pods, ?)`, v))
			outputPods := v.(models.InputPodsArray)
			sb = sb.Set("output_pod_values", utils.StructToJSON(outputPods.ToMapKeyValue()).Raw)
		case "note":
			sb = sb.Set("note", squirrel.Expr(`coalesce(note,'{}')::jsonb || ?`, v))
		case "output_files":
			sb = sb.Set("output_files", squirrel.Expr(`coalesce(output_files,'{}')::jsonb || ?`, v))
		case "form_callback":
			sb = sb.Set("form_callback", squirrel.Expr(`coalesce(form_callback,'{}')::jsonb || ?`, v))
		}
	}
	sql, args, err := sb.ToSql()
	if err != nil {
		return err
	}
	res, err := dao.getExecer().Exec(sql, args...)
	if err != nil {
		return err
	}
	r, err := res.RowsAffected()
	if err != nil {
		return err
	}
	if r == 0 {
		return errors.NotFoundErr
	}
	return nil
}

func (dao *EtsDao) GetServicePrice(ids []int) (map[int][]*models.EtsPrice, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	sql, args, err := dao.Db.PSQL.Select(priceCols...).From(priceTable).Where(squirrel.Eq{"ets_id": ids}).ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := dao.getQueryer().Queryx(sql, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	res := map[int][]*models.EtsPrice{}
	for rows.Next() {
		var p models.EtsPrice
		if err := rows.StructScan(&p); err != nil {
			return nil, err
		}
		res[p.EtsID] = append(res[p.EtsID], &p)
	}
	return res, nil
}

func (dao *EtsDao) GetServiceOrderByID(id int) (*models.ServiceOrder, error) {
	sql := dao.Db.PSQL.Select(orderCols...).From(ordersTable).Where(squirrel.Eq{"id": id})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.ServiceOrder
	for rows.Next() {
		var order models.ServiceOrder
		if err := rows.StructScan(&order); err != nil {
			return nil, err
		}
		res = append(res, &order)
	}
	if len(res) == 0 {
		return nil, errors.NotFoundErr
	}
	return res[0], nil
}

func (dao *EtsDao) GetServiceOrders(q map[string][]any) ([]*models.ServiceOrder, error) {
	sql := dao.Db.PSQL.Select(orderCols...).From(ordersTable)
	for k, v := range q {
		switch k {
		case "id", "status", "user_id", "org_id", "service_id", "payment_id":
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.ServiceOrder
	for rows.Next() {
		var o models.ServiceOrder
		if err := rows.StructScan(&o); err != nil {
			return nil, err
		}
		res = append(res, &o)
	}
	return res, nil
}

func (dao *EtsDao) UpdateServiceTaskStatus(toStatus string, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	sb := dao.Db.PSQL.Update(tasksTable).Set("updated_at", time.Now()).Set("status", toStatus).
		Where(squirrel.Eq{"id": ids})
	sql, args, err := sb.ToSql()
	if err != nil {
		return err
	}
	_, err = dao.getExecer().Exec(sql, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *EtsDao) UpdateServiceTaskStatusByOrderID(toStatus string, orderID int64) error {
	sb := dao.Db.PSQL.Update(tasksTable).Set("updated_at", time.Now()).Set("status", toStatus).
		Where(squirrel.NotEq{"status": []string{models.EtsTaskStatusReviewed, models.EtsTaskStatusApproved, models.EtsTaskStatusDenied, models.EtsTaskStatusCancelled}}).
		Where(squirrel.Eq{"order_id": orderID})
	sql, args, err := sb.ToSql()
	if err != nil {
		return err
	}
	_, err = dao.getExecer().Exec(sql, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *EtsDao) GetEtsProviders() ([]*models.EtsProvider, error) {
	sql := dao.Db.PSQL.Select(providerCols...).From(etsProviderTable)
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.EtsProvider
	for rows.Next() {
		var order models.EtsProvider
		if err := rows.StructScan(&order); err != nil {
			return nil, err
		}
		res = append(res, &order)
	}
	return res, nil
}

func (dao *EtsDao) GetEtsProviderByID(id string) (*models.EtsProvider, error) {
	sql := dao.Db.PSQL.Select(providerCols...).From(etsProviderTable).Where(squirrel.Eq{"id": id})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.EtsProvider
	for rows.Next() {
		var order models.EtsProvider
		if err := rows.StructScan(&order); err != nil {
			return nil, err
		}
		res = append(res, &order)
	}
	return res[0], nil
}

func (dao *EtsDao) GetEtsProviderByOrgID(id int64) (*models.EtsProvider, error) {
	sql := dao.Db.PSQL.Select(providerCols...).From(etsProviderTable).Where(squirrel.Eq{"org_id": id})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.EtsProvider
	for rows.Next() {
		var order models.EtsProvider
		if err := rows.StructScan(&order); err != nil {
			return nil, err
		}
		res = append(res, &order)
	}
	return res[0], nil
}

func (dao *EtsDao) QueryEtsPod(q map[string]any) ([]*models.EtsPod, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := dao.Db.PSQL.Select("*").From(etsPodTable)
	for k, v := range q {
		switch k {
		case "service_type", "tasks":
			k = fmt.Sprintf("body->'%s'->>'%s'", "attributes", k)
			sql = sql.Where(squirrel.Eq{k: v})
		case "country", "region_of_residence", "terminal":
			k = fmt.Sprintf("body->'%s'->>'%s'", "attributes", k)
			sql = sql.Where(squirrel.ILike{k: fmt.Sprintf("%%%s%%", v)})
		case "services":
			sql = sql.Where(fmt.Sprintf("services && '{%s}'", v))
		default:
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	fmt.Printf(sqlstr)
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.EtsPod
	for rows.Next() {
		var pod models.EtsPod
		if err := rows.StructScan(&pod); err != nil {
			return nil, err
		}
		res = append(res, &pod)
	}
	return res, nil
}

func (dao *EtsDao) QueryEtsQueryPod(q map[string]any) ([]*models.EtsQueryPod, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := dao.Db.PSQL.Select(`pod_id,service,body,"order"`).From("ets_query_pods")
	for k, v := range q {
		switch k {
		default:
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.EtsQueryPod
	for rows.Next() {
		var pod models.EtsQueryPod
		if err := rows.StructScan(&pod); err != nil {
			return nil, err
		}
		res = append(res, &pod)
	}
	return res, nil
}

func (dao *EtsDao) AddETSOutputFiles(id int, update map[string]string) error {
	if id <= 0 || len(update) <= 0 {
		return nil
	}
	exec := dao.getExecer()
	sb := dao.Db.PSQL
	sql := sb.Update(ordersTable).Where(squirrel.Eq{"id": id})
	byt, err := json.Marshal(update)
	if err != nil {
		return err
	}
	sql = sql.Set("output_files", squirrel.Expr("output_files || ?", byt))

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return err
	}
	_, err = exec.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *EtsDao) AddETSTaskOutputFiles(id int64, update map[string]string) error {
	if id <= 0 || len(update) <= 0 {
		return nil
	}
	exec := dao.getExecer()
	sb := dao.Db.PSQL
	sql := sb.Update(tasksTable).Where(squirrel.Eq{"id": id})
	byt, err := json.Marshal(update)
	if err != nil {
		return err
	}
	sql = sql.Set("output_files", squirrel.Expr("output_files || ?", byt))

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return err
	}
	_, err = exec.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *EtsDao) DeleteETSTaskOutputFiles(taskId int64, key string) error {
	if taskId <= 0 {
		return nil
	}
	exec := dao.getExecer()
	sb := dao.Db.PSQL
	sql := sb.Update(tasksTable).
		Where(squirrel.Eq{"id": taskId}).
		Set("output_files", squirrel.Expr(fmt.Sprintf(`output_files - '%s'`, key)))

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return err
	}
	_, err = exec.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *EtsDao) QueryEtsTask(q map[string]any) ([]string, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := dao.Db.PSQL.Select("tasks").From(etsTable).
		GroupBy("tasks").
		OrderBy(" array_position(array[ 'new', 'renew', 'damaged', 'lost_stolen'], tasks::text)")
	for k, v := range q {
		switch k {
		case "id", "service_type", "currency", "status":
			sql = sql.Where(squirrel.Eq{k: v})
		case "country":
			sql = sql.Where(squirrel.Like{k: fmt.Sprintf("%%%s%%", v)})
		case "tag", "tasks", "airport":
			switch vv := v.(type) {
			case string:
				sql = sql.Where(squirrel.Like{k: fmt.Sprintf("%%%s%%", vv)})
			case []string:
				for _, s := range vv {
					sql = sql.Where(squirrel.Like{k: fmt.Sprintf("%%%s%%", s)})
				}
			}
		case "region_of_residence":
			k = fmt.Sprintf("%s->>'%s'", "attributes", k)
			sql = sql.Where(squirrel.Like{k: fmt.Sprintf("%%%s%%", v)})
		case "user_age":
			k = fmt.Sprintf("%s->>'%s'", "attributes", k)
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []string
	for rows.Next() {
		var s models.ExtendedTravelService
		if err := rows.StructScan(&s); err != nil {
			return nil, err
		}
		res = append(res, s.Tasks[0])
	}
	return res, nil
}

func (dao *EtsDao) GetETSByID(etsID int) (*models.ExtendedTravelService, error) {
	sql := dao.Db.PSQL.Select("id, service_type, country, airport, tasks, attributes, tag").From(etsTable).Where("id = ?", etsID)
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	if rows.Next() {
		var s models.ExtendedTravelService
		if err := rows.StructScan(&s); err != nil {
			return nil, err
		}
		return &s, nil
	} else {
		return nil, errors.NotFoundErr
	}
}

func (dao *EtsDao) QueryEts(q map[string]any) ([]models.ExtendedTravelService, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := dao.Db.PSQL.Select("id, service_type, country, airport, tasks, attributes, tag").From(etsTable).Where("status = ?", "active")
	for k, v := range q {
		switch k {
		case "service_type", "airport":
			sql = sql.Where(squirrel.Eq{k: v})
		case "country":
			sql = sql.Where(squirrel.Like{k: fmt.Sprintf("%%%s%%", v)})
		case "region_of_residence", "nationality":
			k = fmt.Sprintf("%s->>'%s'", "attributes", k)
			sql = sql.Where(squirrel.Like{k: fmt.Sprintf("%%%s%%", v)})
		default:
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []models.ExtendedTravelService
	for rows.Next() {
		var s models.ExtendedTravelService
		if err := rows.StructScan(&s); err != nil {
			return nil, err
		}
		res = append(res, s)
	}
	return res, nil
}

func (dao *EtsDao) CleanEtsValidateResult(id int) error {
	if _, err := dao.Db.PSQL.Delete("ets_atlas_result").
		Where("service_task_id IN (SELECT id FROM service_tasks WHERE order_id = ?)", id).
		RunWith(dao.Db.Db).Exec(); err != nil {
		return err
	}
	return nil
}

func (dao *EtsDao) GetServiceType() ([]models.ServiceType, error) {
	var query = dao.Db.PSQL.Select(`e.service_type`).
		From("ets e").
		Where("e.status = 'active'").
		GroupBy("e.service_type").
		OrderBy("e.service_type")

	sqlStr, args, _ := query.ToSql()
	rows, err := dao.getQueryer().Queryx(sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []models.ServiceType
	for rows.Next() {
		var (
			serviceType string
		)
		if err := rows.Scan(&serviceType); err != nil {
			return nil, err
		}
		res = append(res, models.ServiceType{
			ServiceType: serviceType,
		})
	}
	return res, nil
}

func (dao *EtsDao) GetShippingServiceByID(id int64) (*models.ShippingService, error) {
	rows, err := dao.Db.PSQL.Select("id, status, carrier, service, price, currency, created_at, updated_at, shipping_time").
		From("shipping_service").
		Where("id = ?", id).RunWith(dao.Db.Db).Query()

	if err != nil {
		return nil, err
	}
	defer rows.Close()

	if rows.Next() {
		item := &models.ShippingService{}
		if err := rows.Scan(&item.ID, &item.Status, &item.Carrier, &item.Service, &item.Price, &item.Currency, &item.CreatedAt, &item.UpdatedAt, &item.ShippingTime); err != nil {
			return nil, err
		}
		return item, nil
	}
	return nil, nil
}

func (dao *EtsDao) DeleteETSTask(id []string) error {
	if len(id) <= 0 {
		return nil
	}

	var runner squirrel.BaseRunner
	if dao.tx != nil {
		runner = dao.tx.Tx
	} else {
		runner = dao.Db.Db
	}

	if _, err := dao.Db.PSQL.Delete("service_tasks").Where(squirrel.Eq{
		"id": id,
	}).RunWith(runner).Exec(); err != nil {
		return err
	}

	return nil
}

func (dao *EtsDao) GetProfileDataByCondition(userID, table, field, condition, value string) (any, error) {
	if funk.ContainsString([]string{"address_book", "passport_book"}, table) {
		query := dao.Db.PSQL.Select(field).From(table).
			Where("traveler_id IN (select traveler_id from traveler_type_index tti where  user_id = ? AND profile_type = 'my_profile')", userID)
		if table == "address_book" {
			query = query.Where("status = ?", "active")
		}

		if condition != "" {
			if condition == "is_primary_address" {
				query = query.Where("(is_primary_address = ? OR TRUE)", value) // Private or first one
			} else {
				query = query.Where(squirrel.Eq{
					condition: value,
				})
			}

		}
		rows, err := query.RunWith(dao.Db.Db).Query()
		if err != nil {
			return nil, err
		}
		defer rows.Close()
		if rows.Next() {
			var res any
			if err := rows.Scan(&res); err != nil {
				return nil, err
			}
			return res, nil
		}
	}
	if table == "personal_info" {
		rows, err := dao.Db.PSQL.Select(field).From("traveler").
			Where("id IN (select traveler_id from traveler_type_index tti where  user_id = ? AND profile_type = 'my_profile')", userID).
			RunWith(dao.Db.Db).Query()
		if err != nil {
			return nil, err
		}
		defer rows.Close()
		if rows.Next() {
			var res any
			if err := rows.Scan(&res); err != nil {
				return nil, err
			}
			return res, nil
		}
	}
	return nil, nil

}

func (dao *EtsDao) GetUserCorpDataByCondition(userID string, field string) (any, error) {
	rows, err := dao.Db.PSQL.Select("corporation_name, corporation_address,phone").From("user_corp_info").
		Where("user_id = ?", userID).
		RunWith(dao.Db.Db).Query()
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	if rows.Next() {
		var userCorp models.UserCorpInfo
		if err := rows.Scan(&userCorp.CorporationName, &userCorp.CorporationAddress, &userCorp.Phone); err != nil {
			return nil, err
		}

		if field == "phone" {
			return userCorp.Phone, nil
		} else {
			return utils.StructToJSON(userCorp.CorporationAddress).Get(field).String(), nil
		}
	}
	return nil, nil
}

func (dao *EtsDao) QueryServiceCustomerData(userID string, queryStr string) ([]models.ServiceCustomerData, error) {
	query := dao.Db.PSQL.Select("passport_number, user_id, app_name, pod_values").
		From("service_customer_pod_data").
		Where("user_id = ?", userID).
		Limit(20)

	if queryStr != "" {
		query = query.Where("(passport_number::text ILIKE ? OR app_name::text ILIKE ?)", "%"+queryStr+"%", "%"+queryStr+"%")
	}
	rows, err := query.RunWith(dao.Db.Db).Query()

	if err != nil {
		return nil, err
	}
	defer rows.Close()

	result := []models.ServiceCustomerData{}
	for rows.Next() {
		var passportNumber, userID, appName string
		var podValues json.RawMessage

		if err := rows.Scan(&passportNumber, &userID, &appName, &podValues); err != nil {
			return nil, err
		}

		var podValuesMap map[string]any
		err = json.Unmarshal(podValues, &podValuesMap)
		if err != nil {
			return nil, err
		}

		data := models.ServiceCustomerData{
			PassportNumber: passportNumber,
			UserID:         userID,
			AppName:        appName,
			// PodValues:      podValuesMap,
		}

		result = append(result, data)
	}

	return result, nil
}

func (dao *EtsDao) GetServiceCustomerData(passportNumber string, userID string) (map[string]any, error) {
	var podValues json.RawMessage
	query := dao.Db.PSQL.Select("pod_values").
		From("service_customer_pod_data").
		Where("passport_number = ?", passportNumber)

	if userID != "" {
		query = query.Where("user_id = ?", userID)
	}

	err := query.RunWith(dao.Db.Db).QueryRow().Scan(&podValues)
	if err != nil {
		return nil, nil
	}

	var podValuesMap map[string]any
	err = json.Unmarshal(podValues, &podValuesMap)
	if err != nil {
		return nil, err
	}

	return podValuesMap, nil
}

func (dao *EtsDao) CreateServiceCustomerData(userID, appName, passportNumber string, inputPodValues map[string]any) error {
	_, err := dao.Db.Db.Exec(`INSERT INTO service_customer_pod_data (passport_number,user_id,app_name, pod_values) 
	VALUES ($1, $2, $3, $4) ON CONFLICT (passport_number, user_id) DO UPDATE SET app_name=$3,  pod_values = service_customer_pod_data.pod_values || $4`,
		passportNumber,
		userID,
		appName,
		utils.StructToJSON(inputPodValues).Raw)

	if err != nil {
		return err
	}

	return nil
}

func (dao *EtsDao) CreateWatchdogJob(job models.WatchDogJob) error {
	_, err := dao.Db.Db.Exec(`INSERT INTO watchdog_jobs (job_name, job_data, schedule_at, status, created_at, updated_at) 
	VALUES ($1, $2, $3, $4, $5, $6)`,
		job.JobName,
		job.JobData,
		job.ScheduleAt,
		"pending",
		time.Now(),
		time.Now())

	if err != nil {
		return err
	}

	return nil
}
