package db

import (
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

var (
	extendedServiceColunms = []string{
		"id",
		"status",
		"name",
		"currency",
		"price",
		"discount",
		"description",
		"created_at",
		"updated_at",
	}
)

const (
	extendedServiceTable = "extended_service"
)

func (dao *Dao) QueryExtendServices(q map[string][]any) ([]*models.ExtendedService, error) {
	return queryExtendedServices(dao.getQueryer(), dao.Db.PSQL, q)
}

func queryExtendedServices(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any) ([]*models.ExtendedService, error) {
	sql := sb.Select(extendedServiceColunms...).From(extendedServiceTable)
	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.ExtendedService
	for rows.Next() {
		var one models.ExtendedService
		if err = rows.StructScan(&one); err != nil {
			return nil, err
		}
		res = append(res, &one)
	}
	return res, nil
}
