package db

import (
	"fmt"
	"net/url"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

var (
	paymentTable   = "payment"
	paymentColumns = []string{
		"id",
		"method",
		"external_transaction_id",
		"method_desc",
		"currency",
		"count",
		"unit_price",
		"tax_rate",
		"discount",
		"total",
		"status",
		"invoice",
		"promotion_code",
		"description",
		"properties",
		"receipt_id",
		"receipt_file",
		"created_at",
		"updated_at",
	}
)

type PaymentsDao struct {
	Db *AuroraDB
	tx *sqlx.Tx
}

func NewPaymentDaoWithDb(db *AuroraDB) *PaymentsDao {
	return &PaymentsDao{Db: db}
}

func (p *PaymentsDao) InsertPayment(payment *models.Payment) error {
	var execer sqlx.Execer
	execer = p.Db.Db
	if p.tx != nil {
		execer = p.tx
	}
	return insertPayment(execer, payment, p.Db.PSQL)
}

func (p *PaymentsDao) UpdatePayment(payment *models.Payment) error {
	var execer sqlx.Execer
	execer = p.Db.Db
	if p.tx != nil {
		execer = p.tx
	}
	return updatePayment(execer, payment, p.Db.PSQL)
}

func (p *PaymentsDao) GetPayment(id string) (*models.Payment, error) {
	if id == "" {
		return nil, nil
	}
	q := url.Values{}
	q.Add("id", id)
	var queryer sqlx.Queryer
	queryer = p.Db.Db
	if p.tx != nil {
		queryer = p.tx
	}
	payments, err := queryPayments(queryer, &q, p.Db.PSQL)
	if err != nil {
		return nil, err
	}
	if len(payments) == 0 {
		return nil, nil
	}
	return payments[0], nil
}

func (p *PaymentsDao) GetPaymentByExternalID(id string) (*models.Payment, error) {
	q := url.Values{}
	q.Add("external_transaction_id", id)
	var queryer sqlx.Queryer
	queryer = p.Db.Db
	if p.tx != nil {
		queryer = p.tx
	}
	res, err := queryPayments(queryer, &q, p.Db.PSQL)
	if err != nil {
		return nil, err
	}
	if len(res) != 1 {
		return nil, fmt.Errorf("got more than one payments for transaction ID %s", id)
	}
	return res[0], nil
}

// GetPaymentList get payment list
func (p *PaymentsDao) GetPaymentList(req models.PaymentListRequest) ([]models.Payment, error) {
	var res = []models.Payment{}

	sq := p.Db.PSQL.Select(paymentColumns...).From("payment")

	if !req.GetAllItems {
		sq = sq.Limit(req.Limit).Offset(req.Offset)
	}

	if len(req.Status) > 0 {
		sq = sq.Where(squirrel.Eq{"status": req.Status})
	}

	if len(req.PaymentMethod) > 0 {
		sq = sq.Where(squirrel.Eq{"method": req.PaymentMethod})
	}

	if req.FromDate != nil {
		sq = sq.Where(squirrel.GtOrEq{"created_at": *req.FromDate})
	}

	if req.ToDate != nil {
		sq = sq.Where(squirrel.LtOrEq{"created_at": *req.ToDate})
	}

	sql, args, err := sq.ToSql()
	if err != nil {
		return res, err
	}

	var queryer sqlx.Queryer
	queryer = p.Db.Db
	if p.tx != nil {
		queryer = p.tx
	}
	rows, err := queryer.Queryx(sql, args...)
	if err != nil {
		return res, err
	}
	defer rows.Close()

	for rows.Next() {
		var p models.Payment
		if err = rows.StructScan(&p); err != nil {
			return res, err
		}
		res = append(res, p)
	}
	return res, nil
}

func (p *PaymentsDao) Begin() error {
	tx, err := p.Db.Db.Beginx()
	if err != nil {
		return err
	}
	p.tx = tx
	return nil
}

func (p *PaymentsDao) Commit() error {
	if p.tx == nil {
		return nil
	}
	if err := p.tx.Commit(); err != nil {
		return err
	}
	p.tx = nil
	return nil
}

func (p *PaymentsDao) Rollback() error {
	if p.tx == nil {
		return nil
	}
	if err := p.tx.Rollback(); err != nil {
		return err
	}
	p.tx = nil
	return nil
}

func insertPayment(exec sqlx.Execer, payment *models.Payment, builder *squirrel.StatementBuilderType) error {
	sql := builder.Insert("payment")
	sql = sql.SetMap(payment.ToMap())

	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return err
	}
	_, err = exec.Exec(sqlStr, args...)
	if err != nil {
		return err
	}
	return nil
}

func updatePayment(exec sqlx.Execer, update *models.Payment, builder *squirrel.StatementBuilderType) error {
	if update.ID == "" {
		return fmt.Errorf("missing id to update")
	}
	sql := builder.Update("payment").Where(squirrel.Eq{"id": update.ID})
	if update.Status != "" {
		sql = sql.Set("status", update.Status)
	}
	if update.ExternalTransactionID != "" {
		sql = sql.Set("external_transaction_id", update.ExternalTransactionID)
	}
	if update.Description != "" {
		sql = sql.Set("description", update.Description)
	}
	if update.PromotionCode != "" {
		sql = sql.Set("promotion_code", update.PromotionCode)
	}
	if update.Invoice != "" {
		sql = sql.Set("invoice", update.Invoice)
	}
	if update.Discount != 0.0 {
		sql = sql.Set("discount", update.Discount)
	}
	if update.TaxRate != 0.0 {
		sql = sql.Set("tax_rate", update.TaxRate)
	}
	if update.UnitPrice != 0.0 {
		sql = sql.Set("unit_price", update.UnitPrice)
	}
	if update.Count != 0 {
		sql = sql.Set("count", update.Count)
	}
	if update.Currency != "" {
		sql = sql.Set("currency", update.Currency)
	}
	if update.Method != "" {
		sql = sql.Set("method", update.Method)
	}
	if update.MethodDescription != "" {
		sql = sql.Set("method_desc", update.MethodDescription)
	}
	if update.Total != 0.0 {
		sql = sql.Set("total", update.Total)
	}
	if update.Properties != nil && len(*update.Properties) > 0 {
		sql = sql.Set("properties", update.Properties)
	}
	if update.ReceiptID != "" {
		sql = sql.Set("receipt_id", update.ReceiptID)
	}
	if update.ReceiptFile != "" {
		sql = sql.Set("receipt_file", update.ReceiptFile)
	}
	sql = sql.Set("updated_at", time.Now())

	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return err
	}
	_, err = exec.Exec(sqlStr, args...)
	if err != nil {
		return err
	}
	return nil
}

func queryPayments(q sqlx.Queryer, query *url.Values, builder *squirrel.StatementBuilderType) ([]*models.Payment, error) {
	if len(*query) == 0 {
		return nil, fmt.Errorf("empty query")
	}
	sql := builder.Select(paymentColumns...).From("payment")
	validQuery := false
	for k, v := range *query {
		switch k {
		case "status", "method", "receipt_id":
			sql = sql.Where(squirrel.Eq{k: v})
			validQuery = true
		case "id", "external_transaction_id":
			sql = sql.Where(squirrel.Or{
				squirrel.Eq{"id": v},
				squirrel.Eq{"external_transaction_id": v},
			})
			validQuery = true
		case "cart_id":
			sql = sql.Where("EXISTS (SELECT * FROM cart WHERE payment_id = payment.id)")
			validQuery = true
		}
	}
	if !validQuery {
		return nil, fmt.Errorf("no supported query field, %v", *query)
	}
	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := q.Queryx(sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.Payment
	for rows.Next() {
		var p models.Payment
		if err = rows.StructScan(&p); err != nil {
			return nil, err
		}
		res = append(res, &p)
	}
	return res, nil
}

func (p *PaymentsDao) QueryPayments(query url.Values) ([]*models.Payment, error) {
	var queryer sqlx.Queryer
	queryer = p.Db.Db
	if p.tx != nil {
		queryer = p.tx
	}
	return queryPayments(queryer, &query, p.Db.PSQL)
}
