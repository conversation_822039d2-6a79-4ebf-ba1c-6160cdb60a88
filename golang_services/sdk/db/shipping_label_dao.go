package db

import (
	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
)

const (
	shippingLabelTable = "shipping_label"
)

var (
	shippingLabelColumns = []string{"id", "visa_shipment_id", "tracking_number", "currency", "price", "label", "creator", "status", "ship_date", "created_at", "updated_at"}
)

func (dao *Dao) QueryShippingLabels(q map[string][]any) ([]*models.ShippingLabel, error) {
	return queryShippingLabel(dao.getQueryer(), dao.Db.PSQL, q)
}

func queryShippingLabel(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any) ([]*models.ShippingLabel, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := sb.Select(shippingLabelColumns...).From(shippingLabelTable)
	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}
	sql = sql.OrderBy("created_at DESC")
	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := queryer.Queryx(sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.ShippingLabel
	for rows.Next() {
		var s models.ShippingLabel
		if err := rows.StructScan(&s); err != nil {
			return nil, err
		}
		res = append(res, &s)
	}
	return res, nil
}
