package db

import (
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/service/rds/rdsutils"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
)

type AuroraDB struct {
	Db   *sqlx.DB
	PSQL *squirrel.StatementBuilderType
}

func NewAuroraDBWithAwsCred(endpoint, region, user, dbname string, cred *credentials.Credentials) (*AuroraDB, error) {
	connStr, err := rdsutils.NewConnectionStringBuilder(endpoint, region, user, dbname, cred).Build()
	if err != nil {
		return nil, err
	}
	return NewAuroraDBWithDns(connStr)
}

func NewAuroraDBWithDns(dns string) (*AuroraDB, error) {
	connector, err := pq.NewConnector(dns)
	if err != nil {
		return nil, err
	}

	db := sql.OpenDB(connector)

	psql := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar)
	return &AuroraDB{Db: sqlx.NewDb(db, "postgres"), PSQL: &psql}, nil
}

func NewAuroraDBFromConfigMap(config map[string]any) (*AuroraDB, error) {
	pwd, dbname, port, username, wHost := config["password"], config["dbname"], config["port"], config["username"], config["write_host"]
	dns := fmt.Sprintf("postgres://%s:%s@%s:%0.0f/%s?sslmode=disable", username, pwd, wHost, port, dbname)
	return NewAuroraDBWithDns(dns)
}
