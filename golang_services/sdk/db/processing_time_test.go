package db

import (
	"testing"
)

func Test_calculateMedianWorkingHours(t *testing.T) {
	type args struct {
		workingHours map[string][]string
	}
	tests := []struct {
		name string
		args args
		want float64
	}{
		{
			name: "Empty working hours",
			args: args{
				workingHours: map[string][]string{},
			},
			want: 0,
		},
		{
			name: "Single day working hours",
			args: args{
				workingHours: map[string][]string{
					"mon": {"9:00", "12:00", "13:00", "17:00"},
				},
			},
			want: 8,
		},
		{
			name: "Multiple days with same hours",
			args: args{
				workingHours: map[string][]string{
					"mon": {"9:00", "12:00", "13:00", "17:00"},
					"tue": {"9:00", "12:00", "13:00", "17:00"},
					"wed": {"9:00", "12:00", "13:00", "17:00"},
				},
			},
			want: 8,
		},
		{
			name: "Multiple days with different hours",
			args: args{
				workingHours: map[string][]string{
					"mon": {"9:00", "12:00", "13:00", "17:00"},
					"tue": {"9:00", "12:00"},
					"wed": {"9:00", "12:00", "13:00"},
				},
			},
			want: 6,
		},
		{
			name: "All days included",
			args: args{
				workingHours: map[string][]string{
					"sun": {},
					"mon": {"9:00", "12:00", "13:00", "17:00"},
					"tue": {"9:00", "12:00", "13:00", "17:00"},
					"wed": {"9:00", "12:00", "13:00", "17:00"},
					"thu": {"9:00", "12:00", "13:00", "17:00"},
					"fri": {"9:00", "12:00", "13:00", "17:00"},
					"sat": {},
				},
			},
			want: 8,
		},
		{
			name: "All days included",
			args: args{
				workingHours: map[string][]string{
					"sun": {},
					"mon": {"9:00", "12:00", "13:00", "17:00"},
					"tue": {"9:00", "12:00", "13:00", "17:00"},
					"wed": {"9:00", "12:00", "13:00", "17:00"},
					"thu": {"9:00", "12:00", "13:00", "17:00"},
					"fri": {"9:00", "12:00", "13:00", "17:00"},
					"sat": {"9:00", "12:00"},
				},
			},
			want: 8,
		},
		{
			name: "Median of 2 days",
			args: args{
				workingHours: map[string][]string{
					"mon": {"9:00", "12:00", "13:00"},
					"tue": {"9:00", "12:00", "13:00", "17:00"},
				},
			},
			want: 7,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := calculateMedianWorkingHours(tt.args.workingHours); got != tt.want {
				t.Errorf("calculateMedianWorkingHours() = %v, want %v", got, tt.want)
			}
		})
	}
}
