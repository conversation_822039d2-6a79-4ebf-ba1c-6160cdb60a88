package db

import (
	"fmt"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
)

const (
	cartItemTable = "cart_item"
)

var (
	cartItemColumns = []string{
		"id",
		"cart_id",
		"product_type",
		"product_id",
		"created_at",
		"updated_at",
	}
)

// AddItemToCart add item to cart
func (dao *Dao) AddItemToCart(c *models.CartItem) error {
	if c == nil {
		return nil
	}

	if c.Status == "" {
		c.Status = models.CartItemStatus.Created
	}

	return dao.Db.PSQL.Insert(cartItemTable).
		Columns("cart_id, product_type, product_id", "status").
		Values(c.CartID, c.ProductType, c.ProductID, c.Status).
		Suffix("RETURNING id, created_at").
		RunWith(dao.Db.Db).QueryRow().
		Scan(&c.ID, &c.CreatedAt)
}

// RemoveItemFromCart remove item from cart
func (dao *Dao) RemoveItemFromCart(id int) error {
	result, err := dao.Db.PSQL.Delete("cart_item ci USING cart c").
		Where("ci.cart_id = c.id").
		Where("ci.id = ?", id).
		RunWith(dao.Db.Db).Exec()

	if err != nil {
		return err
	}

	if rowAffected, err := result.RowsAffected(); err != nil || rowAffected == 0 {
		return fmt.Errorf("ItemID: %d is not exist in cart", id)
	}

	return nil
}

func (dao *Dao) GetCartItemByCardID(cartID int) ([]*models.CartItem, error) {
	if cartID == 0 {
		return nil, nil
	}
	q := map[string][]any{"cart_id": {cartID}}
	return queryCartItems(dao.getQueryer(), dao.Db.PSQL, q)
}

func queryCartItems(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any) ([]*models.CartItem, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := sb.Select(cartItemColumns...).From(cartItemTable)
	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}
	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := queryer.Queryx(sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.CartItem
	for rows.Next() {
		var s models.CartItem
		if err := rows.StructScan(&s); err != nil {
			return nil, err
		}
		res = append(res, &s)
	}
	return res, nil
}

// UpdateCartItem update cart item
func (dao *Dao) UpdateCartItem(id any, update map[string]any) error {
	update["updated_at"] = time.Now()
	query := dao.Db.PSQL.Update(cartItemTable).Where("id = ?", id)
	for key, val := range update {
		switch key {
		case "status":
			query = query.Set(key, val)
		}
	}

	_, err := query.RunWith(dao.Db.Db).Exec()
	return err
}
