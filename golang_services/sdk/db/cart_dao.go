package db

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/types"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/Masterminds/squirrel"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/jmoiron/sqlx"
	"github.com/thoas/go-funk"
)

const (
	cartTable = "cart"
)

var (
	cartColumns = []string{
		"id",
		"payment_id",
		"is_current",
		"status",
		"user_id",
		"created_at",
		"updated_at",
	}
)

// GetCartByID get cart by ID
func (dao *Dao) GetCartByID(cartID int) (*models.CartData, error) {
	data, err := dao.GetCartList(models.CartFilter{
		CartID: cartID,
	})

	if err != nil {
		return nil, err
	}

	if len(data.Data) == 0 {
		return nil, fmt.Errorf("No items in cart")
	}

	return &data.Data[0], nil
}

// GetUserCart get current user cart
func (dao *Dao) GetUserCart(userID string) (*models.CartData, error) {
	data, err := dao.GetCartList(models.CartFilter{
		UserID:    userID,
		IsCurrent: aws.Bool(true),
	})

	if err != nil {
		return nil, err
	}

	if len(data.Data) == 0 {
		cart := models.Cart{
			UserID: userID,
		}

		if err = dao.CreateCart(&cart); err != nil {
			return nil, err
		}

		return &models.CartData{
			ServiceData: []models.CartItem{},
			Cart:        cart,
		}, nil
	}

	return &data.Data[0], nil
}

// GetCartByPayment get cart by payment id
func (dao *Dao) GetCartByPayment(paymentID string) (*models.CartData, error) {
	data, err := dao.GetCartList(models.CartFilter{
		PaymentID: paymentID,
	})

	if err != nil {
		return nil, err
	}

	if len(data.Data) == 0 {
		return nil, nil
	}

	return &data.Data[0], nil
}

// GetCartList get the cart list
func (dao *Dao) GetCartList(filter models.CartFilter) (models.CartFilterData, error) {
	query := dao.Db.PSQL.Select("c.id,c.payment_id,c.status,c.is_current,c.user_id,c.created_at,c.updated_at, coalesce(jsonb_agg(to_jsonb(ci))  FILTER (WHERE ci.id IS NOT NULL),'[]'),vo.name").
		From("cart c").
		LeftJoin("users u ON u.id = c.user_id").
		LeftJoin("v_organizations vo ON vo.id = u.organization_id AND vo.type = 'corporation'").
		LeftJoin("cart_item ci ON ci.cart_id = c.id").
		LeftJoin("payment p ON p.id = c.payment_id").
		Where(squirrel.Or{
			squirrel.Expr("ci.product_type = ? AND EXISTS (SELECT * FROM service_orders WHERE id::text = ci.product_id AND deleted_at is NULL)", models.ProductType.AirPortService),
			squirrel.Expr("ci IS NULL"),
		}).
		GroupBy("c.id,c.payment_id,c.status,c.user_id,c.created_at,c.updated_at,vo.name")

	// Query by card_id, order number, visa info and service info
	if len(filter.Query) > 0 {
		text := "%" + filter.Query + "%"
		query = query.Where(squirrel.Or([]squirrel.Sqlizer{
			squirrel.ILike{"c.id::text": text},
			squirrel.ILike{"ci.product_id": text},
			squirrel.Expr(`ci.product_id IN (select st.order_id::text from service_tasks st,jsonb_to_recordset(st.input_pods) as items(name text, value jsonb)
			where items.name = ANY(ARRAY['passenger_name_list','given_name','surname']) and items.value->>'fe' ILIKE ?)`, text),
		}))
	}

	if len(filter.Status) > 0 {
		query = query.Where(squirrel.Eq{"status": filter.Status})
	}

	if filter.UserID != "" {
		query = query.Where("user_id = ?", filter.UserID)
	}

	if filter.CartID > 0 {
		query = query.Where("c.id = ?", filter.CartID)
	}

	if filter.PaymentID != "" {
		query = query.Where("c.payment_id = ?", filter.PaymentID)
	}

	if len(filter.PaymentStatus) > 0 {
		query = query.Where(squirrel.Eq{"p.status": filter.PaymentStatus})
	}

	if filter.IsCurrent != nil {
		query = query.Where("is_current = ?", *filter.IsCurrent)
	}

	if filter.Pagination {
		query = query.Limit(filter.Limit).Offset(filter.Offset)
	}

	var sortField, sortOrder string

	switch filter.SortField {
	case "id":
		sortField = "c.id"
	case "status", "method", "total", "updated_at":
		sortField = "p." + filter.SortField
		query = query.GroupBy(sortField)
	case "remain":
		sortField = "p.properties->>'remain'"
		query = query.GroupBy("p.properties")
	default:
		sortField = "c.created_at"
	}

	if strings.ToUpper(filter.SortOrder) == "ASC" {
		sortOrder = "ASC"
	} else {
		sortOrder = "DESC"
	}

	query = query.OrderBy(sortField + " " + sortOrder)

	result := models.CartFilterData{}

	sqlStr, args, _ := query.ToSql()
	rows, err := dao.Db.Db.Query(sqlStr, args...)
	if err != nil {
		return result, err
	}
	defer rows.Close()

	for rows.Next() {
		c := models.CartData{}
		if err = rows.Scan(&c.Cart.ID, &c.Cart.PaymentID, &c.Cart.Status, &c.Cart.IsCurrent, &c.Cart.UserID, &c.Cart.CreatedAt, &c.Cart.UpdatedAt, &c.Cart.CartItems, &c.CorporationName); err != nil {
			return result, err
		}

		// Get payment info
		if c.Cart.PaymentID.Valid {
			payment := types.ExtJSON{}
			if err = dao.DB().Db.PSQL.Select("to_json(p)").From("payment p").Where("id = ?", c.Cart.PaymentID).
				RunWith(dao.Db.Db).QueryRow().Scan(&payment); err != nil {
				return result, err
			}

			if err = json.NewDecoder(bytes.NewBuffer(payment.Data)).Decode(&c.Payment); err != nil {
				return result, err
			}
		}

		// Get cart items detail
		cartItems := []models.CartItem{}
		if err = json.NewDecoder(bytes.NewBuffer(c.Cart.CartItems.Data)).Decode(&cartItems); err != nil {
			return result, err
		}

		c.ServiceData = []models.CartItem{}

		// Group cart items by type
		if len(cartItems) > 0 {
			// Group airport service data
			serviceItems := cartItems

			serviceIDs := funk.Map(serviceItems, func(ci models.CartItem) string {
				return ci.ProductID
			}).([]string)

			if len(serviceIDs) > 0 {
				etsDao := &EtsDao{
					Db: dao.Db,
				}

				serviceList, err := etsDao.QueryServiceOrders(models.ServiceOrderFilter{
					ID:              serviceIDs,
					IncludeTasks:    true,
					IncludePrice:    true,
					IncludeShipment: true,
					Limit:           uint64(^uint8(0)),
				})

				if err != nil {
					return result, err
				}

				mapServiceList := funk.ToMap(serviceList.Data, "ID").(map[int]*models.ServiceOrderDetail)
				// Get service data from map packages
				c.ServiceData = funk.Map(serviceItems, func(ci models.CartItem) models.CartItem {
					productID, _ := strconv.Atoi(ci.ProductID)
					if val, ok := mapServiceList[productID]; ok {
						ci.ServiceDetail = val
					}
					return ci
				}).([]models.CartItem)
			}
		}

		result.Data = append(result.Data, c)
	}

	// Get total count
	if filter.Pagination {
		sqlCount := utils.QueryCountItemV2(sqlStr)
		if err = dao.Db.Db.Get(&result.Total, sqlCount, args...); err != nil {
			return result, err
		}
	}

	result.Success = true
	return result, nil
}

// CreateCart create new cart
func (dao *Dao) CreateCart(c *models.Cart) error {
	if c == nil {
		return nil
	}

	return dao.Db.PSQL.Insert(cartTable).
		Columns("user_id").Values(c.UserID).
		Suffix("RETURNING id, status, created_at").
		RunWith(dao.Db.Db).QueryRow().
		Scan(&c.ID, &c.Status, &c.CreatedAt)

}

// UpdateCart update cart
func (dao *Dao) UpdateCart(id int, update map[string]any) error {
	update["updated_at"] = time.Now()
	query := dao.Db.PSQL.Update(cartTable).Where("id = ?", id)
	for key, val := range update {
		switch key {
		case "status", "payment_id", "is_current":
			query = query.Set(key, val)
		}
	}

	_, err := query.RunWith(dao.Db.Db).Exec()
	return err
}

// CheckItemBeforeAddToCart check item before add to cart
func (dao *Dao) CheckItemBeforeAddToCart(item models.CartItem) error {
	switch item.ProductType {
	case models.ProductType.AirPortService:
		status := ""

		// Check is service exist and status is open
		err := dao.Db.PSQL.Select("status").From("service_orders").Where("id = ?", item.ProductID).
			RunWith(dao.Db.Db).QueryRow().Scan(&status)
		if err == sql.ErrNoRows {
			return fmt.Errorf("Order: %s is not exist", item.ProductID)
		} else if err != nil {
			return err
		}

		if status != string(models.EtsOrderStatusOpen) && status != string(models.EtsOrderStatusPendingPayment) {
			return fmt.Errorf("Order with status: %s can't add to cart", status)
		}

	default:
		return fmt.Errorf("Unsupported payment product")
	}

	// Check is package_id already in cart
	count := 0
	if err := dao.Db.PSQL.Select("COUNT(*)").From("cart c").
		LeftJoin("cart_item ci ON ci.cart_id = c.id").
		Where("product_type = ? AND product_id = ? AND c.is_current = ?", item.ProductType, item.ProductID, true).
		RunWith(dao.Db.Db).QueryRow().Scan(&count); err != nil {
		return err
	}

	if count > 0 {
		return fmt.Errorf("Product: %s already added to cart", item.ProductID)
	}
	return nil
}

func (dao *Dao) GetCartByPaymentID(paymentID string) (*models.Cart, error) {
	if paymentID == "" {
		return nil, nil
	}
	q := map[string][]any{"payment_id": {paymentID}}
	res, err := queryCarts(dao.getQueryer(), dao.Db.PSQL, q)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

func queryCarts(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any) ([]*models.Cart, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := sb.Select(cartColumns...).From(cartTable)
	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}
	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := queryer.Queryx(sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.Cart
	for rows.Next() {
		var s models.Cart
		if err := rows.StructScan(&s); err != nil {
			return nil, err
		}
		res = append(res, &s)
	}
	return res, nil
}
