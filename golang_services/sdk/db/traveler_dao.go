package db

import (
	sqllib "database/sql"
	"encoding/json"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
	uuid "github.com/satori/go.uuid"
)

const (
	travelerTable = "traveler"
)

var (
	travelerCols = []string{
		"id",
		"user_id",
		"surname",
		"given_name",
		"phone",
		"prefer_name",
		"residency",
		"marital_status",
		"work_email",
		"personal_email",
		"work_phone",
		"job_title",
		"occupation",
		"social_network",
		"home_address",
		"resident_info",
		"travel_history",
		"created_at",
		"updated_at",
	}
)

func (dao *Dao) QueryTravelers(q map[string][]any) ([]*models.Traveler, error) {
	res, err := queryTravelers(dao.getQueryer(), dao.Db.PSQL, q)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res, nil
}

func (dao *Dao) GetTravelerByID(id string) (*models.Traveler, error) {
	if id == "" {
		return nil, nil
	}
	q := map[string][]any{"id": {id}}
	res, err := queryTravelers(dao.getQueryer(), dao.Db.PSQL, q)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

func queryTravelers(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any) ([]*models.Traveler, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := sb.Select(travelerCols...).From(travelerTable)
	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}
	sql = sql.OrderBy("updated_at DESC")
	sqlstr, args, err := sql.ToSql()
	if err != nil && err != sqllib.ErrNoRows {
		return nil, err
	}

	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var res []*models.Traveler
	for rows.Next() {
		var item models.Traveler
		if err = rows.StructScan(&item); err != nil {
			return nil, err
		}
		res = append(res, &item)
	}
	return res, nil
}

func (dao *Dao) InsertTraveler(item *models.Traveler) (string, error) {
	if item == nil {
		return "", nil
	}

	now := time.Now()
	id := uuid.NewV4().String()
	traveler := map[string]any{
		"id":             id,
		"user_id":        item.UserID,
		"surname":        item.Surname,
		"given_name":     item.GivenName,
		"phone":          item.Phone,
		"prefer_name":    item.PreferName,
		"residency":      item.Residency,
		"marital_status": item.MaritalStatus,
		"work_email":     item.WorkEmail,
		"personal_email": item.PersonalEmail,
		"work_phone":     item.WorkPhone,
		"job_title":      item.JobTitle,
		"occupation":     item.Occupation,
		"social_network": item.SocialNetwork,
		"home_address":   item.HomeAddress,
		"resident_info":  item.ResidentInfo,
		"travel_history": item.TravelHistory,
		"created_at":     now,
		"updated_at":     now,
	}
	return insertTraveler(dao.getQueryer(), dao.Db.PSQL, traveler)
}

func insertTraveler(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, newTraveler map[string]any) (string, error) {
	sql := sb.Insert(travelerTable).SetMap(newTraveler).Suffix("RETURNING id")
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return "", err
	}
	row := queryer.QueryRowx(sqlstr, args...)
	if row.Err() != nil {
		return "", row.Err()
	}
	var id string
	if err := row.Scan(&id); err != nil {
		return "", err
	}
	return id, nil
}

func (dao *Dao) UpdateTraveler(id string, update map[string]any) error {
	if id == "" || update == nil {
		return nil
	}
	return updateTraveler(dao.getExecer(), dao.Db.PSQL, id, update)
}

func updateTraveler(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id string, update map[string]any) error {
	sql := sb.Update(travelerTable)
	for k, v := range update {
		switch k {
		case "surname", "given_name", "phone", "prefer_name", "residency", "marital_status", "work_email", "personal_email", "work_phone", "job_title", "occupation":
			sql = sql.Set(k, v)
		case "social_network", "home_address", "resident_info", "travel_history":
			byt, err := json.Marshal(v)
			if err != nil {
				return err
			}
			sql = sql.Set(k, byt)
		default:
			// do nothing
		}
	}
	sql = sql.Set("updated_at", time.Now().UTC())
	sql = sql.Where(squirrel.Eq{"id": id})

	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return nil
	}
	_, err = execer.Exec(sqlStr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *Dao) DeleteTraveler(id string) error {
	if id == "" {
		return nil
	}
	return deleteTraveler(dao.getExecer(), dao.Db.PSQL, id)
}

func deleteTraveler(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id string) error {
	sqlstr, args, err := sb.Delete(travelerTable).Where(squirrel.Eq{"id": id}).ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *Dao) QueryTravelersWithType(userID string, profileType string) ([]*models.Traveler, error) {
	res, err := queryTravelersWithType(dao.getQueryer(), dao.Db.PSQL, userID, profileType)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res, nil
}

func queryTravelersWithType(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, userID string, profileType string) ([]*models.Traveler, error) {
	var sql = sb.Select("t.*").From("traveler as t").LeftJoin("traveler_type_index as i on t.id=i.traveler_id").Where("t.user_id = ?", userID)
	if profileType != "" {
		sql = sql.Where("i.profile_type = ?", profileType)
	} else {
		sql = sql.Where("i.profile_type is null")
	}
	sql = sql.OrderBy("updated_at DESC")
	sqlstr, args, err := sql.ToSql()

	if err != nil && err != sqllib.ErrNoRows {
		return nil, err
	}

	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var res []*models.Traveler
	for rows.Next() {
		var item models.Traveler
		if err = rows.StructScan(&item); err != nil {
			return nil, err
		}
		res = append(res, &item)
	}
	return res, nil
}
