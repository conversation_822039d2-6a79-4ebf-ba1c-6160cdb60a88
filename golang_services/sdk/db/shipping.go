package db

import (
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

type ShippingData interface {
	QueryShippingRequirements(productType string, productID int) (*models.ProductShippingRequirements, error)
}

type ShippingDataDao struct {
	db *AuroraDB
}

func NewShippingDataDao(db *AuroraDB) *ShippingDataDao {
	return &ShippingDataDao{db: db}
}

func (s *ShippingDataDao) QueryShippingRequirements(productType string, productID int) (*models.ProductShippingRequirements, error) {
	id := fmt.Sprintf("%s-%d", productType, productID)
	sqlstr, args, err := s.db.PSQL.Select("id", "requirements", "created_at", "updated_at").
		From("shipping_requirements").
		Where(squirrel.Eq{"id": id}).
		ToSql()
	if err != nil {
		return nil, err
	}
	row := s.db.Db.QueryRowx(sqlstr, args...)
	if row.Err() != nil {
		return nil, err
	}
	var res models.ProductShippingRequirements
	if err = row.StructScan(&res); err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return &res, nil
}
