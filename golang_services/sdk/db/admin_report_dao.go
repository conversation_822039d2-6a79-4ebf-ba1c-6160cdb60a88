package db

import (
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/thoas/go-funk"
)

func (dao *Dao) GetReportList(req models.AdminReportReq) (*models.AdminReportQueryRes, error) {
	query := dao.Db.PSQL.Select("id", "report_from", "report_to", "service", "report_file", "created_at").
		From("admin_report")

	if req.Limit > 0 {
		query = query.Limit(req.Limit)
	}

	if req.Offset > 0 {
		query = query.Offset(req.Offset)
	}

	if funk.ContainsString([]string{"report_number", "report_from", "report_to", "created_at"}, req.SortField) {
		if strings.ToLower(req.SortOrder) != "desc" {
			req.SortOrder = "asc"
		}
		query = query.OrderBy(req.<PERSON>rt<PERSON>ield + " " + req.SortOrder)
	} else {
		query = query.OrderBy("id DESC")
	}

	sql, args, _ := query.ToSql()
	rows, err := dao.Db.Db.Queryx(sql, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	result := &models.AdminReportQueryRes{
		Data:    []models.AdminReport{},
		Success: true,
		Offset:  req.Offset,
		Limit:   req.Limit,
	}
	for rows.Next() {
		var item models.AdminReport
		if err = rows.Scan(
			&item.ID,
			&item.From,
			&item.To,
			&item.Service,
			&item.ReportFile,
			&item.CreatedAt,
		); err != nil {
			return nil, err
		}
		result.Data = append(result.Data, item)
	}

	sqlCount := utils.QueryCountItems(sql)
	if err := dao.Db.Db.Get(&result.Total, sqlCount, args...); err != nil {
		return nil, err
	}
	return result, nil
}

func (dao *Dao) DeleteReport(id int) error {
	query := dao.Db.PSQL.Delete("admin_report").Where("id = ?", id)
	_, err := query.RunWith(dao.Db.Db).Exec()
	return err
}
