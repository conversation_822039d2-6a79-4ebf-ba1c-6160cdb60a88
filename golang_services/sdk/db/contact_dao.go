package db

//
//import (
//	"github.com/Masterminds/squirrel"
//
//	"bitbucket.org/persistence17/aria/golang_services/models"
//)
//
//const (
//	contactTable = "contact"
//)
//
//var (
//	contactColmns = []string{
//		"id",
//		"given_name",
//		"surname",
//		"phone",
//		"email",
//		"secondary_phone",
//		"secondary_email",
//		"website_url",
//	}
//)
//
//func (dao *Dao) InsertContact(contact *models.Contact) (int, error) {
//	if contact == nil {
//		return 0, nil
//	}
//	sql := dao.Db.PSQL.Insert(contactTable).
//		Columns("given_name", "surname", "phone", "email", "secondary_phone", "secondary_email", "website_url").
//		Values(contact.GivenName, contact.Surname, contact.Phone, contact.Email, contact.SecondaryPhone,
//			contact.SecondaryEmail, contact.WebsiteUrl).
//		Suffix("RETURNING id")
//	sqlstr, args, err := sql.ToSql()
//	if err != nil {
//		return 0, err
//	}
//	row := dao.getQueryer().QueryRowx(sqlstr, args...)
//	if row.Err() != nil {
//		return 0, row.Err()
//	}
//	var id int
//	if err = row.Scan(&id); err != nil {
//		return 0, err
//	}
//	return id, nil
//}
//
//func (dao *Dao) GetContactByID(id int) (*models.Contact, error) {
//	if id <= 0 {
//		return nil, nil
//	}
//	sql := dao.Db.PSQL.Select(contactColmns...).From(contactTable).Where(squirrel.Eq{"id": id})
//	sqlstr, args, err := sql.ToSql()
//	if err != nil {
//		return nil, err
//	}
//	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
//	if err != nil {
//		return nil, err
//	}
//	defer rows.Close()
//	var res models.Contact
//	if rows.Next() {
//		if err = rows.StructScan(&res); err != nil {
//			return nil, err
//		}
//	}
//	return &res, nil
//}
//
//func (dao *Dao) UpdateContact(id int, update *models.Contact) error {
//	if update == nil {
//		return nil
//	}
//	sql := dao.Db.PSQL.Update(contactTable).SetMap(map[string]any{
//		"given_name":      update.GivenName,
//		"surname":         update.Surname,
//		"phone":           update.Phone,
//		"email":           update.Email,
//		"secondary_phone": update.SecondaryPhone,
//		"secondary_email": update.SecondaryEmail,
//		"website_url":     update.WebsiteUrl,
//	}).Where(squirrel.Eq{"id": id})
//
//	sqlstr, args, err := sql.ToSql()
//	if err != nil {
//		return err
//	}
//	if _, err = dao.getExecer().Exec(sqlstr, args...); err != nil {
//		return err
//	}
//	return nil
//}
