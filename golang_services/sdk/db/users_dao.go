package db

import (
	"errors"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
)

const (
	userTable   = "users"
	userColumns = "id,username,surname,given_name,surname_in_native,given_name_in_native,avatar,role,organization_id,email,password,status,email_verified,app_language,staff_contact,auto_assigned_order,created_at,updated_at"
)

func (dao *Dao) InsertUser(user *models.User) (*string, error) {
	if user == nil {
		return nil, nil
	}
	return insertUser(dao.getQueryer(), dao.Db.PSQL, user)
}

func insertUser(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, user *models.User) (*string, error) {
	sql := sb.Insert(userTable)
	sql = sql.SetMap(map[string]any{
		"id":             user.ID,
		"username":       user.Username,
		"given_name":     user.GivenName,
		"surname":        user.Surname,
		"avatar":         user.Avatar,
		"role":           user.Role,
		"email":          user.Email,
		"password":       user.Password,
		"status":         user.Status,
		"email_verified": user.EmailVerified,
		"updated_at":     time.Now(),
	}).Suffix("RETURNING id")

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	res := queryer.QueryRowx(sqlstr, args...)
	if res == nil {
		return nil, errors.New("failed to insert visa, returning empty")
	}
	var id string
	if err = res.Scan(&id); err != nil {
		return nil, err
	}
	return &id, nil
}

func (dao *Dao) GetUserByID(id string) (*models.User, error) {
	sql := dao.Db.PSQL.Select(userColumns).From(userTable).Where(squirrel.Eq{"id": id})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	queryer := dao.getQueryer()
	row, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer row.Close()
	var u models.User
	if row.Next() {
		if err = row.StructScan(&u); err != nil {
			return nil, err
		}
	}
	return &u, nil
}

func (dao *Dao) GetUserByUnderOrganizationID(orgID int64) ([]models.User, error) {
	sql := dao.Db.PSQL.Select(userColumns).From(userTable).Where(squirrel.Eq{"organization_id": orgID})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	queryer := dao.getQueryer()
	row, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer row.Close()
	result := []models.User{}
	for row.Next() {
		var u models.User
		if err = row.StructScan(&u); err != nil {
			return nil, err
		}
		result = append(result, u)
	}
	return result, nil
}

func (dao *Dao) GetUserByEmail(email string) (*models.User, error) {
	sql := dao.Db.PSQL.Select(userColumns).From(userTable).Where(squirrel.Eq{"email": email})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	queryer := dao.getQueryer()
	row, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer row.Close()
	var u models.User
	if row.Next() {
		if err = row.StructScan(&u); err != nil {
			return nil, err
		}
	}
	return &u, nil
}
