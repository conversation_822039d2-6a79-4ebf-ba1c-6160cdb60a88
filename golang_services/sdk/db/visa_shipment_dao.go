package db

import (
	"github.com/Masterminds/squirrel"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

const (
	visaShipmentTable = "visa_shipment"
)

var (
	visaShipmentColmns = []string{
		"id",
		"recipient_full_name",
		"shipping_contact",
		"shipping_address",
		"pickup_at_consulate",
		"shipping_carrier",
		"consulate_code",
		"properties",
		"labels",
	}
)

func (dao *Dao) GetVisaShipment(id string) (*models.VisaShipment, error) {
	sql := dao.Db.PSQL.Select(visaShipmentColmns...).From(visaShipmentTable).Where(squirrel.Eq{"id": id})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	queryer := dao.getQueryer()
	row, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer row.Close()
	var shipment models.VisaShipment
	if row.Next() {
		if err = row.StructScan(&shipment); err != nil {
			return nil, err
		}
	}
	return &shipment, nil
}

func (dao *Dao) InsertVisaShipment(spmt *models.VisaShipment) (string, error) {
	if spmt == nil || spmt.ID == "" {
		return "", nil
	}
	sql := dao.Db.PSQL.Insert(visaShipmentTable).SetMap(map[string]any{
		"id":                  spmt.ID,
		"shipping_carrier":    spmt.ShippingCarrier,
		"consulate_code":      spmt.ConsulateCode,
		"pickup_at_consulate": spmt.PickupAtConsulate,
		"recipient_full_name": spmt.RecipientFullName,
		"shipping_address":    spmt.ShippingAddress,
		"shipping_contact":    spmt.ShippingContact,
		"properties":          spmt.Properties,
		// "labels":                spmt.Labels,
	})

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return "", err
	}

	_, err = dao.getExecer().Exec(sqlstr, args...)
	if err != nil {
		return "", err
	}
	return spmt.ID, nil
}

func (dao *Dao) UpdateVisaShipment(id string, update *models.VisaShipment) error {
	if update == nil {
		return nil
	}
	sql := dao.Db.PSQL.Update(visaShipmentTable).SetMap(map[string]any{
		"recipient_full_name": update.RecipientFullName,
		"pickup_at_consulate": update.PickupAtConsulate,
		"shipping_carrier":    update.ShippingCarrier,
		"consulate_code":      update.ConsulateCode,
		"properties":          update.Properties,
		"shipping_address":    update.ShippingAddress,
		"shipping_contact":    update.ShippingContact,
		"labels":              update.Labels,
	}).Where(squirrel.Eq{"id": id})

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return err
	}
	if _, err = dao.getExecer().Exec(sqlstr, args...); err != nil {
		return err
	}
	return nil
}
