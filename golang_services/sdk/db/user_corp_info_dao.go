package db

import (
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

const (
	userCorpTable = "user_corp_info"
)

type UserCorpInfoDao interface {
	GetUserCorpInfo(userIDs []string) ([]*models.UserCorpInfo, error)
	CreateOrUpdateUserCorpInfo(info *models.UserCorpInfo) error
	UpdateUserCorpInfo(updated *models.UserCorpInfo) error

	Begin() error
	Commit() error
	Rollback() error
}

type PGUserCorpInfoDao struct {
	Db *AuroraDB
	tx *sqlx.Tx
}

func (d *PGUserCorpInfoDao) Begin() error {
	if d.tx != nil {
		return nil
	}
	tx, err := d.Db.Db.Beginx()
	if err != nil {
		return err
	}
	d.tx = tx
	return nil
}

func (d *PGUserCorpInfoDao) Commit() error {
	if d.tx == nil {
		return nil
	}
	if err := d.tx.Commit(); err != nil {
		return err
	}
	d.tx = nil
	return nil
}

func (d *PGUserCorpInfoDao) Rollback() error {
	if d.tx == nil {
		return nil
	}
	if err := d.tx.Rollback(); err != nil {
		return err
	}
	d.tx = nil
	return nil
}

func (d *PGUserCorpInfoDao) GetUserCorpInfo(userIDs []string) ([]*models.UserCorpInfo, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}
	sql := d.Db.PSQL.Select(`uc.user_id, uc.corporation_name, uc.corporation_address, uc.phone,
		uc.corporation_code, uc.department_id,cd.name, uc.cost_center_id, cc.name, 
		uc.manager_id, jsonb_build_object('name',CONCAT_WS(' ',m_u.given_name,m_u.surname),'email',m_u.email,'phone_number',m_uc.phone),
		uc.hr_contact_id, jsonb_build_object('name',CONCAT_WS(' ',hr_u.given_name,hr_u.surname),'email',hr_u.email,'phone_number',hr_uc.phone),
		uc.approved_countries, uc.created_at, uc.updated_at`).
		From("user_corp_info uc").
		LeftJoin("corporation_departments cd ON cd.id = uc.department_id").
		LeftJoin("corporation_cost_centers cc ON cc.id = uc.cost_center_id").
		LeftJoin("users m_u ON m_u.id = uc.manager_id").
		LeftJoin("user_corp_info m_uc ON m_uc.user_id = m_u.id").
		LeftJoin("users hr_u ON hr_u.id = uc.hr_contact_id").
		LeftJoin("user_corp_info hr_uc ON hr_uc.user_id = hr_u.id").
		Where(squirrel.Eq{"uc.user_id": userIDs})

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := d.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.UserCorpInfo
	for rows.Next() {
		var info models.UserCorpInfo
		if err := rows.Scan(
			&info.UserID,
			&info.CorporationName,
			&info.CorporationAddress,
			&info.Phone,
			&info.CorporationCode,
			&info.DepartmentID,
			&info.DepartmentName,
			&info.CostCenterID,
			&info.CostCenterName,
			&info.ManagerID,
			&info.Manager,
			&info.HRContactID,
			&info.HRContact,
			&info.ApprovedCountries,
			&info.CreatedAt,
			&info.UpdatedAt,
		); err != nil {
			return nil, err
		}
		res = append(res, &info)
	}
	return res, nil
}

func (d *PGUserCorpInfoDao) CreateOrUpdateUserCorpInfo(info *models.UserCorpInfo) error {
	if info.UserID == "" {
		return fmt.Errorf("user ID cannot be empty")
	}
	sqlstr, args, err := d.Db.PSQL.Insert(userCorpTable).SetMap(map[string]any{
		"user_id":             info.UserID,
		"corporation_name":    info.CorporationName,
		"corporation_address": info.CorporationAddress,
		"phone":               info.Phone,
		"manager_id":          info.ManagerID,
		"hr_contact_id":       info.HRContactID,
		"department_id":       info.DepartmentID,
		"cost_center_id":      info.CostCenterID,
		"corporation_code":    info.CorporationCode,
	}).Suffix("ON CONFLICT ON CONSTRAINT user_corp_info_pkey DO UPDATE SET corporation_name=?, corporation_address=?, phone=?, manager_id=?, hr_contact_id=?, department_id=?, cost_center_id=?, corporation_code=?, updated_at=?",
		info.CorporationName, info.CorporationAddress, info.Phone, info.ManagerID, info.HRContactID, info.DepartmentID, info.CostCenterID, info.CorporationCode, time.Now()).ToSql()
	if err != nil {
		return err
	}
	_, err = d.getExecer().Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (d *PGUserCorpInfoDao) UpdateUserCorpInfo(updated *models.UserCorpInfo) error {
	if updated.UserID == "" {
		return nil
	}
	sqlstr, args, err := d.Db.PSQL.Update(userCorpTable).SetMap(map[string]any{
		"corporation_name":    updated.CorporationName,
		"corporation_address": updated.CorporationAddress,
		"phone":               updated.Phone,
		"manager_id":          updated.ManagerID,
		"hr_contact_id":       updated.HRContactID,
		"department_id":       updated.DepartmentID,
		"cost_center_id":      updated.CostCenterID,
		"corporation_code":    updated.CorporationCode,
		"updated_at":          time.Now(),
	}).Where(squirrel.Eq{"user_id": updated.UserID}).ToSql()
	if err != nil {
		return err
	}
	_, err = d.getExecer().Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (d *PGUserCorpInfoDao) getQueryer() sqlx.Queryer {
	if d.tx != nil {
		return d.tx
	}
	return d.Db.Db
}

func (d *PGUserCorpInfoDao) getExecer() sqlx.Execer {
	if d.tx != nil {
		return d.tx
	}
	return d.Db.Db
}
