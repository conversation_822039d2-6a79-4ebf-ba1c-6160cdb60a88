package db

import (
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

const (
	visaCheckTable = "visa_check"
)

var (
	visaCheckColumns = []string{
		"from_country_alpha3",
		"to_country_alpha3",
		"requirement",
	}
)

func (dao *Dao) QueryVisaChecks(q map[string][]any) ([]*models.VisaCheck, error) {
	return queryVisaChecks(dao.getQueryer(), dao.Db.PSQL, q)
}

func queryVisaChecks(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any) ([]*models.VisaCheck, error) {
	sql := sb.Select(visaCheckColumns...).From(visaCheckTable)
	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.VisaCheck
	for rows.Next() {
		var p models.VisaCheck
		if err = rows.StructScan(&p); err != nil {
			return nil, err
		}
		res = append(res, &p)
	}

	return res, nil
}
