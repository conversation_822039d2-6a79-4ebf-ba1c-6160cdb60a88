package db

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func buildGroupItemV2(query squirrel.SelectBuilder, queryTypes []string) squirrel.SelectBuilder {
	subQueryETS := squirrel.StatementBuilder.Select(`ets.service_type, ets.tasks service_task, ets.country, so.id service_id, so.user_id, so.org_id, COALESCE(so.updated_at,so.created_at) last_updated, so.submitted_time, so.order_time, so.service_id visa_product_id, so.processing_time_expired_at, so.status, so.provider_id, so.config, so.tracking_times,
	coalesce(so.tracking_times ->> 'cancelled', so.tracking_times ->> 'completed', so.tracking_times ->> 'sent_visa_package', so.tracking_times ->> 'completed') as completed_time,
	coalesce(so.tracking_times ->> 'in_delivery', so.tracking_times ->> 'previewed') as approved_time, to_jsonb(ets) product, vfo.service_date fastlane_service_date, vfo.service_flight_number fastlane_flight_number,
	to_jsonb(payment) payment, to_jsonb(u) creator, to_jsonb(ep) provider`).
		From("service_orders so").
		LeftJoin("ets ON ets.id = so.service_id").
		LeftJoin("v_ets_actions vea ON vea.order_id = so.id").
		LeftJoin("v_ets_tabs vet ON vet.id = so.id").
		LeftJoin("v_fastlane_orders vfo ON vfo.id = so.id").
		LeftJoin("payment ON payment.id = so.payment_id").
		LeftJoin("users u ON u.id = so.user_id").
		LeftJoin("ets_provider ep ON ep.id = so.provider_id").
		Where("so.deleted_at IS NULL")

	// https://ad-app-version.s3.us-west-2.amazonaws.com/app-passport-config/admin_process.json
	etsConditions := []squirrel.Sqlizer{}
	for _, queryType := range queryTypes {
		etsConditions = append(etsConditions, squirrel.Expr(fmt.Sprintf("vet.%q = true", queryType)))
	}

	if len(etsConditions) > 0 {
		subQueryETS = subQueryETS.Where(squirrel.Or(etsConditions))
	}

	sqlFirst, _, _ := subQueryETS.ToSql()
	return query.From(fmt.Sprintf("(%s) v", sqlFirst))
}

// GetGroupItems get group item list
func (dao *Dao) GetGroupItems(req models.CartGroupItemRequest) (*models.CartGroupItemResponse, error) {
	now := time.Now()
	query := dao.Db.PSQL.Select("service_type, service_id, service_task, country, last_updated, submitted_time, order_time, processing_time_expired_at, fastlane_service_date")

	if len(req.Types) == 0 {
		req.Types = req.Headers
	}

	if strings.ToUpper(req.SortOrder) == "DESC" {
		req.SortOrder = "DESC NULLS LAST"
	} else {
		req.SortOrder = "ASC NULLS LAST"
	}

	query = buildGroupItemV2(query, req.Types)

	if val, ok := map[string]string{
		"region_of_residence":         "config->'region_of_residence'::text",
		"service_id":                  "service_id",
		"service_type":                "service_type",
		"service_task":                "service_task",
		"country":                     "country",
		"submitted_time":              "submitted_time",
		"order_time":                  "order_time",
		"status":                      "status",
		"processing_time_expired_at":  "processing_time_expired_at",
		"completed_time":              "completed_time",
		"approved_time":               "approved_time",
		"service_airport":             "product->'airport'::text",
		"service_tasks":               "product->'tasks'::text",
		"service_attributes_terminal": "product->'attributes'->>'terminal'::text",
		"fastlane_service_date":       "fastlane_service_date",
		"fastlane_flight_number":      "fastlane_flight_number",
		"creator_name":                `concat_ws(' ', creator->>'given_name'::text, creator->>'surname'::text)`,
		"provider_name":               `provider->>'name'::text`,
	}[req.SortField]; ok {
		req.SortField = val
		query = query.OrderBy(fmt.Sprintf("%s %s", req.SortField, req.SortOrder))
	} else if funk.ContainsString(req.Types, "need-action") || funk.ContainsString(req.Types, "consulate-in-process") {
		query = query.OrderBy("processing_time_expired_at ASC")
	} else {
		query = query.OrderBy("last_updated DESC")
	}

	if req.UserID != "" {
		query = query.Where("(user_id = ? OR service_id IN (SELECT order_id FROM service_order_staffs WHERE staff_id = ? AND staff_confirm_status != 'rejected'))", req.UserID, req.UserID)
	}

	if len(req.ServiceType) > 0 {
		query = query.Where(squirrel.Eq{"service_type": req.ServiceType})
	}

	if req.From != nil && req.To != nil {
		query = query.Where("order_time BETWEEN ? AND ?", req.From, req.To)
	}

	if len(req.ProviderID) > 0 {
		query = query.Where(squirrel.Eq{"provider_id": req.ProviderID})
	}

	if len(req.ProviderIDs) > 0 {
		query = query.Where(squirrel.Eq{"provider_id": req.ProviderIDs})
	}

	if len(req.Status) > 0 {
		query = query.Where(squirrel.Eq{"status": req.Status})
	}

	if len(req.Country) > 0 {
		query = query.Where(squirrel.Eq{"country": req.Country})
	}

	if len(req.OrderIDs) > 0 {
		query = query.Where(squirrel.Eq{"service_id": req.OrderIDs})
	}

	if len(req.ServiceTask) > 0 {
		query = query.Where(squirrel.Eq{"service_task": req.ServiceTask})
	}

	if len(req.PaymentStatus) > 0 {
		query = query.Where(squirrel.Eq{"payment->>'status'::text": req.PaymentStatus})
	}

	if req.StaffID != "" {
		query = query.Where("service_id IN (SELECT order_id FROM service_order_staffs WHERE staff_id = ? AND staff_confirm_status != 'rejected')", req.StaffID)
	}

	if req.Query != "" {
		query = query.Where(squirrel.Or{
			squirrel.Expr("service_id::text ILIKE ?", "%"+req.Query+"%"),
			squirrel.Expr("service_type ILIKE ?", "%"+req.Query+"%"),
			squirrel.Expr("service_task ILIKE ?", "%"+req.Query+"%"),
			squirrel.Expr(`(service_type != 'visa' AND EXISTS (
				select * from (select jsonb_array_elements(input_pods) m FROM service_tasks WHERE order_id = service_id) v
				where m->>'name'::text IN ('given_name', 'surname','passenger_name_list','welcome_name','passport_number') AND m->'value'->>'fe'::text ILIKE ?))`, "%"+req.Query+"%"),
		})
	}

	if req.OrgID > 0 {
		query = query.Where("org_id = ?", req.OrgID)
	}

	if len(req.CartItemID) > 0 {
		query = query.Where(squirrel.Eq{"current_cart->'cart_item'->>'id'": req.CartItemID})
	}

	// No pagination for cart items
	if !funk.ContainsString(req.Types, "in-cart") {
		query = query.Limit(req.Limit).Offset(req.Offset)
	}

	sqlStr, args, _ := query.ToSql()
	rows, err := dao.getQueryer().Queryx(sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// Get data
	var res = models.CartGroupItemResponse{
		Data:   []models.CartGroupItem{},
		Limit:  req.Limit,
		Offset: req.Offset,
	}

	for rows.Next() {
		var p models.CartGroupItem
		if err := rows.StructScan(&p); err != nil {
			return nil, err
		}
		res.Data = append(res.Data, p)
	}

	fmt.Println("CartGroupItemResponse: ", time.Since(now))
	var (
		etsIDs []int
	)
	for _, item := range res.Data {
		etsIDs = append(etsIDs, item.ServiceID)
	}

	now = time.Now()

	etsQuery, err := NewEtsDao(dao.Db).QueryServiceOrders(models.ServiceOrderFilter{
		IDs:             etsIDs,
		IncludeTasks:    true,
		IncludeService:  true,
		IncludePayment:  true,
		IncludePrice:    true,
		IncludeShipment: true,
		IncludeCreator:  true,
		SkipPagination:  true,
		IncludeProvider: true,
		Limit:           uint64(len(etsIDs)),
	})
	if err != nil {
		return nil, err
	}
	fmt.Println("QueryServiceOrders: ", time.Since(now))
	now = time.Now()

	etsMap := funk.ToMap(etsQuery.Data, "ID").(map[int]*models.ServiceOrderDetail)

	for i, item := range res.Data {
		if item.ServiceType == "fastlane" || item.ServiceType == "country_tourist" {
			if len(etsMap[item.ServiceID].Tasks) > 0 {
				inputPodJ := utils.StructToJSON(etsMap[item.ServiceID].Tasks[0].InputPods.ToMapKeyValueV2(etsMap[item.ServiceID].InputPods))
				res.Data[i].NumberOfTraveler = int(inputPodJ.Get("travel_passenger_info_no_of_traveler").Int())
				if res.Data[i].NumberOfTraveler == 1 {
					names := []string{}
					if val := inputPodJ.Get("travel_traveler_contact_info_full_name").String(); val != "" {
						names = append(names, val)
					} else {
						if val := inputPodJ.Get("travel_traveler_contact_info_given_name").String(); val != "" {
							names = append(names, val) //given_name
						}

						if val := inputPodJ.Get("travel_traveler_contact_info_surname").String(); val != "" {
							names = append(names, val) //surname
						}
					}

					res.Data[i].DisplayName = strings.Join(names, " ") // given_name middle_name
				} else {
					res.Data[i].DisplayName = strconv.Itoa(res.Data[i].NumberOfTraveler)
				}
			}
			res.Data[i].Data = etsMap[item.ServiceID]
		} else if item.ServiceType == "passport" {
			res.Data[i].DisplayName = strconv.Itoa(len(etsMap[item.ServiceID].Tasks))
			res.Data[i].NumberOfTraveler = len(etsMap[item.ServiceID].Tasks)
			if len(etsMap[item.ServiceID].Tasks) == 1 {
				task := etsMap[item.ServiceID].Tasks[0]
				res.Data[i].DisplayName = task.GetAppName(item.ServiceType)
				if res.Data[i].DisplayName == "" {
					res.Data[i].DisplayName = "1"
				}
			}

			res.Data[i].Data = etsMap[item.ServiceID]
		} else if item.ServiceType == "global_entry" {
			res.Data[i].NumberOfTraveler = len(etsMap[item.ServiceID].Tasks)
			res.Data[i].DisplayName = strconv.Itoa(res.Data[i].NumberOfTraveler)
			if res.Data[i].NumberOfTraveler == 1 {
				task := etsMap[item.ServiceID].Tasks[0]
				res.Data[i].DisplayName = task.GetAppName(item.ServiceType)
				if res.Data[i].DisplayName == "" {
					res.Data[i].DisplayName = "1"
				}
			}
			res.Data[i].Data = etsMap[item.ServiceID]
		} else if item.ServiceType == "id_photo" {
			res.Data[i].NumberOfTraveler = 0
			for _, task := range etsMap[item.ServiceID].Tasks {
				if numberOfPhoto := cast.ToInt(task.InputPods.ToMapKeyValue()["service_core_info_number_of_set"]); numberOfPhoto > 0 {
					res.Data[i].NumberOfTraveler += numberOfPhoto
				}
			}
			if res.Data[i].NumberOfTraveler == 0 {
				res.Data[i].NumberOfTraveler = 1
			}
			res.Data[i].DisplayName = strconv.Itoa(res.Data[i].NumberOfTraveler)
			res.Data[i].Data = etsMap[item.ServiceID]
		} else if item.ServiceType == "tsa_precheck" {
			res.Data[i].NumberOfTraveler = len(etsMap[item.ServiceID].Tasks)
			res.Data[i].DisplayName = strconv.Itoa(res.Data[i].NumberOfTraveler)
			if res.Data[i].NumberOfTraveler == 1 {
				task := etsMap[item.ServiceID].Tasks[0]
				res.Data[i].DisplayName = task.GetAppName(item.ServiceType)
				if res.Data[i].DisplayName == "" {
					res.Data[i].DisplayName = "1"
				}
			}
			res.Data[i].Data = etsMap[item.ServiceID]
		} else if item.ServiceType == "sentri" {
			res.Data[i].NumberOfTraveler = len(etsMap[item.ServiceID].Tasks)
			res.Data[i].DisplayName = strconv.Itoa(res.Data[i].NumberOfTraveler)
			if res.Data[i].NumberOfTraveler == 1 {
				task := etsMap[item.ServiceID].Tasks[0]
				res.Data[i].DisplayName = task.GetAppName(item.ServiceType)
				if res.Data[i].DisplayName == "" {
					res.Data[i].DisplayName = strconv.Itoa(res.Data[i].NumberOfTraveler)
				}
			}
			res.Data[i].Data = etsMap[item.ServiceID]
		} else if item.ServiceType == "nexus" {
			res.Data[i].NumberOfTraveler = len(etsMap[item.ServiceID].Tasks)
			res.Data[i].DisplayName = strconv.Itoa(res.Data[i].NumberOfTraveler)
			if res.Data[i].NumberOfTraveler == 1 {
				task := etsMap[item.ServiceID].Tasks[0]
				res.Data[i].DisplayName = task.GetAppName(item.ServiceType)
				if res.Data[i].DisplayName == "" {
					res.Data[i].DisplayName = strconv.Itoa(res.Data[i].NumberOfTraveler)
				}
			}
			res.Data[i].Data = etsMap[item.ServiceID]
		} else if item.ServiceType == "fast_truck" {
			res.Data[i].NumberOfTraveler = len(etsMap[item.ServiceID].Tasks)
			res.Data[i].DisplayName = strconv.Itoa(res.Data[i].NumberOfTraveler)
			if res.Data[i].NumberOfTraveler == 1 {
				task := etsMap[item.ServiceID].Tasks[0]
				res.Data[i].DisplayName = task.GetAppName(item.ServiceType)
				if res.Data[i].DisplayName == "" {
					res.Data[i].DisplayName = strconv.Itoa(res.Data[i].NumberOfTraveler)
				}
			}
			res.Data[i].Data = etsMap[item.ServiceID]
		} else if item.ServiceType == "covid_test" {
			res.Data[i].NumberOfTraveler = len(etsMap[item.ServiceID].Tasks)
			res.Data[i].DisplayName = strconv.Itoa(res.Data[i].NumberOfTraveler)
			if res.Data[i].NumberOfTraveler == 1 {
				task := etsMap[item.ServiceID].Tasks[0]
				if val := cast.ToString(task.InputPods.ToMapKeyValue()["personal_core_info_full_name"]); val != "" {
					res.Data[i].DisplayName = val
				}
			}
			res.Data[i].Data = etsMap[item.ServiceID]
		} else {
			res.Data[i].NumberOfTraveler = len(etsMap[item.ServiceID].Tasks)
			res.Data[i].DisplayName = strconv.Itoa(res.Data[i].NumberOfTraveler)
			if res.Data[i].NumberOfTraveler == 1 {
				task := etsMap[item.ServiceID].Tasks[0]
				res.Data[i].DisplayName = task.GetAppName(item.ServiceType)
				if res.Data[i].DisplayName == "" {
					res.Data[i].DisplayName = strconv.Itoa(res.Data[i].NumberOfTraveler)
				}
			}
			res.Data[i].Data = etsMap[item.ServiceID]
		}

	}

	// Get total count
	sqlCount := utils.QueryCountItemV2(sqlStr)
	rowCount, err := dao.getQueryer().Queryx(sqlCount, args...)
	if err != nil {
		return nil, err
	}
	defer rowCount.Close()

	if rowCount.Next() {
		if err := rowCount.Scan(&res.TotalCount); err != nil {
			return nil, err
		}
	}
	fmt.Println(time.Since(now))
	res.Success = true

	return &res, nil
}

// GetGroupFilterItem: get group filter item from query search
func (dao *Dao) GetGroupFilterItem(req models.CartGroupItemRequest) (*models.GroupFilterItemResponse, error) {
	query := dao.Db.PSQL.Select("vgo.service_id, vgo.service_type, vgo.status, vgo.user_id, provider_id, org_id, vo.org_name, e.country, e.tasks as task").
		From("v_group_orders vgo").
		LeftJoin("v_organizations vo ON vo.profile = provider_id").
		LeftJoin("ets e on e.id = visa_product_id").
		OrderBy("order_time desc")

	if req.UserID != "" {
		query = query.Where("user_id = ?", req.UserID)
	}

	if len(req.ServiceType) > 0 {
		query = query.Where(squirrel.Eq{"vgo.service_type": req.ServiceType})
	}

	if req.From != nil && req.To != nil {
		query = query.Where("vgo.order_time BETWEEN ? AND ?", req.From, req.To)
	}

	if len(req.OrderIDs) > 0 {
		query = query.Where(squirrel.Eq{"vgo.service_id": req.OrderIDs})
	}

	if len(req.ProviderIDs) > 0 {
		query = query.Where(squirrel.Eq{"provider_id": req.ProviderIDs})
	}

	if len(req.ProviderID) > 0 {
		query = query.Where(squirrel.Eq{"provider_id": req.ProviderID})
	}

	if len(req.Status) > 0 {
		query = query.Where(squirrel.Eq{"vgo.status": req.Status})
	}

	if req.Query != "" {
		queryStr := "%" + req.Query + "%"
		query = query.Where(squirrel.Or{
			squirrel.Expr("vgo.service_id::text ILIKE ?", queryStr),
			squirrel.Expr("vgo.service_type ILIKE ?", queryStr),
			squirrel.Expr("vgo.service_task ILIKE ?", queryStr),
			squirrel.Expr(`(vgo.service_type = 'visa' AND EXISTS (SELECT * FROM application WHERE package_id = service_id AND CONCAT_WS(' ',given_name,surname) ILIKE ?))`, queryStr),
			squirrel.Expr(`(vgo.service_type != 'visa' AND EXISTS (
			select * from (select jsonb_array_elements(input_pods) m FROM service_tasks WHERE order_id = service_id) v
			where m->>'name'::text IN ('given_name', 'middle_name', 'surname') AND m->'value'->>'fe'::text ILIKE ?))`, queryStr),
			squirrel.Expr("provider_id IN (SELECT profile FROM v_organizations WHERE name ILIKE ?)", queryStr),
		})
	}

	sqlStr, args, _ := query.ToSql()
	rows, err := dao.Db.Db.Query(sqlStr, args...)
	if err != nil {
		return nil, err
	}

	var orders []models.GroupOrderItem
	for rows.Next() {
		order := models.GroupOrderItem{}
		if err := rows.Scan(&order.ID, &order.ServiceType, &order.Status, &order.UserID, &order.ProviderID, &order.OrgID, &order.ProviderName, &order.Country, &order.Task); err != nil {
			return nil, err
		}
		orders = append(orders, order)
	}

	// Get data
	var res = models.GroupFilterItemResponse{
		Data: []models.GroupFilter{},
	}

	var groupFilter []models.GroupFilter
	var itemStatusFilter []models.ItemFilter
	var itemServiceFilter []models.ItemFilter
	var itemProviderNameFilter []models.ItemFilter
	var itemCountryFilter []models.ItemFilter
	var itemTaskFilter []models.ItemFilter
	for _, order := range orders {
		if order.Status != "" {
			if !funk.Contains(itemStatusFilter, models.ItemFilter{Key: order.Status, Value: ""}) {
				itemStatusFilter = append(itemStatusFilter, models.ItemFilter{Key: order.Status, Value: ""})
			}
		}

		if order.ServiceType != "" {
			if !funk.Contains(itemServiceFilter, models.ItemFilter{Key: order.ServiceType, Value: ""}) {
				itemServiceFilter = append(itemServiceFilter, models.ItemFilter{Key: order.ServiceType, Value: ""})
			}
		}

		if order.Task.String != "" && !funk.ContainsString([]string{"covid"}, order.Task.String) {
			if !funk.Contains(itemTaskFilter, models.ItemFilter{Key: order.Task.String, Value: ""}) {
				itemTaskFilter = append(itemTaskFilter, models.ItemFilter{Key: order.Task.String, Value: ""})
			}
		}

		if order.ProviderName.String != "" {
			if !funk.Contains(itemProviderNameFilter, models.ItemFilter{Key: order.ProviderID.String, Value: order.ProviderName.String}) {
				itemProviderNameFilter = append(itemProviderNameFilter, models.ItemFilter{Key: order.ProviderID.String, Value: order.ProviderName.String})
			}
		}

		if order.Country.String != "" && !funk.ContainsString([]string{"tsa_precheck", "nexus", "sentri", "fast_truck", "global_entry"}, order.ServiceType) {
			if !funk.Contains(itemCountryFilter, models.ItemFilter{Key: order.Country.String, Value: ""}) {
				itemCountryFilter = append(itemCountryFilter, models.ItemFilter{Key: order.Country.String, Value: ""})
			}
		}
	}

	groupFilter = append(groupFilter, models.GroupFilter{Title: "status", Key: "status", Value: itemStatusFilter})
	groupFilter = append(groupFilter, models.GroupFilter{Title: "service", Key: "service_type", Value: itemServiceFilter})
	groupFilter = append(groupFilter, models.GroupFilter{Title: "task", Key: "task", Value: itemTaskFilter})
	groupFilter = append(groupFilter, models.GroupFilter{Title: "provider", Key: "provider_id", Value: itemProviderNameFilter})
	groupFilter = append(groupFilter, models.GroupFilter{Title: "country", Key: "country", Value: itemCountryFilter})

	res.Data = groupFilter
	res.Success = true
	return &res, nil
}

// GetGroupItemsHeader get group item list header
func (dao *Dao) GetGroupItemsHeader(req models.CartGroupItemRequest) (map[string]int, error) {

	result := map[string]int{}

	// count status for header
	for _, k := range req.Headers {
		if !funk.ContainsString([]string{"id", "complete", "submit", "submit-in-consulate", "submit-in-mobile", "consulate-in-process", "consulate-in-review", "in-cart", "need-action", "incomplete", "draft", "need-more-document", "reviewed", "consulate-not-process", "pending-payment", "need-submit-online", "need-watch-submit-online", "fastlane-submit", "cancel", "order-pending"}, k) {
			return nil, fmt.Errorf("invalid header: %s", k)
		}

		result[k] = 0 // Init value
		query := dao.Db.PSQL.Select("COUNT(*)")
		query = buildGroupItemV2(query, []string{k})

		if req.UserID != "" {
			query = query.Where("(user_id = ? OR service_id IN (SELECT order_id FROM service_order_staffs WHERE staff_id = ? AND staff_confirm_status != 'rejected'))", req.UserID, req.UserID)
		}

		if len(req.ServiceType) > 0 {
			query = query.Where(squirrel.Eq{"service_type": req.ServiceType})
		}

		if len(req.ProviderID) > 0 {
			query = query.Where(squirrel.Eq{"provider_id": req.ProviderID})
		}

		if len(req.ProviderIDs) > 0 {
			query = query.Where(squirrel.Eq{"provider_id": req.ProviderIDs})
		}

		if req.OrgID > 0 {
			query = query.Where("org_id = ?", req.OrgID)
		}

		if req.From != nil && req.To != nil {
			query = query.Where("order_time BETWEEN ? AND ?", req.From, req.To)
		}

		if req.StaffID != "" {
			query = query.Where("service_id IN (SELECT order_id FROM service_order_staffs WHERE staff_id = ? AND staff_confirm_status != 'rejected')", req.StaffID)
		}

		sqlStr, args, _ := query.ToSql()
		rows, err := dao.getQueryer().Queryx(sqlStr, args...)
		if err != nil {
			return nil, err
		}
		defer rows.Close()
		if rows.Next() {
			var count int
			if err := rows.Scan(&count); err != nil {
				return nil, err
			}
			result[k] = count
		}
	}

	all := 0
	for _, v := range result {
		all += v
	}
	result["all"] = all
	return result, nil
}

type queryOptions struct {
	IncludeApps    bool
	IncludePrice   bool
	IncludeCreator bool
}
