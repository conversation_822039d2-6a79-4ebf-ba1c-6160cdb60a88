package db

import (
	sqllib "database/sql"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

var (
	shippingServiceCols = []string{
		"id",
		"status",
		"carrier",
		"service",
		"price",
		"currency",
		"shipping_time",
		"created_at",
		"updated_at",
	}
	shippingServiceTable = "shipping_service"
)

func (dao *Dao) GetShippingServiceByID(id int64) (*models.ShippingService, error) {
	sql := dao.Db.PSQL.Select(shippingServiceCols...).From(shippingServiceTable).Where(squirrel.Eq{"id": id})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	queryer := dao.getQueryer()
	row, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer row.Close()
	var u models.ShippingService
	if row.Next() {
		if err = row.StructScan(&u); err != nil {
			return nil, err
		}
	}
	return &u, nil
}

func (dao *Dao) GetShippingServices(q map[string][]any) ([]*models.ShippingService, error) {
	queryer := dao.getQueryer()
	return queryShippingServices(queryer, dao.Db.PSQL, q)
}

func queryShippingServices(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any) ([]*models.ShippingService, error) {
	sql := sb.Select(shippingServiceCols...).From(shippingServiceTable)
	for k, v := range q {
		switch k {
		case "id", "status", "carrier", "service", "price", "currency", "shipping_time":
			sql = sql.Where(squirrel.Eq{k: v})
		default:
			// ignore
		}
	}
	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := queryer.Queryx(sqlStr, args...)
	if err != nil {
		if err == sqllib.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	defer rows.Close()
	var res []*models.ShippingService
	for rows.Next() {
		var item models.ShippingService
		if err := rows.StructScan(&item); err != nil {
			return nil, err
		}
		res = append(res, &item)
	}
	return res, nil
}
