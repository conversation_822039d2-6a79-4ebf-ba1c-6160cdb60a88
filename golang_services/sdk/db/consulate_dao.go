package db

import (
	"fmt"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
)

const (
	consulateTable = "consulate"
)

var (
	consulateColumns = []string{
		"id",
		"status",
		"name",
		"email",
		"secondary_email",
		"country",
		"address",
		"contact",
		"secondary_contact",
		"timezone_name",
		"support_pickup",
		"support_shipping",
		"supported_visa_product",
		"website",
		"served_countries",
		"served_area",
		"org_id",
		"created_at",
		"updated_at",
	}
)

func (dao *Dao) GetConsulateByID(id string) (*models.Consulate, error) {
	if id == "" {
		return nil, nil
	}
	q := map[string][]any{"id": {id}}

	res, err := queryConsulates(dao.getQueryer(), dao.Db.PSQL, q)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

func (dao *Dao) GetConsulateByOrgID(id int64) (*models.Consulate, error) {
	if id <= 0 {
		return nil, nil
	}
	q := map[string][]any{"org_id": {id}}

	res, err := queryConsulates(dao.getQueryer(), dao.Db.PSQL, q)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

func queryConsulates(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any) ([]*models.Consulate, error) {
	sql := sb.Select(consulateColumns...).From(consulateTable)

	for k, v := range q {
		switch k {
		case "served_countries", "served_area":
			sql = sql.Where(squirrel.Like{k: fmt.Sprintf("%%%s%%", v[0])})
		default:
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}
	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := queryer.Queryx(sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.Consulate
	for rows.Next() {
		var p models.Consulate
		if err = rows.StructScan(&p); err != nil {
			return nil, err
		}
		res = append(res, &p)
	}
	return res, nil
}

func (dao *Dao) QueryConsulates(q map[string][]any) ([]*models.Consulate, error) {
	queryer := dao.getQueryer()
	return queryConsulates(queryer, dao.Db.PSQL, q)
}
