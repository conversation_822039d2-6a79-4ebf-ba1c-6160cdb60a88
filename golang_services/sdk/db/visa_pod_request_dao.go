package db

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/types"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/bluele/gcache"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

const (
	visaPodRequestTable = "visa_pod_requests"
)

var (
	visaPodRequestColumns = []string{"id", "request_type", "pod_id", "version", "pre_pod", "exp_pod", "reason", "reviewer_comment", "user_id", "status", "created_at", "updated_at"}
)

// FetchNewPodID fetch new visa pod id
// func (dao *Dao) FetchNewPodID(p *models.PodExtSchema) error {
// 	if p == nil {
// 		return nil
// 	}

// 	// Get max id from pods by category
// 	if err := dao.Db.Db.Get(&p.ID, "SELECT coalesce(MAX(id),0) FROM visa_pods WHERE category = $1", p.Category); err != nil {
// 		return err
// 	}

// 	// Get max id from pod requests
// 	var requestPodID string
// 	if err := dao.Db.Db.Get(&requestPodID, "SELECT coalesce(MAX(pod_id),0) FROM visa_pod_requests WHERE pre_pod->>'category'= $1 OR exp_pod->>'category'= $1", p.Category); err != nil {
// 		return err
// 	}

// 	if requestPodID != p.ID {
// 		p.ID = requestPodID
// 	}

// 	// New category
// 	if p.ID == 0 {
// 		// Get max id from pods
// 		if err := dao.Db.Db.Get(&p.ID, "SELECT coalesce(MAX(id),0) FROM visa_pods"); err != nil {
// 			return err
// 		}
// 		p.ID = (p.ID/1000 + 1) * 1000
// 	}

// 	p.ID++ // new ID

// 	return nil
// }

// FetchNewPodOrder fetch new visa pod order
func (dao *Dao) FetchNewPodOrder(p *models.PodExtSchema) error {
	if p == nil {
		return nil
	}

	// Get max order from pods by category
	if err := dao.Db.Db.Get(&p.Order, `SELECT coalesce(MAX("order"),0) FROM visa_pods WHERE category = $1`, p.Category); err != nil {
		return err
	}

	// New category
	if p.Order == 0 {
		// Get max order from pods
		if err := dao.Db.Db.Get(&p.Order, `SELECT coalesce(MAX("order"),0) FROM visa_pods`); err != nil {
			return err
		}
		p.Order = (p.Order/1000 + 1) * 1000
	}

	p.Order++ // new Order

	return nil
}

// InsertVisaPodRequest add new visa pod request
func (dao *Dao) InsertVisaPodRequest(p *models.VisaPodRequest) error {
	if p == nil {
		return nil
	}

	var visaPod models.PodExtSchema
	switch err := dao.Db.Db.Get(&visaPod, "SELECT * FROM visa_pods WHERE id = $1", p.PodID); err {
	case sql.ErrNoRows:
		p.PrePodData = nil
	case nil:
		if buff, err := json.Marshal(visaPod); err == nil {
			p.PrePodData = &types.ExtJSON{
				Data: buff,
			}
		}
	default:
		return err
	}

	if !p.IsDataUpdated() {
		return fmt.Errorf("Pod data not change, please check it! ")
	}

	query := dao.Db.PSQL.Insert(visaPodRequestTable).
		Columns("pod_id", "request_type", "version", "pre_pod", "exp_pod", "reason", "user_id", "status").
		Values(p.PodID, p.RequestType, p.Version, p.PrePodData, p.ExpPodData, p.Reason, p.UserID, p.Status).
		Suffix("RETURNING id, created_at")
	sqlStr, args, err := query.ToSql()
	if err != nil {
		return err
	}

	return dao.Db.Db.QueryRowx(sqlStr, args...).Scan(&p.ID, &p.CreatedAt)
}

// UpdateVisaPodRequest update the exist visa pod request
func (dao *Dao) UpdateVisaPodRequest(id int64, update map[string]any) error {
	if len(update) <= 0 {
		return nil
	}

	query := dao.Db.PSQL.Update(visaPodRequestTable).Where("id = ?", id)

	var allowUpdateColumns = []string{"pod_id", "version", "pre_pod", "exp_pod", "reason", "user_id", "status"}
	for k, v := range update {
		if utils.Contain(k, allowUpdateColumns) {
			query = query.Set(k, v)
		}
	}

	query = query.Set("updated_at", time.Now())

	sqlStr, args, err := query.ToSql()
	if err != nil {
		return err
	}

	_, err = dao.Db.Db.Exec(sqlStr, args...)
	return err
}

// DeleteVisaPodRequest delete the exist visa pod request
func (dao *Dao) DeleteVisaPodRequest(id int64) error {
	query := dao.Db.PSQL.Delete(visaPodRequestTable).Where("id = ?", id)
	sqlStr, args, err := query.ToSql()
	if err != nil {
		return err
	}

	_, err = dao.Db.Db.Exec(sqlStr, args...)
	return err
}

// UpdateVisaPodRequestStatus update visa pod request status
func (dao *Dao) UpdateVisaPodRequestStatus(req *models.VisaPodRequest) error {
	var tx, err = dao.Db.Db.Begin()
	if err != nil {
		return err
	}

	// Avoid panic issues
	defer func() {
		if tx != nil {
			tx.Rollback()
		}
	}()

	query := dao.Db.PSQL.Update(visaPodRequestTable).
		Set("status", req.Status).
		Set("reviewer_comment", req.ReviewerComment).
		Set("updated_at", time.Now()).
		Where("id = ?", req.ID)

	sqlStr, args, err := query.ToSql()
	if err != nil {
		return err
	}

	if _, err = tx.Exec(sqlStr, args...); err != nil {
		tx.Rollback()
		return err
	}

	if req.Status != models.RequestStatus.Approved {
		tx.Commit()
		return nil // Just change status and return
	}

	var pod = new(models.PodExtSchema)
	if req.ExpPodData != nil {
		if err = json.Unmarshal(req.ExpPodData.Data, &pod); err != nil {
			tx.Rollback()
			return err
		}
	}

	if req.RequestType == models.RequestType.Add {
		var countNameExist int
		if err := dao.Db.Db.Get(&countNameExist, "SELECT COUNT(*) FROM visa_pods WHERE name = $1 AND category = $2 AND sub_category = $3", pod.Name, pod.Category, pod.SubCategory); err != nil {
			tx.Rollback()
			return err
		}
		if countNameExist > 0 {
			tx.Rollback()
			return fmt.Errorf("PodName: %s already exist in Category: %s and SubCategory: %s", pod.Name, pod.Category, pod.SubCategory)
		}
		query := dao.Db.PSQL.Insert(visaPodTable).
			Columns("id", "name", "category", "sub_category", "body", "status", "title", `"order"`).
			Values(pod.ID, pod.Name, pod.Category, pod.SubCategory, pod.Body, pod.Status, pod.Title, pod.Order).
			Suffix("RETURNING id, created_at")
		sqlStr, args, err := query.ToSql()
		if err != nil {
			tx.Rollback()
			return err
		}

		if err := tx.QueryRow(sqlStr, args...).Scan(&pod.ID, &pod.CreatedAt); err != nil {
			tx.Rollback()
			return err
		}
	}

	if req.RequestType == models.RequestType.Edit {
		var countNameExist int
		if err := dao.Db.Db.Get(&countNameExist, "SELECT COUNT(*) FROM visa_pods WHERE name = $1 AND category = $2 AND sub_category = $3 AND id <> $4", pod.Name, pod.Category, pod.SubCategory, pod.ID); err != nil {
			tx.Rollback()
			return err
		}
		if countNameExist > 0 {
			tx.Rollback()
			return fmt.Errorf("PodName: %s already exist in Category: %s and SubCategory: %s", pod.Name, pod.Category, pod.SubCategory)
		}
		query := dao.Db.PSQL.Update(visaPodTable).
			Set("name", pod.Name).
			// Set("category", pod.Category).
			// Set("sub_category", pod.SubCategory).
			Set("body", pod.Body).
			Set("status", pod.Status).
			Set("title", pod.Title).
			Set(`"order"`, pod.Order).
			Set("updated_at", time.Now()).
			Where("id = ?", pod.ID)
		sqlStr, args, err := query.ToSql()
		if err != nil {
			tx.Rollback()
			return err
		}

		if _, err := tx.Exec(sqlStr, args...); err != nil {
			tx.Rollback()
			return err
		}

		// Get related visa product schemas
		var visaProducts []struct {
			ID     string `db:"id"`
			Schema string `db:"schema"`
		}

		if err = dao.Db.Db.Select(&visaProducts, "select id, schema from visa_product"); err != nil {
			tx.Rollback()
			return err
		}

		for _, vp := range visaProducts {
			var podJSON = &podJSON{
				src:    vp.Schema,
				oldPod: gjson.ParseBytes(req.PrePodData.Raw).Get("body").Raw,
				newPod: gjson.ParseBytes(req.ExpPodData.Raw).Get("body").Raw,
			}

			if err := podJSON.updatePodJSON(); err != nil {
				return err
			}

			// Only update changed visa products
			if podJSON.src != vp.Schema {
				if _, err = tx.Exec("UPDATE visa_product SET schema = $1 WHERE id = $2", podJSON.src, vp.ID); err != nil {
					tx.Rollback()
					return err
				}
			}
		}
	}

	if req.RequestType == models.RequestType.Delete {
		query := dao.Db.PSQL.Update(visaPodTable).
			Set("status", "inactive").
			Where("id = ?", req.PodID)
		sqlStr, args, err := query.ToSql()
		if err != nil {
			tx.Rollback()
			return err
		}

		if _, err := tx.Exec(sqlStr, args...); err != nil {
			tx.Rollback()
			return err
		}

		// Get related visa product schemas
		var visaProducts []struct {
			ID     string `db:"id"`
			Schema string `db:"schema"`
		}

		if err = dao.Db.Db.Select(&visaProducts, fmt.Sprintf("select id, schema from visa_product where schema_pods @> array[%d]", req.PodID)); err != nil {
			tx.Rollback()
			return err
		}

		for _, vp := range visaProducts {
			var podJSON = &podJSON{
				src:    vp.Schema,
				oldPod: gjson.ParseBytes(req.PrePodData.Data).Get("body").Raw,
				newPod: "",
			}

			if err := podJSON.updatePodJSON(); err != nil {
				return err
			}
			if podJSON.src != vp.Schema {
				if _, err = tx.Exec("UPDATE visa_product SET schema = $1, schema_pods = ARRAY(SELECT unnest(schema_pods) EXCEPT SELECT unnest(ARRAY[$2])) WHERE id = $3", podJSON.src, req.PodID, vp.ID); err != nil {
					tx.Rollback()
					return err
				}
			}
		}
	}

	tx.Commit()

	return nil
}

// RevertVisaPodRequest revert visa pod request
func (dao *Dao) RevertVisaPodRequest(req *models.VisaPodRequest) error {
	var err error
	if req, err = dao.GetVisaPodRequestByID(req.ID); err != nil {
		return err
	}

	if req.Status != models.RequestStatus.Approved {
		return fmt.Errorf("Can only revert approved request! ")
	}

	req.Status = models.RequestStatus.Submitted
	req.ExpPodData, req.PrePodData = req.PrePodData, req.ExpPodData // swap
	switch req.RequestType {
	case models.RequestType.Add:
		req.RequestType = models.RequestType.Delete
	case models.RequestType.Delete:
		req.RequestType = models.RequestType.Add
	}

	req.Reason = fmt.Sprintf(`Revert for request: %d, version: %s
	%s`, req.ID, req.Version, req.Reason)

	req.WithVersion()

	query := dao.Db.PSQL.Insert(visaPodRequestTable).
		Columns("pod_id", "request_type", "version", "pre_pod", "exp_pod", "reason", "user_id", "status").
		Values(req.PodID, req.RequestType, req.Version, req.PrePodData, req.ExpPodData, req.Reason, req.UserID, req.Status).
		Suffix("RETURNING id, created_at")
	sqlStr, args, err := query.ToSql()
	if err != nil {
		return err
	}

	return dao.Db.Db.QueryRowx(sqlStr, args...).Scan(&req.ID, &req.CreatedAt)
}

// GetVisaPodRequestByID get visa pod request detail by id
func (dao *Dao) GetVisaPodRequestByID(id int64) (*models.VisaPodRequest, error) {
	var query = dao.Db.PSQL.Select(visaPodRequestColumns...).
		Column("(SELECT CONCAT_WS(' ',given_name ,surname ) FROM users WHERE id = user_id ) user_display_name").
		From(visaPodRequestTable).Where("id = ?", id)

	var result = models.VisaPodRequest{}

	sqlStr, args, _ := query.ToSql()
	if err := dao.Db.Db.Get(&result, sqlStr, args...); err != nil {
		return nil, err
	}

	return &result, nil
}

// GetVisaPodRequestList get visa pod request list
func (dao *Dao) GetVisaPodRequestList(req models.VisaPodRequestFilter) (*models.VisaPodRequestFilterResponse, error) {
	var query = dao.Db.PSQL.Select(visaPodRequestColumns...).
		Column("(SELECT CONCAT_WS(' ',given_name ,surname ) FROM users WHERE id = user_id ) user_display_name").
		From(visaPodRequestTable)

	if req.IsMyRequest {
		query = query.Where("user_id = ?", req.UserID) // My request
	} else {
		query = query.Where("user_id <> ? AND status = ?", req.UserID, models.RequestStatus.Submitted) // Reviewer request
	}

	if req.PodID != "" {
		query = query.Where("pre_pod->>'id'::text LIKE ?", "%"+req.PodID+"%")
	}

	if req.SortField != "" {
		query = query.OrderBy(fmt.Sprintf("%s %s", req.SortField, req.SortOrder))
	} else {
		query = query.OrderBy("id DESC")
	}

	query = query.Limit(req.Limit).Offset(req.Offset)

	sqlStr, args, _ := query.ToSql()
	rows, err := dao.getQueryer().Queryx(sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// Get data
	var res = models.VisaPodRequestFilterResponse{}

	for rows.Next() {
		var p models.VisaPodRequest
		if err := rows.StructScan(&p); err != nil {
			return nil, err
		}
		res.Data = append(res.Data, &p)
	}

	// Get total count
	sqlCount := utils.QueryCountItems(sqlStr)
	rowCount, err := dao.getQueryer().Queryx(sqlCount, args...)
	if err != nil {
		return nil, err
	}
	defer rowCount.Close()

	if rowCount.Next() {
		if err := rowCount.Scan(&res.Total); err != nil {
			return nil, err
		}
	}

	return &res, nil
}

var (
	doOnce sync.Once
	gc     gcache.Cache
)

// GetVisaPodMetaData get visa pod meta data
func (dao *Dao) GetVisaPodMetaData() (any, error) {
	doOnce.Do(func() {
		gc = gcache.New(20).LRU().Build()
	})

	var (
		lastCreatedPod time.Time
		lastUpdatedPod *time.Time
	)

	if err := dao.Db.Db.Get(&lastCreatedPod, `SELECT MAX(created_at) FROM visa_pods WHERE status = 'active'`); err != nil {
		return nil, err
	}
	if err := dao.Db.Db.Get(&lastUpdatedPod, `SELECT MAX(updated_at) FROM visa_pods WHERE status = 'active'`); err != nil {
		return nil, err
	}

	// Get cache
	var withCache bool = true
	if val, err := gc.Get("last_updated_pod"); err == nil {
		if !funk.IsEqual(val, lastUpdatedPod) {
			withCache = false
		}
	}
	if val, err := gc.Get("last_created_pod"); err == nil {
		if !funk.IsEqual(val, lastCreatedPod) {
			withCache = false
		}
	}
	if val, err := gc.Get("meta_data"); err == nil && withCache {
		return val, nil
	}

	var res = map[string]any{}

	var pods []*models.PodExtSchema
	if err := dao.Db.Db.Select(&pods, `SELECT id, name, category, sub_category, body, status, created_at, updated_at, title, "order" FROM visa_pods WHERE status = 'active'`); err != nil {
		return nil, err
	}

	// Category meta list
	var categories = funk.UniqString(funk.Map(pods, func(pod *models.PodExtSchema) string {
		return pod.Category
	}).([]string))

	sort.Strings(categories)

	res["category_meta"] = categories

	// Sub category meta list
	var subCategories = funk.UniqString(funk.Map(pods, func(pod *models.PodExtSchema) string {
		return pod.SubCategory
	}).([]string))

	sort.Strings(subCategories)

	res["sub_category_meta"] = subCategories

	// TypeFE meta list
	var typeFEs = funk.UniqString(funk.Map(pods, func(pod *models.PodExtSchema) string {
		return gjson.ParseBytes(pod.Body).Get("type_fe").String()
	}).([]string))
	typeFEs = funk.FilterString(typeFEs, func(v string) bool { return v != "" })

	sort.Slice(typeFEs, func(i, j int) bool { return typeFEs[i] < typeFEs[j] })

	res["type_fe_meta"] = typeFEs

	// TypeBE meta list
	var typeBEs = funk.UniqString(funk.Map(pods, func(pod *models.PodExtSchema) string {
		return gjson.ParseBytes(pod.Body).Get("type").String()
	}).([]string))

	typeBEs = funk.FilterString(typeBEs, func(v string) bool { return v != "" })

	sort.Strings(typeBEs)

	res["type_be_meta"] = typeBEs

	// Suggest key meta list
	var suggestKeysInf = funk.Map(pods, func(pod *models.PodExtSchema) []string {
		if val, err := utils.GetJSONKeys(pod.Body); err == nil {
			return val
		}
		return nil
	})

	var suggestKeys []string
	funk.ForEach(suggestKeysInf, func(val []string) {
		suggestKeys = append(suggestKeys, val...)
	})
	suggestKeys = funk.UniqString(suggestKeys)

	sort.Strings(suggestKeys)

	res["suggest_key_meta"] = suggestKeys

	// Save cache
	gc.Set("meta_data", res)
	gc.Set("last_updated_pod", lastUpdatedPod)
	gc.Set("last_created_pod", lastCreatedPod)

	return res, nil
}

type podJSON struct {
	src    string
	oldPod string
	newPod string
	paths  []string
}

func (s *podJSON) updatePodJSON() error {
	if s == nil {
		return nil
	}
	s.searchForJSONPaths(gjson.Parse(s.src), "")
	for i := len(s.paths) - 1; i >= 0; i-- {
		if s.newPod == "" {
			s.src, _ = sjson.Delete(s.src, s.paths[i])
		} else {
			var oldPod = gjson.Get(s.src, s.paths[i]).Raw
			_, delPaths := utils.GetJSONPathDiff(s.oldPod, s.newPod)

			for _, delPath := range delPaths {
				oldPod, _ = sjson.Delete(oldPod, delPath)
			}

			var newPod = utils.MergeJSON([]byte(oldPod), []byte(s.newPod))
			s.src, _ = sjson.Set(s.src, s.paths[i], gjson.Parse(newPod).Value())
		}
	}
	return nil
}

func (s *podJSON) searchForJSONPaths(obj gjson.Result, prePath string) {

	if !obj.IsObject() && !obj.IsArray() {
		return
	}

	var i = 0
	obj.ForEach(func(key, value gjson.Result) bool {
		var subPath = []string{}
		if prePath != "" {
			subPath = append(subPath, prePath)
		}
		if obj.IsArray() {
			subPath = append(subPath, strconv.Itoa(i))
		}

		if obj.IsObject() {
			subPath = append(subPath, key.String())
		}

		if isPod(value) && value.Get("id").Int() == gjson.Parse(s.oldPod).Get("id").Int() {
			s.paths = append(s.paths, strings.Join(subPath, "."))

		}

		if obj.IsArray() {
			i++
		}

		s.searchForJSONPaths(value, strings.Join(subPath, "."))

		return true
	})
}

// Asume that pod is object have fields: id, name, category, subcategory
func isPod(item gjson.Result) bool {
	if !item.Get("id").Exists() {
		return false
	}
	if !item.Get("name").Exists() {
		return false
	}
	if !item.Get("category").Exists() {
		return false
	}
	if !item.Get("sub_category").Exists() {
		return false
	}

	return true
}
