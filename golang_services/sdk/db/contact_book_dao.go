package db

import (
	sqllib "database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

const (
	ContactBookTable = "contact_book"
)

var (
	contactBookCols = []string{
		"id",
		"user_id",
		"traveler_id",
		"given_name",
		"surname",
		"middle_name",
		"phone",
		"email",
		"relationship",
		"ref_name",
		"status",
		"created_at",
		"updated_at",
	}
)

func (dao *Dao) GetContactBookItemByID(id int) (*models.ContactBookItem, error) {
	if id <= 0 {
		return nil, nil
	}
	contacts, err := queryContactBookItems(dao.getQueryer(), dao.Db.PSQL, map[string]any{"id": id})
	if err != nil {
		return nil, err
	}
	if len(contacts) < 1 {
		return nil, nil
	}
	return contacts[0], nil
}

func (dao *Dao) GetContactBookItemsByTravelerID(travelerID string, q map[string]any) ([]*models.ContactBookItem, error) {
	if travelerID == "" {
		return nil, nil
	}
	query := map[string]any{
		"traveler_id": travelerID,
	}
	for k, v := range q {
		switch k {
		case "id", "given_name", "surname", "phone", "email", "relationship", "ref_name", "status", "middle_name":
			query[k] = v
		}
	}
	return queryContactBookItems(dao.getQueryer(), dao.Db.PSQL, query)
}

func queryContactBookItems(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string]any) ([]*models.ContactBookItem, error) {
	sql := sb.Select(contactBookCols...).From(ContactBookTable)
	for k, v := range q {
		switch k {
		case "given_name", "surname", "phone", "email", "ref_name", "middle_name":
			sql = sql.Where(squirrel.Like{k: v})
		default:
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}
	sql = sql.OrderBy("updated_at DESC")
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil && err != sqllib.ErrNoRows {
		return nil, err
	}
	defer rows.Close()
	var res []*models.ContactBookItem
	for rows.Next() {
		var item models.ContactBookItem
		if err = rows.StructScan(&item); err != nil {
			return nil, err
		}
		res = append(res, &item)
	}
	return res, nil
}

func (dao *Dao) InsertContactBookItem(item *models.ContactBookItem) (int, error) {
	if item == nil {
		return 0, nil
	}
	if item.UserID == "" {
		return 0, fmt.Errorf("invalid user ID")
	}
	now := time.Now()
	m := map[string]any{
		"user_id":      item.UserID,
		"traveler_id":  item.TravelerID,
		"given_name":   item.GivenName,
		"surname":      item.Surname,
		"middle_name":  item.MiddleName,
		"phone":        item.Phone,
		"email":        item.Email,
		"relationship": item.Relationship,
		"ref_name":     item.RefName,
		"created_at":   now,
		"updated_at":   now,
	}
	return insertContactBookItem(dao.getQueryer(), dao.Db.PSQL, m)
}

func insertContactBookItem(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, m map[string]any) (int, error) {
	sqlstr, args, err := sb.Insert(ContactBookTable).SetMap(m).Suffix("RETURNING id").ToSql()
	if err != nil {
		return 0, err
	}
	row := queryer.QueryRowx(sqlstr, args...)
	if row.Err() != nil {
		return 0, err
	}
	var id int
	if err = row.Scan(&id); err != nil {
		return 0, err
	}
	return id, nil
}

func (dao *Dao) UpdateContactBookItem(id int, update *models.ContactBookItem) error {
	if id <= 0 || update == nil {
		return nil
	}
	m := map[string]any{
		"given_name":   update.GivenName,
		"surname":      update.Surname,
		"middle_name":  update.MiddleName,
		"phone":        update.Phone,
		"email":        update.Email,
		"relationship": update.Relationship,
		"ref_name":     update.RefName,
		"updated_at":   time.Now(),
	}
	return updateContactBookItem(dao.getExecer(), dao.Db.PSQL, id, m)
}

func updateContactBookItem(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id int, update map[string]any) error {
	sqlsrt, args, err := sb.Update(ContactBookTable).SetMap(update).Where(squirrel.Eq{"id": id}).ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlsrt, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *Dao) DeactivateContactBookItem(id int) error {
	if id <= 0 {
		return nil
	}
	return deactivateContactBookItem(dao.getExecer(), dao.Db.PSQL, id)
}

func deactivateContactBookItem(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id int) error {
	sqlstr, args, err := sb.Update(ContactBookTable).Set("status", "inactive").Where(squirrel.Eq{"id": id}).ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}
