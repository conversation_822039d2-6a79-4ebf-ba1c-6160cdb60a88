package db

import (
	"bitbucket.org/persistence17/aria/golang_services/models"
)

const (
	exchangeRate = "exchange_rate"
)

// GetExchangeRate get exchange rate
func (dao *Dao) GetExchangeRate(rate *models.ExchangeRate) error {
	return dao.Db.PSQL.Select("id, rate, round, status, created_at, updated_at").From(exchangeRate).
		Where("from_currency = ?", rate.FromCurrency).
		Where("to_currency = ?", rate.ToCurrency).
		Where("status = ?", "active").
		RunWith(dao.Db.Db).QueryRow().
		Scan(&rate.ID, &rate.Rate, &rate.Round, &rate.Status, &rate.CreatedAt, &rate.UpdatedAt)
}
