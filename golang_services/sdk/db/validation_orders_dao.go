package db

import (
	"time"

	"github.com/Masterminds/squirrel"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

const (
	validationOrdersTable = "validation_orders"
)

var (
	validationOrdersCols = []string{
		"id",
		"user_id",
		"org_id",
		"status",
		"input_files",
		"created_at",
		"updated_at",
	}
)

func (dao *Dao) CreateValidationOrder(input *models.ValidationOrder) (int, error) {
	sql, args, err := dao.Db.PSQL.Insert(validationOrdersTable).
		Columns("user_id", "org_id", "status", "input_files", "created_at").
		Values(input.UserID, input.OrgID, input.Status, input.InputFiles, time.Now()).
		Suffix("RETURNING id").ToSql()
	if err != nil {
		return -1, err
	}

	res := dao.getQueryer().QueryRowx(sql, args...)
	if res.Err() != nil {
		return -1, res.Err()
	}

	var id int
	if err = res.Scan(&id); err != nil {
		return -1, err
	}
	return id, nil
}

func (dao *Dao) UpdateValidationOrder(input *models.ValidationOrder) error {
	sql, args, err := dao.Db.PSQL.Update(validationOrdersTable).
		SetMap(map[string]any{
			"input_files": input.InputFiles,
			"updated_at":  time.Now(),
		}).Where(squirrel.Eq{"id": input.ID, "org_id": input.OrgID}).ToSql()

	_, err = dao.getExecer().Exec(sql, args...)
	if err != nil {
		return err
	}

	return nil
}

func (dao *Dao) AddInputFileToValidationOrder(id int, files ...*models.InputFile) error {
	toAdd := models.InputFiles(files)
	sql, args, err := dao.Db.PSQL.Update(validationOrdersTable).
		Set("updated_at", time.Now()).
		Set("input_files", squirrel.Expr("output_files || ?", toAdd)).
		Where(squirrel.Eq{"id": id}).ToSql()

	if err != nil {
		return err
	}
	_, err = dao.getExecer().Exec(sql, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *Dao) QueryValidationOrders(q map[string]any, limit, offset uint) ([]*models.ValidationOrder, error) {
	sql := dao.Db.PSQL.Select(validationOrdersCols...).From(validationOrdersTable)
	for k, v := range q {
		switch k {
		case "id", "status", "user_id", "org_id":
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}
	if limit > 0 {
		sql = sql.Limit(uint64(limit))
	}
	if offset > 0 {
		sql = sql.Offset(uint64(offset))
	}
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.ValidationOrder
	for rows.Next() {
		var one models.ValidationOrder
		if err = rows.StructScan(&one); err != nil {
			return nil, err
		}
		res = append(res, &one)
	}
	return res, nil
}
