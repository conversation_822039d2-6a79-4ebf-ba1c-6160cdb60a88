package db

import (
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
	"github.com/rs/zerolog/log"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

const (
	atlasResultTable     = "atlas_result"
	atlasValidationTable = "atlas_validations"
)

var (
	atlasResultCols = []string{
		"request_id",
		"service_type",
		"application_id",
		"request_ts",
		"result_ts",
		"inputs",
		"outputs",
	}
	atlasValidationsCols = []string{
		"request_id",
		"service_type",
		"validation_order_id",
		"request_ts",
		"result_ts",
		"inputs",
		"outputs",
	}
)

type AtlasDao struct {
	Db *AuroraDB
	tx *sqlx.Tx
}

func (dao *AtlasDao) Begin() error {
	if dao.tx != nil {
		return nil
	}
	tx, err := dao.Db.Db.Beginx()
	if err != nil {
		return err
	}
	dao.tx = tx
	return nil
}

func (dao *AtlasDao) Commit() error {
	if dao.tx == nil {
		return nil
	}
	if err := dao.tx.Commit(); err != nil {
		return err
	}
	dao.tx = nil
	return nil
}

func (dao *AtlasDao) Rollback() error {
	if dao.tx == nil {
		return nil
	}
	if err := dao.tx.Rollback(); err != nil {
		return err
	}
	dao.tx = nil
	return nil
}

func (dao *AtlasDao) getQueryer() sqlx.Queryer {
	if dao.tx != nil {
		return dao.tx
	}
	return dao.Db.Db
}

func (dao *AtlasDao) getExecer() sqlx.Execer {
	if dao.tx != nil {
		return dao.tx
	}
	return dao.Db.Db
}

func (dao *AtlasDao) CreateAtlasTask(tasks []*models.AtlasTask) error {
	return createAtlasTask(dao.getExecer(), dao.Db.PSQL, tasks)
}

func createAtlasTask(execer sqlx.Execer, sb *squirrel.StatementBuilderType, tasks []*models.AtlasTask) error {
	sql := sb.Insert(atlasResultTable).
		Columns(atlasResultCols...)
	for _, task := range tasks {
		sql = sql.Values(task.RequestID, task.ServiceType, task.ApplicationID, task.RequestTs, task.ResultTs, task.Inputs, task.Outputs)
	}
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *AtlasDao) CreateETSAtlasTask(tasks []*models.ETSAtlasTask) error {
	if len(tasks) <= 0 {
		return nil
	}
	sql := dao.Db.PSQL.Insert("ets_atlas_result").
		Columns("request_id", "service_type", "service_task_id", "request_ts", "result_ts", "inputs", "outputs")
	for _, task := range tasks {
		sql = sql.Values(task.RequestID, task.ServiceType, task.ServiceTaskID, task.RequestTs, task.ResultTs, task.Inputs, task.Outputs)
	}
	_, err := sql.RunWith(dao.Db.Db).Exec()
	return err
}

func (dao *AtlasDao) QueryAtlasTask(q map[string]any) ([]*models.AtlasTask, error) {
	if len(q) == 0 {
		return nil, nil
	}
	return queryAtlasTask(dao.getQueryer(), dao.Db.PSQL, q)
}

func queryAtlasTask(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string]any) ([]*models.AtlasTask, error) {
	sql := sb.Select(atlasResultCols...).From(atlasResultTable)
	validQuery := false
	for k, v := range q {
		switch k {
		case "request_id", "application_id", "service_type":
			sql = sql.Where(squirrel.Eq{k: v})
			validQuery = true
		}
	}
	if !validQuery {
		return nil, nil
	}
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.AtlasTask
	for rows.Next() {
		var task models.AtlasTask
		if err = rows.StructScan(&task); err != nil {
			return nil, err
		}
		res = append(res, &task)
	}
	return res, nil
}

func (dao *AtlasDao) QueryETSAtlasTask(q map[string]any) ([]*models.ETSAtlasTask, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := dao.Db.PSQL.Select("request_id", "service_type", "service_task_id", "request_ts", "result_ts", "inputs", "outputs").
		From("ets_atlas_result")

	for k, v := range q {
		switch k {
		case "request_id", "application_id", "service_type":
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	var res []*models.ETSAtlasTask
	if err := dao.Db.Db.Select(&res, sqlstr, args...); err != nil {
		return nil, err
	}
	return res, nil
}

func (dao *AtlasDao) UpdateAtlasTask(id string, updates *models.AtlasTask) error {
	if id == "" || id != updates.RequestID {
		return nil
	}
	return updateAtlasTask(dao.getExecer(), dao.Db.PSQL, id, updates)
}

func updateAtlasTask(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id string, updates *models.AtlasTask) error {
	sql := sb.Update(atlasResultTable).
		Set("outputs", updates.Outputs).
		Set("result_ts", updates.ResultTs).
		Where(squirrel.Eq{"request_id": id})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *AtlasDao) UpdateETSAtlasTask(id string, updates *models.ETSAtlasTask) error {
	if id == "" || id != updates.RequestID {
		return nil
	}

	_, err := dao.Db.PSQL.Update("ets_atlas_result").
		Set("outputs", updates.Outputs).
		Set("result_ts", updates.ResultTs).
		Where(squirrel.Eq{"request_id": id}).
		RunWith(dao.Db.Db).Exec()

	return err

}

func (dao *AtlasDao) GetLatestAtlasResult(pkgID int, appIDs []int, requestIDs, st []string) ([]*models.AtlasTask, error) {
	if pkgID <= 0 {
		return nil, nil
	}

	subq1 := dao.Db.PSQL.Select("ar.*").
		From("atlas_result ar ").
		Join("application on application.id=ar.application_id").
		Where(squirrel.Eq{"application.package_id": pkgID})
	if len(appIDs) > 0 {
		subq1 = subq1.Where(squirrel.Eq{"application.id": appIDs})
	}

	if len(requestIDs) > 0 {
		subq1 = subq1.Where(squirrel.Eq{"ar.request_id": requestIDs})
	}
	if len(st) > 0 {
		subq1 = subq1.Where(squirrel.Eq{"service_type": st})
	}
	subq2 := dao.Db.PSQL.Select("*", "ROW_NUMBER() OVER (PARTITION BY application_id, service_type ORDER BY request_ts DESC) as rn").
		FromSelect(subq1, "t1")
	sql := dao.Db.PSQL.Select(atlasResultCols...).FromSelect(subq2, "t2").Where(squirrel.Eq{"t2.rn": 1})

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	//log.Info().Str("sql", sqlstr).Interface("args", args).Msg("get latest atlas result query")
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.AtlasTask
	for rows.Next() {
		var t models.AtlasTask
		if err := rows.StructScan(&t); err != nil {
			return nil, err
		}
		res = append(res, &t)
	}
	return res, nil
}

func (dao *AtlasDao) GetLatestETSAtlasResult(orderID int, tasks []string, requestIDs, st []string) ([]*models.ETSAtlasTask, error) {
	if orderID <= 0 {
		return nil, nil
	}
	subq1 := dao.Db.PSQL.Select("ar.request_id", "ar.service_type", "ar.service_task_id",
		"ar.request_ts", "ar.result_ts", "ar.inputs", "ar.outputs").
		From("ets_atlas_result ar").
		Join("service_tasks t on t.id=ar.service_task_id").
		Where(squirrel.Eq{"t.order_id": orderID})
	if len(tasks) > 0 {
		subq1 = subq1.Where(squirrel.Eq{"t.id": tasks})
	}

	if len(requestIDs) > 0 {
		subq1 = subq1.Where(squirrel.Eq{"ar.request_id": requestIDs})
	}
	if len(st) > 0 {
		subq1 = subq1.Where(squirrel.Eq{"service_type": st})
	}

	subq2 := dao.Db.PSQL.Select("*", "ROW_NUMBER() OVER (PARTITION BY service_task_id, service_type ORDER BY request_ts DESC) as rn").
		FromSelect(subq1, "t1")

	sql := dao.Db.PSQL.Select("request_id", "service_type", "service_task_id", "request_ts", "result_ts", "inputs", "outputs").
		FromSelect(subq2, "t2").
		Where(squirrel.Eq{"t2.rn": 1})

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.ETSAtlasTask
	for rows.Next() {
		var t models.ETSAtlasTask
		if err := rows.StructScan(&t); err != nil {
			return nil, err
		}
		res = append(res, &t)
	}
	return res, nil
}

func (dao *AtlasDao) CreateAtlasValidation(tasks []*models.AtlasValidation) error {
	if len(tasks) == 0 {
		return nil
	}
	sql := dao.Db.PSQL.Insert(atlasValidationTable).Columns(atlasValidationsCols...)
	for _, t := range tasks {
		sql = sql.Values(t.RequestID, t.ServiceType, t.ValidationOrderID, t.ResultTs, t.ResultTs, t.Inputs, t.Outputs)
	}
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return err
	}
	_, err = dao.getExecer().Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *AtlasDao) QueryAtlasValidations(q map[string]any) ([]*models.AtlasValidation, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := dao.Db.PSQL.Select(atlasValidationsCols...).From(atlasValidationTable)
	validQuery := false
	for k, v := range q {
		switch k {
		case "request_id", "service_type", "validation_order_id":
			sql = sql.Where(squirrel.Eq{k: v})
			validQuery = true
		}
	}
	if !validQuery {
		return nil, nil
	}
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.AtlasValidation
	for rows.Next() {
		var one models.AtlasValidation
		if err = rows.StructScan(&one); err != nil {
			return nil, err
		}
		res = append(res, &one)
	}
	return res, nil
}

func (dao *AtlasDao) UpdateAtlasValidation(reqID string, update *models.AtlasValidation) error {
	if reqID == "" {
		return nil
	}
	sql, args, err := dao.Db.PSQL.Update(atlasValidationTable).
		Set("outputs", update.Outputs).
		Set("result_ts", update.ResultTs).
		Where(squirrel.Eq{"request_id": reqID}).ToSql()
	if err != nil {
		return err
	}
	_, err = dao.getExecer().Exec(sql, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *AtlasDao) GetLatestAtlasValidations(orgID int, orderIDs []int, st []string) ([]*models.AtlasValidation, error) {
	if orgID <= 0 || len(orderIDs) == 0 {
		return nil, nil
	}
	subq1 := dao.Db.PSQL.Select("atlas_validations.request_id", "atlas_validations.service_type", "atlas_validations.validation_order_id",
		"atlas_validations.request_ts", "atlas_validations.result_ts", "atlas_validations.inputs", "atlas_validations.outputs").
		From(atlasValidationTable).
		Join("validation_orders on validation_orders.id=atlas_validations.validation_order_id").
		Where(squirrel.Eq{"validation_orders.org_id": orgID})
	if len(orderIDs) > 0 {
		subq1 = subq1.Where(squirrel.Eq{"validation_orders.id": orderIDs})
	}
	if len(st) > 0 {
		subq1 = subq1.Where(squirrel.Eq{"service_type": st})
	}
	subq2 := dao.Db.PSQL.Select("*", "ROW_NUMBER() OVER (PARTITION BY validation_order_id, service_type ORDER BY request_ts DESC) as rn").FromSelect(subq1, "t1")
	sql := dao.Db.PSQL.Select(atlasValidationsCols...).FromSelect(subq2, "t2").Where(squirrel.Eq{"t2.rn": 1})

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	log.Info().Str("sql", sqlstr).Interface("args", args).Msg("get latest atlas validation result query")
	rows, err := dao.getQueryer().Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.AtlasValidation
	for rows.Next() {
		var t models.AtlasValidation
		if err := rows.StructScan(&t); err != nil {
			return nil, err
		}
		res = append(res, &t)
	}
	return res, nil
}
