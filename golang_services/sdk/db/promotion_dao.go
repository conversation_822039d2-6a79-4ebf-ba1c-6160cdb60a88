package db

import (
	"bitbucket.org/persistence17/aria/golang_services/models"
)

func (dao *Dao) GetPromotionCodes() (models.PromotionCouponList, error) {
	query := dao.Db.PSQL.Select("id, promotion_code, promotion_type, discount_rules, currency, quantity, remain, active, start_date, end_date, created_at, updated_at").
		From("promotion_codes")

	sqlStr, args, _ := query.ToSql()
	res := []models.PromotionCoupon{}
	if err := dao.Db.Db.Select(&res, sqlStr, args...); err != nil {
		return nil, err
	}
	return res, nil
}
