package db

import (
	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
)

const (
	emailTemplateTable = "email_template"
)

var (
	emailTemplateColumns = []string{
		"id",
		"email_type",
		"name",
		"save_as_notification",
		"language",
		"from",
		"title",
		"parameters",
		"htmlbody",
		"textbody",
		"active",
		"created_at",
		"updated_at",
	}
)

func (dao *Dao) GetEmailTemplateByName(name, language string) (*models.EmailTemplate, error) {
	if name == "" {
		return nil, nil
	}
	if language == "" {
		language = "EN"
	}
	q := map[string][]any{"name": {name}, "language": {language}}

	res, err := queryEmailTemplates(dao.getQueryer(), dao.Db.PSQL, q)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}
func queryEmailTemplates(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any) ([]*models.EmailTemplate, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := sb.Select("*").From(emailTemplateTable)

	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}
	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := queryer.Queryx(sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.EmailTemplate
	for rows.Next() {
		var p models.EmailTemplate
		if err = rows.StructScan(&p); err != nil {
			return nil, err
		}
		res = append(res, &p)
	}
	return res, nil
}
