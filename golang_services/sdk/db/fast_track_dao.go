package db

import (
	sqllib "database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

const (
	fastTrackTable = "fast_track"
)

var (
	fastTrackCols = []string{
		"id",
		"user_id",
		"number",
		"type",
		"expiration_date",
		"created_at",
		"updated_at",
	}
)

func (dao *Dao) GetFastTrackByID(id int) (*models.FastTrack, error) {
	if id <= 0 {
		return nil, nil
	}
	adds, err := queryFastTracks(dao.getQueryer(), dao.Db.PSQL, map[string]any{"id": id})
	if err != nil {
		return nil, err
	}
	if len(adds) < 1 {
		return nil, nil
	}
	return adds[0], nil
}

func (dao *Dao) QueryFastTrack(q map[string]any) ([]*models.FastTrack, error) {
	queryer := dao.getQueryer()
	return queryFastTracks(queryer, dao.Db.PSQL, q)
}

func queryFastTracks(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string]any) ([]*models.FastTrack, error) {
	sql := sb.Select(fastTrackCols...).From(fastTrackTable)
	for k, v := range q {
		switch k {
		case "user_id", "number":
			sql = sql.Where(squirrel.Like{k: v})
		default:
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}
	sql = sql.OrderBy("updated_at DESC")
	sqlstr, args, err := sql.ToSql()
	if err != nil && err != sqllib.ErrNoRows {
		return nil, err
	}
	fmt.Printf(sqlstr)
	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var res []*models.FastTrack
	for rows.Next() {
		var item models.FastTrack
		if err = rows.StructScan(&item); err != nil {
			return nil, err
		}
		res = append(res, &item)
	}
	return res, nil
}

func (dao *Dao) InsertFastTrack(item *models.FastTrack) (int, error) {
	if item == nil {
		return 0, nil
	}
	if item.UserID == "" {
		return 0, fmt.Errorf("invalid user ID")
	}
	now := time.Now()
	m := map[string]any{
		"user_id":         item.UserID,
		"number":          item.Number,
		"type":            item.Type,
		"expiration_date": item.ExpirationDate,
		"created_at":      now,
		"updated_at":      now,
	}
	return insertFastTrack(dao.getQueryer(), dao.Db.PSQL, m)
}

func insertFastTrack(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, newFastTrack map[string]any) (int, error) {
	sql := sb.Insert(fastTrackTable).SetMap(newFastTrack).Suffix("RETURNING id")
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return 0, err
	}
	row := queryer.QueryRowx(sqlstr, args...)
	if row.Err() != nil {
		return 0, row.Err()
	}
	var id int
	if err := row.Scan(&id); err != nil {
		return 0, err
	}
	return id, nil
}

func (dao *Dao) UpdateFastTrack(id int, update *models.FastTrack) error {
	if update == nil || id <= 0 {
		return nil
	}
	return updateFastTrack(dao.getExecer(), dao.Db.PSQL, id, update)
}

func updateFastTrack(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id int, update *models.FastTrack) error {
	now := time.Now()
	sql := sb.Update(fastTrackTable).SetMap(map[string]any{
		"number":          update.Number,
		"type":            update.Type,
		"expiration_date": update.ExpirationDate,
		"updated_at":      &now,
	}).Where(squirrel.Eq{"id": id})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *Dao) DeleteFastTrack(id int) error {
	if id <= 0 {
		return nil
	}
	return deleteFastTrack(dao.getExecer(), dao.Db.PSQL, id)
}

func deleteFastTrack(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id int) error {
	sqlstr, args, err := sb.Delete(fastTrackTable).Where(squirrel.Eq{"id": id}).ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}
