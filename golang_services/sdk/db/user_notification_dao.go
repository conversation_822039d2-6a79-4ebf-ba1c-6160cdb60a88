package db

import (
	"regexp"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/k3a/html2text"
)

const (
	userNotificationTable = "user_notifications"
)

func (dao *Dao) CreateUserEmailNotification(title, content, languageCode, userID string) error {
	plain := html2text.HTML2Text(content)
	plain = regexp.MustCompile(`\s+`).ReplaceAllString(plain, " ") // Remove duplicate whitespace and break

	const MAX_TRUNCATE = 255

	contentTruncate := plain
	if len(contentTruncate) > MAX_TRUNCATE {
		contentTruncate = plain[0:MAX_TRUNCATE]
	}
	_, err := dao.Db.PSQL.Insert(userNotificationTable).
		Columns("type", "title", "content", "content_truncate", "language_code", "user_id", "sent_at", "created_at").
		Values("email_notification", title, content, contentTruncate, languageCode, userID, time.Now(), time.Now()).
		PlaceholderFormat(squirrel.Dollar).RunWith(dao.Db.Db).Exec()
	return err
}
