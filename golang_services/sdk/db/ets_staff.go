package db

import (
	"encoding/json"
	"fmt"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/Masterminds/squirrel"
)

func (dao *EtsDao) UpdateServiceOrderStaff(orderID int64, staffID, code string, userID string, note json.RawMessage) error {
	if _, err := dao.Db.PSQL.Insert("service_order_staffs").
		Columns("order_id", "staff_id", "note", "user_id", "staff_confirm_code").
		Values(orderID, staffID, note, userID, code).
		Suffix(`ON CONFLICT(order_id, staff_id) DO UPDATE
		SET note = EXCLUDED.note, user_id = EXCLUDED.user_id, staff_confirm_code = EXCLUDED.staff_confirm_code RETURNING id`).RunWith(dao.Db.Db).Exec(); err != nil {
		return err
	}
	return nil

}

func (dao *EtsDao) GetCountServiceOrderByStaffs(staffIDs []string) ([]string, error) {
	rows, err := dao.Db.PSQL.Select("staff_id, count(order_id)").
		From("service_order_staffs").
		Where(squirrel.Eq{"staff_id": staffIDs}).
		GroupBy("staff_id").
		OrderBy("count asc").Limit(1).
		RunWith(dao.Db.Db).Query()
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	result := []string{}
	for rows.Next() {
		var (
			staffID string
			count   int
		)

		if err := rows.Scan(&staffID, &count); err != nil {
			return nil, err
		}
		result = append(result, staffID)

	}
	return result, nil

}

func (dao *EtsDao) GetServiceOrderStaffs(orderID int64, staffID string) ([]models.ServiceOrderStaff, error) {
	rows, err := dao.Db.PSQL.Select("id, order_id, staff_id, note, user_id, staff_confirm_code, staff_confirm_at, staff_confirm_status, created_at, updated_at").
		From("service_order_staffs").
		Where("order_id = ?", orderID).
		Where("staff_id = ?", staffID).
		RunWith(dao.Db.Db).Query()
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	result := []models.ServiceOrderStaff{}
	for rows.Next() {
		var staff models.ServiceOrderStaff
		if err := rows.Scan(&staff.ID, &staff.OrderID, &staff.StaffID, &staff.Note, &staff.UserID, &staff.StaffConfirmCode, &staff.StaffConfirmAt, &staff.StaffConfirmStatus, &staff.CreatedAt, &staff.UpdatedAt); err != nil {

			return nil, err
		}
		result = append(result, staff)
	}
	return result, nil

}

func (dao *EtsDao) GetServiceOrderStaffByCode(orderID int64, code string) (*models.ServiceOrderStaff, error) {
	rows, err := dao.Db.PSQL.Select("id, order_id, staff_id, note, user_id, staff_confirm_code, staff_confirm_at, staff_confirm_status, created_at, updated_at").
		From("service_order_staffs").
		Where("order_id = ?", orderID).
		Where("staff_confirm_code = ?", code).
		RunWith(dao.Db.Db).Query()
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	if rows.Next() {
		var staff models.ServiceOrderStaff
		if err := rows.Scan(&staff.ID, &staff.OrderID, &staff.StaffID, &staff.Note, &staff.UserID, &staff.StaffConfirmCode, &staff.StaffConfirmAt, &staff.StaffConfirmStatus, &staff.CreatedAt, &staff.UpdatedAt); err != nil {

			return nil, err
		}
		return &staff, nil
	}
	return nil, fmt.Errorf("not found")

}

func (dao *EtsDao) StaffConfirmHandleServiceOrder(orderID int64, note json.RawMessage, code, status string) error {
	if _, err := dao.Db.PSQL.Update("service_order_staffs").
		Set("staff_confirm_at", time.Now()).
		Set("note", note).
		Set("staff_confirm_status", status). // confirmed. rejected
		Where("order_id = ?", orderID).
		Where("staff_confirm_code = ?", code).
		RunWith(dao.Db.Db).Exec(); err != nil {
		return err
	}
	return nil

}

func (dao *EtsDao) DeleteServiceOrderStaff(orderID int64, staffID string) error {
	if _, err := dao.Db.PSQL.Delete("service_order_staffs").
		Where("order_id = ?", orderID).
		Where("staff_id = ?", staffID).
		RunWith(dao.Db.Db).Exec(); err != nil {
		return err
	}
	return nil
}

func (dao *EtsDao) GetStaffsByOrderID(orderID int64) ([]models.ServiceOrderStaff, error) {
	rows, err := dao.Db.PSQL.Select("id, order_id, staff_id, note, user_id, staff_confirm_code, staff_confirm_at, staff_confirm_status, created_at, updated_at").
		From("service_order_staffs").
		Where("order_id = ?", orderID).
		RunWith(dao.Db.Db).Query()
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	result := []models.ServiceOrderStaff{}
	for rows.Next() {
		var staff models.ServiceOrderStaff
		if err := rows.Scan(&staff.ID, &staff.OrderID, &staff.StaffID, &staff.Note, &staff.UserID, &staff.StaffConfirmCode, &staff.StaffConfirmAt, &staff.StaffConfirmStatus, &staff.CreatedAt, &staff.UpdatedAt); err != nil {

			return nil, err
		}
		result = append(result, staff)
	}
	return result, nil

}
