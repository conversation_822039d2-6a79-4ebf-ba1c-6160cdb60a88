package db

import (
	sqllib "database/sql"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
)

const (
	travelerTypeIndexTable = "traveler_type_index"
)

var (
	travelerTypeIndexCols = []string{
		"id",
		"traveler_id",
		"profile_type",
		"user_id",
		"created_at",
		"updated_at",
	}
)

func (dao *Dao) QueryTravelerTypeIndex(q map[string]any) ([]*models.TravelerTypeIndex, error) {
	queryer := dao.getQueryer()
	return queryTravelerTypeIndexs(queryer, dao.Db.PSQL, q)
}

func queryTravelerTypeIndexs(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string]any) ([]*models.TravelerTypeIndex, error) {
	sql := sb.Select(travelerTypeIndexCols...).From(travelerTypeIndexTable)
	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}
	sql = sql.OrderBy("updated_at DESC")
	sqlstr, args, err := sql.ToSql()
	if err != nil && err != sqllib.ErrNoRows {
		return nil, err
	}

	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var res []*models.TravelerTypeIndex
	for rows.Next() {
		var item models.TravelerTypeIndex
		if err = rows.StructScan(&item); err != nil {
			return nil, err
		}
		res = append(res, &item)
	}
	return res, nil
}

func (dao *Dao) InsertTravelerTypeIndex(item *models.TravelerTypeIndex) error {
	if item == nil {
		return nil
	}

	exec := dao.getExecer()

	sql := `INSERT INTO traveler_type_index (user_id, profile_type, traveler_id, created_at) VALUES ($1,$2,$3,now()) ON CONFLICT ON CONSTRAINT traveler_type_index_profile_type_user_id_key DO UPDATE SET traveler_id = $3, updated_at=now()`
	_, err := exec.Exec(sql, item.UserID, item.ProfileType, item.TravelerID)
	if err != nil {
		return err
	}

	return nil
}
