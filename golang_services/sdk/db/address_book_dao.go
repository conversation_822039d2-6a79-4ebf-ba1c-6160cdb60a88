package db

import (
	sqllib "database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

const (
	AddressBookTable = "address_book"
)

var (
	addressBookCols = []string{
		"id",
		"company_name",
		"recipient_full_name",
		"user_id",
		"traveler_id",
		"address",
		"address_in_native",
		"city",
		"zip_code",
		"state",
		"country",
		"type",
		"ref_name",
		"is_primary_address",
		"extra",
		"status",
		"validated",
		"created_at",
		"updated_at",
	}
)

func (dao *Dao) GetAddressBookItemByID(id int) (*models.AddressBookItem, error) {
	if id <= 0 {
		return nil, nil
	}
	adds, err := queryAddressBookItems(dao.getQueryer(), dao.Db.PSQL, map[string]any{"id": id})
	if err != nil {
		return nil, err
	}
	if len(adds) < 1 {
		return nil, nil
	}
	return adds[0], nil
}

func (dao *Dao) GetAddressBookItemsByTravelerID(travelerID string, q map[string]any) ([]*models.AddressBookItem, error) {
	if len(travelerID) == 0 {
		return nil, nil
	}
	query := map[string]any{"traveler_id": travelerID}
	for k, v := range q {
		switch k {
		case "id", "company_name", "recipient_full_name", "address", "address_in_native", "city", "zip_code", "state", "country",
			"type", "ref_name", "is_primary_address", "status":
			query[k] = v
		}
	}
	return queryAddressBookItems(dao.getQueryer(), dao.Db.PSQL, query)
}

func queryAddressBookItems(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string]any) ([]*models.AddressBookItem, error) {
	sql := sb.Select(addressBookCols...).From("address_book")
	for k, v := range q {
		switch k {
		case "company_name", "recipient_full_name", "address", "address_in_native", "city", "zip_code", "state", "country", "ref_name":
			sql = sql.Where(squirrel.Like{k: v})
		default:
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}
	sql = sql.OrderBy("updated_at DESC")
	sqlstr, args, err := sql.ToSql()
	if err != nil && err != sqllib.ErrNoRows {
		return nil, err
	}
	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var res []*models.AddressBookItem
	for rows.Next() {
		var item models.AddressBookItem
		if err = rows.StructScan(&item); err != nil {
			return nil, err
		}
		res = append(res, &item)
	}
	return res, nil
}

func (dao *Dao) InsertAddressBookItem(item *models.AddressBookItem) (int, error) {
	if item == nil {
		return 0, nil
	}
	if item.UserID == "" {
		return 0, fmt.Errorf("invalid user ID")
	}
	now := time.Now()
	m := map[string]any{
		"company_name":        item.CompanyName,
		"recipient_full_name": item.RecipientFullName,
		"user_id":             item.UserID,
		"traveler_id":         item.TravelerID,
		"address":             item.Address,
		"address_in_native":   item.AddressInNative,
		"city":                item.City,
		"zip_code":            item.ZipCode,
		"state":               item.State,
		"country":             item.Country,
		"type":                item.Type,
		"ref_name":            item.RefName,
		"is_primary_address":  item.IsPrimaryAddress,
		"extra":               item.Extra,
		"status":              item.Status,
		"validated":           item.Validated,
		"created_at":          now,
		"updated_at":          now,
	}
	return insertAddressBookItem(dao.getQueryer(), dao.Db.PSQL, m)
}

func insertAddressBookItem(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, newAddress map[string]any) (int, error) {
	sql := sb.Insert(AddressBookTable).SetMap(newAddress).Suffix("RETURNING id")
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return 0, err
	}
	row := queryer.QueryRowx(sqlstr, args...)
	if row.Err() != nil {
		return 0, row.Err()
	}
	var id int
	if err := row.Scan(&id); err != nil {
		return 0, err
	}
	return id, nil
}

func (dao *Dao) UpdateAddressBookItem(id int, update *models.AddressBookItem) error {
	if update == nil || id <= 0 {
		return nil
	}
	return updateAddressBookItem(dao.getExecer(), dao.Db.PSQL, id, update)
}

func updateAddressBookItem(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id int, update *models.AddressBookItem) error {
	now := time.Now()
	sql := sb.Update(AddressBookTable).SetMap(map[string]any{
		"company_name":        update.CompanyName,
		"recipient_full_name": update.RecipientFullName,
		"address":             update.Address,
		"address_in_native":   update.AddressInNative,
		"city":                update.City,
		"zip_code":            update.ZipCode,
		"state":               update.State,
		"country":             update.Country,
		"type":                update.Type,
		"ref_name":            update.RefName,
		"is_primary_address":  update.IsPrimaryAddress,
		"extra":               update.Extra,
		"validated":           update.Validated,
		"updated_at":          &now,
	}).Where(squirrel.Eq{"id": id})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *Dao) DeactivateAddressBookItem(id int) error {
	if id <= 0 {
		return nil
	}
	return deactivateAddressBookItem(dao.getExecer(), dao.Db.PSQL, id)
}

func deactivateAddressBookItem(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id int) error {
	sqlstr, args, err := sb.Update(AddressBookTable).Set("status", "inactive").Where(squirrel.Eq{"id": id}).ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}
