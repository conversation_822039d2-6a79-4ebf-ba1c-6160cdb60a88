package db

import (
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
)

const (
	documentTable = "document"
)

var (
	documentColumns = []string{
		"id",
		"type",
		"user_id",
		"package_id",
		"template",
		"input",
		"output",
		"status",
		"created_at",
		"updated_at",
	}
)

func (dao *Dao) QueryDocuments(q map[string][]any) ([]*models.Document, error) {
	return queryDocuments(dao.getQueryer(), dao.Db.PSQL, q)
}

func (dao *Dao) GetDocumentByID(id int) (*models.Document, error) {
	if id <= 0 {
		return nil, nil
	}

	q := map[string][]any{"id": {id}}
	res, err := queryDocuments(dao.getQueryer(), dao.Db.PSQL, q)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}

	return res[0], nil
}

func queryDocuments(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any) ([]*models.Document, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := sb.Select(documentColumns...).From(documentTable)
	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}
	sql = sql.OrderBy("updated_at DESC")
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var res []*models.Document
	for rows.Next() {
		var item models.Document
		if err = rows.StructScan(&item); err != nil {
			return nil, err
		}
		res = append(res, &item)
	}

	return res, nil
}

func (dao *Dao) InsertDocument(item *models.Document) (int, error) {
	if item == nil {
		return 0, nil
	}

	newRow := map[string]any{
		"type":       item.Type,
		"user_id":    item.UserID,
		"package_id": item.PackageID,
		"template":   item.Template,
		"input":      item.Input,
		"status":     item.Status,
		"created_at": time.Now(),
		"updated_at": time.Now(),
	}

	sql := dao.Db.PSQL.Insert(documentTable).SetMap(newRow).Suffix("RETURNING id")
	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return 0, err
	}

	row := dao.getQueryer().QueryRowx(sqlStr, args...)
	if row.Err() != nil {
		return 0, nil
	}

	var id int
	if err := row.Scan(&id); err != nil {
		return 0, err
	}

	return id, nil
}

func (dao *Dao) UpdateDocument(id int, update map[string]any) error {
	if id == 0 || len(update) == 0 {
		return nil
	}

	sql := dao.Db.PSQL.Update(documentTable)
	for k, v := range update {
		sql = sql.Set(k, v)
	}
	sql = sql.Set("updated_at", time.Now().UTC())
	sql = sql.Where(squirrel.Eq{"id": id})

	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return err
	}

	_, err = dao.getExecer().Exec(sqlStr, args...)
	if err != nil {
		return err
	}

	return nil
}
