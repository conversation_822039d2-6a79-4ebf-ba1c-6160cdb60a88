package db

import (
	"fmt"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/Masterminds/squirrel"
)

const (
	ricTable = "residence_immigration_check"
)

var (
	ricColumns = []string{
		"nationality",
		"region_of_residence",
		"destination",
		"document",
		"status",
		"length_of_stay",
	}
)

func (dao *Dao) QueryResidenceImmigrationCheck(q map[string]any) ([]*models.ResidenceImmigrationCheck, error) {
	queryer := dao.getQueryer()
	sb := dao.Db.PSQL
	sql := sb.Select(ricColumns...).From(ricTable)
	for k, v := range q {
		switch k {
		case "nationality":
			sql = sql.Where(squirrel.Like{k: fmt.Sprintf("%%%s%%", v)})
		default:
			sql = sql.Where(squirrel.Eq{k: v})
		}
	}

	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.ResidenceImmigrationCheck
	for rows.Next() {
		var p models.ResidenceImmigrationCheck
		if err = rows.StructScan(&p); err != nil {
			return nil, err
		}
		res = append(res, &p)
	}

	return res, nil
}
