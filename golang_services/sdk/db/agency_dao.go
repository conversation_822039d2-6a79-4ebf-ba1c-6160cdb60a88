package db

import (
	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
)

const (
	agencyTable = "agency"
)

var (
	agencyColumns = []string{
		"id",
		"status",
		"name",
		"code",
		"country",
		"address",
		"contact",
		"secondary_contact",
		"timezone_name",
		"created_at",
		"updated_at",
	}
)

func (dao *Dao) GetAgencyByID(id string) (*models.Agency, error) {
	if id == "" {
		return nil, nil
	}
	q := map[string][]any{"id": {id}}

	res, err := queryAgencys(dao.getQueryer(), dao.Db.PSQL, q)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

func queryAgencys(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any) ([]*models.Agency, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := sb.Select(agencyColumns...).From(agencyTable)

	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}
	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := queryer.Queryx(sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.Agency
	for rows.Next() {
		var p models.Agency
		if err = rows.StructScan(&p); err != nil {
			return nil, err
		}
		res = append(res, &p)
	}
	return res, nil
}
