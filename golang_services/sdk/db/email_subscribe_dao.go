package db

import (
	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
)

const (
	emailSubscribeTable = "email_subscribes"
)

func (dao *Dao) GetEmailSubscribe(email string) (*models.EmailSubscribe, error) {
	if email == "" {
		return nil, nil
	}

	q := map[string][]any{"email": {email}}

	res, err := queryEmailSubscribes(dao.getQueryer(), dao.Db.PSQL, q)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}
func queryEmailSubscribes(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string][]any) ([]*models.EmailSubscribe, error) {
	if len(q) == 0 {
		return nil, nil
	}
	sql := sb.Select("*").From(emailSubscribeTable)

	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}
	sqlStr, args, err := sql.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := queryer.Queryx(sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var res []*models.EmailSubscribe
	for rows.Next() {
		var p models.EmailSubscribe
		if err = rows.StructScan(&p); err != nil {
			return nil, err
		}
		res = append(res, &p)
	}
	return res, nil
}
