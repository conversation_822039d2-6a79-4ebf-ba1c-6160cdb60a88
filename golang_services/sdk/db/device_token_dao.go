package db

import (
	"fmt"

	"bitbucket.org/persistence17/aria/golang_services/models/device"
	"github.com/Masterminds/squirrel"
)

const (
	deviceTokenTable = "device_tokens"
	userDeviceTable  = "user_device_tokens"
)

type DeviceTokenDao struct {
	conn *AuroraDB
}

func NewDeviceTokenDao(conn *AuroraDB) *DeviceTokenDao {
	return &DeviceTokenDao{
		conn: conn,
	}
}

func (d *DeviceTokenDao) InsertOrUpdateDeviceToken(dt *device.DeviceToken) error {
	q := d.conn.PSQL.Insert(deviceTokenTable).SetMap(map[string]any{
		"token":      dt.Token,
		"created_at": dt.CreatedAt,
		"updated_at": dt.UpdatedAt,
	}).Suffix("ON CONFLICT (token) DO UPDATE set updated_at=EXCLUDED.updated_at WHERE device_tokens.token=EXCLUDED.token")
	sql, args, err := q.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build query for InsertOrUpdateDeviceToken, %w", err)
	}
	_, err = d.conn.Db.Exec(sql, args...)
	if err != nil {
		return fmt.Errorf("failed to InsertOrUpdateDeviceToken, err %w", err)
	}
	return nil
}

func (d *DeviceTokenDao) UpdateDeviceToken(dt *device.DeviceToken) error {
	q := d.conn.PSQL.Update(deviceTokenTable).Set("updated_at", dt.UpdatedAt).Where(squirrel.Eq{"token": dt.Token})
	sql, args, err := q.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build query for UpdateDeviceToken, %w", err)
	}
	_, err = d.conn.Db.Exec(sql, args...)
	if err != nil {
		return fmt.Errorf("failed to UpdateDeviceToken, err %w", err)
	}
	return nil
}

func (d *DeviceTokenDao) GetDeviceToken(token string) (*device.DeviceToken, error) {
	panic("implement me")
}

func (d *DeviceTokenDao) InsertOrUpdateUserDeviceToken(udt *device.UserDeviceToken) error {
	q := d.conn.PSQL.Insert(userDeviceTable).SetMap(map[string]any{
		"device_id":  udt.DeviceID,
		"token":      udt.Token,
		"user_id":    udt.UserID,
		"created_at": udt.CreatedAt,
		"updated_at": udt.UpdatedAt,
	}).Suffix("ON CONFLICT (device_id, token, user_id) DO UPDATE set updated_at=EXCLUDED.updated_at, token=EXCLUDED.token")
	sql, args, err := q.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build query for InsertOrUpdateUserDeviceToken, %w", err)
	}
	_, err = d.conn.Db.Exec(sql, args...)
	if err != nil {
		return fmt.Errorf("failed to InsertOrUpdateUserDeviceToken, err %w", err)
	}
	return nil
}

func (d *DeviceTokenDao) UpdateUserDeviceToken(udt *device.UserDeviceToken) error {
	q := d.conn.PSQL.Update(userDeviceTable).SetMap(map[string]any{
		"updated_at": udt.UpdatedAt,
		"user_id":    udt.UserID,
	}).Where(squirrel.Eq{"token": udt.Token})
	sql, args, err := q.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build query for UpdateUserDeviceToken, %w", err)
	}
	_, err = d.conn.Db.Exec(sql, args...)
	if err != nil {
		return fmt.Errorf("failed to UpdateUserDeviceToken, err %w", err)
	}
	return nil
}

func (d *DeviceTokenDao) DeleteUserDeviceToken(userID, deviceID string) error {
	q := d.conn.PSQL.Delete(userDeviceTable).Where(squirrel.Eq{"device_id": deviceID}).Where(squirrel.Eq{"user_id": userID})
	sql, args, err := q.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build query for DeleteUserDeviceToken, %w", err)
	}
	_, err = d.conn.Db.Exec(sql, args...)
	if err != nil {
		return fmt.Errorf("failed to DeleteUserDeviceToken, err %w", err)
	}
	return nil
}

func (d *DeviceTokenDao) GetUserDeviceToken(userID, token string) (*device.UserDeviceToken, error) {
	panic("implement me")
}

func (d *DeviceTokenDao) GetUserDeviceTokenByUserID(userID string) ([]*device.UserDeviceToken, error) {
	q := d.conn.PSQL.Select("device_id", "token", "user_id", "created_at", "updated_at").From(userDeviceTable).
		Where(squirrel.Eq{"user_id": userID})
	sql, args, err := q.ToSql()
	if err != nil {
		return nil, fmt.Errorf("failed to build query for GetUserDeviceTokenByUserID, %w", err)
	}
	rows, err := d.conn.Db.Queryx(sql, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to GetUserDeviceTokenByUserID, err %w", err)
	}
	defer rows.Close()
	var res []*device.UserDeviceToken
	for rows.Next() {
		var ud device.UserDeviceToken
		if err := rows.StructScan(&ud); err != nil {
			return nil, fmt.Errorf("failed to parse result from database for GetUserDeviceTokenByUserID, err %w", err)
		}
		res = append(res, &ud)
	}
	return res, nil
}
