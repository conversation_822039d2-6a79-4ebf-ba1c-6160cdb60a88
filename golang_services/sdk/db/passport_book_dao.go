package db

import (
	sqllib "database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

const (
	PassportBookTable = "passport_book"
)

var (
	passportBookCols = []string{
		"id",
		"user_id",
		"traveler_id",
		"given_name",
		"surname",
		"middle_name",
		"gender",
		"nationality",
		"date_of_birth",
		"expiration_date",
		"issue_date",
		"country_issuing_authority",
		"country_of_birth",
		"city_of_birth",
		"is_primary",
		"remind_renew_enabled",
		"remind_renew_email",
		"created_at",
		"updated_at",
		"passport_image",
	}
)

func (dao *Dao) GetPassportBookByID(id int) (*models.PassportBook, error) {
	if id <= 0 {
		return nil, nil
	}
	adds, err := queryPassportBooks(dao.getQueryer(), dao.Db.PSQL, map[string]any{"id": id})
	if err != nil {
		return nil, err
	}
	if len(adds) < 1 {
		return nil, nil
	}
	return adds[0], nil
}

func (dao *Dao) QueryPassportBook(q map[string]any) ([]*models.PassportBook, error) {
	queryer := dao.getQueryer()
	return queryPassportBooks(queryer, dao.Db.PSQL, q)
}

func queryPassportBooks(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, q map[string]any) ([]*models.PassportBook, error) {
	sql := sb.Select(passportBookCols...).From(PassportBookTable)
	for k, v := range q {
		sql = sql.Where(squirrel.Eq{k: v})
	}
	sql = sql.OrderBy("updated_at DESC")
	sqlstr, args, err := sql.ToSql()
	if err != nil && err != sqllib.ErrNoRows {
		return nil, err
	}

	rows, err := queryer.Queryx(sqlstr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var res []*models.PassportBook
	for rows.Next() {
		var item models.PassportBook
		if err = rows.StructScan(&item); err != nil {
			return nil, err
		}
		res = append(res, &item)
	}
	return res, nil
}

func (dao *Dao) InsertPassportBook(item *models.PassportBook) (int, error) {
	if item == nil {
		return 0, nil
	}
	if item.UserID == "" {
		return 0, fmt.Errorf("invalid user ID")
	}
	now := time.Now()
	m := map[string]any{
		"user_id":                   item.UserID,
		"traveler_id":               item.TravelerID,
		"given_name":                item.GivenName,
		"surname":                   item.Surname,
		"middle_name":               item.MiddleName,
		"gender":                    item.Gender,
		"nationality":               item.Nationality,
		"date_of_birth":             item.DateOfBirth,
		"expiration_date":           item.ExpirationDate,
		"issue_date":                item.IssueDate,
		"country_of_birth":          item.CountryOfBirth,
		"city_of_birth":             item.CityOfBirth,
		"country_issuing_authority": item.CountryIssuingAuthority,
		"is_primary":                item.IsPrimary,
		"remind_renew_enabled":      item.RemindRenewEnabled,
		"remind_renew_email":        item.RemindRenewEmail,
		"created_at":                now,
		"updated_at":                now,
		"passport_image":            item.PassportImage,
	}
	return insertPassportBook(dao.getQueryer(), dao.Db.PSQL, m)
}

func insertPassportBook(queryer sqlx.Queryer, sb *squirrel.StatementBuilderType, newPassportBook map[string]any) (int, error) {
	sql := sb.Insert(PassportBookTable).SetMap(newPassportBook).Suffix("RETURNING id")
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return 0, err
	}
	row := queryer.QueryRowx(sqlstr, args...)
	if row.Err() != nil {
		return 0, row.Err()
	}
	var id int
	if err := row.Scan(&id); err != nil {
		return 0, err
	}
	return id, nil
}

func (dao *Dao) UpdatePassportBook(id int, update *models.PassportBook) error {
	if update == nil || id <= 0 {
		return nil
	}
	return updatePassportBook(dao.getExecer(), dao.Db.PSQL, id, update)
}

func updatePassportBook(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id int, update *models.PassportBook) error {
	now := time.Now()
	sql := sb.Update(PassportBookTable).SetMap(map[string]any{
		"given_name":                update.GivenName,
		"surname":                   update.Surname,
		"middle_name":               update.MiddleName,
		"gender":                    update.Gender,
		"nationality":               update.Nationality,
		"date_of_birth":             update.DateOfBirth,
		"expiration_date":           update.ExpirationDate,
		"issue_date":                update.IssueDate,
		"country_of_birth":          update.CountryOfBirth,
		"city_of_birth":             update.CityOfBirth,
		"country_issuing_authority": update.CountryIssuingAuthority,
		"is_primary":                update.IsPrimary,
		"remind_renew_enabled":      update.RemindRenewEnabled,
		"remind_renew_email":        update.RemindRenewEmail,
		"last_remind":               update.LastRemind,
		"updated_at":                &now,
		"passport_image":            update.PassportImage,
	}).Where(squirrel.Eq{"id": id})
	sqlstr, args, err := sql.ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}

func (dao *Dao) DeletePassportBook(id int) error {
	if id <= 0 {
		return nil
	}
	return deletePassportBook(dao.getExecer(), dao.Db.PSQL, id)
}

func deletePassportBook(execer sqlx.Execer, sb *squirrel.StatementBuilderType, id int) error {
	sqlstr, args, err := sb.Delete(PassportBookTable).Where(squirrel.Eq{"id": id}).ToSql()
	if err != nil {
		return err
	}
	_, err = execer.Exec(sqlstr, args...)
	if err != nil {
		return err
	}
	return nil
}
