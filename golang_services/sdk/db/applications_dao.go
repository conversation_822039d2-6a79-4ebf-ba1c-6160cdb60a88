package db

import (
	"time"

	"github.com/Masterminds/squirrel"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

// GetApplicationValidateResult get last validate result
func (dao *Dao) GetApplicationValidateResult(id int) (*models.AppValidateResult, error) {
	var query = dao.Db.PSQL.Select("result_ts, outputs").From("atlas_result").
		Where("application_id = ?", id).
		Where(squirrel.Eq{"service_type": []string{"d10001", "d10002", "d10003"}}). //visa type
		OrderBy("request_ts DESC").
		Limit(1)

	sqlStr, args, _ := query.ToSql()
	rows, err := dao.getQueryer().Queryx(sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	if rows.Next() {
		var (
			resultTS *time.Time
			result   *models.AppValidateResult
		)

		if err := rows.Scan(&resultTS, &result); err != nil {
			return nil, err
		}

		if result == nil && resultTS == nil {
			return &models.AppValidateResult{
				Status: "PROCESSING",
			}, nil
		}
		return result, nil
	}
	return nil, nil
}

func (dao *Dao) CleanApplicationValidateResult(id int) error {
	if _, err := dao.Db.PSQL.Delete("atlas_result").
		Where("application_id = ?", id).RunWith(dao.Db.Db).Exec(); err != nil {
		return err
	}

	if _, err := dao.Db.PSQL.Update("application").
		Set("pod_validation", "{}").
		Where("id = ?", id).RunWith(dao.Db.Db).Exec(); err != nil {
		return err
	}

	return nil
}
