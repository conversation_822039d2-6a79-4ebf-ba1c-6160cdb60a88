package db

import (
	"fmt"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

const (
	visaPodTable = "visa_pods"
)

var (
	visaPodColumns = []string{"id", "name", "category", "sub_category", "body", "status", "title", `"order"`, "created_at", "updated_at"}
)

// GetVisaPodByID get visa pod detail by id
func (dao *Dao) GetVisaPodByID(id int) (*models.PodExtSchema, error) {
	var query = dao.Db.PSQL.Select(visaPodColumns...).From(visaPodTable).Where("id = ?", id)

	sqlStr, args, _ := query.ToSql()
	rows, err := dao.getQueryer().Queryx(sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// Get data
	var result = new(models.PodExtSchema)

	if rows.Next() {
		if err := rows.StructScan(&result); err != nil {
			return nil, err
		}
	}

	return result, nil
}

// GetVisaPodList get visa pod list
func (dao *Dao) GetVisaPodList(req models.VisaPodFilter) (*models.VisaPodFilterResponse, error) {
	var query = dao.Db.PSQL.Select(visaPodColumns...).
		From(visaPodTable)

	if req.ID != "" {
		query = query.Where("id::text LIKE ?", "%"+req.ID+"%")
	}

	if req.Title != "" {
		query = query.Where("title LIKE ?", "%"+req.Title+"%")
	}

	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	if req.Tags != "" {
		query = query.Where("tags = ?", req.Tags)
	}

	if req.Category != "" {
		query = query.Where("category = ?", req.Category)
	}

	if req.Country != "" {
		query = query.Where("body->'attributes'->>'country'::text = ?", req.Country)
	}

	if req.RegionOfResidence != "" {
		query = query.Where("body->'attributes'->>'region_of_residence'::text = ?", req.RegionOfResidence)
	}

	if req.Destination != "" {
		query = query.Where("body->'attributes'->>'destination'::text = ?", req.Destination)
	}

	if req.Nationality != "" {
		query = query.Where("body->'attributes'->>'nationality'::text = ?", req.Nationality)
	}

	if utils.Contain(req.SortField, []string{"created_at", "id", "title", "name", "category", "sub_category", "order"}) {
		query = query.OrderBy(fmt.Sprintf("%s %s", req.SortField, utils.IfThenElse(req.SortOrder == "desc", "DESC", "ASC")))
	}

	if req.Limit != 0 {
		query = query.Limit(req.Limit).Offset(req.Offset)
	}

	// Get data
	var res = models.VisaPodFilterResponse{}

	sqlStr, args, _ := query.ToSql()
	if err := dao.Db.Db.Select(&res.Data, sqlStr, args...); err != nil {
		return nil, err
	}

	// Get total count
	sqlCount := utils.QueryCountItems(sqlStr)
	rowCount, err := dao.getQueryer().Queryx(sqlCount, args...)
	if err != nil {
		return nil, err
	}
	defer rowCount.Close()

	if rowCount.Next() {
		if err := rowCount.Scan(&res.Total); err != nil {
			return nil, err
		}
	}

	return &res, nil
}
