package db

import (
	"bitbucket.org/persistence17/aria/golang_services/models"
)

func (dao *Dao) GetIDPhotoDocumentList(country string) ([]models.IDPhotoDocument, error) {
	rows, err := dao.Db.PSQL.Select("id, country_code_alpha2, country_code_alpha3, name, dimensions, width, height, bg").
		From("id_photo_documents").Where("country_code_alpha3 = ?", country).
		RunWith(dao.Db.Db).Query()
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	result := []models.IDPhotoDocument{}
	for rows.Next() {
		item := models.IDPhotoDocument{}
		rows.Scan(
			&item.ID,
			&item.CountryCodeAlpha2,
			&item.CountryCodeAlpha3,
			&item.Name,
			&item.Dimensions,
			&item.Width,
			&item.Height,
			&item.BG,
		)

		result = append(result, item)
	}
	return result, nil
}
