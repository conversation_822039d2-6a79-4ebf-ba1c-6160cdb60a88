package db

import (
	"encoding/json"
	"net/url"

	"bitbucket.org/persistence17/aria/golang_services/models"
	uam "bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"github.com/jmoiron/sqlx"
)

type Dao struct {
	Db *AuroraDB
	Tx *sqlx.Tx
}

type IPaymentsDao interface {
	InsertPayment(payment *models.Payment) error

	UpdatePayment(payment *models.Payment) error

	GetPayment(id string) (*models.Payment, error)

	GetPaymentByExternalID(id string) (*models.Payment, error)

	GetPaymentList(models.PaymentListRequest) ([]models.Payment, error)

	QueryPayments(query url.Values) ([]*models.Payment, error)
}

type IDao interface {
	GetApplicationValidateResult(id int) (*models.AppValidateResult, error)
	CleanApplicationValidateResult(id int) error

	// user
	InsertUser(user *models.User) (*string, error)
	GetUserByID(id string) (*models.User, error)
	GetUserByEmail(email string) (*models.User, error)

	// Email template
	GetEmailTemplateByName(name, language string) (*models.EmailTemplate, error)

	DB() *Dao
	Begin() error
	Commit() error
	Rollback() error

	//visa customer price
	QueryVisaCustomerPrice(q map[string][]any) ([]*models.VisaCustomerPrice, error)

	//package shipment
	GetVisaShipment(id string) (*models.VisaShipment, error)
	InsertVisaShipment(spmt *models.VisaShipment) (string, error)
	UpdateVisaShipment(id string, update *models.VisaShipment) error

	//visa pod
	GetVisaPodByID(int) (*models.PodExtSchema, error)
	GetVisaPodList(models.VisaPodFilter) (*models.VisaPodFilterResponse, error)

	//visa pod request
	// FetchNewPodID(p *models.PodExtSchema) error
	FetchNewPodOrder(p *models.PodExtSchema) error
	InsertVisaPodRequest(*models.VisaPodRequest) error
	UpdateVisaPodRequest(int64, map[string]any) error
	DeleteVisaPodRequest(int64) error
	RevertVisaPodRequest(*models.VisaPodRequest) error
	UpdateVisaPodRequestStatus(*models.VisaPodRequest) error
	GetVisaPodRequestByID(int64) (*models.VisaPodRequest, error)
	GetVisaPodRequestList(models.VisaPodRequestFilter) (*models.VisaPodRequestFilterResponse, error)
	GetVisaPodMetaData() (any, error)

	QueryExtendServices(q map[string][]any) ([]*models.ExtendedService, error)

	// consulate
	GetConsulateByID(id string) (*models.Consulate, error)
	GetConsulateByOrgID(id int64) (*models.Consulate, error)
	QueryConsulates(q map[string][]any) ([]*models.Consulate, error)

	// corporation
	GetCorporationByID(id string) (*models.Corporation, error)
	GetCorporationByOrgID(id int64) (*models.Corporation, error)
	GetCorporations(q map[string][]any) ([]*models.Corporation, error)

	// organization
	// GetOrganizationByID(id int64) (*models.Organization, error)

	// agency
	GetAgencyByID(id string) (*models.Agency, error)

	// shipping label
	QueryShippingLabels(q map[string][]any) ([]*models.ShippingLabel, error)
	// visa check
	QueryVisaChecks(q map[string][]any) ([]*models.VisaCheck, error)

	// shipping services
	GetShippingServices(q map[string][]any) ([]*models.ShippingService, error)
	GetShippingServiceByID(id int64) (*models.ShippingService, error)

	// contact book
	GetContactBookItemByID(id int) (*models.ContactBookItem, error)
	GetContactBookItemsByTravelerID(traverlerID string, q map[string]any) ([]*models.ContactBookItem, error)
	InsertContactBookItem(item *models.ContactBookItem) (int, error)
	UpdateContactBookItem(id int, update *models.ContactBookItem) error
	DeactivateContactBookItem(id int) error

	// corporation team member
	GetTeamMemberByUserID(userID string) (*uam.CorporationTeamMember, error)
	GetTeamMemberIDsByManager(managerID string) ([]string, error)
	GetTeamRoleByUserID(userID string) (string, error)

	// address book
	GetAddressBookItemByID(id int) (*models.AddressBookItem, error)
	GetAddressBookItemsByTravelerID(travelerID string, q map[string]any) ([]*models.AddressBookItem, error)
	InsertAddressBookItem(item *models.AddressBookItem) (int, error)
	UpdateAddressBookItem(id int, update *models.AddressBookItem) error
	DeactivateAddressBookItem(id int) error

	// passport book
	QueryPassportBook(q map[string]any) ([]*models.PassportBook, error)
	InsertPassportBook(item *models.PassportBook) (int, error)
	GetPassportBookByID(id int) (*models.PassportBook, error)
	UpdatePassportBook(id int, update *models.PassportBook) error
	DeletePassportBook(id int) error

	// visa book
	QueryVisaBook(q map[string]any) ([]*models.VisaBook, error)
	InsertVisaBook(item *models.VisaBook) (int, error)
	GetVisaBookByID(id int) (*models.VisaBook, error)
	UpdateVisaBook(id int, update *models.VisaBook) error
	DeleteVisaBook(id int) error

	// membership
	QueryMembership(q map[string]any) ([]*models.Membership, error)
	InsertMembership(item *models.Membership) (int, error)
	GetMembershipByID(id int) (*models.Membership, error)
	UpdateMembership(id int, update *models.Membership) error
	DeactivateMembership(id int) error
	QueryMembershipProvider(q map[string]any) ([]*models.MembershipProvider, error)

	// fastTrack
	QueryFastTrack(q map[string]any) ([]*models.FastTrack, error)
	InsertFastTrack(item *models.FastTrack) (int, error)
	GetFastTrackByID(id int) (*models.FastTrack, error)
	UpdateFastTrack(id int, update *models.FastTrack) error
	DeleteFastTrack(id int) error

	// traveler
	InsertTraveler(item *models.Traveler) (string, error)
	GetTravelerByID(id string) (*models.Traveler, error)
	QueryTravelers(q map[string][]any) ([]*models.Traveler, error)
	UpdateTraveler(id string, update map[string]any) error
	DeleteTraveler(id string) error
	QueryTravelersWithType(userID string, profileType string) ([]*models.Traveler, error)
	// traveler type index
	QueryTravelerTypeIndex(q map[string]any) ([]*models.TravelerTypeIndex, error)
	InsertTravelerTypeIndex(item *models.TravelerTypeIndex) error

	// validation orders
	CreateValidationOrder(input *models.ValidationOrder) (int, error)
	UpdateValidationOrder(input *models.ValidationOrder) error
	AddInputFileToValidationOrder(id int, files ...*models.InputFile) error
	QueryValidationOrders(q map[string]any, limit, offset uint) ([]*models.ValidationOrder, error)

	// document
	QueryDocuments(q map[string][]any) ([]*models.Document, error)
	GetDocumentByID(id int) (*models.Document, error)
	InsertDocument(item *models.Document) (int, error)
	UpdateDocument(id int, update map[string]any) error

	// Cart
	GetCartList(filter models.CartFilter) (models.CartFilterData, error)
	GetUserCart(userID string) (*models.CartData, error)
	GetCartByID(cartID int) (*models.CartData, error)
	GetCartByPayment(paymentID string) (*models.CartData, error)
	CreateCart(*models.Cart) error
	UpdateCart(int, map[string]any) error
	CheckItemBeforeAddToCart(item models.CartItem) error
	AddItemToCart(*models.CartItem) error
	RemoveItemFromCart(int) error
	GetCartByPaymentID(paymentID string) (*models.Cart, error)
	GetGroupItems(req models.CartGroupItemRequest) (*models.CartGroupItemResponse, error)
	GetGroupItemsHeader(req models.CartGroupItemRequest) (map[string]int, error)
	GetGroupFilterItem(req models.CartGroupItemRequest) (*models.GroupFilterItemResponse, error)

	UpdateCartItem(any, map[string]any) error
	// Cart Item
	GetCartItemByCardID(cartID int) ([]*models.CartItem, error)
	// Exchange rate
	GetExchangeRate(rate *models.ExchangeRate) error

	// ric
	QueryResidenceImmigrationCheck(q map[string]any) ([]*models.ResidenceImmigrationCheck, error)

	// Shorten url
	CreateShortenLink(item *models.ShortenLink) error
	GetShortenLink(code string) (*models.ShortenLink, error)

	// Invoice
	GetInvoiceList(req models.InvoiceQueryReq) (*models.InvoiceQueryRes, error)
	GetInvoice(id int) (*models.CorporationInvoice, error)
	UpdateInvoice(req models.CorporationInvoice) error
	DeleteInvoice(id int) error

	// ID photo
	GetIDPhotoDocumentList(country string) ([]models.IDPhotoDocument, error)

	//Report
	GetReportList(req models.AdminReportReq) (*models.AdminReportQueryRes, error)
	DeleteReport(id int) error

	GetPromotionCodes() (models.PromotionCouponList, error)
}

func NewDaoWithDb(db *AuroraDB) *Dao {
	return &Dao{Db: db}
}

func (dao *Dao) getQueryer() sqlx.Queryer {
	if dao.Tx != nil {
		return dao.Tx
	}
	return dao.Db.Db
}

func (dao *Dao) getExecer() sqlx.Execer {
	if dao.Tx != nil {
		return dao.Tx
	}
	return dao.Db.Db
}

func (dao *Dao) DB() *Dao {
	return dao
}

func (dao *Dao) Begin() error {
	if dao.Tx != nil {
		return nil
	}
	tx, err := dao.Db.Db.Beginx()
	if err != nil {
		return err
	}
	dao.Tx = tx
	return nil
}

func (dao *Dao) Commit() error {
	if dao.Tx == nil {
		return nil
	}
	if err := dao.Tx.Commit(); err != nil {
		return err
	}
	dao.Tx = nil
	return nil
}

func (dao *Dao) Rollback() error {
	if dao.Tx == nil {
		return nil
	}
	if err := dao.Tx.Rollback(); err != nil {
		return err
	}
	dao.Tx = nil
	return nil
}

type IAtlasDao interface {
	CreateAtlasTask(tasks []*models.AtlasTask) error
	QueryAtlasTask(q map[string]any) ([]*models.AtlasTask, error)
	UpdateAtlasTask(id string, updates *models.AtlasTask) error
	GetLatestAtlasResult(pkgID int, appIDs []int, requestIDs, st []string) ([]*models.AtlasTask, error)

	CreateETSAtlasTask(tasks []*models.ETSAtlasTask) error
	QueryETSAtlasTask(q map[string]any) ([]*models.ETSAtlasTask, error)
	UpdateETSAtlasTask(id string, updates *models.ETSAtlasTask) error
	GetLatestETSAtlasResult(orderID int, tasks []string, requestIDs, st []string) ([]*models.ETSAtlasTask, error)

	CreateAtlasValidation(tasks []*models.AtlasValidation) error
	QueryAtlasValidations(q map[string]any) ([]*models.AtlasValidation, error)
	UpdateAtlasValidation(reqID string, update *models.AtlasValidation) error
	GetLatestAtlasValidations(orgID int, orderIDs []int, st []string) ([]*models.AtlasValidation, error)

	Begin() error
	Commit() error
	Rollback() error
}

type IEtsDao interface {
	QueryExtendedTravelServices(q map[string]any, offset, limit uint) ([]*models.ExtendedTravelService, error)
	QueryExtendedTravelServicesByOrderIDs(soIDs []int) (map[int]*models.ExtendedTravelService, error)
	GetServiceProviders(svcIDs []int) (map[int][]*models.ServiceProvider, error)
	QueryServiceOrders(req models.ServiceOrderFilter) (*models.ServiceOrderResponse, error)
	CreateServiceOrder(o *models.ServiceOrder) error
	UpdateServiceOrder(update map[string]any, id int) error
	DeleteServiceOrder(id []string) error
	QueryServiceTasks(q map[string]any, offset, limit uint) ([]*models.ServiceTask, int, error)
	DeleteETSTask(id []string) error
	QueryServiceProviderTasks(req models.ServiceTaskWithETSFilter) (*models.ServiceTaskWithETSData, error)
	CreateServiceTask(task *models.ServiceTask) error
	UpdateServiceTask(update map[string]any, id int64) error
	GetServiceOrderByID(id int) (*models.ServiceOrder, error)
	GetServiceOrders(q map[string][]any) ([]*models.ServiceOrder, error)
	GetServicePrice(ids []int) (map[int][]*models.EtsPrice, error)
	UpdateServiceTaskStatus(toStatus string, ids []int64) error
	UpdateServiceTaskStatusByOrderID(toStatus string, orderID int64) error
	GetEtsProviderByID(id string) (*models.EtsProvider, error)
	GetEtsProviders() ([]*models.EtsProvider, error)
	GetEtsProviderByOrgID(id int64) (*models.EtsProvider, error)
	QueryServiceRelateTasks(ids []int64) ([]*models.ServiceTask, error)
	QueryEtsPod(q map[string]any) ([]*models.EtsPod, error)
	QueryEtsQueryPod(q map[string]any) ([]*models.EtsQueryPod, error)
	AddETSOutputFiles(id int, update map[string]string) error
	QueryEtsTask(q map[string]any) ([]string, error)
	QueryEts(q map[string]any) ([]models.ExtendedTravelService, error)
	CleanEtsValidateResult(id int) error
	GetServiceType() ([]models.ServiceType, error)
	GetShippingServiceByID(id int64) (*models.ShippingService, error)
	GetProfileDataByCondition(userID, table, field, condition, value string) (any, error)
	GetUserCorpDataByCondition(userID string, field string) (any, error)
	AddETSTaskOutputFiles(id int64, update map[string]string) error
	DeleteETSTaskOutputFiles(taskId int64, key string) error
	GetServiceOrderStaffs(orderID int64, staffID string) ([]models.ServiceOrderStaff, error)
	GetCountServiceOrderByStaffs(staffIDs []string) ([]string, error)
	UpdateServiceOrderStaff(orderID int64, staffID, code string, userID string, note json.RawMessage) error
	DeleteServiceOrderStaff(orderID int64, staffID string) error
	StaffConfirmHandleServiceOrder(orderID int64, note json.RawMessage, code, status string) error
	GetServiceOrderStaffByCode(orderID int64, code string) (*models.ServiceOrderStaff, error)
	GetStaffsByOrderID(orderID int64) ([]models.ServiceOrderStaff, error)
	GetETSByID(etsID int) (*models.ExtendedTravelService, error)
	QueryServiceCustomerData(userID string, queryStr string) ([]models.ServiceCustomerData, error)
	GetServiceCustomerData(passportNumber string, userID string) (map[string]any, error)
	CreateServiceCustomerData(userID, appName, passportNumber string, inputPodValues map[string]any) error
	VisaDB() *Dao
	Begin() error
	Commit() error
	Rollback() error
}
