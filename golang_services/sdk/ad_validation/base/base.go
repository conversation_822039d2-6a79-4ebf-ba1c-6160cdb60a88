package base

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/dlclark/regexp2"
	"github.com/rs/zerolog/log"
	"github.com/thoas/go-funk"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

type InputPodsValidator struct {
	inputPods *models.InputPods
}

type InputPodValidator struct {
	Pod *models.InputPod
}

type OptionPodValidator struct {
	Pod *models.InputPod
}

func NewInputPodsValidator(input *models.InputPods) *InputPodsValidator {
	return &InputPodsValidator{inputPods: input}
}

func (pod *InputPodValidator) Validate(input any) (bool, error) {
	if pod == nil || pod.Pod == nil {
		return true, nil
	}

	if pod.Pod.PodType == models.PodTypeOutPut {
		return true, nil
	}

	if strings.Contains(pod.Pod.UI, "noshow") {
		return true, nil
	}

	if pod.Pod.Category == "worker" {
		return true, nil
	}

	if pod.Pod.TypeFE == "warning" {
		return true, nil
	}

	switch pod.Pod.Type {
	case "string":
		if valid, err := validateStringVal(pod.Pod); !valid {
			return valid, err
		}
	case "boolean":
		if valid, err := validateBoolVal(pod.Pod); !valid {
			return valid, err
		}
	case "random":
		return true, nil
	case "array":
		return true, nil
	case "integer":
		if valid, err := validateIntegerVal(pod.Pod); !valid {
			return valid, err
		}
	case "date", "datetime", "date-time":
		if valid, err := validateDateTimeVal(pod.Pod); !valid {
			return valid, err
		}
	default:
		// unknown type, ignore
		return true, nil
	}
	return true, nil
}

func (opt *OptionPodValidator) Validate(input any) (bool, error) {
	if opt == nil || opt.Pod == nil {
		return true, nil
	}

	if opt.Pod.PodType == models.PodTypeOutPut {
		return true, nil
	}

	if strings.Contains(opt.Pod.UI, "noshow") {
		return true, nil
	}

	if opt.Pod.Category == "worker" {
		return true, nil
	}

	if opt.Pod.TypeFE == "warning" {
		return true, nil
	}

	switch opt.Pod.Type {
	case "string":
		if valid, err := validateOpPodStringVal(opt.Pod); !valid {
			return valid, err
		}
	case "boolean":
		if valid, err := validateOpPodBoolVal(opt.Pod); !valid {
			return valid, err
		}
	case "random":
		return true, nil
	case "array":
		return true, nil
	case "integer":
		if valid, err := validateOpPodIntegerVal(opt.Pod); !valid {
			return valid, err
		}
	case "date", "datetime", "date-time":
		if valid, err := validateOpPodDateTimeVal(opt.Pod); !valid {
			return valid, err
		}
	default:
		// unknown type, ignore
		return true, nil
	}
	return true, nil
}

func (v *InputPodsValidator) Validate(input any) (bool, error) {
	for _, onePod := range *v.inputPods {
		validator := &InputPodValidator{Pod: onePod}
		if valid, err := validator.Validate(input); !valid {
			return valid, err
		}
	}
	return true, nil
}

func validateBoolVal(input *models.InputPod) (bool, error) {
	if input == nil {
		return true, nil
	}
	if !input.Optional && (input.Value == nil || input.Value.FE == nil) {
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s is required", input.Name), "", input.ID)
	}
	value, ok := input.Value.FE.(bool)
	if !ok {
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s value is not a boolean value", input.Name), "", input.ID)
	}
	var choices []*models.InputPod
	if value {
		choices = input.OptionChoice["true"]
	} else {
		choices = input.OptionChoice["false"]
	}
	if len(choices) == 0 {
		return true, nil
	}
	for _, choice := range choices {
		validator := &OptionPodValidator{Pod: choice}
		if valid, err := validator.Validate(choice); !valid {
			return valid, err
		}
	}
	return true, nil
}

func validateStringVal(input *models.InputPod) (bool, error) {
	if input == nil {
		return true, nil
	}

	if input.Optional {
		return true, nil
	}

	if input.Value == nil || input.Value.FE == nil {
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s is required", input.Name), "", input.ID)
	}

	if input.Value == nil || input.Value.FE == nil {
		return true, nil
	}

	switch reflect.TypeOf(input.Value.FE).Kind() {
	case reflect.String:
		value, ok := input.Value.FE.(string)
		if !ok {
			return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
				fmt.Sprintf("%s value is not a string", input.Name), "", input.ID)
		}

		if len(input.OptionList) > 0 {
			// check if value is a valid option
			validOption := false
			for _, op := range input.OptionList {
				opStr, isStr := op.(string)
				if !isStr {
					continue
				}
				if opStr == value {
					validOption = true
				}
			}
			if !validOption {
				// if not a valid option, return false
				return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
					fmt.Sprintf("%s must be one of %v", input.Name, input.OptionList), "", input.ID)
			}
			choices, ok := input.OptionChoice[value]
			if !ok {
				return true, nil
			}
			for _, choice := range choices {
				validator := &OptionPodValidator{Pod: choice}
				if valid, err := validator.Validate(choice); !valid {
					return valid, err
				}
			}
		}
		if input.MinLength > 0 {
			if len(value) < input.MinLength {
				switch input.Name {
				case "exit_flight":
					return false, errors.ExitFlightNumberFmtErr
				case "enter_flight":
					return false, errors.EnterFlightNumberFmtErr
				default:
					return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
						fmt.Sprintf("%s should not be shorter than %d", input.Name, input.MinLength), "", input.ID)
				}
			}
		}

		if input.MaxLength > 0 {
			if len(value) > input.MaxLength {
				return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
					fmt.Sprintf("%s should not be longer than %d", input.Name, input.MaxLength), "", input.ID)
			}
		}

		if input.Optional && value == "" {
			return true, nil
		}

		if len(input.Pattern) > 0 {
			r, err := regexp2.Compile(input.Pattern, regexp2.RE2)
			if err != nil {
				log.Warn().Str("pattern", input.Pattern).Str("pod", input.ID).Str("error", err.Error()).Msg("invalid pattern")
				return true, nil
			}
			if match, _ := r.MatchString(value); !match {
				return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
					fmt.Sprintf("%s should match pattern %s", input.Name, input.Pattern), "", input.ID)
			}
		}
	case reflect.Slice:
	default:
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s value is not a string", input.Name), "", input.ID)
	}

	return true, nil
}

func validateIntegerVal(input *models.InputPod) (bool, error) {
	if input == nil {
		return true, nil
	}
	if !input.Optional && (input.Value == nil || input.Value.FE == nil) {
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s is required", input.Name), "", input.ID)
	}
	_, ok := input.Value.FE.(float64)
	if !ok {
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s is not a number", input.Name), "", input.ID)
	}
	return true, nil
}

func findDate(date time.Time, operation, unit, value string) time.Time {
	num, _ := strconv.Atoi(operation + value)
	switch unit {
	case "h":
		date = date.Add(time.Hour * time.Duration(num))
	case "d":
		date = date.AddDate(0, 0, num)
	case "M":
		date = date.AddDate(0, num, 0)
	case "y":
		date = date.AddDate(num, 0, 0)
	}
	return date
}

func validateDateTimeVal(input *models.InputPod) (bool, error) {
	if input == nil {
		return true, nil
	}
	if input.Value == nil || input.Value.FE == nil {
		if input.Optional {
			return true, nil
		}
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s is required", input.Name), "", input.ID)
	}
	s, ok := input.Value.FE.(string)
	if !ok {
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s is not date time format", input.Name), "", input.ID)
	}
	val, err := time.Parse(time.RFC3339, s)
	if err != nil {
		switch input.Name {
		case "enter_timestamp":
			return false, errors.EnterTimeErr
		case "exit_timestamp":
			return false, errors.ExitTimeErr
		default:
			return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
				fmt.Sprintf("%s is not a valid date time format, should be in RFC3339", input.Name), "", input.ID)
		}
	}

	inputJ := utils.StructToJSON(input)
	if funk.ContainsString([]string{"current_date", "current_datetime"}, inputJ.Get("range_from.field").String()) {
		descDate := findDate(
			time.Now(),
			inputJ.Get("range_from.operation").String(),
			inputJ.Get("range_from.unit").String(),
			inputJ.Get("range_from.value").String(),
		)
		if inputJ.Get("range_from.field").String() == "current_date" {
			val = time.Date(val.Year(), val.Month(), val.Day(), 23, 59, 59, 9999, time.UTC)
		}
		if val.Before(descDate) {
			return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
				fmt.Sprintf("%s must be later than %s", input.Name, descDate.Format(time.RFC3339)), "", input.ID)
		}
	}

	if funk.ContainsString([]string{"current_date", "current_datetime"}, inputJ.Get("range_to.field").String()) {
		descDate := findDate(
			time.Now(),
			inputJ.Get("range_to.operation").String(),
			inputJ.Get("range_to.unit").String(),
			inputJ.Get("range_to.value").String(),
		)
		if inputJ.Get("range_from.field").String() == "current_date" {
			val = time.Date(val.Year(), val.Month(), val.Day(), 0, 0, 0, 0, time.UTC)
		}
		if val.After(descDate) {
			return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
				fmt.Sprintf("%s must be before %s", input.Name, descDate.Format(time.RFC3339)), "", input.ID)
		}
	}
	return true, nil
}

func validateOpPodStringVal(input *models.InputPod) (bool, error) {
	if input == nil {
		return true, nil
	}

	if input.Optional {
		return true, nil
	}

	if input.Value == nil || input.Value.FE == nil {
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s is required", input.Name), "", input.ID)
	}

	switch reflect.TypeOf(input.Value.FE).Kind() {
	case reflect.String:
		value, ok := input.Value.FE.(string)
		if !ok {
			return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
				fmt.Sprintf("%s value is not a string", input.Name), "", input.ID)
		}

		if len(input.OptionList) > 0 {
			// check if value is a valid option
			validOption := false
			for _, op := range input.OptionList {
				opStr, isStr := op.(string)
				if !isStr {
					continue
				}
				if opStr == value {
					validOption = true
				}
			}
			if !validOption {
				// if not a valid option, return false
				return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
					fmt.Sprintf("%s must be one of %v", input.Name, input.OptionList), "", input.ID)
			}
		}
		if input.MinLength > 0 {
			if len(value) < input.MinLength {
				switch input.Name {
				case "exit_flight":
					return false, errors.ExitFlightNumberFmtErr
				case "enter_flight":
					return false, errors.EnterFlightNumberFmtErr
				default:
					return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
						fmt.Sprintf("%s should not be shorter than %d", input.Name, input.MinLength), "", input.ID)
				}
			}
		}

		if input.MaxLength > 0 {
			if len(value) > input.MaxLength {
				return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
					fmt.Sprintf("%s should not be longer than %d", input.Name, input.MaxLength), "", input.ID)
			}
		}

		if input.Optional && value == "" {
			return true, nil
		}

		if len(input.Pattern) > 0 {
			r, err := regexp2.Compile(input.Pattern, regexp2.RE2)
			if err != nil {
				log.Warn().Str("pattern", input.Pattern).Str("pod", input.ID).Str("error", err.Error()).Msg("invalid pattern")
				return true, nil
			}
			if match, _ := r.MatchString(value); !match {
				if input.Category == "passport" && input.Name == "passport_number" {
					return false, errors.PassportNumberFmtErr
				}
				return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
					fmt.Sprintf("%s should match pattern %s", input.Name, input.Pattern), "", input.ID)
			}
		}
		return true, nil
	case reflect.Slice:
	default:
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s value is not a string", input.Name), "", input.ID)
	}
	return true, nil
}

func validateOpPodIntegerVal(input *models.InputPod) (bool, error) {
	if input == nil {
		return true, nil
	}
	if !input.Optional && (input.Value == nil || input.Value.FE == nil) {
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s is required", input.Name), "", input.ID)
	}
	_, ok := input.Value.FE.(float64)
	if !ok {
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s is not a number", input.Name), "", input.ID)
	}
	return true, nil
}

func validateOpPodDateTimeVal(input *models.InputPod) (bool, error) {
	if input == nil {
		return true, nil
	}
	if input.Value == nil || input.Value.FE == nil {
		if input.Optional {
			return true, nil
		}
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s is required", input.Name), "", input.ID)
	}

	s, ok := input.Value.FE.(string)
	if !ok {
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s is not date time format", input.Name), "", input.ID)
	}
	val, err := time.Parse(time.RFC3339, s)
	if err != nil {
		switch input.Name {
		case "enter_timestamp":
			return false, errors.EnterTimeErr
		case "exit_timestamp":
			return false, errors.ExitTimeErr
		default:
			return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
				fmt.Sprintf("%s is not a valid date time format, should be in RFC3339", input.Name), "", input.ID)
		}
	}

	inputJ := utils.StructToJSON(input)
	if funk.ContainsString([]string{"current_date", "current_datetime"}, inputJ.Get("range_from.field").String()) {
		descDate := findDate(
			time.Now(),
			inputJ.Get("range_from.operation").String(),
			inputJ.Get("range_from.unit").String(),
			inputJ.Get("range_from.value").String(),
		)
		if inputJ.Get("range_from.field").String() == "current_date" {
			val = time.Date(val.Year(), val.Month(), val.Day(), 23, 59, 59, 9999, time.UTC)
		}
		if val.Before(descDate) {
			return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
				fmt.Sprintf("%s must be later than %s", input.Name, descDate.Format(time.RFC3339)), "", input.ID)
		}
	}

	if funk.ContainsString([]string{"current_date", "current_datetime"}, inputJ.Get("range_to.field").String()) {
		descDate := findDate(
			time.Now(),
			inputJ.Get("range_from.operation").String(),
			inputJ.Get("range_from.unit").String(),
			inputJ.Get("range_from.value").String(),
		)
		if inputJ.Get("range_from.field").String() == "current_date" {
			val = time.Date(val.Year(), val.Month(), val.Day(), 0, 0, 0, 0, time.UTC)
		}
		if val.After(descDate) {
			return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
				fmt.Sprintf("%s must be before %s", input.Name, descDate.Format(time.RFC3339)), "", input.ID)
		}
	}
	return true, nil
}

func validateOpPodBoolVal(input *models.InputPod) (bool, error) {
	if input == nil {
		return true, nil
	}
	if !input.Optional && (input.Value == nil || input.Value.FE == nil) {
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s is required", input.Name), "", input.ID)
	}
	_, ok := input.Value.FE.(bool)
	if !ok {
		return false, errors.NewValidationErrorWithCategoryAndFieldStr(input.Category, input.Name,
			fmt.Sprintf("%s value is not a boolean value", input.Name), "", input.ID)
	}
	return true, nil
}
