package application_validation

type ValidatorChain struct {
	chain []Validator
}

func (c *ValidatorChain) Add(validator ...Validator) *ValidatorChain {
	if len(validator) == 0 {
		return c
	}
	c.chain = append(c.chain, validator...)
	return c
}

func (c *ValidatorChain) DoValidation(input any) (bool, error) {
	if len(c.chain) == 0 {
		return true, nil
	}
	for _, v := range c.chain {
		if valid, err := v.Validate(input); !valid {
			return valid, err
		}
	}
	return true, nil
}
