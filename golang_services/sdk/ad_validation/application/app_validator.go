package application_validation

import (
	"time"

	"bitbucket.org/persistence17/aria/golang_services/sdk/ad_validation/base"
)

const (
	kPassportSchema       = "passport"
	kVisaOrderSchema      = "visa_order"
	kTravelSchema         = "travel"
	kAdditionalInfoSchema = "additional"
	kOneYear              = 365 * 24 * time.Hour
)

var (
	sixMonthHeadroomPPValidator = NewPassportExpirationValidator(6*30*24*time.Hour, "")
)

type AppValidator struct {
	BaseValidator       *base.InputPodsValidator
	PassportValidator   *ValidatorChain
	VisaOrderValidator  *ValidatorChain
	TravelInfoValidator *ValidatorChain
}
