package application_validation

import (
	"fmt"
	"time"

	"github.com/rs/zerolog/log"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/models"
)

type PassportExpirationValidator struct {
	headroom time.Duration
	country  string
}

func NewPassportExpirationValidator(headroom time.Duration, country string) *PassportExpirationValidator {
	return &PassportExpirationValidator{headroom: headroom, country: country}
}

func (v *PassportExpirationValidator) Validate(input any) (bool, error) {
	pods, ok := input.(*models.InputPods)
	if !ok {
		return false, fmt.Errorf("PassportExpirationValidator only takes InputPods as input")
	}
	if pods == nil || len(*pods) == 0 {
		return true, nil
	}
	pod := (*pods)["901007"]
	if pod == nil {
		return true, nil // VNM eVisa don't have this field
	}
	if pod.Value == nil || pod.Value.FE == nil {
		return false, errors.PassportExpErr
	}
	s, ok := pod.Value.FE.(string)
	if !ok {
		return false, errors.NewValidationError(errors.ValidationCategoryPassport, 0, pod.ID, "", "invalid expiration date format")
	}
	ppExp, err := time.Parse(time.RFC3339, s)
	if err != nil {
		log.Warn().Str("error", err.Error()).Str("expiration_date", s).Msg("passport expiration date in wrong format")
		return false, errors.NewValidationError(errors.ValidationCategoryPassport, 0, pod.ID, "", "invalid expiration date format")
	}
	exp := time.Now().Add(v.headroom)
	if ppExp.Before(exp) {
		if v.headroom == 365*24*time.Hour {
			return false, errors.NewOneYearPassportExpErr(v.country)
		}
		return false, errors.PassportExpErr
	}
	return true, nil
}
