package application_validation

import (
	"time"

	"github.com/rs/zerolog/log"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/models"
)

type TravelDatesValidator struct {
	exitTsPod    *models.InputPod
	entryDatePod *models.InputPod
	enterTsPod   *models.InputPod
}

func (v *TravelDatesValidator) Validate(input any) (bool, error) {
	if v.exitTsPod == nil || v.entryDatePod == nil || v.enterTsPod == nil ||
		v.exitTsPod.Value == nil || v.entryDatePod.Value == nil || v.enterTsPod.Value == nil ||
		v.exitTsPod.Value.FE == nil || v.entryDatePod.Value.FE == nil || v.enterTsPod.Value.FE == nil {
		return true, nil
	}
	var err error

	s, ok := v.entryDatePod.Value.FE.(string)
	if !ok {
		return false, errors.NewValidationError(errors.ValidationCategoryTravelInfo, 0, v.entryDatePod.ID, "", "invalid entry date format")
	}
	var entryDate time.Time
	entryDate, err = time.Parse(time.RFC3339, s)
	if err != nil {
		log.Warn().Str("entry_date", s).Msg("entry_date with invalid format")
		return false, errors.NewValidationError(errors.ValidationCategoryTravelInfo, 0, v.entryDatePod.ID, "", "invalid entry date format")
	}

	s, ok = v.enterTsPod.Value.FE.(string)
	if !ok {
		return false, errors.NewValidationError(errors.ValidationCategoryTravelInfo, 0, v.enterTsPod.ID, "", "invalid enter timestamp format")
	}
	var enterTs time.Time
	enterTs, err = time.Parse(time.RFC3339, s)
	if err != nil {
		log.Warn().Str("enter_timestamp", s).Msg("enter_timestamp with invalid format")
		return false, errors.NewValidationError(errors.ValidationCategoryTravelInfo, 0, v.enterTsPod.ID, "", "invalid enter timestamp format")
	}

	s, ok = v.exitTsPod.Value.FE.(string)
	if !ok {
		return false, errors.NewValidationError(errors.ValidationCategoryTravelInfo, 0, v.exitTsPod.ID, "", "invalid exit timestamp format")
	}
	var exitTs time.Time
	exitTs, err = time.Parse(time.RFC3339, s)
	if err != nil {
		log.Warn().Str("exit_timestamp", s).Msg("exit_timestamp with invalid format")
		return false, errors.NewValidationError(errors.ValidationCategoryTravelInfo, 0, v.enterTsPod.ID, "", "invalid exit timestamp format")
	}
	yyyy1, MM1, dd1 := entryDate.Date()
	yyyy, MM, dd := enterTs.Date()
	// date of enter timestamp is not equal to entry date
	if yyyy != yyyy1 || MM != MM1 || dd != dd1 {
		return false, errors.EnterTimeErr
	}

	yyyy, MM, dd = exitTs.Date()
	// date of exit timestamp is earlier than entry date
	if yyyy > yyyy1 || (yyyy == yyyy1 && MM > MM1) || (yyyy == yyyy1 && MM == MM1 && dd > dd1) {
		return false, errors.ExitTimeErr
	}
	return true, nil
}
