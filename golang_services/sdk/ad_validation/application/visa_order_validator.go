package application_validation

import (
	"fmt"
	"time"

	"github.com/pariz/gountries"
	"github.com/rs/zerolog/log"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/time_util"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

var (
	countryQuery = gountries.New()
)

type VisaOrderDatesValidator struct {
	PassportExpPod *models.InputPod //901007
	EntryDatePod   *models.InputPod //902006
	ExitDatePod    *models.InputPod // 902007
}

func (v *VisaOrderDatesValidator) Validate(input any) (bool, error) {
	hasPpExp := true
	var passportExp time.Time
	var err error
	if v.PassportExpPod == nil || v.PassportExpPod.Value == nil || v.PassportExpPod.Value.FE == nil {
		hasPpExp = false
	} else {
		s, ok := v.PassportExpPod.Value.FE.(string)
		if !ok {
			hasPpExp = false
		}
		passportExp, err = time.Parse(time.RFC3339, s)
		if err != nil {
			hasPpExp = false
		}
	}

	hasEntry := true
	var entryDate time.Time
	if v.EntryDatePod == nil {
		hasEntry = false
	} else {
		enterDateStr := utils.StructToJSON(v.EntryDatePod).Get("value.fe").String()
		if enterDateStr == "" {
			hasEntry = false
		}
		entryDate, err = time.Parse(time.RFC3339, enterDateStr)
		if err != nil {
			hasEntry = false
		}
	}

	hasExit := true
	var exitDate time.Time
	exitDateStr := utils.StructToJSON(v.ExitDatePod).Get("value.fe").String()
	if exitDateStr == "" {
		hasExit = false
	} else {
		if hasEntry {
			exitDate = entryDate
		} else {
			hasExit = false
		}
	}

	now := time.Now()

	if hasEntry {
		entryTime := entryDate.Add(time.Hour * 24).Truncate(time.Hour * 24)
		if entryTime.Before(now) {
			return false, errors.NewEntryDateErr()
		}
	}

	if hasExit {
		exitTime := exitDate.Add(time.Hour * 24).Truncate(time.Hour * 24)
		if exitTime.Before(now) {
			return false, errors.NewExitDateErr()
		}
	}
	if hasExit && exitDate.Before(entryDate) {
		return false, errors.NewEntryDateLaterThanExitDateErr()
	}
	if hasPpExp && entryDate.After(passportExp) {
		return false, errors.NewEntryDateErr()
	}
	return true, nil
}

type VisaOrderProcessingTimeValidator struct {
	processingTimePod *models.InputPod
	visaCountryPod    *models.InputPod
	entryDatePod      *models.InputPod
}

func (v *VisaOrderProcessingTimeValidator) Validate(input any) (bool, error) {
	if v == nil || v.processingTimePod == nil || v.visaCountryPod == nil {
		return true, nil
	}
	if v.processingTimePod == nil || v.processingTimePod.Value == nil || v.processingTimePod.Value.FE == nil {
		return true, nil
	}
	pt, ok := v.processingTimePod.Value.FE.(string)
	if !ok {
		return true, nil
	}

	readyTime, err := time_util.CalculateNormalDays(time.Now(), pt)
	if err != nil {
		log.Error().Str("error", err.Error()).Str("procesing_time_string", pt).
			Msg("VisaOrderProcessingTimeValidator.Validate: failed to convert processing time string to duration")
		return true, nil
	}

	if v.entryDatePod.Value == nil || v.entryDatePod.Value.FE == nil {
		return false, errors.NewValidationError(errors.ValidationCategoryTravelInfo, 0, v.entryDatePod.ID, "", "missing value")
	}
	d, ok := v.entryDatePod.Value.FE.(string)
	if !ok {
		return false, errors.NewValidationError(errors.ValidationCategoryTravelInfo, 0, v.entryDatePod.ID, "", "bad value")
	}
	entryDate, err := time.Parse(time.RFC3339, d)
	if err != nil {
		return false, errors.NewValidationError(errors.ValidationCategoryTravelInfo, 0, v.entryDatePod.ID, "", "bad value")
	}
	if v.visaCountryPod.Value == nil || v.visaCountryPod.Value.FE == nil {
		return false, errors.NewValidationError(errors.ValidationCategoryTravelInfo, 0, v.visaCountryPod.ID, "", "missing value")
	}
	// country, ok := v.visaCountryPod.Value.FE.(string)
	// if !ok {
	// 	return false, errors.NewValidationError(errors.ValidationCategoryTravelInfo, 0, v.visaCountryPod.ID, "", "bad value")
	// }
	entryDateInLoc := entryDate.Add(time.Hour * 24).Truncate(time.Hour * 24).Add(-time.Millisecond)
	fmt.Println(readyTime)
	fmt.Println(entryDateInLoc)

	if readyTime.After(entryDateInLoc) {
		return false, errors.ProcessingTimeEntryDateErr
	}
	return true, nil
}
