package task_validation

import (
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/ad_validation/base"
)

type ETSTaskValidator struct {
	BaseValidator *base.InputPodsValidator
}

func NewETSTaskValidator(task *models.ServiceTask) (*ETSTaskValidator, error) {
	pods := models.InputPods{}
	for k, v := range task.InputPods.ToMap() {
		pods[k] = v
	}
	return &ETSTaskValidator{
		BaseValidator: base.NewInputPodsValidator(&pods),
	}, nil
}

func (v *ETSTaskValidator) Validate(task *models.ServiceTask) (bool, error) {
	if v.BaseValidator != nil {
		if valid, err := v.BaseValidator.Validate(task); err != nil {
			return valid, err
		}
	}

	return true, nil
}
