package ets

import (
	"encoding/json"
	"strconv"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/rs/zerolog/log"
)

func FlattenInputPods(task models.ServiceTask) *models.ServiceTask {
	for _, v := range task.InputPods {
		nestedPodsLevel2 := flattenInputPods(v)
		task.InputPods = append(task.InputPods, nestedPodsLevel2...)
		for _, x := range nestedPodsLevel2 {
			nestedPodsLevel3 := flattenInputPods(x)
			task.InputPods = append(task.InputPods, nestedPodsLevel3...)
		}
	}
	return &task
}

func flattenInputPods(v *models.InputPod) []*models.InputPod {
	result := []*models.InputPod{}
	if len(v.OptionChoice) > 0 {
		var value string
		var arr []any
		switch s := v.Value.FE.(type) {
		case int:
			value = strconv.Itoa(s)
		case string:
			value = s
		case bool:
			value = strconv.FormatBool(s)
		case []any:
			arr = s
		default:
			log.Warn().Interface("fe_value", v.Value.FE).Msg("unknown input value fe")
		}
		if value != "" {
			visaPodArr := v.OptionChoice[value]
			if len(visaPodArr) > 0 {
				for i := 0; i < len(visaPodArr); i++ {
					optionPod, _ := json.Marshal(visaPodArr[i])
					inputPod := &models.InputPod{}
					json.Unmarshal(optionPod, &inputPod)

					result = append(result, inputPod)
				}
			}
		}
		if len(arr) >= 0 {
			for _, item := range arr {
				visaPodArr := v.OptionChoice[item.(string)]
				for i := 0; i < len(visaPodArr); i++ {
					optionPod, _ := json.Marshal(visaPodArr[i])
					inputPod := &models.InputPod{}
					json.Unmarshal(optionPod, &inputPod)
					result = append(result, inputPod)
				}
			}
		}
	}
	return result
}
