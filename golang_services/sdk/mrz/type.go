package mrz

type Position struct {
	Line  uint `json:"line"`
	Start uint `json:"start"`
	End   uint `json:"end"`
}

type Range struct {
	Position *Position `json:"position"`
	Raw      string    `json:"raw"`
}

type Field struct {
	Name     string    `json:"name"`
	Value    string    `json:"value"`
	Valid    bool      `json:"valid"`
	Ranges   []Range   `json:"ranges"`
	Position *Position `json:"position"`
}

type Document struct {
	Format  string            `json:"format"`
	Details []*Field          `json:"details"`
	Fields  map[string]string `json:"fields"`
	Valid   bool              `json:"valid"`
}

func (d Document) ValidCount() int {
	count := 0
	for _, field := range d.Details {
		if field.Valid && field.Value != "" {
			count++
		}
	}
	return count
}

type fieldParser func(lines []string) (*Field, error)
type textParser func(s string) (string, error)
