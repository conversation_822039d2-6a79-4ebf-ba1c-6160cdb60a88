package mrz

import (
	"regexp"
	"strconv"
	"strings"
)

func clearText(str string) string {
	str = regexp.MustCompile("<+$").ReplaceAllString(str, "")
	clearStr := regexp.MustCompile("<").ReplaceAllString(str, " ")
	return strings.TrimSpace(clearStr)
}

func getText(lines []string, pos *Position) string {
	return lines[pos.Line][pos.Start:pos.End]
}

func checkDigitCode(src, code string) bool {
	c := 0
	factors := []int{7, 3, 1}
	for i, charCode := range []rune(src) {
		code := int(charCode)
		if code == 60 {
			code = 0
		}
		if code >= 65 {
			code -= 55
		}
		if code >= 48 {
			code -= 48
		}
		code *= factors[i%3]
		c += code
	}
	return strconv.Itoa(c%10) == code
}

func textBetweenSeparate(text string, separate string) string {
	start := strings.Index(text, separate)
	end := strings.Index(text[start+2:], separate)
	if end <= 0 {
		return ""
	}
	return text[start+len(separate) : start+end+2]
}
