package mrz

import (
	"reflect"
	"testing"
)

func TestParse(t *testing.T) {
	tests := []struct {
		input string
		want  map[string]string
	}{
		{
			input: "P<UTOERIKSSON<<ANNA<MARIA<<<<<<<<<<<<<<<<<<<\nL898902C36UTO7408122F1204159ZE184226B<<<<<10",
			want: map[string]string{
				"birthDate":      "740812",
				"sex":            "F",
				"expirationDate": "120415",
				"lastName":       "ERIKSSON",
				"firstName":      "ANNA MARIA",
				"documentNumber": "L898902C3",
			},
		},
		{
			input: "P<IND<<JYOTHI<NAVILA<MARY<<<<<<<<<<<<<<<<<<<\nN8952132<3IND8501112F2603221<<<<<<<<<<<<<<08",
			want: map[string]string{
				"birthDate":      "850111",
				"sex":            "F",
				"expirationDate": "260322",
				"lastName":       "LNU",
				"firstName":      "JYOTHI NAVILA MARY",
				"documentNumber": "N8952132",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			got, err := Parse(tt.input)
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			if !reflect.DeepEqual(map[string]string{
				"birthDate":      got.Fields["birthDate"],
				"sex":            got.Fields["sex"],
				"expirationDate": got.Fields["expirationDate"],
				"lastName":       got.Fields["lastName"],
				"firstName":      got.Fields["firstName"],
				"documentNumber": got.Fields["documentNumber"],
			}, tt.want) {
				t.Errorf("Unexpected result. Expected: %v, but got: %v", tt.want, got.Fields)
			}
		})
	}
}
