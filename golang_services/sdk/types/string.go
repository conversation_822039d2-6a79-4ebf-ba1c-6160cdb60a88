package types

import (
	"database/sql/driver"
	"encoding/json"
	"strconv"
	"strings"
)

type StringArray []string

func (s *StringArray) UnmarshalJSON(data []byte) error {
	str, _ := strconv.Unquote(string(data))
	val := strings.Split(str, ",")
	*s = val
	return nil
}

func (s StringArray) MarshalJSON() ([]byte, error) {
	if s == nil {
		return []byte("[]"), nil
	}
	return json.Marshal([]string(s))
}

// Value implements the driver Valuer interface.
func (s StringArray) Value() (driver.Value, error) {
	if s == nil {
		return []byte(""), nil
	}
	return strings.Join(s, ","), nil
}

// Scan implements the Scanner interface.
func (s *StringArray) Scan(src any) error {
	val := strings.Split(src.(string), ",")
	*s = val
	return nil
}
