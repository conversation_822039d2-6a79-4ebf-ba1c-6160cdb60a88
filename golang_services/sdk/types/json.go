package types

import (
	"database/sql/driver"
	"strconv"
	"strings"
	"time"

	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

type ExtJSON struct {
	Data []byte
	Raw  []byte
}

func (j *ExtJSON) UnmarshalJSON(data []byte) error {
	j.Raw = data
	j.Data = data
	return nil
}

func (j ExtJSON) MarshalJSON() ([]byte, error) {
	return j.Data, nil
}

// Value implements the driver Valuer interface.
func (j ExtJSON) Value() (driver.Value, error) {
	// j.WithPatternParser(false, true)

	return j.Data, nil
}

// Scan implements the Scanner interface.
func (j *ExtJSON) Scan(src any) error {
	val := src.([]byte)

	j.Raw = val
	j.Data = val
	// j.WithPatternParser(true, false)
	j.WithISOString()

	return nil
}

// WithISOString replace postgresql time with ISO string
func (j *ExtJSON) WithISOString() {
	if j == nil {
		return
	}
	var dataJSON = gjson.ParseBytes(j.Data)
	for _, path := range GetJSONPaths(dataJSON.Raw) {
		if t, err := time.Parse("2006-01-02T15:04:05.999999999", dataJSON.Get(path).String()); err == nil {
			if val, err := sjson.Set(string(j.Data), path, t.Format(time.RFC3339)); err == nil {
				j.Data = []byte(val)
			}
		}
	}
}

func (j *ExtJSON) WithPatternParser(escape, unescape bool) {
	if j == nil {
		return
	}
	var dataJSON = gjson.ParseBytes(j.Data)
	for _, path := range GetJSONPaths(dataJSON.Raw) {
		if item := strings.Split(path, "."); len(item) > 0 && item[len(item)-1] == "pattern" {
			if escape {
				if val, err := sjson.Set(string(j.Data), path, EscapeString(dataJSON.Get(path).String())); err == nil {
					j.Data = []byte(val)
				}
			}
			if unescape {
				if val, err := sjson.Set(string(j.Data), path, UnescapeString(dataJSON.Get(path).String())); err == nil {
					j.Data = []byte(val)
				}
			}
		}
	}
}

func EscapeString(s string) string {
	return strings.NewReplacer(`\`, `\\`, `"`, `\"`).Replace(s)

}

func UnescapeString(s string) string {
	return strings.NewReplacer(`\\`, `\`, `\"`, `"`).Replace(s)
}

func GetJSONPaths(src string) []string {
	return getJSONPaths(gjson.Parse(src), "", nil)
}

func getJSONPaths(obj gjson.Result, prePath string, path []string) []string {
	if !obj.IsObject() && !obj.IsArray() {
		return path
	}

	var i = 0
	obj.ForEach(func(key, value gjson.Result) bool {
		var subPath = []string{}
		if prePath != "" {
			subPath = append(subPath, prePath)
		}
		if obj.IsArray() {
			subPath = append(subPath, strconv.Itoa(i))
			i++
		}

		if obj.IsObject() {
			subPath = append(subPath, key.String())
		}

		path = append(path, strings.Join(subPath, "."))
		path = getJSONPaths(value, path[len(path)-1], path)

		return true
	})
	return path
}
