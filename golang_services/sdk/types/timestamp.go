package types

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

type TimeStamp time.Time

func (s *TimeStamp) UnmarshalJSON(data []byte) error {
	if data == nil {
		s = nil
		return nil
	}

	if val, err := time.Parse(time.RFC3339, string(data)); err == nil {
		*s = TimeStamp(val)
	}

	if val, err := time.Parse("2006-01-02T15:04:05.999999999", string(data)); err == nil {
		*s = TimeStamp(val)
	}

	return nil
}

func (s TimeStamp) MarshalJSON() ([]byte, error) {
	return json.Marshal(time.Time(s))
}

// Value implements the driver Valuer interface.
func (s TimeStamp) Value() (driver.Value, error) {
	return time.Time(s), nil
}

// <PERSON>an implements the Scanner interface.
func (s *TimeStamp) Scan(src any) error {
	if src == nil {
		s = nil
		return nil
	}

	if val, ok := src.(time.Time); ok {
		*s = TimeStamp(val)
		return nil
	}
	if val, err := time.Parse(time.RFC3339, src.(string)); err == nil {
		*s = TimeStamp(val)
		return nil
	}

	if val, err := time.Parse("2006-01-02T15:04:05.999999999", src.(string)); err == nil {
		*s = TimeStamp(val)
		return nil
	}

	return nil
}
