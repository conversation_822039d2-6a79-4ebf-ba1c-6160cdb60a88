package aws

import (
	"bytes"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/rs/zerolog/log"
)

type S3Svc struct {
	svc        *s3.S3
	sess       *session.Session
	delBatcher *s3manager.BatchDelete
}

type S3Downloader struct {
	down *s3manager.Downloader
}

type S3Manager struct {
	sess *session.Session
	svc  *s3.S3
}

func NewS3Svc(sess *session.Session) *S3Svc {
	svc := s3.New(sess)
	del := s3manager.NewBatchDelete(sess)
	return &S3Svc{
		svc:        svc,
		sess:       sess,
		delBatcher: del,
	}
}

func NewDownloader(sess *session.Session) *S3Downloader {
	downloader := s3manager.NewDownloader(sess)
	return &S3Downloader{
		down: downloader,
	}
}

func UploadFile(sess *session.Session, bucket string, key string, buff []byte) error {
	contentType := http.DetectContentType(buff)
	_, err := s3.New(sess).PutObject(&s3.PutObjectInput{
		Bucket:        aws.String(bucket),
		Key:           aws.String(key),
		ACL:           aws.String("private"),
		Body:          bytes.NewReader(buff),
		ContentLength: aws.Int64(int64(len(buff))),
		ContentType:   aws.String(contentType),
	})
	return err
}

func (dl *S3Downloader) DownloadFromS3Bucket(bucket, key, localFile string) error {
	file, err := os.Create(localFile)
	if err != nil {
		fmt.Println(err)
	}

	defer file.Close()
	_, err = dl.down.Download(file,
		&s3.GetObjectInput{
			Bucket: aws.String(bucket),
			Key:    aws.String(key),
		})
	if err != nil {
		return err
	}
	return nil
}

func (dl *S3Downloader) DownloadFromS3BucketToBuffer(bucket, key string) ([]byte, error) {
	buff := &aws.WriteAtBuffer{}

	_, err := dl.down.Download(buff,
		&s3.GetObjectInput{
			Bucket: aws.String(bucket),
			Key:    aws.String(key),
		})
	if err != nil {
		return nil, err
	}
	return buff.Bytes(), nil
}

func (ps *S3Svc) PresignUploadUrl(bucket, key, contentType string, expire time.Duration) (string, error) {
	input := &s3.PutObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	}
	if contentType != "" {
		input.ContentType = aws.String(contentType)
		input.ContentDisposition = aws.String("attachment")
	}
	req, _ := ps.svc.PutObjectRequest(input)

	return req.Presign(expire)
}

func (ps *S3Svc) PresignUrl(bucket, key string, expire time.Duration) (string, error) {
	req, _ := ps.svc.GetObjectRequest(&s3.GetObjectInput{
		Bucket:                     aws.String(bucket),
		Key:                        aws.String(key),
		ResponseContentDisposition: aws.String("attachment"),
	})

	return req.Presign(expire)
}

func (ps *S3Svc) PresignUploadPublicUrl(bucket, key, contentType string, expire time.Duration) (string, error) {
	input := &s3.PutObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
		ACL:    aws.String("public-read"),
	}
	if contentType != "" {
		input.ContentType = aws.String(contentType)
		input.ContentDisposition = aws.String("attachment")
	}
	req, _ := ps.svc.PutObjectRequest(input)

	return req.Presign(expire)
}

func (ps *S3Svc) PresignPublicUrl(bucket, key string, expire time.Duration) (string, error) {
	req, _ := ps.svc.GetObjectRequest(&s3.GetObjectInput{
		Bucket:                     aws.String(bucket),
		Key:                        aws.String(key),
		ResponseContentDisposition: aws.String("attachment"),
	})

	urlStr, err := req.Presign(expire)
	if err != nil {
		return "", err
	}

	return urlStr, nil
}

func S3PathToBucketAndKey(path string) (string, string) {
	parts := strings.SplitN(path, "/", 2)
	if len(parts) != 2 {
		return "", ""
	}
	return parts[0], parts[1]
}

func (ps *S3Svc) UploadFile(bucket string, key string, fileDir string, contentType string) error {

	// Open the file for use
	file, err := os.Open(fileDir)
	if err != nil {
		return err
	}
	defer file.Close()

	// Get file size and read the file content into a buffer
	fileInfo, _ := file.Stat()
	var size int64 = fileInfo.Size()
	buffer := make([]byte, size)
	file.Read(buffer)

	// Properties settings: this is where you choose the bucket, filename, content-type etc.
	// of the file you're uploading.
	_, err = s3.New(ps.sess).PutObject(&s3.PutObjectInput{
		Bucket:               aws.String(bucket),
		Key:                  aws.String(key),
		ACL:                  aws.String("private"),
		Body:                 bytes.NewReader(buffer),
		ContentLength:        aws.Int64(size),
		ContentType:          aws.String(contentType),
		ContentDisposition:   aws.String("attachment"),
		ServerSideEncryption: aws.String("AES256"),
	})
	if err != nil {
		return err
	}
	return nil
}

func (ps *S3Svc) UploadFileBuffer(bucket string, key string, buffer []byte, contentType string) (string, error) {
	_, err := s3.New(ps.sess).PutObject(&s3.PutObjectInput{
		Bucket:               aws.String(bucket),
		Key:                  aws.String(key),
		ACL:                  aws.String("private"),
		Body:                 bytes.NewReader(buffer),
		ContentLength:        aws.Int64(int64(len(buffer))),
		ContentType:          aws.String(contentType),
		ContentDisposition:   aws.String("attachment"),
		ServerSideEncryption: aws.String("AES256"),
	})
	if err != nil {
		return "", err
	}
	return ps.PresignPublicUrl(bucket, key, 24*time.Hour)
}

func PresignAllFilesInMap(ps *S3Svc, m *models.FileMap, expire time.Duration) {
	if m == nil {
		return
	}
	for k, v := range *m {
		if s, ok := v.(string); ok {
			bucket, key, err := utils.UrlToS3BucketAndKey(s)
			if err != nil {
				log.Warn().Fields(map[string]any{
					"file_path": s,
					"error":     err.Error(),
				}).Msg("cannot convert url to s3 bucket and key")
				continue
			}
			presigned, err := ps.PresignUrl(bucket, key, expire)
			if err != nil {
				log.Warn().Fields(map[string]any{
					"error":  err.Error(),
					"bucket": bucket,
					"key":    key,
				}).Msg("failed to presign")
				continue
			}
			(*m)[k] = presigned
		}
	}
}

func (ps *S3Svc) ListObjects(bucket, prefix string) ([]string, error) {
	resp, err := ps.svc.ListObjectsV2(&s3.ListObjectsV2Input{
		Bucket: aws.String(bucket),
		Prefix: aws.String(prefix),
	})
	if err != nil {
		return nil, err
	}
	var res []string
	for _, o := range resp.Contents {
		res = append(res, *o.Key)
	}
	return res, nil
}

func (ps *S3Svc) CopyObj(fromBucket, fromKey, toBucket, toKey string) error {
	_, err := ps.svc.CopyObject(&s3.CopyObjectInput{
		Bucket:     aws.String(toBucket),
		CopySource: aws.String(fmt.Sprintf("%s/%s", fromBucket, fromKey)),
		Key:        aws.String(toKey),
	})
	if err != nil {
		return err
	}
	return nil
}

func (ps *S3Svc) DeleteObjs(bucket string, keys []string) error {
	var objects []s3manager.BatchDeleteObject
	for i := range keys {
		k := keys[i]
		objects = append(objects, s3manager.BatchDeleteObject{
			Object: &s3.DeleteObjectInput{
				Key:    aws.String(k),
				Bucket: aws.String(bucket),
			},
		})
	}
	if err := ps.delBatcher.Delete(aws.BackgroundContext(), &s3manager.DeleteObjectsIterator{Objects: objects}); err != nil {
		return err
	}
	return nil
}
