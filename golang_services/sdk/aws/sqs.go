package aws

import (
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/rs/zerolog/log"
	uuid "github.com/satori/go.uuid"
)

type SQSClient struct {
	svc *sqs.SQS
	url string
}

func NewSQSClient(sess *session.Session, url string) *SQSClient {
	svc := sqs.New(sess)
	return &SQSClient{svc: svc, url: url}
}

func (c *SQSClient) Send(msg any) error {
	byt, err := json.Marshal(msg)
	if err != nil {
		return err
	}
	msgBody := string(byt)
	inputMsg := &sqs.SendMessageInput{
		MessageBody: &msgBody,
		QueueUrl:    &c.url,
	}
	_, err = c.svc.SendMessage(inputMsg)
	if err != nil {
		return err
	}
	return nil
}

func (c *SQSClient) SendWithDelay(msg any, delay int64) error {
	if delay < 0 || delay > 900 {
		return fmt.Errorf(`Invalid delay value: %d. Must be between 0 and 900`, delay)
	}
	byt, err := json.Marshal(msg)
	if err != nil {
		return err
	}
	msgBody := string(byt)
	inputMsg := &sqs.SendMessageInput{
		MessageBody:  &msgBody,
		QueueUrl:     &c.url,
		DelaySeconds: aws.Int64(delay),
	}
	_, err = c.svc.SendMessage(inputMsg)
	if err != nil {
		return err
	}
	return nil
}

func (c *SQSClient) SendAttrMap(m map[string]any) error {
	byt, err := json.Marshal(m)
	if err != nil {
		return err
	}
	msgBody := string(byt)
	attrMap := map[string]*sqs.MessageAttributeValue{}
	for k := range m {
		switch k {
		case "form_template", "application_id", "package_id", "application_data", "s3", "callback", "passport_image", "s3_key", "s3_bucket":
			v := m[k]
			switch vv := v.(type) {
			case string:
				attrMap[k] = &sqs.MessageAttributeValue{
					StringValue: aws.String(vv),
					DataType:    aws.String("String"),
				}
			default:
				bbytes, err := json.Marshal(vv)
				if err != nil {
					return err
				}
				attrMap[k] = &sqs.MessageAttributeValue{
					StringValue: aws.String(string(bbytes)),
					DataType:    aws.String("String"),
				}
			}
		}
	}
	msg := &sqs.SendMessageInput{
		MessageAttributes: attrMap,
		MessageBody:       aws.String(msgBody),
		QueueUrl:          aws.String(c.url),
	}
	_, err = c.svc.SendMessage(msg)
	return err
}

type AsyncSQSProducer struct {
	svc     *sqs.SQS
	url     string
	msgChan chan *sqs.SendMessageBatchRequestEntry
	buff    []*sqs.SendMessageBatchRequestEntry
}

func NewAsyncSQSProducer(sess *session.Session, url string) *AsyncSQSProducer {
	svc := sqs.New(sess)
	ch := make(chan *sqs.SendMessageBatchRequestEntry, 1024)
	return &AsyncSQSProducer{svc: svc, url: url, msgChan: ch}
}

func (c *AsyncSQSProducer) Run() {
	sigChan := make(chan os.Signal)
	// signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM, syscall.SIGHUP)

	ticker := time.NewTicker(1 * time.Minute)
loop:
	for {
		select {
		case msg := <-c.msgChan:
			c.buff = append(c.buff, msg)
			c.doBatchSend()
		case <-sigChan:
			close(c.msgChan)
			ticker.Stop()
			break loop
		case <-ticker.C:
			c.doBatchSend()
		}
	}

	// graceful shutdown
	// read all messages in channel buffer first
	for m := range c.msgChan {
		c.buff = append(c.buff, m)
	}

	// do batch send
	c.doBatchSend()
	return
}

func (c *AsyncSQSProducer) doBatchSend() {
	if len(c.buff) > 0 {
		for i := 0; i < len(c.buff); i++ {
			end := i + 10
			if end > len(c.buff) {
				end = len(c.buff)
			}
			oneBatch := &sqs.SendMessageBatchInput{
				Entries:  c.buff[i:end],
				QueueUrl: &c.url,
			}
			resp, err := c.svc.SendMessageBatch(oneBatch)
			if err != nil {
				log.Error().Fields(map[string]any{
					"error":      err,
					"batch_size": len(oneBatch.Entries),
					"queue_url":  oneBatch.QueueUrl,
				}).Msg("AsyncSQSProducer: SendMessageBatch failed")
			}
			for _, f := range resp.Failed {
				log.Error().Fields(map[string]any{
					"error_code":      *f.Code,
					"error_msg":       *f.Message,
					"is_sender_fault": *f.SenderFault,
				}).Msg("AsyncSQSProducer: failed message")
			}
		}
	}
	c.buff = []*sqs.SendMessageBatchRequestEntry{}
}

func (c *AsyncSQSProducer) Send(msg any) error {
	byt, err := json.Marshal(msg)
	if err != nil {
		return err
	}
	msgBody := string(byt)
	id := uuid.NewV4().String()
	entry := &sqs.SendMessageBatchRequestEntry{
		Id:          &id,
		MessageBody: &msgBody,
	}

	c.msgChan <- entry
	return nil
}

func (c *AsyncSQSProducer) SendAttrMap(m map[string]any) error {
	byt, err := json.Marshal(m)
	if err != nil {
		return err
	}
	msgBody := string(byt)
	attrMap := map[string]*sqs.MessageAttributeValue{}
	for k := range m {
		switch k {
		case "form_template", "application_id", "package_id", "application_data", "s3", "callback", "passport_image", "s3_key", "s3_bucket":
			v := m[k]
			switch vv := v.(type) {
			case string:
				attrMap[k] = &sqs.MessageAttributeValue{
					StringValue: aws.String(vv),
					DataType:    aws.String("String"),
				}
			default:
				bbytes, err := json.Marshal(vv)
				if err != nil {
					return err
				}
				attrMap[k] = &sqs.MessageAttributeValue{
					StringValue: aws.String(string(bbytes)),
					DataType:    aws.String("String"),
				}
			}
		}
	}
	id := uuid.NewV4().String()
	entry := &sqs.SendMessageBatchRequestEntry{
		Id:                &id,
		MessageAttributes: attrMap,
		MessageBody:       aws.String(msgBody),
	}

	c.msgChan <- entry
	return nil
}
