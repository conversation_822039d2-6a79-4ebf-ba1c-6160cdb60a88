package aws

import (
	"encoding/json"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sns"
	"github.com/rs/zerolog/log"
)

// SNSClient SNS client
type SNSClient struct {
	svc      *sns.SNS
	TopicArn string
}

// SNSMessage holder message
type SNSMessage struct {
	DefaultData any `json:"default,omitempty"`
	LambdaData  any `json:"lambda,omitempty"`
	Subject     string
}

// SNSModelMessage struct for sending message on SNS
type SNSModelMessage interface {
	DefaultContent() string
	LambdaContent() string
	SMSContent() string
	EmailContent() string
	Subject() string
}

// MessageModel Model to send SNS
type MessageModel struct {
	TopicArn    string
	EventName   string
	Region      string `json:"region" validate:"required"`
	DefaultData any    `json:"default,omitempty"`
	LambdaData  any    `json:"lambda,omitempty"`
	SMSData     string `json:"sms,omitempty"`
	EmailData   string `json:"email,omitempty"`
	PhoneNumber *string
	Subject     string
	Content     any
	MaxRetries  int
	Deplay      int64
}

// NewSNSClient new SNS client
func NewSNSClient(sess *session.Session, topicArn string) *SNSClient {
	svc := sns.New(sess)
	return &SNSClient{svc: svc, TopicArn: topicArn}
}

// Send SNS message
func (c *SNSClient) Send(message SNSModelMessage) error {
	log.Info().Msg("SNS SENDING")
	log.Info().Interface("Data", message)
	messageModel := SNSMessage{
		DefaultData: message.DefaultContent(),
		LambdaData:  message.LambdaContent(),
		Subject:     message.Subject(),
	}

	data, err := json.Marshal(messageModel)

	snsMessage := &sns.PublishInput{
		Message:          aws.String(string(data)),
		MessageStructure: aws.String("json"),
		Subject:          aws.String(messageModel.Subject),
		TopicArn:         aws.String(c.TopicArn),
	}
	sess := session.New()
	snsService := sns.New(sess)
	result, err := snsService.Publish(snsMessage)
	if err != nil {
		log.Error().Str("error", err.Error()).Msg("SNSClient.Send: failed to publish msg")
		return err
	}
	log.Debug().Interface("result", result).Msg("SNSClient.Send")
	return err
}
