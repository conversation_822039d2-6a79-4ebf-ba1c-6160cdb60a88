/*
Reference
https://docs.aws.amazon.com/ses/latest/DeveloperGuide/send-personalized-email-api.html
*/

package aws

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strings"
	"text/template"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ses"
	"github.com/rs/zerolog/log"
	"github.com/spf13/cast"
	"gopkg.in/gomail.v2"
)

type SESClient struct {
	svc *ses.SES
}

func NewSESClient(sess *session.Session) *SESClient {
	svc := ses.New(sess)
	return &SESClient{svc: svc}
}

type Email struct {
	From        string   `json:"email"` // From source email
	To          []string // To destination email(s)
	Subject     string   // Subject text to send
	Text        string   // Text is the text body representation
	HTML        string   // HTMLBody is the HTML body representation
	ReplyTo     []string // Reply-To email(s)
	Cc          []string // Cc email(s)
	BCC         []string // BCC email(s)
	CharSet     string   `default:"UTF-8"`
	Attachments []string // Path of attach file
}

type TemplatedEmail struct {
	From         string   `json:"email"` // From source email
	To           []string // To destination email(s)
	Subject      string   // Subject text to send
	ReplyTo      []string // Reply-To email(s)
	TemplateName string
	Parameters   map[string]string
}

// SendRawEmail
func (c *SESClient) SendRawEmail(emailData Email) error {
	// Create the raw message
	msg := gomail.NewMessage()
	if len(emailData.Cc) > 0 {
		msg.SetHeader("Cc", strings.Join(emailData.Cc, ","), "")
	}
	if len(emailData.BCC) > 0 {
		msg.SetHeader("BCC", strings.Join(emailData.BCC, ","), "")
	}
	msg.SetHeader("From", emailData.From)
	msg.SetHeader("To", strings.Join(emailData.To, ","))
	msg.SetHeader("Subject", emailData.Subject)
	msg.SetBody("text/html", emailData.HTML)
	for _, i := range emailData.Attachments {
		msg.Attach(i)
	}
	var emailRaw bytes.Buffer
	msg.WriteTo(&emailRaw)
	// Assemble the email.
	params := &ses.SendRawEmailInput{
		RawMessage: &ses.RawMessage{ // Required
			Data: emailRaw.Bytes(), // Required
		},
		Destinations: []*string{},
		Source:       aws.String(emailData.From),
	}
	result, err := c.svc.SendRawEmail(params)
	// Display error messages if they occur.
	if err != nil {
		if aerr, ok := err.(awserr.Error); ok {
			switch aerr.Code() {
			case ses.ErrCodeMessageRejected:
				return fmt.Errorf("%s, %s", ses.ErrCodeMessageRejected, aerr.Error())
			case ses.ErrCodeMailFromDomainNotVerifiedException:
				return fmt.Errorf("%s, %s", ses.ErrCodeMailFromDomainNotVerifiedException, aerr.Error())
			case ses.ErrCodeConfigurationSetDoesNotExistException:
				return fmt.Errorf("%s, %s", ses.ErrCodeConfigurationSetDoesNotExistException, aerr.Error())
			default:
				return fmt.Errorf("%s", aerr.Error())
			}
		} else {
			// Print the error, cast err to awserr.Error to get the Code and
			// Message from an error.
			return fmt.Errorf("%s", aerr.Error())
		}
	}
	log.Debug().Strs("json", emailData.To).Msg("Email Sent to address")
	log.Debug().Interface("json", result).Msg("Send Email.Result data")
	return nil
}

// SendEmailWithTemplate
func (c *SESClient) SendEmailWithTemplate(emailData TemplatedEmail) error {
	// Assemble the email.
	templateData, err := json.Marshal(emailData.Parameters)
	if err != nil {
		return err
	}
	input := &ses.SendTemplatedEmailInput{
		Destination: &ses.Destination{
			CcAddresses: []*string{},
			ToAddresses: aws.StringSlice(emailData.To),
		},
		ReplyToAddresses: aws.StringSlice(emailData.ReplyTo),
		Template:         aws.String(emailData.TemplateName),
		TemplateData:     aws.String(string(templateData)),
		Source:           aws.String(emailData.From),
	}
	// Attempt to send the email.
	result, err := c.svc.SendTemplatedEmail(input)
	// Display error messages if they occur.
	if err != nil {
		if aerr, ok := err.(awserr.Error); ok {
			switch aerr.Code() {
			case ses.ErrCodeMessageRejected:
				return fmt.Errorf("%s, %s", ses.ErrCodeMessageRejected, aerr.Error())
			case ses.ErrCodeMailFromDomainNotVerifiedException:
				return fmt.Errorf("%s, %s", ses.ErrCodeMailFromDomainNotVerifiedException, aerr.Error())
			case ses.ErrCodeConfigurationSetDoesNotExistException:
				return fmt.Errorf("%s, %s", ses.ErrCodeConfigurationSetDoesNotExistException, aerr.Error())
			default:
				return fmt.Errorf("%s", aerr.Error())
			}
		} else {
			// Print the error, cast err to awserr.Error to get the Code and
			// Message from an error.
			return fmt.Errorf("%s", aerr.Error())
		}
	}
	log.Debug().Strs("json", emailData.To).Msg("Email Sent to address")
	log.Debug().Interface("json", result).Msg("Send Email.Result data")
	return nil
}

// SendEmail
func (c *SESClient) SendEmail(emailData Email) error {
	// Assemble the email.
	input := &ses.SendEmailInput{
		Destination: &ses.Destination{
			CcAddresses: []*string{},
			ToAddresses: aws.StringSlice(emailData.To),
		},
		Message: &ses.Message{
			Body: &ses.Body{
				Html: &ses.Content{
					Charset: aws.String(emailData.CharSet),
					Data:    aws.String(emailData.HTML),
				},
				Text: &ses.Content{
					Charset: aws.String(emailData.CharSet),
					Data:    aws.String(emailData.Text),
				},
			},
			Subject: &ses.Content{
				Charset: aws.String(emailData.CharSet),
				Data:    aws.String(emailData.Subject),
			},
		},
		Source:           aws.String(emailData.From),
		ReplyToAddresses: aws.StringSlice(emailData.ReplyTo),
		// Uncomment to use a configuration set
		//ConfigurationSetName: aws.String(ConfigurationSet),
	}

	// Attempt to send the email.
	result, err := c.svc.SendEmail(input)

	// Display error messages if they occur.
	if err != nil {
		if aerr, ok := err.(awserr.Error); ok {
			switch aerr.Code() {
			case ses.ErrCodeMessageRejected:
				return fmt.Errorf("%s, %s", ses.ErrCodeMessageRejected, aerr.Error())
			case ses.ErrCodeMailFromDomainNotVerifiedException:
				return fmt.Errorf("%s, %s", ses.ErrCodeMailFromDomainNotVerifiedException, aerr.Error())
			case ses.ErrCodeConfigurationSetDoesNotExistException:
				return fmt.Errorf("%s, %s", ses.ErrCodeConfigurationSetDoesNotExistException, aerr.Error())
			default:
				return fmt.Errorf("%s", aerr.Error())
			}
		} else {
			// Print the error, cast err to awserr.Error to get the Code and
			// Message from an error.
			return fmt.Errorf("%s", aerr.Error())
		}
	}
	log.Debug().Strs("json", emailData.To).Msg("Email Sent to address")
	log.Debug().Interface("json", result).Msg("Send Email.Result data")
	return nil
}

// SESTemplate ... contains template name, subject, html part & text part
type SESTemplate struct {
	TemplateName string `json:"templateName"`
	Subject      string `json:"subject"`
	HTMLBody     string `json:"htmlBody"`
	TextBody     string `json:"textBody"`
}

func formatSESTemplate(template SESTemplate) *ses.Template {
	return &ses.Template{
		TemplateName: aws.String(template.TemplateName),
		SubjectPart:  aws.String(template.Subject),
		HtmlPart:     aws.String(template.HTMLBody),
		TextPart:     aws.String(template.TextBody),
	}
}

// CreateSESTemplate ... add new template to AWS SES panel
func (c *SESClient) CreateSESTemplate(template SESTemplate) error {
	createTemplateInput := &ses.CreateTemplateInput{
		Template: formatSESTemplate(template),
	}
	_, err := c.svc.CreateTemplate(createTemplateInput)
	if err != nil {
		return err
	}
	return nil
}

// GetSESTemplateByName ... get template by using template name
func (c *SESClient) GetSESTemplateByName(templateName string) (map[string]string, error) {
	templateOutput, err := c.svc.GetTemplate(&ses.GetTemplateInput{TemplateName: aws.String(templateName)})

	if err != nil {
		return nil, err
	}
	template := map[string]string{
		"templateName": cast.ToString(templateOutput.Template.TemplateName),
		"subject":      cast.ToString(templateOutput.Template.SubjectPart),
		"htmlBody":     cast.ToString(templateOutput.Template.HtmlPart),
		"textBody":     cast.ToString(templateOutput.Template.TextPart),
	}
	return template, nil
}

// UpdateSESTemplate ... update an existing template to AWS SES panel
func (c *SESClient) UpdateSESTemplate(template SESTemplate) error {
	updateTemplateInput := &ses.UpdateTemplateInput{
		Template: formatSESTemplate(template),
	}
	_, err := c.svc.UpdateTemplate(updateTemplateInput)
	if err != nil {
		return err
	}
	return nil
}

// DeleteSESTemplateByName ... delete template by using template name
func (c *SESClient) DeleteSESTemplateByName(templateName string) error {
	_, err := c.svc.DeleteTemplate(&ses.DeleteTemplateInput{TemplateName: aws.String(templateName)})
	if err != nil {
		return err
	}
	return nil
}

func (c *SESClient) PrepareAndSendRawEmail(sender string, receiver []string, cc []string, bcc []string, subject string, body string, attachments []string, data map[string]any) error {
	subject, body, err := ParseTemplate(subject, body, data)
	if err != nil {
		return err
	}
	email := Email{
		From:        sender,
		To:          receiver,
		Cc:          cc,
		BCC:         bcc,
		Subject:     subject,
		Text:        string(body),
		HTML:        body,
		Attachments: attachments,
	}
	err = c.SendRawEmail(email)
	if err != nil {
		return err
	}
	return nil
}

func ParseTemplate(subject, body string, data map[string]any) (string, string, error) {
	subj, err := template.New("").Parse(subject)
	if err != nil {
		return "", "", err
	}
	bufSubj := new(bytes.Buffer)
	err = subj.Execute(bufSubj, data)
	subjectString := bufSubj.String()

	b, err := template.New("").Parse(body)
	if err != nil {
		return "", "", err
	}
	bufBody := new(bytes.Buffer)
	err = b.Execute(bufBody, data)
	bodyString := bufBody.String()

	return subjectString, bodyString, nil
}
