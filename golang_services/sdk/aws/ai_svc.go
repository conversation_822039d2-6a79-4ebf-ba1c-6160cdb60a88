package aws

import (
	"sync"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/rekognition"
	"github.com/thoas/go-funk"
)

const (
	passportLabel = "Passport"
	faceLabel     = "Face"
	humanLabel    = "Human"
	personLabel   = "Person"
	otherLabel    = "Other"
)

type ImageCategorizationResult struct {
	Passports      []string
	PersonalPhotos []string
	Others         []string
}

type ImageAnalyzer interface {
	CategorizeImages(bucket string, keys []string) (*ImageCategorizationResult, error)
	GetFaceSimilarity(bucket, img1, img2 string) (float64, error)
}

type RekImageAnalyzer struct {
	sess         *session.Session
	rekongnition *rekognition.Rekognition
}

func NewRekImageAnalyzer(sess *session.Session) *RekImageAnalyzer {
	rek := rekognition.New(sess)
	return &RekImageAnalyzer{
		sess:         sess,
		rekongnition: rek,
	}
}

type labelResult struct {
	label      string
	err        error
	key        string
	confidence float64
}

func (a *RekImageAnalyzer) CategorizeImages(bucket string, keys []string) (*ImageCategorizationResult, error) {
	var wg sync.WaitGroup
	resCh := make(chan *labelResult, len(keys))
	sem := make(chan struct{}, 5) // limit to 5 concurrent goroutines

	for i := range keys {
		k := keys[i]
		wg.Add(1)

		go func(k string) {
			defer wg.Done()

			// Acquire semaphore
			sem <- struct{}{}
			defer func() { <-sem }() // Release semaphore

			req := &rekognition.DetectLabelsInput{
				MinConfidence: aws.Float64(80),
				MaxLabels:     aws.Int64(10),
				Image: &rekognition.Image{
					S3Object: &rekognition.S3Object{
						Bucket: aws.String(bucket),
						Name:   aws.String(k),
					},
				},
			}
			res, err := a.rekongnition.DetectLabels(req)
			if err != nil {
				resCh <- &labelResult{
					err: err,
					key: k,
				}
				return
			}

			allLabels := []string{}
			for _, l := range res.Labels {
				allLabels = append(allLabels, *l.Name)
			}

			// Categorize based on detected labels
			if funk.ContainsString(allLabels, "Passport") || funk.ContainsString(allLabels, "Id Cards") {
				resCh <- &labelResult{
					label:      passportLabel,
					key:        k,
					confidence: 100,
				}
				return
			}

			if funk.ContainsString(allLabels, "Face") {
				resCh <- &labelResult{
					label:      faceLabel,
					key:        k,
					confidence: 100,
				}
				return
			}

			resCh <- &labelResult{
				label: otherLabel,
				key:   k,
			}
		}(k)
	}

	go func() {
		wg.Wait()
		close(resCh)
	}()

	res := &ImageCategorizationResult{}
	for ret := range resCh {
		if ret.err != nil {
			return nil, ret.err
		}
		switch ret.label {
		case passportLabel:
			res.Passports = append(res.Passports, ret.key)
		case faceLabel:
			res.PersonalPhotos = append(res.PersonalPhotos, ret.key)
		default:
			res.Others = append(res.Others, ret.key)
		}
	}

	return res, nil
}

func (a *RekImageAnalyzer) GetFaceSimilarity(bucket, img1, img2 string) (float64, error) {
	req := &rekognition.CompareFacesInput{
		QualityFilter:       aws.String(rekognition.QualityFilterAuto),
		SimilarityThreshold: aws.Float64(50),
		SourceImage: &rekognition.Image{
			S3Object: &rekognition.S3Object{
				Bucket: aws.String(bucket),
				Name:   aws.String(img1),
			}},
		TargetImage: &rekognition.Image{
			S3Object: &rekognition.S3Object{
				Bucket: aws.String(bucket),
				Name:   aws.String(img2),
			}},
	}
	res, err := a.rekongnition.CompareFaces(req)
	if err != nil {
		return 0, err
	}
	if len(res.FaceMatches) == 0 {
		return 0, nil
	}
	for _, m := range res.FaceMatches {
		if m.Similarity != nil {
			return *m.Similarity, nil
		}
	}
	return 0, nil
}
