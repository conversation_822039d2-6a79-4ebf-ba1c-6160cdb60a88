package visaprices

import (
	"bitbucket.org/persistence17/aria/golang_services/models"
)

func GetLowestPrice(prices []*models.VisaCustomerPrice) *models.VisaCustomerPrice {
	if len(prices) == 0 {
		return nil
	}
	lowest := prices[0]
	for i := 1; i < len(prices); i++ {
		p := prices[i]
		if p == nil {
			continue
		}
		if lowest == nil {
			lowest = p
			continue
		}
		cur, _ := GetTotal(p)
		low, _ := GetTotal(lowest)
		if cur < low {
			lowest = p
		}
	}
	return lowest
}

func GetAdditionalFee(fees *models.PropertyMap) float32 {
	if fees == nil {
		return 0.0
	}
	var total float32 = 0.0

	if len(*fees) > 0 {
		for _, p := range *fees {
			if cost, ok := p.(float64); ok {
				total += float32(cost)
			}
		}
	}
	return total
}

func GetTotal(vcp *models.VisaCustomerPrice) (float64, string) {
	if vcp == nil {
		return 0.0, ""
	}
	total := vcp.Price
	currency := vcp.Currency

	if vcp.AdditionalFee != nil && len(*vcp.AdditionalFee) > 0 {
		for _, p := range *vcp.AdditionalFee {
			if cost, ok := p.(float64); ok {
				total = total + cost
			}
		}
	}
	vcp.Amount = total
	return total, currency
}
