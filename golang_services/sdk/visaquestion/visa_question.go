package visaquestion

import (
	"sort"
	"strings"

	"github.com/iancoleman/strcase"

	"bitbucket.org/persistence17/aria/golang_services/models"
)

func OneSchemaObjectToQuestions(obj map[string]any, objKey string) []*models.Question {
	if objKey == "" || len(obj) == 0 {
		return []*models.Question{}
	}
	category := obj<PERSON>ey
	switch objKey {
	case "passport":
		category = "passport_info"
	case "travel":
		category = "travel_info"
	}
	// check top level for `required` list first
	requiredMap := map[string]bool{}
	if required, ok := obj["required"]; ok {
		if list, ok1 := required.([]any); ok1 {
			for _, item := range list {
				if s, ok2 := item.(string); ok2 {
					requiredMap[s] = true
				}
			}
		}
	}
	var res []*models.Question
	for k, v := range obj {
		switch k {
		case "properties":
			if properties, ok := v.(map[string]any); ok {
				// only check properties field for `required` list when required map is empty,
				// which means there is no `required` list on top level of the object
				if len(requiredMap) == 0 {
					if required, ok := properties["required"]; ok {
						if list, ok1 := required.([]any); ok1 {
							for _, item := range list {
								if s, ok2 := item.(string); ok2 {
									requiredMap[s] = true
								}
							}
						}
					}
				}

				for kk, vv := range properties {
					switch kk {
					case "required":
						continue
					default:
						if oneProp, ok := vv.(map[string]any); ok {
							q := onePropertyToQuestion(oneProp, kk)
							if q == nil {
								continue
							}
							(*q)["optional"] = !requiredMap[kk]
							(*q)["category"] = category
							res = append(res, q)
						}
					}
				}
			}
		default:
			continue
		}
	}
	return res
}

func onePropertyToQuestion(prop map[string]any, propName string) *models.Question {
	if propName == "" || len(prop) == 0 {
		return nil
	}
	res := models.Question{"id": propName}
	title := strcase.ToDelimited(propName, ' ')
	if title != "" {
		res["title"] = title
	}
	format := ""
	if v, ok := prop["format"]; ok {
		if s, ok1 := v.(string); ok1 {
			switch s {
			case "date-time":
				format = "datetime"
			case "date":
				format = s
			default:
				format = ""
			}
		}
	}
	if options, ok := prop["enum"]; ok {
		format = "list"
		res["option_id"] = options
	}

	res["format"] = format
	for k, v := range prop {
		switch k {
		case "type", "format", "enum", "propertyOrder", "title":
			continue
		default:
			fn := strcase.ToSnake(k)
			res[fn] = v
		}
	}
	return &res
}

func sortAddtionalQuestions(questions []*models.Question) []*models.Question {
	sortMap := map[string]int{
		"departure_airport":   1,
		"departure_airline":   2,
		"departure_timestamp": 3,
		"departure_flight":    4,
		"arrival_airport":     5,
		"arrival_airline":     6,
		"arrival_timestamp":   7,
		"arrival_flight":      8,
	}

	sort.SliceStable(questions, func(i, j int) bool {
		q1, q2 := questions[i], questions[j]
		id1, id2 := "", ""
		if id, ok := (*q1)["id"]; ok {
			if s, ok1 := id.(string); ok1 {
				id1 = s
			}
		} else {
			return false
		}
		if id, ok := (*q2)["id"]; ok {
			if s, ok1 := id.(string); ok1 {
				id2 = s
			}
		} else {
			return true
		}
		if id1 == "" {
			return false
		}
		idx1, ok1 := sortMap[id1]
		idx2, ok2 := sortMap[id2]
		if ok1 && ok2 {
			return idx1 < idx2
		} else if ok1 && !ok2 {
			return true
		} else if !ok1 && ok2 {
			return false
		} else {
			return strings.Compare(id1, id2) == -1
		}
	})
	return questions
}
