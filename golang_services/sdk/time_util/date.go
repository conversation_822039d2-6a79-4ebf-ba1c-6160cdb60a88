package time_util

import (
	"time"

	"github.com/go-playground/tz"
	"github.com/pariz/gountries"
)

var (
	countries    = gountries.New()
	usWestLoc, _ = time.LoadLocation("America/Los_Angeles")
)

func DateToDateInLoc(date time.Time, countryCode string) time.Time {
	yyyy, MM, dd := date.Date()
	alfa2 := countries.Alpha3ToAlpha2[countryCode]
	c, found := tz.GetCountry(alfa2)
	if found {
		loc := GetLocationByName(c.Zones[0].Name)
		return time.Date(yyyy, MM, dd, 0, 0, 1, 0, loc)
	}
	// if location not found, use US west locatioin
	return time.Date(yyyy, MM, dd, 0, 0, 1, 0, usWestLoc)
}
