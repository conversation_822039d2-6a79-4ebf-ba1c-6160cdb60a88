package time_util

import (
	"sync"
	"time"

	"github.com/bluele/gcache"
	"github.com/rs/zerolog/log"
)

var (
	l                 gcache.Cache
	locationCacheOnce sync.Once
	utc               = time.UTC
)

func GetLocationByName(name string) *time.Location {
	locationCacheOnce.Do(func() {
		l = gcache.New(64).
			LRU().
			LoaderFunc(
				func(key any) (v any, err error) {
					return time.LoadLocation(key.(string))
				}).Build()
	})

	loc, err := l.Get(name)
	if err != nil {
		log.Error().Interface("error", err).Msg("GetLocationByName failed")
		return utc
	}
	return loc.(*time.Location)
}
