package main

import (
	"fmt"
	"time"
)

func IsWorkingTime(t time.Time) bool {
	loc, err := time.LoadLocation("Asia/Ho_Chi_Minh")
	if err != nil {
		// Handle error
		return false
	}

	// Convert the given time to the local time zone
	t = t.In(loc)

	// Check whether the time falls within working hours
	return t.Hour() >= 9 && t.Hour() <= 17 && t.Weekday() != time.Saturday && t.Weekday() != time.Sunday
}

func main() {
	t := time.Date(2023, 4, 26, 11, 0, 0, 0, time.UTC)
	fmt.Println(IsWorkingTime(t)) // Output: true
}
