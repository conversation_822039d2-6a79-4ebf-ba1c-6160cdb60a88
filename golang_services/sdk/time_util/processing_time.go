package time_util

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/go-playground/tz"
	"github.com/rickar/cal/v2"
	"github.com/rickar/cal/v2/aa"
	"github.com/rickar/cal/v2/gb"
	"github.com/rickar/cal/v2/us"
)

func ProcessingTimeStrToDuration(processingTime string) (time.Duration, error) {
	if processingTime == "" {
		return 0, nil
	}

	if strings.HasSuffix(processingTime, "h") {
		n := processingTime[0 : len(processingTime)-1]
		h, err := strconv.ParseInt(n, 10, 32)
		if err != nil {
			return 0, err
		}
		return time.Duration(h) * time.Hour, nil
	}

	if strings.HasSuffix(processingTime, "d") {
		n := processingTime[0 : len(processingTime)-1]
		d, err := strconv.ParseInt(n, 10, 32)
		if err != nil {
			return 0, err
		}
		return time.Duration(24*d) * time.Hour, nil
	}

	if strings.HasSuffix(processingTime, "w") {
		n := processingTime[0 : len(processingTime)-1]
		d, err := strconv.ParseInt(n, 10, 32)
		if err != nil {
			return 0, err
		}
		return 7 * time.Duration(24*d) * time.Hour, nil
	}

	if strings.HasSuffix(processingTime, "M") {
		n := processingTime[0 : len(processingTime)-1]
		d, err := strconv.ParseInt(n, 10, 32)
		if err != nil {
			return 0, err
		}
		return 30 * time.Duration(24*d) * time.Hour, nil
	}

	return 0, fmt.Errorf("unsupported duration format")
}

func AddDurationSkipWeekends(t time.Time, d time.Duration) time.Time {
	// if t is weekend, add days
	if t.Weekday() == time.Sunday {
		t = t.AddDate(0, 0, 1)
	} else if t.Weekday() == time.Saturday {
		t = t.AddDate(0, 0, 2)
	}
	res := t.Add(d)
	if d < 24*time.Hour {
		return res
	}
	numOfWeeks := int(d.Hours()) / 24 / 5

	// add 2 days for every 5 days
	res = res.AddDate(0, 0, numOfWeeks*2)

	// if res is in weekend, add days
	if res.Weekday() == time.Sunday {
		res = res.AddDate(0, 0, 1)
	} else if res.Weekday() == time.Saturday {
		res = res.AddDate(0, 0, 2)
	}
	return res
}

func NewBusinessCalendar(country string) *cal.BusinessCalendar {
	c := cal.NewBusinessCalendar()
	// Add support country holidays
	switch country {
	case "us":
		c.AddHoliday(us.Holidays...)
	case "gb":
		c.AddHoliday(gb.Holidays...)
	default:
		c.AddHoliday(aa.NewYear)
	}
	// TODO: add more holiday use lib or our config

	// Set working day Mon - Fri, 9am-6pm work week
	c.SetWorkday(time.Saturday, false)
	c.SetWorkday(time.Sunday, false)
	c.SetWorkHours(9*time.Hour, 17*time.Hour)
	return c
}

func CalculateWorkingDays(from time.Time, processingTime, country string) (t time.Time, err error) {
	c := NewBusinessCalendar(country)
	workingDurationDay := 8 * time.Hour

	if strings.HasSuffix(processingTime, "h") {
		n := processingTime[0 : len(processingTime)-1]
		h, err := strconv.Atoi(n)
		if err != nil {
			return t, err
		}

		t = from.Add(time.Duration(h) * time.Hour)
		for c.WorkHoursInRange(from, t) < time.Duration(h)*time.Hour {
			t = t.Add(1 * time.Hour) // Increase by 1 hour
		}
		return t, nil
	}

	if strings.HasSuffix(processingTime, "d") {
		n := processingTime[0 : len(processingTime)-1]
		d, err := strconv.ParseInt(n, 10, 32)
		if err != nil {
			return t, err
		}

		t = from.Add(time.Duration(d) * 24 * time.Hour)
		for c.WorkHoursInRange(from, t) < time.Duration(d)*workingDurationDay {
			t = t.Add(24 * time.Hour) // Increase by 1 day
		}
		return t, nil
	}

	if strings.HasSuffix(processingTime, "w") {
		n := processingTime[0 : len(processingTime)-1]
		d, err := strconv.Atoi(n)
		if err != nil {
			return t, err
		}

		t = from.AddDate(0, 0, 7*d)
		return t, nil
	}

	if strings.HasSuffix(processingTime, "M") {
		n := processingTime[0 : len(processingTime)-1]
		d, err := strconv.Atoi(n)
		if err != nil {
			return t, err
		}

		t = from.AddDate(0, d, 0)
		return t, nil
	}
	return t, fmt.Errorf("invalid proccessing time format")
}

func CalculateReverseWorkingDays(from time.Time, processingTime, country string) (t time.Time, err error) {
	c := NewBusinessCalendar(country)
	workingDurationDay := 8 * time.Hour

	if strings.HasSuffix(processingTime, "h") {
		n := processingTime[0 : len(processingTime)-1]
		h, err := strconv.Atoi(n)
		if err != nil {
			return t, err
		}

		t = from.Add(-time.Duration(h) * time.Hour)
		for c.WorkHoursInRange(t, from) < time.Duration(h)*time.Hour {
			t = t.Add(-1 * time.Hour) // Increase by 1 hour
		}
		return t, nil
	}

	if strings.HasSuffix(processingTime, "d") {
		n := processingTime[0 : len(processingTime)-1]
		d, err := strconv.ParseInt(n, 10, 32)
		if err != nil {
			return t, err
		}

		t = from.Add(-time.Duration(d) * 24 * time.Hour)
		fmt.Println(t)
		fmt.Println(from)
		for c.WorkHoursInRange(t, from) < time.Duration(d)*workingDurationDay {
			t = t.Add(-24 * time.Hour) // Increase by 1 day
		}
		return t, nil
	}

	if strings.HasSuffix(processingTime, "w") {
		n := processingTime[0 : len(processingTime)-1]
		d, err := strconv.Atoi(n)
		if err != nil {
			return t, err
		}

		t = from.AddDate(0, 0, -7*d)
		return t, nil
	}

	if strings.HasSuffix(processingTime, "M") {
		n := processingTime[0 : len(processingTime)-1]
		d, err := strconv.Atoi(n)
		if err != nil {
			return t, err
		}

		t = from.AddDate(0, -d, 0)
		return t, nil
	}
	return t, fmt.Errorf("invalid proccessing time format")
}

func CalculateProcessingTimeWorkingDays(from time.Time, processingTime, country string) (result *time.Time, err error) {
	if processingTime == "" {
		return nil, nil
	}
	result, err = nil, nil

	to, unit, err := parseProcessingTime(processingTime)
	if err != nil {
		return nil, err
	}

	if to > 0 {
		if val, err := CalculateNormalDays(from, fmt.Sprintf("%d%s", to, unit)); err == nil {
			result = aws.Time(val)
		}
	}
	return

}

// 1d, 1w, 1M, 10-12w
func parseProcessingTime(processingTime string) (to int, unit string, err error) {
	unit = processingTime[len(processingTime)-1:]
	err = nil

	if strings.Contains(processingTime, "-") {
		parts := strings.Split(processingTime[:len(processingTime)-1], "-")
		to, err = strconv.Atoi(parts[1])
		if err != nil {
			return
		}
	} else {
		to, err = strconv.Atoi(processingTime[:len(processingTime)-1])
		if err != nil {
			return
		}
	}
	return
}

func CalculateNormalDays(from time.Time, processingTime string) (t time.Time, err error) {
	suffix := processingTime[len(processingTime)-1:]
	n := processingTime[:len(processingTime)-1]

	switch suffix {
	case "h":
		h, err := strconv.Atoi(n)
		if err != nil {
			return t, err
		}
		t = from.Add(time.Duration(h) * time.Hour)
		t = t.Truncate(time.Hour)
		t = t.Add(time.Hour)

	case "d":
		d, err := strconv.ParseInt(n, 10, 32)
		if err != nil {
			return t, err
		}
		t = from.Add(time.Duration(d) * 24 * time.Hour)
		t = t.Truncate(time.Hour * 24)
		t = t.AddDate(0, 0, 1)

	case "w":
		d, err := strconv.Atoi(n)
		if err != nil {
			return t, err
		}
		t = from.AddDate(0, 0, 7*d)
		t = t.Truncate(time.Hour * 24)
		t = t.AddDate(0, 0, 1)

	case "M":
		d, err := strconv.Atoi(n)
		if err != nil {
			return t, err
		}
		t = from.AddDate(0, d, 0)
		t = t.Truncate(time.Hour * 24)
		t = t.AddDate(0, 0, 1)

	default:
		return t, fmt.Errorf("invalid processing time format")
	}

	return t, nil
}

func CountryToTimeZone(alpha3 string) (*time.Location, error) {
	country, _ := tz.GetCountry(alpha3)
	return time.LoadLocation(country.Zones[0].Name)
}

func DiffTwoTimeZone(loc1, loc2 *time.Location) (time.Duration, error) {
	t1 := time.Now().In(loc1)
	year, month, day := t1.Year(), t1.Month(), t1.Day()
	hour, minute, second := t1.Hour(), t1.Minute(), t1.Second()
	t11 := time.Date(year, month, day, hour, minute, second, 0, loc1)
	t21 := time.Date(year, month, day, hour, minute, second, 0, loc2)
	return t11.Sub(t21), nil
}

func LocalTimeAsUTC(t time.Time) time.Time {
	year, month, day, hour, minute, second := t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), t.Second()
	return time.Date(year, month, day, hour, minute, second, 0, time.UTC)
}
