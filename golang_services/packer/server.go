package main

import (
	"errors"
	"fmt"

	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"

	"bitbucket.org/persistence17/aria/golang_services/handlers/packers"
	packers_v1 "bitbucket.org/persistence17/aria/golang_services/handlers/packers/v1"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/urfave/cli"
)

func startPackerService() cli.ActionFunc {
	return func(c *cli.Context) error {
		port := c.String("port")

		e, err := newPackerServer(c)
		if err != nil {
			return err
		}

		return e.Run(":" + port)
	}
}

func newPackerServer(c *cli.Context) (*gin.Engine, error) {
	e := gin.New()
	e.Use(logger.LoggerMW(), logger.CustomLoggerMW(), gzip.Gzip(gzip.DefaultCompression))
	logger.InitGraylogLogger()
	e.Use(logger.GraylogLoggerMiddleware(logger.DefaultRequestLoggerConfig()))

	awsUSConfig := utils.GetMapEnv("ad_aws")
	region := awsUSConfig["region"].(string)
	if region == "" {
		return nil, fmt.Errorf("missing region in ad_aws")
	}

	awsConfig := aws.NewConfig().WithRegion(region).WithLogLevel(aws.LogOff)

	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return nil, err
	}

	// sqs
	_, err = loadPackerRelatedQueues(utils.GetMapEnv("ad_sqs"), e, sess)
	if err != nil {
		return nil, err
	}

	// cors
	corsConf := cors.DefaultConfig()
	corsConf.AddAllowHeaders("x-access-token", "Language")
	corsConf.AllowAllOrigins = true

	// db
	dbConfigMap := utils.GetMapEnv("ad_db")
	if dbConfigMap["write_host"].(string) == "" {
		log.Fatal().Err(fmt.Errorf("missing write_host in ad_db"))
	}
	if dbConfigMap["dbname"].(string) == "" {
		log.Fatal().Err(fmt.Errorf("missing dbname in ad_db"))
	}
	if dbConfigMap["username"].(string) == "" {
		log.Fatal().Err(fmt.Errorf("missing username in ad_db"))
	}
	if dbConfigMap["password"].(string) == "" {
		log.Fatal().Err(fmt.Errorf("missing password in ad_db"))
	}
	conn, err := db.NewAuroraDBFromConfigMap(dbConfigMap)
	if err != nil {
		return nil, err
	}

	svcConfigMap := utils.GetMapEnv("ad_packer_service")
	log.Info().Interface("ad_packer_service", svcConfigMap).Msg("Service config from ENV")

	bucketMap := utils.GetMapEnv("ad_s3")
	log.Info().Interface("ad_s3", bucketMap).Msg("S3 bucket config from ENV")

	userAccountServiceConfigMap := utils.GetMapEnv("ad_user_account_service")
	log.Info().Interface("account-service-config", userAccountServiceConfigMap).Msg("Account Service config from ENV")

	apiTokenMap := utils.GetMapEnv("ad_api_token")
	log.Info().Interface("ad_api_token", apiTokenMap).Msg("Token config from ENV")
	if err := middlewares.InitSecretMap("ad_secrets"); err != nil {
		return nil, err
	}

	e.Use(
		cors.New(corsConf),
		middlewares.AuthorizationMW(),
		middlewares.VisaDaoMW(conn),
		middlewares.NewS3PresignerMW(sess),
		middlewares.ServiceConfigMapMW(svcConfigMap),
		middlewares.NewS3BucketsMW(bucketMap),
		middlewares.AccountServiceConfigMW(userAccountServiceConfigMap),
		middlewares.APITokenConfigMW(apiTokenMap),
	)

	packers.LoadV1PackersHandlers(e)

	return e, nil
}

func loadPackerRelatedQueues(conf map[string]any, r gin.IRouter, sess *session.Session) (gin.IRouter, error) {
	log.Info().Interface("sqs_config", conf).Msg("SQS config from env")

	prefix := conf["url_prefix"].(string)

	// offline
	packerOfflineQueue := prefix + "/" + conf["packer_offline_sqs_name"].(string)
	if packerOfflineQueue == "" {
		return nil, errors.New("missing packer_offline_sqs_name")
	}
	packerOfflineQ := awslib.NewSQSClient(sess, packerOfflineQueue)

	// online
	packerOnlineQueue := prefix + "/" + conf["packer_online_sqs_name"].(string)
	if packerOnlineQueue == "" {
		return nil, errors.New("missing packer_online_sqs_name")
	}
	packerOnlineQ := awslib.NewSQSClient(sess, packerOnlineQueue)

	// website
	websiteQueue := prefix + "/" + conf["website_sqs_name"].(string)
	if websiteQueue == "" {
		return nil, errors.New("missing website_sqs_name")
	}
	websiteQ := awslib.NewSQSClient(sess, websiteQueue)

	// online captcha
	packerOnlineCaptchaQueue := prefix + "/" + conf["packer_online_captcha_sqs_name"].(string)
	if packerOnlineCaptchaQueue == "" {
		return nil, errors.New("missing packer_online_captcha_sqs_name")
	}
	packerOnlineCaptchaQ := awslib.NewSQSClient(sess, packerOnlineCaptchaQueue)

	r.Use(
		middlewares.SimpleQueueMW(packerOfflineQ, packers_v1.PackerOfflineQueue),
		middlewares.SimpleQueueMW(packerOnlineQ, packers_v1.PackerOnlineQueue),
		middlewares.SimpleQueueMW(packerOnlineCaptchaQ, packers_v1.PackerOnlineCaptchaQueue),
		middlewares.SimpleQueueMW(websiteQ, packers_v1.FormTypeWebsite),
	)

	return r, nil
}
