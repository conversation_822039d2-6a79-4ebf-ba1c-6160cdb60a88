package main

import (
	"log"
	"os"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	"github.com/urfave/cli"
)

func main() {
	app := cli.NewApp()
	app.Name = "aria-packer"
	app.Usage = "packer service for Aria"
	app.Commands = []cli.Command{
		serviceCmd(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}
}

func serviceCmd() cli.Command {
	return cli.Command{
		Name:      "service",
		ShortName: "p",
		Usage:     "Start packer service for Aria",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:   "port",
				Usage:  "Port the server listens to",
				EnvVar: "PORT",
				Value:  "3000",
			},
			cli.StringFlag{
				Name:     "ad_sqs",
				Usage:    "Env for s3 config",
				EnvVar:   "ad_sqs",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_aws",
				Usage:    "Env for aws config",
				EnvVar:   "ad_aws",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_db",
				Usage:    "Env for db config",
				EnvVar:   "ad_db",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_s3",
				Usage:    "Env for db config",
				EnvVar:   "ad_s3",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_packer_service",
				Usage:    "Env for packer service config",
				EnvVar:   "ad_packer_service",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_user_account_service",
				Usage:    "Env for user account configuration",
				EnvVar:   "ad_user_account_service",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_secrets",
				Usage:    "Env for secret configuration",
				EnvVar:   "ad_secrets",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_api_token",
				Usage:    "Env for api token configuration",
				EnvVar:   "ad_api_token",
				Required: true,
			},
			flags.LogLevelFlag,
		},
		Action: startPackerService(),
	}
}
