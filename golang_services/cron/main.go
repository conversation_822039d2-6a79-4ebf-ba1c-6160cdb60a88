package main

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"sync"

	"github.com/robfig/cron"
	"github.com/tidwall/gjson"
)

type APIResponse struct {
	URL      string
	Response string
}

func main() {
	c := cron.New()
	API_BASE := gjson.Parse(os.Getenv("ad_endpoint")).Get("api_base_url").String()

	cronJobs := []struct {
		schedule string
		paths    []string
	}{
		// Run every 1 second
		// {"* * * * * *", []string{
		// 	"/api/cron/orders",
		// 	"/api/cron/site/finances",
		// }},
	}

	externalCronJobs := []struct {
		schedule string
		paths    []string
	}{
		// Run every 6 hours
		{"0 0 */6 * * *", []string{
			"https://form.ariadirectcorp.com/api/flightstats/cron_get_flights",
			// "https://form.ariadirectcorp.com/api/flightstats/cron_aircraft",
		}},
		{"0 0 * * 1 *", []string{
			"https://form.ariadirectcorp.com/api/visa_check/crawl_data",
		}},
	}
	for _, job := range cronJobs {
		urls := make([]string, len(job.paths))
		for i, path := range job.paths {
			urls[i] = fmt.Sprintf("%s%s", API_BASE, path)
		}
		if err := c.AddFunc(job.schedule, func() { fetchAPIs(urls) }); err != nil {
			fmt.Println("Error adding cron job:", err)
			return
		}
	}

	for _, job := range externalCronJobs {
		if err := c.AddFunc(job.schedule, func() { fetchAPIs(job.paths) }); err != nil {
			fmt.Println("Error adding cron job:", err)
			return
		}
	}

	c.Start()
	select {}
}

func fetchAPIs(urls []string) {
	var wg sync.WaitGroup
	responseCh := make(chan APIResponse, len(urls))

	for _, url := range urls {
		wg.Add(1)
		go func(url string) {
			defer wg.Done()
			response, err := callAPI(url)
			if err != nil {
				fmt.Printf("Error calling %s: %v\n", url, err)
				return
			}
			responseCh <- APIResponse{URL: url, Response: response}
		}(url)
	}

	wg.Wait()
	close(responseCh)

	for response := range responseCh {
		fmt.Printf("Response from %s: %s\n", response.URL, response.Response)
	}
}

func callAPI(url string) (string, error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", err
	}

	INTERNAL_API_KEY := os.Getenv("INTERNAL_API_KEY")
	req.Header.Set("x-access-token", INTERNAL_API_KEY)

	client := http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(body), nil
}
