package shipments

import (
	shipments_v1 "bitbucket.org/persistence17/aria/golang_services/handlers/shipments/v1"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"

	"github.com/gin-gonic/gin"
)

func LoadV1ShipmentsHandlers(r gin.IRouter) gin.IRouter {
	g1 := r.Group("/v1/shipment")
	{
		g1.GET("version", shipments_v1.VersionHandler)
		g1.GET("status", shipments_v1.VersionHandler)
		shipments := g1.Group("/shipments")
		{
			shipments.POST("/address/validation", shipments_v1.ValidationAddressHandler)
			// shipments.GET("/shipping-requirements", shipments_v1.QueryShippingRequirements)
		}
	}
	return r
}

func InitShipmentHandlers(db *db.AuroraDB) {
	shipments_v1.InitV1ShipmentsHandlers(db)
}
