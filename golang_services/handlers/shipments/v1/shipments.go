package shipments_v1

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/version"
)

var (
	shippingData           db.ShippingData
	countryListCanValidate = []string{"USA", "US", "CAN", "CA"}
)

func InitV1ShipmentsHandlers(database *db.AuroraDB) {
	shippingData = db.NewShippingDataDao(database)
}

func VersionHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "OK",
		"message": "This is Aria shipment service",
		"version": version.Version,
	})
}

type ValidationAddressReq struct {
	ID          string         `json:"id"`
	AddressInfo models.Address `json:"address_info"`
}

func ValidationAddressHandler(c *gin.Context) {
	var req []ValidationAddressReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	if len(req) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
		})
		return
	}
	var resps []map[string]any
	fdx := middlewares.GetFedexMW(c)
	for i := 0; i < len(req); i++ {
		resp := map[string]any{
			"id": req[i].ID,
		}
		if funk.ContainsString(countryListCanValidate, req[i].AddressInfo.Country) {
			result, err := fdx.ValidationAddress(req[i].AddressInfo)
			if err != nil {
				resp["error"] = err
			} else {
				resp["valid_address"] = result
			}
		} else {
			resp["error"] = errors.CantValidateAddress
		}
		resps = append(resps, resp)
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    resps,
	})
}

type DeleteShippingLabelReq struct {
	TrackingNumber string `json:"tracking_number"`
}

func DeleteShippingLabel(c *gin.Context) {
	var req DeleteShippingLabelReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	fdx := middlewares.GetFedexMW(c)

	err := fdx.DeleteShipment(req.TrackingNumber)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
