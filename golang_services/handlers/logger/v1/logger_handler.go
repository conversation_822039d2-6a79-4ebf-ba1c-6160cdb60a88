package v1

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/response"
)

type LogOrderEvent struct {
	ServiceType string `json:"service_type"`
	OrderID     int64  `json:"order_id"`
	Event       string `json:"event"`
	Message     string `json:"message"`
	LogType     string `json:"log_type"`
}

// SaveLogOrderEvent save log
func SaveLogOrderEvent(c *gin.Context) {
	var req LogOrderEvent

	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	orm := middlewares.GetORMDao(c)

	orm.Table("log_order_events").Create(&req)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
