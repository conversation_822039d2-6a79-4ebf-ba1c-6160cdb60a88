package payments_v1

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/gin-gonic/gin"
	"github.com/lib/pq"
	"github.com/pariz/gountries"
	"github.com/rs/xid"
	"github.com/rs/zerolog/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
	"gopkg.in/guregu/null.v3"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/payments"
	"bitbucket.org/persistence17/aria/golang_services/sdk/summary"
	"bitbucket.org/persistence17/aria/golang_services/sdk/time_util"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/sdk/version"
)

type PreOrderRequest struct {
	CartID      int     `json:"cart_id"`
	Method      string  `json:"method"`
	Total       float64 `json:"total"`
	Email       string  `json:"email"`
	Description string  `json:"description"`
	Type        string  `json:"type"`
}

func VersionHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "OK",
		"message": "This is Aria Payments service",
		"version": version.Version,
	})
}

var gocountry = gountries.New()

func PreOrderHandler(c *gin.Context) {
	var req PreOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if req.Total <= 0.0 || req.CartID <= 0 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid input"))
		return
	}

	authInfo := middlewares.GetAuthInfo(c)
	visaDao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	etsDao := db.EtsDao{Db: visaDao.DB().Db}

	if req.Email == "" {
		req.Email = authInfo.Email
	}

	cart, err := visaDao.GetCartByID(req.CartID)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if cart == nil {
		response.HandleResponse(c, nil, fmt.Errorf("Cart not found"))
		return
	}

	userEmails := []string{}
	for _, item := range cart.ServiceData {
		if item.ServiceDetail.ShipmentInfo.String == "" {
			continue
		}
		shipment, err := visaDao.GetVisaShipment(item.ServiceDetail.ShipmentInfo.String)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		userEmails = append(userEmails, shipment.ShippingContact.Email)
		summary.CalculateETSServiceSummary(&etsDao, item.ServiceDetail, models.CartSelection{
			PromotionCode: item.ServiceDetail.Summary.PromotionCode,
		}, true)
	}

	cart, err = visaDao.GetCartByID(req.CartID) // Reload data
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if cart.Cart.UserID != authInfo.UserID {
		response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("You can not checkout this cart"))
		return
	}

	if len(cart.ServiceData) == 0 {
		response.HandleResponse(c, nil, fmt.Errorf("No items for checkout"))
		return
	}

	if cart.Payment == nil || cart.Payment.ID == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Cart does not have payment info"))
		return
	}

	dao, err := middlewares.GetPaymentsDao(c)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	cartPayment, err := dao.GetPayment(cart.Payment.ID)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if cartPayment == nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("cart does not have payment info"))
		return
	}

	buff, err := json.Marshal(cartPayment)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	cartPaymentJ := gjson.ParseBytes(buff)

	if cartPayment.Total != req.Total || req.Total != cartPaymentJ.Get("properties.summary.total").Float() {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("total does not match with record"))
		return
	}

	if val, ok := payments.TransactionFee[cartPayment.Method]; ok && val > 0 && cartPaymentJ.Get("properties.summary.transaction_fee").Float() <= 0 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("transaction fee is incorrect"))
		return
	}

	if cartPayment.Status == models.PaymentStatusPending && cartPayment.ExternalTransactionID != "" && strings.EqualFold(cartPayment.Method, req.Method) {
		// get existing order response
		if cartPayment.Properties != nil {
			if resp, ok := (*cartPayment.Properties)["external_order"]; ok {
				c.JSON(http.StatusOK, gin.H{
					"data":    resp,
					"success": true,
				})
				return
			}
		}
	}

	if cartPayment.Method != "" && !strings.EqualFold(cartPayment.Method, req.Method) {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest,
			fmt.Errorf("payment method does not match, try re-open package if need to change payment method"))
		return
	}

	if cartPayment.Status != "" && cartPayment.Status != models.PaymentStatusOpen {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("order is already checkout checkout"))
		return
	}

	resp := &payments.PlaceOrderResponse{}
	if req.Method == payments.CorpPay {
		corp, err := visaDao.GetCorporationByOrgID(authInfo.OrgID.Int64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err,
			})
			return
		}
		if corp == nil || corp.Status != "active" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "only corporation can use this method",
			})
			return
		}
		cartPayment.Status = models.PaymentStatusSuccess
		cartPayment.ExternalTransactionID = req.Description

		// Update service data
		orderIDs := funk.Map(cart.ServiceData, func(item models.CartItem) string {
			return item.ProductID
		}).([]string)

		if len(orderIDs) > 0 {
			if _, err = visaDao.DB().Db.Db.Exec("UPDATE service_orders SET status = $1, order_time = $2, updated_at = $3 WHERE id = ANY($4)",
				models.EtsOrderStatusPaid, time.Now(), time.Now(), pq.Array(orderIDs)); err != nil {
				response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
				return
			}

		}

		// Update cart
		if err = visaDao.UpdateCart(cart.Cart.ID, map[string]any{
			"status":     models.CartStatus.Completed,
			"is_current": false,
		}); err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}

		// Update payment status
		payment, err := dao.GetPayment(cart.Payment.ID)
		if err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}
		if payment.Properties == nil {
			payment.Properties = &models.PropertyMap{}
		}
		(*payment.Properties)["remain"] = 0
		(*payment.Properties)["paid"] = payment.Total
		if err = dao.UpdatePayment(&models.Payment{
			ID:         cart.Payment.ID,
			Properties: payment.Properties,
			Status:     models.PaymentStatusSuccess,
		}); err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}
	} else {
		paymentClient, err := middlewares.GetPaymentClients(c, req.Method)
		if err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}

		preorder := &payments.PlaceOrderRequest{
			CartID:            cart.Cart.ID,
			AriaTransactionID: cartPayment.ID,
			NotifyURL:         middlewares.GetPaymentNotifyURL(c, req.Method),
			TotalAmount:       cartPayment.Total,
			Email:             req.Email,
			Description:       req.Description,
			Type:              req.Type,
			Currency:          cartPayment.Currency,
		}

		resp, err = paymentClient.PlaceOrder(preorder)
		if err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}
		cartPayment.Status = models.PaymentStatusPending
		if resp.OrderID != "" {
			cartPayment.ExternalTransactionID = resp.OrderID
		} else {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("vendor did not respond with order ID"))
			return
		}
	}

	if req.Method == payments.ZellePay || req.Method == payments.BankPay {
		orderIDs := funk.Map(cart.ServiceData, func(item models.CartItem) string {
			return item.ProductID
		}).([]string)
		if _, err = visaDao.DB().Db.Db.Exec("UPDATE service_orders SET status = $1, order_time = $2 WHERE id = ANY($3)",
			models.EtsOrderStatusWaitingPayment, time.Now(), pq.Array(orderIDs)); err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}

		if _, err = visaDao.DB().Db.Db.Exec("UPDATE cart SET is_current = $1 WHERE id = $2", false, cart.Cart.ID); err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}

		// Get user info
		user, err := visaDao.GetUserByID(authInfo.UserID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		shipment, err := visaDao.GetVisaShipment(cart.ServiceData[0].ServiceDetail.ShipmentInfo.String)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
		shipmentJ := utils.StructToJSON(shipment).Get("shipping_contact")

		fullName := "SIR/MADAM"
		if user.GivenName != "" {
			fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(user.GivenName), strings.TrimSpace(user.Surname)))
		} else if shipmentJ.Get("given_name").String() != "" {
			fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(shipmentJ.Get("given_name").String()), strings.TrimSpace(shipmentJ.Get("surname").String())))
		}

		emailConfigMap := utils.GetMapEnv("ad_email")
		supportEmail, ok := emailConfigMap["support"]
		if !ok {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "missing support in ad_email",
			})
			return
		}

		// send email async
		go func() {
			orders := []map[string]string{}
			for _, service := range cart.ServiceData {
				country, _ := gocountry.FindCountryByAlpha(service.ServiceDetail.Service.Country)
				orders = append(orders, map[string]string{
					"OrderID": service.ProductID,
					"Service": service.ServiceDetail.Service.ServiceType,
					"Country": country.Name.Common,
				})
			}

			if req.Method == payments.ZellePay {
				if err := sendMessageToSQS("send_payment_notification", map[string]any{
					"CartNo":       cart.Cart.ID,
					"Amount":       fmt.Sprintf("%0.2f", req.Total),
					"Currency":     cart.Payment.Currency,
					"DueDate":      time.Now().AddDate(0, 0, 1).Format("Mon, 02 Jan 2006"),
					"ContactEmail": c.GetString("zellepay_contact_email"),
					"ContactPhone": c.GetString("zellepay_contact_phone"),
					"FullName":     fullName,
					"Orders":       orders,
					"OrderIDs":     strings.Join(orderIDs, ", "),
				}, req.Email, userEmails, []string{cast.ToString(supportEmail)}); err != nil {
					log.Error().Err(err)
				}
			}

			if req.Method == payments.BankPay {
				if err := sendMessageToSQS("send_payment_notification_with_bank", map[string]any{
					"CartNo":     cart.Cart.ID,
					"Amount":     fmt.Sprintf("%0.2f", req.Total),
					"Currency":   cart.Payment.Currency,
					"DueDate":    time.Now().AddDate(0, 0, 1).Format("Mon, 02 Jan 2006"),
					"BankName":   c.GetString("bank_1_name"),
					"BankOwner":  c.GetString("bank_1_owner"),
					"BankNumber": c.GetString("bank_1_number"),
					"FullName":   fullName,
					"Orders":     orders,
					"OrderIDs":   strings.Join(orderIDs, ", "),
				}, req.Email, userEmails, []string{cast.ToString(supportEmail)}); err != nil {
					log.Error().Err(err)
				}
			}

		}()

	}

	if cartPayment.Properties != nil {
		(*cartPayment.Properties)["external_order"] = resp
	} else {
		properties := models.PropertyMap{
			"external_order": resp,
		}
		cartPayment.Properties = &properties
	}
	cartPayment.Method = req.Method
	if err := dao.UpdatePayment(cartPayment); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if err := updateOrderProcessingTime(visaDao.DB(), cart.Payment.ID); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    resp,
	})
}

func updateOrderProcessingTime(dao *db.Dao, paymentID string) error {
	rows, err := dao.Db.PSQL.Select(`so.id, COALESCE(ets.attributes->>'processing_time','')`).From("service_orders so").
		LeftJoin("ets ON ets.id = so.service_id").
		Where("so.payment_id = ?", paymentID).
		RunWith(dao.Db.Db).Query()
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var (
			orderID        int
			processingTime string
		)
		if err := rows.Scan(&orderID, &processingTime); err != nil {
			return err
		}

		process, err := time_util.CalculateProcessingTimeWorkingDays(time.Now(), processingTime, "")
		if err != nil {
			return err
		}

		if _, err := dao.Db.PSQL.Update("service_orders").
			Set("processing_time_expired_at", process).
			Where("id = ?", orderID).RunWith(dao.Db.Db).Exec(); err != nil {
			return err
		}
	}
	return nil
}

func PublicPreOrderHandler(c *gin.Context) {
	var req PreOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if req.Total <= 0.0 || req.CartID <= 0 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid input"))
		return
	}

	visaDao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	cart, err := visaDao.GetCartByID(req.CartID)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if cart == nil {
		response.HandleResponse(c, nil, fmt.Errorf("Cart not found"))
		return
	}

	if len(cart.ServiceData) == 0 {
		response.HandleResponse(c, nil, fmt.Errorf("No items for checkout"))
		return
	}

	if cart.Payment == nil || cart.Payment.ID == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Cart does not have payment info"))
		return
	}

	dao, err := middlewares.GetPaymentsDao(c)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	payment, err := dao.GetPayment(cart.Payment.ID)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if payment == nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("cart does not have payment info"))
		return
	}

	buff, err := json.Marshal(payment)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	orderJSON := gjson.ParseBytes(buff)

	if payment.Total != req.Total || req.Total != orderJSON.Get("properties.summary.total").Float() {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("total does not match with record"))
		return
	}

	resp := &payments.PlaceOrderResponse{}
	if req.Method == payments.AuthorizePay || req.Method == payments.PayPal {
		paymentClient, err := middlewares.GetPaymentClients(c, req.Method)
		if err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}

		preorder := &payments.PlaceOrderRequest{
			CartID:            cart.Cart.ID,
			AriaTransactionID: payment.ID,
			NotifyURL:         middlewares.GetPaymentNotifyURL(c, req.Method),
			TotalAmount:       payment.Total,
			Email:             req.Email,
			Description:       req.Description,
			Type:              req.Type,
			Currency:          payment.Currency,
		}

		resp, err = paymentClient.PlaceOrder(preorder)
		if err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}
		payment.Status = models.PaymentStatusPending
		if resp.OrderID != "" {
			payment.ExternalTransactionID = resp.OrderID
		} else {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("vendor did not respond with order ID"))
			return
		}
	} else {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Unsupported payment method"))
		return
	}

	if payment.Properties != nil {
		(*payment.Properties)["external_order"] = resp
	} else {
		properties := models.PropertyMap{
			"external_order": resp,
		}
		payment.Properties = &properties
	}
	payment.Method = req.Method
	if err := dao.UpdatePayment(payment); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    resp,
	})
}

type AuthorizeSignature struct {
	Total       float64 `json:"total"`
	Method      string  `json:"method"`
	Currency    string  `json:"currency"`
	Description string  `json:"description"`
	URL         string  `json:"url"`
	Signature   string  `json:"signature"`
	OrderID     int64   `json:"order_id"`
}

func AuthorizeSignatureHandler(c *gin.Context) {
	var req AuthorizeSignature
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if req.Total <= 0.0 || req.Currency == "" || req.Description == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid input"))
		return
	}

	if req.Method != payments.AuthorizePay && req.Method != payments.PayPal && req.Method != payments.ZellePay {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("unsupported payment method"))
		return
	}

	dao, err := middlewares.GetPaymentsDao(c)

	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	visaDao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	adEmail := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String()

	paymentClient, err := middlewares.GetPaymentClients(c, req.Method)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	paymentID := xid.New().String()
	preOrder := &payments.PlaceOrderRequest{
		AriaTransactionID: paymentID,
		NotifyURL:         middlewares.GetPaymentNotifyURL(c, req.Method),
		TotalAmount:       req.Total,
		Email:             adEmail,
		Description:       req.Description,
		Currency:          req.Currency,
	}

	resp, err := paymentClient.PlaceOrder(preOrder)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	req.Signature = resp.Signature
	req.URL = utils.StructToJSON(resp.Extra).Get("paymentUrl").String()

	payment := &models.Payment{
		ID:                    xid.New().String(),
		ExternalTransactionID: paymentID,
		Count:                 1,
		Method:                req.Method,
		Currency:              req.Currency,
		UnitPrice:             req.Total,
		Total:                 req.Total,
		Status:                models.PaymentStatusPending,
		Properties: &models.PropertyMap{
			"paid":   0,
			"total":  req.Total,
			"remain": req.Total,
			"summary": models.ServiceSummary{
				Currency:       req.Currency,
				ShippingPrice:  0,
				SubTotal:       req.Total,
				Total:          req.Total,
				Discount:       0,
				TransactionFee: 0,
			},
		},
		CreatedAt: time.Now(),
	}

	if err := dao.InsertPayment(payment); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	cart := &models.Cart{
		PaymentID: null.NewString(payment.ID, true),
		Status:    models.CartStatus.Checkout,
	}

	visaDao.DB().Db.Db.QueryRow("SELECT user_id FROM service_orders WHERE id = ?", req.OrderID).Scan(&cart.UserID)

	visaDao.CreateCart(cart)

	visaDao.UpdateCart(cart.ID, map[string]any{
		"payment_id": payment.ID,
		"status":     models.CartStatus.Checkout,
		"is_current": false,
	})

	if req.OrderID > 0 {
		visaDao.AddItemToCart(&models.CartItem{
			CartID:      cart.ID,
			ProductID:   fmt.Sprintf("%d", req.OrderID),
			ProductType: models.ProductType.AirPortService,
		})

		visaDao.DB().Db.Db.Exec("UPDATE service_orders SET payment_id = $1 WHERE id = $2", payment.ID, req.OrderID)

		if _, err = visaDao.DB().Db.Db.Exec("UPDATE service_orders SET status = $1, order_time = $2 WHERE id = $3",
			models.EtsOrderStatusWaitingPayment, time.Now(), req.OrderID); err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}

		// Get user info
		user, err := visaDao.GetUserByID(cart.UserID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		emailConfigMap := utils.GetMapEnv("ad_email")
		supportEmail, ok := emailConfigMap["support"]
		if !ok {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "missing support in ad_email",
			})
			return
		}

		// send email async
		go func() {
			orders := []map[string]string{}
			// country, _ := gocountry.FindCountryByAlpha(service.ServiceDetail.Service.Country)
			orders = append(orders, map[string]string{
				"OrderID": cast.ToString(req.OrderID),
				"Service": "",
				"Country": "VNM",
			})

			if req.Method == payments.ZellePay {
				if err := sendMessageToSQS("send_payment_notification", map[string]any{
					"CartNo":       cart.ID,
					"Amount":       fmt.Sprintf("%0.2f", req.Total),
					"Currency":     "USD",
					"DueDate":      time.Now().AddDate(0, 0, 1).Format("Mon, 02 Jan 2006"),
					"ContactEmail": c.GetString("zellepay_contact_email"),
					"ContactPhone": c.GetString("zellepay_contact_phone"),
					"FullName":     user.GivenName,
					"Orders":       orders,
					"OrderIDs":     strings.Join(lo.Map(orders, func(item map[string]string, _ int) string { return item["OrderID"] }), ", "),
				}, user.Email, []string{}, []string{cast.ToString(supportEmail)}); err != nil {
					log.Error().Err(err)
				}
			}
		}()
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    req,
	})
}

func sendMessageToSQS(template string, parameters map[string]any, to string, cc []string, bcc []string) error {
	message := &map[string]any{
		"template_name": template,
		"to":            to,
		"cc":            cc,
		"bcc":           bcc,
		"parameters":    parameters,
	}

	messageData, err := json.Marshal(message)
	if err != nil {
		return err
	}

	sqsConfig := map[string]any{}
	if err := json.Unmarshal([]byte(os.Getenv("ad_sqs")), &sqsConfig); err != nil {
		return err
	}

	var awsJSON = gjson.ParseBytes([]byte(os.Getenv("ad_aws")))

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(awsJSON.Get("region").String()),
	})
	if err != nil {
		return err
	}

	qURL := sqsConfig["url_prefix"].(string) + "/" + sqsConfig["notification_sqs_name"].(string)
	fmt.Println(qURL)
	_, err = sqs.New(sess).SendMessage(&sqs.SendMessageInput{
		MessageBody: aws.String(string(messageData)),
		QueueUrl:    &qURL,
	})
	return err
}

func CallbackNotifHandler(c *gin.Context) {
	pType := c.Param("type")
	h, err := middlewares.GetPaymentNotificationHandler(c, pType)
	if err != nil {
		// TODO: log error
		log.Error().Str("error", err.Error()).Msg("failed to get notification handler")
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	update, err := h.ReadNotification(c.Request)
	if err != nil {
		// TODO: log error
		log.Error().Str("error", err.Error()).Msg("failed to read notification")
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}

	dao, err := middlewares.GetPaymentsDao(c)
	if err != nil {
		log.Error().Str("error", err.Error()).Msg("failed to get payment dao")
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	existing, err := dao.GetPayment(update.ID)
	if err != nil {
		log.Error().Str("error", err.Error()).Msg("failed to get payment in db")
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if existing.ID != update.ID && existing.ExternalTransactionID != update.ID {
		log.Error().Str("error", err.Error()).Msg("aria ID does not match")
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("aria ID does not match"))
		return
	}

	existing.Status = update.Status
	existing.Method = strings.ToUpper(pType) //Update method with newest callback

	if existing.Properties != nil {
		(*existing.Properties)["notification"] = (*update.Properties)["notification"]
	} else {
		existing.Properties = update.Properties
	}

	if update.ExternalTransactionID != "" {
		existing.ExternalTransactionID = update.ExternalTransactionID
	}

	visaDao, err := middlewares.GetVisaDao(c)
	if err != nil {
		log.Error().Str("error", err.Error()).Msg("failed to get visa dao")
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	cart, err := visaDao.GetCartByPayment(existing.ID)
	if err != nil {
		(*existing.Properties)["pkg_error"] = "failed to get cart for payment"
	} else {
		curentCart, err := visaDao.GetUserCart(cart.Cart.UserID)
		if err != nil {
			(*existing.Properties)["cart_error"] = "failed to get user current cart"
		}

		// Update Airport services
		orderIDs := funk.Map(cart.ServiceData, func(item models.CartItem) string {
			return item.ProductID
		}).([]string)
		if len(orderIDs) > 0 {
			switch update.Status {
			case models.PaymentStatusSuccess:
				if existing.Properties != nil {
					(*existing.Properties)["remain"] = 0
					(*existing.Properties)["paid"] = cart.Payment.Total
					(*existing.Properties)["total"] = cart.Payment.Total
				}
				if _, err = visaDao.DB().Db.Db.Exec("UPDATE service_orders SET status = $1, order_time = $2, updated_at = $2 WHERE id = ANY($3)",
					models.EtsOrderStatusPaid, time.Now(), pq.Array(orderIDs)); err != nil {
					(*existing.Properties)["pkg_update_error"] = err.Error()
				}

			case models.PaymentStatusClosed, models.PaymentStatusFailed:
				// if _, err = visaDao.DB().Db.Db.Exec("UPDATE service_orders SET status = $1, order_time = $2, updated_at = $2 WHERE id = ANY($3)",
				// 	models.PackageStatusReady, time.Now(), pq.Array(orderIDs)); err != nil {
				// 	(*existing.Properties)["pkg_update_error"] = err.Error()
				// }

				// Save payment fail order to current cart
				for _, orderID := range orderIDs {
					if _, err := visaDao.DB().Db.Db.Exec(`INSERT INTO cart_item(cart_id, product_type, product_id) 
						VALUES ($1,$2,$3) ON CONFLICT DO UPDATE product_id = EXCLUDED.product_id`, curentCart.Cart.ID, models.ProductType.AirPortService, orderID); err != nil {
						(*existing.Properties)["pkg_update_error"] = err.Error()
					}

				}
			}
		}
	}

	if err = dao.UpdatePayment(existing); err != nil {
		log.Error().Str("error", err.Error()).Msg("failed to update payment")
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	// Update cart
	if err = visaDao.UpdateCart(cart.Cart.ID, map[string]any{
		"status":     models.CartStatus.Completed,
		"is_current": false,
	}); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	h.AckNotification(c.Writer)
}

func CheckCallbackHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func GetPaymentByID(c *gin.Context) {
	paymentID := c.Param("id")
	if paymentID == "" {
		response.HandleResponse(c, nil, fmt.Errorf("invalid payment id"))
	}

	paymentDao, err := middlewares.GetPaymentsDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	payment, err := paymentDao.GetPayment(paymentID)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	response.HandleResponse(c, payment, nil)
}
