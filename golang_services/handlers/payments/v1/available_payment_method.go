package payments_v1

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/thoas/go-funk"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/payments"
)

var (
	availablePaymentsByCurrency map[string][]string
	supportedCurrencies         []string
	defaultPaymentsByRegion     map[string][]*PaymentMethods
	byCurrencyConfChecksum      string
	regionConfChecksum          string

	s3Downloader *awslib.S3Downloader

	byCurrencyConfMux sync.RWMutex
	regionConfMux     sync.RWMutex
)

const (
	localByCurrencyConf = "/tmp/by_currency_conf.json"
	localRegionConf     = "/tmp/region_conf.json"

	defaultCurrency = "USD"
)

type PaymentMethods struct {
	Currency       string   `json:"currency"`
	PaymentMethods []string `json:"payment_methods"`
}

func InitPaymentMethodsConfig(region, bucket, byCurrencyConf, regionConf string) error {
	awsConfig := aws.NewConfig().WithRegion(region).WithLogLevel(aws.LogOff)
	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return err
	}

	s3Downloader = awslib.NewDownloader(sess)
	if err := downloadAndReadConfig(bucket, byCurrencyConf, regionConf); err != nil {
		return err
	}

	ticker := time.NewTicker(10 * time.Minute)
	go func() {
		for {
			select {
			case <-ticker.C:
				if err := downloadAndReadConfig(bucket, byCurrencyConf, regionConf); err != nil {
					log.Error().Str("error", err.Error()).Msg("failed to download and read payment config file")
				}
			}
		}
	}()
	return nil
}

func downloadAndReadConfig(bucket, byCurrencyConf, regionConf string) error {
	defer func() {
		_ = os.Remove(localByCurrencyConf)
		_ = os.Remove(localRegionConf)
	}()
	if err := s3Downloader.DownloadFromS3Bucket(bucket, byCurrencyConf, localByCurrencyConf); err != nil {
		return err
	}
	byt, err := ioutil.ReadFile(localByCurrencyConf)
	if err != nil {
		return err
	}
	hasher := md5.New()
	_, err = hasher.Write(byt)
	if err != nil {
		return err
	}
	checksumByCurrencyConf := fmt.Sprintf("%x", hasher.Sum(nil))
	if checksumByCurrencyConf != byCurrencyConfChecksum {
		var conf map[string][]string
		if err := json.Unmarshal(byt, &conf); err != nil {
			return err
		}
		currencies := make([]string, 0, len(conf))
		for k := range conf {
			currencies = append(currencies, k)
		}
		byCurrencyConfMux.Lock()
		byCurrencyConfChecksum = checksumByCurrencyConf
		availablePaymentsByCurrency = conf
		supportedCurrencies = currencies
		byCurrencyConfMux.Unlock()
	}

	if err := s3Downloader.DownloadFromS3Bucket(bucket, regionConf, localRegionConf); err != nil {
		return err
	}
	hasher.Reset()
	byt2, err := ioutil.ReadFile(localRegionConf)
	if err != nil {
		return err
	}
	_, err = hasher.Write(byt2)
	if err != nil {
		return err
	}
	checksumRegionConf := fmt.Sprintf("%x", hasher.Sum(nil))
	if checksumRegionConf != regionConfChecksum {
		var regionCurrencyConf map[string][]string
		if err := json.Unmarshal(byt2, &regionCurrencyConf); err != nil {
			return err
		}
		conf := map[string][]*PaymentMethods{}
		for region, currencies := range regionCurrencyConf {
			for _, c := range currencies {
				if methods := availablePaymentsByCurrency[c]; len(methods) > 0 {
					conf[region] = append(conf[region], &PaymentMethods{
						Currency:       c,
						PaymentMethods: methods,
					})
				}
			}
		}
		regionConfMux.Lock()
		regionConfChecksum = checksumRegionConf
		defaultPaymentsByRegion = conf
		regionConfMux.Unlock()
	}
	return nil
}

func GetPaymentMethodsByCurrency(c *gin.Context) {
	// read currency input first
	currency := c.Query("currency")
	if currency == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "currency parameter must be provided",
		})
		return
	}
	byCurrencyConfMux.RLock()
	defer byCurrencyConfMux.RUnlock()
	var res []string
	res = append(res, availablePaymentsByCurrency[currency]...)
	res = append(res, getPaymentMethodsByUserType(c)...)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    res,
	})
}

func GetDefaultPayments(c *gin.Context) {
	// read region input first
	region := c.Query("region")
	if region == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "region parameter must be provided",
		})
		return
	}

	regionConfMux.RLock()
	method, ok := defaultPaymentsByRegion[region]
	regionConfMux.RUnlock()
	if !ok {
		// use USD if there is no config for the region
		method = []*PaymentMethods{{
			Currency:       defaultCurrency,
			PaymentMethods: availablePaymentsByCurrency[defaultCurrency],
		}}
	}
	userSpecialMethods := getPaymentMethodsByUserType(c)
	var res []*PaymentMethods
	for _, m := range method {
		mm := &PaymentMethods{
			Currency: m.Currency,
		}
		mm.PaymentMethods = append(mm.PaymentMethods, m.PaymentMethods...)
		if len(userSpecialMethods) > 0 {
			mm.PaymentMethods = append(mm.PaymentMethods, userSpecialMethods...)
		}
		res = append(res, mm)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    res,
	})
}

func GetSupportedCurrencies(c *gin.Context) {
	byCurrencyConfMux.RLock()
	defer byCurrencyConfMux.RUnlock()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    supportedCurrencies,
	})
}

func getPaymentMethodsByUserType(c *gin.Context) []string {
	authInfo := middlewares.GetAuthInfo(c)
	if funk.ContainsString(authInfo.UserTypes, models.UserTypeCorporation) {
		return []string{payments.CorpPay}
	}

	return nil
}
