package payments

import (
	"github.com/gin-gonic/gin"

	payments_v1 "bitbucket.org/persistence17/aria/golang_services/handlers/payments/v1"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
)

func LoadV1PaymentsHandlers(r gin.IRouter) gin.IRouter {
	g := r.Group("/v1/payment")
	{
		g.GET("version", payments_v1.VersionHandler)
		g.GET("status", payments_v1.VersionHandler)
		payments := g.Group("/payments")
		{
			payments.POST("/callback/:type", payments_v1.CallbackNotifHandler)
			payments.GET("/callback/:type", payments_v1.CheckCallbackHandler)

			payments.POST("/public/pre-order", payments_v1.PublicPreOrderHandler)
			payments.POST("/public/authorize-net", payments_v1.AuthorizeSignatureHandler)
			payments.POST("/public/create-payment-urls", payments_v1.AuthorizeSignatureHandler)

			userGroup := payments.Group("/")
			{
				userGroup.Use(middlewares.AuthenticateMW())
				userGroup.GET("/details/:id", payments_v1.GetPaymentByID)
				userGroup.POST("/pre-order", payments_v1.PreOrderHandler)
			}
		}
		paymentMethods := g.Group("/methods")
		paymentMethods.Use(middlewares.AuthenticateMW())
		{
			paymentMethods.GET("/supported-currencies", payments_v1.GetSupportedCurrencies)
			paymentMethods.GET("/methods-by-currency", payments_v1.GetPaymentMethodsByCurrency)
			paymentMethods.GET("/default-methods-by-region", payments_v1.GetDefaultPayments)
		}
	}
	return r
}
