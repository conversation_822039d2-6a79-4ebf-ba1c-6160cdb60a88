package packers_v1

import (
	"net/http"
	"path"

	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/gin-gonic/gin"
	"github.com/pariz/gountries"
)

var query = gountries.New()

type DocumentCallbackReq struct {
	FormType string `json:"form_type"`
	S3Bucket string `json:"form_upload_s3_bucket"`
	S3Key    string `json:"form_upload_s3_key"`
}

func UpdateDocument(c *gin.Context) {
	docID, err := utils.GetIntPathParam(c, "document-id")
	if err != nil || docID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid document ID",
		})
	}

	var req DocumentCallbackReq
	if err = c.ShouldBind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	doc, err := dao.GetDocumentByID(docID)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if doc.ID == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
		})
	}

	// update
	up := map[string]any{
		"status": models.DocumentTypeGenerated,
		"output": &models.FileMap{req.FormType: path.Join(req.S3Bucket, req.S3Key)},
	}
	if err := dao.UpdateDocument(doc.ID, up); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
