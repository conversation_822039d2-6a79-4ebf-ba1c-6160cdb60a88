package packers_v1

import (
	"net/http"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/version"
	"github.com/gin-gonic/gin"
)

const (
	PackerOfflineQueue        = "packer-offline-queue"
	PackerOnlineQueue         = "packer-online-queue"
	PackerOnlineCaptchaQueue  = "packer-online-captcha-queue"
	ApplicationBucketKey      = "ariadirect_prod_applications"
	DocumentTemplateBucketKey = "ariadirect_prod_document_templates"
	expirationForFiles        = 1 * time.Hour
	TimestampFormat           = "2006-01-02T15:04:05Z"
	VisaApplicationForm       = "visa-application-form"
	FormTypeApprovalLetter    = "approval_letter"
	FormTypeVisaForm          = "visa_form"
	FormTypeWebsite           = "website"
)

func VersionHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "OK",
		"message": "This is Aria packers service",
		"version": version.Version,
	})
}

type ApplicationIdsRequest struct {
	ApplicationIds []int `json:"application_ids"`
}

type GenerateFormCaptchaRequest struct {
	Captcha string `json:"captcha"`
}

func sendPackerQueue(c *gin.Context, queueName string, msg map[string]any) error {
	sender := middlewares.GetSimpleQueueSender(c, queueName)
	err := sender.Send(msg)
	if err != nil {
		return err
	}
	return nil
}

func findQueueNameByFormType(formType string) string {
	return FormTypeWebsite
}

func findQueueNameByFormName(formName string) string {
	switch formName {
	case "evisa", "vnm_native_visa", "evisa_urgent":
		return PackerOnlineQueue
	}
	return FormTypeWebsite
}

type UploadRequest struct {
	FileName    string `json:"file_name"`
	ContentType string `json:"content_type"`
}

type UpdateAppFormRequest struct {
	URL string `json:"url"`
}
