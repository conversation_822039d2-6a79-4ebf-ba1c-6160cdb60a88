package packers_v1

import (
	"os"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/tidwall/gjson"
)

func sendEmail(data string) error {
	var awsJSON = gjson.ParseBytes([]byte(os.Getenv("ad_aws")))

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(awsJSON.Get("region").String()),
	})
	if err != nil {
		return err
	}
	svc := sqs.New(sess)
	sqsConf := gjson.Parse(os.Getenv("ad_sqs"))
	qURL := sqsConf.Get("url_prefix").String() + "/" + sqsConf.Get("notification_sqs_name").String()

	if _, err := svc.SendMessage(&sqs.SendMessageInput{
		MessageBody: aws.String(data),
		QueueUrl:    &qURL,
	}); err != nil {
		return err
	}
	return nil
}
