package packers_v1

import (
	"fmt"
	"net/http"
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/notification"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/gin-gonic/gin"
	"github.com/metal3d/go-slugify"
	"github.com/rs/zerolog/log"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
	"gorm.io/datatypes"
)

type generateETSFormReq struct {
	OrderForms []string           `json:"order_forms"`
	TaskForms  map[int64][]string `json:"task_forms"`
}

func GenerateETSForm(c *gin.Context) {
	id := c.Param("order-id")
	var req generateETSFormReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	dao := getEtsDao(c)
	orders, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:                          []string{id},
		IncludeTasks:                true,
		IncludeService:              true,
		IncludePrice:                false,
		IncludePayment:              false,
		IncludeOutputPodsBeforePaid: true,
		Limit:                       1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(orders.Data) != 1 || orders.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}
	order := orders.Data[0]

	for _, formName := range req.OrderForms {
		msg := map[string]any{
			"form_name": formName,
			"order_id":  order.ID,
		}
		if err := sendPackerQueue(c, "website", msg); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}
	for taskID, formNames := range req.TaskForms {
		for _, formName := range formNames {
			msg := map[string]any{
				"form_name": formName,
				"order_id":  order.ID,
				"task_id":   taskID,
			}
			// send queue
			if err := sendPackerQueue(c, "website", msg); err != nil {
				response.HandleResponse(c, nil, err)
				return
			}
		}

	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func GenerateETSFormJob(c *gin.Context) {
	var req generateETSFormReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	for _, formName := range req.OrderForms {
		msg := map[string]any{
			"form_name": formName,
		}
		if err := sendPackerQueue(c, "website", msg); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func getEtsDao(c *gin.Context) db.IEtsDao {
	d, err := middlewares.GetVisaDao(c)
	if err != nil {
		log.Error().Err(err)
		return nil
	}
	return db.NewEtsDao(d.DB().Db)
}

type UpdateServiceTaskFormReq struct {
	FormName     string         `json:"form_name"`
	FormFile     string         `json:"form_file"`
	FormCallback datatypes.JSON `json:"form_callback"`
	Success      bool           `json:"success"`
	TaskID       int64          `json:"task_id"`
}

func UpdateServiceTaskForm(c *gin.Context) {
	var req UpdateServiceTaskFormReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}
	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil || id <= 0 {
		c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
			"success": false,
		})
		return
	}
	dao := getEtsDao(c)
	if req.FormName != "" && req.FormFile != "" {
		m := map[string]string{req.FormName: req.FormFile}
		if err := dao.AddETSTaskOutputFiles(req.TaskID, m); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   err.Error(),
				"success": false,
			})
			return
		}
	}

	if req.FormCallback != nil {
		tasks, _, err := dao.QueryServiceTasks(map[string]any{"id": req.TaskID}, 0, 1)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   err.Error(),
				"success": false,
			})
			return
		}
		task := tasks[0]
		orderResp, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
			ID:             []string{fmt.Sprintf("%d", task.OrderID)},
			IncludeTasks:   true,
			IncludeService: true,
			Limit:          1,
		})
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   err.Error(),
				"success": false,
			})
			return
		}

		order := orderResp.Data[0]
		update := map[string]any{
			"form_callback": datatypes.JSONMap{
				req.FormName: datatypes.JSONMap{
					"success": req.Success,
					"file":    req.FormFile,
					"data":    req.FormCallback,
				},
			},
		}
		if !req.Success {
			update["status"] = models.EtsTaskStatusFormGenerateFailed
		}
		formCJ := gjson.ParseBytes(req.FormCallback)

		oldOutputPodValues := task.OutputPods.ToMapKeyValue()

		if formCJ.Get("document_id").String() != "" {
			task.OutputPods.SetFEValue("application_application_info_registration_code", formCJ.Get("document_id").String())
		}
		if formCJ.Get("email").String() != "" {
			task.OutputPods.SetFEValue("application_application_info_registration_email", formCJ.Get("email").String())
		}
		if formCJ.Get("note").String() != "" {
			task.OutputPods.SetFEValue("application_application_info_note", formCJ.Get("note").String())
		}

		task.OutputPods.SetFEValue("application_application_info_application_status", "created")

		if funk.ContainsString([]string{"ind_visa", "cam_visa"}, req.FormName) {
			task.OutputPods.SetFEValue("application_application_info_note", strings.Join([]string{
				"Code: " + formCJ.Get("document_id").String(),
				"Email: " + formCJ.Get("email").String(),
				"Apply: " + formCJ.Get("created_at").String(),
				"Payment URL: " + formCJ.Get("tracking_url").String(),
			}, ",\n"))

		}

		if funk.ContainsString([]string{"vnm_fastlane"}, req.FormName) {
			task.OutputPods.SetFEValue("application_application_info_note", strings.Join([]string{
				"Tracking URL: " + formCJ.Get("tracking_url").String(),
			}, ",\n"))
			update["output_pods"] = task.OutputPods
		}

		if funk.ContainsString([]string{"hkg_evisa"}, req.FormName) {
			task.OutputPods.SetFEValue("application_application_info_note", strings.Join([]string{
				"Code: " + formCJ.Get("document_id").String(),
				"DOB: " + formCJ.Get("dob").String(),
				"Email: " + formCJ.Get("email").String(),
				"Apply: " + formCJ.Get("created_at").String(),
				"Payment URL: " + formCJ.Get("tracking_url").String(),
			}, ",\n"))
			update["output_pods"] = task.OutputPods
		}
		if funk.ContainsString([]string{"us_passport_renew_online"}, req.FormName) {
			update["output_pods"] = task.OutputPods
		}

		if req.FormName == "vnm_evisa" {
			if formCJ.Get("document_id").String() != "" && formCJ.Get("tracking_url").String() != "" && formCJ.Get("dob").String() != "" {
				note := fmt.Sprintf("Code: %s, DOB: %s, Email: %s, Payment URL: %s",
					formCJ.Get("document_id").String(),
					formCJ.Get("dob").String(),
					formCJ.Get("email").String(),
					formCJ.Get("tracking_url").String(),
				)
				task.OutputPods.SetFEValue("application_application_info_note", note)
			}

			update["output_pods"] = task.OutputPods

			if err := dao.UpdateServiceTask(update, req.TaskID); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   err.Error(),
					"success": false,
				})
				return
			}

			if funk.ContainsString(notification.URGENT_PRODUCT_NAMES, order.Service.Name) {
				isFirstNotification := strings.TrimSpace(cast.ToString(oldOutputPodValues["application_application_info_registration_code"])) == ""
				if !isFirstNotification {
					if err := notification.NotifyViaZaloForUpdateUrgentVisa(dao, cast.ToInt64(order.ID), task.ID, isFirstNotification); err != nil {
						c.JSON(http.StatusBadRequest, gin.H{
							"error":   err.Error(),
							"success": false,
						})
						return
					}
				} else {
					// First time will send when submit
				}

			}
		}

		if req.FormName == "vn_approval_letter" {
			order, err := dao.GetServiceOrderByID(task.OrderID)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   err.Error(),
					"success": false,
				})
				return
			}
			user, err := dao.VisaDB().GetUserByID(order.UserID)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   err.Error(),
					"success": false,
				})
				return
			}
			shipment, err := dao.VisaDB().GetVisaShipment(order.ShipmentInfo.String)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"error":   err.Error(),
					"success": false,
				})
				return
			}
			shipmentJ := utils.StructToJSON(shipment).Get("shipping_contact")
			adEmail := utils.GetMapEnv("ad_email")["services"].(string)

			attachments := []map[string]any{}

			formFile := utils.StructToJSON(task.OutputFiles).Get("vn_approval_letter.file_url").String()
			if formFile != "" {
				bucket, key, _ := utils.UrlToS3BucketAndKey(formFile)
				attachments = append(attachments, map[string]any{
					"bucket":    bucket,
					"key":       key,
					"file_name": fmt.Sprintf("%s_Vietnam_Approval_Letter.pdf", slugify.Marshal(task.GetAppName("new_visa"))),
				})

			}

			// TODO: send 1 email for group
			applications := []map[string]any{}
			applications = append(applications, map[string]any{
				"FullName": task.GetAppName("new_visa"),
			})

			message := map[string]any{
				"template_name": "send_approval_letter_to_user",
				"to":            user.Email,
				"cc":            []string{shipmentJ.Get("email").String()},
				"bcc":           []string{adEmail},
				"parameters": map[string]any{
					"PackageID":    order.ID,
					"AppName":      task.GetAppName("new_visa"),
					"VisaCountry":  "Viet Nam",
					"FullName":     shipmentJ.Get("given_name").String() + " " + shipmentJ.Get("surname").String(),
					"Applications": applications,
				},
				"attachments": attachments,
			}

			if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   err.Error(),
				})
				return
			}
		}
		update["output_pods"] = task.OutputPods

		if err := dao.UpdateServiceTask(update, req.TaskID); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   err.Error(),
				"success": false,
			})
			return
		}
	}

	if err := CheckAllFormGeneratedAndUpdateTask(c, int64(id)); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

type UpdateServiceOrderFormReq struct {
	FormName     string         `json:"form_name"`
	FormFile     string         `json:"form_file"`
	Success      bool           `json:"success"`
	FormCallback datatypes.JSON `json:"form_callback"`
}

func UpdateServiceOrderForm(c *gin.Context) {
	var req UpdateServiceOrderFormReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}
	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil || id <= 0 {
		c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
			"success": false,
		})
		return
	}
	dao := getEtsDao(c)
	if req.FormName != "" && req.FormFile != "" {
		m := map[string]string{req.FormName: req.FormFile}
		if err := dao.AddETSOutputFiles(id, m); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   err.Error(),
				"success": false,
			})
			return
		}
	}

	update := map[string]any{
		"form_callback": datatypes.JSONMap{
			req.FormName: datatypes.JSONMap{
				"success": req.Success,
				"file":    req.FormFile,
				"data":    req.FormCallback,
			},
		},
	}

	if !req.Success {
		update["status"] = models.EtsOrderStatusFormGenerateFailed
	}

	if err := dao.UpdateServiceOrder(update, id); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	if req.FormName == "vn_approval_letter" {
		order, err := dao.GetServiceOrderByID(id)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   err.Error(),
				"success": false,
			})
			return
		}
		user, err := dao.VisaDB().GetUserByID(order.UserID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   err.Error(),
				"success": false,
			})
			return
		}
		shipment, err := dao.VisaDB().GetVisaShipment(order.ShipmentInfo.String)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   err.Error(),
				"success": false,
			})
			return
		}
		shipmentJ := utils.StructToJSON(shipment).Get("shipping_contact")
		adEmail := utils.GetMapEnv("ad_email")["services"].(string)

		attachments := []map[string]any{}

		if req.FormFile != "" {
			bucket, key, _ := utils.UrlToS3BucketAndKey(req.FormFile)
			attachments = append(attachments, map[string]any{
				"bucket":    bucket,
				"key":       key,
				"file_name": fmt.Sprintf("Order_%d_Vietnam_Approval_Letter.pdf", id),
			})

		}

		applications := []map[string]any{}
		tasks, _, err := dao.QueryServiceTasks(map[string]any{"order_id": id}, 0, 0)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   err.Error(),
				"success": false,
			})
			return
		}

		for _, task := range tasks {
			applications = append(applications, map[string]any{
				"FullName": task.GetAppName(""),
			})
		}

		message := map[string]any{
			"template_name": "send_approval_letter_to_user",
			"to":            user.Email,
			"cc":            []string{shipmentJ.Get("email").String()},
			"bcc":           []string{adEmail},
			"parameters": map[string]any{
				"PackageID":    order.ID,
				"VisaCountry":  "Viet Nam",
				"FullName":     shipmentJ.Get("given_name").String() + " " + shipmentJ.Get("surname").String(),
				"Applications": applications,
			},
			"attachments": attachments,
		}

		if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	if err := CheckAllFormGeneratedAndUpdateTask(c, int64(id)); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func CheckAllFormGeneratedAndUpdateTask(c *gin.Context, orderID int64) error {
	dao := getEtsDao(c)
	orders, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:                          []string{fmt.Sprintf("%d", orderID)},
		IncludeTasks:                true,
		IncludeService:              true,
		IncludePrice:                false,
		IncludePayment:              false,
		IncludeOutputPodsBeforePaid: true,
		Limit:                       1,
	})
	if err != nil {
		return err
	}
	order := orders.Data[0]
	attrJ := utils.StructToJSON(order.Service.Attributes)
	orderForms := attrJ.Get("form.order_forms").Array()
	taskForms := attrJ.Get("form.task_forms").Array()
	totalForms := len(orderForms) + len(order.Tasks)*len(taskForms)

	generatedForms := 0
	for _, formName := range orderForms {
		if utils.StructToJSON(order.FormCallback).Get(formName.String()).Get("success").Bool() {
			generatedForms++
		}
	}
	for _, task := range order.Tasks {
		generatedTaskForms := 0
		for _, formName := range taskForms {
			if utils.StructToJSON(task.FormCallback).Get(formName.String()).Get("success").Bool() {
				generatedTaskForms++
			}
		}
		if generatedTaskForms == len(taskForms) {
			if err := dao.UpdateServiceTask(map[string]any{"status": models.EtsTaskStatusConfirmed}, task.ID); err != nil {
				return err
			}
		}
		generatedForms += generatedTaskForms
	}

	if totalForms == generatedForms {
		// TODO: Update order status
	}

	return nil
}
