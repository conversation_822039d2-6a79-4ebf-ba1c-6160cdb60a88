package packers

import (
	packers_v1 "bitbucket.org/persistence17/aria/golang_services/handlers/packers/v1"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"github.com/gin-gonic/gin"
)

func LoadV1PackersHandlers(r gin.IRouter) gin.IRouter {

	g := r.Group("/v1/packer")
	{
		g.GET("version", packers_v1.VersionHandler)
		g.GET("status", packers_v1.VersionHandler)

		internalPackers := g.Group("/internals-packers")
		{
			internalPackers.Use(middlewares.AuthenticateInternalMW())
			// internalPackers.POST("/job/generate-ets-form", packers_v1.GenerateETSFormJob)
			internalPackers.POST("/:order-id/generate-ets-form", packers_v1.GenerateETSForm)
			internalPackers.POST("/:order-id/update-service-task-form", packers_v1.UpdateServiceTaskForm)
			internalPackers.POST("/:order-id/update-service-order-form", packers_v1.UpdateServiceOrderForm)
		}
	}
	return r
}
