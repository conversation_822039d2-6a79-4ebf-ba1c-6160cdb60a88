package device

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models/device"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

type RegisterDeviceRequest struct {
	DeviceID  string `json:"device_id"`
	Token     string `json:"token"`
	Timestamp int64  `json:"timestamp"`
}

type RegisterUserDeviceRequest struct {
	DeviceID  string `json:"device_id"`
	Token     string `json:"token"`
	UserID    string `json:"user_id"`
	Timestamp int64  `json:"timestamp"`
}

type DeviceDAOer interface {
	InsertOrUpdateDeviceToken(dt *device.DeviceToken) error
	UpdateDeviceToken(dt *device.DeviceToken) error
	InsertOrUpdateUserDeviceToken(udt *device.UserDeviceToken) error
	UpdateUserDeviceToken(udt *device.UserDeviceToken) error
	DeleteUserDeviceToken(userID, deviceID string) error
	GetUserDeviceTokenByUserID(userID string) ([]*device.UserDeviceToken, error)
}

type MessagingAdmin interface {
	SubscribeToTopics(token string, topics []string) error
	SendMessage(deviceToken, title, content, url string) error
	SendMessagesToMultiDevices(tokens []string, title, content, url string) error
}

var (
	deviceDao      DeviceDAOer
	firebaseClient MessagingAdmin

	topicsToSubscribe = []string{"test-topic"}
)

func RegisterDevice(c *gin.Context) {
	var req RegisterDeviceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if req.Token == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("empty token"))
		return
	}
	now := time.Now()
	// check if the timestamp is more than 10 minutes old
	// app side should always send a fresh timestamp
	if req.Timestamp < (now.UTC().UnixMilli() - 10*60e3) {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("timestamp too old"))
		return
	}
	err := registerDeviceToken(req.Token, req.Timestamp)
	if err != nil {
		e := fmt.Errorf("failed to register device token, err %w", err)
		log.Error().Err(e).Msg("failed to register device token")
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, e)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func registerDeviceToken(token string, ts int64) error {
	if err := deviceDao.InsertOrUpdateDeviceToken(&device.DeviceToken{
		Token:     token,
		CreatedAt: ts,
		UpdatedAt: ts,
	}); err != nil {
		return err
	}

	if err := firebaseClient.SubscribeToTopics(token, topicsToSubscribe); err != nil {
		return err
	}
	return nil
}

func RegisterUserDevice(c *gin.Context) {
	var req RegisterUserDeviceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	if req.Token == "" || req.UserID == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("empty token/user ID"))
		return
	}
	now := time.Now()
	// check if the timestamp is more than 10 minutes old
	// app side should always send a fresh timestamp
	if req.Timestamp < (now.UTC().UnixMilli() - 10*60e3) {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("timestamp too old"))
		return
	}
	a := middlewares.GetAuthInfo(c)
	if a == nil || a.UserID != req.UserID {
		response.HandleErrResponseWithCode(c, http.StatusUnauthorized, fmt.Errorf("invalid user ID"))
		return
	}
	if err := registerUserDevice(req.DeviceID, req.Token, req.UserID, req.Timestamp); err != nil {
		e := fmt.Errorf("failed to register user device, err %w", err)
		log.Error().Err(e).Msg("failed to register user device")
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, e)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func registerUserDevice(deviceID, token, userID string, ts int64) error {
	if err := registerDeviceToken(token, ts); err != nil {
		return fmt.Errorf("failed to register device token when register user device, err %w", err)
	}
	if err := deviceDao.InsertOrUpdateUserDeviceToken(&device.UserDeviceToken{
		DeviceID:  deviceID,
		Token:     token,
		UserID:    userID,
		CreatedAt: ts,
		UpdatedAt: ts,
	}); err != nil {
		return fmt.Errorf("failed to add user device, err %w", err)
	}
	return nil
}

type SendMsgReq struct {
	Token   string `json:"token"`
	Content string `json:"content"`
	Title   string `json:"title"`
	Url     string `json:"url"`
	UserID  string `json:"user_id"`
}

func SendMsgToDevice(c *gin.Context) {
	var req SendMsgReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	//a := middlewares.GetAuthInfo(c)
	//if a == nil || a.UserID == "" {
	//	response.HandleErrResponseWithCode(c, http.StatusUnauthorized, fmt.Errorf("invalid user ID"))
	//	return
	//}

	var tokens []string
	if req.Token != "" {
		tokens = append(tokens, req.Token)
	} else {
		if req.UserID != "" {
			uds, err := deviceDao.GetUserDeviceTokenByUserID(req.UserID)
			if err != nil {
				log.Error().Str("user_id", req.UserID).Err(err).Msg("failed to get user tokens for user id")
				response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
				return
			}
			for _, ud := range uds {
				tokens = append(tokens, ud.Token)
			}
		}
	}

	if len(tokens) == 0 {
		log.Warn().Interface("request", req).Msg("got empty token list, no message will be sent")
	}

	err := firebaseClient.SendMessagesToMultiDevices(tokens, req.Title, req.Content, req.Url)
	if err != nil {
		byt, _ := json.Marshal(tokens)
		log.Error().Str("device_token", string(byt)).Err(err).Msg("failed to send message to devices")
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

type UnbindRequest struct {
	DeviceID string `json:"device_id"`
	UserID   string `json:"user_id"`
}

func UnbindUserDevice(c *gin.Context) {
	var req UnbindRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if req.DeviceID == "" || req.UserID == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("empty device/user ID"))
		return
	}

	a := middlewares.GetAuthInfo(c)
	if a == nil || a.UserID != req.UserID {
		response.HandleErrResponseWithCode(c, http.StatusUnauthorized, fmt.Errorf("invalid user ID"))
		return
	}
	if err := deviceDao.DeleteUserDeviceToken(req.UserID, req.DeviceID); err != nil {
		log.Error().Err(err).Msg("failed to delete user device")
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
