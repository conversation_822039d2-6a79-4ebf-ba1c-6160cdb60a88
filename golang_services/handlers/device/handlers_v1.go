package device

import (
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"github.com/gin-gonic/gin"
)

func LoadDevicesV1(r gin.IRouter) gin.IRouter {
	g := r.Group("/v1/device_tokens")
	{
		g.GET("/version", VersionHandler)
		g.GET("/status", VersionHandler)

		g.POST("/register", RegisterDevice)
		priv := g.Group("/", middlewares.AuthenticateMW())
		{
			priv.POST("register_user", RegisterUserDevice)
			priv.POST("/unbind", UnbindUserDevice)
		}
		internal := g.Group("/", middlewares.AuthenticateInternalMW())
		{
			internal.POST("send_notification", SendMsgToDevice)
		}
		g.POST("/test", SendMsgToDevice)

	}
	return r
}
