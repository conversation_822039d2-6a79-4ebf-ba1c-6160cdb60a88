package device

import (
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/version"
	"github.com/gin-gonic/gin"
)

func InitDeviceHandlers(conn *db.AuroraDB, projectID, firebaseCreds string) {
	// deviceDao = db.NewDeviceTokenDao(conn)
	// firebaseClient = firebase.MustNewFCMWithCred(projectID, firebaseCreds)
}

func VersionHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "OK",
		"message": "This is Aria packages service, v1",
		"version": version.Version,
	})
}
