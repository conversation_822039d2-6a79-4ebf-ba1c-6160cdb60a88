package album_photo

import (
	"github.com/gin-gonic/gin"

	handler "bitbucket.org/persistence17/aria/golang_services/handlers/album_photo/v1"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
)

// AlbumPhotoHandlers load all routes
func AlbumPhotoHandlers(r gin.IRouter) gin.IRouter {

	g := r.Group("/v1/album-photo")
	{
		g.GET("version", handler.VersionHandler)
		g.GET("status", handler.VersionHandler)

		g.POST("public/generate-url", handler.GeneratePublicURL)

		user := g.Group("/", middlewares.AuthenticateMW())
		{
			user.POST("generate-photo-url", handler.GeneratePhotoURL)
			user.POST("albums", handler.CreateAlbum)
			user.GET("albums", handler.GetAlbumList)
			user.GET("albums/:album_id", handler.GetAlbum)
			user.PUT("albums/:album_id", handler.UpdateAlbum)
			user.DELETE("albums/:album_id", handler.DeleteAlbum)

			user.POST("albums/:album_id/photos", handler.CreatePhoto)
			user.GET("albums/:album_id/photos", handler.GetPhotoList)
			user.PUT("albums/:album_id/photos/:photo_id", handler.UpdatePhoto)
			user.GET("albums/:album_id/photos/:photo_id", handler.GetPhoto)
			user.POST("albums/:album_id/batch-update-photos", handler.BatchUpdatePhoto)
			user.POST("albums/:album_id/batch-delete-photos", handler.BatchDeletePhoto)

		}
	}
	return r
}
