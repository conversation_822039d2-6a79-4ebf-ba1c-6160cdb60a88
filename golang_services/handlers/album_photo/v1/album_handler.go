package v1

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/Masterminds/squirrel"
	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
	"gorm.io/datatypes"
)

type Album struct {
	ID        int            `json:"id" db:"id"`
	Name      string         `json:"name" db:"name"`
	UserID    string         `json:"user_id" db:"user_id"`
	CropSize  datatypes.JSON `json:"crop_size" db:"crop_size"`
	CreatedAt *time.Time     `json:"created_at" db:"created_at"`
	UpdatedAt *time.Time     `json:"updated_at" db:"updated_at"`
	DeletedAt *time.Time     `json:"deleted_at" db:"deleted_at"`
}

func CreateAlbum(c *gin.Context) {
	var (
		req Album
		err error
	)

	if err = c.ShouldBindJ<PERSON>(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	authInfo := middlewares.GetAuthInfo(c)
	req.UserID = authInfo.UserID

	orm := middlewares.GetORMDao(c)

	if err := orm.Where("name = ? AND user_id = ? AND deleted_at IS NULL", req.Name, authInfo.UserID).First(&req).Error; err == nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Album with name %s already exists", req.Name))
		return
	}

	if err := orm.Create(&req).Error; err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    req,
	})
}

func UpdateAlbum(c *gin.Context) {
	var req *Album
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	orm := middlewares.GetORMDao(c)

	id, _ := utils.GetIntPathParam(c, "album_id")

	authInfo := middlewares.GetAuthInfo(c)

	query := squirrel.StatementBuilder.Update("albums").Where("id = ?", id)
	if req.Name != "" {
		if err := orm.Where("name = ? AND user_id = ? AND deleted_at IS NULL AND id != ?", req.Name, authInfo.UserID, id).First(&req).Error; err == nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Album with name %s already exists", req.Name))
			return
		}
		query = query.Set("name", req.Name)
	}

	if req.CropSize != nil {
		query = query.Set("crop_size", req.CropSize)
	}

	sql, args, _ := query.ToSql()
	if err := orm.Exec(sql, args...).Error; err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

type albumListRequest struct {
	Limit     uint64 `form:"limit"`
	Offset    uint64 `form:"offset"`
	SortField string `form:"sort_field"`
	SortOrder string `form:"sort_order"`
	Query     string `form:"query"`
}

type albumListResponse struct {
	Data    []*Album `json:"data"`
	Limit   uint64   `json:"limit"`
	Offset  uint64   `json:"offset"`
	Total   int64    `json:"total"`
	Success bool     `json:"success"`
}

func GetAlbumList(c *gin.Context) {
	var req albumListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	authInfo := middlewares.GetAuthInfo(c)

	orm := middlewares.GetORMDao(c)

	query := squirrel.StatementBuilder.Select("id, name, user_id, crop_size, created_at, updated_at, deleted_at").
		From("albums").Where("user_id = ?", authInfo.UserID).Where("deleted_at IS NULL")

	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 10
	}
	query = query.Limit(req.Limit).Offset(req.Offset)

	if req.Query != "" {
		query = query.Where("name ILIKE ?", "%"+req.Query+"%")
	}

	if funk.ContainsString([]string{"id", "name", "created_at"}, req.SortField) {
		if strings.ToUpper(req.SortOrder) == "DESC" {
			query = query.OrderBy(req.SortField + " DESC")
		} else {
			query = query.OrderBy(req.SortField + " ASC")
		}
	}

	sqlStr, args, _ := query.ToSql()

	rows, err := orm.Raw(sqlStr, args...).Rows()
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	defer rows.Close()
	result := albumListResponse{
		Success: true,
		Limit:   req.Limit,
		Offset:  req.Offset,
	}
	for rows.Next() {
		item := new(Album)
		if err := rows.Scan(&item.ID, &item.Name, &item.UserID, &item.CropSize, &item.CreatedAt, &item.UpdatedAt, &item.DeletedAt); err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}
		result.Data = append(result.Data, item)
	}

	sqlCount := utils.QueryCountItems(sqlStr)
	if err := orm.Raw(sqlCount, args...).Row().Scan(&result.Total); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	c.JSON(http.StatusOK, result)
}

func GetAlbum(c *gin.Context) {
	var req Album

	orm := middlewares.GetORMDao(c)

	id, _ := utils.GetIntPathParam(c, "album_id")
	if err := orm.Where("id = ?", id).Where("deleted_at IS NULL").First(&req).Error; err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    req,
	})
}

func DeleteAlbum(c *gin.Context) {
	id, _ := utils.GetIntPathParam(c, "album_id")

	orm := middlewares.GetORMDao(c)
	if err := orm.Exec("UPDATE albums SET deleted_at = NOW() WHERE id = ?", id).Error; err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
