package v1

import (
	"fmt"
	"mime"
	"net/http"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/Masterminds/squirrel"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"gorm.io/datatypes"
)

type Photo struct {
	ID             int            `json:"id" db:"id"`
	AlbumID        int            `json:"album_id" db:"album_id"`
	Name           string         `json:"name" db:"name"`
	Size           int            `json:"size" db:"size"`
	CropSize       datatypes.JSON `json:"crop_size" db:"crop_size"`
	LastModified   *time.Time     `json:"last_modified" db:"last_modified"`
	URL            string         `json:"url" db:"url"`
	ProcessURL     string         `json:"process_url" db:"process_url"`
	RotateURL      string         `json:"rotate_url" db:"rotate_url"`
	ValidateStatus string         `json:"validate_status" db:"validate_status"`
	CheckList      datatypes.JSON `json:"check_list" db:"check_list"`
	UserID         string         `json:"user_id" db:"user_id"`
	CreatedAt      *time.Time     `json:"created_at" db:"created_at"`
	UpdatedAt      *time.Time     `json:"updated_at" db:"updated_at"`
	DeletedAt      *time.Time     `json:"deleted_at" db:"deleted_at"`
}

type UploadRequest struct {
	Key         string `json:"key"`
	AlbumName   string `json:"album_name"`
	FileName    string `json:"file_name"`
	ContentType string `json:"content_type"`
}

func GeneratePublicURL(c *gin.Context) {
	var upload UploadRequest
	if err := c.ShouldBindJSON(&upload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	if upload.FileName == "" && upload.Key == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
		return
	}

	ps := middlewares.GetS3Presigner(c)
	bucket := utils.StructToJSON(middlewares.GetS3Buckets(c)).Get("ariadirect_prod_album").String()
	key := fmt.Sprintf("%s/%s/%s", "public", uuid.NewV4().String(), upload.FileName)
	if upload.Key != "" {
		key = upload.Key
	}

	var mimeType string
	if !strings.HasPrefix(upload.ContentType, ".") {
		mimeType = mime.TypeByExtension("." + upload.ContentType)
	} else {
		mimeType = mime.TypeByExtension(upload.ContentType)
	}

	uploadURLpresigned, err := ps.PresignUploadUrl(bucket, key, mimeType, 15*time.Minute)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	downURLpresigned, err := ps.PresignUrl(bucket, key, time.Hour*24)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"key":          key,
			"upload_url":   uploadURLpresigned,
			"download_url": downURLpresigned,
		},
	})
}

func GeneratePhotoURL(c *gin.Context) {
	var upload UploadRequest
	if err := c.ShouldBindJSON(&upload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	if upload.FileName == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
		return
	}
	authInfo := middlewares.GetAuthInfo(c)

	ps := middlewares.GetS3Presigner(c)
	bucket := utils.StructToJSON(middlewares.GetS3Buckets(c)).Get("ariadirect_prod_album").String()
	key := fmt.Sprintf("%s/%s/%s", authInfo.UserID, upload.AlbumName, upload.FileName)

	var mimeType string
	if !strings.HasPrefix(upload.ContentType, ".") {
		mimeType = mime.TypeByExtension("." + upload.ContentType)
	} else {
		mimeType = mime.TypeByExtension(upload.ContentType)
	}

	uploadURLpresigned, err := ps.PresignUploadUrl(bucket, key, mimeType, 15*time.Minute)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	downURLpresigned, err := ps.PresignUrl(bucket, key, time.Hour*24)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"upload_url":   uploadURLpresigned,
			"download_url": downURLpresigned,
		},
	})
}

func CreatePhoto(c *gin.Context) {
	var (
		req Photo
		err error
	)

	if err = c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	authInfo := middlewares.GetAuthInfo(c)
	req.UserID = authInfo.UserID

	orm := middlewares.GetORMDao(c)
	req.AlbumID, _ = utils.GetIntPathParam(c, "album_id")

	if req.AlbumID <= 0 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Missing album id"))
		return
	}

	if err := orm.Where("album_id = ? AND name = ? AND user_id = ? AND deleted_at IS NULL", req.AlbumID, req.Name, authInfo.UserID).First(&req).Error; err == nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Photo with name %s already exists", req.Name))
		return
	}

	if err := orm.Create(&req).Error; err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    req,
	})
}

func UpdatePhoto(c *gin.Context) {
	var req *Photo
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	orm := middlewares.GetORMDao(c)

	req.AlbumID, _ = utils.GetIntPathParam(c, "album_id")
	if req.AlbumID <= 0 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Missing album id"))
		return
	}

	id, _ := utils.GetIntPathParam(c, "photo_id")

	authInfo := middlewares.GetAuthInfo(c)

	query := squirrel.StatementBuilder.Update("photos").Where("id = ?", id)
	if req.Name != "" {
		if err := orm.Where("album_id = ? AND name = ? AND user_id = ? AND deleted_at IS NULL AND id != ?", req.AlbumID, req.Name, authInfo.UserID, id).First(&req).Error; err == nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Photo with name %s already exists", req.Name))
			return
		}
		query = query.Set("name", req.Name)
	}

	if req.Size > 0 {
		query = query.Set("size", req.Size)
	}

	if req.CropSize != nil {
		query = query.Set("crop_size", req.CropSize)
	}

	if req.URL != "" {
		query = query.Set("url", req.URL)
	}

	if req.ProcessURL != "" {
		query = query.Set("process_url", req.ProcessURL)
	}

	if req.RotateURL != "" {
		query = query.Set("rotate_url", req.ProcessURL)
	}

	if req.ValidateStatus != "" {
		query = query.Set("validate_status", req.ValidateStatus)
	}

	if req.CheckList != nil {
		query = query.Set("check_list", req.CheckList)
	}

	sql, args, _ := query.ToSql()
	if err := orm.Exec(sql, args...).Error; err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func BatchUpdatePhoto(c *gin.Context) {
	var req []Photo
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	orm := middlewares.GetORMDao(c)

	albumID, _ := utils.GetIntPathParam(c, "album_id")
	if albumID <= 0 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Missing album id"))
		return
	}

	authInfo := middlewares.GetAuthInfo(c)

	for _, photo := range req {
		query := squirrel.StatementBuilder.Update("photos").Where("id = ?", photo.ID)
		if photo.Name != "" {
			if err := orm.Where("album_id = ? AND name = ? AND user_id = ? AND deleted_at IS NULL", albumID, photo.Name, authInfo.UserID).First(&req).Error; err == nil {
				response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Photo with name %s already exists", photo.Name))
				return
			}
			query = query.Set("name", photo.Name)
		}

		if photo.Size > 0 {
			query = query.Set("size", photo.Size)
		}

		if photo.CropSize != nil {
			query = query.Set("crop_size", photo.CropSize)
		}

		if photo.URL != "" {
			query = query.Set("url", photo.URL)
		}

		if photo.ProcessURL != "" {
			query = query.Set("process_url", photo.ProcessURL)
		}

		if photo.RotateURL != "" {
			query = query.Set("rotate_url", photo.ProcessURL)
		}

		if photo.ValidateStatus != "" {
			query = query.Set("validate_status", photo.ValidateStatus)
		}

		if photo.CheckList != nil {
			query = query.Set("check_list", photo.CheckList)
		}

		sql, args, _ := query.ToSql()
		if err := orm.Exec(sql, args...).Error; err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}

	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func GetPhotoList(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	result := []*Photo{}
	orm := middlewares.GetORMDao(c)

	albumID, _ := utils.GetIntPathParam(c, "album_id")
	if albumID <= 0 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Missing album id"))
		return
	}

	orm.Where("user_id = ?", authInfo.UserID).Where("album_id = ?", albumID).Where("deleted_at IS NULL").Find(&result)

	ps := middlewares.GetS3Presigner(c)
	for i := range result {
		if result[i].URL != "" {
			bucket, key, _ := utils.UrlToS3BucketAndKey(result[i].URL)
			result[i].URL, _ = ps.PresignUrl(bucket, key, time.Hour*24)
		}
		if result[i].ProcessURL != "" {
			bucket, key, _ := utils.UrlToS3BucketAndKey(result[i].ProcessURL)
			result[i].ProcessURL, _ = ps.PresignUrl(bucket, key, time.Hour*24)
		}
		if result[i].RotateURL != "" {
			bucket, key, _ := utils.UrlToS3BucketAndKey(result[i].RotateURL)
			result[i].RotateURL, _ = ps.PresignUrl(bucket, key, time.Hour*24)
		}
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func GetPhoto(c *gin.Context) {
	var req *Photo
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	orm := middlewares.GetORMDao(c)

	albumID, _ := utils.GetIntPathParam(c, "album_id")
	if albumID <= 0 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Missing album id"))
		return
	}

	id, _ := utils.GetIntPathParam(c, "photo_id")
	if req.Name != "" {
		orm.Where("album_id = ? AND id = ? AND deleted_at IS NULL", albumID, id).First(&req)
	}

	ps := middlewares.GetS3Presigner(c)
	if req.URL != "" {
		bucket, key, _ := utils.UrlToS3BucketAndKey(req.URL)
		req.URL, _ = ps.PresignUrl(bucket, key, time.Hour*24)
	}
	if req.ProcessURL != "" {
		bucket, key, _ := utils.UrlToS3BucketAndKey(req.ProcessURL)
		req.ProcessURL, _ = ps.PresignUrl(bucket, key, time.Hour*24)
	}
	if req.RotateURL != "" {
		bucket, key, _ := utils.UrlToS3BucketAndKey(req.RotateURL)
		req.RotateURL, _ = ps.PresignUrl(bucket, key, time.Hour*24)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    req,
	})
}

func BatchDeletePhoto(c *gin.Context) {
	var req []int
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	albumID, _ := utils.GetIntPathParam(c, "album_id")
	if albumID <= 0 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Missing album id"))
		return
	}

	orm := middlewares.GetORMDao(c)
	for _, id := range req {
		if err := orm.Exec("UPDATE photos SET deleted_at = NOW() WHERE id = ?", id).Error; err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}

	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
