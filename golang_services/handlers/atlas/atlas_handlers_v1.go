package atlas

import (
	"github.com/gin-gonic/gin"

	atlas_v1 "bitbucket.org/persistence17/aria/golang_services/handlers/atlas/v1"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
)

func LoadV1AtlasHandlers(r gin.IRouter) gin.IRouter {

	g1 := r.Group("/v1/atlas")
	g1.GET("/version", atlas_v1.VersionHandler)
	g1.GET("status", atlas_v1.VersionHandler)
	external1 := g1.Group("/")
	{
		external1.Use(middlewares.AuthenticateMW())
		orgs := external1.Group("/orgs/:org-id")
		orgs.Use(middlewares.OrgCheckerMW)
		{
			vo := orgs.Group("validation-orders")
			{
				vo.POST("", atlas_v1.CreateAtlasValidationTask)
				vo.GET("", atlas_v1.QueryAtlasValidationResult)
			}

			ets := orgs.Group("/ets/orders")
			{
				ets.POST("/:order-id/tasks/:task-id", atlas_v1.CreateETSAtlasTask)
				ets.POST("/:order-id/tasks/:task-id/tasks", atlas_v1.CreateETSAtlasTasks)
				ets.POST("/:order-id/ai_results", atlas_v1.GetLatestETSTaskResults)
			}
		}

	}
	internal1 := g1.Group("/internal")
	{
		internal1.Use(middlewares.AuthenticateInternalMW())
		internal1.POST("/atlas-result", atlas_v1.UpdateAtlasTaskResult)
		internal1.POST("/ets-atlas-result", atlas_v1.UpdateETSAtlasTaskResult)
		internal1.POST("/atlas-validation-result", atlas_v1.UpdateAtlasValidation)
	}
	return r
}
