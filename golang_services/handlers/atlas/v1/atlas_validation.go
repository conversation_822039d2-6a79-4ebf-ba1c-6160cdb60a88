package v1

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	uuid "github.com/satori/go.uuid"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/queue"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

type CreateAtlasValidationRequest struct {
	ServiceType string `json:"service_type"`
	OrderIDs    []int  `json:"order_ids"`
}

type atlasValidationTaskMsg struct {
	RequestID        string              `json:"request_id"`
	Type             string              `json:"type"`
	ValiationOrderID int                 `json:"application_id"`
	Inputs           *models.PropertyMap `json:"inputs"`
	Callback         string              `json:"callback"`
	RequestTs        time.Time           `json:"request_ts"`
}

func CreateAtlasValidationTask(c *gin.Context) {
	orgID, err := utils.GetIntPathParam(c, "org-id")
	if err != nil || orgID <= 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
		})
		return
	}
	var req CreateAtlasValidationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil || authInfo.OrgID.Int64 != int64(orgID) {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("org not found"))
		return
	}

	visaDao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	orders, err := visaDao.QueryValidationOrders(map[string]any{
		"org_id": orgID,
		"id":     req.OrderIDs,
	}, 0, 0)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(orders) == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
		})
		return
	}
	task, err := createAtlasValidationTasks(middlewares.GetAtlasDao(c),
		middlewares.GetSimpleQueueSender(c, AtlasQueue),
		&req, orders, fmt.Sprintf("https://%s/v1/atlas/internal/atlas-validation-result", callbackHost))
	if err != nil {
		if strings.HasPrefix(err.Error(), "invalid input") || strings.HasPrefix(err.Error(), "unsupported atlas service type") {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    task,
	})
}

func createAtlasValidationTasks(dao db.IAtlasDao,
	producer queue.ISimpleQueueProducer,
	request *CreateAtlasValidationRequest,
	orders []*models.ValidationOrder,
	callback string) ([]*models.AtlasValidation, error) {
	tasks, err := buildAtlasValidationTask(request, orders)
	if err != nil {
		return nil, err
	}
	for _, task := range tasks {
		if err := producer.Send(&atlasValidationTaskMsg{
			RequestID:        task.RequestID,
			Type:             task.ServiceType,
			ValiationOrderID: task.ValidationOrderID,
			Inputs:           task.Inputs,
			Callback:         callback,
			RequestTs:        *task.RequestTs,
		}); err != nil {
			return nil, err
		}
	}
	if err := dao.CreateAtlasValidation(tasks); err != nil {
		return nil, err
	}
	return tasks, nil
}

func buildAtlasValidationTask(req *CreateAtlasValidationRequest, orders []*models.ValidationOrder) ([]*models.AtlasValidation, error) {
	if req == nil || len(orders) == 0 {
		return nil, fmt.Errorf("empty input")
	}
	var tasks []*models.AtlasValidation
	switch req.ServiceType {
	case "vld-test":
		for _, order := range orders {
			inputs := models.PropertyMap{}
			inputs["files"] = order.InputFiles

			now := time.Now().Round(time.Second)
			task := &models.AtlasValidation{
				RequestID:         uuid.NewV4().String(),
				ServiceType:       req.ServiceType,
				ValidationOrderID: orders[0].ID,
				RequestTs:         &now,
				ResultTs:          nil,
				Inputs:            &inputs,
				Outputs:           nil,
			}
			tasks = append(tasks, task)
		}
	}
	return tasks, nil
}

type queryAtlasValidationReq struct {
	Limit        uint     `form:"limit"`
	Offset       uint     `form:"offset"`
	OrderIDs     []int    `form:"order_ids"`
	ServiceTypes []string `form:"service_types"`
}

func QueryAtlasValidationResult(c *gin.Context) {
	orgID, err := utils.GetIntPathParam(c, "org-id")
	if err != nil || orgID <= 0 {
		c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
			"success": false,
		})
		return
	}
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil || authInfo.UserID == "" || authInfo.OrgID.Int64 != int64(orgID) {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("org not found"))
		return
	}
	var req queryAtlasValidationReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	if len(req.OrderIDs) == 0 {
		response.HandleResponse(c, nil, nil)
		return
	}
	visaDao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	orders, err := visaDao.QueryValidationOrders(map[string]any{
		"org_id": orgID,
		"id":     req.OrderIDs,
	}, req.Limit, req.Offset)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(orders) <= 0 {
		c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
			"success": false,
		})
		return
	}
	atlasDao := middlewares.GetAtlasDao(c)
	tasks, err := queryAtlasValidationTask(atlasDao, orgID, orders, req.ServiceTypes)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    tasks,
	})
}

func queryAtlasValidationTask(dao db.IAtlasDao, orgID int, orders []*models.ValidationOrder, st []string) ([]*models.AtlasValidation, error) {
	if len(orders) == 0 {
		return []*models.AtlasValidation{}, nil
	}
	var orderIDs []int
	for _, order := range orders {
		orderIDs = append(orderIDs, order.ID)
	}

	return dao.GetLatestAtlasValidations(orgID, orderIDs, st)
}

type validationCallbackRequest struct {
	RequestID         string         `json:"request_id"`
	ValidationOrderID int            `json:"application_id"`
	Confidence        float32        `json:"confidence"`
	Outputs           map[string]any `json:"outputs"`
	RequestTs         *time.Time     `json:"request_ts"`
	ResultTs          *time.Time     `json:"result_ts"`
}

func UpdateAtlasValidation(c *gin.Context) {
	var req validationCallbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	atlasdao := middlewares.GetAtlasDao(c)
	task, err := atlasdao.QueryAtlasValidations(map[string]any{"request_id": req.RequestID})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(task) == 0 || task[0] == nil || task[0].ValidationOrderID != req.ValidationOrderID {
		log.Warn().Str("request_id", req.RequestID).
			Int("validation_order_id", req.ValidationOrderID).
			Msg("task in update request does not exist in db")
		c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
			"success": false,
		})
		return
	}
	if err := updateAtlasValidation(atlasdao, &req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.Status(http.StatusOK)
}

func updateAtlasValidation(dao db.IAtlasDao, req *validationCallbackRequest) error {
	o := models.PropertyMap(req.Outputs)
	o["confidence"] = req.Confidence
	updated := &models.AtlasValidation{
		ResultTs: req.ResultTs,
		Outputs:  &o,
	}
	return dao.UpdateAtlasValidation(req.RequestID, updated)
}
