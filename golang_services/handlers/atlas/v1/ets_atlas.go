package v1

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	uuid "github.com/satori/go.uuid"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/queue"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func CreateETSAtlasTask(c *gin.Context) {
	var req CreateAtlasRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	serviceTaskID := c.Param("task-id")
	task, err := createETSAtlasTask(
		middlewares.GetAtlasDao(c),
		middlewares.GetSimpleQueueSender(c, AtlasQueue),
		&req, serviceTaskID,
		middlewares.GetAWSRegion(c),
		fmt.Sprintf("https://%s/v1/atlas/internal/ets-atlas-result", callbackHost),
	)
	if err != nil {
		if strings.HasPrefix(err.Error(), "invalid input") || strings.HasPrefix(err.Error(), "unsupported atlas service type") {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    task,
	})
}

func CreateETSAtlasTasks(c *gin.Context) {
	var reqs []CreateAtlasRequest
	if err := c.ShouldBindJSON(&reqs); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	serviceTaskID := c.Param("task-id")

	result := []*models.ETSAtlasTask{}
	for _, req := range reqs {
		task, err := createETSAtlasTask(
			middlewares.GetAtlasDao(c),
			middlewares.GetSimpleQueueSender(c, AtlasQueue),
			&req,
			serviceTaskID,
			middlewares.GetAWSRegion(c),
			fmt.Sprintf("https://%s/v1/atlas/internal/ets-atlas-result", callbackHost),
		)
		if err != nil {
			if strings.HasPrefix(err.Error(), "invalid input") || strings.HasPrefix(err.Error(), "unsupported atlas service type") {
				response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
				return
			}
			response.HandleResponse(c, nil, err)
			return
		}
		result = append(result, task)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func createETSAtlasTask(dao db.IAtlasDao,
	producer queue.ISimpleQueueProducer,
	request *CreateAtlasRequest,
	serviceTaskID, region, callback string) (*models.ETSAtlasTask, error) {
	task, err := buildETSAtlasTask(request, serviceTaskID, region)
	if err != nil {
		return nil, err
	}
	if err := producer.Send(&atlasTaskMsg{
		RequestID:     task.RequestID,
		Type:          task.ServiceType,
		ApplicationID: task.ServiceTaskID,
		Inputs:        task.Inputs,
		Callback:      callback,
		RequestTs:     *task.RequestTs,
	}); err != nil {
		return nil, err
	}
	if err := dao.CreateETSAtlasTask([]*models.ETSAtlasTask{task}); err != nil {
		return nil, err
	}
	return task, nil
}

func buildETSAtlasTask(req *CreateAtlasRequest, serviceTaskID, region string) (*models.ETSAtlasTask, error) {
	if req == nil {
		return nil, fmt.Errorf("empty input")
	}
	inputs := models.PropertyMap{}
	inputs["level"] = req.Level
	if req.Level == "" {
		inputs["level"] = "EASY"
	}

	switch req.ServiceType {
	case "901029", "908005", "909001":
		if len(req.Images) > 0 {
			bucket, key, err := utils.UrlToS3BucketAndKey(req.Images[0])
			if err != nil {
				log.Warn().Str("error", err.Error()).Msg("bad input image link")
				return nil, fmt.Errorf("invalid input: bad image link")
			}
			inputs["images"] = []map[string]any{
				{"bucket": bucket, "key": key},
			}
			break
		}

		return nil, fmt.Errorf("invalid input")

	case "d909001":
		if len(req.Images) > 0 {
			bucket, key, err := utils.UrlToS3BucketAndKey(req.Images[0])
			if err != nil {
				log.Warn().Str("error", err.Error()).Msg("bad input image link")
				return nil, fmt.Errorf("invalid input: bad image link")
			}
			inputs["images"] = []map[string]any{
				{"bucket": bucket, "key": key},
			}
			break
		}

		return nil, fmt.Errorf("invalid input")
	case "c10001":
		if len(req.Images) < 2 {
			s := req.Images[0]
			bucket, key, err := utils.UrlToS3BucketAndKey(s)
			if err != nil {
				log.Warn().Str("error", err.Error()).Msgf("bad input value for %s", req.ServiceType)
				return nil, fmt.Errorf("invalid input: bad file link")
			}

			inputs["images"] = []map[string]any{
				{"bucket": bucket, "key": key},
			}
		} else {
			s := req.Images[0]
			if s == "" {
				return nil, fmt.Errorf("invalid input: no passport scan uploaded")
			}
			bucket, key, err := utils.UrlToS3BucketAndKey(s)
			if err != nil {
				log.Warn().Str("error", err.Error()).Msgf("bad input value for %s", req.ServiceType)
				return nil, fmt.Errorf("invalid input: bad image link")
			}
			s = req.Images[1]
			if s == "" {
				return nil, fmt.Errorf("invalid input: bad input value for passport scan")
			}
			bucket1, key1, err := utils.UrlToS3BucketAndKey(s)
			if err != nil {
				log.Warn().Str("error", err.Error()).Msgf("bad input value for %s", req.ServiceType)
				return nil, fmt.Errorf("invalid input: bad file link")
			}

			inputs["images"] = []map[string]any{
				{"bucket": bucket, "key": key}, {"bucket": bucket1, "key": key1},
			}
		}
	default:
		return nil, fmt.Errorf("unsupported atlas service type %s", req.ServiceType)
	}

	now := time.Now().Round(time.Second)
	task := &models.ETSAtlasTask{
		RequestID:     uuid.NewV4().String(),
		ServiceType:   req.ServiceType,
		ServiceTaskID: serviceTaskID,
		RequestTs:     &now,
		ResultTs:      nil,
		Inputs:        &inputs,
		Outputs:       nil,
	}
	return task, nil
}

func UpdateETSAtlasTaskResult(c *gin.Context) {
	var req callbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	atlasdao := middlewares.GetAtlasDao(c)
	task, err := atlasdao.QueryETSAtlasTask(map[string]any{"request_id": req.RequestID})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(task) == 0 || task[0] == nil {
		c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
			"success": false,
		})
		return
	}
	if err := updateETSAtlasTask(atlasdao, &req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.Status(http.StatusOK)
}

func updateETSAtlasTask(dao db.IAtlasDao, req *callbackRequest) error {
	o := models.PropertyMap(req.Outputs)
	o["confidence"] = req.Confidence
	update := &models.ETSAtlasTask{
		ServiceTaskID: req.ApplicationID,
		ResultTs:      req.ResultTs,
		Outputs:       &o,
		RequestID:     req.RequestID,
	}

	return dao.UpdateETSAtlasTask(req.RequestID, update)
}

type batchGetETSResultRequest struct {
	Tasks        []string `json:"tasks"`
	ServiceTypes []string `json:"service_types"`
	Requests     []string `json:"requests"`
}

func GetLatestETSTaskResults(c *gin.Context) {
	orderID, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
		})
		return
	}

	var req batchGetETSResultRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	dao := middlewares.GetAtlasDao(c)
	res, err := dao.GetLatestETSAtlasResult(orderID, req.Tasks, req.Requests, req.ServiceTypes)
	if err != nil {
		log.Error().Err(err).Msg("GetLatestETSTaskResults failed")
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    res,
	})
}
