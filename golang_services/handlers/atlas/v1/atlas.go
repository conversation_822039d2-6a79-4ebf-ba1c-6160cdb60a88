package v1

import (
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
)

const (
	AtlasQueue = "atlas"
)

var (
	once         sync.Once
	callbackHost string
)

func InitAtlasHandlers(callbackhost string) {
	once.Do(func() {
		callbackHost = callbackhost
	})
}

type CreateAtlasRequest struct {
	ServiceType string   `json:"service_type"`
	Level       string   `json:"level"`
	Images      []string `json:"images,omitempty"`
}

type atlasTaskMsg struct {
	RequestID     string              `json:"request_id"`
	Type          string              `json:"type"`
	ApplicationID any                 `json:"application_id"`
	Inputs        *models.PropertyMap `json:"inputs"`
	Callback      string              `json:"callback"`
	RequestTs     time.Time           `json:"request_ts"`
}

type callbackRequest struct {
	RequestID     string         `json:"request_id"`
	ApplicationID any            `json:"application_id"`
	Confidence    float32        `json:"confidence"`
	Outputs       map[string]any `json:"outputs"`
	RequestTs     *time.Time     `json:"request_ts"`
	ResultTs      *time.Time     `json:"result_ts"`
}

type batchGetResultRequest struct {
	Applications []int    `json:"applications"`
	ServiceTypes []string `json:"service_types"`
}

func UpdateAtlasTaskResult(c *gin.Context) {
	var req callbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	atlasdao := middlewares.GetAtlasDao(c)
	task, err := atlasdao.QueryAtlasTask(map[string]any{"request_id": req.RequestID})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(task) == 0 || task[0] == nil {
		log.Warn().Str("request_id", req.RequestID).
			Interface("application_id", req.ApplicationID).
			Msg("task in update request does not exist in db")
		c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
			"success": false,
		})
		return
	}
	if err := updateAtlasTask(atlasdao, &req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.Status(http.StatusOK)
}

func updateAtlasTask(dao db.IAtlasDao, req *callbackRequest) error {
	o := models.PropertyMap(req.Outputs)
	o["confidence"] = req.Confidence
	update := &models.AtlasTask{
		ApplicationID: req.ApplicationID,
		ResultTs:      req.ResultTs,
		Outputs:       &o,
		RequestID:     req.RequestID,
	}

	return dao.UpdateAtlasTask(req.RequestID, update)
}
