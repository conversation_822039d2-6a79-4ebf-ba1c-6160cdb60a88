package internal_v1

import (
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/version"
	"github.com/gin-gonic/gin"
)

const (
	ContactUsEmailTemplateName       = "contact_us"
	RequestCodeUserEmailTemplateName = "request_code_to_user"
	RequestCodeADEmailTemplateName   = "request_code_to_ad"
	NotificationQueue                = "notification-queue"
)

func VersionHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "OK",
		"message": "This is Aria internals service",
		"version": version.Version,
	})
}

type RequestCodeRequest struct {
	ContactName    string `json:"contact_name"`
	Title          string `json:"title"`
	Email          string `json:"email"`
	Phone          string `json:"phone"`
	Region         string `json:"region"`
	CompanyName    string `json:"company_name"`
	CompanyAddress string `json:"company_address"`
	CompanyWebsite string `json:"company_website"`
}

func RequestCodeHandler(c *gin.Context) {
	var req RequestCodeRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	// send queue
	// send to user
	q := middlewares.GetSimpleQueueSender(c, NotificationQueue)

	message := map[string]any{
		"template_name": RequestCodeUserEmailTemplateName,
		"to":            req.Email,
		"bcc":           []string{"<EMAIL>"},
		"parameters": map[string]any{
			"ContactName":    req.ContactName,
			"Title":          req.Title,
			"Email":          req.Email,
			"Phone":          req.Phone,
			"Region":         req.Region,
			"CompanyName":    req.CompanyName,
			"CompanyAddress": req.CompanyAddress,
			"CompanyWebsite": req.CompanyWebsite,
		},
	}
	err := q.Send(message)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	// send to AD
	message = map[string]any{
		"template_name": RequestCodeADEmailTemplateName,
		"to":            "<EMAIL>",
		"bcc":           []string{"<EMAIL>"},
		"parameters": map[string]any{
			"ContactName":    req.ContactName,
			"Title":          req.Title,
			"Email":          req.Email,
			"Phone":          req.Phone,
			"Region":         req.Region,
			"CompanyName":    req.CompanyName,
			"CompanyAddress": req.CompanyAddress,
			"CompanyWebsite": req.CompanyWebsite,
		},
	}
	err = q.Send(message)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

type ContactUsRequest struct {
	FullName string `json:"fullname"`
	Email    string `json:"email"`
	Subject  string `json:"subject"`
	Message  string `json:"message"`
}

func ContactUsHandler(c *gin.Context) {
	var req ContactUsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	// send queue
	q := middlewares.GetSimpleQueueSender(c, NotificationQueue)

	message := map[string]any{
		"template_name": ContactUsEmailTemplateName,
		"to":            req.Email,
		"bcc":           []string{"<EMAIL>"},
		"parameters": map[string]any{
			"FullName":    req.FullName,
			"Subject":     req.Subject,
			"Description": req.Message,
			"Email":       req.Email,
		},
	}
	err := q.Send(message)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
