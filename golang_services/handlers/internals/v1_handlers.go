package internals

import (
	internal_v1 "bitbucket.org/persistence17/aria/golang_services/handlers/internals/v1"
	"github.com/gin-gonic/gin"
)

func LoadV1InternalsHandlers(r gin.IRouter) gin.IRouter {
	g := r.Group("/v1")
	{
		g.GET("version", internal_v1.VersionHandler)
		internals := g.Group("/internals")
		{
			internals.POST("/request-code", internal_v1.RequestCodeHandler)
			internals.POST("/contact-us", internal_v1.ContactUsHandler)
		}
	}

	g1 := r.Group("/v1/int")
	{
		g1.GET("version", internal_v1.VersionHandler)
		g1.GET("status", internal_v1.VersionHandler)
		internals := g1.Group("/internals")
		{
			internals.POST("/request-code", internal_v1.RequestCodeHandler)
			internals.POST("/contact-us", internal_v1.ContactUsHandler)
		}
	}
	return r
}
