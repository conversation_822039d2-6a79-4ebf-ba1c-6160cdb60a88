package packages

import (
	"github.com/gin-gonic/gin"

	packages_v1 "bitbucket.org/persistence17/aria/golang_services/handlers/packages/v1"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/middlewares/ets_mw"
)

func LoadV1PackagesHandlers(r gin.IRouter) gin.IRouter {

	r.GET("test", func(c *gin.Context) {
		// notification.NotifyFeedbackToUser(5414)
	})
	g1 := r.Group("v1/pkg")
	{
		g1.GET("version", packages_v1.VersionHandler)
		g1.GET("status", packages_v1.VersionHandler)
		g1.GET("visa-query", packages_v1.GetVisaQuery)
		g1.GET("screening-questions", packages_v1.GetVisaScreeningQuestions)
		g1.GET("available-countries", packages_v1.GetAvailableProductCountry)
		g1.GET("available-trip-dates", packages_v1.GetAvailableDateByProcessingTime)
		g1.GET("available-from-dates", packages_v1.GetAvailableDateByProcessingTimeWithInput)
		g1.GET("membership-provider", packages_v1.GetMembershipProvider)
		g1.GET("ric", packages_v1.ResidenceImmigrationCheck)
		//g1.GET("matching-test", packages_v1.BatchMatchPassport)
		g1.GET("promotions", packages_v1.GetPromotions)
		g1.POST("shorten-link", packages_v1.CreateShortenLink)
		g1.GET("shorten-link", packages_v1.GetUnShortenLink)
		g1.GET("email/validate", packages_v1.EmailValidationV2)
		g1.GET("service_types", packages_v1.GetServiceType)

		// g1.GET("public/packages/:package-id/feedbacks", packages_v1.GetPackageFeedback)
		// g1.POST("public/packages/:package-id/feedbacks", packages_v1.CreatePackageFeedback)
		g1.GET("public/orders/:order-id/get-evisa-files", packages_v1.GetEVisaPdfForms)

		// Generate batch id and compare passport
		g1.GET("public/batch/keys", packages_v1.GetBatchUploadID)
		g1.POST("public/batches/:batch-id/files", packages_v1.GeneratePresignUrlForBatchUpload)
		g1.GET("public/batches/:batch-id/files", packages_v1.GetPresignUrlForBatchUpload)
		g1.DELETE("public/batches/:batch-id/files", packages_v1.DeleteFileFromBatchUpload)
		g1.POST("public/batches/:batch-id/matches", packages_v1.MatchPassportURLsWithBatchID)
		g1.POST("public/batches/:batch-id/evisa_files", packages_v1.UpdateVisaFileToOrderWithBatchID)
		g1.POST("public/batch/files", packages_v1.GeneratePresignUrlForBatchUploadV2) // Main function, another is deprecated
		g1.POST("public/crash-logs", packages_v1.SendCrashLog)
		g1.GET("public/ets", packages_v1.GetAvailableEts)

		external := g1.Group("")
		{
			external.POST("visa-check", packages_v1.VisaCheck)
			external.Use(middlewares.AuthenticateMW())
			orgs := external.Group("orgs/:org-id")
			orgs.Use(middlewares.OrgCheckerMW)
			{
				orgs.POST("generate-upload-url", packages_v1.GeneratePresignUrlForUpload)
				orgs.POST("generate-batch-upload-urls", packages_v1.GenerateBatchMatchingUploadUrl)
				// orgs.POST("batch-passport-match", packages_v1.BatchMatchPassportWithBatchID)

				vo := orgs.Group("validation-orders")
				{
					vo.POST("", packages_v1.CreateValidationOrder)
					vo.GET("", packages_v1.QueryValidationOrders)
					vo.POST(":order-id/generate-upload-url", packages_v1.GenerateUploadUrlForValidationOrder)
					vo.PUT(":order-id", packages_v1.UpdateValidationOrder)
				}

				cart := orgs.Group("")
				{
					// Current cart
					cart.POST("cart/items", packages_v1.AddItemToCart)
					cart.DELETE("cart/items/:item-id", packages_v1.RemoveItemFromCart)
					cart.DELETE("cart/items", packages_v1.RemoveCartItems)
					cart.GET("cart/current", packages_v1.GetCurrentCart)
					cart.GET("cart/items", packages_v1.GetCartItems)
					cart.GET("cart/group-items", packages_v1.GetGroupItems)
					cart.GET("cart/group-item/headers", packages_v1.GetGroupItemsHeader)
					cart.POST("cart/group-item/bulk-deletes", packages_v1.BulkDeleteGroupItems)
					cart.GET("cart/payment-options", packages_v1.GetCartPaymentOptions)
					cart.GET("cart/exchange-rates", packages_v1.GetExchangeRate)

					// Special cart
					cart.GET("carts", packages_v1.GetCartList)
					cart.GET("carts/:cart-id/items", packages_v1.GetCartItems)
					cart.POST("carts/:cart-id/summary", packages_v1.GetCartSummary)
					cart.GET("carts/:cart-id/coupons", packages_v1.GetAvailableCartCoupon)
					cart.POST("carts/:cart-id/coupons", packages_v1.CartCouponValidate)
					cart.POST("carts/:cart-id/checkout", packages_v1.CheckoutCart)
					cart.POST("carts/:cart-id/lock", packages_v1.LockCart)
					cart.POST("carts/:cart-id/cancel", packages_v1.CancelCart)
					cart.POST("carts/:cart-id/re-payment", packages_v1.RePayment)
					cart.POST("carts/:cart-id/shipment", packages_v1.UpdateCartShipments)
					cart.POST("cart/shipment", packages_v1.UpdateCartShipments)
					cart.GET("carts/:cart-id/shipments", packages_v1.GetCartShipments) // Get available shipment for ets and visa
					cart.GET("cart/shipments", packages_v1.GetCartShipments)
					cart.POST("carts/:cart-id/transactions", packages_v1.CreateManualTransaction)
					cart.GET("carts/:cart-id/transactions", packages_v1.GetManualTransactions)
					cart.POST("carts/:cart-id/validations", packages_v1.CartValidation)
					cart.POST("carts/:cart-id/items/:item-id/lock", packages_v1.LockCardForModify)
					cart.POST("carts/:cart-id/items/:item-id/unlock", packages_v1.UnLockCardFromModify)
					cart.POST("carts/:cart-id/send-indirect-payment", packages_v1.SendInDirectPayment)

					cart.GET("cart/provider/group-items/filter", packages_v1.GetGroupFilterItem)
					cart.GET("cart/provider/invoices", packages_v1.GetCorporationSelfInvoiceList)
					cart.GET("cart/provider/invoices/:invoice-id", packages_v1.GetCorporationSelfInvoice)
					cart.POST("cart/provider/invoices", packages_v1.CreateCorporationSelfInvoice)
					cart.DELETE("cart/provider/invoices/:invoice-id", packages_v1.DeleteCorporationSelfInvoice)

				}

				group := orgs.Group("")
				{
					group.GET("group/items", packages_v1.GetGroupItems)

				}

				// Admin can view and edit traveler in a corporation
				traveler := orgs.Group("traveler")
				{
					traveler.GET("", packages_v1.GetTravelerByUser)
					traveler.POST("", packages_v1.CreateTravelerByUser)

					singleTraveler := traveler.Group(":traveler-id", middlewares.TravelerCheckerMW)
					{
						singleTraveler.GET("", packages_v1.GetTraveler)
						singleTraveler.PUT("", packages_v1.UpdateTraveler)
						singleTraveler.DELETE("", packages_v1.DeleteTraveler)

						passportBook := singleTraveler.Group("passport-book")
						{
							passportBook.GET("", packages_v1.ListPassportBooks)
							passportBook.POST("", packages_v1.AddPassportBook)
							passportBook.PUT(":passport-book-id", packages_v1.UpdatePassportBook)
							passportBook.DELETE(":passport-book-id", packages_v1.DeletePassportBook)
						}
						visaBook := singleTraveler.Group("visa-book")
						{
							visaBook.GET("", packages_v1.ListVisaBooks)
							visaBook.POST("", packages_v1.AddVisaBook)
							visaBook.PUT(":visa-book-id", packages_v1.UpdateVisaBook)
							visaBook.DELETE(":visa-book-id", packages_v1.DeleteVisaBook)
						}
						addressBook := singleTraveler.Group("address-book")
						{
							addressBook.GET("", packages_v1.ListAddresses)
							addressBook.POST("", packages_v1.AddAddress)
							addressBook.PUT(":address-id", packages_v1.UpdateAddress)
							addressBook.DELETE(":address-id", packages_v1.DeleteAddress)
						}
						contactBook := singleTraveler.Group("contact-book")
						{
							contactBook.GET("", packages_v1.ListContacts)
							contactBook.POST("", packages_v1.AddContact)
							contactBook.PUT(":contact-id", packages_v1.UpdateContact)
							contactBook.DELETE(":contact-id", packages_v1.DeleteContact)
						}
						membership := singleTraveler.Group("membership")
						{
							membership.GET("", packages_v1.ListMemberships)
							membership.POST("", packages_v1.AddMembership)
							membership.PUT(":membership-id", packages_v1.UpdateMembership)
							membership.DELETE(":membership-id", packages_v1.DeleteMembership)
						}
					}
				}

				shipment := orgs.Group("")
				{
					shipment.POST("shipments/:shipment_id/labels", packages_v1.UpdateVisaShipmentLabel)
				}
			}

			traveler := external.Group("traveler")
			{
				traveler.GET("", packages_v1.ListTravelers)
				traveler.POST("", packages_v1.CreateTraveler)
				singleTraveler := traveler.Group(":traveler-id", middlewares.TravelerCheckerMW)
				{
					singleTraveler.GET("", packages_v1.GetTraveler)
					singleTraveler.PUT("", packages_v1.UpdateTraveler)
					singleTraveler.DELETE("", packages_v1.DeleteTraveler)
					singleTraveler.POST("my-profile", packages_v1.SetMyProfileForTraveler)
					singleTraveler.PUT("import-passport", packages_v1.ImportPassportToTraveler)

					passportBook := singleTraveler.Group("passport-book")
					{
						passportBook.GET("", packages_v1.ListPassportBooks)
						passportBook.POST("", packages_v1.AddPassportBook)
						passportBook.PUT(":passport-book-id", packages_v1.UpdatePassportBook)
						passportBook.DELETE(":passport-book-id", packages_v1.DeletePassportBook)
					}
					visaBook := singleTraveler.Group("visa-book")
					{
						visaBook.GET("", packages_v1.ListVisaBooks)
						visaBook.POST("", packages_v1.AddVisaBook)
						visaBook.PUT(":visa-book-id", packages_v1.UpdateVisaBook)
						visaBook.DELETE(":visa-book-id", packages_v1.DeleteVisaBook)
					}
					addressBook := singleTraveler.Group("address-book")
					{
						addressBook.GET("", packages_v1.ListAddresses)
						addressBook.POST("", packages_v1.AddAddress)
						addressBook.PUT(":address-id", packages_v1.UpdateAddress)
						addressBook.DELETE(":address-id", packages_v1.DeleteAddress)
					}
					contactBook := singleTraveler.Group("contact-book")
					{
						contactBook.GET("", packages_v1.ListContacts)
						contactBook.POST("", packages_v1.AddContact)
						contactBook.PUT(":contact-id", packages_v1.UpdateContact)
						contactBook.DELETE(":contact-id", packages_v1.DeleteContact)
					}
					membership := singleTraveler.Group("membership")
					{
						membership.GET("", packages_v1.ListMemberships)
						membership.POST("", packages_v1.AddMembership)
						membership.PUT(":membership-id", packages_v1.UpdateMembership)
						membership.DELETE(":membership-id", packages_v1.DeleteMembership)
					}
				}
			}

			// Only admin can access invoice
			invoice := orgs.Group("invoices")
			{
				invoice.GET("", packages_v1.GetInvoiceList)
				invoice.POST("", packages_v1.MakeInvoiceByAD)
				invoice.PUT(":id", packages_v1.UpdateInvoice)
				invoice.POST(":id/generate-new-invoices", packages_v1.GenerateNewInvoice)
				invoice.POST(":id/send-emails", packages_v1.SendInvoiceEmail)
				invoice.DELETE(":id", packages_v1.DeleteInvoice)
			}

			report := orgs.Group("report")
			{
				report.GET("", packages_v1.GetReportList)
				report.POST("", packages_v1.MakeReportByAD)
				report.DELETE(":id", packages_v1.DeleteReport)
			}

			fedexReport := orgs.Group("fedex-reports")
			{
				fedexReport.GET("", packages_v1.GetFedExReportList)
				fedexReport.POST("", packages_v1.MakeFedExReport)
				fedexReport.DELETE(":id", packages_v1.DeleteFedExReport)
			}
		}

		internal := g1.Group("", middlewares.AuthenticateInternalMW())
		{
			ets := internal.Group("ets-internal/:order-id")
			{
				ets.POST("update-shipment-status", packages_v1.ETSShipmentUpdateHandler)
				ets.POST("update-status", packages_v1.ETSUpdateStatusHandler)
				ets.POST("add-output-file", packages_v1.AddOutputFileToEtsOrder)
			}

			order := internal.Group("internal")
			{
				order.GET("orders/:order-id", packages_v1.GetInternalEtsOrder)
				order.GET("orders/:order-id/tasks/:task-id", packages_v1.GetTasksByID)
				order.POST("update-id-photos", packages_v1.UpdatedOrderIDPhoto)
				order.POST("id-photo-walgreen-callback", packages_v1.IDPhotoWalgreenSubmitCallback)
				order.POST("orders/:order-id/create_fastlane_orders", packages_v1.CreateFastlaneOrderFromVisaOrder)
				order.POST("zelle-webhook", packages_v1.ZelleWebhook)
			}

			cart := internal.Group("cart-internal/:cart-id")
			{
				cart.POST("cancel", packages_v1.CancelCart)
			}
		}

		ets := g1.Group("ets")
		{
			ets.GET("", middlewares.AuthenticateMW(), packages_v1.GetAvailableEts)
			ets.GET("common-products", middlewares.AuthenticateMW(), packages_v1.GetCommonETSProduct)
			ets.GET("tasks", packages_v1.GetAvailableETSTasks)
			ets.GET("ets-query/filters", packages_v1.GetAvailableETSQuery)

			ets.GET("pods", packages_v1.GetEtsPodsHandler)
			ets.GET("services/:service-id", packages_v1.GetETSByID)
			ets.POST("services/:service-id/contacts", middlewares.AuthenticateMW(), packages_v1.ETSendEmailToAdmin)
			ets.GET("screening-questions", packages_v1.GetEtsScreeningQuestions)
			ets.GET("service/:service_name/ets_query", packages_v1.GetEtsQuery)
			ets.GET("orgs/:org-id/order/tasks", middlewares.AuthenticateMW(), packages_v1.GetTasksByUser)
			ets.GET("orgs/:org-id/order/headers", middlewares.AuthenticateMW(), packages_v1.GetETSHeader)
			ets.POST("orgs/:org-id/order/clones", middlewares.AuthenticateMW(), packages_v1.CloneEtsOrder)
			ets.GET("orgs/:org-id/order/query-customer-passport-numbers", middlewares.AuthenticateMW(), packages_v1.QueryCustomerPassportNumber)
			// ets.PUT("public/orders/:order-id/tasks/:task-id", packages_v1.PublicUpdateEtsTask) // Use api staff/confirm-handle-orders instead
			ets.GET("public/orders/:order-id/feedbacks", packages_v1.GetETSFeedback)
			ets.POST("public/orders/:order-id/feedbacks", packages_v1.CreateETSFeedback)
			ets.POST("public/orders/:order-id/staff/confirm-handle-orders", packages_v1.StaffConfirmHandleEtsOrder)

			ets.POST("public/orders/:order-id/tasks", middlewares.AuthenticateMW(), packages_v1.CreatePublicTasksByOrder)
			{
				ets.POST("public/external/orders", packages_v1.ExternalCreateEtsOrder)
			}
			ets.GET("public/orders/:order-id/tasks", packages_v1.GetPublicTasksByOrder)
			ets.POST("orgs/:org-id/order/delete", middlewares.AuthenticateMW(), middlewares.OrgCheckerMW, packages_v1.DeleteMultiEtsOrderV2)
			orders := ets.Group("orgs/:org-id/orders")
			orders.Use(middlewares.AuthenticateMW(), middlewares.OrgCheckerMW)
			{
				orders.POST("", packages_v1.CreateEtsOrder)
				orders.GET("", packages_v1.ListEtsOrders)
				orders.DELETE("", packages_v1.DeleteMultiEtsOrder)
				o := orders.Group(":order-id")
				{
					o.GET("", packages_v1.GetEtsOrder)
					o.DELETE("", packages_v1.DeleteEtsOrder)
					o.PUT("", packages_v1.UpdateEtsOrder)
					o.POST("update-trip-dates", packages_v1.UpdateETSTripDate)
					o.POST("tasks-by-passport-photos", packages_v1.CreateTaskByPassportPhoto)
					o.POST("validations", packages_v1.ValidateEtsOrder)
					o.POST("clear-validations", packages_v1.ClearValidateEtsOrderResult)
					o.POST("generate-forms", packages_v1.GenerateETSForm)
					o.PUT("update-output-files", packages_v1.UpdateOutputFilesEtsOrder)
					o.GET("download-all-files", packages_v1.DownloadETSDocumentFiles)
					o.GET("download-all-visa-files", packages_v1.DownloadETSVisaFiles)
					o.GET("download-pdf-files", packages_v1.DownloadETSPdfFile)
					o.GET("download-evisa-payment-codes", packages_v1.GetEVisaPaymentCodes)
					o.GET("tasks/:task-id/download-all-form", packages_v1.DownloadETSDocumentForm)
					o.POST("traveler/imports", packages_v1.ImportTaskToTraveler)
					o.POST("change-shipments", packages_v1.ChangeETSShipment)
					o.POST("email-remind-to-providers", packages_v1.ETSEmailReminderToProvider)
					o.POST("regenerate-labels", packages_v1.ReGenerateLabelForETS)
					o.POST("staffs", packages_v1.AddStaffToEtsOrder)
					o.DELETE("staffs", packages_v1.DeleteStaffFromOrder)
					// o.POST("re-summary", packages_v1.GetOrderSummary)
					o.Use(ets_mw.EtsOrderAccessControlMW("admin", "ad_admin"))
					{
						o.POST("total-actual", packages_v1.UpdatePaymentTotalActual)

						o.GET("feedbacks", packages_v1.GetETSFeedback)
						o.POST("feedbacks", packages_v1.CreateETSFeedback)

						o.PUT("providers/:provider-id", packages_v1.ChangeOrderProvider)

						o.POST("tasks", packages_v1.CreateTaskByOrder)
						o.GET("tasks", packages_v1.GetTasksByOrder)
						o.GET("tasks/:task-id", packages_v1.GetTasksByID)
						o.PUT("tasks/:task-id", packages_v1.UpdateEtsTask)
						o.DELETE("tasks/:task-id", packages_v1.DeleteETSTask)
						o.PUT("tasks/:task-id/status", packages_v1.UpdateEtsTaskStatus)
						o.POST("task/batch_update_status", packages_v1.UpdateBatchEtsTaskStatus)
						// o.POST("orgs/:org-id/tasks/update-status", packages_v1.UpdateTaskStatus) // MOVE TO  tasks/:task-id/status

						// o.POST("tasks/:task-id/clone", packages_v1.CloneETSTask)
						o.POST("tasks/:task-id/generate-upload-url", packages_v1.GeneratePresignUrlForTaskUpload)
						o.PUT("tasks/:task-id/update-output-files", packages_v1.UpdateOutputFilesEtsTask)
						o.POST("tasks/:task-id/download-files", packages_v1.DownloadSingleFileInETSTask)
					}
					booking := o.Group("booking-appointment")
					{
						booking.Use(ets_mw.EtsOrderBookingAppointmentMW())
						{
							booking.POST("", packages_v1.CreateAppointment)
							booking.DELETE("", packages_v1.CancelAppointment)
						}
					}
				}

			}
		}

		etsProvier := g1.Group("ets-providers", middlewares.AuthenticateMW(), middlewares.OrgCheckerMW)
		{
			etsProvier.GET("orgs/:org-id/orders", packages_v1.GetEtsOrdersForProvider)
			etsProvier.GET("orgs/:org-id/order/headers", packages_v1.GetEtsOrdersHeaderForProvider)
			etsProvier.GET("orgs/:org-id/tasks", packages_v1.ListProviderEtsTasks)
			etsProvier.GET("orgs/:org-id/tasks/:task-id", packages_v1.GetProviderEtsTask)
			etsProvier.POST("orgs/:org-id/tasks/update/:task-id", packages_v1.UpdateEtsTaskProvider)
			etsProvier.GET("orgs/:org-id/orders/:order-id", packages_v1.GetEtsOrder)
			etsProvier.PUT("orgs/:org-id/orders/:order-id", packages_v1.UpdateEtsOrderForProvider)
			etsProvier.GET("orgs/:org-id/orders/:order-id/download-all-files", packages_v1.DownloadETSDocumentFiles)
			etsProvier.POST("orgs/:org-id/orders/:order-id/generate-upload-letter-url", packages_v1.ETSGeneratePresignUrlForLetter)
		}

		dashboard := g1.Group("dashboard", middlewares.AuthenticateMW())
		{
			dashboard.GET("summary", packages_v1.GetAdminDashboard)
			dashboard.GET("users", packages_v1.GetAdminDashboardForUser)
			dashboard.GET("providers", packages_v1.GetAdminDashboardForProvider)
			dashboard.GET("payments", packages_v1.GetAdminDashboardForPayment)
			dashboard.GET("services", packages_v1.GetAdminDashboardForService)
		}

		booking := g1.Group("booking-appointment")
		{
			booking.GET("business-days", packages_v1.GetBusinessDays)
			booking.GET("post-office", packages_v1.GetPostOffice)
			booking.GET("date", packages_v1.GetAppoimentDate)
			booking.GET("time", packages_v1.GetAppointmentTime)
		}
	}
	return r
}
