package v1

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"github.com/tidwall/gjson"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

type addStaffToOrderReq struct {
	StaffIDS []string        `json:"staff_ids"`
	Note     json.RawMessage `json:"note"`
}

func AddStaffToEtsOrder(c *gin.Context) {
	var req addStaffToOrderReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	auth := middlewares.GetAuthInfo(c)
	dao := getEtsDao(c)

	orderID, err := strconv.ParseInt(c.Param("order-id"), 10, 64)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err := FuncAddStaffToEtsOrder(orderID, req, auth, dao); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func FuncAddStaffToEtsOrder(orderID int64, req addStaffToOrderReq, auth *models.AuthInfo, dao db.IEtsDao) error {
	for _, staffID := range req.StaffIDS {
		existStaffs, err := dao.GetServiceOrderStaffs(orderID, staffID)
		if err != nil {
			return err
		}
		if len(existStaffs) > 0 {
			continue
		}

		staff, err := dao.VisaDB().GetUserByID(staffID)
		if err != nil {
			return err
		}
		if req.Note == nil {
			req.Note = []byte(`{}`)
		}

		randomCode := uuid.NewV4().String()
		if err := dao.UpdateServiceOrderStaff(orderID, staffID, randomCode, auth.UserID, req.Note); err != nil {
			return err
		}

		// Auto approve for self assign
		if staffID == auth.UserID {
			if err := dao.StaffConfirmHandleServiceOrder(orderID, []byte(`{}`), randomCode, "confirmed"); err != nil {
				return err
			}
			continue
		}

		// Prepare for email sending
		orderData, err := dao.QueryServiceOrders(models.ServiceOrderFilter{ID: []string{strconv.FormatInt(orderID, 10)}, Limit: 1})
		if err != nil {
			return err
		}

		order := orderData.Data[0]

		tasks, _, err := dao.QueryServiceTasks(map[string]any{"order_id": order.ID}, 0, 10)
		if err != nil {
			return err
		}

		inputPods := tasks[0].InputPods.ToMapKeyValue()

		etsJ := utils.StructToJSON(order.Service)

		webBaseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("web_base_url").String()

		var params = map[string]any{
			"OrderID":         order.ID,
			"FullName":        staff.GivenName + " " + staff.Surname,
			"Country":         order.Service.Country, //
			"ServiceType":     "Fastlane",
			"Terminal":        strings.Title(etsJ.Get("attributes.terminal").String()),
			"Task":            strings.Title(strings.Join(order.Service.Tasks, " ")),
			"ProcessingTime":  etsJ.Get("attributes.processing_time").String(),
			"NoOfTraveler":    inputPods["travel_passenger_info_no_of_traveler"],
			"AirportName":     inputPods["travel_exit_flight_exit_airport"],
			"AirlineName":     inputPods["travel_exit_flight_exit_airline"],
			"WelcomeName":     inputPods["travel_passenger_info_welcome_name"],
			"FlightNo":        utils.GetStringOrText(inputPods["travel_exit_flight_exit_flight"]),
			"ServiceDateTime": inputPods["travel_exit_flight_exit_timestamp"],
			"URL":             fmt.Sprintf("%s/fastlane/pickup-confirmed?order_id=%d&task_id=%d&service=ets&code=%s", webBaseURL, order.ID, tasks[0].ID, randomCode),
		}

		country, _ := gocountry.FindCountryByAlpha(order.Service.Country)
		params["Country"] = country.Name.Common

		if tasks[0].Type == "arrival" || tasks[0].Type == "vip_arrival" {
			params["AirportName"] = inputPods["travel_enter_flight_enter_airport"]
			params["AirlineName"] = inputPods["travel_enter_flight_enter_airline"]
			params["FlightNo"] = utils.GetStringOrText(inputPods["travel_enter_flight_enter_flight"])
			params["ServiceDateTime"] = inputPods["travel_enter_flight_enter_timestamp"]
		}

		params["ServiceDateTime"] = utils.StructToJSON(params).Get("ServiceDateTime").Time().Format("Mon, 02 Jan 2006 15:04")

		emailConfigMap := utils.GetMapEnv("ad_email")
		supportEmail := utils.StructToJSON(emailConfigMap).Get("support").String()
		var message = map[string]any{
			"template_name": "fastlane_request_to_staff",
			"to":            staff.Email,
			"bcc":           []string{supportEmail},
			"parameters":    params,
		}

		// Send email to sqs
		if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
			return err
		}

	}
	return nil
}

func StaffConfirmHandleEtsOrder(c *gin.Context) {
	var req models.ServiceOrderStaff
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	dao := getEtsDao(c)

	orderID, err := strconv.ParseInt(c.Param("order-id"), 10, 64)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if req.Note == nil {
		req.Note = []byte("{}")
	}

	if err := dao.StaffConfirmHandleServiceOrder(orderID, req.Note, req.StaffConfirmCode, req.StaffConfirmStatus); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	// Begin: Prepare for email sending
	orderData, err := dao.QueryServiceOrders(models.ServiceOrderFilter{ID: []string{strconv.FormatInt(orderID, 10)}, IncludeProvider: true, Limit: 1})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	order := orderData.Data[0]

	etsJ := utils.StructToJSON(order.Service)

	webBaseURL := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("web_base_url").String()
	adEmail := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String()
	user, err := dao.VisaDB().GetUserByID(order.UserID)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	params := map[string]any{
		"OrderID":       order.ID,
		"Country":       order.Service.Country,
		"ServiceType":   "Fastlane",
		"Terminal":      strings.Title(etsJ.Get("attributes.terminal").String()),
		"Task":          strings.Title(strings.Join(order.Service.Tasks, " ")),
		"ConfirmStatus": "Confirmed",
		"URL":           fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", webBaseURL, order.ID),
	}
	if req.StaffConfirmStatus == "rejected" {
		params["ConfirmStatus"] = "Rejected"
	}

	country, _ := gocountry.FindCountryByAlpha(order.Service.Country)
	params["Country"] = country.Name.Common

	// Notification for provider and AD
	{
		message := map[string]any{
			"template_name": "notify_ad_staff_confirm_fastlane",
			"to":            utils.StructToJSON(order.ProviderRaw).Get("contact.email").String(),
			"bcc":           []string{adEmail},
			"parameters":    params,
		}
		if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	// Notification for user
	if req.StaffConfirmStatus == "confirmed" {
		serviceOrderStaff, err := dao.GetServiceOrderStaffByCode(orderID, req.StaffConfirmCode)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		staff, err := dao.VisaDB().GetUserByID(serviceOrderStaff.StaffID)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

		staffContactJ := gjson.ParseBytes(staff.StaffContact)
		paramData := []PodDataChange{}

		paramData = append(paramData, PodDataChange{
			Title:    "Full Name",
			NewValue: staff.GivenName + " " + staff.Surname,
		})

		paramData = append(paramData, PodDataChange{
			Title:    "Email",
			NewValue: staff.Email,
		})

		if staffContactJ.Get("phone").String() != "" {
			paramData = append(paramData, PodDataChange{
				Title:    "Phone",
				NewValue: staffContactJ.Get("phone").String(),
			})
		}

		if networks := staffContactJ.Get("social_networks").Array(); len(networks) > 0 {
			for _, network := range networks {
				paramData = append(paramData, PodDataChange{
					Title:    strings.ToTitle(network.Get("channel").String()),
					NewValue: network.Get("id").String(),
				})
			}
		}

		shipment, err := dao.VisaDB().GetVisaShipment(order.ShipmentInfo.String)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

		to := user.Email
		cc := []string{}

		if shipment != nil && shipment.ShippingContact != nil && shipment.ShippingContact.Email != "" {
			if user.IsGuest() {
				to = shipment.ShippingContact.Email
			} else {
				cc = append(cc, shipment.ShippingContact.Email)
			}
		}

		message := map[string]any{
			"template_name": "notify_user_for_provider_contact",
			"to":            to,
			"cc":            cc,
			"bcc":           []string{adEmail},
			"parameters": map[string]any{
				"FullName": user.GivenName + " " + user.Surname,
				"OrderID":  orderID,
				"URL":      fmt.Sprintf("%s/dashboard/customer-orders/detail?order_id=%d&service=ets", webBaseURL, orderID),
				"Data":     paramData,
			},
			"attachments": []map[string]any{},
		}
		if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	// End: Prepare for email sending

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func DeleteStaffFromOrder(c *gin.Context) {
	var req addStaffToOrderReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	dao := getEtsDao(c)

	orderID, err := strconv.ParseInt(c.Param("order-id"), 10, 64)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	for _, staffID := range req.StaffIDS {
		if err := dao.DeleteServiceOrderStaff(orderID, staffID); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
