package v1

import (
	"bytes"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/gin-gonic/gin"
	xlsxt "github.com/ivahaev/go-xlsx-templater"
	"github.com/thoas/go-funk"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func GetEVisaPaymentCodes(c *gin.Context) {
	data, err := getEVisaPaymentCodes(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}

func getEVisaPaymentCodes(c *gin.Context) (string, error) {
	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		return "", err
	}

	dao := getEtsDao(c)

	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:             []string{strconv.Itoa(id)},
		IncludeTasks:   true,
		IncludePayment: true,
		Limit:          1,
	})
	if err != nil {
		return "", err
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		return "", fmt.Errorf("order does not exist")
	}
	o := existing.Data[0]
	bucket := middlewares.GetS3Buckets(c)
	awsConfig := aws.NewConfig().WithRegion(middlewares.GetAWSRegion(c)).WithLogLevel(aws.LogOff)
	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return "", err
	}
	ps := middlewares.GetS3Presigner(c)

	doc := xlsxt.New()
	if err := doc.ReadTemplate("VNEVisaPaymentCodes.xlsx"); err != nil {
		return "", err
	}
	paymentCodes := []map[string]any{}
	index := 0
	total := 0
	for _, task := range o.Tasks {
		rawCodes := utils.StructToJSON(task.FormCallback).Get("data").Array()
		inputPodPairJ := utils.StructToJSON(task.InputPods.ToMapKeyValue())
		outputPodPairJ := utils.StructToJSON(task.OutputPods.ToMapKeyValue())
		numberOfEntries := inputPodPairJ.Get("travel_enter_flight_number_of_entries").String()
		if numberOfEntries == "" {
			numberOfEntries = utils.StructToJSON(o.Service.Attributes).Get(("number_of_entries")).String()
		}
		surname := inputPodPairJ.Get("passport_core_info_surname").String()
		givenName := inputPodPairJ.Get("passport_core_info_given_name").String()

		fixedPrice := map[string]int{
			"single_entry":     650000,
			"multiple_entries": 1100000,
		}
		for _, rawCode := range rawCodes {
			index++
			paymentCode := map[string]any{
				"No":              index,
				"Code":            rawCode.Get("maSoHoSo").String(),
				"Surname":         surname,
				"GivenName":       givenName,
				"NumberOfEntries": funk.ShortIf(numberOfEntries == "single_entry", "Single entry", "Multiple entries"),
				"Price":           fixedPrice[numberOfEntries],
				"Currency":        "VND",
				"Action":          "",
			}
			if givenName == "FNU" {
				paymentCode["Action"] = "Fix given name"
			}
			if surname == "LNU" {
				paymentCode["Action"] = "Fix last name"
			}

			total += fixedPrice[numberOfEntries]
			paymentCodes = append(paymentCodes, paymentCode)
		}
		if outputPodPairJ.Get("application_application_info_application_status").String() == "created" {
			task.OutputPods.SetFEValue("application_application_info_application_status", "paid")
		}
		if err := dao.UpdateServiceTask(map[string]any{
			"output_pods": task.OutputPods,
		}, task.ID); err != nil {
			return "", err
		}
	}

	if err := doc.Render(map[string]any{
		"Data":          paymentCodes,
		"Total":         total,
		"TotalCurrency": "VND",
	}); err != nil {
		return "", err
	}

	var bb bytes.Buffer
	doc.Write(&bb)

	fileBucket := bucket["ariadirect_prod_applications"].(string)
	fileKey := fmt.Sprintf("files/Order_%d_payment_codes.xlsx", o.ID)

	xlsxBuff := bb.Bytes()

	// Save xlsx to S3
	if _, err := s3.New(sess).PutObject(&s3.PutObjectInput{
		Bucket:        aws.String(fileBucket),
		Key:           aws.String(fileKey),
		ACL:           aws.String("private"),
		Body:          bytes.NewReader(xlsxBuff),
		ContentLength: aws.Int64(int64(len(xlsxBuff))),
		ContentType:   aws.String("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
	}); err != nil {
		return "", err
	}

	fileURL, err := ps.PresignUrl(fileBucket, fileKey, 24*time.Hour)
	if err != nil {
		return "", err
	}
	return fileURL, nil
}
