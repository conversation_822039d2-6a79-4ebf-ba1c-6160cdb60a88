package v1

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/photo"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	uuid "github.com/satori/go.uuid"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"gopkg.in/guregu/null.v3"
	"gorm.io/datatypes"
)

type passengerInfo struct {
	EvisaBae64       string `json:"evisa_base64"`
	PassportBase64   string `json:"passport_base64"`
	PortraitBase64   string `json:"portrait_base64"`
	RegistrationCode string `json:"registration_code"`
}
type externalCreateETSOrderReq struct {
	ServiceID        int             `json:"service_id"`
	Passengers       []passengerInfo `json:"passengers"`
	BoardingTime     time.Time       `json:"boarding_time"`
	ArrivalTime      time.Time       `json:"arrival_time"`
	ContactPhone     string          `json:"contact_phone"`
	ContactEmail     string          `json:"contact_email"`
	ArrivalAirport   string          `json:"arrival_airport"`
	DepartureAirport json.RawMessage `json:"departure_airport"`
}

func ExternalCreateEtsOrder(c *gin.Context) {
	var req externalCreateETSOrderReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	if req.ServiceID == 0 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("service_id must be provided"))
		return
	}
	// Create user and send temp password
	dao := getEtsDao(c)
	randomPassword := utils.RandomPassword()

	user, err := dao.VisaDB().GetUserByEmail(req.ContactEmail)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if user.ID == "" {
		user = &models.User{
			ID:             uuid.NewV4().String(),
			Username:       strings.ToLower(req.ContactEmail),
			Email:          strings.ToLower(req.ContactEmail),
			Password:       randomPassword,
			PasswordInit:   randomPassword,
			OrganizationID: null.IntFrom(0),
		}
		dao.VisaDB().ORM().Create(user)
	}

	var o models.ServiceOrder
	o.ServiceID, o.UserID, o.Status, o.OrgID = req.ServiceID, user.ID, models.EtsOrderStatusOpen, int(user.OrganizationID.Int64)
	o.QueryPodValues = datatypes.JSON(utils.StructToJSON(gin.H{
		"service_core_info_country":    "VNM",
		"service_type":                 "new_visa_urgent",
		"service_core_info_entry_date": req.ArrivalTime,
	}).Raw)

	price, err := externalCreateEtsOrder(c, dao, &o, req)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	webBaseURL := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("web_base_url").String()
	message := map[string]any{
		"template_name": "ets_external_urgent_order_created",
		"to":            req.ContactEmail,
		"cc":            []string{},
		"bcc":           []string{utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String()},
		"parameters": map[string]any{
			"OrderID":  o.ID,
			"URL":      fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", webBaseURL, o.ID),
			"Email":    req.ContactEmail,
			"Password": randomPassword,
		},
		"attachments": []map[string]any{},
	}

	if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"order_id": o.ID,
		"price":    price,
	})
}

func externalCreateEtsOrder(c *gin.Context, dao db.IEtsDao, o *models.ServiceOrder, req externalCreateETSOrderReq) (*models.EtsPrice, error) {
	as, err := dao.QueryExtendedTravelServices(map[string]any{"id": o.ServiceID}, 0, 1)
	if err != nil {
		return nil, err
	}
	if len(as) == 0 {
		return nil, errors.InvalidEtsIDErr
	}

	priceMap, err := dao.GetServicePrice([]int{o.ServiceID})
	if err != nil {
		return nil, err
	}

	p := priceMap[o.ServiceID]
	if len(p) == 0 {
		return nil, fmt.Errorf("service without price %d", o.ServiceID)
	}
	price := p[0]

	inputPods, outputPods := ETSToInputOutputPods(dao, o.QueryPodValues, as[0], nil, false)
	orderInputPods, orderOutputPods := ETSToOrderInputOutputPods(dao, o.QueryPodValues, as[0].OrderSchema)
	o.ProviderID = price.ProviderID

	if req.ArrivalAirport == "" {
		req.ArrivalAirport = "Noi Bai International Airport"
	}

	orderInputPods.SetFEValue("travel_enter_flight_enter_airport", req.ArrivalAirport)
	orderInputPods.SetFEValue("travel_traveler_contact_info_contact_method", "phone")
	orderInputPods.SetFEValue("travel_traveler_contact_info_phone", map[string]string{"country": "USA", "phone": req.ContactPhone})
	orderInputPods.SetFEValue("travel_enter_flight_enter_timestamp", req.ArrivalTime)
	orderInputPods.SetFEValue("travel_exit_flight_exit_airport", utils.StructToJSON(req.DepartureAirport).Get("name").String())
	orderInputPods.SetFEValue("travel_exit_flight_boarding_datetime", req.BoardingTime)

	o.InputPods = funk.Values(orderInputPods).([]*models.InputPod)
	o.OutputPods = funk.Values(orderOutputPods).([]*models.InputPod)

	o.InputPodValues = o.InputPods.ToMapKeyValue()
	o.OutPodValues = o.OutputPods.ToMapKeyValue()
	if err := dao.CreateServiceOrder(o); err != nil {
		return nil, err
	}

	go func() {
		dao.UpdateServiceOrder(map[string]any{
			"summary": models.ServiceSummary{
				Currency:          price.Currency,
				UnitPrice:         price.Price,
				Total:             price.Price * float64(len(req.Passengers)),
				TotalActual:       price.Price * float64(len(req.Passengers)),
				TotalActualNote:   "",
				UnitPriceWithFees: price.Price,
			},
		}, o.ID)
		for _, passenger := range req.Passengers {
			task := &models.ServiceTask{
				Type:       (*as[0]).Tasks[0],
				Status:     models.EtsTaskStatusOpen,
				OrderID:    o.ID,
				ProviderID: price.ProviderID,
				InputPods:  make([]*models.InputPod, 0),
			}
			for _, inputPod := range inputPods {
				task.InputPods = append(task.InputPods, inputPod)
			}

			for _, outputPod := range outputPods {
				task.OutputPods = append(task.OutputPods, outputPod)
			}

			passportResp := getPassportDataFromImageBase64(passenger.PassportBase64)
			passsportFields := utils.StructToJSON(passportResp).Get("data").Get("fields")
			presigner := middlewares.GetS3Presigner(c)
			bucket := cast.ToString(middlewares.GetS3Buckets(c)[PassportImagesUploadBucketKey])

			evisaFileBuff, _ := base64.StdEncoding.DecodeString(passenger.EvisaBae64)
			passportBuff, _ := base64.StdEncoding.DecodeString(passenger.PassportBase64)
			passportURL, _ := presigner.UploadFileBuffer(bucket, fmt.Sprintf("passport_images/%s.jpg", uuid.NewV4().String()), passportBuff, "image/jpeg")

			passportPodValues := map[string]any{
				"travel_enter_flight_enter_airport":                    req.ArrivalAirport,
				"document_copy_of_passport_copy_of_passport_main_page": passportURL,
				"travel_visa_info_registration_email":                  req.ContactEmail,
			}
			if passenger.PortraitBase64 != "" {
				portraitBuff, _ := base64.StdEncoding.DecodeString(passenger.PortraitBase64)
				portraitBuff, _ = photo.RemoveImageBackground(portraitBuff, "4x6")
				portrailURL, _ := presigner.UploadFileBuffer(bucket, fmt.Sprintf("passport_images/%s.jpg", uuid.NewV4().String()), portraitBuff, "image/jpeg")
				passportPodValues["photo_passport_photo_copy_of_photo"] = portrailURL
			}
			if passenger.EvisaBae64 != "" {
				evisaContentType := http.DetectContentType(evisaFileBuff)
				extension := strings.Split(evisaContentType, "/")[1]
				evisaURL, _ := presigner.UploadFileBuffer(bucket, fmt.Sprintf("passport_images/%s.%s", uuid.NewV4().String(), extension), evisaFileBuff, evisaContentType)
				passportPodValues["travel_visa_info_proof_of_evisa_issued"] = evisaURL
				evisaResp := getEvisaDataImageURL(evisaURL)
				if val := utils.StructToJSON(evisaResp).Get("data").Get("fields").Get("registration_code").String(); val != "" {
					passenger.RegistrationCode = val
				}
				diffFields := []string{}
				evisaFields := utils.StructToJSON(evisaResp).Get("data").Get("fields")
				if passsportFields.Get("gender").String() != evisaFields.Get("gender").String() {
					diffFields = append(diffFields, "gender")
				}
				if passsportFields.Get("date_of_birth").String() != evisaFields.Get("date_of_birth").String() {
					diffFields = append(diffFields, "date_of_birth")
				}
				if (passsportFields.Get("surname").String() + " " + passsportFields.Get("given_name").String()) != evisaFields.Get("full_name").String() {
					diffFields = append(diffFields, "incorrect_name")
				}
				if passsportFields.Get("passport_number").String() != evisaFields.Get("passport_number").String() {
					diffFields = append(diffFields, "passport_number")
				}
				if passsportFields.Get("expiration_date").String() != evisaFields.Get("passport_expired_date").String() {
					diffFields = append(diffFields, "expiration_date")
				}
				task.InputPods.SetFEValue("travel_visa_info_evisa_error", diffFields)

			}
			if passenger.RegistrationCode != "" {
				passportPodValues["travel_visa_info_registration_code"] = passenger.RegistrationCode
				task.OutputPods.SetFEValue("application_application_info_registration_code", passenger.RegistrationCode)
				task.OutputPods.SetFEValue("application_application_info_registration_email", req.ContactEmail)

			}

			if cast.ToBool(passportResp["success"]) {
				for k, v := range cast.ToStringMap(cast.ToStringMap(passportResp["data"])["fields"]) {
					switch k {
					case "passport_number":
						passportPodValues["passport_core_info_passport_number"] = v
					case "date_of_birth":
						passportPodValues["passport_core_info_date_of_birth"] = v
					case "expiration_date":
						passportPodValues["passport_core_info_expiration_date"] = v
					case "issue_date":
						passportPodValues["passport_core_info_issue_date"] = v
					case "surname":
						passportPodValues["passport_core_info_surname"] = v
					case "given_name":
						passportPodValues["passport_core_info_given_name"] = v
					case "gender":
						passportPodValues["passport_core_info_gender"] = v
					case "nationality":
						passportPodValues["passport_core_info_nationality"] = v
					}
				}
			}
			for k, v := range passportPodValues {
				task.InputPods.SetFEValue(k, v)
			}

			if err := dao.CreateServiceTask(task); err != nil {
				return
			}
		}
	}()

	return price, nil
}

func getPassportDataFromImageBase64(base64String string) map[string]any {
	decodedBytes, err := base64.StdEncoding.DecodeString(base64String)
	if err != nil {
		return map[string]any{
			"success": false,
			"error":   "Failed to decode base64 string: " + err.Error(),
		}
	}

	client := resty.New()

	fileBuffer := bytes.NewBuffer(decodedBytes)

	var result map[string]any

	resp, err := client.R().
		SetFileReader("file", "image.jpg", fileBuffer).
		SetHeader("Content-Type", "multipart/form-data").
		SetResult(&result).
		Post("https://api.ariadirectcorp.com/v1/mrz-parser/parse-with-binary")

	if err != nil {
		return map[string]any{
			"success": false,
			"error":   "Failed to make API request: " + err.Error(),
		}
	}

	if resp.IsSuccess() {
		return result
	} else {
		return map[string]any{
			"success": false,
			"error":   "API request failed with status code: " + resp.Status(),
		}
	}
}

func getEvisaDataImageURL(url string) map[string]any {
	client := resty.New()
	var result map[string]any

	resp, err := client.R().
		SetBody(map[string]any{"image": url}).
		SetHeader("Content-Type", "application/json").
		SetResult(&result).
		Post("https://api.ariadirectcorp.com/v1/mrz-parser/vnm-evisa-details")

	if err != nil {
		return map[string]any{
			"success": false,
			"error":   "Failed to make API request: " + err.Error(),
		}
	}

	if resp.IsSuccess() {
		return result
	} else {
		return map[string]any{
			"success": false,
			"error":   "API request failed with status code: " + resp.Status(),
		}
	}
}
