package v1

import (
	"fmt"
	"mime"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"github.com/metal3d/go-slugify"
	uuid "github.com/satori/go.uuid"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	aderr "bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/file"
	"bitbucket.org/persistence17/aria/golang_services/sdk/notification"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

const (
	PackageSubmitQueue              = "pkg-submit-queue"
	UploadPreviewQueue              = "preview-queue"
	PackerQueue                     = "packer-queue"
	PackerOnlineQueue               = "packer-online"
	WebsiteQueue                    = "website"
	NotiUserQueue                   = "notification-user"
	NotiQueue                       = "notification"
	PassportImagesUploadBucketKey   = "ariadirect_prod_passport_images"
	ApplicationFilesUploadBucketKey = "ariadirect_prod_applications"
	VisaFormsBucketKey              = "ariadirect_prod_visa_forms"
	TravelProfileBucketKey          = "ariadirect_prod_traveler_profile"
	CorporationInvoiceBucketKey     = "ariadirect_prod_corporation_invoices"
	expirationForFiles              = 24 * time.Hour
)

type UploadRequest struct {
	FileName    string `json:"file_name"`
	ContentType string `json:"content_type"`
}

func GeneratePresignUrlForUpload(c *gin.Context) {
	typ := c.Query("file_type")
	if !utils.Contain(typ, []string{"passport", "application", "traveler-profile", "corporation-invoice", "label"}) {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "only supports passport/application file_type query param",
		})
		return
	}

	var upload UploadRequest
	if err := c.ShouldBindJSON(&upload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	if upload.FileName == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
		return
	}

	ps := middlewares.GetS3Presigner(c)
	bucket, key := "", ""
	if typ == "passport" {
		bucket = middlewares.GetS3Buckets(c)[PassportImagesUploadBucketKey].(string)
		key = "user-passport"
	} else if typ == "application" {
		bucket = middlewares.GetS3Buckets(c)[ApplicationFilesUploadBucketKey].(string)
		key = "user-passport"
	} else if typ == "traveler-profile" {
		bucket = middlewares.GetS3Buckets(c)[TravelProfileBucketKey].(string)
		key = "traveler-profile"
	} else if typ == "corporation-invoice" {
		bucket = middlewares.GetS3Buckets(c)[CorporationInvoiceBucketKey].(string)
		key = "self-invoice"
	} else if typ == "label" {
		bucket = middlewares.GetS3Buckets(c)[ApplicationFilesUploadBucketKey].(string)
		key = "label"
	} else {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "file_type is not supported",
			"success": false,
		})
		return
	}

	fileName := uuid.NewV4().String() + "." + strings.Split(upload.FileName, ".")[len(strings.Split(upload.FileName, "."))-1]
	key = file.BuildDocumentTemplateS3Key(fileName, key)
	var mimeType string
	if !strings.HasPrefix(upload.ContentType, ".") {
		mimeType = mime.TypeByExtension("." + upload.ContentType)
	} else {
		mimeType = mime.TypeByExtension(upload.ContentType)
	}

	uploadURLpresigned, err := ps.PresignUploadUrl(bucket, key, mimeType, 15*time.Minute)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	downURLpresigned, err := ps.PresignUrl(bucket, key, expirationForFiles)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"upload_url":   uploadURLpresigned,
			"download_url": downURLpresigned,
		},
	})
}

func GetBatchUploadID(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"batch_id": uuid.NewV4().String(),
		},
	})
}

type UploadDownloadURL struct {
	FileName    string `json:"file_name"`
	DownloadURL string `json:"download_url"`
	UploadURL   string `json:"upload_url"`
}

func GeneratePresignUrlForBatchUpload(c *gin.Context) {
	var files []UploadRequest
	if err := c.ShouldBindJSON(&files); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	batchID := c.Param("batch-id")

	if batchID == "" {
		batchID = uuid.NewV4().String()
	}

	ps := middlewares.GetS3Presigner(c)
	bucket := middlewares.GetS3Buckets(c)[ApplicationFilesUploadBucketKey].(string)

	result := []UploadDownloadURL{}
	for _, item := range files {
		item.FileName = strings.ReplaceAll(item.FileName, " ", "_")
		// fileName := uuid.NewV4().String() + "." + strings.Split(item.FileName, ".")[len(strings.Split(item.FileName, "."))-1]
		fileBaseName := filepath.Base(item.FileName) // example.png => example
		fileExtension := filepath.Ext(item.FileName) // example.png => .png
		fileWithoutExtension := fileBaseName[:len(fileBaseName)-len(filepath.Ext(fileBaseName))]

		newFileName := fileWithoutExtension + "_" + uuid.NewV4().String()[0:4] + strings.ToLower(fileExtension)

		key := fmt.Sprintf("batches/%s/%s", batchID, newFileName)

		var mimeType string
		if !strings.HasPrefix(item.ContentType, ".") {
			mimeType = mime.TypeByExtension("." + item.ContentType)
		} else {
			mimeType = mime.TypeByExtension(item.ContentType)
		}

		uploadURLpresigned, err := ps.PresignUploadUrl(bucket, key, mimeType, 15*time.Minute)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		downURLpresigned, err := ps.PresignUrl(bucket, key, expirationForFiles)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		result = append(result, UploadDownloadURL{
			FileName:    item.FileName,
			DownloadURL: downURLpresigned,
			UploadURL:   uploadURLpresigned,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"batch_id": batchID,
		"data":     result,
	})
}

type UploadRequestV2 struct {
	BatchID   string          `json:"batch_id"`
	GroupName string          `json:"group_name"`
	Files     []UploadRequest `json:"files"`
}

func GeneratePresignUrlForBatchUploadV2(c *gin.Context) {
	var req UploadRequestV2
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	if req.BatchID == "" {
		req.BatchID = uuid.NewV4().String()
	}

	groupNames := []string{"passport", "label", "airline_ticket", "visa", "photo_portrait", "document"}
	if !funk.ContainsString(groupNames, req.GroupName) {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "group_name is not supported, " + strings.Join(groupNames, ", "),
		})
		return
	}

	ps := middlewares.GetS3Presigner(c)
	bucket := middlewares.GetS3Buckets(c)[ApplicationFilesUploadBucketKey].(string)

	result := []UploadDownloadURL{}
	for _, item := range req.Files {
		item.FileName = strings.ReplaceAll(item.FileName, " ", "_")
		// fileName := uuid.NewV4().String() + "." + strings.Split(item.FileName, ".")[len(strings.Split(item.FileName, "."))-1]
		fileBaseName := filepath.Base(item.FileName) // example.png => example
		fileExtension := filepath.Ext(item.FileName) // example.png => .png
		fileWithoutExtension := fileBaseName[:len(fileBaseName)-len(filepath.Ext(fileBaseName))]

		newFileName := fileWithoutExtension + strings.ToLower(fileExtension)
		if newFileName == "image.jpg" {
			newFileName = uuid.NewV4().String() + ".jpg"
		}

		key := fmt.Sprintf("batches/%s/%s", req.BatchID, newFileName) // ariadirect_prod_applications/batches/:batch_id/:file_name

		var mimeType string
		if !strings.HasPrefix(item.ContentType, ".") {
			mimeType = mime.TypeByExtension("." + item.ContentType)
		} else {
			mimeType = mime.TypeByExtension(item.ContentType)
		}

		uploadURLpresigned, err := ps.PresignUploadUrl(bucket, key, mimeType, 15*time.Minute)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		downURLpresigned, err := ps.PresignUrl(bucket, key, expirationForFiles)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		result = append(result, UploadDownloadURL{
			FileName:    item.FileName,
			DownloadURL: downURLpresigned,
			UploadURL:   uploadURLpresigned,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": map[string]any{
			"batch_id":   req.BatchID,
			"group_name": req.GroupName,
			"files":      result,
		},
	})
}

func MatchPassportURLsWithBatchID(c *gin.Context) {
	ps := middlewares.GetS3Presigner(c)
	bucket := middlewares.GetS3Buckets(c)[ApplicationFilesUploadBucketKey].(string)

	images, err := ps.ListObjects(bucket, fmt.Sprintf("batches/%s", c.Param("batch-id")))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	resp, err := batchMatchPassport(bucket, images, ps)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data":    resp,
		"success": true,
	})
}

func GetPresignUrlForBatchUpload(c *gin.Context) {
	batchID := c.Param("batch-id")

	if batchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "batch_id is missing",
		})
		return
	}

	ps := middlewares.GetS3Presigner(c)
	bucket := middlewares.GetS3Buckets(c)[ApplicationFilesUploadBucketKey].(string)

	fileURLs, err := ps.ListObjects(bucket, fmt.Sprintf("batches/%s", batchID))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	result := []string{}
	for _, fileURL := range fileURLs {
		downURLpresigned, err := ps.PresignUrl(bucket, fileURL, expirationForFiles)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
		result = append(result, downURLpresigned)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func DeleteFileFromBatchUpload(c *gin.Context) {
	batchID := c.Param("batch-id")

	if batchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "batch_id is missing",
		})
		return
	}

	ps := middlewares.GetS3Presigner(c)
	var fileURLs []string
	if err := c.ShouldBindJSON(&fileURLs); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	for _, fileURL := range fileURLs {
		bucket, key, _ := utils.UrlToS3BucketAndKey(fileURL)
		if err := ps.DeleteObjs(bucket, []string{key}); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

type updateEtsTripDateReq struct {
	EntryDate time.Time  `json:"entry_date"`
	ExitDate  *time.Time `json:"exit_date"`
}

// UpdateETSTripDate update ets trip date
func UpdateETSTripDate(c *gin.Context) {
	var req updateEtsTripDateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	orderID := c.Param("order-id")
	if orderID == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}

	dao := getEtsDao(c)
	orders, err := dao.GetServiceOrders(map[string][]any{"id": {orderID}})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	svcs, err := dao.QueryEts(map[string]any{"id": orders[0].ServiceID})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(svcs) == 0 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("inactive service ID or service not found"))
		return
	}

	minEntryDate, config, err := dao.VisaDB().CalculateServiceProcessingTime(svcs[0].ID)
	fmt.Println(minEntryDate)
	if minEntryDate == nil || err != nil {
		response.HandleResponse(c, nil, aderr.ProcessingTimeEntryDateErr)
		return
	} else {
		serviceDate := getTimeWithConfigLocation(aws.Time(req.EntryDate), config)
		if minEntryDate.After(*serviceDate) {
			response.HandleResponse(c, nil, aderr.ProcessingTimeEntryDateErr)
			return
		}
	}

	tasks, _, err := dao.QueryServiceTasks(map[string]any{"order_id": orderID}, 0, 0)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	orders[0].InputPods.SetFEValue("service_core_info_entry_date", req.EntryDate)
	orders[0].InputPods.SetFEValue("service_core_info_exit_date", req.ExitDate)
	orders[0].InputPodValues = orders[0].InputPods.ToMapKeyValue()

	for _, task := range tasks {
		task.InputPods.SetFEValue("service_core_info_entry_date", req.EntryDate)
		task.InputPods.SetFEValue("service_core_info_exit_date", req.ExitDate)
		if err := dao.UpdateServiceTask(map[string]any{"input_pods": task.InputPods}, task.ID); err != nil {
			dao.Rollback()
			response.HandleResponse(c, nil, err)
			return
		}
	}

	orders[0].QueryPodValues, _ = sjson.SetBytes(orders[0].QueryPodValues, "service_core_info_entry_date", req.EntryDate)
	orders[0].QueryPodValues, _ = sjson.SetBytes(orders[0].QueryPodValues, "service_core_info_exit_date", req.ExitDate)

	if err := dao.UpdateServiceOrder(map[string]any{
		"query_pod_values": orders[0].QueryPodValues,
		"input_pods":       orders[0].InputPods,
		"input_pod_values": orders[0].InputPodValues,
	}, orders[0].ID); err != nil {
		dao.Rollback()
		response.HandleResponse(c, nil, err)
		return
	}

	if err = dao.Commit(); err != nil {
		dao.Rollback()
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func canUserAccessAllPkgs(authInfo *models.AuthInfo) bool {
	if authInfo == nil || len(authInfo.Roles) == 0 {
		return false
	}
	for _, r := range authInfo.Roles {
		switch r {
		case "ad_admin":
			// add more roles that can access all packages here
			return true
		default:
			// do nothing
		}
	}
	return false
}

func SummaryPackage(c *gin.Context) {

}

type clonePackageReq struct {
	EntryDate time.Time `json:"entry_date"`
	ExitDate  time.Time `json:"exit_date"`
}

type CompressedFilesReq struct {
	Files []string `json:"files"`
}

type DownloadSingleFile struct {
	URL     string `json:"url"`
	PodName string `json:"pod_name"`
}

func DownloadSingleFileInETSTask(c *gin.Context) {
	var req DownloadSingleFile
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	id, err := utils.GetIntPathParam(c, "task-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}

	var fileNameKey string
	if id != 0 {
		dao := getEtsDao(c)
		tasks, _, err := dao.QueryServiceTasks(map[string]any{"id": id}, 0, 1)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		if len(tasks) == 0 {
			response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("task not found"))
			return
		}
		app := utils.StructToJSON(tasks[0].InputPods)
		fileNameKey = getETSNameFromPod(0, app)
	} else {
		fileNameKey = uuid.NewV4().String()
	}

	downloader := middlewares.GetS3Downloader(c)
	ext := filepath.Ext(req.URL)

	bucket, key, err := utils.UrlToS3BucketAndKey(req.URL)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	buff, err := downloader.DownloadFromS3BucketToBuffer(bucket, key)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	newKey := fmt.Sprintf("%s/%s", "download/order-"+c.Param("package-id"), fileNameKey)
	if req.PodName != "" {
		newKey += "_" + strings.ReplaceAll(strings.Title(req.PodName), " ", "_")
	}
	newKey += ext

	contentType := mime.TypeByExtension(ext)
	ps := middlewares.GetS3Presigner(c)

	if _, err = ps.UploadFileBuffer(bucket, newKey, buff, contentType); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	downURLpresigned, err := ps.PresignUrl(bucket, newKey, expirationForFiles)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"url": downURLpresigned,
		},
	})
}

type VisaScreeningQuestionsQuery struct {
	Country           string `form:"country"`
	RegionOfResidence string `form:"region_of_residence"`
	Destination       string `form:"destination"`
	Nationality       string `form:"nationality"`
}

func GetVisaScreeningQuestions(c *gin.Context) {
	var query VisaScreeningQuestionsQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	dao, _ := middlewares.GetVisaDao(c)

	req := models.VisaPodFilter{
		Category: "screening",
	}
	if query.Country != "" {
		req.Country = query.Country
	}

	if query.RegionOfResidence != "" {
		req.RegionOfResidence = query.RegionOfResidence
	}

	if query.Destination != "" {
		req.Destination = query.Destination
	}

	if query.Nationality != "" {
		req.Nationality = query.Nationality
	}

	res, err := dao.GetVisaPodList(req)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, res)
}

func GetEVisaPdfForms(c *gin.Context) {
	data, err := getEVisaPdfForms(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}

type EVisaFile struct {
	FileName string `json:"file_name"`
	FileURL  string `json:"file_url"`
}

func getEVisaPdfForms(c *gin.Context) (map[int64][]string, error) {
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return nil, err
	}

	etsDao := getEtsDao(c)
	localize := middlewares.GetLocalize(c)

	orderID, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		return nil, err
	}

	order, err := etsDao.GetServiceOrderByID(orderID)
	if err != nil {
		return nil, err
	}

	tasks, _, err := etsDao.QueryServiceTasks(map[string]any{"order_id": orderID}, 0, 0)
	if err != nil {
		return nil, err
	}

	baseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("api_base_url").String()
	result := map[int64][]string{}

	totalAppGrantedVisa := 0
	eVisaFiles := []EVisaFile{}

	shipment, err := dao.GetVisaShipment(order.ShipmentInfo.String)
	if err != nil {
		return nil, err
	}
	shipmentJ := utils.StructToJSON(shipment).Get("shipping_contact")

	ccEmails := []string{shipmentJ.Get("email").String()}
	for _, task := range tasks {
		if task.OutputFiles == nil {
			task.OutputFiles = &models.FileMap{}
		}

		inputPodJ := task.InputPods.ToMapKeyValueV2(order.InputPods)
		outputPodJ := task.OutputPods.ToMapKeyValue()
		dob := cast.ToTime(inputPodJ["passport_core_info_date_of_birth"]).Format("02/01/2006")
		email := cast.ToString(outputPodJ["application_application_info_registration_email"])
		paymentStatus := cast.ToString(outputPodJ["application_application_info_application_status"])

		userInputEmail := cast.ToString(inputPodJ["travel_visa_info_registration_email"])
		if userInputEmail == "" {
			userInputEmail = cast.ToString(outputPodJ["application_application_info_registration_email"])
		}
		if userInputEmail != "" {
			ccEmails = append(ccEmails, userInputEmail)
		}

		if paymentStatus == "granted" {
			totalAppGrantedVisa++
			continue
		}

		if paymentStatus != "paid" {
			continue
		}

		eVisaCodeStr := cast.ToString(outputPodJ["application_application_info_registration_code"])
		eVisaCodes := regexp.MustCompile(`E[A-Z0-9]+`).FindAllString(eVisaCodeStr, -1)

		newFiles := []string{}
		for _, code := range eVisaCodes {
			resp, err := resty.New().R().SetBody(map[string]any{
				"maSoHoSo": code,
				"email":    email,
				"ngaySinh": dob,
			}).Post(baseURL + "/v1/third-party-service/evisa/get-vnm-visa")
			if err != nil {
				fmt.Println(err)
			}
			fmt.Println(resp.String())
			pdfFormFile := gjson.Parse(resp.String()).Get("data").String()
			if pdfFormFile != "" {
				newFiles = append(newFiles, pdfFormFile)
			}
		}

		if len(newFiles) > 0 && len(newFiles) == len(eVisaCodes) {
			(*task.OutputFiles)["copy_of_new_visa"] = newFiles
			task.OutputPods.SetFEValue("application_application_info_copy_of_new_visa", newFiles)
			task.OutputPods.SetFEValue("application_application_info_application_status", "granted")
			if note := cast.ToString(outputPodJ["application_application_info_note"]); note != "" {
				task.OutputPods.SetFEValue("application_application_info_note", note+"\n"+"Granted at: "+time.Now().Format("02/01/2006"))
			}
			if err := etsDao.UpdateServiceTask(map[string]any{
				"output_files": task.OutputFiles,
				"output_pods":  task.OutputPods,
				"status":       models.EtsTaskStatusApproved,
			}, task.ID); err != nil {
				return nil, err
			}
			result[task.ID] = newFiles
			for _, file := range newFiles {
				eVisaFiles = append(eVisaFiles, EVisaFile{
					FileName: slugify.Marshal(task.GetAppName("")) + ".pdf",
					FileURL:  file,
				})
			}
			totalAppGrantedVisa++
		}
	}

	if len(eVisaFiles) > 0 {
		// Make order as complete when all tasks are approved
		if totalAppGrantedVisa == len(tasks) {
			if order.TrackingTimes == nil {
				order.TrackingTimes = &models.PropertyMap{}
			}

			(*order.TrackingTimes)[string(models.EtsOrderStatusCompleted)] = time.Now()

			if err := etsDao.UpdateServiceOrder(map[string]any{
				"status":                   models.EtsOrderStatusCompleted,
				"completed_time":           time.Now(),
				"tracking_times":           order.TrackingTimes,
				"is_send_feedback_to_user": true,
			}, orderID); err != nil {
				return nil, err
			}

			if !order.IsSendFeedbackToUser {
				if err := notification.NotifyFeedbackToUser(orderID); err != nil {
					return nil, err
				}
			}

		}

		user, err := dao.GetUserByID(order.UserID)
		if err != nil {
			return nil, err
		}

		attachments := []map[string]any{}
		for _, file := range eVisaFiles {
			bucket, key, _ := utils.UrlToS3BucketAndKey(file.FileURL)
			attachments = append(attachments, map[string]any{
				"bucket":    bucket,
				"key":       key,
				"file_name": file.FileName,
			})
		}

		ets, err := etsDao.GetETSByID(order.ServiceID)
		if err != nil {
			return nil, err
		}

		fullName := "SIR/MADAM"
		if user.GivenName != "" {
			fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(user.GivenName), strings.TrimSpace(user.Surname)))
		} else if shipmentJ.Get("given_name").String() != "" {
			fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(shipmentJ.Get("given_name").String()), strings.TrimSpace(shipmentJ.Get("surname").String())))
		}

		parameters := map[string]any{
			"TotalGrantedVisa": totalAppGrantedVisa,
			"TotalApps":        len(tasks),
			"PackageID":        orderID,
			"FullName":         fullName,
			"IssueMethod":      localize.EN.ServiceType[ets.ServiceType],
			"VisaCountry":      localize.EN.Country[ets.Country],
		}
		emailConfigMap := utils.StructToJSON(utils.GetMapEnv("ad_email"))

		sendVisaToTemplate := utils.StructToJSON(ets.Attributes).Get("email_template.send_visa_to_user").String() // Get email template from attributes

		if sendVisaToTemplate != "" {
			// Send max 20 attachments per email
			for _, attachmentItems := range chunkBy(attachments, 20) {
				message := map[string]any{
					"template_name": sendVisaToTemplate,
					"to":            user.Email,
					"cc":            ccEmails,
					"bcc":           []string{emailConfigMap.Get("visas").String()},
					"parameters":    parameters,
					"attachments":   attachmentItems,
				}

				if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
					return nil, err
				}
			}
		}

	}

	return result, nil
}

func chunkBy[T any](items []T, chunkSize int) (chunks [][]T) {
	for chunkSize < len(items) {
		items, chunks = items[chunkSize:], append(chunks, items[0:chunkSize:chunkSize])
	}
	return append(chunks, items)
}
