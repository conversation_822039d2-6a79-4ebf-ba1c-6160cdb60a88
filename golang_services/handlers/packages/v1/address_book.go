package v1

import (
	"net/http"

	"github.com/gin-gonic/gin"

	aderr "bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func ListAddresses(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	addresses, err := listAddress(dao, travelInfo.ID)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    addresses,
	})
}

func listAddress(dao db.IDao, travelerID string) ([]*models.AddressBookItem, error) {
	addresses, err := dao.GetAddressBookItemsByTravelerID(travelerID, map[string]any{"status": "active"})
	if err != nil {
		return nil, err
	}
	return addresses, err
}

func UpdateAddress(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	id, err := utils.GetIntPathParam(c, "address-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	var update models.AddressBookItem
	if err := c.ShouldBind(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	updated, err := updateAddress(dao, travelInfo.ID, id, &update)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    updated,
	})
}

func updateAddress(dao db.IDao, travelerID string, id int, update *models.AddressBookItem) (*models.AddressBookItem, error) {
	existing, err := dao.GetAddressBookItemByID(id)
	if err != nil {
		return nil, err
	}
	if existing == nil || existing.TravelerID != travelerID || existing.Status != "active" {
		return nil, aderr.NotFoundErr
	}

	var primaryAddress *models.AddressBookItem
	if update.IsPrimaryAddress == true {
		if primaryAddress, err = getPrimaryAddress(dao, travelerID); err != nil {
			return nil, err
		}
	}

	if err := dao.Begin(); err != nil {
		dao.Rollback()
		return nil, err
	}

	if primaryAddress != nil && primaryAddress.ID != id {
		primaryAddress.IsPrimaryAddress = false
		if err = dao.UpdateAddressBookItem(primaryAddress.ID, primaryAddress); err != nil {
			dao.Rollback()
			return nil, err
		}
	}

	if err = dao.UpdateAddressBookItem(id, update); err != nil {
		dao.Rollback()
		return nil, err
	}

	if err = dao.Commit(); err != nil {
		dao.Rollback()
		return nil, err
	}

	return dao.GetAddressBookItemByID(id)
}

func AddAddress(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	var newAddress models.AddressBookItem
	if err := c.ShouldBind(&newAddress); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	newAddress.TravelerID = travelInfo.ID
	newAddress.UserID = travelInfo.UserID
	added, err := addAddress(dao, &newAddress)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    added,
	})
}

func addAddress(dao db.IDao, add *models.AddressBookItem) (*models.AddressBookItem, error) {
	var primaryAddress *models.AddressBookItem
	var err error
	if add.IsPrimaryAddress == true {
		if primaryAddress, err = getPrimaryAddress(dao, add.TravelerID); err != nil {
			return nil, err
		}
	}

	if err := dao.Begin(); err != nil {
		dao.Rollback()
		return nil, err
	}

	if primaryAddress != nil {
		primaryAddress.IsPrimaryAddress = false
		if err = dao.UpdateAddressBookItem(primaryAddress.ID, primaryAddress); err != nil {
			dao.Rollback()
			return nil, err
		}
	}

	add.Status = "active"
	id, err := dao.InsertAddressBookItem(add)
	if err != nil {
		dao.Rollback()
		return nil, err
	}

	if err = dao.Commit(); err != nil {
		dao.Rollback()
		return nil, err
	}

	return dao.GetAddressBookItemByID(id)
}

func DeleteAddress(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	id, err := utils.GetIntPathParam(c, "address-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err = deactivateAddress(dao, travelInfo.ID, id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func deactivateAddress(dao db.IDao, travelerID string, id int) error {
	existing, err := dao.GetAddressBookItemByID(id)
	if err != nil {
		return err
	}
	if existing == nil || existing.TravelerID != travelerID || existing.Status != "active" {
		return aderr.NotFoundErr
	}
	if err = dao.DeactivateAddressBookItem(id); err != nil {
		return err
	}
	return nil
}

func getPrimaryAddress(dao db.IDao, travelerID string) (*models.AddressBookItem, error) {
	addresses, err := dao.GetAddressBookItemsByTravelerID(travelerID, map[string]any{"status": "active", "is_primary_address": "true"})
	if err != nil {
		return nil, err
	}
	if len(addresses) == 0 {
		return nil, nil
	}
	return addresses[0], nil
}
