package v1

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"net/smtp"
	"os/exec"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tidwall/gjson"
)

var (
	whitelistedDomains = map[string]bool{
		"privaterelay.appleid.com": true,
		"mailinator.com":           true,
		"yopmail.com":              true,
	}
)

func getMXRecords(domain string) ([]string, error) {
	mx, err := net.LookupMX(domain)
	if err != nil {
		return nil, err
	}
	mxRecords := make([]string, len(mx))
	for i, mxRecord := range mx {
		mxRecords[i] = mxRecord.Host
	}
	return mxRecords, nil
}

func normalizeEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return email
	}

	localPart := parts[0]
	domain := parts[1]

	if idx := strings.Index(localPart, "+"); idx != -1 {
		localPart = localPart[:idx]
	}

	localPart = strings.ReplaceAll(localPart, ".", "")

	return localPart + "@" + domain
}

func checkSMTP(ctx context.Context, mxRecord, email string) error {
	ports := []string{"25", "587", "465"}
	for _, port := range ports {
		var (
			conn net.Conn
			err  error
		)

		dialCtx, cancel := context.WithTimeout(ctx, 1*time.Second)
		defer cancel()

		d := net.Dialer{}
		if port == "465" {
			conn, err = tls.DialWithDialer(&d, "tcp", mxRecord+":"+port, &tls.Config{})
		} else {
			conn, err = d.DialContext(dialCtx, "tcp", mxRecord+":"+port)
		}

		if err != nil {
			continue
		}
		defer conn.Close()

		client, err := smtp.NewClient(conn, mxRecord)
		if err != nil {
			return err
		}
		defer client.Close()

		if err := client.Hello("ariadirect.com"); err != nil {
			return err
		}
		if err := client.Mail("<EMAIL>"); err != nil {
			return err
		}
		if err := client.Rcpt(email); err != nil {
			return err
		}

		return nil
	}
	return fmt.Errorf("failed to connect to SMTP server")
}

func verifyEmail(email string) (bool, error) {
	email = normalizeEmail(email)
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false, fmt.Errorf("invalid email format")
	}

	domain := parts[1]
	if whitelistedDomains[domain] {
		return true, nil
	}

	mxRecords, err := getMXRecords(domain)
	if err != nil || len(mxRecords) == 0 {
		return false, fmt.Errorf("MX record: %v", err)
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	var wg sync.WaitGroup
	errCh := make(chan error, len(mxRecords))

	for _, mx := range mxRecords {
		wg.Add(1)
		go func(mx string) {
			defer wg.Done()
			if err := checkSMTP(ctx, mx, email); err == nil {
				cancel()
				errCh <- nil
			} else {
				errCh <- err
			}
		}(mx)
	}

	go func() {
		wg.Wait()
		close(errCh)
	}()

	for err := range errCh {
		if err == nil {
			return true, nil
		}
	}

	return true, nil
}

type EmailValidate struct {
	Email   string `json:"email"`
	Valid   bool   `json:"valid"`
	Message string `json:"message"`
}

func EmailValidation(c *gin.Context) {
	emails := c.QueryArray("email")
	if len(emails) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "email is required",
		})
		return
	}

	var wg sync.WaitGroup
	resultChan := make(chan *EmailValidate, len(emails))

	for _, email := range emails {
		wg.Add(1)
		go func(email string) {
			defer wg.Done()

			valid, err := verifyEmail(email)

			if valid && err == nil {
				resultChan <- &EmailValidate{
					Valid:   true,
					Email:   email,
					Message: fmt.Sprintf("Email %s is valid", email),
				}
			} else {
				resultChan <- &EmailValidate{
					Valid:   false,
					Email:   email,
					Message: fmt.Sprintf("Email %s is invalid: %v", email, err),
				}
			}
		}(email)
	}

	go func() {
		wg.Wait()
		close(resultChan)
	}()

	var result []*EmailValidate
	for r := range resultChan {
		result = append(result, r)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func EmailValidationV2(c *gin.Context) {
	emails := c.QueryArray("email")
	if len(emails) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "email is required",
		})
		return
	}

	var result []*EmailValidate

	var wg sync.WaitGroup
	resultChan := make(chan *EmailValidate, len(emails))

	for _, email := range emails {
		wg.Add(1)
		go func(email string) {
			defer wg.Done()

			cmd := exec.Command("./check_email_exist", email)
			output, err := cmd.Output()
			if err != nil {
				resultChan <- &EmailValidate{
					Valid:   false,
					Email:   email,
					Message: fmt.Sprintf("Failed to run command for email %s: %v", email, err),
				}
				return
			}
			fmt.Println(string(output))
			reactable := gjson.ParseBytes(output).Get("is_reachable").String()
			if reactable == "invalid" {
				resultChan <- &EmailValidate{
					Valid:   false,
					Email:   email,
					Message: fmt.Sprintf("Email %s is invalid", email),
				}
			} else {
				resultChan <- &EmailValidate{
					Valid:   true,
					Email:   email,
					Message: fmt.Sprintf("Email %s is valid", email),
				}
			}
		}(email)
	}

	go func() {
		wg.Wait()
		close(resultChan)
	}()

	for r := range resultChan {
		result = append(result, r)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}
