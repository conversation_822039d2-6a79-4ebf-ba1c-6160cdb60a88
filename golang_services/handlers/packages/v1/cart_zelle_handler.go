package v1

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/Jeffail/gabs"
	"github.com/gin-gonic/gin"
	"github.com/lib/pq"
	"github.com/rs/xid"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func CreateManualTransaction(c *gin.Context) {
	if err := createManualTransaction(c); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
}

func createManualTransaction(c *gin.Context) error {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil || !funk.ContainsString(authInfo.UserTypes, models.UserTypeAdmin) {
		return fmt.Errorf("This access need admin permission")
	}

	var req = models.PaymentManual{}
	if err := c.ShouldBindJSON(&req); err != nil {
		return err
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}

	// Get cart
	cartData, err := getCartItems(c)
	if err != nil {
		return err
	}

	masterPayment := cartData.Payment

	if masterPayment == nil {
		return fmt.Errorf("Cart don't have payment info")
	}

	// if masterPayment.Status != models.PaymentStatusPending {
	// 	return fmt.Errorf("Payment status is not pending")
	// }

	if masterPayment.Properties == nil {
		masterPayment.Properties = &models.PropertyMap{
			"cart_id": cartData.Cart.ID,
		}
	}

	buff, err := json.Marshal(masterPayment.Properties)
	if err != nil {
		return err
	}

	masterPaymentJ, err := gabs.ParseJSON(buff)
	if err != nil {
		return err
	}

	transID := xid.New().String()
	childPayment := &models.Payment{
		ID:                    transID,
		ExternalTransactionID: masterPayment.ID,
		Count:                 1,
		Method:                req.Method,
		Currency:              req.Currency,
		UnitPrice:             req.Amount,
		Total:                 req.Amount,
		Status:                models.PaymentStatusSuccess,
		Properties: &models.PropertyMap{
			"cart_id":           cartData.Cart.ID,
			"confirmation_code": req.ConfirmationCode,
			"note":              req.Note,
			"created_by":        authInfo.Email,
		},
		CreatedAt: time.Now(),
	}

	paymentDao, err := middlewares.GetPaymentsDao(c)
	if err != nil {
		return err
	}

	if err := paymentDao.InsertPayment(childPayment); err != nil {
		return err
	}

	// Add a transaction to  payment details
	masterPaymentJ.ArrayAppendP(childPayment, "transactions")

	// Set sub-payment in the payment's properties
	if err = json.Unmarshal(masterPaymentJ.Bytes(), masterPayment.Properties); err != nil {
		return err
	}

	res, err := calculateRemainAmount(dao.DB(), cartData, true, req.ForcePaid)
	if err != nil {
		return err
	}

	if _, err = masterPaymentJ.Set(res.Remain, "remain"); err != nil {
		return err
	}

	if _, err = masterPaymentJ.Set(res.Paid, "paid"); err != nil {
		return err
	}

	// Update package's payment properties
	if _, err = dao.DB().Db.PSQL.Update("payment").
		Set("properties", string(masterPaymentJ.Bytes())).Where("id = ?", masterPayment.ID).
		RunWith(dao.DB().Db.Db).Exec(); err != nil {
		return err
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    res,
	})
	return nil
}

func GetManualTransactions(c *gin.Context) {
	result, err := getManualTransactions(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}
func getManualTransactions(c *gin.Context) (*models.RemainAmount, error) {
	// Get cart
	cartData, err := getCartItems(c)
	if err != nil {
		return nil, err
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return nil, err
	}

	if cartData.Payment == nil {
		return nil, fmt.Errorf("Cart don't have payment info")
	}

	return calculateRemainAmount(dao.DB(), cartData, false, false)
}

// calculateRemainAmount calculate remaining amount due fees
func calculateRemainAmount(dao *db.Dao, cartData *models.CartData, checkPackageStatus bool, forcePaid bool) (*models.RemainAmount, error) {
	payment := cartData.Payment
	buff, err := json.Marshal(payment.Properties)
	if err != nil {
		return nil, nil
	}

	// Get sub payments
	payments := []models.Payment{}

	trans := gjson.ParseBytes(buff).Get("transactions")
	if len(trans.Array()) > 0 {
		if err = json.Unmarshal([]byte(trans.Raw), &payments); err != nil {
			return nil, err
		}
	}

	var paid float64
	for _, payment := range payments {
		if payment.Status == models.PaymentStatusSuccess {
			paid += payment.Total
		}
	}
	res := &models.RemainAmount{
		Total:       payment.Total,
		Paid:        paid,
		Remain:      payment.Total - paid,
		Transaction: payment,
	}
	cart, err := dao.GetCartByPayment(payment.ID)
	if err != nil {
		return nil, err
	}

	serviceIDs := []string{}
	notPaidServiceIDs := []string{}

	for _, item := range cart.ServiceData {
		if !item.ServiceDetail.IsPaid() {
			notPaidServiceIDs = append(notPaidServiceIDs, item.ProductID)
		}
		serviceIDs = append(serviceIDs, item.ProductID)

	}

	if checkPackageStatus {
		if paid >= payment.Total {
			res.Remain = 0
			// Change package status: waitting_payment ==> paid
			if len(notPaidServiceIDs) > 0 {
				if _, err = dao.DB().Db.Db.Exec("UPDATE service_orders SET status = $1, order_time = $2, updated_at = $2 WHERE id = ANY($3)",
					models.EtsOrderStatusPaid, time.Now(), pq.Array(notPaidServiceIDs)); err != nil {
					return nil, err
				}
			}

			// Update main payment
			if _, err = dao.DB().Db.Db.Exec("UPDATE payment SET status = $1, updated_at = $2 WHERE id = $3", models.PaymentStatusSuccess, time.Now(), payment.ID); err != nil {
				return nil, err
			}

			// Update cart
			if err = dao.UpdateCart(cart.Cart.ID, map[string]any{
				"status":     models.CartStatus.Completed,
				"is_current": false,
			}); err != nil {
				return nil, err
			}

			// Update pod
			var tasks []models.ServiceTask
			if err := dao.DB().Db.Db.Select(&tasks, fmt.Sprintf(`SELECT id, input_pods, output_pods FROM service_tasks WHERE order_id IN (%s)`, strings.Join(serviceIDs, ","))); err != nil {
				return nil, err
			}

			for _, task := range tasks {
				for i := range task.InputPods {
					if task.InputPods[i].Name == "copy_of_photo" {
						newValue := strings.ReplaceAll(cast.ToString(task.InputPods[i].GetFEValue()), "-demo", "")
						task.InputPods.SetFEValue(task.InputPods[i].ID, newValue)
					}
				}
				if _, err = dao.DB().Db.Db.Exec(`UPDATE service_tasks SET input_pods = $1, input_pod_values = $2 WHERE id = $3`,
					utils.StructToJSON(task.InputPods).Raw,
					utils.StructToJSON(task.InputPods.ToMapKeyValue()).Raw,
					task.ID); err != nil {
					return nil, err
				}
			}

		}
	}
	// else {
	// 	// Change cart status: waiting_payment ==> lock
	// 	if checkPackageStatus {
	// 		cart, err := dao.GetCartByPayment(payment.ID)
	// 		if err != nil {
	// 			return nil, err
	// 		}

	// 		// Update cart
	// 		if err = dao.UpdateCart(cart.Cart.ID, map[string]any{"status": models.CartStatus.Lock}); err != nil {
	// 			return nil, err
	// 		}
	// 	}
	// }

	return res, nil
}
