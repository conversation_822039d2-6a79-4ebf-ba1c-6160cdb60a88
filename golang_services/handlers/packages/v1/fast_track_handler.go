package v1

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	aderr "bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func ListFastTracks(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil || authInfo.UserID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "missing user info",
		})
		return
	}
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	fastTracks, err := listFastTracks(dao, authInfo.UserID)
	if err != nil {
		response.HandleResponse(c, nil, err)
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    fastTracks,
	})
}

func listFastTracks(dao db.IDao, userID string) ([]*models.FastTrack, error) {
	fastTracks, err := dao.QueryFastTrack(map[string]any{"user_id": userID})
	if err != nil {
		return nil, err
	}
	return fastTracks, nil
}

func AddFastTrack(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil || authInfo.UserID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "missing user info",
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	var newFastTrack models.FastTrack
	if err := c.ShouldBind(&newFastTrack); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// checking fastTrack number exist
	fastTracks, err := dao.QueryFastTrack(map[string]any{"user_id": authInfo.UserID, "number": newFastTrack.Number})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(fastTracks) > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   errors.AlreadyExistErr,
		})
		return
	}

	added, err := addFastTrack(dao, authInfo.UserID, &newFastTrack)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    added,
	})
}

func addFastTrack(dao db.IDao, userID string, fastTrack *models.FastTrack) (*models.FastTrack, error) {
	fastTrack.UserID = userID
	id, err := dao.InsertFastTrack(fastTrack)
	if err != nil {
		return nil, err
	}
	return dao.GetFastTrackByID(id)
}

func UpdateFastTrack(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil || authInfo.UserID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "missing user info",
		})
		return
	}
	id, err := utils.GetIntPathParam(c, "fast-track-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	var update models.FastTrack
	if err := c.ShouldBind(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	updated, err := updateFastTrack(dao, authInfo.UserID, id, &update)
	if err != nil {
		response.HandleResponse(c, nil, err)
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    updated,
	})
}

func updateFastTrack(dao db.IDao, userID string, id int, update *models.FastTrack) (*models.FastTrack, error) {
	existing, err := dao.GetFastTrackByID(id)
	if err != nil {
		return nil, err
	}
	if existing == nil || existing.UserID != userID {
		return nil, aderr.NotFoundErr
	}
	update.UserID = userID
	if err = dao.UpdateFastTrack(id, update); err != nil {
		return nil, err
	}
	return dao.GetFastTrackByID(id)
}

func DeleteFastTrack(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil || authInfo.UserID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "missing user info",
		})
		return
	}
	id, err := utils.GetIntPathParam(c, "fast-track-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err = deleteFastTrack(dao, authInfo.UserID, id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func deleteFastTrack(dao db.IDao, userID string, id int) error {
	existing, err := dao.GetFastTrackByID(id)
	if err != nil {
		return err
	}
	if existing == nil || existing.UserID != userID {
		return aderr.NotFoundErr
	}

	if err = dao.DeleteFastTrack(id); err != nil {
		return err
	}
	return nil
}
