package v1

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func GetGroupFilterItem(c *gin.Context) {
	var filter models.CartGroupItemRequest
	if err := c.BindQuery(&filter); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	authInfo := middlewares.GetAuthInfo(c)

	if filter.Limit <= 0 {
		filter.Limit = 10 // Default 10 Items
	}

	dao, err := middlewares.GetVisaDao(c)
	orm := middlewares.GetORMDao(c)
	if !authInfo.IsADAdmin() {
		if err := orm.Raw("SELECT profile FROM v_organizations WHERE id = ?", authInfo.OrgID.Int64).Scan(&filter.ProviderIDs).Error; err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	data, err := dao.GetGroupFilterItem(filter)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, data)
}

func GetCorporationSelfInvoiceList(c *gin.Context) {
	var req models.CorporationSelfInvoiceReq
	if err := c.BindQuery(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 10
	}

	authInfo := middlewares.GetAuthInfo(c)
	resp := models.CorporationSelfInvoiceRes{
		Data:    []models.CorporationSelfInvoice{},
		Success: true,
		Limit:   req.Limit,
		Offset:  req.Offset,
		Total:   0,
	}

	orm := middlewares.GetORMDao(c)
	query := orm.Table("corporation_self_invoices").Where("deleted_at IS NULL ")
	if !authInfo.IsADAdmin() {
		query = query.Where("user_id = ?", authInfo.UserID)
	}
	if req.Query != "" {
		query = query.Where("id::text LIKE ?", "%"+req.Query+"%")
	}

	if err := query.Limit(int(req.Limit)).Offset(int(req.Offset)).Find(&resp.Data).Error; err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	// Presign File URL
	ps := middlewares.GetS3Presigner(c)
	for i := range resp.Data {
	M:
		for j := range resp.Data[i].Files {
			bucket, key, err := utils.UrlToS3BucketAndKey(resp.Data[i].Files[j])
			if err != nil {
				fmt.Println(err)
				continue M
			}
			url, err := ps.PresignUrl(bucket, key, time.Hour*24)
			if err != nil {
				fmt.Println(err)
				continue M
			}
			resp.Data[i].Files[j] = url
		}

	}

	if err := query.Count(&resp.Total).Error; err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, resp)
}

func GetCorporationSelfInvoice(c *gin.Context) {
	var req *models.CorporationSelfInvoice

	authInfo := middlewares.GetAuthInfo(c)

	orm := middlewares.GetORMDao(c)

	query := orm.Table("corporation_self_invoices").
		Where("deleted_at IS NULL ").
		Where("id = ?", c.Param("invoice-id"))

	if !authInfo.IsADAdmin() {
		query = query.Where("user_id = ?", authInfo.UserID)
	}

	if err := query.First(&req).Error; err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if req != nil {
		ps := middlewares.GetS3Presigner(c)
		for i := range req.Files {
			bucket, key, err := utils.UrlToS3BucketAndKey(req.Files[i])
			if err != nil {
				fmt.Println(err)
				continue
			}
			url, err := ps.PresignUrl(bucket, key, time.Hour*24)
			if err != nil {
				fmt.Println(err)
				continue
			}
			req.Files[i] = url
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    req,
	})
}

func CreateCorporationSelfInvoice(c *gin.Context) {
	var req models.CorporationSelfInvoice
	if err := c.BindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	authInfo := middlewares.GetAuthInfo(c)
	req.Status = "created"
	req.UserID = authInfo.UserID

	orm := middlewares.GetORMDao(c)

	if err := orm.Table("corporation_self_invoices").Create(&req).Error; err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    req,
	})
}

func DeleteCorporationSelfInvoice(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)

	orm := middlewares.GetORMDao(c)
	if err := orm.Exec("UPDATE corporation_self_invoices SET deleted_at = NOW() WHERE user_id = ? AND id = ?", authInfo.UserID, c.Param("invoice-id")).Error; err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true})
}
