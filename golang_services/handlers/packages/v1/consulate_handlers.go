package v1

import (
	"fmt"
	"mime"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/file"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

const (
	ApprovalLetterFieldName = "approval-letter"
)

type UpdateLetterRequest struct {
	AppIDs []int  `json:"app_ids"`
	URL    string `json:"url"`
}

func ETSGeneratePresignUrlForLetter(c *gin.Context) {
	var upload UploadRequest
	if err := c.ShouldBindJSON(&upload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	if upload.FileName == "" || upload.ContentType == "" {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
		})
		return
	}

	bucket := middlewares.GetS3Buckets(c)[ApplicationFilesUploadBucketKey].(string)

	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}
	dao := getEtsDao(c)
	o, err := dao.GetServiceOrderByID(id)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order"))
		return
	}
	ownerID := fmt.Sprintf("%d", o.OrgID)

	key := file.BuildETSConsulateInputS3Key(ownerID, o.ID, upload.FileName)

	var mimeType string
	if !strings.HasPrefix(upload.ContentType, ".") {
		mimeType = mime.TypeByExtension("." + upload.ContentType)
	} else {
		mimeType = mime.TypeByExtension(upload.ContentType)
	}

	ps := middlewares.GetS3Presigner(c)
	uploadURLpresigned, err := ps.PresignUploadUrl(bucket, key, mimeType, 15*time.Minute)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	downURLpresigned, err := ps.PresignUrl(bucket, key, expirationForFiles)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"upload_url":   uploadURLpresigned,
			"download_url": downURLpresigned,
		},
	})
}

type ApplicationInfoMsg struct {
	ID             int               `json:"id"`
	State          string            `json:"state"`
	Remark         string            `json:"remark"`
	GivenName      string            `json:"given_name"`
	Surname        string            `json:"surname"`
	PassportNumber string            `json:"passport_number"`
	OutputFiles    *models.FileMap   `json:"output_files,omitempty"`
	OutputPods     *models.InputPods `json:"output_pods"`
}
