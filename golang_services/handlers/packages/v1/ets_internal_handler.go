package v1

import (
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/sjson"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/summary"
)

func CreateFastlaneOrderFromVisaOrder(c *gin.Context) {
	dao := getEtsDao(c)
	orderID := cast.ToInt(c.Param("order-id"))

	q := models.ServiceOrderFilter{
		ID:              []string{cast.ToString(orderID)},
		IncludeTasks:    true,
		IncludeService:  true,
		IncludePrice:    true,
		IncludePayment:  true,
		IncludeShipment: true,
		Limit:           1,
	}

	orderResp, err := dao.QueryServiceOrders(q)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(orderResp.Data) != 1 || orderResp.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	newOrderID, err := cloneETSOrderToFastlaneOrder(dao, orderResp.Data[0])
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	newOrder, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:              []string{cast.ToString(newOrderID)},
		IncludeTasks:    true,
		IncludeService:  true,
		IncludePrice:    true,
		IncludePayment:  true,
		IncludeShipment: true,
		Limit:           1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, newOrder.Data[0])
}

func cloneETSOrderToFastlaneOrder(dao db.IEtsDao, order *models.ServiceOrderDetail) (int, error) {
	oldOrderID := order.ServiceOrder.ID
	inputPodValues := order.Tasks[0].InputPods.ToMapKeyValueV2(order.InputPods)
	order.Status = models.EtsOrderStatusPaid
	order.ID = 0         // Reset  ID
	order.PaymentID = "" // Reset payment ID
	order.QueryPodValues = nil
	order.QueryPodValues, _ = sjson.SetBytes(order.QueryPodValues, "service_core_info_airport", inputPodValues["travel_enter_flight_enter_airport"])
	order.QueryPodValues, _ = sjson.SetBytes(order.QueryPodValues, "service_core_info_country", "VNM")
	order.QueryPodValues, _ = sjson.SetBytes(order.QueryPodValues, "service_core_info_service_date", inputPodValues["travel_enter_flight_enter_timestamp"])
	order.QueryPodValues, _ = sjson.SetBytes(order.QueryPodValues, "service_core_info_service_type", "fastlane")
	order.QueryPodValues, _ = sjson.SetBytes(order.QueryPodValues, "service_core_info_terminal", "international")
	order.Summary = &models.ServiceSummary{
		UnitPrice:           0,
		UnitPriceWithFees:   0,
		ShippingPrice:       0,
		Currency:            "USD",
		Total:               0,
		AdditionalFee:       nil,
		AdditionAppServices: nil,
		Quantity:            len(order.Tasks),
		SubTotal:            0,
		TotalActual:         0,
		TotalActualNote:     `Free Service from Order #` + cast.ToString(oldOrderID),
	}

	assignEmail := ""
	if os.Getenv("ad_env") == "prod" {
		assignEmail = "<EMAIL>"
	} else {
		assignEmail = "<EMAIL>"
	}
	order.ServiceID = 1000005
	airport := map[string]string{
		"Tan Son Nhat International Airport": "SGN",
		"Noi Bai International Airport":      "HAN",
	}[cast.ToString(inputPodValues["travel_enter_flight_enter_airport"])]
	if airport == "SGN" {
		order.ServiceID = 1000005
	} else if airport == "HAN" {
		order.ServiceID = 1000017
	} else {
		return 0, fmt.Errorf("Invalid airport")
	}

	as, err := dao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return 0, err
	}

	assignUser, err := dao.VisaDB().GetUserByEmail(assignEmail)
	if err != nil {
		return 0, err
	}
	order.UserID = assignUser.ID
	priceMap, err := dao.GetServicePrice([]int{order.ServiceID})
	if err != nil {
		return 0, err
	}

	prices := priceMap[order.ServiceID]
	order.ServiceOrder.ProviderID = prices[0].ProviderID
	if err := dao.CreateServiceOrder(order.ServiceOrder); err != nil {
		return 0, err
	}

	if _, err = dao.VisaDB().Db.PSQL.Update("service_orders").
		Set("provider_id", order.ServiceOrder.ProviderID).
		Set("shipment_info", order.ServiceOrder.ShipmentInfo).
		Set("order_time", time.Now()).
		Set("updated_at", time.Now()).
		Where(squirrel.Eq{"id": order.ServiceOrder.ID}).RunWith(dao.VisaDB().Db.Db).Exec(); err != nil {
		return 0, err
	}

	newTask := models.ServiceTask{
		OrderID:      order.ServiceOrder.ID,
		Status:       models.EtsTaskStatusOpen,
		Type:         "arrival",
		OutputFiles:  nil,
		OutputPods:   nil,
		FormCallback: nil,
	}

	inputPods, outputPods := ETSToInputOutputPods(dao, order.QueryPodValues, as[0], nil, false)

	taskInput := getEmptyInputPodsForTask(&(as[0].Schema), (*as[0]).Tasks[0])
	taskInput.Merge(&inputPods)

	newInputPodValues := map[string]any{
		"service_core_info_airport":                 airport,
		"service_core_info_country":                 "VNM",
		"service_core_info_service_type":            "fastlane",
		"service_core_info_task":                    []string{"arrival"},
		"travel_passenger_info_no_of_traveler":      len(order.Tasks),
		"travel_passenger_info_passport_photos":     []string{},
		"travel_passenger_info_passenger_name_list": "",
		"travel_additional_information_note":        "Give the eVisa to customer - only immigration service",
		"travel_passenger_info_welcome_name":        fmt.Sprintf("%s %s", cast.ToString(inputPodValues["passport_core_info_surname"]), cast.ToString(inputPodValues["passport_core_info_given_name"])),
		// "travel_traveler_contact_info_given_name":   cast.ToString(inputPodValues["passport_core_info_given_name"]),
		// "travel_traveler_contact_info_surname":      cast.ToString(inputPodValues["passport_core_info_surname"]),
		"travel_traveler_contact_info_full_name": cast.ToString(inputPodValues["travel_traveler_contact_info_full_name"]),
	}

	podToClones := []string{
		"service_core_info_processing_time",
		"travel_enter_flight_enter_airline",
		"travel_enter_flight_enter_airport",
		"travel_enter_flight_enter_flight",
		"travel_enter_flight_enter_timestamp",
		// "travel_passenger_info_welcome_name",
		// "travel_traveler_contact_info_given_name",
		// "travel_traveler_contact_info_surname",
		"travel_traveler_contact_info_contact_method",
		"travel_traveler_contact_info_phone",
		"travel_traveler_contact_info_social_network_id",
		"travel_traveler_contact_info_social_network_channel",
	}
	for _, key := range podToClones {
		newInputPodValues[key] = inputPodValues[key]
	}
	passportPhotos := []string{}
	travelerNames := []string{}
	for _, task := range order.Tasks {
		taskValues := task.InputPods.ToMapKeyValueV2(order.InputPods)
		passportPhotos = append(passportPhotos, cast.ToString(taskValues["document_copy_of_passport_copy_of_passport_main_page"]))
		travelerNames = append(travelerNames, cast.ToString(taskValues["passport_core_info_given_name"])+" "+cast.ToString(taskValues["passport_core_info_surname"]))
	}
	newInputPodValues["travel_passenger_info_passport_photos"] = passportPhotos
	newInputPodValues["travel_passenger_info_passenger_name_list"] = strings.Join(travelerNames, "\n")

	newTask.InputPods = funk.Values(taskInput).([]*models.InputPod)
	newTask.InputPods.FillByPodValues(newInputPodValues, false)

	taskOutput := getEmptyOutPodsForTask(&(as[0].Schema), (*as[0]).Tasks[0])
	taskOutput.Merge(&outputPods)
	newTask.OutputPods = funk.Values(taskOutput).([]*models.InputPod)

	if err := dao.CreateServiceTask(&newTask); err != nil {
		return 0, err
	}

	newOrders, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:              []string{cast.ToString(order.ServiceOrder.ID)},
		IncludeService:  true,
		IncludeShipment: true,
		IncludePayment:  true,
		IncludeTasks:    true,
		IncludePrice:    true,
		Limit:           1,
	})
	if err != nil {
		return 0, err
	}
	newOrder := newOrders.Data[0]

	if _, err := summary.CalculateGeneralETSSummary(dao, newOrder, models.CartSelection{}, true); err != nil {
		return 0, err
	}

	return order.ServiceOrder.ID, nil
}
