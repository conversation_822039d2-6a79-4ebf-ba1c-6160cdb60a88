package v1

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/localize"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/gin-gonic/gin"
	xlsxt "github.com/ivahaev/go-xlsx-templater"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
	"gopkg.in/guregu/null.v3"
)

func GetReportList(c *gin.Context) {
	var req models.AdminReportReq
	if err := c.BindQuery(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 100
	}

	authInfo := middlewares.GetAuthInfo(c)
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	ps := middlewares.GetS3Presigner(c)
	if authInfo.IsADAdmin() {
		req.OrgID = authInfo.OrgID.Int64
	}

	result, err := dao.GetReportList(req)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	for i := range result.Data {
		{
			b, k, err := utils.UrlToS3BucketAndKey(result.Data[i].ReportFile)
			if err != nil {
				response.HandleResponse(c, nil, err)
				return
			}
			result.Data[i].ReportFile, err = ps.PresignUrl(b, k, expirationForFiles)
			if err != nil {
				response.HandleResponse(c, nil, err)
				return
			}
		}
	}
	c.JSON(http.StatusOK, result)
}

type reportRequest struct {
	From    time.Time `json:"from"`
	To      time.Time `json:"to"`
	Service []string  `json:"service"`
}

func MakeReportByAD(c *gin.Context) {
	req := reportRequest{}
	if err := c.BindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}

	if !funk.ContainsString(authInfo.Roles, "ad_admin") {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "admin can access their org information only",
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	bucket := middlewares.GetS3Buckets(c)
	awsConfig := aws.NewConfig().WithRegion(middlewares.GetAWSRegion(c)).WithLogLevel(aws.LogOff)
	sess, err := session.NewSession(awsConfig)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	localize := middlewares.GetLocalize(c)
	report, err := makeReportAdminV2(dao, sess, bucket, req.Service, req.From, req.To, authInfo, localize)
	if err != nil {
		if err.Error() == "NO_DATA" {
			c.JSON(http.StatusOK, gin.H{
				"success":       false,
				"error_code":    "ERR02000",
				"error_message": "No report data",
			})
			return
		}
		response.HandleResponse(c, nil, err)
		return
	}

	ps := middlewares.GetS3Presigner(c)
	{
		b, k, err := utils.UrlToS3BucketAndKey(report.ReportFile)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		report.ReportFile, err = ps.PresignUrl(b, k, expirationForFiles)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"report_url": report.ReportFile,
	})
}

func makeReportAdminV2(dao db.IDao, sess *session.Session, bucket map[string]any, service []string, from, to time.Time, authInfo *models.AuthInfo, localize *localize.Localize) (*models.AdminReport, error) {
	etsDao := db.NewEtsDao(dao.DB().Db)
	// Get all consulates
	// consulates, err := dao.QueryConsulates(map[string][]any{})
	// if err != nil {
	// 	return nil, err
	// }
	// consulateMap := funk.ToMap(consulates, "ID").(map[string]*models.Consulate)

	// Get all corporations
	corporations, err := dao.GetCorporations(map[string][]any{})
	if err != nil {
		return nil, err
	}
	corporationMap := funk.ToMap(corporations, "OrgID").(map[int64]*models.Corporation)

	// Get all providers
	providers, err := etsDao.GetEtsProviders()
	if err != nil {
		return nil, err
	}
	providerMap := funk.ToMap(providers, "ID").(map[string]*models.EtsProvider)

	// Get all visa orders that payment success
	// Get all ets orders that payment success

	report := models.AdminReport{
		From:          from,
		FromFormatted: from.Format("2006-01-02"),
		To:            to,
		ToFormatted:   to.Format("2006-01-02"),
		Service:       strings.Join(service, ", "),
	}

	report.Data = []models.ReportAdminItem{}

	serviceOrderData, err := etsDao.QueryServiceOrders(models.ServiceOrderFilter{
		IncludeTasks:   true,
		IncludeService: true,
		IncludePayment: true,
		SkipPagination: false,
		ServiceType:    service,
		CreatedAtGte:   from.Format(time.RFC3339),
		CreatedAtLte:   to.Format(time.RFC3339),
		Limit:          1000,
	})
	if err != nil {
		return nil, err
	}

	for _, order := range serviceOrderData.Data {
		if order.DeletedAt != nil {
			continue
		}

		if !order.IsPaid() {
			continue
		}

		if order.Summary == nil || order.PaymentID == "" {
			continue
		}

		provider := providerMap[order.ProviderID]

		agency := "INDIVIDUAL"
		if order.OrgID > 0 {
			agency = corporationMap[int64(order.OrgID)].Name
		}

		service := order.Service
		orderJ := utils.StructToJSON(order)

		appNames := []string{}
		flights := []string{}
		for _, task := range order.Tasks {
			pair := task.InputPods.ToMapKeyValueV2(order.InputPods)
			appNames = append(appNames, task.GetAppName(service.ServiceType))
			if flight := utils.GetStringOrValue(pair["travel_exit_flight_exit_flight"]); flight != "" {
				flights = append(flights, flight)
			}
			if flight := utils.GetStringOrValue(pair["travel_enter_flight_enter_flight"]); flight != "" {
				flights = append(flights, flight)
			}
		}
		reportItem := models.ReportAdminItem{
			ConsulateName:     provider.Name,
			Agency:            agency,
			Country:           service.Country,                                                                                //
			RegionOfResidence: utils.StructToJSON(order.QueryPodValues).Get("service_core_info_region_of_residence").String(), //
			ServiceName:       localize.EN.ServiceType[service.ServiceType],
			ServiceType:       localize.EN.Tasks[strings.Join(service.Tasks, ", ")],
			ProcessingTime:    localize.EN.ProcessingTime[utils.StructToJSON(service.Attributes).Get("processing_time").String()],
			Status:            order.Status,
			Note:              gjson.ParseBytes(order.Note).Get(string(order.Status)).Get("message").String(),
			Tag:               null.StringFromPtr(service.Tag).String,
			OrderNo:           strconv.Itoa(order.ID),
			ApplicationName:   strings.Join(appNames, ", "),
			Quantity:          order.Summary.Quantity,
			ServiceFee:        order.Summary.UnitPrice * float64(order.Summary.Quantity),
			GovernmentFee:     order.Summary.GetAdditionalFee() * float64(order.Summary.Quantity),
			ShippingFee:       order.Summary.ShippingPrice,
			Discount:          order.Summary.Discount,
			CouponCode:        order.Summary.PromotionCode,
			Total:             order.Summary.Total,
			TotalActual:       funk.ShortIf(order.Summary.TotalActual > 0, order.Summary.TotalActual, order.Summary.Total).(float64),
			TotalActualNote:   order.Summary.TotalActualNote,
			OrderDate:         orderJ.Get("order_time").Time().Format("2006-01-02"),
			SubmittedDate:     orderJ.Get("submitted_time").Time().Format("2006-01-02"),
			ApprovedDate:      orderJ.Get("tracking_times.completed").Time().Format("2006-01-02"),
			ServiceDate:       "", //
			CompletedDate:     orderJ.Get("tracking_times.completed").Time().Format("2006-01-02"),
			Currency:          order.Summary.Currency,
			PaymentMethod:     utils.StructToJSON(order.PaymentRaw).Get("method").String(),
			Flight:            strings.Join(flights, ", "),
			Description:       "",
		}

		notes := []string{}
		notes = append(notes, Coalesce(localize.EN.Product[order.Service.Name], order.Service.Name))

		if val := cast.ToString(order.Service.Tag); val != "" {
			notes = append(notes, Coalesce(localize.EN.Tag[val], val))
		}
		attrJ := utils.StructToJSON(order.Service.Attributes)
		if val := attrJ.Get("validity").String(); val != "" {
			notes = append(notes, Coalesce(localize.EN.ProcessingTime[val], val))
		}
		if val := attrJ.Get("number_of_entries").String(); val != "" {
			notes = append(notes, Coalesce(localize.EN.NumberOfEntry[val], val))
		}
		if val := attrJ.Get("airport").String(); val != "" {
			notes = append(notes, Coalesce(localize.EN.Airport[val], val))

		}
		if val := attrJ.Get("terminal").String(); val != "" {
			notes = append(notes, Coalesce(localize.EN.Terminal[val], val))
		}
		reportItem.Description = strings.Join(notes, ", ")

		inputPods := order.Tasks[0].InputPods.ToMapKeyValueV2(order.InputPods)
		if service.ServiceType == "fastlane" {

			reportItem.ServiceDate = cast.ToTime(inputPods["travel_exit_flight_exit_timestamp"]).Format("2006-01-02")
			if order.Tasks[0].Type == "arrival" || order.Tasks[0].Type == "vip_arrival" {
				reportItem.ServiceDate = cast.ToTime(inputPods["travel_enter_flight_enter_timestamp"]).Format("2006-01-02")
			}
			reportItem.Additional = cast.ToString(inputPods["travel_driver_information_park_at_gate"])
		}

		report.Data = append(report.Data, reportItem)
	}

	// TODO: Localize
	for i := range report.Data {
		country, _ := gocountry.FindCountryByAlpha(report.Data[i].Country)
		report.Data[i].Country = country.Name.Common

		country, _ = gocountry.FindCountryByAlpha(report.Data[i].RegionOfResidence)
		report.Data[i].RegionOfResidence = country.Name.Common

		if report.Data[i].OrderDate == "0001-01-01" {
			report.Data[i].OrderDate = ""
		}
		if report.Data[i].SubmittedDate == "0001-01-01" {
			report.Data[i].SubmittedDate = ""
		}
		if report.Data[i].ApprovedDate == "0001-01-01" {
			report.Data[i].ApprovedDate = ""
		}
		if report.Data[i].ServiceDate == "0001-01-01" {
			report.Data[i].ServiceDate = ""
		}
		if report.Data[i].CompletedDate == "0001-01-01" {
			report.Data[i].CompletedDate = ""
		}
	}

	report.Service = strings.Join(service, ", ")
	newReport, err := saveReportAdmin(dao, sess, bucket, report)
	return newReport, err
}

func saveReportAdmin(dao db.IDao, sess *session.Session, bucket map[string]any, report models.AdminReport) (*models.AdminReport, error) {
	buff, err := json.Marshal(report)
	if err != nil {
		return nil, err
	}

	ctx := map[string]any{}
	if err := json.Unmarshal(buff, &ctx); err != nil {
		return nil, err
	}

	doc := xlsxt.New()
	if err := doc.ReadTemplate("ADReport.xlsx"); err != nil {
		return nil, err
	}
	if err := doc.Render(ctx); err != nil {
		return nil, err
	}

	var bb bytes.Buffer
	doc.Write(&bb)

	// Save to local file for debugs
	// os.Remove("report.xlsx")
	// if err := doc.Save("report.xlsx"); err != nil {
	// 	return nil, err
	// }

	reportBucket := bucket["ariadirect_prod_corporation_invoices"].(string)
	reportKey := fmt.Sprintf("Report_%s.xlsx", time.Now().Format("20060102150405"))

	reportURL := reportBucket + "/" + reportKey

	xlsxBuff := bb.Bytes()

	// Save docx to S3
	if _, err := s3.New(sess).PutObject(&s3.PutObjectInput{
		Bucket:        aws.String(reportBucket),
		Key:           aws.String(reportKey),
		ACL:           aws.String("private"),
		Body:          bytes.NewReader(xlsxBuff),
		ContentLength: aws.Int64(int64(len(xlsxBuff))),
		ContentType:   aws.String("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
	}); err != nil {
		return nil, err
	}

	report.ReportFile = reportURL
	if err := dao.DB().Db.Db.QueryRow(`INSERT INTO admin_report
	(report_from, report_to, service, report_file, items)
	VALUES($1, $2, $3, $4 ,$5) RETURNING id`,
		report.From, report.To, report.Service, report.ReportFile, buff).
		Scan(&report.ID); err != nil {
		return nil, err
	}
	return &report, nil
}

// Delete Report
func DeleteReport(c *gin.Context) {
	id, err := utils.GetIntPathParam(c, "id")
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err = dao.DeleteReport(id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
