package v1

import (
	"fmt"
	"math"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

type CrashLogFile struct {
	FileName    string `json:"file_name"`
	FileContent string `json:"file_content"`
}

type CrashLog struct {
	Files  []CrashLogFile `json:"files"`
	Device string         `json:"device"`
}

// Function receipt crash logs from client then send to our email
func SendCrashLog(c *gin.Context) {
	var req CrashLog
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	ps := middlewares.GetS3Presigner(c)
	attachments := []map[string]any{}
	for _, file := range req.Files {
		bucket := middlewares.GetS3Buckets(c)[ApplicationFilesUploadBucketKey].(string)
		key := "crash_logs/" + fmt.Sprintf("%d", time.Now().Unix()) + "/" + file.FileName
		if _, err := ps.UploadFileBuffer(bucket, key, []byte(file.FileContent), "text/plain"); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

		attachments = append(attachments, map[string]any{
			"file_name": file.FileName,
			"bucket":    bucket,
			"key":       key,
		})
	}

	//  Send Feedback Email
	supportEmail := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String()
	message := map[string]any{
		"template_name": "send_crash_log_to_admin",
		"to":            supportEmail,
		"cc":            []string{},
		"bcc":           []string{},
		"parameters": map[string]any{
			"Device": req.Device,
			"Time":   time.Now().Format("2006-01-02 15:04:05"),
		},
		"attachments": attachments,
	}
	if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func GetPackageFeedback(c *gin.Context) {
	var result []models.UserFeedback

	dao := middlewares.GetORMDao(c)
	if err := dao.Order("created_at DESC").Find(&result, "service_type = ? AND order_id = ?", "visa", c.Param("package-id")).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if len(result) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result[0],
	})
}

func CreatePackageFeedback(c *gin.Context) {
	var req models.UserFeedback
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	req.ServiceType = "visa"
	req.OrderID, _ = utils.GetIntPathParam(c, "package-id")

	if req.Rate < 0 || req.Rate > 5 || math.Round(req.Rate*2)/2 != req.Rate {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid rate",
		})
		return
	}

	dao := middlewares.GetORMDao(c)
	if err := dao.Create(&req).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	//  Send Feedback Email
	webBaseURL := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("web_base_url").String()
	supportEmail := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String()

	message := map[string]any{
		"template_name": "notify_feedback_to_ad_provider",
		"to":            supportEmail,
		"cc":            []string{},
		"bcc":           []string{},
		"parameters": map[string]any{
			"OrderID":  req.OrderID,
			"FullName": "Admin",
			"URL":      fmt.Sprintf("%s/dashboard/customer-orders/detail?order_id=%d&service=visa", webBaseURL, req.OrderID),
		},
		"attachments": []map[string]any{},
	}
	if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    req,
	})
}

func GetETSFeedback(c *gin.Context) {
	var result []models.UserFeedback

	dao := middlewares.GetORMDao(c)
	if err := dao.Order("created_at DESC").Find(&result, "service_type != ? AND order_id = ?", "visa", c.Param("order-id")).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if len(result) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result[0],
	})
}

func CreateETSFeedback(c *gin.Context) {
	var req models.UserFeedback
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	req.OrderID, _ = utils.GetIntPathParam(c, "order-id")
	if req.ServiceType == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid service type",
		})
		return
	}

	if req.Rate < 0 || req.Rate > 5 || math.Round(req.Rate*2)/2 != req.Rate {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid rate",
		})
		return
	}

	dao := middlewares.GetORMDao(c)
	if err := dao.Create(&req).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	//  Send Feedback Email
	webBaseURL := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("web_base_url").String()
	supportEmail := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String()

	message := map[string]any{
		"template_name": "notify_feedback_to_ad_provider",
		"to":            supportEmail,
		"cc":            []string{},
		"bcc":           []string{},
		"parameters": map[string]any{
			"OrderID":  req.OrderID,
			"FullName": "Admin",
			"URL":      fmt.Sprintf("%s/dashboard/customer-orders/detail?order_id=%d&service=ets", webBaseURL, req.OrderID),
		},
		"attachments": []map[string]any{},
	}
	if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    req,
	})
}
