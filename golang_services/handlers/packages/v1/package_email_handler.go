package v1

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

type sendInDirectPaymentRequest struct {
	Email  string `json:"email"`
	Method string `json:"method"`
	Total  uint64 `json:"total"`
}

func SendInDirectPayment(c *gin.Context) {
	var req sendInDirectPaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	authInfo := middlewares.GetAuthInfo(c)
	dao := middlewares.GetORMDao(c)

	user := &models.User{}
	if err := dao.Where("email = ?", authInfo.Email).First(&user).Error; err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	webBaseURL := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("web_base_url").String()

	// Send to user
	message := map[string]any{
		"template_name": "send_payment_link_to_user",
		"to":            req.Email,
		"bcc":           []string{utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String()},
		"parameters": map[string]any{
			"CartNo":   c.Param("cart-id"),
			"Amount":   req.Total,
			"Currency": "USD",
			"Email":    authInfo.Email,
			"FullName": strings.Join([]string{user.GivenName, user.Surname}, " "),
			"URL":      fmt.Sprintf("%s/redirect-payment?cart_id=%s&method=%s&total=%d", webBaseURL, c.Param("cart-id"), req.Method, req.Total),
			"Method":   req.Method,
		},
	}

	if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    nil,
	})
}
