package v1

import (
	"path"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

const (
	MANAGER = "manager"
)

func UploadFileUrlsToS3Path(files []string) []string {
	if len(files) == 0 {
		return []string{}
	}
	res := make([]string, len(files))
	for i := range files {
		f := files[i]
		if f == "" {
			res[i] = f
			continue
		}
		bucket, key, err := utils.UrlToS3BucketAndKey(f)
		if err != nil {
			log.Warn().Fields(map[string]any{
				"raw_url": f,
				"error":   err.<PERSON>rror(),
			}).Msg("cannot convert url to s3 path")
			res[i] = f
		} else {
			res[i] = path.Join(bucket, key)
		}
	}
	return res
}

func InputMapUrlsToS3Path(m map[string]string) map[string]string {
	res := map[string]string{}
	for k, v := range m {
		bucket, key, err := utils.UrlToS3BucketAndKey(v)
		if err != nil {
			log.Warn().Fields(map[string]any{
				"raw_url": v,
				"error":   err.Error(),
			}).Msg("cannot convert url to s3 path")
			res[k] = v
		} else {
			res[k] = path.Join(bucket, key)
		}
	}
	return res
}

func PresignAllFilesInMap(ps *aws.S3Svc, m *models.FileMap, expire time.Duration) {
	if m == nil {
		return
	}
	for k, v := range *m {
		if s, ok := v.(string); ok {
			bucket, key, err := utils.UrlToS3BucketAndKey(s)
			if err != nil {
				log.Warn().Fields(map[string]any{
					"file_path": s,
					"error":     err.Error(),
				}).Msg("cannot convert url to s3 bucket and key")
				continue
			}
			presigned, err := ps.PresignUrl(bucket, key, expire)
			if err != nil {
				log.Warn().Fields(map[string]any{
					"error":  err.Error(),
					"bucket": bucket,
					"key":    key,
				}).Msg("failed to presign")
				continue
			}
			(*m)[k] = presigned
		}
		if ss, ok := v.([]any); ok {
			newSS := []string{}
			for _, s := range ss {
				bucket, key, err := utils.UrlToS3BucketAndKey(s.(string))
				if err != nil {
					log.Warn().Fields(map[string]any{
						"file_path": s,
						"error":     err.Error(),
					}).Msg("cannot convert url to s3 bucket and key")
					continue
				}
				presigned, err := ps.PresignUrl(bucket, key, expire)
				if err != nil {
					log.Warn().Fields(map[string]any{
						"error":  err.Error(),
						"bucket": bucket,
						"key":    key,
					}).Msg("failed to presign")
					continue
				}
				newSS = append(newSS, presigned)

			}
			(*m)[k] = newSS
		}
	}
}

func PresignFilesList(ps *aws.S3Svc, l []string, expire time.Duration) []string {
	res := make([]string, len(l))
	for i := range l {
		s := l[i]
		bucket, key, err := utils.UrlToS3BucketAndKey(s)
		if err != nil {
			log.Warn().Fields(map[string]any{
				"file_path": s,
				"error":     err.Error(),
			}).Msg("cannot convert url to s3 bucket and key")
			continue
		}
		presigned, err := ps.PresignUrl(bucket, key, expire)
		if err != nil {
			log.Warn().Fields(map[string]any{
				"error":  err.Error(),
				"bucket": bucket,
				"key":    key,
			}).Msg("failed to presign")
			continue
		}
		res[i] = presigned
	}
	return res
}

func PresignS3Object(ps *aws.S3Svc, u string, expire time.Duration) (string, error) {
	bucket, key, err := utils.UrlToS3BucketAndKey(u)
	if err != nil {
		return u, err
	}
	presigned, err := ps.PresignUrl(bucket, key, expire)
	if err != nil {
		return u, err
	}
	return presigned, nil
}

func GetPackageConfigQuery(c *gin.Context, queryMap map[string][]any) {
	for k, q := range c.Request.URL.Query() {
		if strings.HasPrefix(k, "config.") {
			for _, val := range q {
				queryMap[k] = append(queryMap[k], strings.ToUpper(val))
			}
		}
	}
}

func IfUserIsManager(userID string, dao db.IDao) (bool, error) {
	teamRole, err := dao.GetTeamRoleByUserID(userID)
	if err != nil {
		return false, err
	}
	if teamRole == MANAGER {
		return true, nil
	}
	return false, nil
}
