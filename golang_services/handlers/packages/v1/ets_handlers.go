package v1

import (
	"archive/zip"
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"image"
	"image/png"
	"io"
	"io/ioutil"
	"math"
	"mime"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares/ets_mw"
	task_validation "bitbucket.org/persistence17/aria/golang_services/sdk/ad_validation/task"
	"bitbucket.org/persistence17/aria/golang_services/sdk/crypto"
	"bitbucket.org/persistence17/aria/golang_services/sdk/notification"
	"bitbucket.org/persistence17/aria/golang_services/sdk/product"
	"bitbucket.org/persistence17/aria/golang_services/sdk/summary"
	"bitbucket.org/persistence17/aria/golang_services/sdk/time_util"
	"bitbucket.org/persistence17/aria/golang_services/sdk/zalo"
	"golang.org/x/image/draw"
	"gopkg.in/guregu/null.v3"
	"gorm.io/datatypes"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/boombuler/barcode/code128"
	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"github.com/metal3d/go-slugify"
	"github.com/pariz/gountries"
	"github.com/rs/zerolog/log"
	"github.com/samber/lo"
	uuid "github.com/satori/go.uuid"
	"github.com/signintech/gopdf"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"github.com/vmihailenco/msgpack/v5"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/applications"
	awsSdk "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/file"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/davegardnerisme/phonegeocode"
)

// country => service_core_info_country
// region_of_residence=> service_core_info_region_of_residence,
// entry_date=>service_core_info_entry_date
// exit_date=>service_core_info_exit_date
// nationality=>service_core_info_nationality
// airport=>service_core_info_airport

type availableEtsQuery struct {
	ServiceType       string     `form:"service_type"`
	Country           string     `form:"service_core_info_country"`
	Airport           []string   `form:"service_core_info_airport"`
	Type              []string   `form:"type"`
	Tag               []string   `form:"tag"`
	Tasks             []string   `form:"service_core_info_tasks"`
	Nationality       string     `form:"service_core_info_nationality"`
	RegionOfResidence string     `form:"service_core_info_region_of_residence"`
	ID                []int      `form:"id"`
	ServiceDate       *time.Time `form:"service_core_info_service_date"`
	UserAge           []string   `form:"user_age"`
	Condition         []string   `form:"condition"`
	Terminal          string     `form:"service_core_info_terminal"`
	NumberOfEntries   string     `form:"number_of_entries"`
	EntryDate         *time.Time `form:"service_core_info_entry_date"`
	ExitDate          *time.Time `form:"service_core_info_exit_date"`
	Offset            uint       `form:"offset"`
	Limit             uint       `form:"limit"`
}

func GetEtsQuery(c *gin.Context) {
	serviceName, err := utils.GetStringPathParam(c, "service_name")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	dao := getEtsDao(c)
	q := map[string]any{
		"service": serviceName,
	}
	res, err := dao.QueryEtsQueryPod(q)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    res,
	})
}

func GetServiceType(c *gin.Context) {
	dao := getEtsDao(c)
	res, err := dao.GetServiceType()
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	webBase := gjson.Parse(os.Getenv("ad_endpoint")).Get("web_base_url").String()

	mapServiceType := map[string]models.ServiceType{}
	for _, item := range res {
		mapServiceType[item.ServiceType] = item
	}

	result := []models.ServiceType{}
	index := 1

	result = append(result, models.ServiceType{
		ServiceType: models.EtsServiceTypeNewVisa,
		HaveQuery:   true,
		Order:       index,
	})
	index++

	if _, ok := mapServiceType[models.EtsServiceTypeNewVisaUrgent]; ok {
		result = append(result, models.ServiceType{
			ServiceType: models.EtsServiceTypeNewVisaUrgent,
			HaveQuery:   false,
			Order:       index,
			URL:         fmt.Sprintf("%s/visa-urgent", webBase),
		})
		index++
	}

	if _, ok := mapServiceType[models.EtsServiceTypePassport]; ok {
		result = append(result, models.ServiceType{
			ServiceType: models.EtsServiceTypePassport,
			HaveQuery:   true,
			Order:       index,
		})
	}
	index++
	if _, ok := mapServiceType[models.EtsServiceTypeFastlane]; ok {
		result = append(result, models.ServiceType{
			ServiceType: models.EtsServiceTypeFastlane,
			HaveQuery:   false,
			URL:         fmt.Sprintf("%s/airport-services", webBase),
			Order:       index,
		})
	}

	index++
	if _, ok := mapServiceType[models.EtsServiceTypeCountryTourist]; ok {
		result = append(result, models.ServiceType{
			ServiceType: models.EtsServiceTypeCountryTourist,
			HaveQuery:   true,
			Order:       index,
		})
	}
	index++

	if _, ok := mapServiceType[models.EtsServiceTypeAirportEntry]; ok {
		result = append(result, models.ServiceType{
			ServiceType: models.EtsServiceTypeAirportEntry,
			HaveQuery:   true,
			Order:       index,
		})
	}
	index++

	ttp := []models.ServiceType{}
	if _, ok := mapServiceType[models.EtsServiceTypeGlobalEntry]; ok {
		ttp = append(ttp, models.ServiceType{
			HaveQuery:   true,
			Order:       1,
			ServiceType: models.EtsServiceTypeGlobalEntry,
		})
	}

	if _, ok := mapServiceType[models.EtsServiceTypeTSAPrecheck]; ok {
		ttp = append(ttp, models.ServiceType{
			HaveQuery:   true,
			Order:       2,
			ServiceType: models.EtsServiceTypeTSAPrecheck,
		})
	}

	if _, ok := mapServiceType[models.EtsServiceTypeSentri]; ok {
		ttp = append(ttp, models.ServiceType{
			HaveQuery:   true,
			Order:       3,
			ServiceType: models.EtsServiceTypeSentri,
		})
	}

	if _, ok := mapServiceType[models.EtsServiceTypeNexus]; ok {
		ttp = append(ttp, models.ServiceType{
			HaveQuery:   true,
			Order:       4,
			ServiceType: models.EtsServiceTypeNexus,
		})
	}

	if _, ok := mapServiceType[models.EtsServiceTypeFastTruck]; ok {
		ttp = append(ttp, models.ServiceType{
			HaveQuery:   true,
			Order:       5,
			ServiceType: models.EtsServiceTypeFastTruck,
		})
	}

	result = append(result, models.ServiceType{
		ServiceType: "ttp",
		HaveQuery:   false,
		Order:       index,
		Children:    ttp,
	})

	if _, ok := mapServiceType[models.EtsServiceTypeIDPhoto]; ok {
		result = append(result, models.ServiceType{
			ServiceType: models.EtsServiceTypeIDPhoto,
			HaveQuery:   false,
			Order:       6,
		})
	}

	if _, ok := mapServiceType[models.EtsServiceTypeSingleCertificate]; ok {
		result = append(result, models.ServiceType{
			ServiceType: models.EtsServiceTypeSingleCertificate,
			HaveQuery:   true,
			Order:       7,
		})
	}

	sort.Slice(result, func(i, j int) bool {
		return result[i].Order < result[j].Order
	})
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func getTimeWithConfigLocation(input *time.Time, config *models.WorkingTimeConfig) *time.Time {
	location, _ := time.LoadLocation(config.Location)
	if input == nil {
		return nil
	}

	if config.QueryTimeSetting == "start_of_day" {
		return aws.Time(time.Date(input.Year(), input.Month(), input.Day(), 0, 0, 0, 0, location))
	}

	if config.QueryTimeSetting == "end_of_day" {
		return aws.Time(time.Date(input.Year(), input.Month(), input.Day(), 23, 59, 59, 999, location))
	}
	// current_time or empty
	return aws.Time(time.Date(input.Year(), input.Month(), input.Day(), input.Hour(), input.Minute(), input.Second(), input.Nanosecond(), location))

}
func GetAvailableEts(c *gin.Context) {
	var q availableEtsQuery
	if err := c.ShouldBindQuery(&q); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	query := map[string]any{
		"status": "active",
	}
	if len(q.ServiceType) != 0 {
		query["service_type"] = q.ServiceType
	}
	if len(q.Country) != 0 {
		query["country"] = q.Country
	}
	if len(q.Nationality) != 0 {
		query["nationality"] = q.Nationality
	}
	if len(q.Type) != 0 {
		query["service_type"] = q.Type
	}
	if len(q.RegionOfResidence) != 0 {
		query["region_of_residence"] = q.RegionOfResidence
	}
	if len(q.Airport) != 0 {
		query["airport"] = q.Airport
	}
	if len(q.Tag) != 0 {
		query["tag"] = q.Tag
	}
	if len(q.Tasks) != 0 {
		query["tasks"] = q.Tasks
	}
	if len(q.ID) != 0 {
		query["id"] = q.ID
	}
	if len(q.UserAge) != 0 {
		query["user_age"] = q.UserAge
	}
	if len(q.Condition) != 0 {
		query["condition"] = q.Condition
	}
	if q.Terminal != "" {
		query["terminal"] = q.Terminal
	}
	if q.NumberOfEntries != "" {
		query["number_of_entries"] = q.NumberOfEntries
	}
	dao := getEtsDao(c)
	authInfo := middlewares.GetAuthInfo(c)
	var orgID int64 = 0
	if authInfo != nil {
		orgID = authInfo.OrgID.Int64
	}

	res, err := queryEts(dao, orgID, query, q.Offset, q.Limit)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	// Filter by check processing time
	result := []*models.ExtendedTravelService{}

	for i, ets := range res {
		skip := false

		if q.ServiceDate != nil {
			minEntryDate, config, err := dao.VisaDB().CalculateServiceProcessingTime(ets.ID)
			if minEntryDate == nil || err != nil {
				skip = true
			} else {
				serviceDate := getTimeWithConfigLocation(q.ServiceDate, config)
				if minEntryDate.After(*serviceDate) {
					skip = true
				}
			}
			fmt.Println(minEntryDate)

		}
		if q.EntryDate != nil {
			minEntryDate, config, err := dao.VisaDB().CalculateServiceProcessingTime(ets.ID)
			fmt.Println(minEntryDate)
			if minEntryDate == nil || err != nil {
				skip = true
			} else {
				serviceDate := getTimeWithConfigLocation(q.EntryDate, config)
				if minEntryDate.After(*serviceDate) {
					skip = true
				}
			}
			ets.WorkingTimeConfig = config
			ets.DebugData = minEntryDate

		}

		if q.EntryDate != nil && q.ExitDate != nil {
			// entryDateEndOfDay := time.Date((*q.EntryDate).Year(), (*q.EntryDate).Month(), (*q.EntryDate).Day(), 23, 59, 59, 0, time.UTC)
			// minEntryDate, err := dao.VisaDB().CalculateServiceProcessingTime(ets.ID, entryDateEndOfDay)
			// if minEntryDate == nil || err != nil {
			// 	skip = true
			// } else {
			// 	if minEntryDate.Before(*q.ServiceDate) {
			// 		skip = true
			// 	}
			// }
			// if ok, err := res[i].IsEnoughTransitTime(entryDateEndOfDay, *q.ExitDate); err == nil && !ok {
			// 	skip = true
			// }

			if ok, err := res[i].IsEnoughValidity(*q.EntryDate, *q.ExitDate); err == nil && !ok {
				skip = true
			}
		}

		if !skip {
			result = append(result, res[i])
		}
	}

	sort.Slice(result, func(i, j int) bool { return result[i].Price.SubTotal < result[j].Price.SubTotal })
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func GetCommonETSProduct(c *gin.Context) {
	var q availableEtsQuery
	if err := c.ShouldBindQuery(&q); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	query := map[string]any{
		"status": "active",
	}

	if q.Limit <= 0 {
		q.Limit = 10
	}
	authInfo := middlewares.GetAuthInfo(c)
	dao := getEtsDao(c)
	res, err := queryEts(dao, authInfo.OrgID.Int64, query, 0, 0)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	etsMap := map[int]*models.ExtendedTravelService{}
	for _, ets := range res {
		etsMap[ets.ID] = ets
	}

	rows, err := dao.VisaDB().ORM().Raw(`
		SELECT user_id, service_id, count(service_id) count_ets
		FROM service_orders WHERE status != 'cancelled'  AND user_id = ?
		GROUP BY user_id, service_id ORDER BY count_ets DESC
	`, authInfo.UserID).Rows()
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	result := []*models.ExtendedTravelService{}
	for rows.Next() {
		var commonProductID int
		if err := rows.Scan(aws.String(""), &commonProductID, aws.String("")); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		if val, ok := etsMap[commonProductID]; ok {
			result = append(result, val)
		}
	}

	if len(result) >= 5 {
		response.HandleResponse(c, result[0:5], nil)
	} else {
		response.HandleResponse(c, result, nil)
	}

}

func GetETSByID(c *gin.Context) {
	dao := getEtsDao(c)
	res, err := dao.QueryExtendedTravelServices(map[string]any{"id": c.Param("service-id")}, 0, 1)
	if err != nil {
		response.HandleResponse(c, res, nil)
		return
	}

	if len(res) == 0 {
		response.HandleResponse(c, nil, errors.NotFoundErr)
		return
	}

	priceMap, err := dao.GetServicePrice([]int{res[0].ID})
	if err != nil {
		response.HandleResponse(c, res, nil)
		return
	}

	if price, ok := priceMap[res[0].ID]; ok && len(price) > 0 {
		res[0].Price = price[0].GetCalculatePrice()
	}

	response.HandleResponse(c, res[0], nil)

}

func queryEts(dao db.IEtsDao, orgID int64, q map[string]any, offset, limit uint) ([]*models.ExtendedTravelService, error) {
	res, err := dao.QueryExtendedTravelServices(q, offset, limit)
	if err != nil {
		return nil, err
	}
	var ids = make([]int, len(res))
	for i := range res {
		ids[i] = res[i].ID
	}
	priceMap, err := dao.GetServicePrice(ids)
	if err != nil {
		return nil, err
	}

	result := []*models.ExtendedTravelService{}
R:
	for i := range res {
		p := priceMap[res[i].ID]
		if len(p) == 0 {
			return nil, fmt.Errorf("service without price %d", res[i].ID)
		}
		res[i].Price = p[0].GetCalculatePrice()

		limitOrgs := []int64{}
		for _, limitOrgID := range utils.StructToJSON(res[i].Attributes).Get("limitations.orgs").Array() {
			limitOrgs = append(limitOrgs, limitOrgID.Int())
		}
		if len(limitOrgs) > 0 {
			if !lo.SomeBy(limitOrgs, func(limitOrgID int64) bool {
				return limitOrgID == orgID
			}) {
				continue R
			}
		}
		result = append(result, res[i])

	}
	return result, nil
}

func ListEtsOrders(c *gin.Context) {
	var req models.ServiceOrderFilter
	if err := c.ShouldBindQuery(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if req.Limit <= 0 {
		req.Limit = 10
	}

	auth := middlewares.GetAuthInfo(c)

	if !utils.Contain("ad_admin", auth.Roles) {
		if auth.OrgID.Valid {
			req.OrgID = auth.OrgID.Int64
		}

		req.UserID = auth.UserID // admin and user can only see their created order
	} else {
		req.IncludeOutputFileBeforePaid = false
		req.IncludeProvider = true
	}

	dao := getEtsDao(c)

	req.IncludeTasks = false
	req.IncludePayment = true
	req.IncludeCreator = true
	if funk.ContainsString(req.ServiceType, "fastlane") {
		req.IncludeTasks = true
	}

	req.ADAdmin = utils.Contain("ad_admin", auth.Roles)

	res, err := dao.QueryServiceOrders(req)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, res)
}

func GetEtsOrder(c *gin.Context) {
	id := c.Param("order-id")
	auth := middlewares.GetAuthInfo(c)

	dao := getEtsDao(c)

	q := models.ServiceOrderFilter{
		ID:              []string{id},
		IncludeTasks:    true,
		IncludeService:  true,
		IncludePrice:    true,
		IncludePayment:  true,
		IncludeShipment: true,
		Limit:           1,
	}

	if utils.Contain("ad_admin", auth.Roles) {
		q.IncludeOutputFileBeforePaid = true
		q.IncludeCreator = true
		q.IncludeProvider = true
	}

	existing, err := dao.QueryServiceOrders(q)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	staffs, err := dao.GetStaffsByOrderID(int64(existing.Data[0].ID))
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	isStaff := false
	for _, staff := range staffs {
		if staff.StaffID == auth.UserID {
			isStaff = true
			break
		}
	}

	if (auth.IsUser() || auth.IsGuest()) && (existing.Data[0].UserID != auth.UserID && !isStaff) {
		response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("you don't have permission"))
		return
	}

	existing.Data[0].InputPodValues = existing.Data[0].InputPods.ToMapKeyValue()

	if existing.Data[0].IsPaid() {
		for _, task := range existing.Data[0].Tasks {
			inputPodPair := task.InputPods.ToMapKeyValue()
			// outputPodPair := task.OutputPods.ToMapKeyValue()
			if val := inputPodPair["photo_passport_photo_copy_of_photo"]; val != nil {
				task.InputPods.SetFEValue("photo_passport_photo_copy_of_photo", strings.ReplaceAll(cast.ToString(val), "-demo", ""))
			}
			// if val := outputPodPair["application_application_info_copy_of_photo_full"]; val != nil {
			// 	task.OutputPods.SetFEValue("application_application_info_copy_of_photo_full", strings.ReplaceAll(cast.ToString(val), "-demo", ""))
			// }
			task.InputPodValues = task.InputPods.ToMapKeyValueV2(existing.Data[0].InputPods)
			task.OutPodValues = task.OutputPods.ToMapKeyValue()

		}
	}

	ps := middlewares.GetS3Presigner(c)
	for i := 0; i < len(existing.Data[0].Tasks); i++ {
		presignTask(ps, existing.Data[0].ServiceOrder, existing.Data[0].Tasks[i])

	}
	PresignAllFilesInMap(ps, existing.Data[0].OutputFiles, expirationForFiles)
	if err := presignETSPaymentReceiptURL(ps, existing.Data[0]); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	// Auto calculate summary for open order
	// if existing.Data[0].Status == models.EtsOrderStatusOpen {
	// 	serviceSummary, err := calculateETSServiceSummary(dao, existing.Data[0], "", true)
	// 	if err != nil {
	// 		response.HandleResponse(c, nil, err)
	// 		return
	// 	}
	// 	existing.Data[0].Summary = serviceSummary
	// }

	response.HandleResponse(c, existing.Data[0], nil)
}

func GetInternalEtsOrder(c *gin.Context) {
	id := c.Param("order-id")
	dao := getEtsDao(c)

	q := models.ServiceOrderFilter{
		ID:                          []string{id},
		IncludeTasks:                true,
		IncludeService:              true,
		IncludePrice:                true,
		IncludePayment:              true,
		IncludeShipment:             true,
		IncludeOutputFileBeforePaid: true,
		IncludeProvider:             true,
		Limit:                       1,
	}

	existing, err := dao.QueryServiceOrders(q)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}
	existing.Data[0].InputPodValues = existing.Data[0].InputPods.ToMapKeyValue()

	ps := middlewares.GetS3Presigner(c)
	for i := 0; i < len(existing.Data[0].Tasks); i++ {
		presignTask(ps, existing.Data[0].ServiceOrder, existing.Data[0].Tasks[i])

	}
	PresignAllFilesInMap(ps, existing.Data[0].OutputFiles, expirationForFiles)
	if err := presignETSPaymentReceiptURL(ps, existing.Data[0]); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	response.HandleResponse(c, existing.Data[0], nil)
}

func presignETSPaymentReceiptURL(ps *awsSdk.S3Svc, s *models.ServiceOrderDetail) error {
	if s.PaymentRaw == nil {
		return nil
	}
	receiptFile := gjson.ParseBytes(*s.PaymentRaw).Get("receipt_file").String()

	if receiptFile != "" {
		bucket, key := utils.GetBucketAndKeyFromS3Path(receiptFile)
		url, err := ps.PresignUrl(bucket, key, 24*time.Hour)
		if err != nil {
			return err
		}
		*s.PaymentRaw, _ = sjson.SetBytes(*s.PaymentRaw, "receipt_file", url)
	}
	return nil
}

type createETSOrderReq struct {
	RefIDs            []string       `json:"ref_ids"`
	QueryPodValues    datatypes.JSON `json:"query_pod_values"`
	ServiceID         int            `json:"service_id"`
	UseMyProfile      bool           `json:"use_my_profile"`
	WithoutNewTask    bool           `json:"without_new_task"`
	RegionOfResidence string         `json:"region_of_residence"`
}

func CreateEtsOrder(c *gin.Context) {
	var req createETSOrderReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	auth := middlewares.GetAuthInfo(c)
	var o models.ServiceOrder
	o.ServiceID, o.UserID, o.Status, o.OrgID = req.ServiceID, auth.UserID, models.EtsOrderStatusOpen, int(auth.OrgID.Int64)
	o.QueryPodValues = req.QueryPodValues
	if o.ServiceID == 0 && len(req.RefIDs) > 0 {
		buff, _ := base64.StdEncoding.DecodeString(req.RefIDs[0])
		var unpackedMsg string
		msgpack.Unmarshal(buff, &unpackedMsg)
		fmt.Println(unpackedMsg)
		unpackedJ := gjson.Parse(unpackedMsg)
		o.ServiceID = int(unpackedJ.Get("service_id").Int())
	}
	if o.ServiceID == 0 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("service_id must be provided"))
		return
	}
	dao := getEtsDao(c)
	created, err := createEtsOrder(dao, &o, auth, req)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	response.HandleResponse(c, created, nil)
}

type UpdatePassportReq struct {
	Form string `json:"form"`
	URL  string `json:"url"`
	ID   int    `json:"id"`
}

func UpdateOutputFilesEtsOrder(c *gin.Context) {
	var req UpdatePassportReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}
	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}
	auth := middlewares.GetAuthInfo(c)
	if !utils.Contain("ad_admin", auth.Roles) {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("can't upload output files"))
		return
	}
	dao := getEtsDao(c)
	bucket, key, err := utils.UrlToS3BucketAndKey(req.URL)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid URL"))
		return
	}
	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:           []string{strconv.Itoa(id)},
		IncludeTasks: true,
		Limit:        1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}
	m := map[string]string{req.Form: path.Join(bucket, key)}
	if err := dao.AddETSOutputFiles(id, m); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func AddOutputFileToEtsOrder(c *gin.Context) {
	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}

	outputFiles := map[string]string{}
	if err = c.ShouldBindJSON(&outputFiles); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	dao := getEtsDao(c)

	if err := dao.AddETSOutputFiles(id, outputFiles); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	response.HandleResponse(c, nil, err)
}
func UpdateEtsOrder(c *gin.Context) {
	var o models.ServiceOrder
	if err := c.ShouldBindJSON(&o); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}
	o.ID = id

	dao := getEtsDao(c)

	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:             []string{strconv.Itoa(id)},
		IncludeTasks:   true,
		IncludeService: true,
		Limit:          1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	auth := middlewares.GetAuthInfo(c)
	staffs, err := dao.GetStaffsByOrderID(int64(existing.Data[0].ID))
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	isStaff := false
	for _, staff := range staffs {
		if staff.StaffID == auth.UserID {
			isStaff = true
			break
		}
	}

	if (auth.IsUser() || auth.IsGuest()) && (existing.Data[0].UserID != auth.UserID && !isStaff) {
		response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("you don't have permission"))
		return
	}

	res, err := updateEtsOrder(c, dao, auth, existing.Data[0], &o)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	response.HandleResponse(c, res, nil)
}

func updateEtsOrder(c *gin.Context, dao db.IEtsDao, auth *models.AuthInfo, exist *models.ServiceOrderDetail, update *models.ServiceOrder) (*models.ServiceOrderDetail, error) {
	u := map[string]any{}
	if update.QueryPodValues != nil {
		u["query_pod_values"] = update.QueryPodValues
	}
	if update.Config != nil {
		u["config"] = update.Config
	}
	if update.Note != nil {
		u["note"] = update.Note
	}

	if update.InputPods != nil {
		updateKV := update.InputPods.ToMapKeyValue()

		if updateKV["travel_enter_flight_enter_timestamp"] != nil {
			updateKV["service_core_info_entry_date"] = updateKV["travel_enter_flight_enter_timestamp"]
		}
		exist.InputPods.FillByPodValues(updateKV, false)
		u["input_pods"] = exist.InputPods
		u["input_pod_values"] = exist.InputPods.ToMapKeyValue()
		for _, task := range exist.Tasks {
			task.InputPods.FillByPodValues(update.InputPods.ToMapKeyValue(), false)
			task.InputPodValues = task.InputPods.ToMapKeyValue()
			if err := dao.UpdateServiceTask(map[string]any{
				"input_pods":       task.InputPods,
				"input_pod_values": task.InputPodValues}, task.ID); err != nil {
				return nil, err
			}
		}
	}

	if update.Status != "" {
		// AD admin or admin can update status
		// https://traversal17.atlassian.net/browse/AD-5534
		now := time.Now()
		u["status"] = update.Status
		if exist.TrackingTimes == nil {
			exist.TrackingTimes = &models.PropertyMap{}
		}
		(*exist.TrackingTimes)[update.Status] = now
		u["tracking_times"] = exist.TrackingTimes

		if funk.ContainsString([]string{models.EtsOrderStatusSubmitted, models.EtsOrderStatusCompleted}, update.Status) {
			update.SubmittedTime = &now
		}
	}
	if err := dao.UpdateServiceOrder(u, update.ID); err != nil {
		return nil, err
	}

	// Update all task to cancelled when order cancelled
	if update.Status == models.EtsOrderStatusCancelled {
		if err := dao.UpdateServiceTaskStatusByOrderID(models.EtsTaskStatusCancelled, int64(exist.ID)); err != nil {
			return nil, err
		}
		if exist.IsPaid() {
			if err := notification.NotifyCancelETSToProvider(c, exist, gjson.ParseBytes(update.Note).Get("cancelled.0.message").String()); err != nil {
				response.HandleResponse(c, nil, err)
				return nil, err
			}
			if funk.ContainsString(notification.URGENT_PRODUCT_NAMES, exist.Service.Name) {
				if err := notification.NotifyViaZaloForCancelUrgentVisa(dao, int64(exist.ID), []int64{}); err != nil {
					return nil, err
				}
			}
		}

	}

	// Update all task to done when order completed
	if update.Status == models.EtsOrderStatusCompleted {
		if err := dao.UpdateServiceTaskStatusByOrderID(models.EtsTaskStatusApproved, int64(exist.ID)); err != nil {
			return nil, err
		}

		if err := notification.NotifyCompletedOrderETSToProvider(c, exist); err != nil {
			response.HandleResponse(c, nil, err)
			return nil, err
		}

		if !exist.IsSendFeedbackToUser {
			if err := dao.UpdateServiceOrder(map[string]any{"is_send_feedback_to_user": true}, update.ID); err != nil {
				return nil, err
			}

			if err := notification.NotifyFeedbackToUser(update.ID); err != nil {
				return nil, err
			}
		}
	}

	findData, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:           []string{strconv.Itoa(update.ID)},
		IncludeTasks: true,
		IncludePrice: true,
		Limit:        1,
	})
	if err != nil {
		return nil, err
	}

	if len(findData.Data) != 1 {
		return nil, errors.NotFoundErr
	}
	return findData.Data[0], nil
}

func createEtsOrder(dao db.IEtsDao, o *models.ServiceOrder, authInfo *models.AuthInfo, req createETSOrderReq) (*models.ServiceOrderDetail, error) {
	as, err := dao.QueryExtendedTravelServices(map[string]any{"id": o.ServiceID}, 0, 1)
	if err != nil {
		return nil, err
	}
	if len(as) == 0 {
		return nil, errors.InvalidEtsIDErr
	}

	if as[0].Tasks == nil || len((*as[0]).Tasks) == 0 {
		return nil, fmt.Errorf("service does not contain any task")
	}

	priceMap, err := dao.GetServicePrice([]int{o.ServiceID})
	if err != nil {
		return nil, err
	}

	p := priceMap[o.ServiceID]
	if len(p) == 0 {
		return nil, fmt.Errorf("service without price %d", o.ServiceID)
	}
	price := p[0]

	inputPods, outputPods := ETSToInputOutputPods(dao, o.QueryPodValues, as[0], authInfo, req.UseMyProfile)
	fmt.Println(inputPods.GetFEValue("personal_individual_reference_in_destination_state"))
	orderInputPods, orderOutputPods := ETSToOrderInputOutputPods(dao, o.QueryPodValues, as[0].OrderSchema)
	o.ProviderID = price.ProviderID

	if err := dao.Begin(); err != nil {
		_ = dao.Rollback()
		return nil, err
	}

	o.InputPods = funk.Values(orderInputPods).([]*models.InputPod)
	o.OutputPods = funk.Values(orderOutputPods).([]*models.InputPod)

	if err := dao.CreateServiceOrder(o); err != nil {
		_ = dao.Rollback()
		return nil, err
	}

	if len(req.RefIDs) > 0 {
		for _, refID := range req.RefIDs {
			for _, t := range (*as[0]).Tasks {
				task := &models.ServiceTask{
					// ID:         uuid.NewV4().String(),
					Type:       t,
					Status:     models.EtsTaskStatusOpen,
					OrderID:    o.ID,
					ProviderID: price.ProviderID,
				}
				taskInput := getEmptyInputPodsForTask(&(as[0].Schema), t)
				taskInput.Merge(&inputPods)

				buff, _ := base64.StdEncoding.DecodeString(refID)
				var unpackedMsg string
				msgpack.Unmarshal(buff, &unpackedMsg)

				unpackedData := gjson.Parse(unpackedMsg)
				task.InputPods = funk.Values(taskInput).([]*models.InputPod)

				if unpackedData.Get("unmatched_fields").Exists() {
					if val := unpackedData.Get("unmatched_fields").Array(); len(val) > 0 {
						errors := []string{}
						for _, err := range val {
							if mapErr, ok := map[string]string{
								"full_name":             "incorrect_name",
								"passport_expired_date": "expiration_date",
							}[err.String()]; ok {
								errors = append(errors, mapErr)

							} else {
								errors = append(errors, err.String())
							}
						}
						task.InputPods.FillByPodValues(map[string]any{
							"travel_visa_info_evisa_error": errors,
						}, false)
					}
					for _, doc := range unpackedData.Get("documents").Array() {
						if doc.Get("format").String() == "passport" {
							task.InputPods.FillByPodValues(map[string]any{
								"document_copy_of_passport_copy_of_passport_main_page": doc.Get("image").String(),
								"passport_core_info_date_of_birth":                     doc.Get("internal_fields.date_of_birth").String(),
								"passport_core_info_expiration_date":                   doc.Get("internal_fields.expiration_date").String(),
								"passport_core_info_gender":                            doc.Get("internal_fields.gender").String(),
								"passport_core_info_given_name":                        doc.Get("internal_fields.given_name").String(),
								"passport_core_info_surname":                           doc.Get("internal_fields.surname").String(),
								"passport_core_info_nationality":                       doc.Get("internal_fields.nationality").String(),
								"passport_core_info_passport_number":                   doc.Get("internal_fields.passport_number").String(),
							}, true)
						}
						if doc.Get("format").String() == "evisa" {
							task.InputPods.FillByPodValues(map[string]any{
								"travel_visa_info_proof_of_evisa_issued": doc.Get("image").String(),
								"travel_visa_info_registration_code":     doc.Get("internal_fields.registration_code").String(),
							}, false)
						}
					}
				}

				if unpackedData.Get("ref_name").String() == "flightstats" {
					task.InputPods.FillByPodValues(map[string]any{
						"service_core_info_airport":                 unpackedData.Get("airport").String(),
						"service_core_info_country":                 unpackedData.Get("country").String(),
						"service_core_info_service_type":            "fastlane",
						"travel_passenger_info_no_of_traveler":      1,
						"travel_passenger_info_passenger_name_list": "",
						"travel_passenger_info_welcome_name":        "",
					}, true)
					if unpackedData.Get("service_type").String() == "departure" {
						task.InputPods.FillByPodValues(map[string]any{
							"travel_exit_flight_exit_airport":   unpackedData.Get("airport_name").String(),
							"travel_exit_flight_exit_timestamp": unpackedData.Get("service_date_time").String(),
						}, false)
						for i := range task.InputPods {
							if task.InputPods[i].ID == "travel_exit_flight_exit_flight" {
								if task.InputPods[i].Type == "object" {
									task.InputPods.FillByPodValues(map[string]any{
										"travel_exit_flight_exit_flight": map[string]string{
											"text":  unpackedData.Get("flight_number").String() + " (" + unpackedData.Get("airline_name").String() + ")",
											"value": unpackedData.Get("flight_number").String(),
										},
									}, true)
								} else {
									task.InputPods.FillByPodValues(map[string]any{
										"travel_exit_flight_exit_flight": unpackedData.Get("flight_number").String(),
									}, true)
								}
							}
						}
					}
					if unpackedData.Get("service_type").String() == "arrival" {
						task.InputPods.FillByPodValues(map[string]any{
							"travel_enter_flight_enter_airport":   unpackedData.Get("airport_name").String(),
							"travel_enter_flight_enter_timestamp": unpackedData.Get("service_date_time").String(),
						}, true)

						for i := range task.InputPods {
							if task.InputPods[i].ID == "travel_enter_flight_enter_flight" {
								if task.InputPods[i].Type == "object" {
									task.InputPods.FillByPodValues(map[string]any{
										"travel_enter_flight_enter_flight": map[string]string{
											"text":  unpackedData.Get("flight_number").String() + " (" + unpackedData.Get("airline_name").String() + ")",
											"value": unpackedData.Get("flight_number").String(),
										},
									}, true)
								} else {
									task.InputPods.FillByPodValues(map[string]any{
										"travel_enter_flight_enter_flight": unpackedData.Get("flight_number").String(),
									}, true)
								}
							}
						}
					}
				}
				task.InputPodValues = task.InputPods.ToMapKeyValue()

				taskOutput := getEmptyOutPodsForTask(&(as[0].Schema), t)
				taskOutput.Merge(&outputPods)
				task.OutputPods = funk.Values(taskOutput).([]*models.InputPod)
				if err := updateOutputPodByInputPods(dao, task); err != nil {
					return nil, err
				}

				task.OutPodValues = task.OutputPods.ToMapKeyValue()
				if err := dao.CreateServiceTask(task); err != nil {
					_ = dao.Rollback()
					return nil, err
				}
			}
		}
	} else if !req.WithoutNewTask {
		for _, t := range (*as[0]).Tasks {
			task := &models.ServiceTask{
				// ID:         uuid.NewV4().String(),
				Type:       t,
				Status:     models.EtsTaskStatusOpen,
				OrderID:    o.ID,
				ProviderID: price.ProviderID,
			}
			taskInput := getEmptyInputPodsForTask(&(as[0].Schema), t)
			taskInput.Merge(&inputPods)

			task.InputPods = funk.Values(taskInput).([]*models.InputPod)

			taskOutput := getEmptyOutPodsForTask(&(as[0].Schema), t)
			taskOutput.Merge(&outputPods)
			task.OutputPods = funk.Values(taskOutput).([]*models.InputPod)

			if err := dao.CreateServiceTask(task); err != nil {
				_ = dao.Rollback()
				return nil, err
			}
		}
	}

	if err := dao.Commit(); err != nil {
		_ = dao.Rollback()
		return nil, err
	}

	findData, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:           []string{strconv.Itoa(o.ID)},
		IncludeTasks: true,
		Limit:        1,
	})

	if err != nil {
		return nil, err
	}
	if len(findData.Data) == 1 {
		return findData.Data[0], nil
	}

	return nil, nil
}

var mapQueryPodToInputPod = map[string][]string{
	"service_core_info_entry_date": {"travel_enter_flight_enter_timestamp"},
	"service_core_info_exit_date":  {"travel_exit_flight_exit_timestamp"},
}

func ETSToInputOutputPods(dao db.IEtsDao, queryPods datatypes.JSON, as *models.ExtendedTravelService, authInfo *models.AuthInfo, useMyProfile bool) (models.InputPods, models.InputPods) {
	input, output := models.InputPods{}, models.InputPods{}
	for _, p := range as.Schema {
		if val := ETSPodToInputPod(dao, queryPods, as, p, authInfo, useMyProfile); val != nil {
			if val.PodType == models.PodTypeInput {
				input[p.ID] = val
			}
			if val.PodType == models.PodTypeOutPut {
				output[p.ID] = val
			}
		}

	}
	fmt.Println(utils.StructToJSON(input.ToMapKeyValues()).Raw)

	return input, output
}

func ETSToOrderInputOutputPods(dao db.IEtsDao, queryPods datatypes.JSON, schema models.SchemaPods) (models.InputPods, models.InputPods) {
	result := models.InputPods{}
P:
	for _, p := range schema {
		if val := gjson.ParseBytes(queryPods).Get(p.ID).String(); val != "" {
			result[p.ID] = &models.InputPod{
				Pod:   p,
				Value: &models.InputValue{FE: val},
			}
			continue
		}
		for key, podIDs := range mapQueryPodToInputPod {
			if val := gjson.ParseBytes(queryPods).Get(key).String(); val != "" {
				if funk.ContainsString(podIDs, p.ID) {
					result[p.ID] = &models.InputPod{
						Pod:   p,
						Value: &models.InputValue{FE: val},
					}
					continue P
				}
			}

		}
		if p.Default != nil {
			if p.TypeFE == "input_phone" {
				result[p.ID] = &models.InputPod{
					Pod: p,
					Value: &models.InputValue{FE: map[string]any{
						"country": p.Default,
						"phone":   "",
					}},
				}
			}
			result[p.ID] = &models.InputPod{
				Pod:   p,
				Value: &models.InputValue{FE: p.Default},
			}
			continue
		}
		result[p.ID] = &models.InputPod{
			Pod:   p,
			Value: &models.InputValue{FE: nil},
		}
	}
	input, output := models.InputPods{}, models.InputPods{}
	for _, p := range result {
		if p.PodType == models.PodTypeInput {
			input[p.ID] = p
		}
		if p.PodType == models.PodTypeOutPut {
			output[p.ID] = p
		}
	}
	return input, output
}

// func ETSToInputQueryPods(dao db.IEtsDao, serviceType string) models.InputPods {
// 	result := models.InputPods{}
// 	pods, err := dao.QueryEtsQueryPod(map[string]any{
// 		"service": serviceType,
// 	})
// 	for _,pod:=range pods{
// 		pod.
// 	}
// }

func ETSPodToInputPod(dao db.IEtsDao, queryPods datatypes.JSON, as *models.ExtendedTravelService, p *models.Pod, authInfo *models.AuthInfo, useMyProfile bool) *models.InputPod {
	attrJ := utils.StructToJSON(as.Attributes)
	if p.ID == "personal_individual_reference_in_destination_state" {
		fmt.Println(123)
	}
	switch p.ID {
	case "service_core_info_service_type":
		return &models.InputPod{
			Pod:   p,
			Value: &models.InputValue{FE: as.ServiceType},
		}
	case "service_core_info_processing_time":
		return &models.InputPod{
			Pod:   p,
			Value: &models.InputValue{FE: (*(as.Attributes))["processing_time"]},
		}
	case "service_core_info_task", "service_core_info_tasks":
		return &models.InputPod{
			Pod:   p,
			Value: &models.InputValue{FE: as.Tasks},
		}
	case "service_core_info_country":
		return &models.InputPod{
			Pod:   p,
			Value: &models.InputValue{FE: as.Country},
		}
	case "service_core_info_airport":
		return &models.InputPod{
			Pod:   p,
			Value: &models.InputValue{FE: as.Airport},
		}
	case "service_core_info_validity":
		return &models.InputPod{
			Pod:   p,
			Value: &models.InputValue{FE: attrJ.Get("validity").String()},
		}
	case "service_core_info_number_of_entries":
		return &models.InputPod{
			Pod:   p,
			Value: &models.InputValue{FE: attrJ.Get("number_of_entries").String()},
		}

	case "service_core_info_purpose":
		return &models.InputPod{
			Pod:   p,
			Value: &models.InputValue{FE: attrJ.Get("purpose").String()},
		}
	case "passport_core_info_nationality":
		return &models.InputPod{
			Pod:   p,
			Value: &models.InputValue{FE: gjson.ParseBytes(queryPods).Get("service_core_info_nationality").String()},
		}
	case "travel_enter_flight_enter_timestamp":
		if len(as.Tasks) > 0 {
			if as.Tasks[0] == "arrival" {
				return &models.InputPod{
					Pod:   p,
					Value: &models.InputValue{FE: gjson.ParseBytes(queryPods).Get("service_core_info_service_date").String()},
				}
			}
		}
		if val := gjson.ParseBytes(queryPods).Get("service_core_info_entry_date").String(); val != "" {
			return &models.InputPod{
				Pod:   p,
				Value: &models.InputValue{FE: val},
			}

		}

	case "travel_exit_flight_exit_timestamp":
		if len(as.Tasks) > 0 {
			if as.Tasks[0] == "departure" {
				return &models.InputPod{
					Pod:   p,
					Value: &models.InputValue{FE: gjson.ParseBytes(queryPods).Get("service_core_info_service_date").String()},
				}
			}
		}
		if val := gjson.ParseBytes(queryPods).Get("service_core_info_exit_date").String(); val != "" {
			return &models.InputPod{
				Pod:   p,
				Value: &models.InputValue{FE: val},
			}

		}

	case "travel_enter_flight_enter_airport":
		if len(as.Tasks) > 0 {
			if as.Tasks[0] == "arrival" {
				airports, _ := dao.VisaDB().GetMasterDataValueByName("airport")
				result := gjson.GetBytes(airports, fmt.Sprintf(`#(iata="%s").name`, gjson.ParseBytes(queryPods).Get("service_core_info_airport").String()))
				return &models.InputPod{
					Pod:   p,
					Value: &models.InputValue{FE: result.String()},
				}
			}
		}
	case "travel_exit_flight_exit_airport":
		if len(as.Tasks) > 0 {
			if as.Tasks[0] == "departure" {
				airports, _ := dao.VisaDB().GetMasterDataValueByName("airport")
				result := gjson.GetBytes(airports, fmt.Sprintf(`#(iata="%s").name`, gjson.ParseBytes(queryPods).Get("service_core_info_airport").String()))
				return &models.InputPod{
					Pod:   p,
					Value: &models.InputValue{FE: result.String()},
				}
			}
		}
	default:
		if val := gjson.ParseBytes(queryPods).Get(p.ID).String(); val != "" {
			return &models.InputPod{
				Pod:   p,
				Value: &models.InputValue{FE: val},
			}
		}
		if p.Default != nil {
			if p.TypeFE == "input_phone" {
				return &models.InputPod{
					Pod: p,
					Value: &models.InputValue{FE: map[string]any{
						"country": p.Default,
						"phone":   "",
					}},
				}
			}
			return &models.InputPod{
				Pod:   p,
				Value: &models.InputValue{FE: p.Default},
			}
		}
		// case *********, *********, *********, *********, *********, *********, *********, 101700049:
		// 	if p.UI == "noshow" {
		// 		return &models.InputPod{
		// 			Pod:   p,
		// 			Value: &models.InputValue{FE: p.Default},
		// 		}
		// 	}
	}

	if authInfo != nil {
		if useMyProfile {
			if p.OptionChoice != nil && len(p.OptionChoice) > 0 {
				for k, o := range p.OptionChoice {
					if val, ok := myProfilePatternToValue(dao, nil, authInfo.UserID, p.MyProfile.String).(string); ok && val == k {
						for i := 0; i < len(o); i++ {
							if o[i].MyProfile.String != "" {
								p.OptionChoice[k][i].Value = &models.InputValue{FE: myProfilePatternToValue(dao, p, authInfo.UserID, o[i].MyProfile.String)}
							}
						}
					}
				}
			}
			if p.MyProfile.String != "" {
				val := myProfilePatternToValue(dao, p, authInfo.UserID, p.MyProfile.String)
				if str, ok := val.(string); ok && str == "" {
					return &models.InputPod{
						Pod: p,
					}
				}

				return &models.InputPod{
					Pod:   p,
					Value: &models.InputValue{FE: val},
				}

			}
		}

		// if authInfo.IsAdmin() && authInfo.OrgID.Int64 > 0 {
		// 	if p.Category == "personal" && p.SubCategory != "emergency_contact" {
		// 		mapNameToDB := map[string]string{
		// 			"phone":              "phone",
		// 			"country":            "country",
		// 			"state":              "state",
		// 			"city":               "city",
		// 			"street_number_name": "address",
		// 			"address":            "address",
		// 			"zip_code":           "zip_code",
		// 		}

		// 		if field, ok := mapNameToDB[p.Name]; ok {
		// 			val, err := dao.GetUserCorpDataByCondition(authInfo.UserID, field)
		// 			if err != nil {
		// 				fmt.Println(err)
		// 			}
		// 			return &models.InputPod{
		// 				Pod:   p,
		// 				Value: &models.InputValue{FE: val},
		// 			}
		// 		}

		// 	}
		// }
	}

	return &models.InputPod{
		Pod: p,
	}
}

var gocountry = gountries.New()

func myProfilePatternToValue(dao db.IEtsDao, p *models.Pod, userID string, myProfilePattern string) any {
	var result any
	// Regrex match 4 groups
	// Example: address_book:zip_code:is_primary_address-true
	regexMyProfile := regexp.MustCompile(`^([A-Za-z_]+):([A-Za-z_]+):([A-Za-z_]+)-([A-Za-z0-9_]+)$`)
	if regexMyProfile.MatchString(myProfilePattern) {
		// Get all match items from regex
		if match := regexMyProfile.FindStringSubmatch(myProfilePattern); len(match) == 5 {
			table, field, condition, value := match[1], match[2], match[3], match[4]
			val, err := dao.GetProfileDataByCondition(userID, table, field, condition, value)
			if err != nil {
				fmt.Println(err)
			}
			result = val

		}

	}

	// Example personal_info:phone
	regexTravelerMyProfile := regexp.MustCompile(`^([a-zA-Z0-9_]+):([a-zA-Z0-9_]+)$`)
	if regexTravelerMyProfile.MatchString(myProfilePattern) {
		// Get all match items from regex
		if match := regexTravelerMyProfile.FindStringSubmatch(myProfilePattern); len(match) == 3 {
			table, field := match[1], match[2]
			val, err := dao.GetProfileDataByCondition(userID, table, field, "", "")
			if err != nil {
				fmt.Println(err)
			}
			result = val

		}
	}

	if phone, ok := result.(string); ok && p != nil && p.TypeFE == "input_phone" && p.Type == "object" {
		countryCode := ""
		if name, err := phonegeocode.New().Country(phone); err == nil {
			country, _ := gocountry.FindCountryByAlpha(name)
			countryCode = country.Alpha3
		}
		if phone == "" || countryCode == "" {
			result = p.Default
		} else {
			result = map[string]any{
				"phone":   phone,
				"country": countryCode,
			}
		}

	}

	return result
}

func updateEtsTaskStatus(c *gin.Context, taskID any, orderID any, status string, note json.RawMessage) error {
	dao := getEtsDao(c)
	orders, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:             []string{cast.ToString(orderID)},
		IncludeService: true,
		IncludeTasks:   true,
		Limit:          1,
	})
	if err != nil {
		return err
	}
	order := orders.Data[0]
	tasks := funk.Filter(order.Tasks, func(t *models.ServiceTask) bool {
		return t.ID == cast.ToInt64(taskID)
	}).([]*models.ServiceTask)

	u := map[string]any{}
	if status != "" {
		u["status"] = status
	}
	if note != nil {
		u["note"] = note
	}
	if err := dao.UpdateServiceTask(u, tasks[0].ID); err != nil {
		return err
	}
	// update order status
	if funk.ContainsString([]string{models.EtsTaskStatusUploadedMissingDocument, models.EtsTaskStatusReviewed}, status) {
		up := map[string]any{"status": models.EtsOrderStatusUnderReview}
		if err := dao.UpdateServiceOrder(up, tasks[0].OrderID); err != nil {
			return err
		}

		if err := notification.NotifyUploadMissingDocumentToConsulate(c, dao, tasks[0]); err != nil {
			return err
		}

	}

	if status == models.EtsTaskStatusNeedMoreDocument {
		if err := notification.NotifyNeedMoreDocument(c, order); err != nil {
			return err
		}
		if err := notification.NotifyNeedMoreDocumentToUser(c, order, tasks[0]); err != nil {
			return err
		}
		up := map[string]any{"status": models.EtsOrderStatusNeedMoreDocument}
		if err := dao.UpdateServiceOrder(up, order.ID); err != nil {
			return err
		}
	}

	// Notification cancel task to provider
	if status == models.EtsTaskStatusCancelled {
		update := map[string]any{"status": models.EtsOrderStatusCancelled}
		if err := dao.UpdateServiceTask(update, tasks[0].ID); err != nil {
			return err
		}

		result, _, err := dao.QueryServiceTasks(map[string]any{"order_id": orderID}, 0, 0)
		if err != nil {
			return err
		}
		allCancelled := true
		for _, item := range result {
			if item.Status != models.EtsOrderStatusCancelled {
				allCancelled = false
				break
			}
		}

		if allCancelled {
			if err := dao.UpdateServiceOrder(map[string]any{"status": models.EtsOrderStatusCancelled}, tasks[0].OrderID); err != nil {
				return err
			}

			if order.IsPaid() {
				if err := notification.NotifyCancelETSToUser(c, order, tasks[0]); err != nil {
					return err
				}

				if err := notification.NotifyCancelETSToProvider(c, order, gjson.ParseBytes(note).Get("cancelled.0.message").String()); err != nil {
					return err
				}
			}
		}

		if order.IsPaid() && funk.ContainsString(notification.URGENT_PRODUCT_NAMES, order.Service.Name) {
			if err := notification.NotifyViaZaloForCancelUrgentVisa(dao, int64(order.ID), []int64{tasks[0].ID}); err != nil {
				return err
			}
		}
	}

	return nil
}

func UpdateEtsTaskStatus(c *gin.Context) {
	var t models.ServiceTask
	if err := c.BindJSON(&t); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	orderID, taskID := c.Param("order-id"), c.Param("task-id")
	if orderID == "" || taskID == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order-id or task-id"))
		return
	}

	err := updateEtsTaskStatus(c, taskID, orderID, t.Status, t.Note)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

type BatchStatusUpdateReq struct {
	TaskIDs   []int64         `json:"task_ids"`
	NewStatus string          `json:"new_status"`
	Note      json.RawMessage `json:"note"`
}

func UpdateBatchEtsTaskStatus(c *gin.Context) {
	var req BatchStatusUpdateReq
	if err := c.BindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	orderID := c.Param("order-id")
	if orderID == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order-i"))
		return
	}

	for _, taskID := range req.TaskIDs {
		err := updateEtsTaskStatus(c, taskID, orderID, req.NewStatus, req.Note)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func UpdateEtsTask(c *gin.Context) {
	var t models.ServiceTask
	if err := c.BindJSON(&t); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	orderID, taskID := c.Param("order-id"), c.Param("task-id")
	if orderID == "" || taskID == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order-id or task-id"))
		return
	}
	visaDao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	dao := getEtsDao(c)
	tasks, _, err := dao.QueryServiceTasks(map[string]any{"order_id": orderID, "id": taskID}, 0, 0)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	var task *models.ServiceTask
	for _, t := range tasks {
		if strconv.FormatInt(t.ID, 10) == taskID {
			task = t
		}
	}
	if task == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("not found"))
		return
	}

	o := ets_mw.GetEtsOrderFromCtx(c)
	authInfo := middlewares.GetAuthInfo(c)

	if authInfo.IsUser() || authInfo.IsGuest() {
		// if !funk.ContainsString([]string{models.EtsOrderStatusOpen, models.EtsOrderStatusPendingPayment, models.EtsOrderStatusWaitingPayment, models.EtsOrderStatusNeedMoreDocument, models.EtsOrderStatusDispatched}, o.Status) {
		// 	c.JSON(http.StatusUnauthorized, gin.H{
		// 		"success": false,
		// 		"error":   "can not update input pods for submit order",
		// 	})
		// 	return
		// }
	}

	updated, err := updateEtsTask(dao, &t, o.ServiceOrder)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	orderTasks, _, err := dao.QueryServiceTasks(map[string]any{"order_id": orderID}, 0, 0)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	allTaskCancelled := true
	for _, orderTask := range orderTasks {
		if orderTask.Status != models.EtsTaskStatusCancelled {
			allTaskCancelled = false
			break
		}
	}

	if allTaskCancelled {
		if err := dao.UpdateServiceOrder(map[string]any{"status": models.EtsOrderStatusCancelled}, o.ID); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	if models.IsOrderPaid(o.Status) {
		errchan := make(chan error)
		go func() {
			errchan <- notification.NotifyUpdateOutputPods(c, cast.ToInt64(orderID), cast.ToInt64(taskID))
		}()

		go func() {
			if tasks[0].Status == models.EtsTaskStatusUploadedMissingDocument {
				errchan <- notification.NotifyUploadMissingDocumentToConsulate(c, dao, task)
			} else {
				errchan <- sendEmailToProviderUserChangeData(c, o, task, updated)
			}
		}()

		for i := 0; i < 2; i++ {
			if err := <-errchan; err != nil {
				response.HandleResponse(c, nil, err)
				return
			}
		}
	}

	_, err = addServiceSummaryByTask(dao, &t)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if o.PaymentID != "" {
		cart, err := visaDao.GetCartByPayment(o.PaymentID)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

		if err := ReCalculateCartItem(visaDao, cart); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	ps := middlewares.GetS3Presigner(c)
	presignTask(ps, o.ServiceOrder, updated)

	response.HandleResponse(c, updated, nil)
}

func PublicUpdateEtsTask(c *gin.Context) {
	orderID, taskID := c.Param("order-id"), c.Param("task-id")
	if orderID == "" || taskID == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order-id or task-id"))
		return
	}
	dao := getEtsDao(c)

	resp, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:             []string{orderID},
		IncludeService: true,
		Limit:          1,
	})
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	order := resp.Data[0]

	tasks, _, err := dao.QueryServiceTasks(map[string]any{"order_id": orderID, "id": taskID}, 0, 0)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	task := tasks[0]

	if task == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("not found"))
		return
	}

	// Update staff confirm stastus
	for i, pod := range task.OutputPods {
		if pod.Name == "staff_confirm_status" {
			task.OutputPods[i].Value = &models.InputValue{FE: "confirmed"}
		}
	}

	etsJ := utils.StructToJSON(order.Service)
	webBaseURL := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("web_base_url").String()

	adEmail := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String()

	params := map[string]any{
		"OrderID":     order.ID,
		"Country":     order.Service.Country,
		"ServiceType": "Fastlane",
		"Terminal":    strings.Title(etsJ.Get("attributes.terminal").String()),
		"Task":        strings.Title(strings.Join(order.Service.Tasks, " ")),
		"URL":         fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", webBaseURL, order.ID),
	}

	country, _ := gocountry.FindCountryByAlpha(order.Service.Country)
	params["Country"] = country.Name.Common

	message := map[string]any{
		"template_name": "notify_ad_staff_confirm_fastlane",
		"to":            adEmail,
		"bcc":           []string{},
		"parameters":    params,
	}
	if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	updated, err := updateEtsTask(dao, task, order.ServiceOrder)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	response.HandleResponse(c, updated, nil)
}

func presignTask(ps *awsSdk.S3Svc, order *models.ServiceOrder, task *models.ServiceTask) {
	isPaid := order.IsPaid()

	if err := applications.PresignInputPods(ps, order.InputPods, expirationForFiles); err != nil {
		log.Error().Fields(map[string]any{
			"error": err.Error(),
		}).Msg("failed to presign input pods")
	}

	if err := applications.PresignInputPods(ps, task.InputPods, expirationForFiles); err != nil {
		log.Error().Fields(map[string]any{
			"error": err.Error(),
		}).Msg("failed to presign input pods")
	}

	if isPaid {
		inputPodPair := task.InputPods.ToMapKeyValue()
		if val := inputPodPair["photo_passport_photo_copy_of_photo"]; val != nil {
			task.InputPods.SetFEValue("photo_passport_photo_copy_of_photo", strings.ReplaceAll(cast.ToString(val), "-demo", ""))
		}
	}

	if err := applications.PresignInputPods(ps, task.InputPods, expirationForFiles); err != nil {
		log.Error().Fields(map[string]any{
			"error": err.Error(),
		}).Msg("failed to presign output pods")
	}

	if err := applications.PresignInputPods(ps, task.OutputPods, expirationForFiles); err != nil {
		log.Error().Fields(map[string]any{
			"error": err.Error(),
		}).Msg("failed to presign output pods")
	}

	task.InputPodValues = task.InputPods.ToMapKeyValueV2(order.InputPods)
	task.OutPodValues = task.OutputPods.ToMapKeyValueV2(order.OutputPods)

	PresignAllFilesInMap(ps, task.OutputFiles, expirationForFiles)
}

func updateEtsTask(dao db.IEtsDao, t *models.ServiceTask, o *models.ServiceOrder) (*models.ServiceTask, error) {
	oldTasks, _, err := dao.QueryServiceTasks(map[string]any{"id": t.ID}, 0, 1)
	if err != nil {
		return nil, err
	}

	updateInputPodValues := t.InputPods.ToMapKeyValueV2(o.InputPods)
	passportNumber := cast.ToString(updateInputPodValues["passport_core_info_passport_number"])
	if passportNumber == "" {
		passportNumber = cast.ToString(updateInputPodValues["passport_most_recent_pp_pp_book_number"])
	}
	if passportNumber == "" {
		passportNumber = cast.ToString(updateInputPodValues["passport_most_recent_pp_pp_card_number"])
	}
	existInputPodValues, err := dao.GetServiceCustomerData(passportNumber, o.UserID)
	if err != nil {
		return nil, err
	}
	delete(existInputPodValues, "service_core_info_entry_date")
	delete(existInputPodValues, "service_core_info_exit_date")
	oldOutputPodValues := oldTasks[0].OutputPods.ToMapKeyValue()

	newTask := oldTasks[0]
	newTask.InputPods.FillByPodValues(existInputPodValues, true)   // Only append data
	newTask.InputPods.FillByPodValues(updateInputPodValues, false) // Override the exist data

	newInputPodKeyValues := newTask.InputPods.ToMapKeyValueV2(o.InputPods)
	u := map[string]any{
		"input_pods":       newTask.InputPods,
		"input_pod_values": newInputPodKeyValues,
	}

	outputPodValues := newTask.OutputPods.ToMapKeyValue()

	if models.IsOrderPaid(o.Status) {
		newOutputPodValues := t.OutputPods.ToMapKeyValue()
		for k, v := range newOutputPodValues {
			if v != nil {
				outputPodValues[k] = v
			}
		}
		newTask.OutputPods.FillByPodValues(outputPodValues, false)
	}

	if err := updateOutputPodByInputPods(dao, newTask); err != nil {
		return nil, err
	}

	u["output_pods"] = newTask.OutputPods
	u["output_pod_values"] = newTask.OutputPods.ToMapKeyValue()

	if t.Status != "" {
		u["status"] = t.Status
	}
	if err := dao.UpdateServiceTask(u, t.ID); err != nil {
		return nil, err
	}

	newOutputPodValues := newTask.OutputPods.ToMapKeyValue()
	oldCode := cast.ToString(oldOutputPodValues["application_application_info_registration_code"])
	newCode := cast.ToString(newOutputPodValues["application_application_info_registration_code"])
	if o.IsPaid() && oldCode != "" && newCode != "" && oldCode != newCode {
		if err := notification.NotifyViaZaloForUpdateUrgentVisa(dao, int64(newTask.OrderID), newTask.ID, false); err != nil {
			return nil, err
		}
	}

	oldProof := utils.StructToJSON(oldOutputPodValues["travel_visa_info_proof_of_evisa_issued"]).Raw
	newProof := utils.StructToJSON(newOutputPodValues["travel_visa_info_proof_of_evisa_issued"]).Raw
	if o.IsPaid() && newProof != "" && oldProof != newProof {
		if err := notification.NotifyViaZaloForUpdateEVisaProofFile(dao, int64(newTask.OrderID), newTask.ID); err != nil {
			return nil, err
		}
	}

	updated, _, err := dao.QueryServiceTasks(map[string]any{"id": t.ID}, 0, 1)
	if err != nil {
		return nil, err
	}
	if len(updated) != 1 {
		return nil, errors.NotFoundErr
	}
	return updated[0], nil
}

func sendEmailToProviderUserChangeData(c *gin.Context, o *models.ServiceOrderDetail, srcTask, descTask *models.ServiceTask) error {
	orm := middlewares.GetORMDao(c)
	rows, err := orm.Table("v_organizations").Where("profile = ?", o.ProviderID).Select("name, address, contact").Rows()
	if err != nil {
		return err
	}

	defer rows.Close()
	if rows.Next() {
		var (
			corpName    string
			corpAddress models.Address
			corpContact models.Contact
		)

		if err := rows.Scan(&corpName, &corpAddress, &corpContact); err != nil {
			return err
		}

		dao, err := middlewares.GetVisaDao(c)
		if err != nil {
			return err
		}
		shipment, err := dao.GetVisaShipment(o.ShipmentInfo.String)
		if err != nil {
			return err
		}

		user, err := dao.GetUserByID(o.UserID)
		if err != nil {
			return err
		}

		webBaseURL := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("web_base_url").String()

		receiveEmail := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String()
		if os.Getenv("ad_env") == "prod" {
			receiveEmail = corpContact.Email
		}

		staffEmails := []string{}
		if err := dao.DB().ORM().Raw(`SELECT u.email FROM service_order_staffs sos left join users u on u.id = sos.staff_id
			  WHERE order_id = ? and sos.staff_confirm_status != 'rejected'`, o.ID).Scan(&staffEmails).Error; err != nil {
			return err
		}

		bcc := []string{}
		if len(descTask.InputPods) > 0 {
			bcc = append(bcc, utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String())

			changedInputPodsData, changedInfo := getAllowChangePods(srcTask.InputPods, descTask.InputPods)

			message := map[string]any{
				"template_name": "notify_provider_for_updated_order",
				"to":            receiveEmail,
				"cc":            staffEmails,
				"bcc":           bcc,
				"parameters": map[string]any{
					"ProviderName": corpName,
					"OrderID":      o.ID,
					"URL":          fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", webBaseURL, o.ID),
					"Data":         changedInputPodsData,
				},
				"attachments": []map[string]any{},
			}
			if changedInfo {
				if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
					return err
				}
				for _, info := range changedInputPodsData {
					if info.Name == "exit_timestamp" || info.Name == "enter_timestamp" || info.Name == "note" {
						inputPods := descTask.InputPods.ToMapKeyValueV2(o.InputPods)
						webBaseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("web_base_url").String()

						// Build Zalo message with addField
						var parts []string
						addField := func(label string, value interface{}) {
							if v := fmt.Sprintf("%v", value); v != "" && v != "0" && v != "Jan 01, 0001 - 00:00" {
								parts = append(parts, fmt.Sprintf("%s: <b>%v</b>", label, value))
							}
						}

						isDeparture := o.Service.Tasks[0] == "departure"
						serviceType := funk.ShortIf(cast.ToString((*o.Service.Attributes)["terminal"]) == "international", "International", "Domestic")
						serviceDirection := funk.ShortIf(isDeparture, "Departure", "Arrival")

						parts = append(parts, fmt.Sprintf(`[<b><span style="color:#db342e">UPDATED FLIGHT</span></b>] Order# <b>%d</b> - Updated request for Fastlane <b>%s %s</b> service as below:`,
							o.ID, serviceType, serviceDirection))

						if isDeparture {
							addField("Airport", cast.ToString(inputPods["travel_exit_flight_exit_airport"]))
							addField("Flight#", utils.GetStringOrText(inputPods["travel_exit_flight_exit_flight"]))
							addField("Date Time", cast.ToTime(inputPods["travel_exit_flight_exit_timestamp"]).Format("Jan 02, 2006 - 15:04"))
						} else {
							addField("Airport", cast.ToString(inputPods["travel_enter_flight_enter_airport"]))
							addField("Flight#", utils.GetStringOrText(inputPods["travel_enter_flight_enter_flight"]))
							addField("Date Time", cast.ToTime(inputPods["travel_enter_flight_enter_timestamp"]).Format("Jan 02, 2006 - 15:04"))
						}

						meetupTime := lo.If(isDeparture, cast.ToTime(inputPods["travel_exit_flight_meetup_time"]).Format("Jan 02, 2006 - 15:04")).Else("")
						addField("Meet Up Time", meetupTime)

						// Get driver contact
						driverName := cast.ToString(inputPods["travel_driver_information_full_name"])
						driverPhone := utils.StructToJSON(inputPods["travel_driver_information_phone"]).Get("phone").String()
						driverContact := ""
						if val := cast.ToString(inputPods["travel_driver_information_social_network_id"]); val != "" {
							driverContact = cast.ToString(inputPods["travel_driver_information_social_network_channel"]) + " - " + cast.ToString(inputPods["travel_driver_information_social_network_id"])
						}
						driverLicensePlate := cast.ToString(inputPods["travel_driver_information_license_plate"])

						addField("Driver Name", driverName)
						addField("Driver Phone", driverPhone)
						addField("Driver Contact", driverContact)
						addField("Driver License Plate", driverLicensePlate)

						addField("Welcome Name", cast.ToString(inputPods["travel_passenger_info_welcome_name"]))
						addField("Number of travelers", cast.ToInt(inputPods["travel_passenger_info_no_of_traveler"]))
						addField("Note", cast.ToString(inputPods["travel_additional_information_note"]))
						parts = append(parts, fmt.Sprintf("To view order details, please click the below button:\n%s/dashboard/orders/detail?order_id=%d",
							webBaseURL, o.ID))

						message := strings.Join(parts, "\n")
						if corpContact.ZaloGroup != "" {
							if err := zalo.SendZaloSMS(corpContact.ZaloGroup, message); err != nil {
								fmt.Println(err) // Consider proper error handling
							}
						}

						cc := []string{}
						if shipment != nil && shipment.ShippingContact != nil && shipment.ShippingContact.Email != "" {
							cc = append(cc, shipment.ShippingContact.Email)
						}

						emailMessage := map[string]any{
							"template_name": "notify_user_for_updated_order",
							"to":            user.Email,
							"cc":            cc,
							"bcc":           []string{utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String(), receiveEmail},
							"parameters": map[string]any{
								"FullName": user.GivenName + " " + user.Surname,
								"OrderID":  o.ID,
								"URL":      fmt.Sprintf("%s/dashboard/customer-orders/detail?order_id=%d&service=ets", webBaseURL, o.ID),
								"Data":     changedInputPodsData,
							},
							"attachments": []map[string]any{},
						}

						if changedInfo { // Note: Email sending is disabled with false
							if err := sendEmail(utils.StructToJSON(emailMessage).Raw); err != nil {
								return err
							}
						}
					}
				}
			}
		}

		if o.Service.ServiceType == "fastlane" && len(descTask.OutputPods) > 0 {
			srcOutputPodJ := srcTask.OutputPods.ToMapByName("Name")
			descOutputPodJ := descTask.OutputPods.ToMapByName("Name")

			srcEmail := utils.StructToJSON(srcOutputPodJ["email_address"]).Get("value.fe").String()
			descEmail := utils.StructToJSON(descOutputPodJ["email_address"]).Get("value.fe").String()
			fullName := utils.StructToJSON(descOutputPodJ["full_name"]).Get("value.fe").String()

			if srcEmail != descEmail {
				inputPods := descTask.InputPods.ToMapKeyValue()

				etsJ := utils.StructToJSON(o.Service)
				var params = map[string]any{
					"OrderID":         o.ID,
					"FullName":        fullName,
					"Country":         o.Service.Country,
					"ServiceType":     "Fastlane",
					"Terminal":        strings.Title(etsJ.Get("attributes.terminal").String()),
					"Task":            strings.Title(strings.Join(o.Service.Tasks, " ")),
					"ProcessingTime":  etsJ.Get("attributes.processing_time").String(),
					"NoOfTraveler":    inputPods["travel_passenger_info_no_of_traveler"],
					"AirportName":     inputPods["travel_exit_flight_exit_airport"],
					"AirlineName":     inputPods["travel_exit_flight_exit_airline"],
					"WelcomeName":     inputPods["travel_passenger_info_welcome_name"],
					"FlightNo":        utils.GetStringOrText(inputPods["travel_exit_flight_exit_flight"]),
					"ServiceDateTime": inputPods["travel_exit_flight_exit_timestamp"],
					"URL":             fmt.Sprintf("%s/fastlane/pickup-confirmed?order_id=%d&task_id=%d&service=ets&code=%s", webBaseURL, o.ID, descTask.ID, uuid.NewV4().String()),
				}

				country, _ := gocountry.FindCountryByAlpha(o.Service.Country)
				params["Country"] = country.Name.Common

				if descTask.Type == "arrival" || descTask.Type == "vip_arrival" {
					params["AirportName"] = inputPods["travel_enter_flight_enter_airport"]
					params["AirlineName"] = inputPods["travel_enter_flight_enter_airline"]
					params["FlightNo"] = utils.GetStringOrText(inputPods["travel_enter_flight_enter_flight"])
					params["ServiceDateTime"] = inputPods["travel_enter_flight_enter_timestamp"]
				}

				params["ServiceDateTime"] = utils.StructToJSON(params).Get("ServiceDateTime").Time().Format("Mon, 02 Jan 2006 15:04")

				emailConfigMap := utils.GetMapEnv("ad_email")
				supportEmail := utils.StructToJSON(emailConfigMap).Get("support").String()
				var message = map[string]any{
					"template_name": "fastlane_request_to_staff",
					"to":            descEmail,
					"bcc":           []string{supportEmail},
					"parameters":    params,
				}

				if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

type PodDataChange struct {
	Name, Title, OldValue, NewValue string
}

func getAllowChangePods(src, edit models.InputPodsArray) ([]PodDataChange, bool) {
	srcMap := src.ToMap()
	result := []PodDataChange{}
	changedInfo := false
	sort.Slice(edit, func(i, j int) bool { return edit[i].Order < edit[j].Order })
	for _, pod := range edit {
		if !lo.SomeBy([]string{
			"travel_passenger_info_no_of_traveler",
			"travel_passenger_info_passenger_name_list",
			"travel_exit_flight",
			"travel_enter_flight",
			"trave_driver",
			"information_note",
		}, func(s string) bool {
			return strings.Contains(pod.ID, s)
		}) {
			continue
		}

		oldValue := utils.StructToJSON(srcMap[pod.ID]).Get("value.fe").String()
		newValue := utils.StructToJSON(pod).Get("value.fe").String()

		if newValue == "" {
			continue
		}

		if oldValue != newValue {
			changedInfo = true
		}

		if val := gjson.Parse(oldValue).Get("value").String(); val != "" {
			oldValue = val
		}

		if val := gjson.Parse(newValue).Get("value").String(); val != "" {
			newValue = val
		}

		if val, err := time.Parse(time.RFC3339, newValue); err == nil {
			newValue = val.Format("Mon, 02 Jan 2006 15:04")
		}

		if val := utils.StructToJSON(pod).Get("value.fe.phone").String(); val != "" {
			newValue = val
		}

		result = append(result, PodDataChange{pod.Name, pod.Title, oldValue, newValue})

	}

	return result, changedInfo
}

func getOutputPodsChanged(showPodNames []string, src, edit models.InputPodsArray) ([]PodDataChange, bool) {
	srcMap := src.ToMap()
	result := []PodDataChange{}
	changedInfo := false

	sort.Slice(edit, func(i, j int) bool { return edit[i].Order < edit[j].Order })

	for _, pod := range edit {
		if strings.Contains(pod.UI, "noshow") {
			continue
		}

		oldValue := utils.StructToJSON(srcMap[pod.ID]).Get("value.fe").String()
		newValue := utils.StructToJSON(pod).Get("value.fe").String()
		if funk.ContainsString(showPodNames, pod.Name) && oldValue != newValue {
			changedInfo = true
		}

		if val, err := time.Parse(time.RFC3339, newValue); err == nil {
			newValue = val.Format("Mon, 02 Jan 2006 15:04")
		}

		if val := utils.StructToJSON(pod).Get("value.fe.phone").String(); val != "" {
			newValue = val
		}

		if newValue != "" {
			result = append(result, PodDataChange{pod.Name, pod.Title, oldValue, newValue})
		}

	}

	return result, changedInfo
}

func addServiceSummaryByTask(dao db.IEtsDao, t *models.ServiceTask) (*models.ServiceSummary, error) {
	findData, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:           []string{strconv.Itoa(t.OrderID)},
		IncludeTasks: true,
		IncludePrice: true,
		Limit:        1,
	})

	if err != nil {
		return nil, err
	}

	return summary.CalculateETSServiceSummary(dao, findData.Data[0], models.CartSelection{}, true)
}

func GetTasksByUser(c *gin.Context) {

	auth := middlewares.GetAuthInfo(c)

	dao := getEtsDao(c)

	tasks, total, err := dao.QueryServiceTasks(map[string]any{"user_id": auth.UserID}, 0, 0)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	order, err := getEtsDao(c).GetServiceOrderByID(tasks[0].OrderID)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	for i := 0; i < len(tasks); i++ {
		ps := middlewares.GetS3Presigner(c)
		presignTask(ps, order, tasks[i])
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    tasks,
		"total":   total,
	})
}

func GetTasksByOrder(c *gin.Context) {
	orderID := c.Param("order-id")
	if orderID == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}
	order, err := getEtsDao(c).GetServiceOrderByID(cast.ToInt(orderID))
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	tasks, _, err := getEtsDao(c).QueryServiceTasks(map[string]any{"order_id": orderID}, 0, 0)
	for i := 0; i < len(tasks); i++ {
		ps := middlewares.GetS3Presigner(c)
		presignTask(ps, order, tasks[i])
	}
	response.HandleResponse(c, tasks, err)
}

func CreatePublicTasksByOrder(c *gin.Context) {
	orderID := c.Param("order-id")
	taskIDs := c.QueryArray("id")

	auth := middlewares.GetAuthInfo(c)

	dao := getEtsDao(c)

	q := models.ServiceOrderFilter{
		ID:              []string{orderID},
		IncludeTasks:    true,
		IncludeService:  true,
		IncludePrice:    true,
		IncludePayment:  true,
		IncludeShipment: true,
		Limit:           1,
	}

	if utils.Contain("ad_admin", auth.Roles) {
		q.IncludeOutputFileBeforePaid = true
		q.IncludeCreator = true
		q.IncludeProvider = true
	}

	existing, err := dao.QueryServiceOrders(q)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	staffs, err := dao.GetStaffsByOrderID(int64(existing.Data[0].ID))
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	isStaff := false
	for _, staff := range staffs {
		if staff.StaffID == auth.UserID {
			isStaff = true
			break
		}
	}

	if (auth.IsUser() || auth.IsGuest()) && (existing.Data[0].UserID != auth.UserID && !isStaff) {
		response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("you don't have permission"))
		return
	}

	orderTaskIDs := []string{}
	for i := 0; i < len(existing.Data[0].Tasks); i++ {
		orderTaskIDs = append(orderTaskIDs, strconv.Itoa(int(existing.Data[0].Tasks[i].ID)))
	}

	taskIDs = funk.IntersectString(taskIDs, orderTaskIDs)

	code, err := crypto.Encrypt([]byte(strings.Join(taskIDs, ",")))
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    code,
	})
}
func GetPublicTasksByOrder(c *gin.Context) {
	orderID := c.Param("order-id")
	decryptedCode := c.Query("code")
	decrypt, err := crypto.Decrypt(decryptedCode)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	taskIDs := strings.Split(string(decrypt), ",")

	dao := getEtsDao(c)

	q := models.ServiceOrderFilter{
		ID:              []string{orderID},
		IncludeTasks:    true,
		IncludeService:  true,
		IncludePrice:    true,
		IncludePayment:  true,
		IncludeShipment: true,
		Limit:           1,
	}

	existing, err := dao.QueryServiceOrders(q)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	ps := middlewares.GetS3Presigner(c)
	limitTasks := []*models.ServiceTask{}
	for i := 0; i < len(existing.Data[0].Tasks); i++ {
		presignTask(ps, existing.Data[0].ServiceOrder, existing.Data[0].Tasks[i])
		if funk.ContainsString(taskIDs, strconv.Itoa(int(existing.Data[0].Tasks[i].ID))) {
			limitTasks = append(limitTasks, existing.Data[0].Tasks[i])
		}
	}
	existing.Data[0].Tasks = limitTasks

	PresignAllFilesInMap(ps, existing.Data[0].OutputFiles, expirationForFiles)
	if err := presignETSPaymentReceiptURL(ps, existing.Data[0]); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	response.HandleResponse(c, existing.Data[0], nil)

}

func GetTasksByID(c *gin.Context) {
	orderID := c.Param("order-id")
	taskID := c.Param("task-id")
	if orderID == "" || taskID == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}
	order, err := getEtsDao(c).GetServiceOrderByID(cast.ToInt(orderID))
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	tasks, _, err := getEtsDao(c).QueryServiceTasks(map[string]any{"order_id": orderID, "id": taskID}, 0, 0)
	if len(tasks) == 0 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid task ID"))
		return
	}
	ps := middlewares.GetS3Presigner(c)
	presignTask(ps, order, tasks[0])

	response.HandleResponse(c, tasks[0], err)
}

func getEtsDao(c *gin.Context) db.IEtsDao {
	d, err := middlewares.GetVisaDao(c)
	if err != nil {
		log.Error().Err(err)
		return nil
	}
	return db.NewEtsDao(d.DB().Db)
}

func getEmptyInputPodsForTask(schema *models.SchemaPods, taskName string) *models.InputPods {
	i := models.InputPods{}
	for _, p := range *schema {
		if (len(p.Task) > 0 && p.Task != taskName) || p.PodType != models.PodTypeInput {
			// skip when the task field of pod is not empty, but not containing the target task name
			continue
		}
		i[p.ID] = &models.InputPod{
			Pod: p,
		}
	}
	return &i
}

func getEmptyOutPodsForTask(schema *models.SchemaPods, taskName string) *models.InputPods {
	i := models.InputPods{}
	for _, p := range *schema {
		if (len(p.Task) > 0 && p.Task != taskName) || p.PodType != models.PodTypeOutPut {
			// skip when the task field of pod is not empty, but not containing the target task name
			continue
		}
		i[p.ID] = &models.InputPod{
			Pod: p,
		}
	}
	return &i
}

func getProvider(dao db.IEtsDao, serviceID int) (string, error) {
	priceMap, err := dao.GetServicePrice([]int{serviceID})
	if err != nil {
		return "", err
	}

	p := priceMap[serviceID]
	if len(p) == 0 {
		return "", fmt.Errorf("service without price %d", serviceID)
	}
	return p[0].ProviderID, nil
}

func DeleteEtsOrder(c *gin.Context) {
	id := c.Param("order-id")

	dao := getEtsDao(c)

	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:    []string{id},
		Limit: 1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	auth := middlewares.GetAuthInfo(c)
	if auth == nil {
		response.HandleErrResponseWithCode(c, http.StatusUnauthorized, nil)
		return
	}

	if !auth.IsADAdmin() {
		if !funk.ContainsString([]string{
			models.EtsOrderStatusOpen,
			models.EtsOrderStatusPendingPayment,
			models.EtsOrderStatusCompleted,
			models.EtsOrderStatusCancelled,
		}, existing.Data[0].Status) {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Can not delete order with status %s", existing.Data[0].Status))
			return
		}
		if existing.Data[0].UserID != auth.UserID {
			response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("Can not delete order created by other user"))
			return
		}
	}

	if err := dao.DeleteServiceOrder([]string{id}); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	response.HandleResponse(c, nil, nil)
}

func DeleteMultiEtsOrder(c *gin.Context) {
	orderIDs := []int{}
	if err := c.BindJSON(&orderIDs); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	orderIDsStr := funk.Map(orderIDs, func(i int) string {
		return strconv.Itoa(i)
	}).([]string)

	dao := getEtsDao(c)
	resp, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:    orderIDsStr,
		Limit: uint64(len(orderIDs)),
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(resp.Data) != len(orderIDs) {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	auth := middlewares.GetAuthInfo(c)

	for i := 0; i < len(resp.Data); i++ {
		if !auth.IsADAdmin() {
			if !funk.ContainsString([]string{
				models.EtsOrderStatusOpen,
				models.EtsOrderStatusPendingPayment,
				models.EtsOrderStatusCompleted,
				models.EtsOrderStatusCancelled,
			}, resp.Data[i].Status) {
				response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Can not delete order %d with status %s", resp.Data[i].ID, resp.Data[i].Status))
				return
			}
			if resp.Data[i].UserID != auth.UserID {
				response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("Can not delete order created by other user"))
				return
			}
		}
	}

	if err := dao.DeleteServiceOrder(orderIDsStr); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	response.HandleResponse(c, nil, nil)
}

type DeleteMultiEtsOrderV2Request struct {
	OrderIDs []int    `json:"order_ids"`
	Status   []string `json:"status"`
}

type DeleteMultiEtsOrderV2Response struct {
	DeletedOrders     []int          `json:"deleted_orders"`
	FailedOrders      []int          `json:"failed_orders"`
	FailedOrderReason map[int]string `json:"failed_order_reason"`
}

func DeleteMultiEtsOrderV2(c *gin.Context) {
	var req DeleteMultiEtsOrderV2Request
	if err := c.BindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	orderIDsStr := funk.Map(req.OrderIDs, func(i int) string {
		return strconv.Itoa(i)
	}).([]string)

	dao := getEtsDao(c)
	auth := middlewares.GetAuthInfo(c)

	orderQuery := models.ServiceOrderFilter{
		Limit: 1000,
	}

	if len(orderIDsStr) == 0 && len(req.Status) == 0 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("order ids and status can not be empty at the same time"))
		return
	}
	if len(orderIDsStr) > 0 {
		orderQuery.ID = orderIDsStr
	}

	if len(req.Status) > 0 {
		orderQuery.Status = req.Status
	}

	if !auth.IsADAdmin() {
		orderQuery.UserID = auth.UserID
	}

	resp, err := dao.QueryServiceOrders(orderQuery)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	result := DeleteMultiEtsOrderV2Response{
		FailedOrderReason: map[int]string{},
		DeletedOrders:     []int{},
		FailedOrders:      []int{},
	}
	for i := 0; i < len(resp.Data); i++ {
		if !auth.IsADAdmin() {
			if !funk.ContainsString([]string{
				models.EtsOrderStatusOpen,
				models.EtsOrderStatusPendingPayment,
				models.EtsOrderStatusCompleted,
				models.EtsOrderStatusCancelled,
			}, resp.Data[i].Status) {
				result.FailedOrders = append(result.FailedOrders, resp.Data[i].ID)
				result.FailedOrderReason[resp.Data[i].ID] = fmt.Sprintf("Can not delete order with status %s", resp.Data[i].Status)
				continue
			}
			if resp.Data[i].UserID != auth.UserID {
				result.FailedOrders = append(result.FailedOrders, resp.Data[i].ID)
				result.FailedOrderReason[resp.Data[i].ID] = "Can not delete order created by other user"
				continue
			}
		}
		result.DeletedOrders = append(result.DeletedOrders, resp.Data[i].ID)
	}

	if err := dao.DeleteServiceOrder(lo.Map(result.DeletedOrders, func(i, index int) string {
		return strconv.Itoa(i)
	})); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    result,
	})
}

func ListProviderEtsTasks(c *gin.Context) {
	req := models.ServiceTaskWithETSFilter{}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if req.Limit <= 0 {
		req.Limit = 10
	}

	auth := middlewares.GetAuthInfo(c)
	if auth == nil || !auth.OrgID.Valid {
		response.HandleErrResponseWithCode(c, http.StatusUnauthorized, nil)
		return
	}

	dao := getEtsDao(c)

	provider, err := dao.GetEtsProviderByOrgID(auth.OrgID.Int64)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	req.ProviderID = provider.ID

	resp, err := dao.QueryServiceProviderTasks(req)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, resp)
}

func GetProviderEtsTask(c *gin.Context) {
	auth := middlewares.GetAuthInfo(c)
	if auth == nil || !auth.OrgID.Valid {
		response.HandleErrResponseWithCode(c, http.StatusUnauthorized, nil)
		return
	}

	dao := getEtsDao(c)

	provider, err := dao.GetEtsProviderByOrgID(auth.OrgID.Int64)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	resp, err := dao.QueryServiceProviderTasks(models.ServiceTaskWithETSFilter{
		ID:         c.Param("task-id"),
		ProviderID: provider.ID,
		Limit:      1,
	})

	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(resp.Data) != 1 {
		response.HandleResponse(c, nil, errors.NotFoundErr)
		return
	}
	// presign S3
	ps := middlewares.GetS3Presigner(c)
	if err := applications.PresignInputPods(ps, resp.Data[0].InputPods, expirationForFiles); err != nil {
		log.Error().Fields(map[string]any{
			"error": err.Error(),
		}).Msg("failed to presign input pods")
	}
	PresignAllFilesInMap(ps, resp.Data[0].OutputFiles, expirationForFiles)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    resp.Data[0],
	})
}

// func UpdateTaskStatus(c *gin.Context) {
// 	var req taskIdsReq
// 	if err := c.ShouldBindJSON(&req); err != nil {
// 		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
// 		return
// 	}
// 	auth := middlewares.GetAuthInfo(c)
// 	if auth == nil || !auth.OrgID.Valid {
// 		response.HandleErrResponseWithCode(c, http.StatusUnauthorized, nil)
// 		return
// 	}

// 	dao := getEtsDao(c)

// 	orders, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
// 		ID:    []string{strconv.Itoa(req.OrderID)},
// 		Limit: 1,
// 	})
// 	if err != nil {
// 		response.HandleResponse(c, nil, err)
// 		return
// 	}

// 	switch req.NewStatus {
// 	case models.EtsTaskStatusOnGoing, models.EtsTaskStatusCancelled, models.EtsTaskStatusDenied, models.EtsTaskStatusReviewed, models.EtsTaskStatusApproved, models.EtsTaskStatusConfirmed, models.EtsTaskStatusNeedMoreDocument:
// 	default:
// 		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid new_status %s", req.NewStatus))
// 		return
// 	}
// 	if err := updateTaskStatus(c, dao, auth, req.TaskIDs, req.NewStatus, orders.Data[0].ServiceOrder); err != nil {
// 		response.HandleResponse(c, nil, err)
// 		return
// 	}

// 	updated, _, err := dao.QueryServiceTasks(map[string]any{"id": req.TaskIDs, "status": req.NewStatus}, 0, 0)
// 	if err != nil {
// 		response.HandleResponse(c, nil, err)
// 		return
// 	}

// 	switch req.NewStatus {
// 	case models.EtsTaskStatusConfirmed:
// 		{
// 			// notification to user if the new status is comfirmed
// 			visaDao, err := middlewares.GetVisaDao(c)
// 			if err != nil {
// 				response.HandleResponse(c, nil, err)
// 				return
// 			}

// 			if err := notificationUpdateTasksStatusToUser(c, visaDao, updated); err != nil {
// 				response.HandleResponse(c, nil, err)
// 				return
// 			}
// 		}
// 	case models.EtsTaskStatusNeedMoreDocument:
// 		{
// 			// reject flow: notification to the AD if the new status is need more document
// 			if err := notification.NotifyNeedMoreDocument(c, orders.Data[0]); err != nil {
// 				response.HandleResponse(c, nil, err)
// 				return
// 			}
// 			if err := notification.NotifyNeedMoreDocumentToUser(c, orders.Data[0], updated[0]); err != nil {
// 				response.HandleResponse(c, nil, err)
// 				return
// 			}
// 			up := map[string]any{"status": models.EtsOrderStatusNeedMoreDocument}
// 			if err := dao.UpdateServiceOrder(up, req.OrderID); err != nil {
// 				response.HandleResponse(c, nil, err)
// 				return
// 			}
// 		}

// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    updated,
// 	})
// }

func updateTaskStatus(c *gin.Context, dao db.IEtsDao, auth *models.AuthInfo, taskIDs []int64, newStatus string, update *models.ServiceOrder) error {
	tasks, _, err := dao.QueryServiceTasks(map[string]any{"id": taskIDs}, 0, 0)
	if err != nil {
		return err
	}
	if len(tasks) == 0 {
		return errors.NotFoundErr
	}
	ids := make([]int64, 0, len(tasks))
	for _, t := range tasks {
		ids = append(ids, t.ID)
	}

	switch newStatus {
	case models.EtsTaskStatusConfirmed, models.EtsTaskStatusOnGoing, models.EtsTaskStatusReviewed, models.EtsTaskStatusApproved, models.EtsTaskStatusDenied, models.EtsTaskStatusCancelled, models.EtsTaskStatusNeedMoreDocument:
		if err = dao.UpdateServiceTaskStatus(newStatus, ids); err != nil {
			return err
		}
	default:
		return fmt.Errorf("invalid status")
	}

	if err = updateServiceOrderByTask(c, dao, auth, ids, update); err != nil {
		return err
	}
	return err
}

func updateServiceOrderByTask(c *gin.Context, dao db.IEtsDao, auth *models.AuthInfo, ids []int64, update *models.ServiceOrder) error {

	tasks, err := dao.QueryServiceRelateTasks(ids)
	if err != nil {
		return err
	}

	// Group tasks by order
	orderTasksMap := map[int][]*models.ServiceTask{}
	for _, task := range tasks {
		orderTasksMap[task.OrderID] = append(orderTasksMap[task.OrderID], task)
	}

	for orderID, orderTasks := range orderTasksMap {
		services, err := dao.QueryExtendedTravelServicesByOrderIDs([]int{orderID})
		if err != nil {
			return err
		}
		// Get tasks with status is confirmed or on-going
		tasksWithStatusConfirmed := funk.Filter(orderTasks, func(t *models.ServiceTask) bool {
			return t.Status == models.EtsTaskStatusOnGoing || t.Status == models.EtsTaskStatusConfirmed
		}).([]*models.ServiceTask)

		// Check if all tasks is done
		if len(tasksWithStatusConfirmed) == len(orderTasks) {
			if err = dao.UpdateServiceOrder(map[string]any{
				"status": models.EtsOrderStatusUnderReview,
			}, orderID); err != nil {
				return err
			}
		}
		// Get tasks with status is approved, denied or canceled
		tasksWithStatusDone := funk.Filter(orderTasks, func(t *models.ServiceTask) bool {
			return t.Status == models.EtsTaskStatusApproved || t.Status == models.EtsTaskStatusDenied || t.Status == models.EtsTaskStatusCancelled
		}).([]*models.ServiceTask)

		// Check if all tasks is done
		if len(tasksWithStatusDone) == len(orderTasks) {
			switch services[orderID].ServiceType {
			case models.EtsServiceTypePassport:
				if err = dao.UpdateServiceOrder(map[string]any{
					"status":         models.EtsOrderStatusCompleted,
					"completed_time": time.Now(),
				}, orderID); err != nil {
					return err
				}
			case models.EtsServiceTypeFastlane:
				if err = dao.UpdateServiceOrder(map[string]any{
					"status":         models.EtsOrderStatusCompleted,
					"completed_time": time.Now(),
				}, orderID); err != nil {
					return err
				}
			}
			// if services[orderID].ServiceType != models.EtsServiceTypeFastlane {
			// 	if err := notification.NotifyFeedbackToUser( orderID); err != nil {
			// 		return err
			// 	}
			// }
		}
	}
	return nil
}

func UpdateEtsTaskProvider(c *gin.Context) {
	var t models.ServiceTask
	if err := c.BindJSON(&t); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	taskID, err := utils.GetIntPathParam(c, "task-id")
	if taskID <= 0 || err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order-id or task-id"))
		return
	}
	auth := middlewares.GetAuthInfo(c)
	if auth == nil || !auth.OrgID.Valid {
		response.HandleErrResponseWithCode(c, http.StatusUnauthorized, nil)
		return
	}

	dao := getEtsDao(c)

	tasks, _, err := dao.QueryServiceTasks(map[string]any{"id": taskID}, 0, 1)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	oldTask := tasks[0]

	newTask, err := updateTaskProvider(dao, int64(taskID), &t)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	orderData, err := dao.QueryServiceOrders(models.ServiceOrderFilter{ID: []string{strconv.Itoa(oldTask.OrderID)}, Limit: 1})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	o := orderData.Data[0]

	// if err := notification.NotifyUpdateOutputPods(c, dao, o.ServiceOrder, &t, o.UserID); err != nil {
	// 	response.HandleResponse(c, nil, err)
	// 	return
	// }

	if err := sendEmailToProviderUserChangeData(c, o, oldTask, &t); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    newTask,
	})
}

func updateTaskProvider(dao db.IEtsDao, taskID int64, update *models.ServiceTask) (*models.ServiceTask, error) {
	tasks, _, err := dao.QueryServiceTasks(map[string]any{"id": taskID}, 0, 1)
	if err != nil {
		return nil, err
	}
	if len(tasks) == 0 {
		return nil, errors.NotFoundErr
	}

	updateTask := map[string]any{}
	if update.InputPods != nil {
		updateTask["input_pods"] = update.InputPods
	}
	if update.OutputPods != nil {
		updateTask["output_pods"] = update.OutputPods
	}

	if update.Note != nil {
		updateTask["note"] = update.Note
	}

	// Update input, output pod
	if err = dao.UpdateServiceTask(updateTask, taskID); err != nil {
		return nil, err
	}

	updated, _, err := dao.QueryServiceTasks(map[string]any{"id": taskID}, 0, 1)
	if err != nil {
		return nil, err
	}
	return updated[0], nil
}

// send notification to user
func notificationUpdateTasksStatusToUser(c *gin.Context, dao db.IDao, tasks []*models.ServiceTask) error {
	daoEts := getEtsDao(c)
	sender := middlewares.GetSimpleQueueSender(c, NotiUserQueue)
	for _, task := range tasks {
		serviceOrder, err := daoEts.GetServiceOrderByID(task.OrderID)
		if err != nil {
			return err
		}
		user, err := dao.GetUserByID(serviceOrder.UserID)
		if err != nil {
			return err
		}
		svcs, err := daoEts.QueryExtendedTravelServices(map[string]any{"id": serviceOrder.ServiceID}, 0, 1)
		if err != nil {
			return err
		}
		msg := map[string]any{
			"action":       "update-task-status",
			"type_product": product.ETSProductType,
			"data": map[string]any{
				"user":                    user,
				"service_order":           serviceOrder,
				"task":                    task,
				"extended_travel_service": svcs[0],
			},
		}
		err = sender.Send(msg)
		if err != nil {
			return err
		}
	}
	return nil
}

type generateETSFormReq struct {
	OrderForms []string           `json:"order_forms"`
	TaskForms  map[int64][]string `json:"task_forms"`
}

func GenerateETSForm(c *gin.Context) {
	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}
	var req generateETSFormReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	dao := getEtsDao(c)
	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:           []string{strconv.Itoa(id)},
		IncludeTasks: true,
		Limit:        1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	auth := middlewares.GetAuthInfo(c)
	if !auth.IsADAdmin() && existing.Data[0].UserID != auth.UserID {
		response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("you don't have permission"))
		return
	}

	if len(req.OrderForms)+len(req.TaskForms) > 0 {
		if err := applications.CallToGenerateETSForm(id, req.OrderForms, req.TaskForms); err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
			})
			return
		}

		for taskID := range req.TaskForms {
			task, ok := lo.Find(existing.Data[0].Tasks, func(t *models.ServiceTask) bool {
				return t.ID == taskID
			})
			if ok && funk.ContainsString([]string{models.EtsTaskStatusOpen, models.EtsTaskStatusFormCreating, models.EtsTaskStatusFormGenerateFailed}, task.Status) {
				if err := dao.UpdateServiceTask(map[string]any{"status": models.EtsTaskStatusFormCreating}, taskID); err != nil {
					c.JSON(http.StatusOK, gin.H{
						"success": false,
					})
					return
				}
			}

		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func GetETSHeader(c *gin.Context) {
	var req models.ServiceOrderFilter
	if err := c.ShouldBindQuery(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	req.Limit = 0

	auth := middlewares.GetAuthInfo(c)

	if !utils.Contain("ad_admin", auth.Roles) {
		if auth.OrgID.Valid {
			req.OrgID = auth.OrgID.Int64
		}

		req.UserID = auth.UserID // admin and user can only see their created order
	}

	dao := getEtsDao(c)

	result := map[string]uint64{}
	{
		req.Status = []string{"open", "pending-payment", "manual_payment"}
		res, err := dao.QueryServiceOrders(req)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		result["draft"] = res.Total
	}
	{
		req.Status = []string{
			"waiting_shipment_user",
			"created_shipment_user",
			"sent_shipment_user",
			"received_user_document",
			"submitted",
			"approved",
			"rejected",
			"lock",
			"paid",
			"dispatched",
			"in_delivery",
			"under_review",
		}
		res, err := dao.QueryServiceOrders(req)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		result["submitted"] = res.Total
	}

	{
		req.Status = []string{
			"completed",
			"sent_visa_package",
		}
		res, err := dao.QueryServiceOrders(req)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		result["completed"] = res.Total
	}
	{
		req.Status = []string{
			"need_more_document",
		}
		res, err := dao.QueryServiceOrders(req)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		result["need-more-document"] = res.Total
	}

	{
		req.Status = []string{
			"under_review", "dispatched",
		}
		res, err := dao.QueryServiceOrders(req)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		result["consulate-in-process"] = res.Total
	}

	{
		req.Status = []string{
			"submitted",
		}
		res, err := dao.QueryServiceOrders(req)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		result["consulate-not-process"] = res.Total
	}

	{
		req.Status = []string{
			"open", "pending-payment", "manual_payment",
			"waiting_shipment_user",
			"created_shipment_user",
			"sent_shipment_user",
			"received_user_document",
			"submitted",
			"approved",
			"rejected",
			"lock",
			"paid",
			"dispatched",
			"in_delivery",
			"under_review",
			"completed",
			"sent_visa_package",
			"need_more_document",
		}
		res, err := dao.QueryServiceOrders(req)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		result["all"] = res.Total
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

type EtsScreeningQuestionsQuery struct {
	Service           string `form:"service"`
	Country           string `form:"service_core_info_country"`
	Tasks             string `form:"service_core_info_tasks"`
	RegionOfResidence string `form:"service_core_info_region_of_residence"`
}

func GetEtsScreeningQuestions(c *gin.Context) {
	var etsQuery EtsScreeningQuestionsQuery
	if err := c.ShouldBindQuery(&etsQuery); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	if etsQuery.Service == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
		})
		return
	}
	dao := getEtsDao(c)
	q := map[string]any{
		"service_type": strings.ToLower(etsQuery.Service),
		"category":     "screening",
	}
	if etsQuery.Country != "" {
		q["country"] = etsQuery.Country
	}
	if etsQuery.Tasks != "" {
		q["tasks"] = strings.ToLower(etsQuery.Tasks)
	}
	if etsQuery.RegionOfResidence != "" {
		q["region_of_residence"] = etsQuery.RegionOfResidence
	}
	res, err := dao.QueryEtsPod(q)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    res,
	})
}

type EtsTasksQuery struct {
	ServiceType       string `form:"service_type"`
	Country           string `form:"service_core_info_country"`
	RegionOfResidence string `form:"service_core_info_region_of_residence"`
	Nationality       string `form:"service_core_info_nationality"`
	Airport           string `form:"service_core_info_airport"`
	EntryDate         string `form:"service_core_info_entry_date"`
	ExitDate          string `form:"service_core_info_exit_date"`
}

func GetAvailableETSTasks(c *gin.Context) {
	var etsQuery EtsTasksQuery
	if err := c.ShouldBindQuery(&etsQuery); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	if etsQuery.ServiceType == "" || etsQuery.Country == "" || etsQuery.RegionOfResidence == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
		})
		return
	}
	q := map[string]any{
		"service_type":        etsQuery.ServiceType,
		"country":             etsQuery.Country,
		"region_of_residence": etsQuery.RegionOfResidence,
	}
	dao := getEtsDao(c)
	res, err := dao.QueryEtsTask(q)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(res) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   errors.NotSupportResidence,
			"success": false,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    res,
	})
}

// country => service_core_info_country
// region_of_residence=> service_core_info_region_of_residence,
// entry_date=>service_core_info_entry_date
// exit_date=>service_core_info_exit_date
// nationality=>service_core_info_nationality

func GetAvailableETSQuery(c *gin.Context) {
	var req EtsTasksQuery
	if err := c.ShouldBindQuery(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if req.ServiceType == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("Missing service_type"))
		return

	}

	q := map[string]any{
		"service_type": req.ServiceType,
		"status":       "active",
	}

	if req.Country != "" {
		q["country"] = req.Country
	}

	if req.RegionOfResidence != "" {
		q["region_of_residence"] = req.RegionOfResidence
	}

	if req.Nationality != "" {
		q["nationality"] = req.Nationality
	}

	if req.Airport != "" {
		q["airport"] = req.Airport
	}

	dao := getEtsDao(c)
	res, err := dao.QueryEts(q)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	result := FilterProductResp{
		Country:           []string{},
		Nationality:       []string{},
		RegionOfResidence: []string{},
		Airport:           []string{},
		EntryDate:         DateFromTo{},
		ExitDate:          DateFromTo{},
	}

	now := time.Now()
	var minProcessingTime int64
	// minProcessingTimeStr := ""
	var minEnterDate *time.Time
	var minTransitTime int64
	for _, v := range res {
		if fieldData := v.Country; fieldData != "" {
			result.Country = append(result.Country, strings.Split(fieldData, ",")...)
		}

		if fieldData := v.Airport; fieldData != nil {
			result.Airport = append(result.Airport, strings.Split(*fieldData, ",")...)
		}

		attrJ := utils.StructToJSON(v.Attributes)
		if fieldData := attrJ.Get("region_of_residence").String(); fieldData != "" {
			result.RegionOfResidence = append(result.RegionOfResidence, strings.Split(fieldData, ",")...)
		}
		if fieldData := attrJ.Get("nationality").String(); fieldData != "" {
			result.Nationality = append(result.Nationality, strings.Split(fieldData, ",")...)
		}

		processingTimeStr := attrJ.Get("processing_time").String()
		if val, err := time_util.ProcessingTimeStrToDuration(processingTimeStr); err == nil {
			if minProcessingTime == 0 || int64(val) < minProcessingTime {
				minProcessingTime = int64(val)
				// minProcessingTimeStr = processingTimeStr
			}
		} else {
			fmt.Println(err)
		}

		entryDate, _, err := dao.VisaDB().CalculateServiceProcessingTime(v.ID)
		if entryDate == nil || err != nil {
			fmt.Println(err)
		} else if minEnterDate == nil || entryDate.Before(*minEnterDate) {
			minEnterDate = entryDate
		}
		fmt.Println(now)
		fmt.Println(processingTimeStr)
		fmt.Println(entryDate)
		fmt.Println(minEnterDate)
		fmt.Println(err)

		if transitTimeStr := attrJ.Get("transit_time").String(); transitTimeStr != "" {
			if val, err := time_util.ProcessingTimeStrToDuration(transitTimeStr); err == nil {
				if minTransitTime == 0 || int64(val) < minTransitTime {
					minTransitTime = int64(val)
				}
			}
		}

	}

	// now := time.Now().Truncate(time.Minute)
	maxDateTime := time.Date(2200, 12, 31, 23, 59, 59, 0, time.UTC)

	// Init value
	result.EntryDate = DateFromTo{
		Value: nil,
		From:  aws.Time(now),
		To:    aws.Time(maxDateTime),
	}

	result.ExitDate = DateFromTo{
		Value: nil,
		From:  aws.Time(now),
		To:    aws.Time(maxDateTime),
	}

	if minEnterDate == nil {
		result.EntryDate = DateFromTo{}
		result.ExitDate = DateFromTo{}
	} else {
		year, month, day := minEnterDate.Date()
		hour, minute, second := minEnterDate.Clock()
		minEnterDate := time.Date(year, month, day, hour, minute, second, 0, time.UTC)
		if req.EntryDate == "" && req.ExitDate == "" {
			result.EntryDate = DateFromTo{
				Value: nil,
				From:  aws.Time(minEnterDate),
				To:    aws.Time(maxDateTime),
			}

			result.ExitDate = DateFromTo{
				Value: nil,
				From:  aws.Time(minEnterDate.Add(time.Duration(minTransitTime))),
				To:    aws.Time(maxDateTime),
			}
		}

		// Calculate exit date by enter date and processing time
		if req.EntryDate != "" && req.ExitDate != "" {
			if val, err := time.Parse(time.RFC3339, req.EntryDate); err == nil {
				result.EntryDate = DateFromTo{
					Value: &val,
					From:  aws.Time(minEnterDate),
					To:    aws.Time(maxDateTime),
				}

				if val2, err := time.Parse(time.RFC3339, req.ExitDate); err == nil {
					result.ExitDate = DateFromTo{
						Value: &val2,
						From:  aws.Time(val.Add(time.Duration(minTransitTime))),
						To:    aws.Time(maxDateTime),
					}
				}

			}
		}

		// Calculate exit date by enter date and processing time
		if req.EntryDate != "" && req.ExitDate == "" {
			if val, err := time.Parse(time.RFC3339, req.EntryDate); err == nil {
				result.EntryDate = DateFromTo{
					Value: &val,
					From:  aws.Time(minEnterDate),
					To:    aws.Time(maxDateTime),
				}

				result.ExitDate = DateFromTo{
					Value: nil,
					From:  aws.Time(val.Add(time.Duration(minTransitTime))),
					To:    aws.Time(maxDateTime),
				}
			}
		}

		// Calculate enter date by exit date and processing time
		if req.EntryDate == "" && req.ExitDate != "" {
			if val, err := time.Parse(time.RFC3339, req.ExitDate); err == nil {

				entryDateTo := val.AddDate(0, 0, -1)
				if entryDateTo.Before(minEnterDate) {
					entryDateTo = maxDateTime
				}
				result.EntryDate = DateFromTo{
					Value: nil,
					From:  aws.Time(minEnterDate),
					To:    aws.Time(entryDateTo),
				}

				result.ExitDate = DateFromTo{
					Value: &val,
					From:  aws.Time(minEnterDate.Add(time.Duration(minTransitTime))),
					To:    aws.Time(maxDateTime),
				}

			}
		}
	}

	result.EntryDate.Valid = true
	result.ExitDate.Valid = true
	if result.EntryDate.Value != nil && result.EntryDate.From != nil && result.EntryDate.To != nil {
		if result.EntryDate.Value.Before(*result.EntryDate.From) || result.EntryDate.Value.After(*result.EntryDate.To) {
			result.EntryDate.Valid = false
		}
	}

	if result.ExitDate.Value != nil && result.ExitDate.From != nil && result.ExitDate.To != nil {
		if result.ExitDate.Value.Before(*result.ExitDate.From) || result.ExitDate.Value.After(*result.ExitDate.To) {
			result.ExitDate.Valid = false
		}
	}

	result.Country = funk.UniqString(result.Country)
	result.Nationality = funk.UniqString(result.Nationality)
	result.RegionOfResidence = funk.UniqString(result.RegionOfResidence)
	result.Airport = funk.UniqString(result.Airport)

	sort.Strings(result.Country)
	sort.Strings(result.Nationality)
	sort.Strings(result.RegionOfResidence)
	sort.Strings(result.Airport)

	if req.Country != "" {
		result.Country = []string{req.Country}
	}
	if req.Nationality != "" {
		result.Nationality = []string{req.Nationality}
	}
	if req.RegionOfResidence != "" {
		result.RegionOfResidence = []string{req.RegionOfResidence}
	}
	if req.Airport != "" {
		result.Airport = []string{req.Airport}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

type EtsPodsQuery struct {
	EtsID       int      `form:"ets_id"`
	Category    []string `form:"category"`
	SubCategory []string `form:"sub_category"`
}

func GetEtsPodsHandler(c *gin.Context) {
	var etsQuery EtsPodsQuery
	if err := c.ShouldBindQuery(&etsQuery); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	if etsQuery.EtsID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
		})
		return
	}
	dao := getEtsDao(c)
	etss, err := dao.QueryExtendedTravelServices(map[string]any{"id": etsQuery.EtsID}, 0, 0)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(etss) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
		})
		return
	}

	pods := filterAndSortETSProductPods(etss[0].Schema, etsQuery.Category, etsQuery.SubCategory, []string{}, []string{})

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    pods,
	})
}

func filterAndSortETSProductPods(pods models.SchemaPods, cats, subCats, ids, names []string) []*models.Pod {
	catMap := map[string]bool{}
	hasCatFilter := false
	for _, c := range cats {
		catMap[c] = true
		hasCatFilter = true
	}
	subCatMap := map[string]bool{}
	hasSubCatFilter := false
	for _, c := range subCats {
		subCatMap[c] = true
		hasSubCatFilter = true
	}
	idMap := map[string]bool{}
	hasIdFilter := false
	for _, s := range ids {
		idMap[s] = true
		hasIdFilter = true
	}
	nameMap := map[string]bool{}
	hasNameFilter := false
	for _, n := range names {
		nameMap[n] = true
		hasNameFilter = true
	}

	var l []*models.Pod
	for _, p := range pods {
		if (hasCatFilter && !catMap[p.Category]) ||
			(hasSubCatFilter && !subCatMap[p.SubCategory]) ||
			(hasIdFilter && !idMap[p.ID]) ||
			(hasNameFilter && !nameMap[p.Name]) {
			continue
		}
		l = append(l, p)
	}

	sort.SliceStable(l, func(i, j int) bool {
		return l[i].Order < l[j].Order
	})
	return l
}

func ETSShipmentUpdateHandler(c *gin.Context) {
	orderID, err := utils.GetIntPathParam(c, "order-id")
	if err != nil || orderID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}
	var req ShipmentRequest
	if err = c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	dao := getEtsDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}
	order, err := dao.GetServiceOrderByID(orderID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}
	if order != nil {
		visaDao, err := middlewares.GetVisaDao(c)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   err.Error(),
				"success": false,
			})
			return
		}
		shipment, err := visaDao.GetVisaShipment(order.ShipmentInfo.String)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   err.Error(),
				"success": false,
			})
			return
		}

		label := models.Label{
			Label:          req.Label,
			TrackingNumber: req.TrackingNumber,
			TrackingURL:    req.TrackingURL,
			Price:          req.Price,
			Currency:       req.Currency,
		}

		if shipment.Labels == nil {
			shipment.Labels = map[string]models.Label{}
		}

		label.LabelHistory = shipment.Labels[req.ShipmentContent].LabelHistory
		if label.LabelHistory == nil {
			label.LabelHistory = map[string]string{}
		}

		label.LabelHistory[req.TrackingNumber] = req.Label

		shipment.Labels[req.ShipmentContent] = label

		if err := visaDao.UpdateVisaShipment(order.ShipmentInfo.String, shipment); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   err.Error(),
				"success": false,
			})
			return
		}

		// update status to ready to submit
		update := map[string]any{"status": req.Status}
		err = dao.UpdateServiceOrder(update, orderID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   err.Error(),
				"success": false,
			})
			return
		}
		// add label file to output_files
		if err = dao.Begin(); err != nil {
			dao.Rollback()
			response.HandleResponse(c, nil, err)
		}
		m := map[string]string{}
		if req.ShipmentContent != "" {
			m[req.ShipmentContent] = req.Label
		}

		if err = dao.AddETSOutputFiles(orderID, m); err != nil {
			dao.Rollback()
			response.HandleResponse(c, nil, err)
		}
		if err = dao.Commit(); err != nil {
			dao.Rollback()
			response.HandleResponse(c, nil, err)
		}
	}
	response.HandleResponse(c, nil, err)
}

func GeneratePresignUrlForTaskUpload(c *gin.Context) {
	var upload UploadRequest
	if err := c.ShouldBindJSON(&upload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	if upload.FileName == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
		return
	}
	orderID, err := utils.GetIntPathParam(c, "order-id")
	if err != nil || orderID <= 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
		})
		return
	}
	taskID := c.Param("task-id")
	if taskID == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order-id or task-id"))
		return
	}
	dao := getEtsDao(c)
	order, err := dao.GetServiceOrderByID(orderID)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	tasks, _, err := dao.QueryServiceTasks(map[string]any{"order_id": orderID, "id": taskID}, 0, 0)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(tasks) == 0 || tasks[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("not found"))
		return
	}

	ps := middlewares.GetS3Presigner(c)
	bucket := middlewares.GetS3Buckets(c)[ApplicationFilesUploadBucketKey].(string)

	var key string
	if order.OrgID != 0 {
		key = file.BuildTaskInputS3Key(upload.FileName, fmt.Sprintf("%d", order.OrgID), order.ID, taskID)
	} else {
		key = file.BuildTaskInputS3Key(upload.FileName, order.UserID, order.ID, taskID)
	}

	var mimeType string
	if !strings.HasPrefix(upload.ContentType, ".") {
		mimeType = mime.TypeByExtension("." + upload.ContentType)
	} else {
		mimeType = mime.TypeByExtension(upload.ContentType)
	}

	uploadURLpresigned, err := ps.PresignUploadUrl(bucket, key, mimeType, 15*time.Minute)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	downURLpresigned, err := ps.PresignUrl(bucket, key, expirationForFiles)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"upload_url":   uploadURLpresigned,
			"download_url": downURLpresigned,
		},
	})
}

type UpdateRequest struct {
	OrderID int    `json:"order_id"`
	Status  string `json:"status"`
}

func ETSUpdateStatusHandler(c *gin.Context) {
	var update UpdateRequest
	if err := c.ShouldBindJSON(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}
	dao := getEtsDao(c)
	q := map[string]any{"status": update.Status}
	if update.Status == models.EtsOrderStatusSubmitted {
		q["submitted_time"] = time.Now()
	}

	err := dao.UpdateServiceOrder(q, update.OrderID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func GetEtsOrdersForProvider(c *gin.Context) {
	var req models.ServiceOrderFilter
	if err := c.ShouldBindQuery(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if req.Limit <= 0 {
		req.Limit = 10
	}

	auth := middlewares.GetAuthInfo(c)

	req.ProviderOrgID = auth.OrgID.Int64

	dao := getEtsDao(c)

	req.IncludePayment = true
	req.IncludeCreator = true
	if funk.ContainsString(req.ServiceType, "fastlane") {
		req.IncludeTasks = true
	}

	if len(req.Status) == 0 {
		req.Status = []string{
			models.EtsOrderStatusSubmitted,
			models.EtsOrderStatusDispatched,
			models.EtsOrderStatusNeedMoreDocument,
			models.EtsOrderStatusUnderReview,
			models.EtsOrderStatusInDelivery,
			models.EtsOrderStatusCompleted,
		}
	}

	res, err := dao.QueryServiceOrders(req)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, res)
}

func GetEtsOrdersHeaderForProvider(c *gin.Context) {
	auth := middlewares.GetAuthInfo(c)
	dao := getEtsDao(c)
	mapHeaderWithStatus := map[string][]string{
		"submitted":          {"submitted", "dispatched"},
		"completed":          {"completed"},
		"need-more-document": {"need_more_document"},
		"all":                {"submitted", "dispatched", "under_review", "need_more_document", "completed"},
	} // map header name with status list

	var req models.ServiceOrderFilter
	if err := c.ShouldBindQuery(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	req.Limit = 1
	req.ProviderOrgID = auth.OrgID.Int64

	result := map[string]uint64{} // map header name with count
	for header, statusList := range mapHeaderWithStatus {
		req.Status = statusList
		res, err := dao.QueryServiceOrders(req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
		result[header] = res.Total

	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func GetEtsOrderForProvider(c *gin.Context) {
	id := c.Param("order-id")
	auth := middlewares.GetAuthInfo(c)

	dao := getEtsDao(c)

	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:              []string{id},
		ProviderOrgID:   auth.OrgID.Int64,
		IncludeTasks:    true,
		IncludeService:  true,
		IncludePrice:    true,
		IncludePayment:  true,
		IncludeShipment: true,
		Limit:           1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	ps := middlewares.GetS3Presigner(c)
	for i := 0; i < len(existing.Data[0].Tasks); i++ {
		presignTask(ps, existing.Data[0].ServiceOrder, existing.Data[0].Tasks[i])
	}
	PresignAllFilesInMap(ps, existing.Data[0].OutputFiles, expirationForFiles)
	response.HandleResponse(c, existing.Data[0], nil)
}

func ValidateEtsOrder(c *gin.Context) {
	id, err := utils.GetIntPathParam(c, "order-id")

	dao := getEtsDao(c)
	auth := middlewares.GetAuthInfo(c)

	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:           []string{strconv.Itoa(id)},
		IncludeTasks: true,
		Limit:        1,
	})

	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) == 0 {
		response.HandleResponse(c, nil, fmt.Errorf("NOT_FOUND"))
		return
	}

	validErr, err := validateEtsOrder(existing.Data[0])
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err,
		})
		return
	}

	if validErr != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   validErr,
		})
		return
	}
	for _, task := range existing.Data[0].Tasks {
		inputPodValues := task.InputPods.ToMapKeyValue()
		passportNumber := cast.ToString(inputPodValues["passport_core_info_passport_number"])
		if passportNumber == "" {
			passportNumber = cast.ToString(inputPodValues["passport_most_recent_pp_pp_book_number"])
		}
		if passportNumber == "" {
			passportNumber = cast.ToString(inputPodValues["passport_most_recent_pp_pp_card_number"])
		}

		delete(inputPodValues, "service_core_info_entry_date")
		delete(inputPodValues, "service_core_info_exit_date")

		if passportNumber != "" {
			if err := dao.CreateServiceCustomerData(auth.UserID, task.GetAppName(""), passportNumber, inputPodValues); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   err,
				})
				return
			}
		}

	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func validateEtsOrder(order *models.ServiceOrderDetail) (validErr, err error) {
	for _, task := range order.Tasks {
		validator, err := task_validation.NewETSTaskValidator(task)
		if err != nil {
			return nil, err
		}

		if _, err := validator.Validate(task); err != nil {
			return err, nil
		}
	}

	return nil, nil
}

func ClearValidateEtsOrderResult(c *gin.Context) {
	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	dao := getEtsDao(c)

	if err := dao.CleanEtsValidateResult(id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func UpdateEtsOrderForProvider(c *gin.Context) {
	var o models.ServiceOrder
	if err := c.ShouldBindJSON(&o); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}
	o.ID = id

	dao := getEtsDao(c)

	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:             []string{strconv.Itoa(id)},
		IncludeTasks:   true,
		IncludeService: true,
		Limit:          1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	auth := middlewares.GetAuthInfo(c)

	res, err := updateEtsOrder(c, dao, auth, existing.Data[0], &o)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	response.HandleResponse(c, res, nil)
}

func DownloadETSDocumentFiles(c *gin.Context) {
	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}

	dao := getEtsDao(c)

	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:             []string{strconv.Itoa(id)},
		IncludeTasks:   true,
		IncludePayment: true,
		Limit:          1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	auth := middlewares.GetAuthInfo(c)
	downloader := middlewares.GetS3Downloader(c)

	res, err := getDownloadFiles(dao, auth, downloader, existing.Data[0])
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	ps := middlewares.GetS3Presigner(c)
	bucket := middlewares.GetS3Buckets(c)[ApplicationFilesUploadBucketKey].(string)
	key := fmt.Sprintf("zip/ets/Order_%d.zip", id)
	if err = ps.UploadFile(bucket, key, res, "application/zip"); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	downURLpresigned, err := ps.PresignUrl(bucket, key, expirationForFiles)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"url": downURLpresigned,
		},
	})
}

func DownloadETSDocumentForm(c *gin.Context) {
	orderID := c.Param("order-id")
	taskID := c.Param("task-id")
	if orderID == "" || taskID == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}

	dao := getEtsDao(c)

	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:             []string{orderID},
		IncludeTasks:   true,
		IncludePayment: true,
		Limit:          1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	downloader := middlewares.GetS3Downloader(c)

	task_id, _ := strconv.ParseInt(taskID, 10, 64)
	order_id, _ := strconv.ParseInt(orderID, 10, 64)
	res, err := getDownloadForm(downloader, existing.Data[0], task_id)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	ps := middlewares.GetS3Presigner(c)
	bucket := middlewares.GetS3Buckets(c)[ApplicationFilesUploadBucketKey].(string)
	key := fmt.Sprintf("zip/ets/Order_%d_%d.zip", order_id, task_id)
	if err = ps.UploadFile(bucket, key, res, "application/zip"); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	downURLpresigned, err := ps.PresignUrl(bucket, key, expirationForFiles)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"url": downURLpresigned,
		},
	})
}

func DownloadETSVisaFiles(c *gin.Context) {
	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}

	dao := getEtsDao(c)

	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:             []string{strconv.Itoa(id)},
		IncludeTasks:   true,
		IncludePayment: true,
		Limit:          1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	auth := middlewares.GetAuthInfo(c)
	downloader := middlewares.GetS3Downloader(c)

	res, err := getDownloadVisaFiles(dao, auth, downloader, existing.Data[0])
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	ps := middlewares.GetS3Presigner(c)
	bucket := middlewares.GetS3Buckets(c)[ApplicationFilesUploadBucketKey].(string)
	key := fmt.Sprintf("zip/ets/Order_%d.zip", id)
	if err = ps.UploadFile(bucket, key, res, "application/zip"); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	downURLpresigned, err := ps.PresignUrl(bucket, key, expirationForFiles)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"url": downURLpresigned,
		},
	})
}

func DownloadETSPdfFile(c *gin.Context) {
	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}

	dao := getEtsDao(c)

	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:             []string{strconv.Itoa(id)},
		IncludeTasks:   true,
		IncludePayment: true,
		Limit:          1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	auth := middlewares.GetAuthInfo(c)
	downloader := middlewares.GetS3Downloader(c)

	res, err := getDownloadPDFSummaryFile(dao, auth, downloader, existing.Data[0])
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	ps := middlewares.GetS3Presigner(c)
	bucket := middlewares.GetS3Buckets(c)[ApplicationFilesUploadBucketKey].(string)
	key := fmt.Sprintf("ets/Order_%d_Summary.pdf", id)
	if err = ps.UploadFile(bucket, key, res, "application/pdf"); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	downURLpresigned, err := ps.PresignUrl(bucket, key, expirationForFiles)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"url": downURLpresigned,
		},
	})
}

func getDownloadFiles(dao db.IEtsDao, auth *models.AuthInfo, downloader *awsSdk.S3Downloader, o *models.ServiceOrderDetail) (string, error) {
	file, err := ioutil.TempFile("", "order_download_")
	if err != nil {
		return "", err
	}
	archive, err := os.Create(file.Name())
	if err != nil {
		return "", err
	}

	defer archive.Close()
	zipWriter := zip.NewWriter(archive)

	// Only send receipt for ad admin and user
	if funk.ContainsString(auth.Roles, "ad_admin") || auth.UserID == o.ServiceOrder.UserID {
		if o.PaymentRaw != nil {
			s3URL := gjson.ParseBytes(*o.PaymentRaw).Get("receipt_file").String()
			if err := copyS3ToZipFile(zipWriter, downloader, s3URL, "Receipt_File"); err != nil {
				return "", err
			}
		}
	}

	utils.StructToJSON(o.OutputFiles).ForEach(func(k, value gjson.Result) bool {
		s3URL := value.Get("file_url").String()
		if err := copyS3ToZipFile(zipWriter, downloader, s3URL, ""); err != nil {
			println(err)
		}
		return true
	})

	for _, task := range o.Tasks {
		inputPods := utils.StructToJSON(task.InputPods)
		outputPods := utils.StructToJSON(task.OutputPods)

		applicantName := getETSNameFromPod(0, inputPods)
		applicantNameWithTaskID := getETSNameFromPod(task.ID, inputPods)

		// Output file
		utils.StructToJSON(task.OutputFiles).ForEach(func(k, value gjson.Result) bool {
			s3URL := value.Get("file_url").String()
			fileName := fmt.Sprintf(`%s/%s_%s`, applicantNameWithTaskID, applicantName, k.String())
			if err := copyS3ToZipFile(zipWriter, downloader, s3URL, fileName); err != nil {
				println(err)
			}
			return true
		})

		paths := recursionPodArrayToPaths(utils.StructToJSON(task.InputPods))

	P:
		for _, path := range paths {
			pod := inputPods.Get(path)
			if !funk.ContainsString([]string{"scan_upload_file", "upload_file", "upload_files"}, pod.Get("type_fe").String()) {
				continue P
			}

			if s3URL := pod.Get("value.fe").String(); s3URL != "" {
				fileName := fmt.Sprintf(`%s/%s_%s`, applicantNameWithTaskID, applicantName, strings.NewReplacer(" ", "_", "/", "_", `\`, "_").Replace(pod.Get("title").String()))
				if err := copyS3ToZipFile(zipWriter, downloader, s3URL, fileName); err != nil {
					return "", err
				}
			}
		}

		outputPaths := recursionPodArrayToPaths(utils.StructToJSON(task.InputPods))

	Q:
		for _, path := range outputPaths {
			pod := outputPods.Get(path)

			if !funk.ContainsString([]string{"scan_upload_file", "upload_file", "upload_files"}, pod.Get("type_fe").String()) {
				continue Q
			}

			if pod.Get("name").String() == "copy_of_photo_full" {
				continue
			}

			if s3URL := pod.Get("value.fe").String(); s3URL != "" {
				fileName := fmt.Sprintf(`%s/%s_%s`, applicantNameWithTaskID, applicantName, strings.NewReplacer(" ", "_", "/", "_", `\`, "_").Replace(pod.Get("title").String()))
				if err := copyS3ToZipFile(zipWriter, downloader, s3URL, fileName); err != nil {
					return "", err
				}
			}
		}
	}

	zipWriter.Close()
	return file.Name(), nil
}

func getDownloadForm(downloader *awsSdk.S3Downloader, o *models.ServiceOrderDetail, taskId int64) (string, error) {
	file, err := ioutil.TempFile("", "order_download_")
	if err != nil {
		return "", err
	}
	archive, err := os.Create(file.Name())
	if err != nil {
		return "", err
	}

	defer archive.Close()
	zipWriter := zip.NewWriter(archive)

	utils.StructToJSON(o.OutputFiles).ForEach(func(k, value gjson.Result) bool {
		s3URL := value.Get("file_url").String()
		if err := copyS3ToZipFile(zipWriter, downloader, s3URL, ""); err != nil {
			println(err)
		}
		return true
	})

	for _, task := range o.Tasks {
		if task.ID == taskId {
			inputPods := utils.StructToJSON(task.InputPods)

			applicantName := getETSNameFromPod(0, inputPods)
			applicantNameWithTaskID := getETSNameFromPod(task.ID, inputPods)

			// Output file
			utils.StructToJSON(task.OutputFiles).ForEach(func(k, value gjson.Result) bool {
				s3URL := value.Get("file_url").String()
				fileName := fmt.Sprintf(`%s/%s_%s`, applicantNameWithTaskID, applicantName, k.String())
				if err := copyS3ToZipFile(zipWriter, downloader, s3URL, fileName); err != nil {
					println(err)
				}
				return true
			})
		}
	}

	zipWriter.Close()
	return file.Name(), nil
}
func getDownloadPDFSummaryFile(dao db.IEtsDao, auth *models.AuthInfo, downloader *awsSdk.S3Downloader, o *models.ServiceOrderDetail) (string, error) {
	file, err := ioutil.TempFile("", "order_download_")
	if err != nil {
		return "", err
	}

	// Initialize PDF
	pdf := gopdf.GoPdf{}
	pageHeight := 841.89 // A4 height in points
	pageWidth := 595.28  // A4 width in points
	margins := 50.0      // margins in points

	pdf.Start(gopdf.Config{PageSize: *gopdf.PageSizeA4})

	tempDir, err := ioutil.TempDir("", "ets_images_")
	if err != nil {
		return "", err
	}
	defer os.RemoveAll(tempDir)

	for _, task := range o.Tasks {
		// Add font
		err = pdf.AddTTFFont("arial", "fonts/arial.ttf")
		pdf.AddPage()
		if err != nil {
			return "", err
		}
		pdf.SetFont("arial", "", 14)

		inputPodValues := task.InputPods.ToMapKeyValue()
		inputPodValuesJ := utils.StructToJSON(inputPodValues)

		// Get passport image from various possible fields
		passportImage := inputPodValuesJ.Get("document_copy_of_document_copy_of_passport_main_page").String()
		if passportImage == "" {
			passportImage = inputPodValuesJ.Get("document_copy_of_document_copy_of_residence_passport").String()
		}
		if passportImage == "" {
			passportImage = inputPodValuesJ.Get("document_copy_of_passport_copy_of_passport_main_page").String()
		}
		if passportImage == "" {
			passportImage = inputPodValuesJ.Get("document_copy_of_passport_copy_of_usa_green_card_front").String()
		}

		// Get optional photo and passport number
		passportPhoto := strings.ReplaceAll(cast.ToString((inputPodValues)["photo_passport_photo_copy_of_photo"]), "-demo", "")
		passportNumber := cast.ToString((inputPodValues)["passport_core_info_passport_number"])
		documentID := utils.StructToJSON(task.FormCallback).Get("vnm_native_visa.data.document_id").String()

		// Define passport image variables
		passportWidth := 300.0
		passportHeight := 200.0 // Default height if no passport image
		var imagePath string
		hasPassportImage := false

		if passportImage != "" {
			imagePath = filepath.Join(tempDir, fmt.Sprintf("passport_%d.jpg", time.Now().UnixNano()))

			// Download passport image
			bucket, key, _ := utils.UrlToS3BucketAndKey(passportImage)
			if err := downloader.DownloadFromS3Bucket(bucket, key, imagePath); err != nil {
				log.Printf("Error downloading passport image: %v", err)
			} else {
				// Get passport image dimensions
				imageFile, err := os.Open(imagePath)
				if err == nil {
					defer imageFile.Close()

					imageConfig, _, err := image.DecodeConfig(imageFile)
					if err == nil {
						// Calculate passport dimensions maintaining aspect ratio
						passportAspectRatio := float64(imageConfig.Height) / float64(imageConfig.Width)
						passportHeight = passportWidth * passportAspectRatio
						hasPassportImage = true
					}
				}
			}
		}

		// Add Order ID
		pdf.SetXY(margins, 30)
		pdf.Cell(nil, fmt.Sprintf("Order ID: %d", o.ID))
		if passportNumber != "" {
			pdf.SetXY(margins, 70)
			pdf.Cell(nil, fmt.Sprintf("Passport Number: %s", passportNumber))
		}

		// Add Document ID and barcode if available
		if documentID != "" {
			// Add Document ID text
			pdf.SetXY(margins, 50)
			pdf.Cell(nil, fmt.Sprintf("Document ID: %s", documentID))

			// Generate barcode
			barcodeImg, err := code128.Encode(documentID)
			if err != nil {
				return "", err
			}

			// Create a new image with desired dimensions
			scaledImg := image.NewRGBA(image.Rect(0, 0, 200, 50))

			// Scale the barcode image
			draw.ApproxBiLinear.Scale(scaledImg, scaledImg.Bounds(), barcodeImg, barcodeImg.Bounds(), draw.Over, nil)

			// Save barcode to temporary file
			barcodePath := "/tmp/barcode.png"
			barcodeFile, err := os.Create(barcodePath)
			if err != nil {
				return "", err
			}
			if err := png.Encode(barcodeFile, scaledImg); err != nil {
				barcodeFile.Close()
				return "", err
			}
			barcodeFile.Close()

			// Add barcode to PDF
			pdf.Image(barcodePath, margins, 65, &gopdf.Rect{
				W: 200,
				H: 50,
			})
		}

		currentY := 130.0 // Starting Y position after header

		// Add passport image if available
		if hasPassportImage && imagePath != "" {
			pdf.Image(imagePath, margins, currentY, &gopdf.Rect{
				W: passportWidth,
				H: passportHeight,
			})
		}
		// No text placeholder when passport image is missing

		// If photo is available, add it
		if passportPhoto != "" {
			photoPath := filepath.Join(tempDir, fmt.Sprintf("photo_%d.jpg", time.Now().UnixNano()))

			// Download passport photo
			photoBucket, photoKey, _ := utils.UrlToS3BucketAndKey(passportPhoto)
			if err := downloader.DownloadFromS3Bucket(photoBucket, photoKey, photoPath); err != nil {
				// Just log the error and continue without the photo
				log.Printf("Error downloading passport photo: %v", err)
			} else {
				// Get passport photo dimensions
				photoFile, err := os.Open(photoPath)
				if err == nil {
					photoConfig, _, err := image.DecodeConfig(photoFile)
					photoFile.Close()

					if err == nil {
						photoWidth := 100.0 // smaller width for the photo
						photoAspectRatio := float64(photoConfig.Height) / float64(photoConfig.Width)
						photoHeight := photoWidth * photoAspectRatio

						// Add photo to PDF
						pdf.Image(photoPath, margins+passportWidth+20, currentY, &gopdf.Rect{
							W: photoWidth,
							H: photoHeight,
						})
					}
				}
				os.Remove(photoPath)
			}
		}

		currentY += passportHeight + 20 // Update Y position after passport section

		// Process additional files
		anotherFiles := []string{}
		inputPodValuesJ.ForEach(func(k, v gjson.Result) bool {
			if funk.ContainsString([]string{
				"document_copy_of_document_copy_of_passport_main_page",
				"document_copy_of_passport_copy_of_passport_main_page",
				"document_copy_of_document_copy_of_residence_passport",
				"document_copy_of_passport_copy_of_usa_green_card_front",
				"photo_passport_photo_copy_of_photo",
			}, k.String()) {
				return true
			}
			if v.IsArray() {
				for _, url := range v.Array() {
					if url.String() != "" && strings.HasPrefix(url.String(), "http") {
						anotherFiles = append(anotherFiles, url.String())
					}
				}
			} else if strVal := v.String(); strings.HasPrefix(strVal, "http") {
				anotherFiles = append(anotherFiles, strVal)
			}
			return true
		})

		for _, fileURL := range anotherFiles {
			if fileURL == "" {
				continue
			}

			// Download file
			bucket, key, err := utils.UrlToS3BucketAndKey(fileURL)
			if err != nil {
				continue
			}

			tempPath := fmt.Sprintf("/tmp/%s", filepath.Base(key))
			if err := downloader.DownloadFromS3Bucket(bucket, key, tempPath); err != nil {
				continue
			}

			// Get image dimensions
			imgFile, err := os.Open(tempPath)
			if err != nil {
				os.Remove(tempPath)
				continue
			}

			imgConfig, _, err := image.DecodeConfig(imgFile)
			imgFile.Close()
			if err != nil {
				os.Remove(tempPath)
				continue
			}

			// Calculate dimensions maintaining aspect ratio
			maxWidth := pageWidth - (margins * 2)
			aspectRatio := float64(imgConfig.Height) / float64(imgConfig.Width)
			width := math.Min(float64(imgConfig.Width), maxWidth)
			height := width * aspectRatio

			// Check if image will fit on current page
			if currentY+height > pageHeight-margins {
				pdf.AddPage()
				currentY = margins
			}

			pdf.Image(tempPath, margins, currentY, &gopdf.Rect{
				W: width,
				H: height,
			})

			currentY += height + 20
			os.Remove(tempPath)
		}
	}

	// Write PDF to temp file
	err = pdf.WritePdf(file.Name())
	if err != nil {
		return "", err
	}

	return file.Name(), nil
}

func getDownloadVisaFiles(dao db.IEtsDao, auth *models.AuthInfo, downloader *awsSdk.S3Downloader, o *models.ServiceOrderDetail) (string, error) {
	file, err := ioutil.TempFile("", "order_download_")
	if err != nil {
		return "", err
	}
	archive, err := os.Create(file.Name())
	if err != nil {
		return "", err
	}

	defer archive.Close()
	zipWriter := zip.NewWriter(archive)

	for _, task := range o.Tasks {
		inputPods := utils.StructToJSON(task.InputPods)
		outputPods := utils.StructToJSON(task.OutputPods)

		applicantName := getETSNameFromPod(0, inputPods)

		outputPaths := recursionPodArrayToPaths(utils.StructToJSON(task.InputPods))

		for _, path := range outputPaths {
			pod := outputPods.Get(path)

			if pod.Get("name").String() != "copy_of_new_visa" {
				continue
			}

			if s3URL := pod.Get("value.fe").String(); s3URL != "" {
				fileName := fmt.Sprintf(`%s_%s`, applicantName, strings.NewReplacer(" ", "_", "/", "_", `\`, "_").Replace(pod.Get("title").String()))
				if err := copyS3ToZipFile(zipWriter, downloader, s3URL, fileName); err != nil {
					return "", err
				}
			}
		}
	}

	zipWriter.Close()
	return file.Name(), nil
}

func recursionPodArrayToPaths(podArrJ gjson.Result) []string {
	result := []string{}
	for i, pod := range podArrJ.Array() {
		result = append(result, fmt.Sprintf("%d", i))
		subPaths := recursionPodPath(pod, fmt.Sprintf("%d", i), []string{})
		result = append(result, subPaths...)
	}
	return result
}

func recursionPodPath(pod gjson.Result, prePath string, paths []string) []string {
	result := paths
	if val := pod.Get("option_choice"); val.String() != "" {
		val.ForEach(func(key, ops gjson.Result) bool {
			opArray := ops.Array()
			for i := range opArray {
				podPath := fmt.Sprintf("%s.option_choice.%s.%d", prePath, key.String(), i)
				result = append(result, podPath)

				subPaths := recursionPodPath(opArray[i], podPath, paths)
				result = append(result, subPaths...)
			}
			return true
		})
	}

	return result
}

func getETSNameFromPod(taskID int64, pod gjson.Result) string {
	arr := pod.Array()
	mapName := map[string]string{}
	for i := range arr {
		pod := arr[i]
		if pod.Get("category").String() == "passport" {
			if name := pod.Get("name").String(); funk.ContainsString([]string{"given_name", "middle_name", "surname"}, name) {
				mapName[name] = pod.Get("value.fe").String()
			}
		}
	}

	names := []string{}
	if val, ok := mapName["given_name"]; ok {
		names = append(names, val)
	}
	if val, ok := mapName["middle_name"]; ok {
		names = append(names, val)
	}

	displayName := strings.Join(names, " ") // given_name middle_name
	if val, ok := mapName["surname"]; ok {
		displayName = val + " " + displayName
	}

	if taskID <= 0 {
		return displayName
	}

	if displayName == "" {
		return strconv.FormatInt(taskID, 10)
	}

	return strconv.FormatInt(taskID, 10) + " - " + slugify.Marshal(displayName)

}
func copyS3ToZipFile(zipWriter *zip.Writer, downloader *awsSdk.S3Downloader, s3URL, prefix string) error {
	s3URL = strings.ReplaceAll(s3URL, "-demo", "")
	s3URLs := []string{}
	if gjson.Parse(s3URL).IsArray() {
		for _, url := range gjson.Parse(s3URL).Array() {
			if url.String() != "" {
				s3URLs = append(s3URLs, url.String())
			}
		}
	} else {
		s3URLs = append(s3URLs, s3URL)
	}

	for i, url := range s3URLs {
		name := filepath.Base(url)
		ext := filepath.Ext(url)
		if prefix != "" {
			if i > 0 {
				name = fmt.Sprintf("%s-%d%s", prefix, i, ext)
			} else {
				name = prefix + ext
			}
		}
		if url == "" {
			continue
		}
		// Parse url to bucket and key
		bucket, key, err := utils.UrlToS3BucketAndKey(url)
		if err != nil {
			return err
		}

		// Get s3 object buffer
		buff, err := downloader.DownloadFromS3BucketToBuffer(bucket, key)
		if err != nil {
			return err
		}

		// Create file
		w1, err := zipWriter.Create(strings.Split(name, "?")[0])
		if err != nil {
			return err
		}

		// Copy buffer to file
		if _, err := io.Copy(w1, bytes.NewBuffer(buff)); err != nil {
			return err
		}

	}

	return nil
}

func UpdateOutputFilesEtsTask(c *gin.Context) {
	var req UpdatePassportReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}
	taskID, err := utils.GetIntPathParam(c, "task-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid task ID"))
		return
	}
	auth := middlewares.GetAuthInfo(c)
	if !utils.Contain("ad_admin", auth.Roles) {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("can't upload output files"))
		return
	}
	dao := getEtsDao(c)
	bucket, key, err := utils.UrlToS3BucketAndKey(req.URL)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid URL"))
		return
	}
	tasks, _, err := dao.QueryServiceTasks(map[string]any{"id": taskID}, 0, 0)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(tasks) != 1 {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("task does not exist"))
		return
	}

	m := map[string]string{req.Form: path.Join(bucket, key)}

	if err := dao.AddETSTaskOutputFiles(int64(taskID), m); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func ETSendEmailToAdmin(c *gin.Context) {
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	etsDao := getEtsDao(c)
	services, err := etsDao.QueryExtendedTravelServices(map[string]any{
		"id": c.Param("service-id"),
	}, 0, 1)

	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	ets := services[0]

	authUser := middlewares.GetAuthInfo(c)
	user, err := dao.GetUserByID(authUser.UserID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	// Send to user
	{
		message := map[string]any{
			"template_name": "contact_us_to_user",
			"to":            user.Email,
			"bcc":           []string{utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String()},
			"parameters": map[string]any{
				"FullName": user.GivenName + " " + user.Surname,
			},
		}
		if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	// Send to admin
	{
		country, _ := gocountry.FindCountryByAlpha(ets.Country)
		message := map[string]any{
			"template_name": "contact_us_to_admin",
			"to":            utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("admin_email").String(),
			"bcc":           []string{utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String()},
			"parameters": map[string]any{
				"FullName": user.GivenName + " " + user.Surname,
				"Email":    user.Email,
				"Country":  country.Name.Common,
				"Service":  strings.Title(ets.ServiceType),
				"Task":     strings.Title(strings.Join(ets.Tasks, " ")),
			},
		}
		if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

type ChangeShipmentRequest struct {
	ServiceType   string `json:"service_type"`
	OrderID       int    `json:"order_id"`
	ShipmentID    string `json:"shipment_id"`
	PromotionCode string `json:"promotion_code"`
}

func ChangeETSShipment(c *gin.Context) {
	var req ChangeShipmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}

	dao := getEtsDao(c)

	update := map[string]any{
		"shipment_info": req.ShipmentID,
		"shipment_order": models.PropertyMap{
			"type":  req.ServiceType,
			"value": req.OrderID,
		},
	}

	if err := dao.UpdateServiceOrder(update, id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:           []string{strconv.Itoa(id)},
		IncludeTasks: true,
		IncludePrice: true,
		Limit:        1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}
	summary.CalculateETSServiceSummary(dao, existing.Data[0], models.CartSelection{
		PromotionCode: req.PromotionCode,
	}, !existing.Data[0].IsPaid())

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func GetOrderSummary(c *gin.Context) {
	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}

	dao := getEtsDao(c)

	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:             []string{strconv.Itoa(id)},
		IncludePayment: true,
		IncludeTasks:   true,
		IncludePrice:   true,
		Limit:          1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	if method := utils.StructToJSON(existing.Data[0].PaymentRaw).Get("method").String(); method != "CORPPAY" {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("Don't allow update summary with method: %s", method))
		return
	}

	summary.CalculateETSServiceSummary(dao, existing.Data[0], models.CartSelection{
		PromotionCode: existing.Data[0].Summary.PromotionCode,
	}, true)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func ETSEmailReminderToProvider(c *gin.Context) {
	etsDao := getEtsDao(c)

	resp, err := etsDao.QueryServiceOrders(models.ServiceOrderFilter{ID: []string{c.Param("order-id")}, IncludeTasks: true, IncludeShipment: true, Limit: 1})
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	order := resp.Data[0]
	etsJ := utils.StructToJSON(order.Service)
	webBaseURL := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("web_base_url").String()

	emailConfigMap := utils.GetMapEnv("ad_email")
	supportEmail := utils.StructToJSON(emailConfigMap).Get("support").String()

	if order.Service.ServiceType == "passport" {
		applications := []map[string]any{}
		for _, task := range order.Tasks {
			applications = append(applications, map[string]any{
				"FullName":          task.GetAppName("passport"),
				"RegionOfResidence": utils.StructToJSON(order.Config).Get("region_of_residence").String(),
				"ProcessingTime":    etsJ.Get("attributes.processing_time").String(),
				"Task":              strings.Join(order.Service.Tasks, ", "),
			})
		}

		provider, err := etsDao.GetEtsProviderByID(order.ProviderID)
		if err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}

		country, _ := gocountry.FindCountryByAlpha(order.Service.Country)

		// Sent email to provider
		var message = map[string]any{
			"template_name": "reminder_vn_passport_consulate",
			"to":            provider.Contact.Email,
			"bcc":           []string{supportEmail},
			"parameters": map[string]any{
				"OrderID":      order.ID,
				"Name":         provider.Name,
				"Country":      country.Name.Common,
				"Applications": applications,
				"URL":          fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", webBaseURL, order.ID),
			},
		}

		// Send email to sqs
		if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	if order.Service.ServiceType == "fastlane" {
		task := order.Tasks[0]

		provider, err := etsDao.GetEtsProviderByID(order.ProviderID)
		if err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
			return
		}
		inputPods := task.InputPods.ToMapKeyValue()
		webBaseURL := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("web_base_url").String()

		var params = map[string]any{
			"OrderID":         order.ID,
			"ProviderName":    provider.Name,
			"Country":         order.Service.Country, //
			"ServiceType":     "Fastlane",
			"Terminal":        strings.Title(etsJ.Get("attributes.terminal").String()),
			"Task":            strings.Title(strings.Join(order.Service.Tasks, " ")),
			"ProcessingTime":  etsJ.Get("attributes.processing_time").String(),
			"NoOfTraveler":    inputPods["travel_passenger_info_no_of_traveler"],
			"AirportName":     inputPods["travel_exit_flight_exit_airport"],
			"FlightNo":        utils.GetStringOrText(inputPods["travel_exit_flight_exit_flight"]),
			"ServiceDateTime": inputPods["travel_exit_flight_exit_timestamp"],
			"URL":             fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", webBaseURL, order.ID),
		}

		if task.Type == "arrival" || task.Type == "vip_arrival" {
			params["FlightNo"] = utils.GetStringOrText(inputPods["travel_enter_flight_enter_flight"])
			params["AirportName"] = inputPods["travel_enter_flight_enter_airport"]
			params["ServiceDateTime"] = inputPods["travel_enter_flight_enter_timestamp"]
		}

		params["ServiceDateTime"] = utils.StructToJSON(params).Get("ServiceDateTime").Time().Format("Mon, 02 Jan 2006 15:04")

		// Sent email to provider
		var message = map[string]any{
			"template_name": "reminder_fastlane_consulate",
			"to":            provider.Contact.Email,
			"bcc":           []string{supportEmail},
			"parameters":    params,
		}
		// Send email to sqs
		if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func ChangeOrderProvider(c *gin.Context) {
	auth := middlewares.GetAuthInfo(c)
	if !auth.IsADAdmin() {
		response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("you don't have permission"))
		return
	}

	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}

	iDao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	dao := getEtsDao(c)

	existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:           []string{strconv.Itoa(id)},
		IncludeTasks: true,
		Limit:        1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if len(existing.Data) != 1 || existing.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	newProviderID := c.Param("provider-id")

	if existing.Data[0].ProviderID == newProviderID {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("provider is not change"))
		return
	}

	provider, err := dao.GetEtsProviderByID(newProviderID)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err := dao.UpdateServiceOrder(map[string]any{
		"provider_id": provider.ID,
	}, id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	order, err := dao.GetServiceOrderByID(existing.Data[0].ID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Send Email To Provider
	if order.IsPaid() {
		localize := middlewares.GetLocalize(c)
		if err := sendEmailToETSProvider(iDao, localize, order); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

type CloneETSOrderRequest struct {
	CloneType           string   `json:"clone_type"` // clone_to_new_order, clone_to_exist_order
	FromOrderID         int      `json:"from_order_id"`
	FromTaskIDs         []int64  `json:"from_task_ids"`
	FromPassportNumbers []string `json:"from_passport_numbers"`
	ToOrderID           int      `json:"to_order_id"`
}

func CloneEtsOrder(c *gin.Context) {
	auth := middlewares.GetAuthInfo(c)
	var req CloneETSOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	dao := getEtsDao(c)

	var fromServiceOrder *models.ServiceOrderDetail
	if req.FromOrderID > 0 {
		q := models.ServiceOrderFilter{
			ID:              []string{cast.ToString(req.FromOrderID)},
			IncludeTasks:    true,
			IncludeService:  true,
			IncludePrice:    true,
			IncludePayment:  true,
			IncludeShipment: true,
			Limit:           1,
		}

		fromOrderResp, err := dao.QueryServiceOrders(q)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

		if len(fromOrderResp.Data) != 1 || fromOrderResp.Data[0] == nil {
			response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
			return
		}

		if !auth.IsADAdmin() && fromOrderResp.Data[0].UserID != auth.UserID {
			response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("you don't have permission"))
			return
		}

		ps := middlewares.GetS3Presigner(c)
		for i := 0; i < len(fromOrderResp.Data[0].Tasks); i++ {
			presignTask(ps, fromOrderResp.Data[0].ServiceOrder, fromOrderResp.Data[0].Tasks[i])
		}
		fromServiceOrder = fromOrderResp.Data[0]
	}

	var (
		newOrderID int
		err        error
	)
	if req.CloneType == "clone_to_new_order" {
		if fromServiceOrder.Service.Status == "inactive" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "inactive_service",
			})
			return
		}
		newOrderID, err = cloneServiceOrder(dao, fromServiceOrder, req.FromTaskIDs)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}
	if req.CloneType == "clone_to_exist_order" {
		toOrderResp, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
			ID:              []string{cast.ToString(req.ToOrderID)},
			IncludeTasks:    true,
			IncludeService:  true,
			IncludePrice:    true,
			IncludePayment:  true,
			IncludeShipment: true,
			Limit:           1,
		})
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

		if len(toOrderResp.Data) != 1 || toOrderResp.Data[0] == nil {
			response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
			return
		}

		if err := cloneToExistServiceOrder(dao, fromServiceOrder, toOrderResp.Data[0], req.FromTaskIDs); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

		newOrderID = req.ToOrderID
	}

	if req.CloneType == "clone_to_exist_order_by_passport_numbers" {
		toOrderResp, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
			ID:              []string{cast.ToString(req.ToOrderID)},
			IncludeTasks:    true,
			IncludeService:  true,
			IncludePrice:    true,
			IncludePayment:  true,
			IncludeShipment: true,
			Limit:           1,
		})
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

		if len(toOrderResp.Data) != 1 || toOrderResp.Data[0] == nil {
			response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
			return
		}

		if err := cloneToExistServiceOrderByPassportNumbers(dao, toOrderResp.Data[0], req.FromPassportNumbers); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

		newOrderID = req.ToOrderID
	}

	newOrder, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:              []string{cast.ToString(newOrderID)},
		IncludeTasks:    true,
		IncludeService:  true,
		IncludePrice:    true,
		IncludePayment:  true,
		IncludeShipment: true,
		Limit:           1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, newOrder)

}

func cloneServiceOrder(dao db.IEtsDao, order *models.ServiceOrderDetail, taskIDs []int64) (int, error) {
	as, err := dao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return 0, err
	}

	order.ServiceOrder.Status = models.EtsOrderStatusOpen
	order.ServiceOrder.ID = 0         // Reset  ID
	order.ServiceOrder.PaymentID = "" // Reset payment ID

	orderInputPods, orderOutputPods := ETSToOrderInputOutputPods(dao, order.QueryPodValues, as[0].OrderSchema)
	orderInputPods.SetFEValue("service_core_info_entry_date", nil)
	orderInputPods.SetFEValue("service_core_info_exit_date", nil)

	order.InputPods = funk.Values(orderInputPods).([]*models.InputPod)
	order.OutputPods = funk.Values(orderOutputPods).([]*models.InputPod)

	if err := dao.CreateServiceOrder(order.ServiceOrder); err != nil {
		return 0, err
	}

	for _, task := range order.Tasks {
		if len(taskIDs) > 0 && !funk.ContainsInt64(taskIDs, task.ID) {
			continue
		}
		newTask := models.ServiceTask{
			OrderID:      order.ServiceOrder.ID,
			Status:       models.EtsTaskStatusOpen,
			Type:         task.Type,
			OutputFiles:  nil,
			OutputPods:   nil,
			FormCallback: nil,
		}

		inputPods, outputPods := ETSToInputOutputPods(dao, order.QueryPodValues, as[0], &models.AuthInfo{
			UserID: order.ServiceOrder.UserID,
			OrgID:  null.IntFrom(int64(order.ServiceOrder.OrgID)),
		}, false)

		taskInput := getEmptyInputPodsForTask(&(as[0].Schema), (*as[0]).Tasks[0])
		taskInput.Merge(&inputPods)

		newTask.InputPods = funk.Values(taskInput).([]*models.InputPod)
		newTask.InputPods.FillByPodValues(task.InputPods.ToMapKeyValue(), true)

		newTask.InputPods.SetFEValue("service_core_info_entry_date", nil)
		newTask.InputPods.SetFEValue("service_core_info_exit_date", nil)

		taskOutput := getEmptyOutPodsForTask(&(as[0].Schema), (*as[0]).Tasks[0])
		taskOutput.Merge(&outputPods)
		newTask.OutputPods = funk.Values(taskOutput).([]*models.InputPod)

		if err := updateOutputPodByInputPods(dao, &newTask); err != nil {
			return 0, err
		}

		newTask.OutPodValues = newTask.OutputPods.ToMapKeyValue()
		if err := dao.CreateServiceTask(&newTask); err != nil {
			return 0, err
		}

	}

	return order.ServiceOrder.ID, nil
}

func cloneToExistServiceOrder(dao db.IEtsDao, fromOrder, toOrder *models.ServiceOrderDetail, fromTaskIDs []int64) error {
	as, err := dao.QueryExtendedTravelServices(map[string]any{"id": toOrder.ServiceID}, 0, 1)
	if err != nil {
		return err
	}

	// Clean up old tasks
	if utils.StructToJSON(toOrder.Service.Attributes).Get("single_application").Bool() {
		if len(fromTaskIDs) > 1 {
			return fmt.Errorf("can not clone multi applications to the single application product")
		}
		for _, task := range toOrder.Tasks {
			if err := dao.DeleteETSTask([]string{cast.ToString(task.ID)}); err != nil {
				return err
			}
		}

	}

	for _, task := range fromOrder.Tasks {
		if len(fromTaskIDs) > 0 && !funk.ContainsInt64(fromTaskIDs, task.ID) {
			continue
		}

		newTask := models.ServiceTask{
			OrderID:      toOrder.ServiceOrder.ID,
			Type:         task.Type,
			Status:       models.EtsTaskStatusOpen,
			OutputPods:   nil,
			FormCallback: nil,
		}

		inputPods, outputPods := ETSToInputOutputPods(dao, toOrder.QueryPodValues, as[0], &models.AuthInfo{
			UserID: toOrder.ServiceOrder.UserID,
			OrgID:  null.IntFrom(int64(toOrder.ServiceOrder.OrgID)),
		}, false)

		taskInput := getEmptyInputPodsForTask(&(as[0].Schema), (*as[0]).Tasks[0])
		taskInput.Merge(&inputPods)
		newTask.InputPods = funk.Values(taskInput).([]*models.InputPod)
		newTask.InputPods.FillByPodValues(task.InputPods.ToMapKeyValue(), true)

		passportNumber := cast.ToString(task.InputPods.ToMapKeyValue()["passport_core_info_passport_number"])
		existInputPodValues, err := dao.GetServiceCustomerData(passportNumber, "")
		if err != nil {
			return err
		}

		newTask.InputPods.FillByPodValues(existInputPodValues, true)
		newTask.InputPods.FillByPodValues(cast.ToStringMap(fromOrder.QueryPodValues), false)
		// newTask.InputPods.SetFEValue("service_core_info_entry_date", nil)
		// newTask.InputPods.SetFEValue("service_core_info_exit_date", nil)

		taskOutput := getEmptyOutPodsForTask(&(as[0].Schema), (*as[0]).Tasks[0])
		taskOutput.Merge(&outputPods)
		newTask.OutputPods = funk.Values(taskOutput).([]*models.InputPod)

		if err := updateOutputPodByInputPods(dao, &newTask); err != nil {
			return err
		}

		if err := dao.CreateServiceTask(&newTask); err != nil {
			return err
		}
	}

	return nil
}

func updateOutputPodByInputPods(dao db.IEtsDao, newTask *models.ServiceTask) error {
	orderResp, _ := dao.QueryServiceOrders(models.ServiceOrderFilter{ID: []string{cast.ToString(newTask.OrderID)}, IncludeService: true, Limit: 1})
	inputToOutputPods := map[string]string{}
	if funk.ContainsString(notification.URGENT_PRODUCT_NAMES, orderResp.Data[0].Service.Name) {
		inputToOutputPods = map[string]string{
			"travel_visa_info_registration_code":  "application_application_info_registration_code",
			"travel_visa_info_registration_email": "application_application_info_registration_email",
		}
	}

	inputPodValues := newTask.InputPods.ToMapKeyValue()
	outputPodValues := newTask.OutputPods.ToMapKeyValue()
	count := 0
	for k, v := range inputToOutputPods {
		if val := cast.ToString(inputPodValues[k]); val != "" {
			outputPodValues[v] = val
			count++
		}
	}

	if count == 2 {
		outputPodValues["application_application_info_application_status"] = "submitted"
		notes := []string{}

		if val := cast.ToStringSlice(inputPodValues["travel_visa_info_evisa_error"]); len(val) > 0 {
			reasons := []string{}
			for _, reason := range val {
				if reasonL, ok := notification.REASON_MAPPING[reason]; ok {
					reasons = append(reasons, reasonL)
				} else {
					reasons = append(reasons, reason)
				}
			}

			notes = append(notes, fmt.Sprintf("Problem: %s", strings.Join(reasons, ", ")))
		}

		if val := cast.ToString(inputPodValues["travel_visa_info_registration_code"]); val != "" {
			notes = append(notes, fmt.Sprintf("Code: %s", val))
		}
		if val := cast.ToTime(inputPodValues["passport_core_info_date_of_birth"]).Format("02/01/2006"); val != "" {
			notes = append(notes, fmt.Sprintf("DOB: %s", val))
		}
		if val := cast.ToString(inputPodValues["travel_visa_info_registration_email"]); val != "" {
			notes = append(notes, fmt.Sprintf("Email: %s", val))
		}

		outputPodValues["application_application_info_note"] = strings.Join(notes, "\n")
	}

	newTask.OutputPods.FillByPodValues(outputPodValues, false)

	newTask.OutPodValues = newTask.OutputPods.ToMapKeyValue()
	return nil
}

func cloneToExistServiceOrderByPassportNumbers(dao db.IEtsDao, toOrder *models.ServiceOrderDetail, fromPassportNumbers []string) error {
	as, err := dao.QueryExtendedTravelServices(map[string]any{"id": toOrder.ServiceID}, 0, 1)
	if err != nil {
		return err
	}

	// Clean up old tasks
	if utils.StructToJSON(toOrder.Service.Attributes).Get("single_application").Bool() {
		if len(fromPassportNumbers) > 1 {
			return fmt.Errorf("can not clone multi applications to the single application product")
		}
		for _, task := range toOrder.Tasks {
			if err := dao.DeleteETSTask([]string{cast.ToString(task.ID)}); err != nil {
				return err
			}
		}

	}

	for _, passportNumber := range fromPassportNumbers {
		newTask := models.ServiceTask{
			Type:         (*as[0]).Tasks[0],
			OrderID:      toOrder.ServiceOrder.ID,
			Status:       models.EtsTaskStatusOpen,
			OutputPods:   nil,
			FormCallback: nil,
		}

		inputPods, outputPods := ETSToInputOutputPods(dao, toOrder.QueryPodValues, as[0], &models.AuthInfo{
			UserID: toOrder.ServiceOrder.UserID,
			OrgID:  null.IntFrom(int64(toOrder.ServiceOrder.OrgID)),
		}, false)

		taskInput := getEmptyInputPodsForTask(&(as[0].Schema), (*as[0]).Tasks[0])
		taskInput.Merge(&inputPods)
		newTask.InputPods = funk.Values(taskInput).([]*models.InputPod)
		existInputPodValues, err := dao.GetServiceCustomerData(passportNumber, "")
		if err != nil {
			return err
		}

		newTask.InputPods.FillByPodValues(existInputPodValues, true)
		newTask.InputPods.SetFEValue("service_core_info_entry_date", nil)
		newTask.InputPods.SetFEValue("service_core_info_exit_date", nil)

		taskOutput := getEmptyOutPodsForTask(&(as[0].Schema), (*as[0]).Tasks[0])
		taskOutput.Merge(&outputPods)
		newTask.OutputPods = funk.Values(taskOutput).([]*models.InputPod)

		if err := updateOutputPodByInputPods(dao, &newTask); err != nil {
			return err
		}

		newTask.OutPodValues = newTask.OutputPods.ToMapKeyValue()
		if err := dao.CreateServiceTask(&newTask); err != nil {
			return err
		}
	}

	return nil
}

func QueryCustomerPassportNumber(c *gin.Context) {
	auth := middlewares.GetAuthInfo(c)
	query := c.Query("query")
	dao := getEtsDao(c)
	result, err := dao.QueryServiceCustomerData(auth.UserID, query)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// SendSMS ...
func SendSMS(phone, message string) error {
	resp, err := resty.New().R().SetHeader("Content-Type", "application/json").SetBody(map[string]any{
		"number":  phone,
		"message": message,
	}).Post(`https://sms.ariadirectcorp.com/send`)
	if err != nil {
		return err
	}
	fmt.Println(resp.String())
	return nil
}
