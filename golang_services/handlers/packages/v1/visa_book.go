package v1

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	aderr "bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func ListVisaBooks(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	visaBooks, err := listVisaBooks(dao, travelInfo.ID)
	if err != nil {
		response.HandleResponse(c, nil, err)
	}

	presigner := middlewares.GetS3Presigner(c)

	for i := 0; i < len(visaBooks); i++ {
		if !visaBooks[i].VisaImage.Valid || visaBooks[i].VisaImage.String == "" {
			continue
		}
		bucket, key, err := utils.UrlToS3BucketAndKey(visaBooks[i].VisaImage.String)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		url, err := presigner.PresignUrl(bucket, key, 24*time.Hour)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

		visaBooks[i].VisaImage.SetValid(url)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    visaBooks,
	})
}

func listVisaBooks(dao db.IDao, travelerID string) ([]*models.VisaBook, error) {
	visaBooks, err := dao.QueryVisaBook(map[string]any{"traveler_id": travelerID})
	if err != nil {
		return nil, err
	}
	return visaBooks, nil
}

func AddVisaBook(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	var newVisaBook models.VisaBook
	if err := c.ShouldBind(&newVisaBook); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	newVisaBook.UserID = travelInfo.UserID
	newVisaBook.TravelerID = travelInfo.ID
	added, err := addVisaBook(dao, &newVisaBook)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    added,
	})
}

func addVisaBook(dao db.IDao, visaBook *models.VisaBook) (*models.VisaBook, error) {
	id, err := dao.InsertVisaBook(visaBook)
	if err != nil {
		return nil, err
	}
	return dao.GetVisaBookByID(id)
}

func UpdateVisaBook(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	id, err := utils.GetIntPathParam(c, "visa-book-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	var update models.VisaBook
	if err := c.ShouldBind(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	updated, err := updateVisaBook(dao, travelInfo.ID, id, &update)
	if err != nil {
		response.HandleResponse(c, nil, err)
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    updated,
	})
}

func updateVisaBook(dao db.IDao, travelerID string, id int, update *models.VisaBook) (*models.VisaBook, error) {
	existing, err := dao.GetVisaBookByID(id)
	if err != nil {
		return nil, err
	}
	if existing == nil || existing.TravelerID != travelerID {
		return nil, aderr.NotFoundErr
	}

	update.LastRemind = nil

	if err = dao.UpdateVisaBook(id, update); err != nil {
		return nil, err
	}
	return dao.GetVisaBookByID(id)
}

func DeleteVisaBook(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	id, err := utils.GetIntPathParam(c, "visa-book-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err = deleteVisaBook(dao, travelInfo.ID, id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func deleteVisaBook(dao db.IDao, travelerID string, id int) error {
	existing, err := dao.GetVisaBookByID(id)
	if err != nil {
		return err
	}
	if existing == nil || existing.TravelerID != travelerID {
		return aderr.NotFoundErr
	}

	if err = dao.DeleteVisaBook(id); err != nil {
		return err
	}
	return nil
}
