package v1

import (
	"fmt"
	"net/http"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares/ets_mw"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/usps"
	"github.com/gin-gonic/gin"
)

func GetBusinessDays(c *gin.Context) {
	rep, err := usps.PostalBusinessDays(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rep,
	})
}

func GetPostOffice(c *gin.Context) {
	zipCode := c.Query("zip_code")
	radius := c.Query("radius")
	date := c.Query("date")
	if zipCode == "" || radius == "" {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "zip_code and radius are required",
		})
		return
	}
	if date == "" {
		current_time := time.Now()
		date = fmt.Sprintf("%d%02d%02d", current_time.Year(), current_time.Month(), current_time.Day())
	}
	rep, err := usps.FacilityScheduleSearch(c, zipCode, radius, date, true)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rep["facilityDetails"],
	})
}

func GetAppoimentDate(c *gin.Context) {
	fdbId := c.Query("fdbId")
	if fdbId == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "fdbId query param is required",
		})
		return
	}
	rep, err := usps.AppointmentDateSearch(c, fdbId, true)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rep["dates"],
	})
}

func GetAppointmentTime(c *gin.Context) {
	fdbId := c.Query("fdbId")
	if fdbId == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "fdbId: query param is required",
		})
		return
	}
	date := c.Query("date")
	if fdbId == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "date: query param is required",
		})
		return
	}
	rep, err := usps.AppointmentTimeSearch(c, fdbId, date, true)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rep["appointmentTimeDetailExtended"],
	})
}

func CreateAppointment(c *gin.Context) {
	order := ets_mw.GetEtsOrderDetailFromCtx(c)

	var data models.BookingAppointmentInfo
	if err := c.ShouldBindJSON(&data); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	if data.FdbID == "" || data.Date == "" || data.Time == "" || data.FirstName == "" || data.LastName == "" || data.Phone == "" || data.Email == "" {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, nil)
		return
	}

	rep, err := usps.CreateAppointment(c, data, true)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	scheduling := rep["scheduling"].(map[string]any)
	data.ConfirmationCode = scheduling["confirmationNumber"].(string)

	rep1, err := usps.GetAppointmentByConfirmation(c, data.ConfirmationCode)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	scheduling1 := rep1["scheduling"].(map[string]any)
	data.ConfirmationNumber = scheduling1["confirmationNumber"].(string)
	data.PostOffice = map[string]any{
		"address": scheduling1["postOfficeAddress"],
		"name":    scheduling1["postOfficeName"],
		"phone":   scheduling1["postOfficePhone"],
	}

	dao := getEtsDao(c)
	(*order.ServiceOrder.Config)["booking-appointment"] = data
	if err := dao.UpdateServiceOrder(map[string]any{"config": order.ServiceOrder.Config}, order.ID); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}

func CancelAppointment(c *gin.Context) {
	order := ets_mw.GetEtsOrderDetailFromCtx(c)
	if bp, ok := (*order.ServiceOrder.Config)["booking-appointment"].(map[string]any); ok {
		if code, ok1 := bp["confirmation-code"].(string); ok1 {
			rep, err := usps.GetAppointmentByConfirmation(c, code)
			if err != nil {
				response.HandleResponse(c, nil, err)
				return
			}
			scheduling := rep["scheduling"].(map[string]any)
			status := scheduling["status"].(string)
			if status == "OPEN" {
				err := usps.CancelAppointmentByConfirmation(c, code)
				if err != nil {
					response.HandleResponse(c, nil, err)
					return
				}
			}
			rep, err = usps.GetAppointmentByConfirmation(c, code)
			if err != nil {
				response.HandleResponse(c, nil, err)
				return
			}
			scheduling = rep["scheduling"].(map[string]any)
			status = scheduling["status"].(string)
			if status == "CANCELLED" {
				dao := getEtsDao(c)
				delete((*order.ServiceOrder.Config), "booking-appointment")
				if err := dao.UpdateServiceOrder(map[string]any{"config": order.ServiceOrder.Config}, order.ID); err != nil {
					response.HandleResponse(c, nil, err)
					return
				}
			}
			c.JSON(http.StatusOK, gin.H{
				"success": true,
			})
		} else {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, nil)
			return
		}
	} else {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, nil)
		return
	}

}
