package v1

import (
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
	"gorm.io/datatypes"
	"gorm.io/gorm"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func GetAdminDashboard(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}

	dao := middlewares.GetORMDao(c)

	result, err := getAdminDashboard(dao)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": err.<PERSON>rror(),
		})
		return
	}

	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func getAdminDashboard(dao *gorm.DB) (map[string]any, error) {
	var (
		totalUser                         int64
		totalVisaOrder, totalETSOrder     int64
		totalVisaService, totalETSService int64
		totalVisaRevenue, totalETSRevenue int64
	)

	if err := dao.Table("users").Where("role = ?", "user").Count(&totalUser).Error; err != nil {
		return nil, err
	}

	if err := dao.Table("service_orders").Where("deleted_at IS NOT NULL").Count(&totalETSOrder).Error; err != nil {
		return nil, err
	}

	totalVisaService = 1 // Visa

	if err := dao.Raw("SELECT COUNT(*) FROM  (SELECT DISTINCT service_type FROM ets) v").Scan(&totalETSService).Error; err != nil {
		return nil, err
	}

	if err := dao.Raw("SELECT SUM(cast(summary->>'total' as integer)) FROM service_orders WHERE status IN ('completed', 'sent_visa_package')").Scan(&totalETSRevenue).Error; err != nil {
		return nil, err
	}

	result := map[string]any{
		"total_order":   totalVisaOrder + totalETSOrder,
		"total_user":    totalUser,
		"total_service": totalVisaService + totalETSService,
		"total_revenue": totalVisaRevenue + totalETSRevenue,
	}

	return result, nil
}

func GetAdminDashboardForUser(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}
	req := adminDashboardForUserFilter{}
	if err := c.BindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 10
	}

	dao := middlewares.GetORMDao(c)

	result, err := getAdminDashboardForUser(dao, req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

type adminDashboardForUserFilter struct {
	From      *time.Time `form:"from"`
	To        *time.Time `form:"to"`
	SortField string     `form:"sort_field"`
	SortOrder string     `form:"sort_order"`
	Limit     uint64     `form:"limit"`
	Offset    uint64     `form:"offset"`
	Query     string     `form:"query"`
}
type adminDashboardForUserItem struct {
	ID                   string          `json:"id"`
	Name                 string          `json:"name"`
	Email                string          `json:"email"`
	LastActivity         *time.Time      `json:"last_activity"`
	Corporation          *datatypes.JSON `json:"corporation"`
	TotalOrder           int64           `json:"total_order"`
	TotalInCompleteOrder int64           `json:"total_incomplete_order"`
}
type adminDashboardForUser struct {
	Data    []adminDashboardForUserItem `json:"data"`
	Limit   uint64                      `json:"limit"`
	Offset  uint64                      `json:"offset"`
	Total   int64                       `json:"total"`
	Success bool                        `json:"success"`
}

func getAdminDashboardForUser(dao *gorm.DB, filter adminDashboardForUserFilter) (*adminDashboardForUser, error) {
	query := squirrel.StatementBuilder.Select("*").From(`(
		SELECT u.id, CONCAT_WS(' ', u.given_name, u.surname) "name", u.email, coalesce(u.last_logon_at,u.created_at) last_activity, to_jsonb(c) corporation,
		(
			(select count(*) from service_orders so where user_id = u.id and deleted_at IS NULL)
		) total_order,
		(
			(select count(*) from service_orders so where user_id = u.id and status IN ('open', 'pending-payment', 'manual_payment') and deleted_at IS NULL)
		) total_incomplete_order
		FROM users u
		LEFT JOIN corporation c ON c.org_id = u.organization_id
		WHERE u.role IN ('user', 'admin') AND ( coalesce(u.organization_id,0) = 0 OR c.id IS NOT NULL)
	) v`)

	if filter.From != nil {
		query = query.Where("v.last_activity >= ?", filter.From)
	}

	if filter.To != nil {
		query = query.Where("v.last_activity <= ?", filter.To)
	}

	if strings.ToLower(filter.SortOrder) != "asc" {
		filter.SortOrder = "desc"
	}

	if len(filter.Query) > 0 {
		queryString := "%" + filter.Query + "%"
		queryConditions := []squirrel.Sqlizer{}
		queryConditions = append(queryConditions, squirrel.ILike{"email::text": queryString})
		queryConditions = append(queryConditions, squirrel.ILike{"name::text": queryString})
		query = query.Where(squirrel.Or(queryConditions))
	}

	sortFieldMap := map[string]string{
		"name":                   `v.name`,
		"last_activity":          `v.last_activity`,
		"total_order":            `v.total_order`,
		"total_incomplete_order": `v.total_incomplete_order`,
	}

	if order, ok := sortFieldMap[filter.SortField]; ok {
		query = query.OrderBy(order + " " + filter.SortOrder)
	} else {
		query = query.OrderBy("v.last_activity " + filter.SortOrder)
	}

	query = query.Limit(filter.Limit).Offset(filter.Offset)
	sql, args, _ := query.ToSql()
	rows, err := dao.Raw(sql, args...).Rows()

	if err != nil {
		return nil, err
	}
	defer rows.Close()
	result := &adminDashboardForUser{
		Data:    []adminDashboardForUserItem{},
		Limit:   filter.Limit,
		Offset:  filter.Offset,
		Success: true,
	}

	for rows.Next() {
		item := adminDashboardForUserItem{}
		if err := rows.Scan(&item.ID, &item.Name, &item.Email, &item.LastActivity, &item.Corporation, &item.TotalOrder, &item.TotalInCompleteOrder); err != nil {
			return nil, err
		}
		result.Data = append(result.Data, item)
	}

	sqlCount := utils.QueryCountItemV2(sql)

	if err := dao.Raw(sqlCount, args...).Scan(&result.Total).Error; err != nil {
		return nil, err
	}

	return result, nil
}

func GetAdminDashboardForProvider(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}
	req := adminDashboardForProviderFilter{}
	if err := c.BindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 10
	}

	dao := middlewares.GetORMDao(c)

	result, err := getAdminDashboardForProvider(dao, req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

type adminDashboardForProviderFilter struct {
	From      *time.Time `form:"from"`
	To        *time.Time `form:"to"`
	Limit     uint64     `form:"limit"`
	Offset    uint64     `form:"offset"`
	SortField string     `form:"sort_field"`
	SortOrder string     `form:"sort_order"`
	Query     string     `form:"query"`
}
type adminDashboardForProviderItem struct {
	ID                                                                                               string `json:"id"`
	OrgID                                                                                            int64  `json:"org_id"`
	ProviderID                                                                                       string `json:"provider_id"`
	Name                                                                                             string `json:"name"`
	Type                                                                                             string `json:"type"`
	TotalInProcessVisaOrder, TotalInProcessETSOrder, TotalCompletedVisaOrder, TotalCompletedETSOrder int64  `json:"-"`
	TotalInProcessOrder                                                                              int64  `json:"total_in_process_order"`
	TotalCompletedOrder                                                                              int64  `json:"total_completed_order"`
	TotalOrder                                                                                       int64  `json:"total_order"`
}
type adminDashboardForProvider struct {
	Data    []adminDashboardForProviderItem `json:"data"`
	Limit   uint64                          `json:"limit"`
	Offset  uint64                          `json:"offset"`
	Total   int64                           `json:"total"`
	Success bool                            `json:"success"`
}

func getAdminDashboardForProvider(dao *gorm.DB, filter adminDashboardForProviderFilter) (*adminDashboardForProvider, error) {
	if filter.From == nil {
		filter.From = aws.Time(time.Now().AddDate(-100, 0, 0))
	}

	if filter.To == nil {
		filter.To = aws.Time(time.Now().AddDate(+100, 0, 0))
	}

	query := squirrel.StatementBuilder.Select(`CONCAT_WS('-', o.type, o.id), o.id, o.name, o.type, o.profile`).
		From("v_organizations o").
		Where("o.type IN ('consulate','ets_provider')").
		GroupBy("o.id, o.name, o.profile, o.type, o.created_at")

	query = query.Column(`(SELECT COUNT(*) FROM service_orders so 
		WHERE provider_id = o.profile
		AND status NOT IN ('completed', 'sent_visa_package') and created_at >= ? and created_at <= ?
		AND deleted_at IS NULL)`, filter.From, filter.To)

	query = query.Column(`(SELECT COUNT(*) FROM service_orders so
		WHERE provider_id = o.profile
		AND status IN ('completed', 'sent_visa_package') and created_at >= ? and created_at <= ?
		AND deleted_at IS NULL)`, filter.From, filter.To)

	if strings.ToUpper(filter.SortOrder) != "ASC" {
		filter.SortOrder = "DESC"
	}
	if funk.ContainsString([]string{"type", "name"}, filter.SortField) {
		query = query.OrderBy("o." + filter.SortField + " " + filter.SortOrder)
	} else {
		query = query.OrderBy("o.created_at DESC")
	}

	sqlCount := squirrel.StatementBuilder.Select(`COUNT(*)`).
		From("v_organizations o").
		Where("o.type IN ('consulate','ets_provider')")

	if len(filter.Query) > 0 {
		queryString := "%" + filter.Query + "%"
		query = query.Where(`o.name::text ILIKE ?`, queryString)
		sqlCount = sqlCount.Where(`o.name::text ILIKE ?`, queryString)
	}

	sqlStr, args, _ := query.ToSql()

	rows, err := dao.Raw(sqlStr, args...).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	result := &adminDashboardForProvider{
		Data:    []adminDashboardForProviderItem{},
		Limit:   filter.Limit,
		Offset:  filter.Offset,
		Success: true,
	}

	for rows.Next() {
		item := adminDashboardForProviderItem{}
		if err := rows.Scan(&item.ID, &item.OrgID, &item.Name, &item.Type, &item.ProviderID, &item.TotalInProcessVisaOrder, &item.TotalInProcessETSOrder, &item.TotalCompletedVisaOrder, &item.TotalCompletedETSOrder); err != nil {
			return nil, err
		}

		item.TotalCompletedOrder = item.TotalCompletedVisaOrder + item.TotalCompletedETSOrder
		item.TotalInProcessOrder = item.TotalInProcessVisaOrder + item.TotalInProcessETSOrder
		item.TotalOrder = item.TotalCompletedOrder + item.TotalInProcessOrder

		if item.TotalOrder > 0 {
			result.Data = append(result.Data, item)
		}
	}
	result.Total = int64(len(result.Data))
	offset := filter.Offset + filter.Limit
	if offset > uint64(result.Total) {
		offset = uint64(result.Total)
	}
	result.Data = result.Data[filter.Offset:offset]
	return result, nil
}

func GetAdminDashboardForPayment(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}
	req := adminDashboardForPaymentFilter{}
	if err := c.BindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 10
	}

	dao := middlewares.GetORMDao(c)

	result, err := getAdminDashboardForPayment(dao, req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

type adminDashboardForPaymentFilter struct {
	From      *time.Time `form:"from"`
	To        *time.Time `form:"to"`
	Status    []string   `form:"status"`
	SortField string     `form:"sort_field"`
	SortOrder string     `form:"sort_order"`
	Limit     uint64     `form:"limit"`
	Offset    uint64     `form:"offset"`
}
type adminDashboardForPaymentItem struct {
	CartID   int        `json:"cart_id"`
	Method   string     `json:"method"`
	Status   string     `json:"status"`
	Currency string     `json:"currency"`
	Date     *time.Time `json:"date"`
	Total    float64    `json:"total"`
	Remain   float64    `json:"remain"`
}
type adminDashboardForPayment struct {
	Data    []adminDashboardForPaymentItem `json:"data"`
	Limit   uint64                         `json:"limit"`
	Offset  uint64                         `json:"offset"`
	Total   int64                          `json:"total"`
	Success bool                           `json:"success"`
}

func getAdminDashboardForPayment(dao *gorm.DB, filter adminDashboardForPaymentFilter) (*adminDashboardForPayment, error) {
	query := squirrel.StatementBuilder.Select("c.id, p.method, p.status, p.currency, p.created_at, p.total, p.properties->>'remain'").
		From("cart c").
		LeftJoin("payment p ON p.id = c.payment_id").
		Where("(select count(*) from cart_item where cart_id = c.id) > 0").
		Where("p.id IS NOT NULL AND p.method <> ''").
		Limit(filter.Limit).
		Offset(filter.Offset)
	if len(filter.Status) > 0 {
		query = query.Where("p.status IN ?", filter.Status)
	}
	if filter.From != nil {
		query = query.Where("p.created_at >= ?", filter.From)
	}
	if filter.To != nil {
		query = query.Where("p.created_at <= ?", filter.To)
	}

	sortFieldMap := map[string]string{
		"cart_id": "c.id",
		"method":  "method",
		"status":  "p.status",
		"date":    "p.created_at",
		"total":   "total",
	}

	if strings.ToLower(filter.SortOrder) != "desc" {
		filter.SortOrder = "asc"
	}

	if order, ok := sortFieldMap[filter.SortField]; ok {
		query = query.OrderBy(order + " " + filter.SortOrder)
	} else {
		query = query.OrderBy("p.status = 'open' desc, p.status = 'pending' desc, p.status = 'success' desc, p.id DESC")
	}

	queryStr, args, _ := query.ToSql()

	rows, err := dao.Raw(queryStr, args...).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	result := &adminDashboardForPayment{
		Data:    []adminDashboardForPaymentItem{},
		Limit:   filter.Limit,
		Offset:  filter.Offset,
		Success: true,
	}

	if err := dao.Raw(utils.QueryCountItemV2(queryStr), args...).Scan(&result.Total).Error; err != nil {
		return nil, err
	}

	for rows.Next() {
		item := adminDashboardForPaymentItem{}
		if err := rows.Scan(&item.CartID, &item.Method, &item.Status, &item.Currency, &item.Date, &item.Total, &item.Remain); err != nil {
			return nil, err
		}

		result.Data = append(result.Data, item)
	}
	return result, nil
}

func GetAdminDashboardForService(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}
	req := adminDashboardForServiceFilter{}
	if err := c.BindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	dao := middlewares.GetORMDao(c)

	result, err := getAdminDashboardForService(dao, req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

type adminDashboardForServiceFilter struct {
	From      *time.Time `form:"from"`
	To        *time.Time `form:"to"`
	SortField string     `form:"sort_field"`
	SortOrder string     `form:"sort_order"`
}
type adminDashboardForServiceItem struct {
	Service        string `json:"service"`
	OpenOrder      int64  `json:"open_order"`
	SubmittedOrder int64  `json:"submitted_order"`
	CompletedOrder int64  `json:"completed_order"`
	TotalOrder     int64  `json:"total_order"`
}
type adminDashboardForService struct {
	Data    []adminDashboardForServiceItem `json:"data"`
	Success bool                           `json:"success"`
}

func getAdminDashboardForService(dao *gorm.DB, filter adminDashboardForServiceFilter) (*adminDashboardForService, error) {
	result := &adminDashboardForService{
		Data:    []adminDashboardForServiceItem{},
		Success: true,
	}

	if filter.From == nil {
		filter.From = aws.Time(time.Now().AddDate(-100, 0, 0))
	}

	if filter.To == nil {
		filter.To = aws.Time(time.Now().AddDate(+100, 0, 0))
	}

	for _, service := range []string{"fast_truck", "nexus", "sentri", "tsa_precheck", "fastlane", "passport", "global_entry", "id_photo"} {
		var visaService adminDashboardForServiceItem
		if err := dao.Raw(`
		SELECT 
			'`+service+`' service,
			(SELECT COUNT(*) FROM service_orders so LEFT JOIN ets e ON e.id = so.service_id WHERE e.service_type = ? AND so.status IN ? AND so.deleted_at IS NULL AND so.created_at >= ? AND so.created_at <= ?) open_order,
			(SELECT COUNT(*) FROM service_orders so LEFT JOIN ets e ON e.id = so.service_id WHERE e.service_type = ? AND so.status IN ? AND so.deleted_at IS NULL AND so.created_at >= ? AND so.created_at <= ?) submitted_order,
			(SELECT COUNT(*) FROM service_orders so LEFT JOIN ets e ON e.id = so.service_id WHERE e.service_type = ? AND so.status IN ? AND so.deleted_at IS NULL AND so.created_at >= ? AND so.created_at <= ?) completed_order
		`,
			service, []string{"open", "pending-payment", "manual_payment"},
			filter.From, filter.To,
			service, []string{"waiting_shipment_user", "created_shipment_user", "sent_shipment_user", "received_user_document", "submitted", "approved", "rejected", "lock", "paid", "dispatched", "in_delivery", "under_review"},
			filter.From, filter.To,
			service, []string{"completed", "sent_visa_package"},
			filter.From, filter.To,
		).Scan(&visaService).Error; err != nil {
			return nil, err
		}
		visaService.TotalOrder = visaService.OpenOrder + visaService.SubmittedOrder + visaService.CompletedOrder
		result.Data = append(result.Data, visaService)
	}

	if filter.SortField == "service" {
		sort.Slice(result.Data, func(i, j int) bool {
			if strings.ToUpper(filter.SortOrder) == "ASC" {
				return result.Data[i].Service < result.Data[j].Service
			} else {
				return result.Data[i].Service > result.Data[j].Service
			}
		})
	}

	return result, nil
}
