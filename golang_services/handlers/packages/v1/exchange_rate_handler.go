package v1

import (
	"database/sql"
	"fmt"
	"math"
	"net/http"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
)

// GetExchangeRate get exchange rates value
func GetExchangeRate(c *gin.Context) {
	var req = models.ExchangeRateFilter{}
	if err := c.BindQuery(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err := getExchangeRate(dao, &req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    req,
	})
}

func getExchangeRate(dao db.IDao, req *models.ExchangeRateFilter) error {
	if req == nil {
		return nil
	}
	rate := &models.ExchangeRate{
		FromCurrency: req.FromCurrency,
		ToCurrency:   req.ToCurrency,
	}

	if err := dao.GetExchangeRate(rate); err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("Rate not found! ")
		}
		return err
	}

	if rate.Rate <= 0 {
		return fmt.Errorf("Rate value: %f is invalid! ", rate.Rate)
	}

	if rate.Round <= 0 {
		return fmt.Errorf("Round value: %f is invalid! ", rate.Round)
	}

	req.ToValues = make([]float64, len(req.FromValues))

	for i, input := range req.FromValues {
		req.ToValues[i] = input * rate.Rate                                                    // exchange each input to value
		req.ToValues[i] = math.Ceil(float64(req.ToValues[i]/rate.Round)) * float64(rate.Round) // Alway round to ceil
	}

	return nil
}
