package v1

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/response"
)

func GetAvailableDateByProcessingTime(c *gin.Context) {
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	etsID, err := strconv.Atoi(c.Query("ets_id"))
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	readyTime, _, err := dao.DB().CalculateServiceProcessingTime(etsID)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	year, month, day := readyTime.Date()
	hour, minute, second := readyTime.Clock()
	readyTime2 := time.Date(year, month, day, hour, minute, second, 0, time.UTC)

	c.<PERSON>(http.StatusOK, gin.H{
		"success":           true,
		"ready_time":        readyTime2,
		"ready_time_buffer": readyTime2,
	})
}

func GetAvailableDateByProcessingTimeWithInput(c *gin.Context) {
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	etsID, err := strconv.Atoi(c.Query("ets_id"))
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	fmt.Println(c.Query("from_time"))
	inputTime, err := time.Parse(time.RFC3339, c.Query("from_time"))
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	readyTime, err := dao.DB().GetProcessingTimeWithInput(etsID, inputTime.In(time.UTC), c.Query("processing_time"))
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	year, month, day := readyTime.Date()
	hour, minute, second := readyTime.Clock()
	readyTime2 := time.Date(year, month, day, hour, minute, second, 0, time.UTC)

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"ready_time": readyTime2,
	})
}
