package v1

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	aderr "bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func ListPassportBooks(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	passportBooks, err := listPassportBooks(dao, travelInfo.ID)
	if err != nil {
		response.HandleResponse(c, nil, err)
	}

	presigner := middlewares.GetS3Presigner(c)
	for i := 0; i < len(passportBooks); i++ {
		if !passportBooks[i].PassportImage.Valid || passportBooks[i].PassportImage.String == "" {
			continue
		}
		bucket, key, err := utils.UrlToS3BucketAndKey((passportBooks[i].PassportImage.String))
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

		url, err := presigner.PresignUrl(bucket, key, 24*time.Hour)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		passportBooks[i].PassportImage.SetValid(url)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    passportBooks,
	})
}

func listPassportBooks(dao db.IDao, travelerID string) ([]*models.PassportBook, error) {
	passportBooks, err := dao.QueryPassportBook(map[string]any{"traveler_id": travelerID})
	if err != nil {
		return nil, err
	}
	return passportBooks, nil
}

func AddPassportBook(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	var newPassportBook models.PassportBook
	if err := c.ShouldBind(&newPassportBook); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	newPassportBook.UserID = travelInfo.UserID
	newPassportBook.TravelerID = travelInfo.ID
	added, err := addPassportBook(dao, &newPassportBook)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    added,
	})
}

func addPassportBook(dao db.IDao, passportBook *models.PassportBook) (*models.PassportBook, error) {
	var primaryPassport *models.PassportBook
	var err error
	if passportBook.IsPrimary == true {
		if primaryPassport, err = getPrimaryPassportBook(dao, passportBook.TravelerID); err != nil {
			return nil, err
		}
	}

	if err := dao.Begin(); err != nil {
		dao.Rollback()
		return nil, err
	}

	if primaryPassport != nil {
		primaryPassport.IsPrimary = false
		if err = dao.UpdatePassportBook(primaryPassport.ID, primaryPassport); err != nil {
			dao.Rollback()
			return nil, err
		}
	}

	id, err := dao.InsertPassportBook(passportBook)
	if err != nil {
		dao.Rollback()
		return nil, err
	}

	if err = dao.Commit(); err != nil {
		dao.Rollback()
		return nil, err
	}

	return dao.GetPassportBookByID(id)
}

func UpdatePassportBook(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	id, err := utils.GetIntPathParam(c, "passport-book-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	var update models.PassportBook
	if err := c.ShouldBind(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	updated, err := updatePassportBook(dao, travelInfo.ID, id, &update)
	if err != nil {
		response.HandleResponse(c, nil, err)
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    updated,
	})
}

func updatePassportBook(dao db.IDao, travelerID string, id int, update *models.PassportBook) (*models.PassportBook, error) {
	existing, err := dao.GetPassportBookByID(id)
	if err != nil {
		return nil, err
	}
	if existing == nil || existing.TravelerID != travelerID {
		return nil, aderr.NotFoundErr
	}

	update.LastRemind = nil

	var primaryPassport *models.PassportBook
	if update.IsPrimary == true {
		if primaryPassport, err = getPrimaryPassportBook(dao, travelerID); err != nil {
			return nil, err
		}
	}

	if err := dao.Begin(); err != nil {
		dao.Rollback()
		return nil, err
	}

	if primaryPassport != nil && primaryPassport.ID != id {
		primaryPassport.IsPrimary = false
		if err = dao.UpdatePassportBook(primaryPassport.ID, primaryPassport); err != nil {
			dao.Rollback()
			return nil, err
		}
	}

	if err = dao.UpdatePassportBook(id, update); err != nil {
		dao.Rollback()
		return nil, err
	}

	if err = dao.Commit(); err != nil {
		dao.Rollback()
		return nil, err
	}

	return dao.GetPassportBookByID(id)
}

func DeletePassportBook(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	id, err := utils.GetIntPathParam(c, "passport-book-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err = deletePassportBook(dao, travelInfo.ID, id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func deletePassportBook(dao db.IDao, travelerID string, id int) error {
	existing, err := dao.GetPassportBookByID(id)
	if err != nil {
		return err
	}
	if existing == nil || existing.TravelerID != travelerID {
		return aderr.NotFoundErr
	}

	if err = dao.DeletePassportBook(id); err != nil {
		return err
	}
	return nil
}

func getPrimaryPassportBook(dao db.IDao, travelerID string) (*models.PassportBook, error) {
	passportBooks, err := dao.QueryPassportBook(map[string]any{"traveler_id": travelerID, "is_primary": true})
	if err != nil {
		return nil, err
	}
	if len(passportBooks) == 0 {
		return nil, nil
	}
	return passportBooks[0], nil
}
