package v1

import (
	"errors"
	"fmt"
	"math"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/gin-gonic/gin"
	"github.com/rs/xid"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"

	aderr "bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/payments"
	"bitbucket.org/persistence17/aria/golang_services/sdk/summary"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

// GetCartSummary get cart summary
func GetCartSummary(c *gin.Context) {
	var cartSelection models.CartSelection
	if err := c.Bind<PERSON>(&cartSelection); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	cartData, err := getCartItems(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	etsDao := getEtsDao(c)

	result, err := getCartSummary(dao, etsDao, cartData, cartSelection)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func getCartSummary(dao db.IDao, etsDao db.IEtsDao, cartData *models.CartData, cartSelection models.CartSelection) (*models.ServiceSummary, error) {
	if cartData.Total() == 0 {
		return nil, fmt.Errorf("No items in cart! ")
	}

	if len(cartSelection.ItemsSelected) == 0 {
		return nil, fmt.Errorf("No items selected for checkout! ")
	}

	if cartSelection.Currency == "" {
		cartSelection.Currency = "USD"
	}

	var shippingPrices, subTotalPrices, totalPrices, totalTransactionFees, totalDiscounts []float64

	for _, cartItem := range cartData.ServiceData {
		if !cartSelection.Includes(cartItem) {
			cartSelection.ItemsSkip = append(cartSelection.ItemsSkip, cartItem)
			continue
		}

		serviceSummary, err := summary.CalculateETSServiceSummary(etsDao, cartItem.ServiceDetail, cartSelection, !cartItem.ServiceDetail.IsPaid())
		if err != nil {
			return nil, err
		}

		shippingPrice := serviceSummary.ShippingPrice
		// Convert to 1 currency
		if serviceSummary.Currency != cartSelection.Currency {
			exchangeReq := models.ExchangeRateFilter{
				FromCurrency: serviceSummary.Currency,
				ToCurrency:   cartSelection.Currency,
				FromValues:   []float64{shippingPrice, serviceSummary.UnitPrice, serviceSummary.SubTotal, serviceSummary.Total, serviceSummary.Discount, serviceSummary.TransactionFee},
			}

			if err := getExchangeRate(dao, &exchangeReq); err != nil {
				return nil, err
			}
			shippingPrice = exchangeReq.ToValues[0]
			serviceSummary.UnitPrice = exchangeReq.ToValues[1]
			serviceSummary.SubTotal = exchangeReq.ToValues[2]
			serviceSummary.Total = exchangeReq.ToValues[3]
			serviceSummary.Discount = exchangeReq.ToValues[4]
			serviceSummary.TransactionFee = exchangeReq.ToValues[5]
		}

		shippingPrices = append(shippingPrices, shippingPrice)
		subTotalPrices = append(subTotalPrices, serviceSummary.SubTotal)
		totalPrices = append(totalPrices, serviceSummary.Total)
		totalDiscounts = append(totalDiscounts, serviceSummary.Discount)
		totalTransactionFees = append(totalTransactionFees, serviceSummary.TransactionFee)
	}

	summary := &models.ServiceSummary{
		Currency:      cartSelection.Currency,
		ShippingPrice: funk.SumFloat64(shippingPrices),
		SubTotal:      funk.SumFloat64(subTotalPrices),
		Total:         funk.SumFloat64(totalPrices),
		Discount:      funk.SumFloat64(totalDiscounts),
	}

	if val, ok := payments.TransactionFee[cartSelection.Method]; ok {
		summary.TransactionFee = math.Ceil(summary.Total * val) // Buffer 3% for transaction fee
		summary.Total += summary.TransactionFee
	}

	return summary, nil
}

// Checkout All Item In Cart
func CheckoutCart(c *gin.Context) {
	result, data, err := checkoutCart(c)
	if errors.Is(err, aderr.CannotCheckoutLockedCartErr) {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err,
			"data":    data,
		})
		return
	}
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// checkoutCart checkout all items in cart
func checkoutCart(c *gin.Context) (any, any, error) {
	cartData, err := getCartItems(c)
	if err != nil {
		return nil, nil, err
	}

	if cartData.Total() == 0 {
		return nil, nil, fmt.Errorf("No items in cart! ")
	}

	var cartSelection models.CartSelection
	if err = c.BindJSON(&cartSelection); err != nil {
		return nil, nil, err
	}

	// if cartSelection.Method == "" {
	// 	return nil, nil, fmt.Errorf("payment method is missing")
	// }

	if len(cartSelection.ItemsSelected) == 0 {
		return nil, nil, fmt.Errorf("No items selected for checkout! ")
	}

	for _, cartItem := range cartData.ServiceData {
		if cartSelection.Includes(cartItem) {
			if cartItem.Status == models.CartItemStatus.ModifyLock {
				return nil, cartItem, aderr.CannotCheckoutLockedCartErr
			}
		}
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return nil, nil, err
	}

	etsDao := getEtsDao(c)

	// Begin Transaction
	if err = dao.Begin(); err != nil {
		return nil, nil, err
	}

	// Check Transaction
	var transErr bool
	defer func() {
		if transErr {
			_ = dao.Rollback()
		} else {
			_ = dao.Commit()
		}
	}()

	paymentDao := &db.PaymentsDao{
		Db: dao.DB().Db,
	}

	if cartSelection.Currency == "" {
		cartSelection.Currency = "USD"
	}

	var totalPrices []float64
	for _, cartItem := range cartData.ServiceData {
		if !cartSelection.Includes(cartItem) {
			cartSelection.ItemsSkip = append(cartSelection.ItemsSkip, cartItem)
			continue
		}
		cartSelection.ServiceDataSelected = append(cartSelection.ServiceDataSelected, cartItem)

		serviceSummary, err := summary.CalculateETSServiceSummary(etsDao, cartItem.ServiceDetail, cartSelection, true)
		if err != nil {
			return nil, nil, err
		}

		// Convert to 1 currency
		if serviceSummary.Currency != cartSelection.Currency {
			exchangeReq := models.ExchangeRateFilter{
				FromCurrency: serviceSummary.Currency,
				ToCurrency:   cartSelection.Currency,
				FromValues:   []float64{serviceSummary.Total, serviceSummary.SubTotal},
			}

			if err := getExchangeRate(dao, &exchangeReq); err != nil {
				return nil, nil, err
			}
			serviceSummary.SubTotal = exchangeReq.ToValues[0]
			serviceSummary.Total = exchangeReq.ToValues[1]
		}

		totalPrices = append(totalPrices, serviceSummary.Total)
	}

	if len(totalPrices) == 0 {
		transErr = true
		return nil, nil, fmt.Errorf("No items can process payment! ")
	}

	cartPayment := models.Payment{
		ID:     xid.New().String(),
		Method: cartSelection.Method,
	}

	// Get currency
	cartPayment.Currency = cartSelection.Currency
	cartPayment.Count += len(totalPrices)
	cartPayment.Total += funk.SumFloat64(totalPrices)

	if val, ok := payments.TransactionFee[cartSelection.Method]; ok {
		transactionFee := math.Ceil(cartPayment.Total * val) // Buffer 3% for transaction fee
		cartPayment.Total += transactionFee
	}

	// Get cart summary
	summary, err := getCartSummary(dao, etsDao, cartData, cartSelection)
	if err != nil {
		transErr = true
		return nil, nil, err
	}

	if cartPayment.Total != summary.Total {
		transErr = true
		return nil, nil, aderr.PaymentAmountNotMatchErr
	}

	cartPayment.Properties = &models.PropertyMap{
		"summary": summary,
		"paid":    0,
		"total":   cartPayment.Total,
		"remain":  cartPayment.Total,
	}

	// Add new cart payment
	now := time.Now()
	cartPayment.Status = models.PaymentStatusOpen
	cartPayment.CreatedAt = now
	cartPayment.UpdatedAt = now
	if err = paymentDao.InsertPayment(&cartPayment); err != nil {
		transErr = true
		return nil, nil, err
	}

	if err = dao.UpdateCart(cartData.Cart.ID, map[string]any{
		"payment_id": cartPayment.ID,
		// "is_current": false,
		"status": models.CartStatus.Checkout,
	}); err != nil {
		transErr = true
		return nil, nil, err
	}

	// Moved not selected items to new cart
	/*
		if len(cartSelection.ItemsSkip) > 0 {
			authInfo := middlewares.GetAuthInfo(c)
			cart := models.Cart{
				UserID: authInfo.UserID,
			}

			if err = dao.CreateCart(&cart); err != nil {
				transErr = true
				return nil, nil, err
			}

			for _, item := range cartSelection.ItemsSkip {
				// remove not select item
				if err = dao.RemoveItemFromCart(item.ID); err != nil {
					transErr = true
					return nil, nil, err
				}

				if err = dao.CheckItemBeforeAddToCart(item); err != nil {
					continue
				}

				// add item to new cart
				item.CartID = cart.ID
				if err = dao.AddItemToCart(&item); err != nil {
					transErr = true
					return nil, nil, err
				}
			}
		}
	*/
	for _, item := range cartSelection.ItemsSkip {
		// remove not select item
		if err = dao.RemoveItemFromCart(item.ID); err != nil {
			transErr = true
			return nil, nil, err
		}
	}

	// Update picked services
	if len(cartSelection.ServiceDataSelected) > 0 {
		orderIDs := funk.Map(cartSelection.ServiceDataSelected, func(ci models.CartItem) int {
			return ci.ServiceDetail.ID
		})

		if _, err = dao.DB().Db.PSQL.Update("service_orders").
			Set("payment_id", cartPayment.ID).
			Set("status", models.EtsOrderStatusPendingPayment).
			Set("order_time", time.Now()).
			Set("updated_at", time.Now()).
			Where(squirrel.Eq{"id": orderIDs}).RunWith(dao.DB().Tx).Exec(); err != nil {
			transErr = true
			return nil, nil, err
		}
	}

	if err = dao.Commit(); err != nil {
		transErr = true
		return nil, nil, err
	}

	return &cartPayment, nil, nil
}

func ReCalculateCartItem(dao db.IDao, cartData *models.CartData) error {
	if cartData == nil {
		return nil
	}

	etsDao := &db.EtsDao{
		Db: dao.DB().Db,
	}

	paymentDao := &db.PaymentsDao{
		Db: dao.DB().Db,
	}

	cartSelection := models.CartSelection{
		Currency:      cartData.Payment.Currency,
		Method:        cartData.Payment.Method,
		PromotionCode: cartData.Payment.PromotionCode,
		ItemsSelected: []int{},
	}
	for _, item := range cartData.ServiceData {
		cartSelection.ItemsSelected = append(cartSelection.ItemsSelected, item.ID)
	}
	// Get cart summary
	summary, err := getCartSummary(dao, etsDao, cartData, cartSelection)
	if err != nil {
		return err
	}

	cartData.Payment.Total = summary.Total
	cartData.Payment.Properties = &models.PropertyMap{
		"summary": summary,
		"paid":    funk.ShortIf(cartData.Payment.Status == models.PaymentStatusSuccess, summary.Total, 0),
		"total":   summary.Total,
		"remain":  funk.ShortIf(cartData.Payment.Status == models.PaymentStatusSuccess, 0, summary.Total),
	}

	if err := paymentDao.UpdatePayment(cartData.Payment); err != nil {
		return err
	}
	return nil
}

func GetCartPaymentOptions(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	var filter models.CartPaymentOptionFilter

	if err := c.ShouldBindQuery(&filter); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	var result []*PaymentOptions

	// Add corp payment option
	codeOpts, err := corporationPaymentOptions(dao, authInfo, filter.Currency)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	result = append(result, codeOpts...)

	// Get payment options by currency
	opts := availablePaymentOptionByCurrency(filter.Currency)
	for i := range opts {
		if opts[i].Method == payments.ZellePay {
			opts[i].Data = map[string]any{
				"zellepay_contact_email": c.GetString("zellepay_contact_email"),
				"zellepay_contact_phone": c.GetString("zellepay_contact_phone"),
			}
		}
		if opts[i].Method == payments.BankPay {
			opts[i].Data = map[string]any{
				"bank_1_name":   c.GetString("bank_1_name"),
				"bank_1_owner":  c.GetString("bank_1_owner"),
				"bank_1_number": c.GetString("bank_1_number"),
			}
		}
	}

	result = append(result, opts...)

	// If Authorize.net is enabled, add indirect method
	if !authInfo.IsGuest() {
		for _, opt := range result {
			if opt.Method == payments.AuthorizePay {
				result = append(result, &PaymentOptions{Method: payments.AuthorizeInDirect, Data: map[string]any{"src_method": payments.AuthorizePay}, Currency: "USD"})
			}

			if opt.Method == payments.PayPal {
				result = append(result, &PaymentOptions{Method: payments.PayPalInDirect, Data: map[string]any{"src_method": payments.PayPal}, Currency: "USD"})
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func LockCart(c *gin.Context) {
	if err := lockCart(c); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func lockCart(c *gin.Context) error {
	// Get cart
	cartData, err := getCartItems(c)
	if err != nil {
		return err
	}

	cart := cartData.Cart
	if cartData.Payment == nil {
		return fmt.Errorf("Payment info not found")
	}

	// Cart already lock
	if utils.Contain(cart.Status, []any{models.CartStatus.Lock, models.CartStatus.Completed}) {
		return nil
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}

	// Begin Transaction
	if err = dao.Begin(); err != nil {
		return err
	}

	// Check Transaction
	var transErr error
	defer func() {
		if transErr == nil {
			_ = dao.Commit()
		} else {
			_ = dao.Rollback()
		}
	}()

	// Nothing to do with cartData.ServiceData

	if err = dao.UpdateCart(cart.ID, map[string]any{
		"status":     models.CartStatus.Lock,
		"is_current": false,
	}); err != nil {
		transErr = err
		return err
	}

	return nil
}

func RePayment(c *gin.Context) {
	if err := rePayment(c); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func rePayment(c *gin.Context) error {
	// Get cart
	cartData, err := getCartItems(c)
	if err != nil {
		return err
	}

	if _, err := cartData.AcceptRePayment(); err != nil {
		return err
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}

	// Begin Transaction
	if err = dao.Begin(); err != nil {
		return err
	}

	// Check Transaction
	var transErr error
	defer func() {
		if transErr == nil {
			_ = dao.Commit()
		} else {
			_ = dao.Rollback()
		}
	}()

	if err = releaseCartItems(dao, cartData); err != nil {
		transErr = err
		return err
	}

	var cart *models.CartData

	// get curent cart
	if cart, err = dao.GetUserCart(cartData.Cart.UserID); err != nil {
		transErr = err
		return err
	}

	// check and add services item to current cart
	for _, item := range cartData.ServiceData {
		if err = dao.CheckItemBeforeAddToCart(item); err != nil {
			continue
		}

		item.CartID = cart.Cart.ID
		if err = dao.AddItemToCart(&item); err != nil {
			transErr = err
			return err
		}
	}

	return nil
}

// GetAvailableCartCoupon get cart coupon validate
func GetAvailableCartCoupon(c *gin.Context) {
	orgID, err := utils.GetIntPathParam(c, "org-id")
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	cartData, err := getCartItems(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	etsDao := getEtsDao(c)

	result, err := getCartAvailableCoupon(dao, etsDao, orgID, cartData)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func getCartAvailableCoupon(dao db.IDao, etsDao db.IEtsDao, orgID int, cartData *models.CartData) ([]models.PromotionCouponSimple, error) {
	if cartData.Total() == 0 {
		return nil, fmt.Errorf("No items in cart! ")
	}

	promotionCodes, err := dao.GetPromotionCodes()
	if err != nil {
		return nil, err
	}

	availableCoupons := []models.PromotionCouponSimple{}
	for _, promotion := range promotionCodes {
		discountOrders := map[int]float64{}
		for _, cartItem := range cartData.ServiceData {
			order := cartItem.ServiceDetail
			user, err := dao.GetUserByID(order.UserID)
			if err != nil {
				return nil, err
			}

			if _, discount, ok := promotionCodes.ApplyCode(promotion.PromotionCode, int64(order.ServiceID), user); ok {
				if discount != 0 {
					discountOrders[cartItem.ID] = discount * float64(order.Summary.Quantity)
				}
			}
		}

		if len(discountOrders) > 0 {
			totalDiscount := 0.0
			for _, discount := range discountOrders {
				totalDiscount += discount
			}

			if gjson.ParseBytes(promotion.DiscountRules).Get("public").Bool() {
				availableCoupons = append(availableCoupons, models.PromotionCouponSimple{
					PromotionCode:  promotion.PromotionCode,
					Discount:       totalDiscount,
					Currency:       promotion.Currency,
					DiscountOrders: discountOrders,
				})
			}
		}
	}

	// Sort by discount
	sort.Slice(availableCoupons, func(i, j int) bool {
		return availableCoupons[i].Discount > availableCoupons[j].Discount
	})

	return availableCoupons, nil
}

// CartCouponValidate get cart coupon validate
func CartCouponValidate(c *gin.Context) {
	orgID, err := utils.GetIntPathParam(c, "org-id")
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	var cartSelection models.CartSelection
	if err := c.BindJSON(&cartSelection); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	cartData, err := getCartItems(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	etsDao := getEtsDao(c)

	result, err := cartCouponValidate(dao, etsDao, orgID, cartData, cartSelection)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func cartCouponValidate(dao db.IDao, etsDao db.IEtsDao, orgID int, cartData *models.CartData, cartSelection models.CartSelection) (map[int]any, error) {
	if cartData.Total() == 0 {
		return nil, fmt.Errorf("No items in cart! ")
	}

	if len(cartSelection.ItemsSelected) == 0 {
		return nil, fmt.Errorf("No items selected! ")
	}

	if cartSelection.Currency == "" {
		cartSelection.Currency = "USD"
	}

	promotionCodes, err := dao.GetPromotionCodes()
	if err != nil {
		return nil, err
	}
	promotionCode := strings.ToUpper(cartSelection.PromotionCode)
	result := map[int]any{}

	for _, cartItem := range cartData.ServiceData {
		result[cartItem.ID] = false
		if !cartSelection.Includes(cartItem) {
			cartSelection.ItemsSkip = append(cartSelection.ItemsSkip, cartItem)
			continue
		}

		order := cartItem.ServiceDetail
		if promotionCode != "" {
			user, err := dao.GetUserByID(order.UserID)
			if err != nil {
				return nil, err
			}

			if _, discount, ok := promotionCodes.ApplyCode(promotionCode, int64(order.ServiceID), user); ok {
				if discount != 0 {
					result[cartItem.ID] = true
				}
			}
		}

	}
	return result, nil
}
