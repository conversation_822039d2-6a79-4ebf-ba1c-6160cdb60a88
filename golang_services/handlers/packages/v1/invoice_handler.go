package v1

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"fmt"
	"html/template"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/localize"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/Sebastiaan<PERSON>lippert/go-wkhtmltopdf"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/gin-gonic/gin"
	"github.com/pariz/gountries"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/tealeg/xlsx/v3"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
	"golang.org/x/text/language"
	"golang.org/x/text/message"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
)

func GetInvoiceList(c *gin.Context) {
	var req models.InvoiceQueryReq
	if err := c.BindQuery(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 10
	}

	if len(req.OrgType) > 0 {
		if req.OrgType[0] == "consulate" {
			req.OrgType = []string{"consulate", "ets_provider"}
		}
	}

	authInfo := middlewares.GetAuthInfo(c)

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	ps := middlewares.GetS3Presigner(c)
	if !authInfo.IsADAdmin() {
		req.Status = models.InvoiceStatus.Sent // User and corp only see sent invoice
		req.OrgID = authInfo.OrgID.Int64
	}
	result, err := dao.GetInvoiceList(req)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	for i := range result.Data {
		{
			b, k, err := utils.UrlToS3BucketAndKey(result.Data[i].InvoiceFile)
			if err != nil {
				response.HandleResponse(c, nil, err)
				return
			}
			result.Data[i].InvoiceFile, err = ps.PresignUrl(b, k, expirationForFiles)
			if err != nil {
				response.HandleResponse(c, nil, err)
				return
			}
		}
		{
			b, k, err := utils.UrlToS3BucketAndKey(result.Data[i].ReportFile)
			if err != nil {
				response.HandleResponse(c, nil, err)
				return
			}

			result.Data[i].ReportFile, err = ps.PresignUrl(b, k, expirationForFiles)
			if err != nil {
				response.HandleResponse(c, nil, err)
				return
			}
		}
	}
	c.JSON(http.StatusOK, result)
}

type invoiceRequest struct {
	From  time.Time `json:"from"`
	To    time.Time `json:"to"`
	OrgID int       `json:"org_id"`
}

func MakeInvoiceByAD(c *gin.Context) {
	req := invoiceRequest{}
	if err := c.BindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}

	if !(funk.ContainsString(authInfo.Roles, "ad_admin") || req.OrgID == int(authInfo.OrgID.Int64)) {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "admin can access their org information only",
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	bucket := middlewares.GetS3Buckets(c)

	awsConfig := aws.NewConfig().WithRegion(middlewares.GetAWSRegion(c)).WithLogLevel(aws.LogOff)
	sess, err := session.NewSession(awsConfig)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	localize := middlewares.GetLocalize(c)

	invoice, err := makeInvoiceAndReport(dao, sess, bucket, req.OrgID, req.From, req.To, authInfo, localize)

	if err != nil {
		if err.Error() == "NO_DATA" {
			c.JSON(http.StatusOK, gin.H{
				"success":       false,
				"error_code":    "ERR02000",
				"error_message": "No invoice data",
			})
			return

		}

		response.HandleResponse(c, nil, err)
		return
	}

	if !funk.ContainsString(authInfo.Roles, "ad_admin") {
		invoiceBucket, invoiceKey, _ := utils.UrlToS3BucketAndKey(invoice.InvoiceFile)
		reportBucket, reportKey, _ := utils.UrlToS3BucketAndKey(invoice.ReportFile)
		message := map[string]any{
			"template_name": "invoice_send_files_to_ariadirect",
			"to":            utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("admin_email").String(),
			"bcc":           []string{utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String()},
			"parameters": map[string]any{
				"ConsulateName": invoice.CorpName,
				"InvoiceNo":     invoice.InvoiceNumber,
				"StartDate":     invoice.From.Format("2006-01-02"),
				"EndDate":       invoice.To.Format("2006-01-02"),
			},
			"attachments": []map[string]any{
				{
					"bucket":    invoiceBucket,
					"key":       invoiceKey,
					"file_name": utils.GetFileNameFromURL(invoiceKey),
				},
				{
					"bucket":    reportBucket,
					"key":       reportKey,
					"file_name": utils.GetFileNameFromURL(reportKey),
				},
			},
		}
		if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		orm := middlewares.GetORMDao(c)

		if err := orm.Exec("UPDATE corporation_invoices SET status = ? WHERE id = ?", models.InvoiceStatus.Sent, invoice.ID).Error; err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	ps := middlewares.GetS3Presigner(c)

	{
		b, k, err := utils.UrlToS3BucketAndKey(invoice.InvoiceFile)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		invoice.InvoiceFile, err = ps.PresignUrl(b, k, expirationForFiles)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

	}

	{
		b, k, err := utils.UrlToS3BucketAndKey(invoice.ReportFile)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		invoice.ReportFile, err = ps.PresignUrl(b, k, expirationForFiles)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

	}

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"invoice_url": invoice.InvoiceFile,
		"report_url":  invoice.ReportFile,
	})
}

func makeInvoiceAndReport(dao db.IDao, sess *session.Session, bucket map[string]any, orgID int, from, to time.Time, authInfo *models.AuthInfo, localize *localize.Localize) (*models.InvoiceReport, error) {
	rows, err := dao.DB().Db.Db.Query(`select id,type, name, code, address, contact from v_organizations WHERE id = $1`, orgID)

	if err != nil {
		return nil, err
	}

	defer rows.Close()

	messagePrinter := message.NewPrinter(language.English)
	if rows.Next() {
		var (
			orgID              int
			orgType            string
			corpName, corpCode string
			corpAddress        models.Address
			corpContact        models.Contact
		)

		if err := rows.Scan(&orgID, &orgType, &corpName, &corpCode, &corpAddress, &corpContact); err != nil {
			return nil, err
		}

		if orgType == "consulate" {
			corpCode = "CONS"
		}
		if orgType == "ets_provider" {
			corpCode = "ETS"
		}
		var countNull sql.NullInt64
		dao.DB().Db.Db.Get(&countNull, "SELECT max(REGEXP_REPLACE(invoice_number, '(.+-)+','')::INTEGER) FROM corporation_invoices WHERE org_id = $1", orgID)
		var invoiceIndex int64 = 1
		if countNull.Valid {
			invoiceIndex = countNull.Int64 + 1
		}
		var invoiceReport models.InvoiceReport
		invoiceReport.From = from // time.Date(from.Year(), from.Month(), from.Day(), 0, 0, 0, 0, time.UTC)
		invoiceReport.To = to     // time.Date(to.Year(), to.Month(), to.Day(), 0, 0, 0, 0, time.UTC).AddDate(0, 0, 1).Add(-time.Second)
		invoiceReport.InvoiceDate = time.Now().Format("01/02/2006")
		invoiceReport.PONumber = fmt.Sprintf("%s%s%s", corpCode, invoiceReport.From.Format("010206"), invoiceReport.To.Format("010206"))
		invoiceReport.InvoiceNumber = fmt.Sprintf("%s-%d", corpCode, invoiceIndex)
		invoiceReport.CorpCode = corpCode
		invoiceReport.CorpName = corpName
		invoiceReport.OrgType = orgType
		invoiceReport.Address1 = corpAddress.Address // 3030 Orchard Pkw
		if corpAddress.Address == "" {
			invoiceReport.Address1 = "[Address]"
		}
		invoiceReport.Status = models.InvoiceStatus.Created
		if !funk.ContainsString(authInfo.Roles, "ad_admin") {
			invoiceReport.Status = models.InvoiceStatus.Sent // Will send email to AD admin
		}

		if corpAddress.City == "" {
			corpAddress.City = "[City]"
		}
		if corpAddress.State == "" {
			corpAddress.State = "[State]"
		}
		if corpAddress.ZipCode.String == "" {
			corpAddress.ZipCode.SetValid("[ZipCode]")
		}

		invoiceReport.Address2 = corpAddress.City + ", " + corpAddress.State + " " + corpAddress.ZipCode.String

		// Only get service with order success in month
		query := dao.DB().Db.PSQL.Select("o.id, o.status, o.summary, o.service_id, o.payment_id, o.user_id, o.created_at, o.deleted_at, to_jsonb(e) service").From("service_orders o").
			LeftJoin("payment p2 on p2.id = o.payment_id").
			LeftJoin("ets e on e.id = o.service_id").
			Where("o.status NOT IN ('open', 'cancelled')").
			Where("o.deleted_at IS NULL").
			Where("p2.status = ?", "success").
			Where("p2.created_at >= ? and p2.created_at <= ?", invoiceReport.From, invoiceReport.To).
			OrderBy("o.submitted_time DESC")

		if orgType == "consulate" || orgType == "ets_provider" {
			query = query.Where("o.provider_id IN (SELECT id FROM ets_provider WHERE org_id = ?)", orgID)
		} else {
			query = query.Where("o.org_id = ?", orgID)
		}
		sql, args, err := query.ToSql()
		if err != nil {
			return nil, err
		}

		var orders []models.ServiceOrderDetail
		if err := dao.DB().Db.Db.Select(&orders, sql, args...); err != nil {
			return nil, err
		}

		for i, order := range orders {
			var tasks []models.ServiceTask

			if err := dao.DB().Db.Db.Select(&tasks, "select input_pods from service_tasks where order_id = $1", order.ID); err != nil {
				return nil, err
			}

			if len(tasks) == 0 {
				continue
			}

			var payment models.Payment
			if err := dao.DB().Db.Db.Get(&payment, "select currency,unit_price, total, method from payment where id = $1", order.PaymentID); err != nil {
				return nil, err
			}

			attrJ := utils.StructToJSON(order.Service.Attributes)
			// Make report item
			report := models.ReportItem{
				CorpName:       corpName,
				OrderNo:        strconv.Itoa(order.ID),
				OrderStatus:    localize.EN.OrderStatus[order.Status],
				AppName:        "",
				OrderDate:      order.CreatedAt.Format("01-02-2006"),
				Country:        localize.EN.Country[order.Service.Country],
				ServiceType:    localize.EN.ServiceType[order.Service.ServiceType],
				ServiceName:    localize.EN.Tasks[order.Service.Tasks[0]],
				ProcessingTime: localize.EN.ProcessingTime[attrJ.Get("processing_time").String()],
				Quantity:       int(utils.StructToJSON(order.Summary).Get("quantity").Int()),
				PaymentMethod:  payment.Method,
			}

			if orgType == "consulate" || orgType == "ets_provider" {
				rows, err := dao.DB().Db.Db.Query("SELECT price, currency FROM provider_price WHERE status = 'active' AND product_id = $1 LIMIT 1", order.ServiceID)
				if err != nil {
					return nil, err
				}
				defer rows.Close()
				if rows.Next() {
					if err := rows.Scan(&report.UnitPrice, &report.Currency); err != nil {
						return nil, err
					}
					report.ShippingPrice = 0
					report.Total = float64(report.Quantity) * report.UnitPrice
				}
			} else {
				report.UnitPrice = utils.StructToJSON(order.Summary).Get("unit_price").Float()
				report.Currency = utils.StructToJSON(order.Summary).Get("currency").String()
				report.ShippingPrice = utils.StructToJSON(order.Summary).Get("shipping_price").Float()
				report.Total = utils.StructToJSON(order.Summary).Get("total").Float()
				if utils.StructToJSON(order.Summary).Get("total_actual").Exists() {
					report.TotalActual = utils.StructToJSON(order.Summary).Get("total_actual").Float()
					report.TotalActualNote = utils.StructToJSON(order.Summary).Get("total_actual_note").String()
				} else {
					report.TotalActual = report.Total
					report.TotalActualNote = "N/A"
				}
				report.AdditionalFee = 0
				utils.StructToJSON(order.Summary).Get("additional_fee").ForEach(func(key, value gjson.Result) bool {
					report.AdditionalFee += value.Float()
					return true
				})
			}

			// Make package item
			var item = new(models.InvoiceItem)
			item.ID = strconv.Itoa(order.ID)
			if order.SubmittedTime != nil {
				item.SubmittedDate = order.SubmittedTime.Format("01/02/2006")
			} else if order.OrderTime != nil {
				item.SubmittedDate = order.OrderTime.Format("01/02/2006")
			} else {
				item.SubmittedDate = order.CreatedAt.Format("01/02/2006")
			}
			item.Item = fmt.Sprintf("%03d", i+1)
			item.Qty = utils.StructToJSON(order.Summary).Get("quantity").String()
			item.Currency = payment.Currency

			item.ShippingPrice = float32(report.ShippingPrice)
			item.Price = float32(report.UnitPrice)
			item.Total = float32(report.Total)
			item.TotalActual = float32(report.TotalActual)
			item.TotalActualNote = report.TotalActualNote
			item.AdditionalFee = float32(report.AdditionalFee)
			item.ProcessingTime = report.ProcessingTime
			if len(order.Service.Tasks) > 0 {
				item.Note = localize.EN.Tasks[order.Service.Tasks[0]]
			}

			if item.Note == "" && order.Service.Tag != nil {
				item.Note = *order.Service.Tag
			}

			var people = lo.Filter(lo.Map(tasks, func(task models.ServiceTask, _ int) string {
				return task.GetAppName(order.Service.ServiceType)
			}), func(name string, _ int) bool {
				return name != ""
			})

			var people2 = lo.Filter(lo.Map(tasks, func(task models.ServiceTask, _ int) string {
				pairs := task.InputPods.ToMapKeyValueV2(order.InputPods)
				return cast.ToString(pairs["travel_passenger_info_passenger_name_list"])
			}), func(name string, _ int) bool {
				return name != ""
			})

			if len(people2) > 0 {
				item.Name = strings.Join(people2, ", ")
			} else {
				item.Name = strings.Join(people, ", ")
			}
			report.AppName = item.Name

			notes := []string{}
			notes = append(notes, Coalesce(localize.EN.Product[order.Service.Name], order.Service.Name))

			if val := cast.ToString(order.Service.Tag); val != "" {
				notes = append(notes, Coalesce(localize.EN.Tag[val], val))
			}
			if val := attrJ.Get("validity").String(); val != "" {
				notes = append(notes, Coalesce(localize.EN.ProcessingTime[val], val))
			}
			if val := attrJ.Get("number_of_entries").String(); val != "" {
				notes = append(notes, Coalesce(localize.EN.NumberOfEntry[val], val))
			}
			if val := attrJ.Get("airport").String(); val != "" {
				notes = append(notes, Coalesce(localize.EN.Airport[val], val))

			}
			if val := attrJ.Get("terminal").String(); val != "" {
				notes = append(notes, Coalesce(localize.EN.Terminal[val], val))
			}
			item.Description = strings.Join(notes, ", ")
			report.Description = item.Description
			serviceJ := utils.StructToJSON(order.Service)
			if order.Service.ServiceType == "passport" {
				item.Group = "passport"
				if country, err := gountries.New().FindCountryByAlpha(order.Service.Country); err == nil {
					report.Country = country.Name.Common
					report.ServiceName = "Passport"
				}
			} else if order.Service.ServiceType == "new_visa" || order.Service.ServiceType == "new_visa_urgent" {
				item.Group = "visa"
			} else if order.Service.ServiceType == "fastlane" || order.Service.ServiceType == "country_tourist" {
				item.Note = serviceJ.Get("airport").String() + " " + serviceJ.Get("attributes.terminal").String()
				inputPodByName := utils.StructToJSON(tasks[0].InputPods.ToMapByName("Name"))
				if inputPodByName.Get("enter_timestamp.value.fe").Exists() {
					item.SubmittedDate = inputPodByName.Get("enter_timestamp.value.fe").Time().Format("01/02/2006")
				} else {
					item.SubmittedDate = inputPodByName.Get("exit_timestamp.value.fe").Time().Format("01/02/2006")
				}
				item.Group = "another"
			} else {
				item.Group = "another"
			}

			invoiceReport.Items = append(invoiceReport.Items, item)

			if item.Group == "visa" {
				invoiceReport.VisaItems = append(invoiceReport.VisaItems, item)
				invoiceReport.ReportVisaItems = append(invoiceReport.ReportVisaItems, report)
			} else if item.Group == "passport" {
				invoiceReport.PassportItems = append(invoiceReport.PassportItems, item)
				invoiceReport.ReportPassportItems = append(invoiceReport.ReportPassportItems, report)
			} else {
				invoiceReport.OtherItems = append(invoiceReport.OtherItems, item)
				invoiceReport.ReportOtherItems = append(invoiceReport.ReportOtherItems, report)
			}
		}

		if len(invoiceReport.Items) == 0 {
			return nil, fmt.Errorf("NO_DATA")
		}

		invoiceReport.SubTotal = funk.SumFloat32(funk.Map(invoiceReport.Items, func(item *models.InvoiceItem) float32 {
			return item.Price
		}).([]float32))

		invoiceReport.SubTotalStr = messagePrinter.Sprintf("%0.2f", invoiceReport.SubTotal)

		invoiceReport.Total = funk.SumFloat32(funk.Map(invoiceReport.Items, func(item *models.InvoiceItem) float32 {
			return item.Total
		}).([]float32))

		invoiceReport.TotalStr = messagePrinter.Sprintf("%0.2f", invoiceReport.Total)

		newInvoice, err := saveInvoice(dao, sess, bucket, orgID, &invoiceReport)
		if err != nil {
			return nil, err
		}
		return newInvoice, nil

	}

	return nil, fmt.Errorf("NO_DATA")
}

func saveInvoice(dao db.IDao, sess *session.Session, bucket map[string]any, orgID int, report *models.InvoiceReport) (*models.InvoiceReport, error) {
	buff, err := json.Marshal(report)
	if err != nil {
		return report, err
	}
	invoiceBucket := bucket["ariadirect_prod_corporation_invoices"].(string)
	invoiceKey := fmt.Sprintf("%s/Invoice_%s.xlsx", report.CorpCode, report.InvoiceNumber)
	reportKey := fmt.Sprintf("%s/Report_%s.xlsx", report.CorpCode, report.InvoiceNumber)

	invoiceURL := invoiceBucket + "/" + invoiceKey
	reportURL := invoiceBucket + "/" + reportKey

	baseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("api_base_url").String()
	xlsxBuff, err := utils.GenerateXlsxFromTemplate(baseURL, "Invoice", buff)
	if err != nil {
		return report, err
	}

	// Save docx to S3
	if _, err := s3.New(sess).PutObject(&s3.PutObjectInput{
		Bucket:        aws.String(invoiceBucket),
		Key:           aws.String(invoiceKey),
		ACL:           aws.String("private"),
		Body:          bytes.NewReader(xlsxBuff),
		ContentLength: aws.Int64(int64(len(xlsxBuff))),
		ContentType:   aws.String("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
	}); err != nil {
		return report, err
	}

	// Generate report buffer
	report.ReportItems = append(report.ReportItems, report.ReportPassportItems...)
	report.ReportItems = append(report.ReportItems, report.ReportVisaItems...)
	report.ReportItems = append(report.ReportItems, report.ReportOtherItems...)
	reportBuff, err := makeReport(report.ReportItems)
	if err != nil {
		return report, err
	}
	// Save report xlsx to S3
	if _, err := s3.New(sess).PutObject(&s3.PutObjectInput{
		Bucket:        aws.String(invoiceBucket),
		Key:           aws.String(reportKey),
		ACL:           aws.String("private"),
		Body:          bytes.NewReader(reportBuff),
		ContentLength: aws.Int64(int64(len(reportBuff))),
		ContentType:   aws.String("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
	}); err != nil {
		return report, err
	}
	report.InvoiceFile = invoiceURL
	report.ReportFile = reportURL
	if err := dao.DB().Db.Db.QueryRow(`INSERT INTO corporation_invoices
	(invoice_from, invoice_to, org_id, org_name, org_type, invoice_number, po_number, sub_total, total, items, invoice_file,report_file,status)
	VALUES($1, $2, $3, $4 ,$5 ,$6 ,$7 ,$8 ,$9, $10, $11, $12, $13)
	ON CONFLICT(po_number) DO UPDATE
	SET invoice_file = EXCLUDED.invoice_file,
		report_file = EXCLUDED.report_file,
		updated_at = current_timestamp,
		deleted_at = NULL
	RETURNING id
	`, report.From, report.To, orgID, report.CorpName, report.OrgType, report.InvoiceNumber, report.PONumber, report.SubTotal, report.Total, buff, invoiceURL, reportURL, "created").
		Scan(&report.ID); err != nil {
		return report, err
	}
	return report, nil
}

func makePdfFromHtml(rawHtml string, data any) ([]byte, error) {
	invoiceTmpl, err := template.New("corp_invoice").Parse(rawHtml)
	if err != nil {
		return nil, err
	}

	var parsedTeml = new(bytes.Buffer)
	if err := invoiceTmpl.Execute(parsedTeml, data); err != nil {
		return nil, err
	}

	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		return nil, err
	}

	// DisableSmartShrinking
	page := wkhtmltopdf.NewPageReader(strings.NewReader(parsedTeml.String()))
	page.DisableSmartShrinking.Set(true)

	pdfg.AddPage(page)
	pdfg.PageSize.Set(wkhtmltopdf.PageSizeA4)

	// Create PDF document in internal buffer
	if err = pdfg.Create(); err != nil {
		return nil, err
	}

	// Export as bytes
	return pdfg.Bytes(), nil
}

func makeReport(data []models.ReportItem) ([]byte, error) {
	buff := bytes.Buffer{}

	headers := []string{
		"Corporation Name", "Order No.", "Order Status", "Application Name", "Order Date", "Country",
		"Service Type", "Service Name", "Description", "Processing Time", "Quantity",
		"Unit Price", "Shipping Price", "Total", "Total Actual", "Total Actual Note", "Currency", "Payment Method",
	}

	sheet, err := xlsx.NewFile().AddSheet("report")
	if err != nil {
		return nil, err
	}

	row := sheet.AddRow()
	row.SetHeight(20)

	for _, header := range headers {
		cell := row.AddCell()
		cell.Value = header
		cell.SetStyle(&xlsx.Style{Font: xlsx.Font{Bold: true}})
	}

	for _, r := range data {
		row := sheet.AddRow()
		row.SetHeight(20)
		row.AddCell().SetString(r.CorpName)
		row.AddCell().SetString(r.OrderNo)
		row.AddCell().SetString(r.OrderStatus)
		row.AddCell().SetString(r.AppName)
		row.AddCell().SetString(r.OrderDate)
		row.AddCell().SetString(r.Country)
		row.AddCell().SetString(r.ServiceType)
		row.AddCell().SetString(r.ServiceName)
		row.AddCell().SetString(r.Description)
		row.AddCell().SetString(r.ProcessingTime)

		row.AddCell().SetInt(r.Quantity)
		row.AddCell().SetFloat(r.UnitPrice)
		row.AddCell().SetFloat(r.ShippingPrice)
		row.AddCell().SetFloat(r.Total)
		row.AddCell().SetFloat(r.TotalActual)
		row.AddCell().SetString(r.TotalActualNote)

		row.AddCell().SetString(r.Currency)
		row.AddCell().SetString(r.PaymentMethod)
	}

	sheet.SetColWidth(1, 14, 20)
	sheet.SetColWidth(3, 3, 30)
	sheet.File.Write(&buff)

	return buff.Bytes(), nil
}
func UpdateInvoice(c *gin.Context) {
	var (
		req models.CorporationInvoice
		err error
	)
	if err = c.BindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if req.ID, err = utils.GetIntPathParam(c, "id"); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	authInfo := middlewares.GetAuthInfo(c)

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if !authInfo.IsADAdmin() {
		req.OrgID = int(authInfo.OrgID.Int64)
	}
	if err = dao.UpdateInvoice(req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// Delete Invoice
func DeleteInvoice(c *gin.Context) {
	id, err := utils.GetIntPathParam(c, "id")
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err = dao.DeleteInvoice(id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func GenerateNewInvoice(c *gin.Context) {
	id, err := utils.GetIntPathParam(c, "id")
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	invoice, err := dao.GetInvoice(id)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	ps := middlewares.GetS3Presigner(c)

	reportBucket, reportKey, _ := utils.UrlToS3BucketAndKey(invoice.ReportFile)
	reportUploadURLpresigned, err := ps.PresignUploadUrl(reportBucket, reportKey, "", 15*time.Minute)
	reportDownURLpresigned, err := ps.PresignUrl(reportBucket, reportKey, expirationForFiles)

	invoiceBucket, invoiceKey, _ := utils.UrlToS3BucketAndKey(invoice.InvoiceFile)
	invoiceUploadURLpresigned, err := ps.PresignUploadUrl(invoiceBucket, invoiceKey, "", 15*time.Minute)
	invoiceDownURLpresigned, err := ps.PresignUrl(invoiceBucket, invoiceKey, expirationForFiles)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"report_file": gin.H{
				"upload_url":   reportUploadURLpresigned,
				"download_url": reportDownURLpresigned,
			},
			"invoice_file": gin.H{
				"upload_url":   invoiceUploadURLpresigned,
				"download_url": invoiceDownURLpresigned,
			},
		},
	})
}

type sendEmailReq struct {
	To string `json:"to"`
}

func SendInvoiceEmail(c *gin.Context) {
	var req sendEmailReq
	if err := c.BindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	id, err := utils.GetIntPathParam(c, "id")
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	invoice, err := dao.GetInvoice(id)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	invoiceBucket, invoiceKey, _ := utils.UrlToS3BucketAndKey(invoice.InvoiceFile)
	reportBucket, reportKey, _ := utils.UrlToS3BucketAndKey(invoice.ReportFile)

	message := map[string]any{
		"template_name": "invoice_send_files_to_user",
		"to":            req.To,
		"bcc":           []string{utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("support_email").String()},
		"parameters": map[string]any{
			"InvoiceNo": invoice.InvoiceNumber,
			"StartDate": invoice.From.Format("2006-01-02"),
			"EndDate":   invoice.To.Format("2006-01-02"),
		},
		"attachments": []map[string]any{
			{
				"bucket":    invoiceBucket,
				"key":       invoiceKey,
				"file_name": utils.GetFileNameFromURL(invoiceKey),
			},
			{
				"bucket":    reportBucket,
				"key":       reportKey,
				"file_name": utils.GetFileNameFromURL(reportKey),
			},
		},
	}
	if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	invoice.Status = models.InvoiceStatus.Sent
	invoice.UpdatedAt = aws.Time(time.Now())
	if err := dao.UpdateInvoice(*invoice); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func sendEmail(data string) error {
	var awsJSON = gjson.ParseBytes([]byte(os.Getenv("ad_aws")))

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(awsJSON.Get("region").String()),
	})
	if err != nil {
		return err
	}
	svc := sqs.New(sess)
	sqsConf := gjson.Parse(os.Getenv("ad_sqs"))
	qURL := sqsConf.Get("url_prefix").String() + "/" + sqsConf.Get("notification_sqs_name").String()

	if _, err := svc.SendMessage(&sqs.SendMessageInput{
		MessageBody: aws.String(data),
		QueueUrl:    &qURL,
	}); err != nil {
		return err
	}
	return nil
}

func Coalesce[T any](a, b T) T {
	if cast.ToString(a) != "" {
		return a
	}
	return b
}
