package v1

import (
	"fmt"
	"os"
	"strings"

	"github.com/tidwall/gjson"

	"bitbucket.org/persistence17/aria/golang_services/localize"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func sendEmailToETSProvider(dao db.IDao, localize *localize.Localize, order *models.ServiceOrder) error {
	etsDao := db.NewEtsDao(dao.DB().Db)

	user, err := dao.GetUserByID(order.UserID)
	if err != nil {
		return err
	}

	svcs, err := etsDao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return err
	}
	if len(svcs) == 0 {
		return nil
	}
	svc := svcs[0]
	attrJ := utils.StructToJSON(svc.Attributes)
	confJ := utils.StructToJSON(order.QueryPodValues)
	emailConfigMap := utils.GetMapEnv("ad_email")
	supportEmail := utils.StructToJSON(emailConfigMap).Get("support").String()

	webBaseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("web_base_url").String()

	provider, err := etsDao.GetEtsProviderByID(order.ProviderID)
	if err != nil {
		return err
	}

	tasks, _, err := etsDao.QueryServiceTasks(map[string]any{"order_id": order.ID}, 0, 1)
	if err != nil {
		return err
	}
	inputPods := tasks[0].InputPods.ToMapKeyValue()

	parameters := map[string]any{
		"OrderID":           order.ID,
		"FullName":          strings.ToUpper(user.GivenName + " " + user.Surname),
		"ProviderName":      strings.ToUpper(provider.Name),
		"Country":           localize.EN.Country[svc.Country],
		"ServiceType":       localize.EN.ServiceType[svc.ServiceType],
		"RegionOfResidence": localize.EN.Country[confJ.Get("service_core_info_region_of_residence").String()],
		"ProcessingTime":    localize.EN.ProcessingTime[attrJ.Get("processing_time").String()],
		"Task":              localize.EN.Tasks[strings.Join(svc.Tasks, ",")],
		"SubmitMethod":      localize.EN.SubmitMethod[attrJ.Get("submit_method").String()],
		"PickupMethod":      localize.EN.PickupMethod[attrJ.Get("receive_method").String()],
		"NoOfTraveler":      fmt.Sprintf("%d", order.Summary.Quantity),
		"Terminal":          localize.EN.Terminal[attrJ.Get("terminal").String()],
		"AirportName":       svc.Airport,
		"URL":               fmt.Sprintf("%s/dashboard/customer-orders/detail?order_id=%d&service=ets", webBaseURL, order.ID),
		// Fastlane
		"FlightNo":        utils.GetStringOrText(inputPods["travel_exit_flight_exit_flight"]),
		"ServiceDateTime": inputPods["travel_exit_flight_exit_timestamp"],
	}

	//Fastlane only
	{
		if tasks[0].Type == "arrival" || tasks[0].Type == "vip_arrival" {
			parameters["FlightNo"] = utils.GetStringOrText(inputPods["travel_enter_flight_enter_flight"])
			parameters["ServiceDateTime"] = inputPods["travel_enter_flight_enter_timestamp"]
		}
		parameters["ServiceDateTime"] = utils.StructToJSON(parameters).Get("ServiceDateTime").Time().Format("Mon, 02 Jan 2006 15:04")
	}
	// Get airport name
	if svc.Airport != nil {
		airlines, err := dao.DB().GetMasterDataValueByName("airport")
		if err != nil {
			return err
		}
		result := gjson.GetBytes(airlines, fmt.Sprintf(`#(iata="%s").name`, *svc.Airport))
		if val := result.String(); val != "" {
			parameters["AirportName"] = val + " (" + *svc.Airport + ")"
		}
	}

	dataApplications := []map[string]any{}
	for _, task := range tasks {
		dataApplications = append(dataApplications, map[string]any{
			"FullName":          task.GetAppName(svc.ServiceType),
			"RegionOfResidence": localize.EN.Country[confJ.Get("service_core_info_region_of_residence").String()],
			"ProcessingTime":    localize.EN.ProcessingTime[attrJ.Get("processing_time").String()],
			"Task":              localize.EN.Tasks[strings.Join(svc.Tasks, ",")],
		})
	}
	parameters["Applications"] = dataApplications

	message := map[string]any{
		"template_name": attrJ.Get("email_template.send_to_provider").String(),
		"to":            provider.Contact.Email,
		"bcc":           []string{supportEmail},
		"parameters":    parameters,
	}

	// Send email to sqs
	if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
		return err
	}
	return nil
}
