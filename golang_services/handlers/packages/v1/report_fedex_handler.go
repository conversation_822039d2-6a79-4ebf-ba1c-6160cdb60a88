package v1

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/Masterminds/squirrel"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/gin-gonic/gin"
	"github.com/tidwall/gjson"
)

type FedExReport struct {
	ID        int        `json:"id" db:"id"`
	DateFrom  time.Time  `json:"date_from" db:"date_from"`
	DateTo    time.Time  `json:"date_to" db:"date_to"`
	ReportURL string     `json:"report_url" db:"report_url"`
	CreatedAt time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt *time.Time `json:"updated_at" db:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at" db:"deleted_at"`
}

type FedExReportFilter struct {
	Offset    uint64 `form:"offset"`
	Limit     uint64 `form:"limit"`
	SortField string `form:"sort_field"`
	SortOrder string `form:"sort_order"`
}

func GetFedExReportList(c *gin.Context) {
	var req FedExReportFilter
	if err := c.BindQuery(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 100
	}

	data := []FedExReport{}
	var count int64

	dao := middlewares.GetORMDao(c)
	dao.Limit(int(req.Limit)).Offset(int(req.Offset)).Find(&data)
	dao.Table("fed_ex_reports").Count(&count)

	ps := middlewares.GetS3Presigner(c)

	for i := range data {
		{
			b, k, err := utils.UrlToS3BucketAndKey(data[i].ReportURL)
			if err != nil {
				response.HandleResponse(c, nil, err)
				return
			}
			data[i].ReportURL, err = ps.PresignUrl(b, k, expirationForFiles)
			if err != nil {
				response.HandleResponse(c, nil, err)
				return
			}
		}
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
		"total":   count,
	})
}

func MakeFedExReport(c *gin.Context) {
	req := reportRequest{}
	if err := c.BindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}

	dao := middlewares.GetORMDao(c)

	type TrackInfo struct {
		ID            int              `json:"id" db:"id"`
		Service       string           `json:"service" db:"service"`
		ShipmentID    string           `json:"shipment_id" db:"shipment_id"`
		Labels        *json.RawMessage `json:"labels" db:"labels"`
		FedexTracking *json.RawMessage `json:"fedex_tracking" db:"fedex_tracking"`
	}

	query1 := squirrel.StatementBuilder.Select("p.id, 'visa' service, vs.id shipment_id, vs.labels, vs.fedex_tracking").From("package p").
		LeftJoin("visa_shipment vs on vs.id = p.shipment_info ").
		Where("vs.fedex_tracking->>'SHIP' >= ?", req.From).
		Where("vs.fedex_tracking->>'SHIP' <= ?", req.To)

	query2 := squirrel.StatementBuilder.Select("so.id, 'ets' service, vs.id shipment_id, vs.labels, vs.fedex_tracking").From("service_orders so").
		LeftJoin("visa_shipment vs on vs.id = so.shipment_info ").
		Where("vs.fedex_tracking->>'SHIP' >= ?", req.From).
		Where("vs.fedex_tracking->>'SHIP' <= ?", req.To)

	sql1, args1, _ := query1.ToSql()
	sql2, args2, _ := query2.ToSql()
	args1 = append(args1, args2...)

	data := []TrackInfo{}
	dao.Raw(fmt.Sprintf("%s UNION ALL %s", sql1, sql2), args1...).Find(&data)

	reportData := []map[string]string{}
	for _, item := range data {
		reportData = append(reportData, map[string]string{
			"OrderID":        strconv.Itoa(item.ID),
			"TrackingNumber": utils.StructToJSON(item.Labels).Get("consulate_to_user.tracking_number").String(),
			"FedExName":      utils.StructToJSON(item.FedexTracking).Get("SERVICE_TYPE").String(),
			"ShipDate":       utils.StructToJSON(item.FedexTracking).Get("SHIP").Time().Format(time.RFC1123),
			"PickupDate":     utils.StructToJSON(item.FedexTracking).Get("ACTUAL_PICKUP").Time().Format(time.RFC1123),
			"DeliveryDate":   utils.StructToJSON(item.FedexTracking).Get("ACTUAL_DELIVERY").Time().Format(time.RFC1123),
			"Price":          utils.StructToJSON(item.Labels).Get("consulate_to_user.currency").String() + " " + utils.StructToJSON(item.Labels).Get("consulate_to_user.price").String(),
		})
	}

	baseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("api_base_url").String()

	buff, _ := json.Marshal(gin.H{
		"data": reportData,
	})
	xlsxBuff, err := utils.GenerateXlsxFromTemplate(baseURL, "FedExReport", buff)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	awsConfig := aws.NewConfig().WithRegion(middlewares.GetAWSRegion(c)).WithLogLevel(aws.LogOff)
	sess, err := session.NewSession(awsConfig)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	bucket := middlewares.GetS3Buckets(c)

	key := fmt.Sprintf("FedExReport_%s_%s.xlsx", req.From.Format("2006-01-02"), req.To.Format("2006-01-02"))
	// Save docx to S3
	if _, err := s3.New(sess).PutObject(&s3.PutObjectInput{
		Bucket:        aws.String(bucket["ariadirect_prod_corporation_invoices"].(string)),
		Key:           aws.String(key),
		ACL:           aws.String("private"),
		Body:          bytes.NewReader(xlsxBuff),
		ContentLength: aws.Int64(int64(len(xlsxBuff))),
		ContentType:   aws.String("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
	}); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	ps := middlewares.GetS3Presigner(c)

	url, err := ps.PresignUrl(bucket["ariadirect_prod_corporation_invoices"].(string), key, expirationForFiles)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err := dao.Create(&FedExReport{
		DateFrom:  req.From,
		DateTo:    req.To,
		ReportURL: url,
	}).Error; err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"report_url": url,
	})
}

func DeleteFedExReport(c *gin.Context) {
	dao := middlewares.GetORMDao(c)
	id, _ := strconv.Atoi(c.Param("id"))

	if err := dao.Exec("DELETE FROM fed_ex_reports WHERE id = ?", id).Error; err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
