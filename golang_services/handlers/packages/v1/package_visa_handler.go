package v1

import (
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/notification"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/sdk/zalo"
)

func UpdateVisaFileToOrderWithBatchID(c *gin.Context) {
	if err := updateVisaFileToOrderWithBatchID(c); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func updateVisaFileToOrderWithBatchID(c *gin.Context) error {
	ps := middlewares.GetS3Presigner(c)
	bucket := middlewares.GetS3Buckets(c)[ApplicationFilesUploadBucketKey].(string)

	images, err := ps.ListObjects(bucket, fmt.Sprintf("batches/%s", c.Param("batch-id")))
	if err != nil {
		return err
	}
	dao, err := middlewares.GetVisaDao(c)
	etsDao := getEtsDao(c)
	if err != nil {
		return err
	}
	images, err = presignFiles(bucket, images, ps)
	if err != nil {
		return err
	}
	for _, image := range images {
		buff, err := utils.UrlToBuff(image)
		if err != nil {
			fmt.Println(err)
		}

		text := utils.PdfToText(buff)
		zalo.SendZaloFile(zalo.GetGroupID("INTERNAL_NOTIFICATION"), image)
		matches := regexp.MustCompile(`E\d{6}[A-Z]{3}[A-Z0-9]{9,12}`).FindStringSubmatch(text)
		if len(matches) > 0 {
			registrationCode := matches[0]
			fmt.Println(registrationCode)

			message := []string{
				`<b><span style="color:#0000ff">EVISA UPLOAD</span></b> Evisa has just been uploaded with registration code:`,
				`<b>` + registrationCode + `</b> at: ` + time.Now().Format("2006-01-02 15:04:05"),
			}

			zalo.SendZaloSMS(zalo.GetGroupID("INTERNAL_NOTIFICATION"), strings.Join(message, "\n"))

			var serviceTasks []models.ServiceTask
			if err := dao.DB().ORM().Select("id,order_id,input_pods, output_pods,output_pod_values").Table("service_tasks").
				Where("input_pod_values->>'travel_visa_info_registration_code'::text = $1 OR output_pod_values->>'application_application_info_registration_code'::text = $1", registrationCode).
				Find(&serviceTasks).Error; err != nil {
				fmt.Println(err)
			}
			if len(serviceTasks) == 0 {
				zalo.SendZaloSMS(zalo.GetGroupID("INTERNAL_NOTIFICATION"), `No application found with registration code: `+registrationCode)
				fmt.Println("No service task found")
				return nil
			}

			for _, serviceTask := range serviceTasks {
				serviceTask.OutputPods.SetFEValue("application_application_info_copy_of_new_visa", []string{image})
				serviceTask.OutPodValues = serviceTask.OutputPods.ToMapKeyValue()

				if err := etsDao.UpdateServiceTask(map[string]any{
					"output_pods":       serviceTask.OutputPods,
					"output_pod_values": serviceTask.OutPodValues,
				}, serviceTask.ID); err != nil {
					return err
				}
				webBaseURL := utils.StructToJSON(middlewares.GetServiceConfigMap(c)).Get("web_base_url").String()
				zalo.SendZaloSMS(zalo.GetGroupID("INTERNAL_NOTIFICATION"), strings.Join([]string{
					fmt.Sprintf(`Evisa file <b>%s</b> has been uploaded for application: <b>%s</b>`, registrationCode, serviceTask.GetAppName("")),
					fmt.Sprintf("Details:  %s/dashboard/orders/detail?order_id=%d&service=ets", webBaseURL, serviceTask.OrderID),
				}, "\n"))

				if err := notification.NotifyUpdateOutputPods(c, cast.ToInt64(serviceTask.OrderID), cast.ToInt64(serviceTask.ID)); err != nil {
					return err
				}

			}

		}
	}

	return nil
}
