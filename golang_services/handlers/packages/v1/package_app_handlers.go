package v1

import (
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/applications"
)

type cloneApplicationReq struct {
	ApplicationID int                `json:"application_id"`
	VisaProductID int64              `json:"visa_product_id"`
	EnterDate     *time.Time         `json:"enter_date" db:"enter_date"`
	ExitDate      *time.Time         `json:"exit_date" db:"exit_date"`
	InputPods     []*models.InputPod `json:"input_pods"`
}

func intersectTwoApplication(srcPods, descPods *models.InputPods) *models.InputPods {
	for key, value := range *descPods {
		// update entry date
		if key == applications.VisaInfoEntryDatePodID {
			continue
		}
		// update exit date
		if key == applications.VisaInfoExitDatePodID {
			continue
		}

		// Copy pod has data
		if val, ok := (*srcPods)[key]; ok && val.GetFEValue() != nil {
			(*descPods)[key] = value
		}

	}
	return descPods
}
