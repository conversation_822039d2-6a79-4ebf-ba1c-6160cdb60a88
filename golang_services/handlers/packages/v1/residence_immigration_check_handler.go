package v1

import (
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"github.com/gin-gonic/gin"
)

func ResidenceImmigrationCheck(c *gin.Context) {
	nationality, ror, destination := c.<PERSON>("nationality"), c<PERSON>("region_of_residence"), c.<PERSON>("destination")

	if nationality == "" || ror == "" || destination == "" {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
		})
		return
	}

	q := map[string]any{
		"nationality":         nationality,
		"region_of_residence": ror,
		"destination":         destination,
	}
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	results, err := dao.QueryResidenceImmigrationCheck(q)
	if err != nil {
		response.HandleResponse(c, nil, err)
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    results,
	})
}
