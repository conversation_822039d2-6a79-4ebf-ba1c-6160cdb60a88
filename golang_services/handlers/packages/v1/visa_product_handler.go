package v1

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
)

// GetAvailableProductCountry get all countries that have products
func GetAvailableProductCountry(c *gin.Context) {
	dao, err := middlewares.GetVisaDao(c)

	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	var countryList = map[string]string{}

	if funk.ContainsString([]string{"ets", ""}, c.Query("type")) {
		rows, err := dao.DB().Db.Db.Queryx("SELECT country FROM ets WHERE status = 'active'")
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		defer rows.Close()

		for rows.Next() {
			var doc string
			err := rows.Scan(&doc)
			if err != nil {
				response.HandleResponse(c, nil, err)
				return
			}
			countryList[doc] = doc
		}
	}

	if funk.ContainsString([]string{"visa", ""}, c.Query("type")) {
		rows, err := dao.DB().Db.Db.Queryx("SELECT country FROM visa_product WHERE status = 'active'")
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		defer rows.Close()

		for rows.Next() {
			var doc string
			err := rows.Scan(&doc)
			if err != nil {
				response.HandleResponse(c, nil, err)
				return
			}
			countryList[doc] = doc
		}
	}

	var results = []models.Country{}

	countries, err := dao.DB().GetMasterDataValueByName("country")
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	allCountries := []models.Country{}
	json.Unmarshal([]byte(countries), &allCountries)

	for _, country := range allCountries {
		if _, ok := countryList[country.IsoAlpha3]; ok {
			results = append(results, country)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    results,
	})
}

func GetVisaQuery(c *gin.Context) {
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	q := models.VisaPodFilter{
		Tags: "query",
	}
	res, err := dao.GetVisaPodList(q)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    res.Data,
	})
}

type VisaProductQuery struct {
	Country           string `form:"country"`
	Nationality       string `form:"nationality"`
	RegionOfResidence string `form:"region_of_residence"`
	EntryDate         string `form:"entry_date"`
	ExitDate          string `form:"exit_date"`
}

type DateFromTo struct {
	Value *time.Time `json:"value"`
	From  *time.Time `json:"from"`
	To    *time.Time `json:"to"`
	Valid bool       `json:"valid"`
}

type FilterProductResp struct {
	Country           []string   `json:"service_core_info_country"`
	Nationality       []string   `json:"service_core_info_nationality"`
	Airport           []string   `json:"service_core_info_airport"`
	RegionOfResidence []string   `json:"service_core_info_region_of_residence"`
	EntryDate         DateFromTo `json:"service_core_info_entry_date"`
	ExitDate          DateFromTo `json:"service_core_info_exit_date"`
}
