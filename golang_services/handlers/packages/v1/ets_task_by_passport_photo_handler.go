package v1

import (
	"net/http"
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/gin-gonic/gin"
	"github.com/tidwall/gjson"
)

type createTaskByPassportPhotoRequest struct {
	UseMyProfile      bool             `json:"use_my_profile"`
	RegionOfResidence string           `json:"region_of_residence"`
	MRZData           []map[string]any `json:"mrz_data"`
}

func CreateTaskByPassportPhoto(c *gin.Context) {
	orderID, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	var req createTaskByPassportPhotoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	dao := getEtsDao(c)
	order, err := dao.GetServiceOrderByID(orderID)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	authInfo := middlewares.GetAuthInfo(c)
	var tasks []*models.ServiceTask
	for _, mrzData := range req.MRZData {
		task, err := createEtsTaskOrder(dao, order, authInfo, mrzData, req.UseMyProfile, req.RegionOfResidence)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		tasks = append(tasks, task)
	}

	response.HandleResponse(c, tasks, nil)
}

func InitInputPodsWithScanValues(inputPods models.InputPods, scanValues map[string]any) models.InputPods {
	for k, pod := range inputPods {
		recursionPodByScanValues(pod, nil, scanValues)
		inputPods[k] = pod
	}
	return inputPods
}

func recursionPodByScanValues(pod *models.InputPod, opPod *models.InputPod, scanValues map[string]any) {
	if pod != nil {
		scan := gjson.ParseBytes(pod.Scan).Array()
		if len(scan) > 0 {
			key := strings.ReplaceAll(scan[0].String(), "passport:", "")
			if val, ok := scanValues[key]; ok {
				pod.Value = &models.InputValue{FE: val}
			}
		}

		if pod.Name == "copy_of_photo" || pod.Name == "copy_of_passport_main_page" {
			pod.Value = &models.InputValue{FE: scanValues[pod.Name]}
		}

		if len(pod.OptionChoice) > 0 {
			for k := range pod.OptionChoice {
				if pod.Default == nil {
					continue
				}
				if val, ok := pod.Default.(string); ok && val == k {
					for i := range pod.OptionChoice[k] {
						recursionPodByScanValues(nil, pod.OptionChoice[k][i], scanValues)
					}
				}

			}
		}
	}

	if opPod != nil {
		scan := gjson.ParseBytes(opPod.Scan).Array()
		if len(scan) > 0 {
			key := strings.ReplaceAll(scan[0].String(), "passport:", "")
			if val, ok := scanValues[key]; ok {
				opPod.Value = &models.InputValue{FE: val}
			}
		}

		if opPod.Name == "copy_of_photo" || opPod.Name == "copy_of_passport_main_page" {
			opPod.Value = &models.InputValue{FE: scanValues[opPod.Name]}
		}

		if len(opPod.OptionChoice) > 0 {
			for k := range opPod.OptionChoice {
				if opPod.Default == nil {
					continue
				}
				if val, ok := opPod.Default.(string); ok && val == k {
					for i := range opPod.OptionChoice[k] {
						recursionPodByScanValues(nil, pod.OptionChoice[k][i], scanValues)
					}
				}

			}
		}
	}

}
