package v1

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/payments"
)

type AvailablePaymentOptionsReq struct {
	VisaProductID     int64  `form:"visa_product_id"`
	RegionOfResidence string `form:"region_of_residence"`
}

type PaymentOptions struct {
	Method   string `json:"method"`
	Currency string `json:"currency,omitempty"`
	Data     any    `json:"data,omitempty"`
}

func GetAvailablePaymentOptions(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}
	var req AvailablePaymentOptionsReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if req.VisaProductID <= 0 || len(req.RegionOfResidence) != 3 {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid input, visa_product_id %d, region_of_residence %s", req.VisaProductID, req.RegionOfResidence))
		return
	}
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	res, err := getAvailablePaymentOptions(dao, req.VisaProductID, authInfo, req.RegionOfResidence)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    res,
	})
}

func getAvailablePaymentOptions(dao db.IDao, vpID int64, authInfo *models.AuthInfo, ror string) ([]*PaymentOptions, error) {
	vcpQuery := map[string][]any{
		"visa_product_id": {vpID},
		"status":          {"active"},
	}
	if authInfo.OrgID.Int64 > 0 {
		vcpQuery["organization_id"] = []any{authInfo.OrgID.Int64}
	} else {
		vcpQuery["organization_id"] = []any{}
	}
	prices, err := dao.QueryVisaCustomerPrice(vcpQuery)
	if err != nil {
		return nil, err
	}
	if len(prices) == 0 {
		return nil, nil
	}
	opts := availablePaymenOptions(prices[0].Currency, ror)

	corpOpts, err := corporationPaymentOptions(dao, authInfo, prices[0].Currency)
	if err != nil {
		return nil, err
	}
	if len(corpOpts) > 0 {
		opts = append(opts, corpOpts...)
	}
	return opts, nil
}

func availablePaymentOptionByCurrency(currency string) []*PaymentOptions {
	options := []*PaymentOptions{}
	switch currency {
	case "USD":
		options = append(options, &PaymentOptions{Method: payments.AuthorizePay, Currency: "USD"})
		options = append(options, &PaymentOptions{Method: payments.PayPal, Currency: "USD"})
		options = append(options, &PaymentOptions{Method: payments.ZellePay, Currency: "USD"})
	case "CNY":
		options = append(options, &PaymentOptions{Method: payments.WechatPay, Currency: "CNY"})
		// options = append(options, &PaymentOptions{Method: payments.PayPal, Currency: "CNY"}) // Banned
	case "VND":
		// options = append(options, &PaymentOptions{Method: payments.OnePay, Currency: "VND"})
		// options = append(options, &PaymentOptions{Method: payments.PayPal, Currency: "VND"})
		options = append(options, &PaymentOptions{Method: payments.BankPay, Currency: "VND"})
	}

	return options
}

func availablePaymenOptions(currency, ror string) []*PaymentOptions {
	switch ror {
	case "USA":
		if currency != "USD" {
			return nil
		}
		return []*PaymentOptions{
			{Method: payments.AuthorizePay, Currency: "USD"},
			{Method: payments.PayPal, Currency: "USD"},
			{
				Method:   payments.ZellePay,
				Currency: "USD",
			},
		}
	case "CHN":
		if currency != "CNY" {
			return nil
		}
		return []*PaymentOptions{{Method: payments.WechatPay, Currency: "CNY"}}
	case "VNM":
		if currency == "USD" {
			return []*PaymentOptions{
				{Method: payments.AuthorizePay, Currency: "USD"},
				{Method: payments.PayPal, Currency: "USD"},
			}
		}
		if currency == "VND" {
			return []*PaymentOptions{{Method: payments.OnePay, Currency: "VND"}}
		}
		return nil
	default:
		if currency == "USD" {
			return []*PaymentOptions{{Method: payments.AuthorizePay, Currency: "USD"}}
		}
		return nil
	}
}

func corporationPaymentOptions(dao db.IDao, authInfo *models.AuthInfo, currency string) ([]*PaymentOptions, error) {
	corp, err := dao.GetCorporationByOrgID(authInfo.OrgID.Int64)
	if err != nil {
		return nil, err
	}
	if corp == nil || corp.Status != "active" {
		return nil, nil
	}

	return []*PaymentOptions{{Method: payments.CorpPay, Currency: currency}}, nil
}
