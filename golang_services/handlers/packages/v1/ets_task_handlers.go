package v1

import (
	"fmt"
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
)

type cloneETSTaskReq struct {
	TaskID    int64              `json:"task_id"`
	InputPods []*models.InputPod `json:"input_pods"`
}

type createETSTaskReq struct {
	UseMyProfile      bool   `json:"use_my_profile"`
	RegionOfResidence string `json:"region_of_residence"`
}

func CreateTaskByOrder(c *gin.Context) {
	orderID, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	var req createETSTaskReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	dao := getEtsDao(c)
	order, err := dao.GetServiceOrderByID(orderID)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	authInfo := middlewares.GetAuthInfo(c)
	created, err := createEtsTaskOrder(dao, order, authInfo, nil, req.UseMyProfile, req.RegionOfResidence)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	response.HandleResponse(c, created, nil)
}
func createEtsTaskOrder(dao db.IEtsDao, order *models.ServiceOrder, authInfo *models.AuthInfo, scanValues map[string]any, useMyProfile bool, ror string) (*models.ServiceTask, error) {
	as, err := dao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return nil, err
	}
	if len(as) == 0 {
		return nil, errors.InvalidEtsIDErr
	}
	if as[0].Tasks == nil || len((*as[0]).Tasks) == 0 {
		return nil, fmt.Errorf("service does not contain any task")
	}

	priceMap, err := dao.GetServicePrice([]int{order.ServiceID})
	if err != nil {
		return nil, err
	}

	p := priceMap[order.ServiceID]
	if len(p) == 0 {
		return nil, fmt.Errorf("service without price %d", order.ServiceID)
	}
	price := p[0]

	inputPods, outputPods := ETSToInputOutputPods(dao, order.QueryPodValues, as[0], authInfo, useMyProfile)

	inputPods = InitInputPodsWithScanValues(inputPods, scanValues)

	if err := dao.Begin(); err != nil {
		_ = dao.Rollback()
		return nil, err
	}

	for _, t := range (*as[0]).Tasks {
		task := &models.ServiceTask{
			// ID:         uuid.NewV4().String(),
			Type:       t,
			Status:     models.EtsTaskStatusOpen,
			OrderID:    order.ID,
			ProviderID: price.ProviderID,
		}
		taskInput := getEmptyInputPodsForTask(&(as[0].Schema), t)
		taskInput.Merge(&inputPods)
		task.InputPods = funk.Values(taskInput).([]*models.InputPod)

		taskOutput := getEmptyOutPodsForTask(&(as[0].Schema), t)
		taskOutput.Merge(&outputPods)
		task.OutputPods = funk.Values(taskOutput).([]*models.InputPod)
		if err := dao.CreateServiceTask(task); err != nil {
			_ = dao.Rollback()
			return nil, err
		}
		if err := dao.Commit(); err != nil {
			_ = dao.Rollback()
			return nil, err
		}
		return task, nil

	}

	return nil, nil
}

func DeleteETSTask(c *gin.Context) {
	dao := getEtsDao(c)
	if err := dao.DeleteETSTask([]string{c.Param("task-id")}); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	response.HandleResponse(c, nil, nil)
}

func CloneETSTask(c *gin.Context) {
	var req cloneETSTaskReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	orderID, err := utils.GetIntPathParam(c, "order-id")
	if err != nil || orderID <= 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
		})
		return
	}
	taskID, err := utils.GetIntPathParam(c, "task-id")
	if taskID <= 0 || err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order-id or task-id"))
		return
	}

	dao := getEtsDao(c)

	newTasks, _, err := dao.QueryServiceTasks(map[string]any{"id": taskID}, 0, 0)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(newTasks) == 0 || newTasks[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("not found"))
		return
	}

	oldTasks, _, err := dao.QueryServiceTasks(map[string]any{"id": req.TaskID}, 0, 0)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(oldTasks) == 0 || oldTasks[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("not found"))
		return
	}

	newTasks[0].InputPods = intersectTwoETSTask(oldTasks[0].InputPods, newTasks[0].InputPods)
	if err := dao.UpdateServiceTask(map[string]any{
		"input_pods": newTasks[0].InputPods,
	}, int64(taskID)); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func intersectTwoETSTask(srcPods, descPods models.InputPodsArray) models.InputPodsArray {
	srcPodsMap := map[string]*models.InputPod{}
	for _, item := range srcPods {
		srcPodsMap[item.ID] = item
	}

	for i := range descPods {
		// Copy pod has data
		if srcVal, ok := srcPodsMap[descPods[i].ID]; ok && srcVal.GetFEValue() != nil {
			descPods[i] = srcVal
		}

	}
	return descPods
}
