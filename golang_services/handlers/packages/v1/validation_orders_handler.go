package v1

import (
	"fmt"
	"mime"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/file"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

const (
	validationOrderBucket = "ariadirect_prod_ai_service"
)

func CreateValidationOrder(c *gin.Context) {
	orgID, err := utils.GetIntPathParam(c, "org-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	authInfo := middlewares.GetAuthInfo(c)
	if orgID <= 0 || authInfo == nil || authInfo.UserID == "" || authInfo.OrgID.Int64 != int64(orgID) {
		response.HandleErrResponseWithCode(c, http.StatusUnauthorized, fmt.Errorf("unauthorized"))
		return
	}
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	created, err := createValidationOrder(dao, orgID, authInfo.UserID)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    created,
	})
}

func createValidationOrder(dao db.IDao, orgID int, userID string) (*models.ValidationOrder, error) {
	order := &models.ValidationOrder{
		ID:        0,
		UserID:    userID,
		OrgID:     orgID,
		Status:    "active",
		CreatedAt: time.Now(),
	}

	id, err := dao.CreateValidationOrder(order)
	if err != nil {
		return nil, err
	}
	created, err := dao.QueryValidationOrders(map[string]any{
		"id": id,
	}, 0, 0)
	if err != nil {
		return nil, err
	}
	if len(created) > 0 {
		return created[0], nil
	}
	return nil, nil
}

func UpdateValidationOrder(c *gin.Context) {
	orgID, err := utils.GetIntPathParam(c, "org-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	authInfo := middlewares.GetAuthInfo(c)
	if orgID <= 0 || authInfo == nil || authInfo.UserID == "" || authInfo.OrgID.Int64 != int64(orgID) {
		response.HandleErrResponseWithCode(c, http.StatusUnauthorized, fmt.Errorf("unauthorized"))
		return
	}
	orderID, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	var req models.ValidationOrder
	if err = c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	for _, f := range req.InputFiles {
		bucket, key, err := utils.UrlToS3BucketAndKey(f.File)
		if err != nil {
			response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("bad file url"))
			return
		}
		f.File = fmt.Sprintf("%s/%s", bucket, key)
	}
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	ps := middlewares.GetS3Presigner(c)
	updated, err := updateValidationOrder(dao, ps, orderID, orgID, authInfo.UserID, &req)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    updated,
	})
}

func updateValidationOrder(dao db.IDao, ps *aws.S3Svc, orderID, orgID int, userID string, update *models.ValidationOrder) (*models.ValidationOrder, error) {
	update.ID, update.UserID, update.OrgID = orderID, userID, orgID
	if err := dao.UpdateValidationOrder(update); err != nil {
		return nil, err
	}

	updated, err := dao.QueryValidationOrders(map[string]any{
		"id": orderID,
	}, 0, 0)

	if err != nil {
		return nil, err
	}
	updated, err = presignFilesInOrder(ps, updated)
	if err != nil {
		return nil, err
	}
	if len(updated) > 0 {
		return updated[0], nil
	}
	return nil, nil
}

type validationOrderQuery struct {
	Offset   uint  `form:"offset"`
	Limit    uint  `form:"limit"`
	OrderIDs []int `form:"order_ids"`
}

func QueryValidationOrders(c *gin.Context) {
	orgID, err := utils.GetIntPathParam(c, "org-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	authInfo := middlewares.GetAuthInfo(c)
	if orgID <= 0 || authInfo == nil || authInfo.UserID == "" || authInfo.OrgID.Int64 != int64(orgID) {
		response.HandleErrResponseWithCode(c, http.StatusUnauthorized, fmt.Errorf("unauthorized"))
		return
	}
	var q validationOrderQuery
	if err := c.ShouldBindQuery(&q); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	query := map[string]any{
		"org_id": orgID,
	}
	if len(q.OrderIDs) > 0 {
		query["id"] = q.OrderIDs
	}
	res, err := dao.QueryValidationOrders(query, q.Limit, q.Offset)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	ps := middlewares.GetS3Presigner(c)
	res, err = presignFilesInOrder(ps, res)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    res,
	})
}

func GenerateUploadUrlForValidationOrder(c *gin.Context) {
	orgID, err := utils.GetIntPathParam(c, "org-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	authInfo := middlewares.GetAuthInfo(c)
	if orgID <= 0 || authInfo == nil || authInfo.UserID == "" || authInfo.OrgID.Int64 != int64(orgID) {
		response.HandleErrResponseWithCode(c, http.StatusUnauthorized, fmt.Errorf("unauthorized"))
		return
	}
	orderID, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	orders, err := dao.QueryValidationOrders(map[string]any{
		"org_id": orgID,
		"id":     orderID,
	}, 0, 0)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(orders) == 0 || orders[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order not found"))
		return
	}
	var req UploadRequest
	if err = c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	if req.FileName == "" {

		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
		return
	}
	var mimeType string
	if !strings.HasPrefix(req.ContentType, ".") {
		mimeType = mime.TypeByExtension("." + req.ContentType)
	} else {
		mimeType = mime.TypeByExtension(req.ContentType)
	}
	bucket := middlewares.GetS3Buckets(c)[validationOrderBucket].(string)
	key := file.BuildValidationOrderInputS3Key(orgID, orderID, req.FileName)

	ps := middlewares.GetS3Presigner(c)
	presigned, err := ps.PresignUploadUrl(bucket, key, mimeType, 15*time.Minute)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	downURLpresigned, err := ps.PresignUrl(bucket, key, expirationForFiles)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"upload_url":   presigned,
			"download_url": downURLpresigned,
		},
	})
}

func presignFilesInOrder(ps *aws.S3Svc, orders []*models.ValidationOrder) ([]*models.ValidationOrder, error) {
	for _, o := range orders {
		for _, f := range o.InputFiles {
			bucket, key := utils.GetBucketAndKeyFromS3Path(f.File)
			presigned, err := ps.PresignUrl(bucket, key, expirationForFiles)
			if err != nil {
				return nil, err
			}
			f.File = presigned
		}
	}
	return orders, nil
}
