package v1

import (
	"fmt"
	"net/http"
	"sort"
	"sync"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/file"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	uuid "github.com/satori/go.uuid"
)

var (
	analyzer aws.ImageAnalyzer
)

type BatchMatchPassportReq struct {
	Bucket    string   `json:"bucket"`
	ImageKeys []string `json:"image_keys"`
	BatchID   string   `json:"batch_id"`
	ProductID int64    `json:"product_id"`
}

type MatchedPassport struct {
	PassportImage string  `json:"passport_image"`
	PersonalImage string  `json:"personal_image"`
	Confidence    float64 `json:"confidence"`
}

type BatchMatchPassportResponse struct {
	MatchedPassports []*MatchedPassport `json:"matched_passports"`
	UnmatchedImages  []string           `json:"unmatched_images"`
}

func InitImageAnalyzer(sess *session.Session) {
	analyzer = aws.NewRekImageAnalyzer(sess)
}

func BatchMatchPassport(c *gin.Context) {
	var req BatchMatchPassportReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	presigner := middlewares.GetS3Presigner(c)
	res, err := batchMatchPassport(req.Bucket, req.ImageKeys, presigner)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	response.HandleResponse(c, res, nil)
}

func batchMatchPassport(bucket string, keys []string, presigner *aws.S3Svc) (*BatchMatchPassportResponse, error) {
	categorized, err := analyzer.CategorizeImages(bucket, keys)
	if err != nil {
		return nil, err
	}

	matched, unmatched, err := matchPassports(bucket, categorized.Passports, categorized.PersonalPhotos)
	if err != nil {
		return nil, err
	}

	if err := presignMatchedPassports(bucket, matched, presigner); err != nil {
		return nil, err
	}
	unmatched, err = presignFiles(bucket, unmatched, presigner)
	if err != nil {
		return nil, err
	}
	others, err := presignFiles(bucket, categorized.Others, presigner)
	if err != nil {
		return nil, err
	}
	return &BatchMatchPassportResponse{
		MatchedPassports: matched,
		UnmatchedImages:  append(unmatched, others...),
	}, nil
}

func presignFiles(bucket string, keys []string, presigner *aws.S3Svc) ([]string, error) {
	res := make([]string, 0, len(keys))
	for _, k := range keys {
		url, err := presigner.PresignUrl(bucket, k, expirationForFiles)
		if err != nil {
			return nil, err
		}
		res = append(res, url)
	}
	return res, nil
}

type matchingResult struct {
	matched    bool
	img1       string
	img2       string
	confidence float64
	err        error
}

func matchPassports(bucket string, passports, personalPhotos []string) ([]*MatchedPassport, []string, error) {
	var wg sync.WaitGroup
	resCh := make(chan *matchingResult, len(passports)*len(personalPhotos))
	matchedFiles := make(map[string]bool)
	mu := sync.Mutex{}

	for _, passport := range passports {
		wg.Add(1)

		go func(pp string) {
			defer wg.Done()
			mu.Lock()
			if matchedFiles[pp] {
				mu.Unlock()
				return
			}
			mu.Unlock()

			for _, photo := range personalPhotos {
				mu.Lock()
				if matchedFiles[photo] {
					mu.Unlock()
					continue
				}
				mu.Unlock()

				confidence, err := analyzer.GetFaceSimilarity(bucket, pp, photo)
				if err != nil {
					resCh <- &matchingResult{err: err, img1: pp, img2: photo}
					continue
				}
				if confidence > 0 {
					mu.Lock()
					matchedFiles[pp] = true
					matchedFiles[photo] = true
					mu.Unlock()
					resCh <- &matchingResult{
						matched:    true,
						img1:       pp,
						img2:       photo,
						confidence: confidence,
					}
					return
				}
			}
		}(passport)
	}

	go func() {
		wg.Wait()
		close(resCh)
	}()

	var matchingResults []*matchingResult
	for m := range resCh {
		if m.err != nil {
			log.Error().Str("passport", m.img1).Str("photo", m.img2).Err(m.err).Msg("failed to match pair")
			continue
		}
		if !m.matched {
			continue
		}
		matchingResults = append(matchingResults, m)
	}

	sort.SliceStable(matchingResults, func(i, j int) bool {
		return matchingResults[i].confidence > matchingResults[j].confidence
	})

	var matchedPassports []*MatchedPassport
	for _, match := range matchingResults {
		if matchedFiles[match.img1] || matchedFiles[match.img2] {
			continue
		}
		matchedPassports = append(matchedPassports, &MatchedPassport{
			PassportImage: match.img1,
			PersonalImage: match.img2,
			Confidence:    match.confidence,
		})
	}

	var unMatchedFiles []string
	for _, k := range passports {
		if !matchedFiles[k] {
			unMatchedFiles = append(unMatchedFiles, k)
		}
	}

	for _, k := range personalPhotos {
		if !matchedFiles[k] {
			unMatchedFiles = append(unMatchedFiles, k)
		}
	}

	return matchedPassports, unMatchedFiles, nil
}

func presignMatchedPassports(bucket string, matchedPassports []*MatchedPassport, presigner *aws.S3Svc) error {
	for _, m := range matchedPassports {
		passportUrl, err := presigner.PresignUrl(bucket, m.PassportImage, expirationForFiles)
		if err != nil {
			return err
		}
		photoUrl, err := presigner.PresignUrl(bucket, m.PersonalImage, expirationForFiles)
		if err != nil {
			return err
		}
		m.PassportImage, m.PersonalImage = passportUrl, photoUrl
	}
	return nil
}

type BatchUploadRequest struct {
	Files   []*UploadRequest `json:"files"`
	BatchID string           `json:"batch_id"`
}

type UploadResp struct {
	UploadUrl   string `json:"upload_url"`
	DownloadUrl string `json:"download_url"`
}

func GenerateBatchMatchingUploadUrl(c *gin.Context) {
	var upload BatchUploadRequest
	if err := c.ShouldBindJSON(&upload); err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	authUser := middlewares.GetAuthInfo(c)
	if authUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "missing user info",
		})
		return
	}
	ps := middlewares.GetS3Presigner(c)
	bucket := middlewares.GetS3Buckets(c)[PassportImagesUploadBucketKey].(string)
	batchID := upload.BatchID
	if batchID == "" {
		batchID = uuid.NewV4().String()
	}
	ownerID := fmt.Sprintf("%d", authUser.OrgID.Int64)
	if ownerID == "0" {
		ownerID = authUser.UserID
	}

	res := map[string]*UploadResp{}
	for _, u := range upload.Files {
		if u.FileName == "" {
			continue
		}

		key := file.BuildBatchUploadKey(u.FileName, ownerID, batchID)

		// mimeType := mime.TypeByExtension(filepath.Ext(u.FileName))

		uploadURLpresigned, err := ps.PresignUploadUrl(bucket, key, "", 15*time.Minute)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
		downURLpresigned, err := ps.PresignUrl(bucket, key, expirationForFiles)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
		res[u.FileName] = &UploadResp{
			UploadUrl:   uploadURLpresigned,
			DownloadUrl: downURLpresigned,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"batch_id":    batchID,
			"upload_urls": res,
		},
	})
}

func batchMatchPassportNoPresign(bucket string, keys []string) (*BatchMatchPassportResponse, error) {
	categorized, err := analyzer.CategorizeImages(bucket, keys)
	if err != nil {
		return nil, err
	}
	if len(categorized.Passports) == 0 || len(categorized.PersonalPhotos) == 0 {
		return &BatchMatchPassportResponse{
			UnmatchedImages: categorized.Others,
		}, nil
	}
	log.Info().
		Fields(map[string]any{"passports": categorized.Passports, "photos": categorized.PersonalPhotos}).
		Msg("batch categorization result")

	matched, unmatched, err := matchPassports(bucket, categorized.Passports, categorized.PersonalPhotos)
	if err != nil {
		return nil, err
	}

	return &BatchMatchPassportResponse{
		MatchedPassports: matched,
		UnmatchedImages:  append(unmatched, categorized.Others...),
	}, nil
}
