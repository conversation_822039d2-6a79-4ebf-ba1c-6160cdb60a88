package v1

import (
	"time"
)

type SubmitterRequest struct {
	PackageID     int    `json:"package_id"`
	PackageStatus string `json:"package_status"`
	SummaryFile   string `json:"summary_file"`
}

type ShipmentRequest struct {
	PackageID       int       `json:"package_id"`
	TrackingNumber  string    `json:"tracking_number"`
	TrackingURL     string    `json:"tracking_url"`
	Currency        string    `json:"currency"`
	Price           float64   `json:"price"`
	Label           string    `json:"label"`
	Status          string    `json:"status"`
	ShipDate        time.Time `json:"ship_date"`
	ShipmentContent string    `json:"shipment_content"`
}

type EVisaFormUpload struct {
	Code    string `json:"code"`
	FileURL string `json:"file_url"`
}
