package v1

import (
	"net/http"
	"path"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	aderr "bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func ListMemberships(c *gin.Context) {
	travelInfo := middlewares.GetTravelerInfo(c)
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	memberships, err := listMemberships(dao, travelInfo.ID)
	if err != nil {
		response.HandleResponse(c, nil, err)
	}
	ps := middlewares.GetS3Presigner(c)
	for i := 0; i < len(memberships); i++ {
		memberships[i].Images = presignAllFilesInMap(ps, memberships[i].Images, expirationForFiles)
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberships,
	})
}

func listMemberships(dao db.IDao, travelerID string) ([]*models.Membership, error) {
	memberships, err := dao.QueryMembership(map[string]any{"traveler_id": travelerID, "status": "active"})
	if err != nil {
		return nil, err
	}
	return memberships, nil
}

func AddMembership(c *gin.Context) {
	travelInfo := middlewares.GetTravelerInfo(c)

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	var newMembership models.Membership
	if err := c.ShouldBind(&newMembership); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	newMembership.UserID = travelInfo.UserID
	newMembership.TravelerID = travelInfo.ID
	// checking membership number exist
	memberships, err := dao.QueryMembership(map[string]any{
		"status":            "active",
		"traveler_id":       travelInfo.ID,
		"membership_number": newMembership.MembershipNumber,
		"name":              newMembership.Name,
		"type":              newMembership.Type,
		"expiration_date":   newMembership.ExpirationDate,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(memberships) > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   errors.AlreadyExistErr,
		})
		return
	}

	added, err := addMembership(dao, &newMembership)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	ps := middlewares.GetS3Presigner(c)
	added.Images = presignAllFilesInMap(ps, added.Images, expirationForFiles)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    added,
	})
}

func addMembership(dao db.IDao, membership *models.Membership) (*models.Membership, error) {
	if membership.Images != nil {
		membership.Images = parseUrlToS3(*membership.Images)
	}

	id, err := dao.InsertMembership(membership)
	if err != nil {
		return nil, err
	}
	return dao.GetMembershipByID(id)
}

func UpdateMembership(c *gin.Context) {
	travelInfo := middlewares.GetTravelerInfo(c)

	id, err := utils.GetIntPathParam(c, "membership-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	var update models.Membership
	if err := c.ShouldBind(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	updated, err := updateMembership(dao, travelInfo.ID, id, &update)
	if err != nil {
		response.HandleResponse(c, nil, err)
	}
	ps := middlewares.GetS3Presigner(c)
	updated.Images = presignAllFilesInMap(ps, updated.Images, expirationForFiles)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    updated,
	})
}

func updateMembership(dao db.IDao, travelerID string, id int, update *models.Membership) (*models.Membership, error) {
	existing, err := dao.GetMembershipByID(id)
	if err != nil {
		return nil, err
	}
	if existing == nil || existing.TravelerID != travelerID || existing.Status != "active" {
		return nil, aderr.NotFoundErr
	}
	if update.Images != nil {
		update.Images = parseUrlToS3(*update.Images)
	}
	if err = dao.UpdateMembership(id, update); err != nil {
		return nil, err
	}
	return dao.GetMembershipByID(id)
}

func DeleteMembership(c *gin.Context) {
	travelInfo := middlewares.GetTravelerInfo(c)

	id, err := utils.GetIntPathParam(c, "membership-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err = deactivateMembership(dao, travelInfo.ID, id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func deactivateMembership(dao db.IDao, travelerID string, id int) error {
	existing, err := dao.GetMembershipByID(id)
	if err != nil {
		return err
	}
	if existing == nil || existing.TravelerID != travelerID || existing.Status != "active" {
		return aderr.NotFoundErr
	}

	if err = dao.DeactivateMembership(id); err != nil {
		return err
	}
	return nil
}

func parseUrlToS3(files models.PropertyMap) *models.PropertyMap {
	var res = models.PropertyMap{}
	for k, item := range files {
		bucket, key, err := utils.UrlToS3BucketAndKey(item.(string))
		if err != nil {
			log.Warn().Str("membership", item.(string)).Str("err", err.Error()).Msg("failed to parse url to s3")
		} else {
			res[k] = path.Join(bucket, key)
		}
	}
	return &res
}

func presignAllFilesInMap(ps *aws.S3Svc, m *models.PropertyMap, expire time.Duration) *models.PropertyMap {
	if m == nil {
		return nil
	}
	var res = models.PropertyMap{}
	for k, v := range *m {
		bucket, key, err := utils.UrlToS3BucketAndKey(v.(string))
		if err != nil {
			log.Warn().Fields(map[string]any{
				"file_path": v,
				"error":     err.Error(),
			}).Msg("cannot convert url to s3 bucket and key")
			continue
		}
		presigned, err := ps.PresignUrl(bucket, key, expire)
		if err != nil {
			log.Warn().Fields(map[string]any{
				"error":  err.Error(),
				"bucket": bucket,
				"key":    key,
			}).Msg("failed to presign")
			continue
		}
		res[k] = presigned
	}
	return &res
}

func GetMembershipProvider(c *gin.Context) {
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	q := map[string]any{}
	types := c.Query("type")
	if types != "" {
		q["type"] = types
	}

	providers, err := dao.QueryMembershipProvider(q)
	if err != nil {
		response.HandleResponse(c, nil, err)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    providers,
	})
}
