package v1

import (
	"fmt"
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/fedex"
	"bitbucket.org/persistence17/aria/golang_services/sdk/product"
	"bitbucket.org/persistence17/aria/golang_services/sdk/shipment"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/gin-gonic/gin"
)

type UpdateVisaShipmentLabelReq struct {
	Label           string `json:"label"`
	TrackingNumber  string `json:"tracking_number"`
	ShippingContent string `json:"shipping_content"`
}

func UpdateVisaShipmentLabel(c *gin.Context) {
	var req UpdateVisaShipmentLabelReq
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	shipment, err := dao.GetVisaShipment(c.Param("shipment_id"))
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if label, ok := shipment.Labels[req.ShippingContent]; ok {
		label.Label = req.Label
		label.TrackingNumber = req.TrackingNumber
		shipment.Labels[req.ShippingContent] = label
	} else {
		shipment.Labels[req.ShippingContent] = models.Label{
			Label:          req.Label,
			TrackingNumber: req.TrackingNumber,
		}
	}

	if err := dao.UpdateVisaShipment(shipment.ID, shipment); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	// if err := dao.DB().ORM().Exec(`UPDATE package SET output_files = output_files || ?::jsonb WHERE shipment_info = ?`, fmt.Sprintf("{%q:%q}", req.ShippingContent, req.Label), shipment.ID).Error; err != nil {
	// 	response.HandleResponse(c, nil, err)
	// 	return
	// }

	if err := dao.DB().ORM().Exec(`UPDATE service_orders SET output_files = output_files || ?::jsonb WHERE shipment_info = ?`, fmt.Sprintf("{%q:%q}", req.ShippingContent, req.Label), shipment.ID).Error; err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    shipment.Labels,
	})
}

type ReGenerateLabelReq struct {
	ShippingContent string `json:"shipping_content"`
	TrackingNumber  string `json:"tracking_number"`
}

func ReGenerateLabelForETS(c *gin.Context) {
	var req ReGenerateLabelReq
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	etsDao := getEtsDao(c)
	if err := reGenerateLabelForETS(dao, etsDao, c.Param("order-id"), req.ShippingContent); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func reGenerateLabelForETS(dao db.IDao, etsDao db.IEtsDao, orderID, content string) error {
	q := models.ServiceOrderFilter{
		ID:              []string{orderID},
		IncludeTasks:    true,
		IncludeService:  true,
		IncludeShipment: true,
		Limit:           1,
	}
	existing, err := etsDao.QueryServiceOrders(q)
	if err != nil {
		return err
	}

	if len(existing.Data) == 0 {
		return fmt.Errorf("Order is not found")
	}
	order := existing.Data[0]

	userContact, err := shipment.GetContactOfUser(dao, order.ShipmentInfo.String)
	if err != nil {
		return err
	}

	var provider models.EtsProvider
	if err := dao.DB().ORM().Select("id, name, address, contact").Table("ets_provider").Where("id = ?", order.ProviderID).First(&provider).Error; err != nil {
		return err
	}

	user, err := dao.GetUserByID(order.UserID)
	if err != nil {
		return err
	}

	var from, to = userContact, &fedex.ShipmentContact{
		City:    provider.Address.City,
		Name:    provider.Name,
		State:   provider.Address.State,
		Phone:   provider.Contact.Phone,
		Email:   provider.Contact.Email,
		Address: provider.Address.Address,
		ZipCode: provider.Address.ZipCode.String,
		Country: provider.Address.Country,
	}

	if content == "consulate_to_user" {
		from, to = to, from
	}

	ets, err := etsDao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return err
	}

	// BEGIN: Get SQS Queue
	awsUSConfig := utils.GetMapEnv("ad_aws")
	if awsUSConfig["region"].(string) == "" {
		return fmt.Errorf("missing region in ad_aws")
	}
	awsConfig := aws.NewConfig().WithRegion(awsUSConfig["region"].(string)).WithLogLevel(aws.LogOff)

	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return err
	}

	sqsConfigMap := utils.GetMapEnv("ad_sqs")
	sqsShipURL := sqsConfigMap["url_prefix"].(string) + "/" + sqsConfigMap["shipment_sqs_name"].(string)
	sqsShip := awslib.NewSQSClient(sess, sqsShipURL)
	// END: Get SQS Queue
	adEndpoint := utils.GetMapEnv("ad_package_service")

	attributeJ := utils.StructToJSON(order.Service.Attributes)
	if shippingServiceID := attributeJ.Get("shipping_labels.consulate_to_user.shipping_service_id").Int(); shippingServiceID > 0 {
		shippingService, err := dao.GetShippingServiceByID(shippingServiceID)
		if err != nil {
			return err
		}

		data := shipment.CreateShipmentData{
			UserID:          order.UserID,
			UserEmail:       user.Email,
			SqsShip:         sqsShip,
			SqsNotification: nil,
			ProductType:     product.ETSProductType,
			OrderID:         order.ID,
			ProductID:       order.ServiceID,
			ShippingContent: "consulate_to_user",
			Config: map[string]any{
				"package_host_name": utils.StructToJSON(adEndpoint).Get("host_name").String(),
			},
			ShipmentCarrier:  shippingService.Carrier,
			Status:           order.Status,
			ServiceType:      shippingService.Service,
			From:             from,
			To:               to,
			OrderServiceType: ets[0].ServiceType,
		}
		if err = shipment.CreateShipment(data); err != nil {
			return err
		}
	}
	return nil

}
