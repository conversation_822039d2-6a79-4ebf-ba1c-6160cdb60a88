package v1

import (
	"crypto/md5"
	"encoding/hex"
	"net/http"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/gin-gonic/gin"
)

func CreateShortenLink(c *gin.Context) {
	var req models.ShortenLink
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	urlEncode := md5.Sum([]byte(req.URL))
	urlMD5 := hex.EncodeToString(urlEncode[:])
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.<PERSON>rror(),
		})
		return
	}

	if err := dao.CreateShortenLink(&models.ShortenLink{
		ShortenCode: urlMD5,
		URL:         req.URL,
		ExpiredAt:   aws.Time(time.Now().Add(24 * time.Hour)),
	}); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    urlMD5,
	})
}

func GetUnShortenLink(c *gin.Context) {
	code := c.Query("code")

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	data, err := dao.GetShortenLink(code)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data.URL,
	})
}
