package v1

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	aderr "bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/ets"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/gin-gonic/gin"
	"gopkg.in/guregu/null.v3"
)

const (
	TimestampFormat = "2006-01-02T15:04:05Z"
)

func GetTravelerByUser(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	user, err := dao.GetUserByID(c.Query("user_id"))
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if !authInfo.IsADAdmin() {
		if user == nil || user.OrganizationID.Int64 != authInfo.OrgID.Int64 {
			response.HandleResponse(c, nil, fmt.Errorf("User don't have permission"))
			return
		}
	}

	result, err := dao.QueryTravelersWithType(user.ID, string(models.ProfileTypeMyProfile))
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":    result,
		"success": true,
	})
}

func CreateTravelerByUser(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	user, err := dao.GetUserByID(c.Query("user_id"))
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if user == nil || user.OrganizationID.Int64 != authInfo.OrgID.Int64 {
		response.HandleResponse(c, nil, fmt.Errorf("User don't have permission"))
		return
	}

	var newItem models.Traveler
	if err := c.ShouldBind(&newItem); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	newItem.UserID = user.ID
	travelerID, err := dao.InsertTraveler(&newItem)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	added, err := getTraveler(dao, travelerID)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    added,
	})
}

func ListTravelers(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil || authInfo.UserID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "missing user info",
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	var result []*models.Traveler
	if profileType := c.Query("profile_type"); profileType != "" {
		if profileType != string(models.ProfileTypeMyProfile) {
			profileType = ""
		}
		result, err = dao.QueryTravelersWithType(authInfo.UserID, profileType)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	} else {
		q := map[string][]any{"user_id": {authInfo.UserID}}
		result, err = dao.QueryTravelers(q)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}
	c.JSON(http.StatusOK, gin.H{
		"data":    result,
		"success": true,
	})
}

func CreateTraveler(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil || authInfo.UserID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "missing user info",
		})
		return
	}

	var newItem models.Traveler
	if err := c.ShouldBind(&newItem); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	newItem.UserID = authInfo.UserID
	travelerID, err := dao.InsertTraveler(&newItem)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	added, err := getTraveler(dao, travelerID)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    added,
	})
}

func getTraveler(dao db.IDao, travelerID string) (*models.Traveler, error) {
	tr, err := dao.GetTravelerByID(travelerID)
	if err != nil {
		return nil, err
	}
	return tr, nil
}

func GetTraveler(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil || authInfo.UserID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "missing user info",
		})
		return
	}

	travelerID, err := utils.GetStringPathParam(c, "traveler-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	result, err := getTraveler(dao, travelerID)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func UpdateTraveler(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil || authInfo.UserID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "missing user info",
		})
		return
	}

	id, err := utils.GetStringPathParam(c, "traveler-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	var update map[string]any
	if err := c.ShouldBind(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	updated, err := updateTraveler(dao, authInfo.UserID, id, update)
	if err != nil {
		response.HandleResponse(c, nil, err)
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    updated,
	})
}

func updateTraveler(dao db.IDao, userID string, id string, update map[string]any) (*models.Traveler, error) {
	existing, err := dao.GetTravelerByID(id)
	if err != nil {
		return nil, err
	}
	if existing == nil || existing.UserID != userID {
		return nil, aderr.NotFoundErr
	}
	if err = dao.UpdateTraveler(id, update); err != nil {
		return nil, err
	}
	return dao.GetTravelerByID(id)
}

func DeleteTraveler(c *gin.Context) {

	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil || authInfo.UserID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "missing user info",
		})
		return
	}

	id, err := utils.GetStringPathParam(c, "traveler-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err = dao.DeleteTraveler(id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func SetMyProfileForTraveler(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil || authInfo.UserID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "missing user info",
		})
		return
	}

	id, err := utils.GetStringPathParam(c, "traveler-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err = setMyProfile(dao, authInfo.UserID, id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func setMyProfile(dao db.IDao, userID string, id string) error {
	traveler, err := dao.GetTravelerByID(id)
	if err != nil {
		return err
	}
	if traveler == nil || traveler.UserID != userID {
		return aderr.NotFoundErr
	}

	new := models.TravelerTypeIndex{
		UserID:      userID,
		TravelerID:  id,
		ProfileType: models.ProfileTypeMyProfile,
	}
	if err = dao.InsertTravelerTypeIndex(&new); err != nil {
		return err
	}

	return nil
}

type ImportAppReq []struct {
	AppID      int    `json:"app_id"`
	TravelerID string `json:"traveler_id"`
}

type ImportTaskReq []struct {
	TaskID     int    `json:"task_id"`
	TravelerID string `json:"traveler_id"`
}

func ImportTaskToTraveler(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil || authInfo.UserID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "missing user info",
		})
		return
	}

	var requests ImportTaskReq
	if err := c.ShouldBindJSON(&requests); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	for _, req := range requests {
		etsDao := getEtsDao(c)
		tasks, _, err := etsDao.QueryServiceTasks(map[string]any{"id": req.TaskID}, 0, 1)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

		dao, err := middlewares.GetVisaDao(c)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

		// import input_pods of application to traveler
		traveler, err := getTraveler(dao, req.TravelerID)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}

		if traveler == nil {
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
				"success": false,
			})
			return
		}

		tasks[0] = ets.FlattenInputPods(*tasks[0])
		// get profile list from input pods
		var inputPods []*models.InputPod
		for _, item := range tasks[0].InputPods {
			if item.Profile != "" {
				inputPods = append(inputPods, item)
			}
		}

		err = importApplication(dao, inputPods, authInfo.UserID, traveler.ID)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

type TravelerData struct {
	Table   string
	Column  string
	Value   any
	Special string
}

func importApplication(dao db.IDao, inputPods []*models.InputPod, userID string, travelerID string) error {
	if len(inputPods) > 0 {
		// group by sub category
		inputPodsGroup := map[string][]TravelerData{}
		for _, item := range inputPods {
			profiles := strings.Split(item.Profile, ":")
			if len(profiles) >= 2 && item.Value != nil && item.Value.FE != nil {
				temp := TravelerData{
					Table:  profiles[0],
					Column: profiles[1],
					Value:  item.Value.FE,
				}
				if item.Type == "object" && item.TypeFE == "input_phone" {
					temp.Value = utils.StructToJSON(item.Value.FE).Get("phone").String()
				}
				if len(profiles) >= 3 {
					temp.Special = profiles[2]
				}
				key := fmt.Sprintf("%s_%s", item.Category, item.SubCategory)
				inputPodsGroup[key] = append(inputPodsGroup[key], temp)
			}
		}

		// save data to traveler
		for _, group := range inputPodsGroup {
			switch group[0].Table {
			case "personal_info":
				{
					// check type of value by format "." Ex: home_address.country
					data := map[string]any{}
					columns := strings.Split(group[0].Column, ".")
					isJsonType := len(columns) == 2
					if columns[0] != "travel_history" { // don't import for travel history
						if isJsonType {
							jsonData := map[string]any{}
							for _, item := range group {
								titles := strings.Split(item.Column, ".")
								jsonData[titles[1]] = item.Value
							}
							data[columns[0]] = jsonData
						} else {
							for _, item := range group {

								data[item.Column] = item.Value
							}
						}

						if err := dao.Begin(); err != nil {
							dao.Rollback()
							return err
						}
						if _, err := updateTraveler(dao, userID, travelerID, data); err != nil {
							dao.Rollback()
							return err
						}

						if err := dao.Commit(); err != nil {
							dao.Rollback()
							return err
						}
					}
				}
			case db.ContactBookTable:
				{
					row := models.ContactBookItem{
						UserID:     userID,
						TravelerID: travelerID,
					}
					query := map[string]any{"status": "active"}
					for _, item := range group {
						switch item.Column {
						case "given_name":
							row.GivenName = strings.ToUpper(item.Value.(string))
							query["given_name"] = row.GivenName
						case "surname":
							row.Surname = strings.ToUpper(item.Value.(string))
							query["surname"] = row.Surname
						case "middle_name":
							row.MiddleName = strings.ToUpper(item.Value.(string))
							query["middle_name"] = row.MiddleName
						case "phone":
							row.Phone = item.Value.(string)
							query["phone"] = row.Phone
						case "email":
							row.Email = strings.ToUpper(item.Value.(string))
							query["email"] = row.Email
						case "relationship":
							row.Relationship = item.Value.(string)
							query["relationship"] = row.Relationship
						case "ref_name":
							row.RefName = item.Value.(string)
							query["ref_name"] = row.RefName
						}
					}
					// special case
					if row.Relationship == "" && group[0].Special != "" {
						specials := strings.Split(group[0].Special, "-")
						if len(specials) >= 2 {
							row.Relationship = specials[1]
							query["relationship"] = row.Relationship
						}
					}
					if err := dao.Begin(); err != nil {
						dao.Rollback()
						return err
					}
					// check exists
					contacts, err := dao.GetContactBookItemsByTravelerID(travelerID, query)
					if err != nil {
						dao.Rollback()
						return err
					}
					// insert
					if len(contacts) == 0 {
						if _, err := addContact(dao, &row); err != nil {
							dao.Rollback()
							return err
						}
					}
					if err = dao.Commit(); err != nil {
						dao.Rollback()
						return err
					}
				}
			case db.AddressBookTable:
				{
					row := models.AddressBookItem{
						UserID:     userID,
						TravelerID: travelerID,
					}
					query := map[string]any{"status": "active"}
					for _, item := range group {
						switch item.Column {
						case "company_name":
							row.CompanyName = null.NewString(strings.ToUpper(item.Value.(string)), true)
							query["company_name"] = row.CompanyName.String
						case "address":
							row.Address = strings.ToUpper(item.Value.(string))
							query["address"] = row.Address
						case "city":
							row.City = strings.ToUpper(item.Value.(string))
							query["city"] = row.City
						case "zip_code":
							row.ZipCode = strings.ToUpper(item.Value.(string))
							query["zip_code"] = row.ZipCode
						case "state":
							row.State = item.Value.(string)
							query["state"] = row.State
						case "country":
							row.Country = item.Value.(string)
							query["country"] = row.Country
						}
					}
					// special case
					if row.CompanyName.Valid {
						row.Type = string(models.AddressTypeBusiness)
					} else {
						row.Type = string(models.AddressTypeResidential)
					}
					query["type"] = row.Type
					if err := dao.Begin(); err != nil {
						dao.Rollback()
						return err
					}
					// check exists
					addresses, err := dao.GetAddressBookItemsByTravelerID(travelerID, query)
					if err != nil {
						dao.Rollback()
						return err
					}
					// insert
					if len(addresses) == 0 {
						row.Validated = false
						if _, err := addAddress(dao, &row); err != nil {
							dao.Rollback()
							return err
						}
					}
					if err = dao.Commit(); err != nil {
						dao.Rollback()
						return err
					}
				}
			case db.PassportBookTable:
				{
					row := models.PassportBook{
						UserID:     userID,
						TravelerID: travelerID,
					}
					query := map[string]any{"traveler_id": travelerID}
					for _, item := range group {
						switch item.Column {
						case "given_name":
							row.GivenName = strings.ToUpper(item.Value.(string))
							query["given_name"] = row.GivenName
						case "surname":
							row.Surname = strings.ToUpper(item.Value.(string))
							query["surname"] = row.Surname
						case "middle_name":
							row.MiddleName = strings.ToUpper(item.Value.(string))
							query["middle_name"] = row.MiddleName
						case "gender":
							row.Gender = item.Value.(string)
							query["gender"] = row.Gender
						case "nationality":
							row.Nationality = item.Value.(string)
							query["nationality"] = row.Nationality
						case "date_of_birth":
							date, err := time.Parse(TimestampFormat, item.Value.(string))
							if err != nil {
								return nil
							}
							row.DateOfBirth = &date
							query["date_of_birth"] = row.DateOfBirth
						case "expiration_date":
							date, err := time.Parse(TimestampFormat, item.Value.(string))
							if err != nil {
								return nil
							}
							row.ExpirationDate = &date
							query["expiration_date"] = row.ExpirationDate
						case "issue_date":
							date, err := time.Parse(TimestampFormat, item.Value.(string))
							if err != nil {
								return nil
							}
							row.IssueDate = &date
							query["issue_date"] = row.IssueDate
						case "country_of_birth":
							row.CountryOfBirth = item.Value.(string)
							query["country_of_birth"] = row.CountryOfBirth
						case "city_of_birth":
							row.CityOfBirth = item.Value.(string)
							query["city_of_birth"] = row.CityOfBirth
						case "country_issuing_authority":
							row.CountryIssuingAuthority = item.Value.(string)
							query["country_issuing_authority"] = row.CountryIssuingAuthority
						}
					}
					// check exists
					passports, err := dao.QueryPassportBook(query)
					if err != nil {
						return err
					}
					// insert
					if len(passports) == 0 {
						if _, err := addPassportBook(dao, &row); err != nil {
							return err
						}
					}
				}
			}
		}
	}
	return nil
}

type ImportPassportReq struct {
	OrderIDs []string `json:"order_ids"`
}

func ImportPassportToTraveler(c *gin.Context) {
	auth := middlewares.GetAuthInfo(c)
	if auth == nil || auth.UserID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "missing user info",
		})
		return
	}

	travelerID, err := utils.GetStringPathParam(c, "traveler-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	var req ImportPassportReq
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	etsDao := getEtsDao(c)

	// validate passport
	orders, err := etsDao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:              req.OrderIDs,
		IncludeTasks:    true,
		IncludeService:  true,
		IncludePrice:    true,
		IncludePayment:  true,
		IncludeShipment: true,
		Limit:           1,
	})
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if len(orders.Data) != 1 || orders.Data[0] == nil {
		response.HandleErrResponseWithCode(c, http.StatusNotFound, fmt.Errorf("order does not exist"))
		return
	}

	for _, order := range orders.Data {
		if order.UserID != auth.UserID {
			response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("you don't have permission"))
			return
		}
		order.Tasks[0] = ets.FlattenInputPods(*order.Tasks[0])
		// get profile list from input pods
		var inputPods []*models.InputPod
		for _, item := range order.Tasks[0].InputPods {
			if item.Profile != "" {
				inputPods = append(inputPods, item)
			}
		}
		err = importApplication(dao, inputPods, auth.UserID, travelerID)
		if err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
