package v1

import (
	"net/http"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/gin-gonic/gin"
)

func GetPromotions(c *gin.Context) {
	orm := middlewares.GetORMDao(c)
	promotions := []models.Promotion{}
	orm.Model(&models.Promotion{}).
		Where("start_date <= ? AND end_date >= ?", time.Now(), time.Now()).
		Where("active is TRUE").
		Order("updated_at DESC").Find(&promotions)

	ps := middlewares.GetS3Presigner(c)
	for i := range promotions {
		bucket, key, err := utils.UrlToS3BucketAndKey(promotions[i].BannerImage)
		if err == nil {
			promotions[i].BannerImage, _ = ps.PresignUrl(bucket, key, 24*time.Hour)
		}
	}

	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    promotions,
	})
}
