package v1

import (
	"fmt"
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/gin-gonic/gin"
)

type PaymentTotalActual struct {
	TotalActual     float64 `json:"total_actual"`
	TotalActualNote string  `json:"total_actual_note"`
}

func UpdatePaymentTotalActual(c *gin.Context) {
	var req PaymentTotalActual
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	id, err := utils.GetIntPathParam(c, "order-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, fmt.Errorf("invalid order ID"))
		return
	}

	if _, err = dao.DB().Db.Db.Exec(fmt.Sprintf(`UPDATE service_orders 
	SET summary = jsonb_set(summary, '{total_actual}', to_jsonb(%0.2f)) WHERE id = $1`, req.TotalActual),
		id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if _, err = dao.DB().Db.Db.Exec(fmt.Sprintf(`UPDATE service_orders 
	SET summary = jsonb_set(summary, '{total_actual_note}', '%q') WHERE id = $1`, req.TotalActualNote),
		id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    req,
	})
}
