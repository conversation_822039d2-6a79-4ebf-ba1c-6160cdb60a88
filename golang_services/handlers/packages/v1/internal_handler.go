package v1

import (
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/gin-gonic/gin"
)

type updateOrderIDPhotoReq struct {
	AppID          int64          `json:"app_id"`
	ServiceType    string         `json:"service_type"`
	Image          string         `json:"image"`
	PrintPhotoInfo map[string]any `json:"print_photo_info"`
}

func UpdatedOrderIDPhoto(c *gin.Context) {
	var req updateOrderIDPhotoReq
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"error":   err.<PERSON>rror(),
			"success": false,
		})
		return
	}

	etsDao := getEtsDao(c)

	orm := middlewares.GetORMDao(c)

	app := models.ServiceTask{}
	if err := orm.Table("service_tasks").Where("id = ?", req.AppID).First(&app).Error; err != nil {
		c.JSO<PERSON>(http.StatusOK, gin.H{
			"error":   err.<PERSON>rror(),
			"success": false,
		})
		return
	}

	for _, v := range app.InputPods {
		if v.Name == "copy_of_photo" {
			app.InputPods.SetFEValue(v.ID, req.Image)
		}
	}

	for _, v := range app.OutputPods {
		if v.Name == "copy_of_photo_full" {
			app.OutputPods.SetFEValue(v.ID, req.Image)
		}
	}
	if err := etsDao.UpdateServiceTask(map[string]any{"input_pods": app.InputPods, "output_pods": app.OutputPods}, req.AppID); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func IDPhotoWalgreenSubmitCallback(c *gin.Context) {
	var req updateOrderIDPhotoReq
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	orm := middlewares.GetORMDao(c)
	app := models.ServiceTask{}
	if err := orm.Table("service_tasks").Where("id = ?", req.AppID).First(&app).Error; err != nil {
		c.JSON(http.StatusOK, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}
	for _, v := range app.OutputPods {
		if v.Name == "print_photo_info" {
			app.OutputPods.SetFEValue(v.ID, req.PrintPhotoInfo)
		}
	}

	if err := orm.Model(&app).Update("output_pods", app.OutputPods).Error; err != nil {
		c.JSON(http.StatusOK, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
