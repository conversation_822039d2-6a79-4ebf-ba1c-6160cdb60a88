package v1

import (
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/gin-gonic/gin"
	"github.com/lib/pq"
	uuid "github.com/satori/go.uuid"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/sdk/zalo"
)

func AddItemToCart(c *gin.Context) {
	authInfo := middlewares.GetAuthInfo(c)
	if !authInfo.IsGuest() && !authInfo.IsUser() && !authInfo.IsAdmin() {
		response.HandleResponse(c, nil, fmt.Errorf("Only user / corporation admin can add item to cart"))
		return
	}

	data, err := addItemToCart(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}

func addItemToCart(c *gin.Context) (any, error) {
	var (
		cart models.Cart
		item models.CartItem
	)
	if err := c.BindJSON(&item); err != nil {
		return nil, err
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return nil, err
	}

	// Get cart
	cartData, err := getCartItems(c)

	cart = cartData.Cart

	if err = dao.CheckItemBeforeAddToCart(item); err != nil {
		return nil, err
	}

	// add item to cart
	item.CartID = cart.ID
	if err = dao.AddItemToCart(&item); err != nil {
		return nil, err
	}

	return cart, nil
}

func RemoveItemFromCart(c *gin.Context) {
	itemID, err := utils.GetIntPathParam(c, "item-id")
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err = dao.RemoveItemFromCart(itemID); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func RemoveCartItems(c *gin.Context) {
	if err := removeCartItems(c); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func removeCartItems(c *gin.Context) error {
	// Get cart
	cartData, err := getCartItems(c)
	if err != nil {
		return err
	}

	if cartData.Payment != nil {
		return fmt.Errorf("Cart already have payment info, that can not remove")
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}

	_, err = dao.DB().Db.PSQL.Delete("cart_item").
		Where("cart_id = ?", cartData.Cart.ID).
		RunWith(dao.DB().Db.Db).Exec()

	return err

}

func GetCartList(c *gin.Context) {
	var filter models.CartFilter
	if err := c.BindQuery(&filter); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	authInfo := middlewares.GetAuthInfo(c)
	if !funk.ContainsString(authInfo.UserTypes, models.UserTypeAdmin) {
		filter.UserID = authInfo.UserID
	}

	filter.Pagination = true
	filter.IsCurrent = aws.Bool(false)
	if filter.Limit <= 0 {
		filter.Limit = 10 // Default 10 Items
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	data, err := dao.GetCartList(filter)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, data)
}

func GetGroupItems(c *gin.Context) {
	var filter models.CartGroupItemRequest
	if err := c.BindQuery(&filter); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	authInfo := middlewares.GetAuthInfo(c)
	orm := middlewares.GetORMDao(c)
	if authInfo.IsADAdmin() {
	} else {
		if funk.ContainsString(authInfo.UserTypes, models.UserTypeConsulate) || funk.ContainsString(authInfo.UserTypes, models.UserTypeETSProvider) {
			if funk.ContainsString(authInfo.Roles, "admin") {
				if err := orm.Raw("SELECT profile FROM v_organizations WHERE id = ?", authInfo.OrgID.Int64).Scan(&filter.ProviderIDs).Error; err != nil {
					response.HandleResponse(c, nil, err)
					return
				}
				if len(filter.Types) == 0 {
					// filter.Types = []string{"submit-in-consulate", "need-more-document", "complete", "cancel"}
					filter.Types = []string{"submit-in-consulate", "consulate-in-review", "complete", "cancel"}
				}
			} else {
				filter.UserID = authInfo.UserID
			}
		} else {
			filter.UserID = authInfo.UserID
		}
	}

	if filter.Limit <= 0 {
		filter.Limit = 10 // Default 10 Items
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	data, err := dao.GetGroupItems(filter)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, data)
}

func GetGroupItemsHeader(c *gin.Context) {
	var filter models.CartGroupItemRequest
	if err := c.BindQuery(&filter); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	authInfo := middlewares.GetAuthInfo(c)
	orm := middlewares.GetORMDao(c)
	if !funk.ContainsString(authInfo.UserTypes, models.UserTypeAdmin) {
		if funk.ContainsString(authInfo.UserTypes, models.UserTypeConsulate) || funk.ContainsString(authInfo.UserTypes, models.UserTypeETSProvider) {
			if funk.ContainsString(authInfo.Roles, "admin") {
				if err := orm.Raw("SELECT profile FROM v_organizations WHERE id = ?", authInfo.OrgID.Int64).Scan(&filter.ProviderIDs).Error; err != nil {
					response.HandleResponse(c, nil, err)
					return
				}
				// if len(filter.Headers) == 0 {
				// 	filter.Headers = []string{"submit-in-consulate", "need-more-document", "complete"}
				// }
			} else {
				filter.UserID = authInfo.UserID
			}
		} else {
			filter.UserID = authInfo.UserID
		}
	}

	if filter.Limit <= 0 {
		filter.Limit = 10 // Default 10 Items
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	data, err := dao.GetGroupItemsHeader(filter)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":    data,
		"success": true,
	})
}

func GetCurrentCart(c *gin.Context) {
	data, err := getCartItems(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err == sql.ErrNoRows {
		response.HandleResponse(c, nil, fmt.Errorf("No items in cart! "))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data.Cart,
	})
}

func GetCartItems(c *gin.Context) {
	data, err := getCartItems(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err == sql.ErrNoRows {
		response.HandleResponse(c, nil, fmt.Errorf("No items in cart! "))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}

func getCartItems(c *gin.Context) (*models.CartData, error) {
	authInfo := middlewares.GetAuthInfo(c)

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return nil, err
	}

	var cart *models.CartData
	if val := c.Param("cart-id"); val != "" {
		cartID, _ := strconv.Atoi(val)
		if cart, err = dao.GetCartByID(cartID); err != nil {
			return nil, err
		}
	} else {
		// Current cart
		if cart, err = dao.GetUserCart(authInfo.UserID); err != nil {
			return nil, err
		}
	}

	return cart, nil
}

func CancelCart(c *gin.Context) {
	if err := cancelCart(c); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func cancelCart(c *gin.Context) error {
	// Get cart
	cartData, err := getCartItems(c)
	if err != nil {
		return err
	}

	if _, err := cartData.AcceptCancel(); err != nil {
		return err
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}

	// Begin Transaction
	if err = dao.Begin(); err != nil {
		return err
	}

	if err = releaseCartItems(dao, cartData); err != nil {
		_ = dao.Rollback()
		return err
	}

	return dao.Commit()
}

func releaseCartItems(dao db.IDao, cartData *models.CartData) error {
	count := 0
	serviceIDs := funk.Map(cartData.ServiceData, func(ci models.CartItem) int {
		return ci.ServiceDetail.ID
	}).([]int)

	// Check service status
	if err := dao.DB().Db.PSQL.Select("COUNT(*)").From("service_orders").
		Where(squirrel.Eq{"id": serviceIDs}).
		Where(squirrel.NotEq{"status": []string{models.EtsOrderStatusOpen, models.EtsOrderStatusPendingPayment, models.EtsOrderStatusWaitingPayment}}).
		RunWith(dao.DB().Db.Db).QueryRow().Scan(&count); err != nil {
		return err
	}

	if count > 0 {
		return fmt.Errorf("Can not cancel/repayment the service already process payment")
	}

	// Change service status
	if _, err := dao.DB().Db.PSQL.Update("service_orders").
		Set("status", models.EtsOrderStatusOpen).
		Set("updated_at", time.Now()).
		Where(squirrel.Eq{"id": serviceIDs}).
		RunWith(dao.DB().Db.Db).Exec(); err != nil {
		return err
	}

	// Change payment status
	if _, err := dao.DB().Db.PSQL.Update("payment").
		Set("status", models.PaymentStatusClosed).
		Set("updated_at", time.Now()).
		Where("id = ?", cartData.Payment.ID).
		RunWith(dao.DB().Db.Db).Exec(); err != nil {
		return err
	}

	// Change cart status
	if _, err := dao.DB().Db.PSQL.Update("cart").
		Set("status", models.CartStatus.Completed).
		Set("is_current", false).
		Set("updated_at", time.Now()).
		Where("id = ?", cartData.Cart.ID).
		RunWith(dao.DB().Db.Db).Exec(); err != nil {
		return err
	}

	return nil
}

func GetCartShipments(c *gin.Context) {
	orgID, err := utils.GetIntPathParam(c, "org-id")
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	// Get cart
	cartData, err := getCartItems(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	result, err := getCartShipments(dao, orgID, cartData)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":    result,
		"success": true,
	})
}

func getCartShipments(dao db.IDao, orgID int, cartData *models.CartData) ([]models.CartVisaShipment, error) {
	result := []models.CartVisaShipment{}
	for _, ci := range cartData.ServiceData {
		idServices := []any{}
		labels := []string{}
		utils.StructToJSON(ci.ServiceDetail.SmallestPrice.Shipments).Get("shipments").
			ForEach(func(key, value gjson.Result) bool {
				idServices = append(idServices, value.Int())
				return true
			})

		utils.StructToJSON(ci.ServiceDetail.SmallestPrice.Shipments).Get("labeling").
			ForEach(func(key, value gjson.Result) bool {
				labels = append(labels, value.String())
				return true
			})

		shippingSvc, err := dao.GetShippingServices(map[string][]any{
			"id":       idServices,
			"currency": {ci.ServiceDetail.SmallestPrice.Currency},
			"status":   {"active"},
		})

		shippingServices := funk.Map(shippingSvc, func(ss *models.ShippingService) *models.ShippingServiceWithLabels {
			return &models.ShippingServiceWithLabels{
				ShippingService: ss,
				Properties: &models.PropertyMap{
					"labeling": labels,
				},
			}
		}).([]*models.ShippingServiceWithLabels)

		if err != nil {
			return nil, err
		}

		result = append(result, models.CartVisaShipment{
			ID:               ci.ID,
			ShippingServices: shippingServices,
			NeedShipping:     len(shippingServices) > 0,
			PayOnDelivery:    false,
		})
	}
	return result, nil

}

func UpdateCartShipments(c *gin.Context) {
	if err := updateCartShipments(c); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func updateCartShipments(c *gin.Context) error {
	var cartShipments []models.CartShipment
	reqBody, err := c.GetRawData()
	if err != nil {
		return err
	}

	if err := json.Unmarshal(reqBody, &cartShipments); err != nil {
		return err
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}

	// Begin Transaction
	if err = dao.Begin(); err != nil {
		return err
	}

	// Check Transaction
	var transErr bool
	defer func() {
		if transErr {
			_ = dao.Rollback()
		} else {
			_ = dao.Commit()
		}
	}()

	for _, cs := range cartShipments {
		if cs.CartItem.ProductType == models.ProductType.AirPortService {
			etsID, err := strconv.Atoi(cs.CartItem.ProductID)
			if err != nil {
				transErr = true
				return fmt.Errorf("invalid ets id")
			}

			// Update shipment
			if err = checkPriceAndUpsertShipment(dao, cs.Shipment.ID, &cs.Shipment); err != nil {
				transErr = true
				return err
			}

			// Update summary
			if err = updateEtsSummaryWithShipment(
				dao, etsID, cs.Shipment.ID,
			); err != nil {
				transErr = true
				return err
			}
		}
	}

	return nil
}

func checkPriceAndUpsertShipment(dao db.IDao, shipmentID string, spmt *models.VisaShipment) error {
	if shipmentID != "" {
		if err := dao.UpdateVisaShipment(shipmentID, spmt); err != nil {
			return err
		}
	} else {
		shipmentID = hex.EncodeToString(uuid.NewV4().Bytes())
		shipmentObj := spmt
		shipmentObj.ID = shipmentID

		if _, err := dao.InsertVisaShipment(shipmentObj); err != nil {
			return err
		}
	}

	return nil
}

func updateEtsSummaryWithShipment(dao db.IDao, orderID int, shipmentID string) error {
	etsDao := &db.EtsDao{
		Db: dao.DB().Db,
	}

	_, err := etsDao.Db.PSQL.Update("service_orders").
		Set("updated_at", time.Now()).
		Set("shipment_info", shipmentID).
		Where(squirrel.Eq{"id": orderID}).RunWith(etsDao.Db.Db).Exec()

	return err
}

func CartValidation(c *gin.Context) {
	result, err := cartValidation(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":    result,
		"success": true,
	})
}

func cartValidation(c *gin.Context) (any, error) {
	// Get cart select
	var cartSelection models.CartSelection
	if err := c.BindJSON(&cartSelection); err != nil {
		return nil, err
	}

	// Get cart
	cartData, err := getCartItems(c)
	if err != nil {
		return nil, err
	}

	now := time.Now()
	result := []models.CartItemValidationResult{}

	for _, cartItem := range cartData.ServiceData {
		if !cartSelection.Includes(cartItem) {
			continue // Skip none selected item
		}

		ok, err := cartItem.ServiceDetail.IsEnoughTimeToProcess(now)
		if err != nil {
			return nil, err
		}

		message := "success"
		if !ok {
			message = fmt.Sprintf("Order: %d don't have enough time to process", cartItem.ServiceDetail.ID)
		}

		result = append(result, models.CartItemValidationResult{
			ID:      cartItem.ID,
			IsValid: ok,
			Message: message,
		})
	}

	return result, nil
}

func LockCardForModify(c *gin.Context) {
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	err = dao.UpdateCartItem(c.Param("item-id"), map[string]any{
		"status": models.CartItemStatus.ModifyLock,
	})

	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func UnLockCardFromModify(c *gin.Context) {
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	err = dao.UpdateCartItem(c.Param("item-id"), map[string]any{
		"status": models.CartItemStatus.Created,
	})

	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

type bulkDeleteGroupItem struct {
	ID      int    `json:"id"`
	Service string `json:"service"`
}

func BulkDeleteGroupItems(c *gin.Context) {
	var filter []bulkDeleteGroupItem
	if err := c.BindJSON(&filter); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	authInfo := middlewares.GetAuthInfo(c)
	orm := middlewares.GetORMDao(c)
	if authInfo.IsADAdmin() {
		for _, item := range filter {
			if item.Service == "visa" {
				if err := orm.Exec("UPDATE package SET deleted_at = NOW() WHERE id = ? AND status IN ('open', 'ready')", item.ID).Error; err != nil {
					response.HandleResponse(c, nil, err)
					return
				}
			} else {
				if err := orm.Exec("UPDATE service_orders SET deleted_at = NOW() WHERE id = ? AND status IN ('open', 'pending-payment', 'manual_payment')", item.ID).Error; err != nil {
					response.HandleResponse(c, nil, err)
					return
				}
			}
		}
	} else {
		for _, item := range filter {
			if item.Service == "visa" {

				if err := orm.Exec("UPDATE package SET deleted_at = NOW() WHERE id = ? AND status IN ('open', 'ready') AND user_id = ?", item.ID, authInfo.UserID).Error; err != nil {
					response.HandleResponse(c, nil, err)
					return
				}
			} else {
				if err := orm.Exec("UPDATE service_orders SET deleted_at = NOW() WHERE id = ? AND status IN ('open', 'pending-payment', 'manual_payment') AND user_id = ?", item.ID, authInfo.UserID).Error; err != nil {
					response.HandleResponse(c, nil, err)
					return
				}
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func ZelleWebhook(c *gin.Context) {
	if err := zelleWebHook(c); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

type EmailHook struct {
	Name        string   `json:"name"`
	Amount      float64  `json:"amount"`
	Transaction string   `json:"transaction"`
	Memo        string   `json:"memo"`
	OrderIDs    []string `json:"order_ids"`
}

func zelleWebHook(c *gin.Context) error {
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		return err
	}

	var req EmailHook
	if err := c.BindJSON(&req); err != nil {
		return err
	}

	if req.Transaction == "" {
		return errors.New("Transaction ID is empty")
	}

	etsDao := getEtsDao(c)
	var order *models.ServiceOrder
	for _, orderID := range req.OrderIDs {
		order, err = etsDao.GetServiceOrderByID(cast.ToInt(orderID))
		if order != nil && err == nil {
			break
		}
	}
	if order == nil {
		message := []string{}
		message = append(message, fmt.Sprintf(`<b>[ZELLE] Transaction Number:</b> <b><span style="color:#ff0000">%s</span></b>`, req.Transaction))
		message = append(message, fmt.Sprintf(`<b>Received $%.2f from %s</b>`, req.Amount, req.Name))
		message = append(message, fmt.Sprintf(`<b>Memo:</b> %s`, req.Memo))
		message = append(message, `<b>Not Found Orders for this Payment</b>`)
		zalo.SendZaloSMS(zalo.GetGroupID("INTERNAL_NOTIFICATION"), strings.Join(message, "\n"))
		return fmt.Errorf("Invalid amount")

	}
	cartData, err := dao.GetCartByPayment(order.PaymentID)
	if err != nil {
		return err
	}

	if req.Amount != cartData.Payment.Total {
		message := []string{}
		message = append(message, fmt.Sprintf(`<b>[ZELLE] Transaction Number:</b> <b><span style="color:#ff0000">%s</span></b>`, req.Transaction))
		message = append(message, fmt.Sprintf(`<b>Received $%.2f from %s</b>`, req.Amount, req.Name))
		message = append(message, fmt.Sprintf(`<b>Memo:</b> %s`, req.Memo))
		message = append(message, fmt.Sprintf(`<b>User paid: %0.2f/%0.2f, so order not auto change status, check it manually</b>`, req.Amount, cartData.Payment.Total))
		zalo.SendZaloSMS(zalo.GetGroupID("INTERNAL_NOTIFICATION"), strings.Join(message, "\n"))
		return fmt.Errorf("Invalid amount")
	}

	// Begin Transaction
	if err = dao.Begin(); err != nil {
		return err
	}

	var serviceIDs = []int{}
	isPaidOrder := false
	for _, ci := range cartData.ServiceData {
		if ci.ServiceDetail.IsPaid() {
			isPaidOrder = true
			continue
		}
		serviceIDs = append(serviceIDs, ci.ServiceDetail.ID)
	}

	if !isPaidOrder {
		if _, err = dao.DB().Db.Db.Exec("UPDATE service_orders SET status = $1, order_time = $2, updated_at = $2 WHERE id = ANY($3)",
			models.EtsOrderStatusPaid, time.Now(), pq.Array(serviceIDs)); err != nil {
			return err
		}

		// Update main payment
		if _, err = dao.DB().Db.Db.Exec("UPDATE payment SET status = $1, updated_at = $2 WHERE id = $3", models.PaymentStatusSuccess, time.Now(), cartData.Payment.ID); err != nil {
			return err
		}

		// Update cart
		if err = dao.UpdateCart(cartData.Cart.ID, map[string]any{
			"status":     models.CartStatus.Completed,
			"is_current": false,
		}); err != nil {
			return err
		}

		if _, err = dao.DB().Db.PSQL.Update("payment").
			Set("properties", squirrel.Expr(fmt.Sprintf("jsonb_set(jsonb_set(jsonb_set(properties, '{remain}', '0'), '{paid}', '%f'), '{total}', '%f')", cartData.Payment.Total, cartData.Payment.Total))).
			Where("id = ?", cartData.Payment.ID).
			RunWith(dao.DB().Db.Db).Exec(); err != nil {
			return err
		}

	}

	message := []string{}
	message = append(message, fmt.Sprintf(`<b>[ZELLE] Transaction Number:</b> <b><span style="color:#ff0000">%s</span></b>`, req.Transaction))
	message = append(message, fmt.Sprintf(`<b>Received $%.2f from %s</b>`, req.Amount, req.Name))
	message = append(message, fmt.Sprintf(`<b>Memo:</b> %s`, req.Memo))
	message = append(message, fmt.Sprintf(`<b>Orders:</b> %s`, strings.Join(funk.Map(serviceIDs, func(id int) string {
		return strconv.Itoa(id)
	}).([]string), ", ")))
	message = append(message, fmt.Sprintf(`<b>All orders above had been changed to Paid</b>`))

	zalo.SendZaloSMS(zalo.GetGroupID("INTERNAL_NOTIFICATION"), strings.Join(message, "\n"))

	return dao.Commit()
}
