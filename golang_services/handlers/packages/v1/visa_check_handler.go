package v1

import (
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"github.com/gin-gonic/gin"
)

type VisaCheckRequest struct {
	FromCountry string   `json:"from_country"`
	ToCountry   []string `json:"to_country"`
}

func VisaCheck(c *gin.Context) {
	var req VisaCheckRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	var results []*models.VisaCheck
	for _, item := range req.ToCountry {
		var result *models.VisaCheck
		if req.FromCountry != item {
			q := map[string][]any{
				"from_country_alpha3": {req.FromCountry},
				"to_country_alpha3":   {item},
			}
			data, err := dao.QueryVisaChecks(q)
			if err != nil {
				response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
				return
			}
			if data != nil {
				result = data[0]
			} else {
				result = &models.VisaCheck{
					FromCountryAlpha3: req.FromCountry,
					ToCountryAlpha3:   item,
					Requirement:       "Not found",
				}
			}
		} else {
			result = &models.VisaCheck{
				FromCountryAlpha3: req.FromCountry,
				ToCountryAlpha3:   item,
				Requirement:       "No visa required",
			}
		}
		results = append(results, result)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    results,
	})

}
