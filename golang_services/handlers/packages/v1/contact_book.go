package v1

import (
	"net/http"

	"github.com/gin-gonic/gin"

	aderr "bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func ListContacts(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	contacts, err := listContacts(dao, travelInfo.ID)
	if err != nil {
		response.HandleResponse(c, nil, err)
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    contacts,
	})
}

func listContacts(dao db.IDao, travelerID string) ([]*models.ContactBookItem, error) {
	contacts, err := dao.GetContactBookItemsByTravelerID(travelerID, map[string]any{"status": "active"})
	if err != nil {
		return nil, err
	}
	return contacts, nil
}

func AddContact(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	var newContact models.ContactBookItem
	if err := c.ShouldBind(&newContact); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	newContact.UserID = travelInfo.UserID
	newContact.TravelerID = travelInfo.ID
	added, err := addContact(dao, &newContact)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    added,
	})
}

func addContact(dao db.IDao, contact *models.ContactBookItem) (*models.ContactBookItem, error) {
	contact.Status = "active"
	id, err := dao.InsertContactBookItem(contact)
	if err != nil {
		return nil, err
	}
	return dao.GetContactBookItemByID(id)
}

func UpdateContact(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	id, err := utils.GetIntPathParam(c, "contact-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	var update models.ContactBookItem
	if err := c.ShouldBind(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	updated, err := updateContact(dao, travelInfo.ID, id, &update)
	if err != nil {
		response.HandleResponse(c, nil, err)
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    updated,
	})
}

func updateContact(dao db.IDao, travelerID string, id int, update *models.ContactBookItem) (*models.ContactBookItem, error) {
	existing, err := dao.GetContactBookItemByID(id)
	if err != nil {
		return nil, err
	}
	if existing == nil || existing.TravelerID != travelerID || existing.Status != "active" {
		return nil, aderr.NotFoundErr
	}
	if err = dao.UpdateContactBookItem(id, update); err != nil {
		return nil, err
	}
	return dao.GetContactBookItemByID(id)
}

func DeleteContact(c *gin.Context) {

	travelInfo := middlewares.GetTravelerInfo(c)

	id, err := utils.GetIntPathParam(c, "contact-id")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err = deactivateContact(dao, travelInfo.ID, id); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

func deactivateContact(dao db.IDao, travelerID string, id int) error {
	existing, err := dao.GetContactBookItemByID(id)
	if err != nil {
		return err
	}
	if existing == nil || existing.TravelerID != travelerID || existing.Status != "active" {
		return aderr.NotFoundErr
	}

	if err = dao.DeactivateContactBookItem(id); err != nil {
		return err
	}
	return nil
}
