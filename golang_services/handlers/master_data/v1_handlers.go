package master_data

import (
	"github.com/gin-gonic/gin"

	podHandler "bitbucket.org/persistence17/aria/golang_services/handlers/master_data/v1"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
)

// LoadV1MasterDataHandlers load all master data API routes
func LoadV1MasterDataHandlers(r gin.IRouter) gin.IRouter {

	g := r.Group("/v1/master-data")
	{
		g.GET("version", podHandler.VersionHandler)
		g.GET("status", podHandler.VersionHandler)
		g.GET("system/status", podHandler.SystemStatusHandler)
		public := g.Group("/public")
		{
			public.GET("ets/pods/:id", podHandler.GetPublicETSPodByID)
			public.GET("visa/pods/:id", podHandler.GetPublicVisaPodByID)
			public.GET("countries", podHandler.GetCountryList)
			public.GET("airlines", podHandler.GetAirlineList)
			public.GET("airports", podHandler.GetAirportList)
			public.GET("us_cities", podHandler.GetUSCities)
			public.POST("visa_check", podHandler.UpdateVisaCheck)
		}

		photo := g.Group("/photo")
		{
			photo.GET("documents", podHandler.GetIDPhotoDocumentList)
		}

		user := g.Group("/", middlewares.AuthenticateMW())
		{

			user.GET("query", podHandler.GetMasterData)
		}
		admin := g.Group("/", middlewares.AuthenticateMW(), middlewares.AdminCheckerMW)
		{
			admin.GET("pods", podHandler.GetVisaPodList)
			admin.GET("pods/:id", podHandler.GetVisaPodByID)
			// admin.POST("pod/fetch-new-id", podHandler.FetchNewPodID)
			admin.GET("pod/requests", podHandler.GetVisaPodRequestList)
			admin.GET("pod/request/metas", podHandler.GetVisaPodRequestMetaData)
			admin.POST("pod/requests", podHandler.CreateVisaPodRequest)
			admin.GET("pod/requests/:id", podHandler.GetVisaPodRequestByID)
			admin.PUT("pod/requests/:id", podHandler.UpdateVisaPodRequest)
			admin.DELETE("pod/requests/:id", podHandler.DeleteVisaPodRequest)
			admin.POST("pod/requests/:id/status", podHandler.UpdateVisaPodRequestStatus)
			admin.POST("pod/requests/:id/revert", podHandler.RevertVisaPodRequest)

			admin.GET("visa/product/prices", podHandler.GetVisaProductPriceList)
			admin.GET("visa/product/prices/:id", podHandler.GetVisaProductPrice)
			admin.PUT("visa/product/prices/:id", podHandler.UpdateVisaProductPrice)

			admin.GET("ets/product/prices", podHandler.GetETSProductPriceList)
			admin.GET("ets/product/prices/:id", podHandler.GetETSProductPrice)
			admin.PUT("ets/product/prices/:id", podHandler.UpdateETSProductPrice)
		}
		internal := g.Group("/internal")
		{
			internal.POST("email_templates", podHandler.AppScriptEmailTemplate)
			internal.POST("photo_documents", podHandler.AppScriptPhotoDocument)
		}
	}
	return r
}
