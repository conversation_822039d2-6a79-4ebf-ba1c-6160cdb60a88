package v1

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
)

// AppScriptPhotoDocument update use app script with google sheet
// https://docs.google.com/spreadsheets/d/1edPfwtU5pHW9Yx5wlKtMoW0g86F7rhLYZL8OcoMUCrY/edit#gid=0
func AppScriptPhotoDocument(c *gin.Context) {
	var rows = [][]any{}
	if err := c.BindJSON(&rows); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.<PERSON><PERSON><PERSON>(),
		})
		return
	}
	for i := 1; i < len(rows); i++ {
		if len(rows[i]) < 9 {
			c.JSO<PERSON>(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "invalid row data",
			})
			return
		}

		row := map[string]any{
			"country_code_alpha2": rows[i][0],
			"country_code_alpha3": rows[i][1],
			"name":                rows[i][3],
			"status":              rows[i][4],
			"dimensions":          rows[i][5],
			"width":               rows[i][6],
			"height":              rows[i][7],
			"bg":                  rows[i][8],
		}

		if row["status"] != "T" {
			continue
		}

		_, err := dao.DB().Db.Db.Exec(`INSERT INTO id_photo_documents (country_code_alpha2, country_code_alpha3, "name", dimensions, width, height, bg, status, head, created_at)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, current_timestamp)
			ON CONFLICT(country_code_alpha3, name) DO UPDATE
			SET "dimensions" = EXCLUDED.dimensions,
				width = EXCLUDED.width,
				height = EXCLUDED.height,
				bg = EXCLUDED.bg,
				updated_at = current_timestamp`,
			row["country_code_alpha2"],
			row["country_code_alpha3"],
			row["name"],
			row["dimensions"],
			row["width"],
			row["height"],
			row["bg"],
			"active",
			"0.67",
		)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
