package v1

import (
	"encoding/json"
	"net/http"

	"github.com/Masterminds/squirrel"
	"github.com/gin-gonic/gin"
	"github.com/meilisearch/meilisearch-go"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/response"
)

type masterDataItem struct {
	ID       int             `json:"id"`
	Category string          `json:"category"`
	Name     string          `json:"name"`
	Value    json.RawMessage `json:"value"`
}

type masterDataResponse struct {
	Data    []masterDataItem `json:"data"`
	Limit   uint64           `json:"limit"`
	Offset  uint64           `json:"offset"`
	Total   uint64           `json:"total"`
	Success bool             `json:"success"`
}

// GetMasterData get master data
func GetMasterData(c *gin.Context) {
	var (
		req masterDataItem
		err error
	)

	if err = c.ShouldBindQuery(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	orm := middlewares.GetORMDao(c)

	sq := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Question).Select(`id, category, name, value`).
		From("master_data").
		Where("category = ?", req.Category).
		Where("name = ?", req.Name).
		Limit(1)

	sqlStr, args, _ := sq.ToSql()
	rows, err := orm.Raw(sqlStr, args...).Rows()
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	defer rows.Close()

	if rows.Next() {
		item := masterDataItem{}
		if err := rows.Scan(&item.ID, &item.Category, &item.Name, &item.Value); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		c.JSON(http.StatusOK, item)
	}

	c.JSON(http.StatusOK, nil)
}

type usCityReq struct {
	Query  string `form:"query"`
	Limit  int64  `form:"limit"`
	Offset int64  `form:"offset"`
}

// GetUSCities get us cities
func GetUSCities(c *gin.Context) {
	var (
		req usCityReq
		err error
	)

	if err = c.ShouldBindQuery(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}

	client := middlewares.GetMeilisearchClient(c)
	filter := &meilisearch.SearchRequest{
		Offset: req.Offset,
		Limit:  req.Limit,
	}

	result, err := client.Index("us_cities").Search(req.Query, filter)

	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":    result.Hits,
		"success": true,
	})
}
