package v1

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
)

func GetIDPhotoDocumentList(c *gin.Context) {
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.<PERSON>rror(),
		})
		return
	}
	result, err := dao.GetIDPhotoDocumentList(c.Query("country"))
	if err != nil {
		c.<PERSON>SO<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}
