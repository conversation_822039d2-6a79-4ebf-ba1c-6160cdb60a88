package v1

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

// GetVisaPodRequestList get list of visa pod requests
func GetVisaPodRequestList(c *gin.Context) {
	var req = models.VisaPodRequestFilter{}

	if err := c.BindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	req.Default()

	authInfo := middlewares.GetAuthInfo(c)
	if authInfo == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}

	req.UserID = authInfo.UserID

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	res, err := dao.GetVisaPodRequestList(req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, res)
}

// GetVisaPodRequestByID get detail of visa pod requests
func GetVisaPodRequestByID(c *gin.Context) {
	var (
		req models.VisaPodRequest
		err error
	)

	if req.ID, err = strconv.ParseInt(c.Param("id"), 10, 64); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   fmt.Errorf("Invalid request id"),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	res, err := dao.GetVisaPodRequestByID(req.ID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    res,
	})
}

// GetVisaPodRequestMetaData get meta data
func GetVisaPodRequestMetaData(c *gin.Context) {
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	res, err := dao.GetVisaPodMetaData()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    res,
	})
}

// FetchNewPodID fetch new pod ids
// func FetchNewPodID(c *gin.Context) {
// 	var req = models.PodExtSchema{}

// 	if err := c.BindJSON(&req); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	dao, err := middlewares.GetVisaDao(c)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	if err := dao.FetchNewPodID(&req); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}
// 	if err := dao.FetchNewPodOrder(&req); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"id":    req.ID,
// 		"order": req.Order,
// 	})
// }

// CreateVisaPodRequest create visa pod request
func CreateVisaPodRequest(c *gin.Context) {
	var req models.VisaPodRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	authInfo, ok := c.Value("admin_auth_info").(models.AuthInfo)
	if !ok {
		response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("This access need admin permission"))
		return
	}
	req.UserID = authInfo.UserID

	if !utils.Contain(req.Status, []string{models.RequestStatus.Draft, models.RequestStatus.Submitted}) {
		response.HandleResponse(c, nil, fmt.Errorf("Invalid request status"))
		return
	}

	if !utils.Contain(req.RequestType, []string{models.RequestType.Add, models.RequestType.Edit, models.RequestType.Delete}) {
		response.HandleResponse(c, nil, fmt.Errorf("Invalid request type"))
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	req.WithVersion()

	if err = dao.InsertVisaPodRequest(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    req,
	})
}

// UpdateVisaPodRequest update visa pod request
func UpdateVisaPodRequest(c *gin.Context) {
	var (
		req models.VisaPodRequest
		err error
	)

	if err = c.ShouldBindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if req.ID, err = strconv.ParseInt(c.Param("id"), 10, 64); err != nil {
		response.HandleResponse(c, nil, fmt.Errorf("Invalid request id"))
		return
	}

	if req.Status == models.RequestStatus.Approved {
		response.HandleResponse(c, nil, fmt.Errorf("Don't allow to update request with status %s", req.Status))
		return
	}

	req.WithVersion()

	if !req.IsDataUpdated() {
		response.HandleResponse(c, nil, fmt.Errorf("Pod data not change, please check it! "))
		return
	}

	authInfo, ok := c.Value("admin_auth_info").(models.AuthInfo)
	if !ok {
		response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("This access need admin permission"))
		return
	}
	req.UserID = authInfo.UserID

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	var update = map[string]any{}

	if req.PodID > 0 {
		update["pod_id"] = req.PodID
	}
	if req.ExpPodData != nil {
		update["exp_pod"] = req.ExpPodData
	}
	if req.Status != "" {
		update["status"] = req.Status
	}
	if req.Reason != "" {
		update["reason"] = req.Reason
	}

	if err = dao.UpdateVisaPodRequest(req.ID, update); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// DeleteVisaPodRequest delete visa pod request
func DeleteVisaPodRequest(c *gin.Context) {
	var reqID, err = strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.HandleResponse(c, nil, fmt.Errorf("Invalid request id"))
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	var req *models.VisaPodRequest

	if req, err = dao.GetVisaPodRequestByID(reqID); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	// if utils.Contain(req.Status, []string{models.RequestStatus.Draft, models.RequestStatus.Rejected}) {
	// 	response.HandleResponse(c, nil, fmt.Errorf("Only delete request status rejected/draft"))
	// 	return
	// }

	authInfo, ok := c.Value("admin_auth_info").(models.AuthInfo)
	if !ok {
		response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("This access need admin permission"))
		return
	}

	if req.UserID != authInfo.UserID {
		response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("Only the owner can delete request"))
		return
	}

	if err = dao.DeleteVisaPodRequest(reqID); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// UpdateVisaPodRequestStatus update visa pod request status
func UpdateVisaPodRequestStatus(c *gin.Context) {
	var (
		req *models.VisaPodRequest
		err error
	)

	if err = c.ShouldBindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if req.ID, err = strconv.ParseInt(c.Param("id"), 10, 64); err != nil {
		response.HandleResponse(c, nil, fmt.Errorf("Invalid request id"))
		return
	}

	if !utils.Contain(req.Status, []string{models.RequestStatus.Approved, models.RequestStatus.Rejected}) {
		response.HandleResponse(c, nil, fmt.Errorf("No support status %s", req.Status))
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	var update = map[string]any{
		"status":           req.Status,
		"reviewer_comment": req.ReviewerComment,
	}

	if req, err = dao.GetVisaPodRequestByID(req.ID); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	authInfo, ok := c.Value("admin_auth_info").(models.AuthInfo)
	if !ok {
		response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("This access need admin permission"))
		return
	}

	if req.UserID == authInfo.UserID {
		response.HandleErrResponseWithCode(c, http.StatusForbidden, fmt.Errorf("Need another admin review this request"))
		return
	}

	if utils.Contain(req.Status, []string{models.RequestStatus.Approved, models.RequestStatus.Rejected}) {
		response.HandleResponse(c, nil, fmt.Errorf("Request already have status: %s", req.Status))
		return
	}

	req.Status = update["status"].(string)
	req.ReviewerComment = update["reviewer_comment"].(string)

	if err = dao.UpdateVisaPodRequestStatus(req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// RevertVisaPodRequest revert visa pod request
func RevertVisaPodRequest(c *gin.Context) {
	var (
		req = new(models.VisaPodRequest)
		err error
	)

	if req.ID, err = strconv.ParseInt(c.Param("id"), 10, 64); err != nil {
		response.HandleResponse(c, nil, fmt.Errorf("Invalid request id"))
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	if err = dao.RevertVisaPodRequest(req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
