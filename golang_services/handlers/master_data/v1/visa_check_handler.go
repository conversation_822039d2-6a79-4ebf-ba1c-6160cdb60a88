package v1

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
)

type VisaCheck struct {
	FromCountryAlpha3 string `json:"from_country_alpha3"`
	ToCountryAlpha3   string `json:"to_country_alpha3"`
	Requirement       string `json:"requirement"`
}

func UpdateVisaCheck(c *gin.Context) {
	var req []VisaCheck
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	for _, visaCheck := range req {
		err = dao.DB().ORM().Exec(`
		INSERT INTO visa_check (from_country_alpha3, to_country_alpha3, requirement)
		VALUES (?, ?, ?)
		ON CONFLICT (from_country_alpha3, to_country_alpha3)
		DO UPDATE SET requirement = EXCLUDED.requirement
	`, visaCheck.FromCountryAlpha3, visaCheck.ToCountryAlpha3, visaCheck.Requirement).Error
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
