package v1

import (
	"net/http"

	"github.com/Masterminds/squirrel"
	"github.com/gin-gonic/gin"
	"gorm.io/datatypes"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

type visaProductRequest struct {
	Limit  uint64 `form:"limit"`
	Offset uint64 `form:"offset"`
}

type visaProductItem struct {
	*models.VisaCustomerPrice
	VisaProductRaw *datatypes.JSON `json:"visa_product"`
	ConsulateRaw   *datatypes.JSON `json:"visa_provider"`
}

type visaProductResponse struct {
	Data    []visaProductItem `json:"data"`
	Limit   uint64            `json:"limit"`
	Offset  uint64            `json:"offset"`
	Total   uint64            `json:"total"`
	Success bool              `json:"success"`
}

// GetVisaProductPriceList get visa product
func GetVisaProductPriceList(c *gin.Context) {
	var (
		req visaProductRequest
		err error
	)

	if err = c.ShouldBindQuery(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	orm := middlewares.GetORMDao(c)

	// get all visa product price with visa product and consulate info
	// to_jsonb(vp)-'schema'-'schema_pods' get all excepts schema and schema_pods
	sq := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Question).Select(`vcp.id, vcp.status, vcp.visa_product_id, vcp.organization_id, vcp.consulate_id, vcp.currency, vcp.price, vcp.additional_fee,
	shipments, vcp.discount, vcp.created_at, vcp.updated_at, to_jsonb(vp)-'schema'-'schema_pods' as visa_product, to_jsonb(c) as consulate`).
		From("visa_customer_price vcp").
		LeftJoin("visa_product vp ON vp.id = vcp.visa_product_id").
		LeftJoin("consulate c ON c.id = vcp.consulate_id").
		Where("vp.status = 'active'").
		OrderBy("vcp.id ASC")

	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 10
	}
	sq = sq.Limit(req.Limit).Offset(req.Offset)

	sqlStr, args, _ := sq.ToSql()
	rows, err := orm.Raw(sqlStr, args...).Rows()
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	defer rows.Close()

	result := visaProductResponse{
		Data:    []visaProductItem{},
		Limit:   req.Limit,
		Offset:  req.Offset,
		Success: true,
	}
	for rows.Next() {
		item := visaProductItem{
			VisaCustomerPrice: &models.VisaCustomerPrice{},
		}
		if err := rows.Scan(&item.ID, &item.Status, &item.VisaProductID, &item.OrganizationID, &item.ConsulateID, &item.Currency, &item.Price, &item.AdditionalFee,
			&item.Shipments, &item.Discount, &item.CreatedAt, &item.UpdatedAt, &item.VisaProductRaw, &item.ConsulateRaw); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		result.Data = append(result.Data, item)
	}

	if err := orm.Raw(utils.QueryCountItems(sqlStr), args...).Scan(&result.Total).Error; err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetVisaProductPrice get visa product price
func GetVisaProductPrice(c *gin.Context) {
	orm := middlewares.GetORMDao(c)

	// get all visa product price with visa product and consulate info
	// to_jsonb(vp)-'schema'-'schema_pods' get all excepts schema and schema_pods
	sq := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Question).Select(`vcp.id, vcp.status, vcp.visa_product_id, vcp.organization_id, vcp.consulate_id, vcp.currency, vcp.price, vcp.additional_fee,
	shipments, vcp.discount, vcp.created_at, vcp.updated_at, to_jsonb(vp)-'schema'-'schema_pods' as visa_product, to_jsonb(c) as consulate`).
		From("visa_customer_price vcp").
		LeftJoin("visa_product vp ON vp.id = vcp.visa_product_id").
		LeftJoin("consulate c ON c.id = vcp.consulate_id").
		Where("vp.status = 'active'").
		Where("vcp.id = ?", c.Param("id"))

	sqlStr, args, _ := sq.ToSql()
	rows, err := orm.Raw(sqlStr, args...).Rows()
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	defer rows.Close()
	if rows.Next() {
		item := visaProductItem{
			VisaCustomerPrice: &models.VisaCustomerPrice{},
		}
		if err := rows.Scan(&item.ID, &item.Status, &item.VisaProductID, &item.OrganizationID, &item.ConsulateID, &item.Currency, &item.Price, &item.AdditionalFee,
			&item.Shipments, &item.Discount, &item.CreatedAt, &item.UpdatedAt, &item.VisaProductRaw, &item.ConsulateRaw); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    item,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    nil,
	})
}

type updateVisaProductPriceRequest struct {
	Price         *float64        `json:"price" db:"price"`
	Currency      string          `json:"currency" db:"currency"`
	Discount      *float64        `json:"discount" db:"discount"`
	AdditionalFee *datatypes.JSON `json:"additional_fee" db:"additional_fee"`
	Shipments     *datatypes.JSON `json:"shipments" db:"shipments"`
}

// UpdateVisaProductPrice update visa product
func UpdateVisaProductPrice(c *gin.Context) {
	var (
		req updateVisaProductPriceRequest
		err error
	)

	if err = c.ShouldBindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	orm := middlewares.GetORMDao(c)

	sq := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Question).
		Update("visa_customer_price").Where("id = ?", c.Param("id"))

	if req.Price != nil {
		sq = sq.Set("price", req.Price)
	}
	if req.Currency != "" {
		sq = sq.Set("currency", req.Currency)
	}

	if req.Discount != nil {
		sq = sq.Set("discount", req.Discount)
	}
	if req.AdditionalFee != nil {
		sq = sq.Set("additional_fee", req.AdditionalFee)
	}
	if req.Shipments != nil {
		sq = sq.Set("shipments", req.Shipments)
	}

	sqlStr, args, _ := sq.ToSql()
	if err := orm.Exec(sqlStr, args...).Error; err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
