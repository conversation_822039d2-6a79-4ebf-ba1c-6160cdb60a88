package v1

import (
	"net/http"

	"github.com/Masterminds/squirrel"
	"github.com/gin-gonic/gin"
	"gorm.io/datatypes"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

type etsProductRequest struct {
	Limit  uint64 `form:"limit"`
	Offset uint64 `form:"offset"`
}

type etsProductItem struct {
	*models.EtsPrice
	ETSProductRaw  *datatypes.JSON `json:"ets_product"`
	ETSProviderRaw *datatypes.JSON `json:"ets_provider"`
}

type etsProductResponse struct {
	Data    []etsProductItem `json:"data"`
	Limit   uint64           `json:"limit"`
	Offset  uint64           `json:"offset"`
	Total   uint64           `json:"total"`
	Success bool             `json:"success"`
}

// GetETSProductPriceList get visa product
func GetETSProductPriceList(c *gin.Context) {
	var (
		req etsProductRequest
		err error
	)

	if err = c.ShouldBindQuery(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	orm := middlewares.GetORMDao(c)

	// get all visa product price with visa product and consulate info
	// to_jsonb(vp)-'schema'-'schema_pods' get all excepts schema and schema_pods
	sq := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Question).Select(`ep.id, ep.status, ep.ets_id, ep.provider_id, ep.price, ep.currency, ep.additional_fee, ep.discount, ep.created_at, ep.updated_at, ep.shipments, ep.price_modified_rules, 
	ep.additional_services, to_jsonb(e)-'schema'-'schema_pods' as ets_product, to_jsonb(epv) as ets_provider`).
		From("ets_price ep").
		LeftJoin("ets e ON e.id = ep.ets_id").
		LeftJoin("ets_provider epv ON epv.id = ep.provider_id").
		Where("e.status = 'active'").
		OrderBy("e.id ASC")

	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 10
	}
	sq = sq.Limit(req.Limit).Offset(req.Offset)

	sqlStr, args, _ := sq.ToSql()
	rows, err := orm.Raw(sqlStr, args...).Rows()
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	defer rows.Close()

	result := etsProductResponse{
		Data:    []etsProductItem{},
		Limit:   req.Limit,
		Offset:  req.Offset,
		Success: true,
	}
	for rows.Next() {
		item := etsProductItem{
			EtsPrice: &models.EtsPrice{},
		}
		if err := rows.Scan(&item.ID, &item.Status, &item.EtsID, &item.ProviderID, &item.Price, &item.Currency, &item.AdditionalFee, &item.Discount, &item.CreatedAt, &item.UpdatedAt,
			&item.Shipments, &item.PriceModifiedRules, &item.AdditionalServices, &item.ETSProductRaw, &item.ETSProviderRaw); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		result.Data = append(result.Data, item)
	}

	if err := orm.Raw(utils.QueryCountItems(sqlStr), args...).Scan(&result.Total).Error; err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetETSProductPrice get visa product price
func GetETSProductPrice(c *gin.Context) {
	orm := middlewares.GetORMDao(c)

	sq := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Question).Select(`ep.id, ep.status, ep.ets_id, ep.provider_id, ep.price, ep.currency, ep.additional_fee, ep.discount, ep.created_at, ep.updated_at, ep.shipments, ep.price_modified_rules, 
	ep.additional_services, to_jsonb(e)-'schema'-'schema_pods' as ets_product, to_jsonb(epv) as ets_provider`).
		From("ets_price ep").
		LeftJoin("ets e ON e.id = ep.ets_id").
		LeftJoin("ets_provider epv ON epv.id = ep.provider_id").
		Where("e.status = 'active'").
		Where("ep.id = ?", c.Param("id"))

	sqlStr, args, _ := sq.ToSql()
	rows, err := orm.Raw(sqlStr, args...).Rows()
	if err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	defer rows.Close()
	if rows.Next() {
		item := etsProductItem{
			EtsPrice: &models.EtsPrice{},
		}
		if err := rows.Scan(&item.ID, &item.Status, &item.EtsID, &item.ProviderID, &item.Price, &item.Currency, &item.AdditionalFee, &item.Discount, &item.CreatedAt, &item.UpdatedAt,
			&item.Shipments, &item.PriceModifiedRules, &item.AdditionalServices, &item.ETSProductRaw, &item.ETSProviderRaw); err != nil {
			response.HandleResponse(c, nil, err)
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    item,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    nil,
	})
}

type updateETSProductPriceRequest struct {
	Price         *float64        `json:"price" db:"price"`
	Discount      *float64        `json:"discount" db:"discount"`
	Currency      string          `json:"currency" db:"currency"`
	AdditionalFee *datatypes.JSON `json:"additional_fee" db:"additional_fee"`
	Shipments     *datatypes.JSON `json:"shipments" db:"shipments"`
}

// UpdateETSProductPrice update visa product
func UpdateETSProductPrice(c *gin.Context) {
	var (
		req updateETSProductPriceRequest
		err error
	)

	if err = c.ShouldBindJSON(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	orm := middlewares.GetORMDao(c)

	sq := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Question).
		Update("ets_price").Where("id = ?", c.Param("id"))

	if req.Price != nil {
		sq = sq.Set("price", req.Price)
	}
	if req.Currency != "" {
		sq = sq.Set("currency", req.Currency)
	}
	if req.Discount != nil {
		sq = sq.Set("discount", req.Discount)
	}

	if req.AdditionalFee != nil {
		sq = sq.Set("additional_fee", req.AdditionalFee)
	}
	if req.Shipments != nil {
		sq = sq.Set("shipments", req.Shipments)
	}

	sqlStr, args, _ := sq.ToSql()
	if err := orm.Exec(sqlStr, args...).Error; err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
