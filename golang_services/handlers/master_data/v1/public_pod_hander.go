package v1

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
)

var PUBLIC_PODS = []string{
	"*********", // Passport photo description
}

func GetPublicETSPodByID(c *gin.Context) {
	podID := c.Param("id")
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// if !funk.Contains(PUBLIC_PODS, podID) {
	// 	c.J<PERSON>(http.StatusBadRequest, gin.H{
	// 		"success": false,
	// 		"error":   "Can not get private pod data",
	// 	})
	// 	return
	// }

	etsDao := db.NewEtsDao(dao.DB().Db)

	res, err := etsDao.QueryEtsPod(map[string]any{"id": podID})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if len(res) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Not found",
		})
		return
	}

	c.JSON(http.StatusOK, res[0].Body)
}

func GetPublicVisaPodByID(c *gin.Context) {
	var podID, err = strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   fmt.Errorf("Invalid request id"),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	res, err := dao.GetVisaPodByID(podID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, res.Body)
}
