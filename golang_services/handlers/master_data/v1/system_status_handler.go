package v1

import (
	"encoding/json"
	"net/http"

	"github.com/Masterminds/squirrel"
	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
)

func SystemStatusHandler(c *gin.Context) {
	var systemStatus json.RawMessage
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if err := squirrel.StatementBuilder.Select("value").From("master_data").Where("name = $1", "system_status").RunWith(dao.DB().Db.Db).Scan(&systemStatus); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    systemStatus,
	})
}
