package v1

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
)

// AppScriptEmailTemplate update email template use app script with google sheet
func AppScriptEmailTemplate(c *gin.Context) {
	var rows = [][]string{}
	if err := c.BindJSON(&rows); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.<PERSON>rror(),
		})
		return
	}
	for i := 1; i < len(rows); i++ {
		if len(rows[i]) < 9 {
			c.JSO<PERSON>(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "invalid row data",
			})
			return
		}
		row := map[string]any{
			"name":                 rows[i][0],
			"email_type":           rows[i][1],
			"save_as_notification": funk.ShortIf(strings.ToUpper(rows[i][2]) == "T", true, false),
			"from":                 rows[i][4],
			"subject_en":           rows[i][5],
			"subject_vi":           rows[i][6],
			"subject_zh":           rows[i][7],
			"body_en":              rows[i][8],
			"body_vi":              rows[i][9],
			"body_zh":              rows[i][10],
			"active":               funk.ShortIf(strings.ToUpper(rows[i][11]) == "T", true, false),
		}
		for _, lang := range []string{"en", "vi", "zh"} {
			_, err := dao.DB().Db.Db.Exec(`INSERT INTO email_template (name, email_type, save_as_notification, "from", title, htmlbody, language, textbody, parameters, active)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
			ON CONFLICT(name, language) DO UPDATE
			SET "from" = EXCLUDED.from,
				title = EXCLUDED.title,
				email_type = EXCLUDED.email_type,
				htmlbody = EXCLUDED.htmlbody,
				save_as_notification = EXCLUDED.save_as_notification,
				active = EXCLUDED.active,
				updated_at = current_timestamp`,
				row["name"],
				row["email_type"],
				row["save_as_notification"],
				row["from"],
				row["subject_"+lang],
				row["body_"+lang],
				strings.ToUpper(lang),
				"",
				"{}",
				row["active"],
			)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   err.Error(),
				})
				return
			}

		}

	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
