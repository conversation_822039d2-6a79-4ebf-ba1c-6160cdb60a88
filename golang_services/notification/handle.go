package main

import (
	"bytes"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"time"

	htmltemplate "html/template"

	"bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/file"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/go-resty/resty/v2"
	"github.com/rs/zerolog/log"
	"github.com/samber/lo"
	"github.com/thoas/go-funk"
)

const (
	ContentTypeMsg = "text/plain; charset=utf-8"
)

type BodyMessage struct {
	TemplateName string         `json:"template_name"`
	To           string         `json:"to"`
	Cc           []string       `json:"cc"`
	BCC          []string       `json:"bcc"`
	Parameters   map[string]any `json:"parameters"`
	Attachments  []Attachment   `json:"attachments"`
}

type Attachment struct {
	Bucket   string `json:"bucket"`
	Key      string `json:"key"`
	S3URL    string `json:"s3_url"`
	FileName string `json:"file_name"`
}

type NotificationProcessor struct {
	dao          *db.Dao
	ses          *awslib.SESClient
	s3Downloader *awslib.S3Downloader
	s3Config     map[string]any
	s3Presigner  *awslib.S3Svc
	webURL       string
}

func NewNotificationProcessor(conn *db.AuroraDB, ses *awslib.SESClient, s3Downloader *awslib.S3Downloader, s3Config map[string]any, s3Presigner *awslib.S3Svc, webURL string) NotificationProcessor {
	dao := db.NewDaoWithDb(conn)
	return NotificationProcessor{dao: dao, ses: ses, s3Downloader: s3Downloader, s3Config: s3Config, s3Presigner: s3Presigner, webURL: webURL}
}

func (n *NotificationProcessor) Notification(message *sqs.Message) error {
	log.Debug().Str("body", *message.Body).Msg("received message from `submit` queue")
	var msg BodyMessage
	err := json.Unmarshal([]byte(*message.Body), &msg)
	if err != nil {
		return err
	}

	// Skip guest generated email
	if regexp.MustCompile(`guest_\<EMAIL>`).MatchString(msg.To) {
		if len(msg.Cc) > 0 {
			msg.To = msg.Cc[0]
			msg.Cc = msg.Cc[1:]
		} else if len(msg.BCC) > 0 {
			msg.To = msg.BCC[0]
			msg.BCC = msg.BCC[1:]
		} else {
			return nil
		}
	}

	msg.Cc = lo.Filter(msg.Cc, func(item string, _ int) bool {
		return item != msg.To && !lo.Contains([]string{
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
		}, item)
	})

	msg.Cc = lo.Uniq(msg.Cc)
	msg.BCC = lo.Filter(msg.BCC, func(item string, _ int) bool {
		return item != msg.To && !lo.Contains(msg.Cc, item)
	})

	msg.BCC = lo.Uniq(msg.BCC)

	lang := "EN"
	user, err := n.dao.GetUserByEmail(msg.To)
	if err == nil && user.AppLanguage != "" {
		lang = user.AppLanguage
	}

	template, err := n.dao.GetEmailTemplateByName(msg.TemplateName, lang)
	if err != nil {
		return err
	}
	if template == nil {
		return fmt.Errorf("Empty template in database")
	}

	emailSubscribe, err := n.dao.GetEmailSubscribe(user.Email)
	if err != nil {
		return err
	}

	// Skip send email if user unsubscribe
	if emailSubscribe != nil {

		// Skip notification order
		if template.EmailType == "notification" && !emailSubscribe.SubscribeOrderNotification {
			return nil
		}

		// Currently we don't have announcement email in system
		// TODO:
	}

	// find signature template in database
	signature, err := n.dao.GetEmailTemplateByName("email_signature", lang)
	if err != nil {
		return err
	}
	if signature == nil {
		return fmt.Errorf("Empty email_signature in database")
	}

	// Parse template in signature
	_, signature.HTMLBody, err = aws.ParseTemplate("", signature.HTMLBody, map[string]any{
		"EMAIL_SUBSCRIBE_URL": fmt.Sprintf("%s/profile/subscribe?email=%s&code=%s", n.webURL, user.Email, url.QueryEscape(encrypt(user.Email))),
	})
	if err != nil {
		return err
	}

	msg.Parameters["EMAIL_SIGNATURE"] = signature.HTMLBody

	attachments := []string{}
	localPath, err := filepath.Abs(filepath.Dir(os.Args[0]))
	if len(msg.Attachments) > 0 {
		if err != nil {
			return err
		}
		for _, item := range msg.Attachments {
			filePath := localPath + "/" + item.FileName
			n.s3Downloader.DownloadFromS3Bucket(item.Bucket, item.Key, filePath)
			defer utils.RemoveFile(filePath)
			attachments = append(attachments, filePath)
		}
	}

	err = n.ses.PrepareAndSendRawEmail(template.From, []string{msg.To}, msg.Cc, msg.BCC, template.Title, template.HTMLBody, attachments, msg.Parameters)
	if err != nil {
		return err
	}
	log.Info().Str("notification_type", msg.TemplateName).Str("sender", msg.To).Str("receiver", msg.To).Strs("cc", msg.Cc).Strs("bcc", msg.BCC).Msg("email was successfully sent")

	// the message to users also backup-ed at S3
	backupFile := template.Name + time.Now().String() + ".txt"
	backupPath := localPath + "/" + backupFile
	err = ioutil.WriteFile(backupPath, []byte(*message.Body), 0755)
	if err != nil {
		return err
	}
	// remove file
	defer utils.RemoveFile(backupPath)
	// add to s3
	bucket := n.s3Config["ariadirect_prod_notification-events"].(string)
	key := file.BuildBackupS3Key(backupFile)
	err = n.s3Presigner.UploadFile(bucket, key, backupPath, ContentTypeMsg)
	if err != nil {
		return err
	}

	// save notification
	subject, body, err := aws.ParseTemplate(template.Title, template.HTMLBody, msg.Parameters)
	if err != nil {
		return err
	}

	if len(msg.Attachments) > 0 {
		for i := range msg.Attachments {
			msg.Attachments[i].S3URL, _ = n.s3Presigner.PresignUrl(msg.Attachments[i].Bucket, msg.Attachments[i].Key, 24*time.Hour)
		}
		const attachmentTemp = `
			</br>
			<b>Attachments:</b>
			<ul style="list-style-type: circle">
			{{range .}}
			<li>
				<a href="{{.S3URL}}">{{.FileName}}</a>
			</li>
			{{end}}
			</ul>`

		buff := bytes.Buffer{}
		if err := htmltemplate.Must(htmltemplate.New("").Parse(attachmentTemp)).Execute(&buff, msg.Attachments); err != nil {
			log.Error().Err(err)
		}
		body += "\n" + buff.String()
	}

	if template.SaveAsNotification {
		if err := n.dao.CreateUserEmailNotification(subject, body, lang, user.ID); err != nil {
			log.Error().Err(err)
		}
	}

	firebaseTemplate, err := n.dao.GetEmailTemplateByName("firebase_"+msg.TemplateName, lang)
	if err != nil {
		log.Error().Err(err)
	}

	if firebaseTemplate != nil {
		endpoint := utils.StructToJSON(utils.GetMapEnv("ad_endpoint"))
		apiEndpoint := endpoint.Get("api_base_url").String()
		apiToken := utils.StructToJSON(utils.GetMapEnv("ad_api_token"))
		fmt.Println(apiEndpoint)
		fmt.Println(apiToken)
		resp, err := resty.New().R().
			EnableTrace().
			SetHeader("x-ad-token", apiToken.Get("token").String()).
			SetBody(map[string]any{
				"user_id": user.ID,
				"content": firebaseTemplate.HTMLBody,
				"title":   firebaseTemplate.Title,
				"url":     funk.ShortIf(msg.Parameters["URL"] != nil, msg.Parameters["URL"], msg.Parameters["Url"]),
			}).
			Post(apiEndpoint + "/v1/device_tokens/send_notification")
		if err != nil {
			log.Error().Err(err)
		}
		fmt.Println(string(resp.Body()))

	}

	return nil
}

func encrypt(text string) string {
	h := sha256.New()
	h.Write([]byte(text))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}
