package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/urfave/cli"

	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/queue"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func main() {
	app := cli.NewApp()
	app.Name = "aria-notification"
	app.Usage = "Notification service for Aria"
	app.Commands = []cli.Command{
		submitCmd(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("Notification failed with error %v", err)
	}
}

func submitCmd() cli.Command {
	return cli.Command{
		Name:      "notification",
		ShortName: "n",
		Usage:     "Start notification inbound message processor",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:     "ad_sqs",
				Usage:    "Env for sqs config",
				EnvVar:   "ad_sqs",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_aws",
				Usage:    "Env for aws config",
				EnvVar:   "ad_aws",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_db",
				Usage:    "Env for aws config",
				EnvVar:   "ad_db",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_s3",
				Usage:    "Env for s3 config",
				EnvVar:   "ad_s3",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_ses",
				Usage:    "Env for ses config",
				EnvVar:   "ad_ses",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_website",
				Usage:    "Env for website config",
				EnvVar:   "ad_website",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_endpoint",
				Usage:    "Env for endpoint config",
				EnvVar:   "ad_endpoint",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_api_token",
				Usage:    "Env for api token configuration",
				EnvVar:   "ad_api_token",
				Required: true,
			},
		},
		Action: runSubmit(),
	}
}

func runSubmit() cli.ActionFunc {
	return func(c *cli.Context) error {

		sqsConfigMap := utils.GetMapEnv("ad_sqs")
		if sqsConfigMap["url_prefix"].(string) == "" {
			return fmt.Errorf("missing url_prefix in ad_sqs")
		}
		if sqsConfigMap["notification_sqs_name"].(string) == "" {
			return fmt.Errorf("missing notification_sqs_name in ad_sqs")
		}

		s3ConfigMap := utils.GetMapEnv("ad_s3")
		if s3ConfigMap["ariadirect_prod_notification-events"].(string) == "" {
			return fmt.Errorf("missing ariadirect_prod_notification-events in ad_s3")
		}

		sqsQueueURL := sqsConfigMap["url_prefix"].(string) + "/" + sqsConfigMap["notification_sqs_name"].(string)

		awsUSConfig := utils.GetMapEnv("ad_aws")
		if awsUSConfig["region"].(string) == "" {
			return fmt.Errorf("missing region in ad_aws")
		}

		awsConfig := aws.NewConfig().WithRegion(awsUSConfig["region"].(string)).WithLogLevel(aws.LogOff)
		sess, err := session.NewSession(awsConfig)
		if err != nil {
			return err
		}

		sesConfigMap := utils.GetMapEnv("ad_ses")
		if sesConfigMap["region"].(string) == "" {
			return fmt.Errorf("missing region in ad_ses")
		}
		awsConfigSES := aws.NewConfig().WithRegion(sesConfigMap["region"].(string)).WithLogLevel(aws.LogOff)

		sessSES, err := session.NewSession(awsConfigSES)
		if err != nil {
			return err
		}

		ses := awslib.NewSESClient(sessSES)

		s3Presigner := awslib.NewS3Svc(sess)
		s3Downloader := awslib.NewDownloader(sess)

		dbConfigMap := utils.GetMapEnv("ad_db")
		if dbConfigMap["write_host"].(string) == "" {
			log.Fatal(fmt.Errorf("missing write_host in ad_db"))
		}
		if dbConfigMap["dbname"].(string) == "" {
			log.Fatal(fmt.Errorf("missing dbname in ad_db"))
		}
		if dbConfigMap["username"].(string) == "" {
			log.Fatal(fmt.Errorf("missing username in ad_db"))
		}
		if dbConfigMap["password"].(string) == "" {
			log.Fatal(fmt.Errorf("missing password in ad_db"))
		}
		conn, err := db.NewAuroraDBFromConfigMap(dbConfigMap)
		if err != nil {
			return err
		}

		websiteConfigMap := utils.GetMapEnv("ad_website")
		webURL, ok := websiteConfigMap["host_name"]
		if !ok {
			log.Fatal(fmt.Errorf("missing host_name in ad_website"))
		}

		notify := NewNotificationProcessor(conn, ses, s3Downloader, s3ConfigMap, s3Presigner, webURL.(string))

		s := queue.NewSQSProcessor(sess, sqsQueueURL, notify.Notification)

		s.Run()
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGHUP, syscall.SIGTERM)

	loop:
		for {
			select {
			case <-sigCh:
				break loop
			}
		}

		return nil
	}
}
