package main

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/defaults"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	"bitbucket.org/persistence17/aria/golang_services/msg_processors/payments"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
	"bitbucket.org/persistence17/aria/golang_services/sdk/queue"
)

func runPaymentInboundMsgProcessor() cli.ActionFunc {
	return func(c *cli.Context) error {
		logger.SetJsonZeroLoggerWithLevel(c.String(flags.LogLevelFlag.Name))
		queueUrl := c.String("queue")
		if queueUrl == "" {
			return fmt.Errorf("empty queue URL")
		}

		cfg := defaults.Config().WithLogLevel(aws.LogOff)
		sess, err := session.NewSession(cfg)
		if err != nil {
			return err
		}

		p := queue.NewSQSProcessor(sess, queueUrl, payments.DummyInboundMsgProcessor())
		p.Run()
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGHUP, syscall.SIGTERM)

	loop:
		for {
			select {
			case <-sigCh:
				break loop
			}
		}

		return nil
	}
}
