package main

import (
	"log"
	"os"

	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
)

func main() {
	app := cli.NewApp()
	app.Name = "aria-payments"
	app.Usage = "Payments service for Aria"
	app.Commands = []cli.Command{
		serviceCmd(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}

}

func serviceCmd() cli.Command {
	return cli.Command{
		Name:      "service",
		ShortName: "svc",
		Usage:     "Start payments service for Aria",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:   "port",
				Usage:  "Port the server listens to",
				EnvVar: "PORT",
				Value:  "3000",
			},
			cli.StringFlag{
				Name:     "conf-file",
				Usage:    "Configuration file for payment service",
				EnvVar:   "CONF_FILE",
				Required: true,
				Value:    "conf/payments.yaml",
			},
			cli.BoolFlag{
				Name:   "use-alipay-prod",
				Usage:  "Use Alipay prod API for payment service, default to false",
				EnvVar: "USE_ALIPAY_PROD",
			},
			cli.BoolFlag{
				Name:   "use-wechat-pay-prod",
				Usage:  "Use WechatPay prod API for payment service, default to false",
				EnvVar: "USE_WECHAT_PAY_PROD",
			},
			cli.BoolFlag{
				Name:   "use-authorize-prod",
				Usage:  "Use Authorize prod API for payment service, default to false",
				EnvVar: "USE_AUTHORIZE_PROD",
			},
			cli.StringFlag{
				Name: "pg-conn-string",
				Usage: "Postgres connection string, including username password. If not provided, server will try to " +
					"get connection string aws instance role",
				EnvVar: "PG_CONN_STR",
			},
			cli.StringFlag{
				Name:  "db-config",
				Usage: "Env for db config",
			},
			cli.StringFlag{
				Name:     "service-config",
				Usage:    "Env name for service configuration",
				EnvVar:   "ad_payment_service",
				Required: true,
			},
			cli.StringFlag{
				Name:     "account-service-config",
				Usage:    "Env for user account configuration",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_secrets",
				Usage:    "Env for secret configuration",
				EnvVar:   "ad_secrets",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_website",
				Usage:    "Env for website config",
				EnvVar:   "ad_website",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_wechatpay",
				Usage:    "Env for wechatpay config",
				EnvVar:   "ad_wechatpay",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_authorize",
				Usage:    "Env for authorize config",
				EnvVar:   "ad_authorize",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_onepay",
				Usage:    "Env for onepay config",
				EnvVar:   "ad_onepay",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_paypal",
				Usage:    "Env for paypal config",
				EnvVar:   "ad_paypal",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_zellepay",
				Usage:    "Env for zelle config",
				EnvVar:   "ad_zellepay",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_stripe",
				Usage:    "Env for stripe config",
				EnvVar:   "ad_stripe",
				Required: true,
			},
			cli.StringFlag{
				Name:     "sqs-config",
				Usage:    "Env name for service configuration",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_email",
				Usage:    "Env for email config",
				EnvVar:   "ad_email",
				Required: true,
			},
			cli.StringFlag{
				Name:   "currency-config-file",
				Usage:  "file key for currency to available payment methods config file on S3",
				EnvVar: "currency_config_file",
				Value:  "/available_payment_methods_by_currency.json",
			},
			cli.StringFlag{
				Name:   "region-payment-config-file",
				Usage:  "key for config file of default payment methods by region on S3",
				EnvVar: "region_payment_config_file",
				Value:  "/default_payment_methods_by_region.json",
			},
			cli.StringFlag{
				Name:     "s3-region",
				Usage:    "AWS S3 region of this service",
				EnvVar:   "S3_REGION",
				Required: true,
			},
			cli.StringFlag{
				Name:     "version-file-bucket",
				Usage:    "S3 bucket of file for app versions",
				EnvVar:   "VERSION_FILE_BUCKET",
				Required: true,
			},
			flags.LogLevelFlag,
		},
		Action: startPaymentsService(),
	}
}

func msgProcessorCmd() cli.Command {
	return cli.Command{
		Name:      "message-processor",
		ShortName: "mp",
		Usage:     "Start payments inbound message processor",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:     "queue",
				Usage:    "SQS url",
				EnvVar:   "QUEUE",
				Required: true,
			},
			flags.LogLevelFlag,
		},
		Action: runPaymentInboundMsgProcessor(),
	}
}
