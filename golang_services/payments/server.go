package main

import (
	"fmt"

	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	"bitbucket.org/persistence17/aria/golang_services/handlers/payments"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
	paymentsLib "bitbucket.org/persistence17/aria/golang_services/sdk/payments"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func startPaymentsService() cli.ActionFunc {
	return func(c *cli.Context) error {
		port := c.String("port")

		e, err := newPaymentsServer(c)
		if err != nil {
			return err
		}

		return e.Run(":" + port)
	}
}

func newPaymentsServer(c *cli.Context) (*gin.Engine, error) {
	logger.SetJsonZeroLoggerWithLevel(c.String(flags.LogLevelFlag.Name))
	e := gin.New()
	e.Use(logger.LoggerMW(), logger.CustomLoggerMW(), gzip.Gzip(gzip.DefaultCompression))
	logger.InitGraylogLogger()
	e.Use(logger.GraylogLoggerMiddleware(logger.DefaultRequestLoggerConfig()))

	// load middlewares
	if _, err := loadPaymentsCommonMW(c, e); err != nil {
		return nil, err
	}

	// load payment method configuration
	// region, bucket, byCurrencyConf, byRegionConf := c.String("s3-region"),
	// 	c.String("version-file-bucket"),
	// 	c.String("currency-config-file"),
	// 	c.String("region-payment-config-file")
	// if err := payments_v1.InitPaymentMethodsConfig(region, bucket, byCurrencyConf, byRegionConf); err != nil {
	// 	return nil, err
	// }

	// load handlers
	payments.LoadV1PaymentsHandlers(e)

	return e, nil
}

func loadPaymentsCommonMW(c *cli.Context, r gin.IRouter) (gin.IRouter, error) {
	r, _, err := middlewares.LoadCommonMWAndConf(c, r)
	if err != nil {
		return nil, err
	}
	// third party payments related
	// init alipay client
	//useAlipayProd := c.Bool("use-alipay-prod")
	//ali, err := newAlipayClient(conf, useAlipayProd)
	//if err != nil {
	//	return nil, err
	//}

	if err := middlewares.InitSecretMap(c.String("ad_secrets")); err != nil {
		return nil, err
	}

	svcConfigMap := utils.GetMapEnv(c.String("service-config"))
	log.Info().Interface("service_config", svcConfigMap).Msg("Service config from ENV")

	// init wechat pay client
	wechatpayConfigMap := utils.GetMapEnv(c.String("ad_wechatpay"))
	log.Info().Interface("ad_wechatpay", wechatpayConfigMap).Msg("Wechatpay config from ENV")

	wechat, err := newWechatPayClient(wechatpayConfigMap, c.Bool("use-wechat-pay-prod"))
	if err != nil {
		return nil, err
	}
	svcConfigMap["payment_url_wechatpay"] = wechatpayConfigMap["payment_url_wechatpay"]

	// init authorize client
	authorizeConfigMap := utils.GetMapEnv(c.String("ad_authorize"))
	log.Info().Interface("ad_authorize", authorizeConfigMap).Msg("Authorize.net config from ENV")

	authorize, err := newAuthorizeClient(authorizeConfigMap)
	if err != nil {
		return nil, err
	}

	// init openpay client
	onepayConfigMap := utils.GetMapEnv(c.String("ad_onepay"))
	log.Info().Interface("ad_onepay", onepayConfigMap).Msg("Onepay config from ENV")

	onepay, err := newOnepayClient(onepayConfigMap)
	if err != nil {
		return nil, err
	}

	// init paypal client
	paypalConfigMap := utils.GetMapEnv(c.String("ad_paypal"))
	log.Info().Interface("ad_paypal", paypalConfigMap).Msg("Paypal config from ENV")

	paypal, err := newPayPalClient(paypalConfigMap)
	if err != nil {
		return nil, err
	}

	// init zelle client
	zelleConfigMap := utils.GetMapEnv(c.String("ad_zellepay"))
	log.Info().Interface("ad_zellepay", zelleConfigMap).Msg("Zelle pay config from ENV")

	zelle, err := newZellePayClient(zelleConfigMap)
	if err != nil {
		return nil, err
	}

	bankpay := &paymentsLib.BankPayClient{
		ReceiverInfo: zelleConfigMap,
	}

	stripeConfigMap := utils.GetMapEnv(c.String("ad_stripe"))
	log.Info().Interface("ad_stripe", zelleConfigMap).Msg("Stripe config from ENV")
	stripeSecret, ok := stripeConfigMap["secret"]
	if !ok {
		return nil, fmt.Errorf("missing secret in Stripe config")
	}

	stripe := paymentsLib.NewStripeClient(stripeSecret.(string))

	// set payment instants
	r.Use(middlewares.PaymentClientsMW(wechat, authorize, onepay, paypal, zelle, bankpay, stripe))

	// db
	var conn *db.AuroraDB
	dbConfigMap := utils.GetMapEnv("ad_db")
	if host := dbConfigMap["write_host"]; host != nil {
		log.Info().Interface("write_host", dbConfigMap["write_host"]).Msg("Loading DB config from ENV")
		conn, err = db.NewAuroraDBFromConfigMap(dbConfigMap)
		if err != nil {
			return nil, err
		}
	}

	userAccountServiceConfigMap := utils.GetMapEnv(c.String("account-service-config"))
	log.Info().Interface("account-service-config", userAccountServiceConfigMap).Msg("Account Service config from ENV")

	paymentConfig := map[string]any{
		"zellepay_contact_email": zelleConfigMap["zellepay_contact_email"],
		"zellepay_contact_phone": zelleConfigMap["zellepay_contact_phone"],
		"bank_1_name":            zelleConfigMap["bank_1_name"],
		"bank_1_owner":           zelleConfigMap["bank_1_owner"],
		"bank_1_number":          zelleConfigMap["bank_1_number"],
	}

	r.Use(
		logger.LoggerMW(),
		gin.Recovery(),
		middlewares.PaymentDaoMW(conn),
		middlewares.VisaDaoMW(conn),
		middlewares.ServiceConfigMapMW(svcConfigMap),
		middlewares.AccountServiceConfigMW(userAccountServiceConfigMap),
		middlewares.PaymentConfigMW(paymentConfig),
	)

	return r, nil
}

// func newAlipayClient(conf map[string]string, isProd bool) (*paymentsLib.AliPayClient, error) {
// 	alipayPubKeyLoc, ok := conf["alipay_pub_key_location"]
// 	if !ok {
// 		return nil, fmt.Errorf("missing alipay_pub_key_location in config file")
// 	}
// 	alipayPrivKeyLoc, ok := conf["alipay_priv_key_location"]
// 	if !ok {
// 		return nil, fmt.Errorf("missing alipay_priv_key_location in config file")
// 	}
// 	appID, ok := conf["alipay_app_id"]
// 	if !ok {
// 		return nil, fmt.Errorf("mssing alipay_app_id in config file")
// 	}
// 	accountID, ok := conf["alipay_account_id"]
// 	if !ok {
// 		return nil, fmt.Errorf("missing alipay_account_id in config file")
// 	}

// 	return paymentsLib.NewAliPayClient(appID, accountID, alipayPrivKeyLoc, alipayPubKeyLoc, isProd)
// }

func newWechatPayClient(conf map[string]any, isProd bool) (*paymentsLib.WechatPayClient, error) {
	appID, ok := conf["wechat_pay_app_id"].(string)
	if !ok {
		return nil, fmt.Errorf("missing wechat_pay_app_id in config file")
	}
	mchID, ok := conf["wechat_pay_account_id"].(string)
	if !ok {
		return nil, fmt.Errorf("missing wechat_pay_account_id in config file")
	}

	apiKey, ok := conf["wechat_pay_api_key"].(string)
	if !ok {
		return nil, fmt.Errorf("missing wechat-pay-api-key in config file")
	}

	return paymentsLib.NewWechatPayClient(appID, apiKey, mchID, isProd), nil
}

func newAuthorizeClient(config map[string]any) (*paymentsLib.AuthorizeClient, error) {
	appToken, ok := config["authorize_api_token"].(string)
	if !ok {
		return nil, fmt.Errorf("missing authorize_api_token in svcConfigMap")
	}
	appKey, ok := config["authorize_key"].(string)
	if !ok {
		return nil, fmt.Errorf("missing authorize_key in svcConfigMap")
	}
	appMode, ok := config["authorize_mode"].(string)
	if !ok {
		return nil, fmt.Errorf("missing authorize_mode in svcConfigMap")
	}
	appEndpoint, ok := config["authorize_endpoint"].(string)
	if !ok {
		return nil, fmt.Errorf("missing authorize_endpoint in svcConfigMap")
	}
	formPost, ok := config["authorize_form_post_url"].(string)
	if !ok {
		return nil, fmt.Errorf("missing authorize_form_post_url in svcConfigMap")
	}
	webhookID, ok := config["authorize_webhook_id"].(string)
	if !ok {
		return nil, fmt.Errorf("missing authorize_webhook_id in svcConfigMap")
	}
	webhookSign, ok := config["authorize_webhook_sign"].(string)
	if !ok {
		return nil, fmt.Errorf("missing authorize_webhook_sign in svcConfigMap")
	}
	urlCallback, ok := config["authorize_webhook_callback"].(string)
	if !ok {
		return nil, fmt.Errorf("missing authorize_webhook_callback in svcConfigMap")
	}
	urlRedirect, ok := config["authorize_redirect_url"].(string)
	if !ok {
		return nil, fmt.Errorf("missing authorize_redirect_url in svcConfigMap")
	}
	cancelRedirect, ok := config["authorize_redirect_cancel_url"].(string)
	if !ok {
		return nil, fmt.Errorf("missing authorize_redirect_cancel_url in svcConfigMap")
	}
	return paymentsLib.NewAuthorizeClient(appToken, appKey, appMode, appEndpoint, formPost, webhookID, webhookSign, urlCallback, urlRedirect, cancelRedirect), nil
}

func newOnepayClient(conf map[string]any) (*paymentsLib.OnePayClient, error) {
	enabled, ok := conf["onepay_enabled"].(bool)
	if !ok {
		return nil, fmt.Errorf("missing onepay_enabled in config file")
	}
	if !enabled {
		return nil, nil
	}

	merchantID, ok := conf["onepay_merchant_id"].(string)
	if !ok {
		return nil, fmt.Errorf("missing onepay_merchant_id in config file")
	}

	merchantOrder, ok := conf["onepay_merchant_order"].(string)
	if !ok {
		return nil, fmt.Errorf("missing onepay_merchant_order in config file")
	}

	onepayUser, ok := conf["onepay_user"].(string)
	if !ok {
		return nil, fmt.Errorf("missing onepay_user in config file")
	}

	onepayPassword, ok := conf["onepay_password"].(string)
	if !ok {
		return nil, fmt.Errorf("missing onepay_password in config file")
	}

	apiKey, ok := conf["onepay_api_key"].(string)
	if !ok {
		return nil, fmt.Errorf("missing onepay_api_key in config file")
	}

	apiSecret, ok := conf["onepay_api_secret"].(string)
	if !ok {
		return nil, fmt.Errorf("missing onepay_api_secret in config file")
	}

	endpoint, ok := conf["onepay_endpoint"].(string)
	if !ok {
		return nil, fmt.Errorf("missing onepay_endpoint in config file")
	}

	queryDC, ok := conf["onepay_query_dc"].(string)
	if !ok {
		return nil, fmt.Errorf("missing onepay_query_dc in config file")
	}

	callbackURL, ok := conf["onepay_callback"].(string)
	if !ok {
		return nil, fmt.Errorf("missing onepay_callback in config file")
	}

	var instant = &paymentsLib.OnePayClient{
		MerchantID:    merchantID,
		MerchantOrder: merchantOrder,
		User:          onepayUser,
		Password:      onepayPassword,
		APIKey:        apiKey,
		APISecret:     apiSecret,
		Endpoint:      endpoint,
		CallbackURL:   callbackURL,
		QueryDCURL:    queryDC,
		Client:        paymentsLib.NewOnePayHTTPClient(true),
	}
	return instant, nil
}

func newPayPalClient(conf map[string]any) (*paymentsLib.PayPalClient, error) {
	apiKey, ok := conf["paypal_api_key"].(string)
	if !ok {
		return nil, fmt.Errorf("missing paypal_api_key in config file")
	}

	apiSecret, ok := conf["paypal_api_secret"].(string)
	if !ok {
		return nil, fmt.Errorf("missing paypal_api_secret in config file")
	}

	endpoint, ok := conf["paypal_endpoint"].(string)
	if !ok {
		return nil, fmt.Errorf("missing paypal_endpoint in config file")
	}

	returnURL, ok := conf["paypal_return"].(string)
	if !ok {
		return nil, fmt.Errorf("missing paypal_return in config file")
	}

	cancelURL, ok := conf["paypal_cancel"].(string)
	if !ok {
		return nil, fmt.Errorf("missing paypal_cancel in config file")
	}

	callbackURL, ok := conf["paypal_callback"].(string)
	if !ok {
		return nil, fmt.Errorf("missing paypal_callback in config file")
	}

	webhookID, ok := conf["paypal_webhook_id"].(string)
	if !ok {
		return nil, fmt.Errorf("missing paypal_webhook_id in config file")
	}

	return paymentsLib.NewPayPalClient(apiKey, apiSecret, endpoint, returnURL, cancelURL, callbackURL, webhookID), nil
}

func newZellePayClient(conf map[string]any) (*paymentsLib.ZellePayClient, error) {
	return &paymentsLib.ZellePayClient{
		ReceiverInfo: conf,
	}, nil
}

func newPaymentsDb(conf map[string]any, creds *credentials.Credentials) (*db.AuroraDB, error) {
	paymentDb, ok := conf["payments-db"]
	if !ok {
		return nil, fmt.Errorf("missing payments-db in config file")
	}
	region, ok := conf["db-region"]
	if !ok {
		return nil, fmt.Errorf("missing db-region in config file")
	}
	user, ok := conf["db-user"]
	if !ok {
		return nil, fmt.Errorf("missing db-user in config file")
	}
	database, ok := conf["db-database"]
	if !ok {
		return nil, fmt.Errorf("missing db-database in config file")
	}

	return db.NewAuroraDBWithAwsCred(paymentDb.(string), region.(string), user.(string), database.(string), creds)
}
