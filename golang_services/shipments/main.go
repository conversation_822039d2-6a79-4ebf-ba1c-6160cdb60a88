package main

import (
	"log"
	"os"

	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
)

func main() {
	app := cli.NewApp()
	app.Name = "aria-shipments"
	app.Usage = "shipments service for Aria"
	app.Commands = []cli.Command{
		serviceCmd(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}

}

func serviceCmd() cli.Command {
	return cli.Command{
		Name:      "service",
		ShortName: "smt",
		Usage:     "Start shipments service for Aria",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:   "port",
				Usage:  "Port the server listens to",
				EnvVar: "PORT",
				Value:  "3000",
			},
			cli.StringFlag{
				Name:     "ad_fedex",
				Usage:    "API key for calling fedex service",
				EnvVar:   "ad_fedex",
				Required: true,
			},
			cli.StringFlag{
				Name: "pg-conn-string",
				Usage: "Postgres connection string, including username password. If not provided, server will try to " +
					"get connection string aws instance role",
				EnvVar: "PG_CONN_STR",
			},
			cli.StringFlag{
				Name:  "db-config",
				Usage: "Env for db config",
			},
			cli.StringFlag{
				Name:     "ad_user_account_service",
				Usage:    "Env for user account configuration",
				EnvVar:   "ad_user_account_service",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_secrets",
				Usage:    "Env for secret configuration",
				EnvVar:   "ad_secrets",
				Required: true,
			},
			flags.LogLevelFlag,
		},
		Action: startShipmentsService(),
	}
}
