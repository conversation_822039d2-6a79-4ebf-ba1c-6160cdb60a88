package main

import (
	"fmt"

	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/handlers/shipments"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/fedex"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func startShipmentsService() cli.ActionFunc {
	return func(c *cli.Context) error {
		port := c.String("port")

		e, err := newShipmentsServer(c)
		if err != nil {
			return err
		}

		return e.Run(":" + port)
	}
}

func newShipmentsServer(c *cli.Context) (*gin.Engine, error) {
	e := gin.New()
	e.Use(logger.LoggerMW(), logger.CustomLoggerMW(), gzip.Gzip(gzip.DefaultCompression))
	// cors
	corsConf := cors.DefaultConfig()
	corsConf.AddAllowHeaders("x-access-token", "Language")
	corsConf.AllowAllOrigins = true

	fedexConfig := utils.GetMapEnv("ad_fedex")
	fdx, err := newFedexClient(fedexConfig)
	if err != nil {
		return nil, err
	}

	if err := middlewares.InitSecretMap("ad_secrets"); err != nil {
		return nil, err
	}

	userAccountServiceConfigMap := utils.GetMapEnv("ad_user_account_service")
	log.Info().Interface("ad_user_account_service", userAccountServiceConfigMap).Msg("Account Service config from ENV")

	// db
	var conn *db.AuroraDB
	dbConfigMap := utils.GetMapEnv("ad_db")
	if host := dbConfigMap["write_host"]; host != nil {
		log.Info().Interface("write_host", dbConfigMap["write_host"]).Msg("Loading DB config from ENV")
		conn, err = db.NewAuroraDBFromConfigMap(dbConfigMap)
		if err != nil {
			return nil, err
		}
	}

	e.Use(
		cors.New(corsConf),
		middlewares.VisaDaoMW(conn),
		middlewares.AuthorizationMW(),
		middlewares.FedexMW(fdx),
		middlewares.AccountServiceConfigMW(userAccountServiceConfigMap),
	)

	shipments.InitShipmentHandlers(conn)
	shipments.LoadV1ShipmentsHandlers(e)

	return e, nil
}

func newFedexClient(conf map[string]any) (*fedex.FedexClient, error) {
	url, ok := conf["url"].(string)
	if !ok {
		return nil, fmt.Errorf("missing url in ad_fedex file")
	}
	key, ok := conf["key"].(string)
	if !ok {
		return nil, fmt.Errorf("missing key in ad_fedex file")
	}
	password, ok := conf["password"].(string)
	if !ok {
		return nil, fmt.Errorf("missing password in ad_fedex file")
	}
	account, ok := conf["account"].(string)
	if !ok {
		return nil, fmt.Errorf("missing account in ad_fedex file")
	}
	meter, ok := conf["meter"].(string)
	if !ok {
		return nil, fmt.Errorf("missing meter in ad_fedex file")
	}
	return fedex.NewFedexClient(url, key, password, account, meter), nil
}
