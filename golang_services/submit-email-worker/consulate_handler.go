package main

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/pariz/gountries"
	"github.com/spf13/cast"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/file"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func (s *SubmitEmailWorkerProcessor) SendVisaEmailToConsulate(orderData OrderData, localPath string) (string, string, error) {
	// 1. create applications excel file
	// Get localize by language default
	fileName := fmt.Sprintf("AriaDirect-%s Visa-%s-Order-Number-%d.xlsx", orderData.ETS.Country, orderData.ETS.Tasks[0], orderData.Order.ID)
	fileDir := localPath + "/" + fileName

	if err := s.CreateNewExcel(orderData, fileDir, localPath, orderData.ETS); err != nil {
		return "", "", err
	}

	defer utils.RemoveFile(fileName)

	// 2. add to s3 and save to package
	bucket := s.s3Config["ariadirect_prod_applications"].(string)
	key := file.BuildPkgInputS3Key(orderData.Order.UserID, orderData.Order.ID, fileName)
	if err := s.s3Presigner.UploadFile(bucket, key, fileDir, ContentTypeExcel); err != nil {
		return "", "", err
	}

	summaryFile := fmt.Sprintf("%s/%s", bucket, key)
	// 3. prepare data

	apps := []map[string]string{}

	for _, item := range orderData.Tasks {
		names := []string{}

		inputPodPair := item.InputPods.ToMapKeyValue()

		// Get given name, middle name, surname
		if val := cast.ToString(inputPodPair["passport_core_info_given_name"]); val != "" {
			names = append(names, val)
		}
		if val := cast.ToString(inputPodPair["passport_core_info_middle_name"]); val != "" {
			names = append(names, val)
		}
		if val := cast.ToString(inputPodPair["passport_core_info_surname"]); val != "" {
			names = append(names, val)
		}

		app := map[string]string{
			"FullName":       strings.Join(names, " "),
			"Duration":       s.localize.EN.Duration[cast.ToString(inputPodPair["service_core_info_validity"])],
			"ProcessingTime": s.localize.EN.ProcessingTime[cast.ToString(inputPodPair["service_core_info_processing_time"])],
			"Purpose":        s.localize.EN.Purpose[cast.ToString(inputPodPair["service_core_info_purpose"])],
		}
		apps = append(apps, app)
	}
	query := gountries.New()
	country, err := query.FindCountryByAlpha(orderData.ETS.Country)
	if err != nil {
		return "", "", err
	}

	parameters := map[string]any{
		"PackageID":    strconv.Itoa(orderData.Order.ID),
		"Name":         orderData.Provider.Name,
		"VisaCountry":  country.Name.Common,
		"VisaType":     s.localize.EN.VisaType[orderData.ETS.Tasks[0]],
		"Applications": apps,
		"URL":          fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=visa", s.conf["website_host_name"].(string), orderData.Order.ID),
	}
	// 4. send message to notification queue
	email := cast.ToString(s.conf["visas_email"])
	if s.conf["env"].(string) == ProductionEnv {
		s.conf["visas_email"] = orderData.Provider.Contact.Email
	}
	ccEmail := []string{}
	if !orderData.Provider.Contact.SecondaryEmail.IsZero() {
		if s.conf["env"].(string) == ProductionEnv {
			ccEmail = []string{orderData.Provider.Contact.SecondaryEmail.String}
		}
		ccEmail = append(ccEmail, cast.ToString(s.conf["visas_email"]))
	}

	templateName := "submit_visa"
	fmt.Print("templateName", templateName)
	fmt.Print("email", email)
	fmt.Print("ccEmail", ccEmail)
	fmt.Print("parameters", parameters)
	// if err = s.SendEmail(templateName, email, ccEmail, parameters, []string{}); err != nil {
	// 	return "", "", err
	// }
	return templateName, summaryFile, nil
}

func (s *SubmitEmailWorkerProcessor) CreateNewExcel(orderData OrderData, fileName string, localPath string, ets *models.ExtendedTravelService) error {
	f := excelize.NewFile()

	f.NewSheet(DefaultSheetName)
	// Setup format
	f.SetColWidth(DefaultSheetName, "A", "R", 20)
	f.SetColWidth(DefaultSheetName, "D", "E", 25)
	// Column
	charStr := "ABCDEFGHIJKLMNOPQ"
	// Add title
	title := []string{"Category", "Order Date", "Application Number", "Passport Image",
		"Photo", "Surname", "Given Name", "Gender", "DOB", "Nationality",
		"Passport Number", "Passport Expiration", "Entry Date", "Number of Entries", "Validity",
		"Purpose", "Processing Time"}

	styleTitle, err := f.NewStyle(`{"font":{"bold":true},"alignment":{"horizontal":"center","vertical":"center"}}`)
	if err != nil {
		return err
	}
	for index := 0; index < len(title); index++ {
		axis := string(charStr[index]) + "1"
		f.SetCellValue(DefaultSheetName, axis, title[index])
		f.SetCellStyle(DefaultSheetName, axis, axis, styleTitle)
	}
	// Add content
	styleContent, err := f.NewStyle(`{"alignment":{"horizontal":"center","vertical":"center"}}`)
	if err != nil {
		return err
	}

	for i := 0; i < len(orderData.Tasks); i++ {
		application := orderData.Tasks[i]
		inputPodPair := application.InputPods.ToMapKeyValue()
		indexStr := strconv.Itoa(i + 2)
		//style
		f.SetRowHeight(DefaultSheetName, i+2, 100)
		f.SetCellStyle(DefaultSheetName, "A"+indexStr, "Q"+indexStr, styleContent)

		// Category
		f.SetCellValue(DefaultSheetName, "A"+indexStr, s.localize.EN.VisaType[orderData.ETS.Tasks[0]])
		// Order date
		f.SetCellValue(DefaultSheetName, "B"+indexStr, orderData.Order.UpdatedAt.Format(DateTimeFormat))
		// Application Number
		f.SetCellValue(DefaultSheetName, "C"+indexStr, application.ID)
		// Passport image
		passportImage := ""
		if val := cast.ToString("document_copy_of_passport_copy_of_passport_main_page"); val != "" {
			passportImage = cast.ToString(inputPodPair["document_copy_of_passport_copy_of_passport_main_page"])
		}

		if passportImage != "" {
			passportImagePath, format, err := s.AddPictureExcel(passportImage, 150, 100, localPath)
			if err != nil {
				return err
			}
			defer utils.RemoveFile(passportImagePath)
			err = f.AddPicture(DefaultSheetName, "D"+indexStr, passportImagePath, format)
			if err != nil {
				return err
			}
		}

		// Passport photo
		passportPhoto := ""
		if val := cast.ToString("photo_passport_photo_copy_of_photo"); val != "" {
			passportPhoto = cast.ToString(inputPodPair["photo_passport_photo_copy_of_photo"])
		}
		if passportPhoto != "" {
			passportPhotoPath, format, err := s.AddPictureExcel(passportPhoto, 75, 100, localPath)
			if err != nil {
				return err
			}
			defer utils.RemoveFile(passportPhotoPath)
			err = f.AddPicture(DefaultSheetName, "E"+indexStr, passportPhotoPath, format)
			if err != nil {
				return err
			}
		}
		// Family name
		f.SetCellValue(DefaultSheetName, "F"+indexStr, cast.ToString(inputPodPair["passport_core_info_surname"]))
		// Given Name
		f.SetCellValue(DefaultSheetName, "G"+indexStr, cast.ToString(inputPodPair["passport_core_info_given_name"]))
		// Gender
		f.SetCellValue(DefaultSheetName, "H"+indexStr, cast.ToString(inputPodPair["passport_core_info_gender"]))
		// DOB  901004
		dateOfBirth, err := cast.ToTimeE(inputPodPair["passport_core_info_date_of_birth"])
		if err != nil {
			return err
		}
		f.SetCellValue(DefaultSheetName, "I"+indexStr, dateOfBirth.Format(DateFormat))
		// Nationality
		query := gountries.New()
		country, err := query.FindCountryByAlpha(cast.ToString(inputPodPair["passport_core_info_nationality"]))
		if err != nil {
			return err
		}

		f.SetCellValue(DefaultSheetName, "J"+indexStr, country.Name.Common)
		// Passport #
		f.SetCellValue(DefaultSheetName, "K"+indexStr, cast.ToString(inputPodPair["passport_core_info_passport_number"]))
		// Passport Expiration
		expirationDate, err := time.Parse(TimestampFormat, cast.ToString(inputPodPair["passport_core_info_expiration_date"]))
		if err != nil {
			return err
		}
		f.SetCellValue(DefaultSheetName, "L"+indexStr, expirationDate.Format(DateFormat))
		// Entry date
		f.SetCellValue(DefaultSheetName, "M"+indexStr, cast.ToTime(inputPodPair["service_core_info_entry_date"]).Format(DateFormat))
		// No of Entry
		f.SetCellValue(DefaultSheetName, "N"+indexStr, s.localize.EN.NumberOfEntry[cast.ToString(inputPodPair["service_core_info_number_of_entries"])])
		// Validity
		f.SetCellValue(DefaultSheetName, "O"+indexStr, s.localize.EN.Duration[cast.ToString(inputPodPair["service_core_info_validity"])])
		// Purpose
		f.SetCellValue(DefaultSheetName, "P"+indexStr, s.localize.EN.Purpose[cast.ToString(inputPodPair["service_core_info_purpose"])])
		// Processing Time
		f.SetCellValue(DefaultSheetName, "Q"+indexStr, s.localize.EN.ProcessingTime[cast.ToString(inputPodPair["service_core_info_processing_time"])])
	}

	// add special sheet for voa
	if orderData.ETS.Tasks[0] == IssueMethodVOA {
		f.NewSheet(ArrivalInfoSheetName)
		f.SetColWidth(ArrivalInfoSheetName, "A", "M", 20)
		// Column
		specialCharStr := "ABCDEFGHIJKLM"
		// // Add title
		specialTitle := []string{"Category", "Order Date", "Arrival Date Time", "Arrival Airline", "Arrival Flight No.",
			"Arrival Airport", "Arrival Number", "Surname", "Given Name", "Gender", "Nationality", "Entry Date", "Number of Entries"}

		for index := 0; index < len(specialTitle); index++ {
			axis := string(specialCharStr[index]) + "1"
			f.SetCellValue(ArrivalInfoSheetName, axis, specialTitle[index])
			f.SetCellStyle(ArrivalInfoSheetName, axis, axis, styleTitle)
		}

		for i := 0; i < len(orderData.Tasks); i++ {
			task := orderData.Tasks[i]
			inputPodPair := task.InputPods.ToMapKeyValue()

			indexStr := strconv.Itoa(i + 2)
			//style
			f.SetRowHeight(ArrivalInfoSheetName, i+2, 100)
			f.SetCellStyle(ArrivalInfoSheetName, "A"+indexStr, "M"+indexStr, styleContent)

			// Category
			f.SetCellValue(ArrivalInfoSheetName, "A"+indexStr, s.localize.EN.VisaType[orderData.ETS.Tasks[0]])
			// Order date
			f.SetCellValue(ArrivalInfoSheetName, "B"+indexStr, orderData.Order.UpdatedAt.Format(DateTimeFormat))
			// Arrival Date Time
			f.SetCellValue(ArrivalInfoSheetName, "C"+indexStr, "")
			// Arrival Airline
			f.SetCellValue(ArrivalInfoSheetName, "D"+indexStr, "")
			// Arrival Flight No.
			f.SetCellValue(ArrivalInfoSheetName, "E"+indexStr, "")
			// Arrival Airport
			f.SetCellValue(ArrivalInfoSheetName, "F"+indexStr, "")
			// Application Number
			f.SetCellValue(ArrivalInfoSheetName, "G"+indexStr, task.ID)
			// Family name , cast.ToString(inputPods["passport_core_info_nationality"]))
			f.SetCellValue(ArrivalInfoSheetName, "H"+indexStr, cast.ToString(inputPodPair["passport_core_info_surname"]))
			// Given Name
			f.SetCellValue(ArrivalInfoSheetName, "I"+indexStr, cast.ToString(inputPodPair["passport_core_info_given_name"]))
			// Gender
			f.SetCellValue(ArrivalInfoSheetName, "J"+indexStr, cast.ToString(inputPodPair["passport_core_info_gender"]))
			// Nationality
			query := gountries.New()
			country, err := query.FindCountryByAlpha(cast.ToString(inputPodPair["passport_core_info_nationality"]))
			if err != nil {
				return err
			}
			f.SetCellValue(ArrivalInfoSheetName, "K"+indexStr, country.Name.Common)
			// Entry date
			f.SetCellValue(ArrivalInfoSheetName, "L"+indexStr, cast.ToTime(inputPodPair["service_core_info_entry_date"]).Format(DateFormat))
			// No of Entry
			f.SetCellValue(ArrivalInfoSheetName, "M"+indexStr, s.localize.EN.NumberOfEntry[cast.ToString(inputPodPair["service_core_info_number_of_entries"])])
		}
	}

	f.DeleteSheet("Sheet1")

	// Save file
	err = f.SaveAs(fileName)
	if err != nil {
		return err
	}
	return nil
}

func (s *SubmitEmailWorkerProcessor) SendPassportEmailToConsulate(id int, localPath string) (string, error) {
	templateName := "submit_ets"
	order, err := s.etsDao.GetServiceOrderByID(id)
	if err != nil {
		return "", err
	}
	tasks, _, err := s.etsDao.QueryServiceTasks(map[string]any{"order_id": id}, 0, 0)
	if err != nil {
		return "", err
	}
	svcs, err := s.etsDao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return "", err
	}
	svc := svcs[0]
	servicePrices, err := s.etsDao.GetServicePrice([]int{order.ServiceID})
	if err != nil {
		return "", err
	}
	servicePrice := servicePrices[order.ServiceID][0]
	provider, err := s.etsDao.GetEtsProviderByID(servicePrice.ProviderID)
	if err != nil {
		return "", err
	}
	if provider == nil {
		return "", fmt.Errorf("not found provider %s in order %d", servicePrice.ProviderID, order.ServiceID)
	}
	apps := []map[string]string{}
	for _, item := range tasks {
		var givenName, surname string
		for _, v := range item.InputPods {
			if v.ID == "passport_core_info_surname" {
				surname = v.Value.FE.(string)
			}
			if v.ID == "passport_core_info_given_name" {
				givenName = v.Value.FE.(string)
			}
		}
		query := gountries.New()
		country, err := query.FindCountryByAlpha((*order.Config)["region_of_residence"].(string))
		if err != nil {
			return "", err
		}
		app := map[string]string{
			"FullName":          fmt.Sprintf("%s %s", givenName, surname),
			"RegionOfResidence": country.Name.Common,
			"ProcessingTime":    s.localize.EN.ProcessingTime[(*svc.Attributes)["processing_time"].(string)],
			"Task":              s.localize.EN.Tasks[strings.Join(svc.Tasks, ",")],
		}
		if svc.Tag != nil {
			app["Task"] = fmt.Sprintf("%s %s", s.localize.EN.Tasks[strings.Join(svc.Tasks, ",")], s.localize.EN.Tag[*svc.Tag])
		}
		apps = append(apps, app)
	}
	query := gountries.New()
	country, err := query.FindCountryByAlpha(svc.Country)
	if err != nil {
		return "", err
	}
	parameters := map[string]any{
		"ID":           strconv.Itoa(id),
		"Name":         provider.Name,
		"Country":      country.Name.Common,
		"Task":         s.localize.EN.Tasks[strings.Join(svc.Tasks, ",")],
		"Applications": apps,
		"URL":          fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", s.conf["website_host_name"].(string), id),
	}
	// 4. send message to notification queue
	var email string
	if s.conf["env"].(string) == ProductionEnv {
		email = provider.Contact.Email
	} else {
		email = s.conf["visas_email"].(string)
	}
	ccEmail := []string{}
	if !provider.Contact.SecondaryEmail.IsZero() {
		if s.conf["env"].(string) != ProductionEnv {
			provider.Contact.SecondaryEmail.SetValid(s.conf["visas_email"].(string))
		}
		ccEmail = []string{provider.Contact.SecondaryEmail.String}
	}
	err = s.SendEmail(templateName, email, ccEmail, parameters, []string{})
	if err != nil {
		return "", err
	}
	return templateName, nil
}
