package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/localize"
	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/queue"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func main() {
	app := cli.NewApp()
	app.Name = "aria-submit"
	app.Usage = "Submit service for Aria"
	app.Commands = []cli.Command{
		submitCmd(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("Submit failed with error %v", err)
	}
}

func submitCmd() cli.Command {
	return cli.Command{
		Name:      "submit",
		ShortName: "s",
		Usage:     "Start submit inbound message processor",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:     "ad_sqs",
				Usage:    "Env for sqs config",
				EnvVar:   "ad_sqs",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_aws",
				Usage:    "Env for aws config",
				EnvVar:   "ad_aws",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_s3",
				Usage:    "Env for s3 config",
				EnvVar:   "ad_s3",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_submit_services",
				Usage:    "Env for submit config",
				EnvVar:   "ad_submit_services",
				Required: true,
			},
			cli.StringFlag{
				Name:     "localize-file",
				Usage:    "Localize file for submit dispatcher",
				Required: true,
				Value:    "conf/localize.yaml",
			},
			cli.StringFlag{
				Name:     "ad_db",
				Usage:    "Env for aws config",
				EnvVar:   "ad_db",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_ses",
				Usage:    "Env for ses config",
				EnvVar:   "ad_ses",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_email",
				Usage:    "Env for email config",
				EnvVar:   "ad_email",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_website",
				Usage:    "Env for website config",
				EnvVar:   "ad_website",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_api_token",
				Usage:    "API key for api token config",
				EnvVar:   "ad_api_token",
				Required: true,
			},
		},
		Action: runSubmit(),
	}
}

func runSubmit() cli.ActionFunc {
	return func(c *cli.Context) error {

		localizeFile := c.String("localize-file")
		localize, err := localize.ReadStringLocalizeMap(localizeFile)
		if err != nil {
			return err
		}

		sqsConfigMap := utils.GetMapEnv("ad_sqs")
		if sqsConfigMap["url_prefix"].(string) == "" {
			return fmt.Errorf("missing url_prefix in ad_sqs")
		}
		if sqsConfigMap["submit_sqs_name"].(string) == "" {
			return fmt.Errorf("missing submit_sqs_name in ad_sqs")
		}

		s3ConfigMap := utils.GetMapEnv("ad_s3")
		if s3ConfigMap["ariadirect_prod_notification-events"].(string) == "" {
			return fmt.Errorf("missing ariadirect_prod_notification-events in ad_s3")
		}

		workerConfigMap := utils.GetMapEnv("ad_submit_services")
		if workerConfigMap["env"].(string) == "" {
			return fmt.Errorf("missing env in ad_submit_services")
		}
		websiteConfigMap := utils.GetMapEnv("ad_website")
		if websiteConfigMap["host_name"].(string) == "" {
			return fmt.Errorf("missing host_name in ad_website")
		}

		emailConfigMap := utils.GetMapEnv("ad_email")
		if emailConfigMap["visas"].(string) == "" {
			return fmt.Errorf("missing visas in ad_email")
		}

		apiTokenConfigMap := utils.GetMapEnv("ad_api_token")
		if apiTokenConfigMap["token"].(string) == "" {
			return fmt.Errorf("missing token in ad_api_token")
		}

		config := map[string]any{
			"env":               workerConfigMap["env"].(string),
			"website_host_name": websiteConfigMap["host_name"].(string),
			"visas_email":       emailConfigMap["visas"].(string),
			"token":             apiTokenConfigMap["token"].(string),
		}

		submitSQSQueueURL := sqsConfigMap["url_prefix"].(string) + "/" + sqsConfigMap["submit_sqs_name"].(string)

		awsUSConfig := utils.GetMapEnv("ad_aws")
		if awsUSConfig["region"].(string) == "" {
			return fmt.Errorf("missing region in ad_aws")
		}

		awsConfig := aws.NewConfig().WithRegion(awsUSConfig["region"].(string)).WithLogLevel(aws.LogOff)

		sess, err := session.NewSession(awsConfig)
		if err != nil {
			return err
		}

		sesConfigMap := utils.GetMapEnv("ad_ses")
		if sesConfigMap["region"].(string) == "" {
			return fmt.Errorf("missing region in ad_ses")
		}
		awsConfigSES := aws.NewConfig().WithRegion(sesConfigMap["region"].(string)).WithLogLevel(aws.LogOff)

		sessSES, err := session.NewSession(awsConfigSES)
		if err != nil {
			return err
		}
		ses := awslib.NewSESClient(sessSES)

		s3Presigner := awslib.NewS3Svc(sess)
		s3Downloader := awslib.NewDownloader(sess)

		dbConfigMap := utils.GetMapEnv("ad_db")
		if dbConfigMap["write_host"].(string) == "" {
			log.Fatal(fmt.Errorf("missing write_host in ad_db"))
		}
		if dbConfigMap["dbname"].(string) == "" {
			log.Fatal(fmt.Errorf("missing dbname in ad_db"))
		}
		if dbConfigMap["username"].(string) == "" {
			log.Fatal(fmt.Errorf("missing username in ad_db"))
		}
		if dbConfigMap["password"].(string) == "" {
			log.Fatal(fmt.Errorf("missing password in ad_db"))
		}
		conn, err := db.NewAuroraDBFromConfigMap(dbConfigMap)
		if err != nil {
			return err
		}

		submit := NewSubmitEmailWorkerProcessor(ses, s3Presigner, s3Downloader, conn, s3ConfigMap, config, localize)

		s := queue.NewSQSProcessor(sess, submitSQSQueueURL, submit.SubmitEmailWorker)
		// submit.SubmitEmailWorker(&sqs.Message{
		// 	Body: aws.String(`{"callback":"https://api.ariadirectcorp.com/v1/pkg/ets-internal/3070/add-output-file","id":3070,"method":"email","product_type":"new_visa"}`),
		// })
		s.Run()
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGHUP, syscall.SIGTERM)

	loop:
		for {
			select {
			case <-sigCh:
				break loop
			}
		}

		return nil
	}
}
