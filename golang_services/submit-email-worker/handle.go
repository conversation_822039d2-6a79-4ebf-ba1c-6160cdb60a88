package main

import (
	"encoding/json"
	"fmt"
	"image"
	_ "image/jpeg"
	_ "image/png"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/sdk/product"

	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/rs/zerolog/log"

	"bitbucket.org/persistence17/aria/golang_services/localize"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/file"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/services"
)

const (
	DateFormat           = "Jan 02, 2006"
	DateTimeFormat       = "Jan 02, 2006 3:04 PM"
	TimestampFormat      = "2006-01-02T15:04:05Z"
	DefaultSheetName     = "Application"
	ArrivalInfoSheetName = "Arrival Info"
	IssueMethodVOA       = "voa"
	IssueMethodRegular   = "regular"
	ProductionEnv        = "prod"
	VietnamIsoAlpha3     = "VNM"
	ContentTypeExcel     = "application/octet-stream"
	ContentTypeMsg       = "text/plain; charset=utf-8"
)

type SubmitEmailWorkerProcessor struct {
	dao           *db.Dao
	etsDao        *db.EtsDao
	ses           *awslib.SESClient
	s3Presigner   *awslib.S3Svc
	s3Downloader  *awslib.S3Downloader
	serviceClient *services.ServiceClient
	s3Config      map[string]any
	conf          map[string]any
	localize      *localize.Localize
}

type BodyMessage struct {
	ProductType string `json:"product_type"`
	ID          int    `json:"id"`
	UserID      string `json:"user_id"`
	Method      string `json:"method"`
	Callback    string `json:"callback"`
}

type OrderData struct {
	Order    models.ServiceOrder   `json:"order"`
	Tasks    []*models.ServiceTask `json:"tasks"`
	Provider models.EtsProvider    `json:"provider"`
	// VisaOrder   models.VisaOrder     `json:"visa_order"`
	ETS *models.ExtendedTravelService `json:"ets"`
}

type Config struct {
	IsGroup  bool `json:"is_group"`
	IsUrgent bool `json:"urgent"`
}

func NewSubmitEmailWorkerProcessor(ses *awslib.SESClient, s3Presigner *awslib.S3Svc, s3Downloader *awslib.S3Downloader, conn *db.AuroraDB, s3Config, conf map[string]any, localize *localize.Localize) *SubmitEmailWorkerProcessor {
	service := services.NewServiceClient()
	dao := db.NewDaoWithDb(conn)
	etsDao := db.NewEtsDao(conn)
	return &SubmitEmailWorkerProcessor{dao: dao, etsDao: etsDao, ses: ses, s3Presigner: s3Presigner, s3Downloader: s3Downloader, s3Config: s3Config, conf: conf, localize: localize, serviceClient: service}
}

func (s *SubmitEmailWorkerProcessor) SubmitEmailWorker(message *sqs.Message) error {
	fmt.Println(*message.Body)
	var msg BodyMessage
	err := json.Unmarshal([]byte(*message.Body), &msg)
	if err != nil {
		return err
	}
	localPath, err := filepath.Abs(filepath.Dir(os.Args[0]))
	if err != nil {
		return err
	}
	var templateName, summaryFile string
	switch msg.ProductType {
	case "new_visa", "new_visa_urgent":
		{
			// get order data
			orderData, err := s.GetOrderData(msg.ID)
			if err != nil {
				return err
			}
			// send email
			templateName, summaryFile, err = s.SendVisaEmailToConsulate(*orderData, localPath)
			if err != nil {
				utils.LogOrderEvent(utils.ERROR, "visa", msg.ID, "SEND_VISA_EMAIL_TO_CONSULATE_ERROR", err.Error())
				log.Error().Err(err).Msg("SendVisaEmailToConsulate")
				summaryFile = ""
			}
			// Update the package status
			updateData := map[string]any{
				"summary_file": summaryFile,
			}
			updateDataJSON, err := json.Marshal(updateData)
			if err != nil {
				return err
			}
			fmt.Println("Callback", msg.Callback) // /add-output-file
			fmt.Println(string(updateDataJSON))
			fmt.Println("Token", s.conf["token"].(string))
			statusCode, _, err := s.serviceClient.SendInternalRequest(updateDataJSON, msg.Callback, s.conf["token"].(string))
			if err != nil {
				utils.LogOrderEvent(utils.ERROR, "visa", msg.ID, "SEND_EMAIL_TO_CONSULATE_CALLBACK_ERROR", err.Error())
				return err
			}
			if statusCode >= 400 {
				utils.LogOrderEvent(utils.ERROR, "visa", msg.ID, "SEND_EMAIL_TO_CONSULATE_CALLBACK_ERROR", "unable to send package status update request")
				return fmt.Errorf("unable to send package status update request")
			}
			utils.LogOrderEvent(utils.SUCCESS, "visa", msg.ID, "SEND_EMAIL_TO_CONSULATE", "success")
		}
	case product.ETSProductType:
		{
			templateName, err = s.SendPassportEmailToConsulate(msg.ID, localPath)
			if err != nil {
				utils.LogOrderEvent(utils.ERROR, "passport", msg.ID, "SEND_EMAIL_TO_CONSULATE_ERROR", err.Error())
				return err
			}
			// Update the package status
			updateData := map[string]any{
				"order_id": msg.ID,
				"status":   string(models.EtsOrderStatusSubmitted),
			}
			updateDataJSON, err := json.Marshal(updateData)
			if err != nil {
				return err
			}
			statusCode, _, err := s.serviceClient.SendInternalRequest(updateDataJSON, msg.Callback, s.conf["token"].(string))
			if err != nil {
				utils.LogOrderEvent(utils.ERROR, "passport", msg.ID, "SEND_EMAIL_TO_CONSULATE_CALLBACK_ERROR", err.Error())
				return err
			}
			if statusCode >= 400 {
				utils.LogOrderEvent(utils.ERROR, "passport", msg.ID, "SEND_EMAIL_TO_CONSULATE_CALLBACK_ERROR", "unable to send package status update request")
				return fmt.Errorf("unable to send package status update request")
			}
			utils.LogOrderEvent(utils.SUCCESS, "passport", msg.ID, "SEND_EMAIL_TO_CONSULATE", "success")
		}
	}
	// the message to users also backup-ed at S3
	backupFile := templateName + time.Now().String() + ".txt"
	backupPath := localPath + "/" + backupFile
	err = ioutil.WriteFile(backupPath, []byte(*message.Body), 0755)
	if err != nil {
		return err
	}
	// remove file
	defer utils.RemoveFile(backupPath)
	// add to s3
	bucket := s.s3Config["ariadirect_prod_notification-events"].(string)
	key := file.BuildPkgInputS3Key(msg.UserID, msg.ID, backupFile)
	err = s.s3Presigner.UploadFile(bucket, key, backupPath, ContentTypeMsg)
	if err != nil {
		return err
	}

	return nil
}

func (s *SubmitEmailWorkerProcessor) SendEmail(templateName, to string, cc []string, parameters map[string]any, attachments []string) error {
	user, err := s.dao.GetUserByEmail(to)
	language := "EN" // Default english
	if err == nil && user.AppLanguage != "" {
		language = user.AppLanguage
	}

	// Skip guest generated email
	if regexp.MustCompile(`guest_\<EMAIL>`).MatchString(to) {
		return nil
	}

	// find template in database
	template, err := s.dao.GetEmailTemplateByName(templateName, language)
	if err != nil {
		return err
	}
	if template == nil {
		return fmt.Errorf("Empty template in database")
	}

	// find signature template in database
	signature, err := s.dao.GetEmailTemplateByName("email_signature", language)
	if err != nil {
		return err
	}
	if signature == nil {
		return fmt.Errorf("Empty email_signature in database")
	}

	parameters["EMAIL_SIGNATURE"] = signature.HTMLBody

	// send email by ses
	err = s.ses.PrepareAndSendRawEmail(template.From, []string{to}, cc, []string{s.conf["visas_email"].(string)}, template.Title, template.HTMLBody, attachments, parameters)
	if err != nil {
		return err
	}

	// save notification
	subject, body, err := aws.ParseTemplate(template.Title, template.HTMLBody, parameters)
	if err != nil {
		return err
	}

	if template.SaveAsNotification {
		if err := s.dao.CreateUserEmailNotification(subject, body, language, user.ID); err != nil {
			return err
		}
	}

	return nil
}

func (s *SubmitEmailWorkerProcessor) AddPictureExcel(imagePath string, widthDefault int, hightDefault int, localPath string) (string, string, error) {
	// download file
	bucket, key, _ := utils.UrlToS3BucketAndKey(imagePath)
	fileName := fmt.Sprintf("%s/%s.jpeg", localPath, time.Now().String())
	err := s.s3Downloader.DownloadFromS3Bucket(bucket, key, fileName)
	if err != nil {
		return "", "", err
	}
	// get file config
	file, err := os.Open(fileName)
	if err != nil {
		return "", "", err
	}
	image, _, err := image.DecodeConfig(file)
	if err != nil {
		return "", "", err
	}
	x := float64(widthDefault) / float64(image.Width)
	y := float64(hightDefault) / float64(image.Height)

	format := map[string]any{
		"x_scale": x,
		"y_scale": y,
	}
	formatByte, _ := json.Marshal(format)
	return fileName, string(formatByte), nil
}

func (s *SubmitEmailWorkerProcessor) GetOrderData(orderID int) (*OrderData, error) {
	order, err := s.etsDao.GetServiceOrderByID(orderID)
	if err != nil {
		return nil, err
	}

	tasks, _, err := s.etsDao.QueryServiceTasks(map[string]any{
		"order_id": orderID,
	}, 0, 0)
	if err != nil {
		return nil, err
	}

	provider, err := s.etsDao.GetEtsProviderByID(order.ProviderID)
	if err != nil {
		return nil, err
	}

	// get visa product info
	ets, err := s.etsDao.GetETSByID(order.ServiceID)
	if err != nil {
		return nil, err
	}
	if ets == nil {
		return nil, fmt.Errorf("Missing product: %v", order.ServiceID)
	}

	return &OrderData{
		Order:    *order,
		Provider: *provider,
		Tasks:    tasks,
		ETS:      ets,
	}, nil
}
