package middlewares

import (
	"encoding/json"
	"net/http"
	"os"
	"strings"
	"sync"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/tidwall/gjson"
	"gopkg.in/guregu/null.v3"

	aderr "bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/services"
)

const (
	serviceClient = "service_client"
	UserRoles     = "user-roles"
	AuthInfo      = "auth-info"
)

var (
	once      sync.Once
	secretMap map[string]any
)

func InitSecretMap(configEnvName string) error {
	var err error
	once.Do(func() {
		jwtSecret := os.Getenv(configEnvName)
		err = json.Unmarshal([]byte(jwtSecret), &secretMap)
	})
	return err
}

func AuthenticateMW() gin.HandlerFunc {
	return func(c *gin.Context) {
		internalToken := c.Request.Header.Get("x-ad-token")
		if internalToken == gjson.Parse(os.Getenv("ad_api_token")).Get("token").String() {
			c.Next()
			return
		}
		token := c.Request.Header.Get("x-access-token")
		token = strings.TrimSpace(token)
		if token == "" {
			log.Info().Msg("empty token")
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   aderr.InvalidTokenErr,
			})
			return
		}

		tokenInfo := &models.Token{}
		_, err := jwt.ParseWithClaims(token, tokenInfo, func(token *jwt.Token) (any, error) {
			return []byte(secretMap["jwt"].(string)), nil
		})
		if err != nil {
			log.Error().Err(err).Msg("failed to parse jwt")
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   aderr.InvalidTokenErr,
			})
			return
		}

		v := c.Value(serviceClient)
		isl, _ := v.(*services.ServiceClient)

		hostName := GetAccountServiceConfig(c)["host_name"].(string)
		url := hostName + "/auth/user/" + tokenInfo.UserID

		statusCode, bodyResp, err := isl.GetUserInfo(token, url)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		if statusCode > 400 {
			log.Error().Str("host_name", hostName).Int("status_code", statusCode).Msg("account service returns non-200")
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   aderr.InvalidTokenErr,
			})
			return
		}
		var userInfo models.UserInfo
		if err := json.Unmarshal(bodyResp, &userInfo); err != nil {
			log.Error().Str("error", err.Error()).Msg("failed to unmarshal response from user service")
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		c.Set(UserRoles, []string{userInfo.Role})
		c.Set("user-id", userInfo.ID)
		authUser := &models.AuthInfo{
			UserID:      userInfo.ID,
			OrgID:       userInfo.OrganizationID,
			Roles:       []string{userInfo.Role},
			Email:       userInfo.Email,
			UserTypes:   userInfo.UserTypes,
			AppLanguage: userInfo.AppLanguage,
		}

		// set 0 org ID as invalid
		if userInfo.OrganizationID.Int64 <= 0 {
			authUser.OrgID = null.NewInt(0, false)
		}

		// default english
		if authUser.AppLanguage == "" {
			authUser.AppLanguage = "EN"
		}

		c.Set(AuthInfo, authUser)
		c.Next()
	}
}

//func AuthenticatePublic(roles []string) gin.HandlerFunc {
//	return func(c *gin.Context) {
//		token := c.Request.Header.Get("Authorization")
//		if token == "" {
//			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
//				"success": false,
//				"data":    aderr.InvalidTokenErr,
//			})
//			return
//		}
//		v := c.Value(serviceClient)
//		if v == nil {
//			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
//				"success": false,
//				"data": gin.H{
//					"code":    "ERR00002",
//					"message": "missing service client",
//				},
//			})
//			return
//		}
//		isl, ok := v.(*services.ServiceClient)
//		if !ok {
//			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
//				"success": false,
//				"data": gin.H{
//					"code":    "ERR00002",
//					"message": "cannot cast to service client",
//				},
//			})
//			return
//		}
//		statusCode, bodyResp, err := isl.ValidationToken(token, roles)
//		if err != nil {
//			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
//				"success": false,
//				"data":    err.Error(),
//			})
//			return
//		}
//		if statusCode > 400 {
//			c.AbortWithStatusJSON(statusCode, gin.H{
//				"success": false,
//				"data":    bodyResp,
//			})
//			return
//		}
//		// TODO: decode user ID from token and set to context
//		c.Next()
//	}
//}

func AuthorizationMW() gin.HandlerFunc {
	service := services.NewServiceClient()
	return func(c *gin.Context) {
		c.Set(serviceClient, service)
		c.Next()
	}
}

func DummyAuthMW() gin.HandlerFunc {
	return func(c *gin.Context) {
		user := c.GetHeader("x-aria-user-id")
		role := c.GetHeader("x-aria-user-type")
		email := c.GetHeader("x-aria-user-email")
		var org null.Int
		if orgID, err := utils.GetIntPathParam(c, "org-id"); err != nil {
			org = null.NewInt(0, false)
		} else {
			org = null.NewInt(int64(orgID), true)
		}

		c.Set(UserRoles, []string{role})
		c.Set("user-id", user)
		c.Set(AuthInfo, &models.AuthInfo{
			UserID: user,
			OrgID:  org,
			Roles:  []string{role},
			Email:  email,
		})
		c.Next()
	}
}

func GetUserRoles(c *gin.Context) []string {
	v := c.Value(UserRoles)
	if v == nil {
		return []string{}
	}

	roles, ok := v.([]string)
	if !ok {
		return []string{}
	}
	return roles
}

func GetAuthInfo(c *gin.Context) *models.AuthInfo {
	v := c.Value(AuthInfo)
	if v == nil {
		return nil
	}

	authInfo, ok := v.(*models.AuthInfo)
	if !ok {
		return nil
	}
	return authInfo
}

func IndividualAuthMW(c *gin.Context) {
	user := GetAuthInfo(c)
	if user == nil {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}
	// set auth user with invalid org ID
	user.OrgID = null.NewInt(0, false)
	c.Set(AuthInfo, user)
	return
}

func AuthenticateInternalMW() gin.HandlerFunc {
	return func(c *gin.Context) {

		token := c.Request.Header.Get("x-ad-token")
		token = strings.TrimSpace(token)
		if token == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   aderr.InvalidTokenErr,
			})
			return
		}

		apiToken := GetAPITokenConfig(c)["token"].(string)
		if token != apiToken {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   aderr.InvalidTokenErr,
			})
			return
		}

		c.Next()
	}
}
