package middlewares

import (
	"context"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/sdk/payments"
)

func GetPaymentClients(c context.Context, pType string) (payments.IPaymentClient, error) {
	var v = c.Value(strings.ToUpper(pType))
	if v == nil {
		return nil, fmt.Errorf("empty payment client")
	}
	client, ok := v.(payments.IPaymentClient)
	if !ok {
		return nil, fmt.<PERSON><PERSON><PERSON>("failed to cast to IPaymentClient")
	}
	return client, nil
}

func GetPaymentNotificationHandler(c context.Context, pType string) (payments.IPaymentCallbackHandler, error) {
	var v = c.Value(strings.ToUpper(pType))
	if v == nil {
		return nil, fmt.Errorf("empty payment client")
	}
	handler, ok := v.(payments.IPaymentCallbackHandler)
	if !ok {
		return nil, fmt.<PERSON><PERSON><PERSON>("failed to cast notification handler")
	}
	return handler, nil
}

// PaymentClientsMW set payment client instants
func PaymentClientsMW(instants ...payments.IPaymentClient) gin.HandlerFunc {
	return func(c *gin.Context) {
		for _, instant := range instants {
			c.Set(instant.GetType(), instant)
		}
		c.Next()
	}
}

func PaymentConfigMW(config map[string]any) gin.HandlerFunc {
	return func(c *gin.Context) {
		for k, v := range config {
			c.Set(k, v)
		}
		c.Next()
	}
}
