package middlewares

import (
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/sdk/config"
)

func LoadCommonMWAndConf(c *cli.Context, r gin.IRouter) (gin.IRouter, map[string]any, error) {
	corsConf := cors.DefaultConfig()
	corsConf.AddAllowHeaders("x-access-token", "Language")
	corsConf.AllowAllOrigins = true
	r.Use(cors.New(corsConf))
	r.Use(IDGeneratorMW())
	if !c.Bool("bypass-auth") {
		r.Use(AuthorizationMW())
	}

	confFile := c.String("conf-file")
	if confFile == "" {
		return r, nil, nil
	}
	conf, err := config.ReadStringConfMap(confFile)
	if err != nil {
		return nil, nil, err
	}
	r.Use(ConfigMW(conf))
	return r, conf, nil
}
