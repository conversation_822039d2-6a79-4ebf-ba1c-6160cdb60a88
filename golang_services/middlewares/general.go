package middlewares

import (
	"os"

	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/tidwall/gjson"
)

func GetDB() *db.Dao {
	dbConfigMap := utils.GetMapEnv("ad_db")
	conn, err := db.NewAuroraDBFromConfigMap(dbConfigMap)
	if err != nil {
		return nil
	}
	return db.NewDaoWithDb(conn)
}

func GetSQS() *awslib.SQSClient {
	region := gjson.Parse(os.Getenv("ad_aws")).Get("region").String()
	if region == "" {
		return nil
	}
	sess, _ := session.NewSession(aws.NewConfig().WithRegion(region).WithLogLevel(aws.LogOff))

	notificationQueue := gjson.Parse(os.Getenv("ad_sqs")).Get("url_prefix").String() + "/" + gjson.Parse(os.Getenv("ad_sqs")).Get("notification_sqs_name").String()
	sender := awslib.NewSQSClient(sess, notificationQueue)
	return sender
}
