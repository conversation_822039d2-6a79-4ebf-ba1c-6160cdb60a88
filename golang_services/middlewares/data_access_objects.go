package middlewares

import (
	"context"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
)

const (
	paymentDao = "payment_dao"
	visaDao    = "visa_dao"
	atlasDao   = "atlas_dao"
	orm        = "orm"
)

func GetPaymentsDao(c context.Context) (db.IPaymentsDao, error) {
	v := c.Value(paymentDao)
	if v == nil {
		return nil, fmt.Errorf("payment db not set")
	}

	dao, ok := v.(*db.PaymentsDao)
	if !ok {
		return nil, fmt.Errorf("wrong db for payments")
	}
	return &db.PaymentsDao{
		Db: dao.Db,
	}, nil
}

func PaymentDaoMW(conn *db.AuroraDB) gin.HandlerFunc {
	return func(c *gin.Context) {
		dao := db.NewPaymentDaoWithDb(conn)
		c.Set(paymentDao, dao)
		c.Next()
	}
}

func VisaDaoMW(conn *db.AuroraDB) gin.HandlerFunc {
	return func(c *gin.Context) {
		dao := &db.Dao{
			Db: conn,
		}
		c.Set(visaDao, dao)
		c.Next()
	}
}

func GetVisaDao(c *gin.Context) (db.IDao, error) {
	v := c.Value(visaDao)
	if v == nil {
		return nil, fmt.Errorf("visa db not set")
	}

	dao, ok := v.(*db.Dao)
	if !ok {
		return nil, fmt.Errorf("wrong db for visa")
	}
	newDao := &db.Dao{Db: dao.Db}
	return newDao, nil
}

func AtlasDaoMW(conn *db.AuroraDB) gin.HandlerFunc {
	return func(c *gin.Context) {
		dao := &db.AtlasDao{Db: conn}
		c.Set(atlasDao, dao)
		c.Next()
	}
}

func GetAtlasDao(c *gin.Context) db.IAtlasDao {
	v := c.Value(atlasDao)
	if v == nil {
		return nil
	}
	dao, ok := v.(*db.AtlasDao)
	if !ok {
		return nil
	}
	newDao := &db.AtlasDao{Db: dao.Db}
	return newDao
}

func ORMDaoMW(conn *db.AuroraDB) gin.HandlerFunc {
	return func(c *gin.Context) {
		db, err := gorm.Open(postgres.New(postgres.Config{Conn: conn.Db}))
		if err != nil {
			fmt.Println(err)
		}
		c.Set(orm, db)
		c.Next()
	}
}

func GetORMDao(c *gin.Context) *gorm.DB {
	v := c.Value(orm)
	if v == nil {
		return nil
	}
	dao, ok := v.(*gorm.DB)
	if !ok {
		return nil
	}
	return dao
}
