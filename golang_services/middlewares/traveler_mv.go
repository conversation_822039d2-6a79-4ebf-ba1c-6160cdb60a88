package middlewares

import (
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
)

const (
	travelerKey = "traveler"
)

func TravelerCheckerMW(c *gin.Context) {
	authInfo := GetAuthInfo(c)
	if authInfo == nil {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}

	dao, err := GetVisaDao(c)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	traveler, err := dao.GetTravelerByID(c.Param("traveler-id"))
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	// User can access their own traveler
	if traveler.UserID == authInfo.UserID {
		c.Set(travelerKey, traveler)
		c.Next()
		return
	}

	// Get traveler owner
	travelerOwner, err := dao.GetUserByID(traveler.UserID)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   err,
		})
		return
	}

	// Admin can access
	if funk.Contains(authInfo.Roles, "ad_admin") {
		c.Set(travelerKey, traveler)
		c.Next()
		return
	}

	// Admin can access to traveler in org
	if funk.Contains(authInfo.Roles, "admin") && travelerOwner.OrganizationID.Int64 == authInfo.OrgID.Int64 {
		c.Set(travelerKey, traveler)
		c.Next()
		return
	}

	c.JSON(http.StatusForbidden, gin.H{
		"success": false,
		"error":   "you don't have permission to access this resource",
	})

}

func GetTravelerInfo(c *gin.Context) *models.Traveler {
	v := c.Value(travelerKey)
	if v == nil {
		return &models.Traveler{}
	}

	travelerIndex, ok := v.(*models.Traveler)

	if !ok {
		return &models.Traveler{}
	}
	return travelerIndex
}
