package middlewares

import (
	sdkAWS "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/rekognition"
	"github.com/gin-gonic/gin"
)

const (
	awsRekognition   = "aws-rekognition"
	awsImageAnalyzer = "aws-image-analyzer"
)

func NewRekognition(sess *session.Session) gin.HandlerFunc {
	svc := rekognition.New(sess, aws.NewConfig().WithRegion("us-west-2")) // Only support some regions
	return func(c *gin.Context) {
		c.Set(awsRekognition, svc)
		c.Next()
	}
}

func GetRekognition(c *gin.Context) *rekognition.Rekognition {
	v := c.Value(awsRekognition)
	if v == nil {
		return nil
	}
	tx, ok := v.(*rekognition.Rekognition)
	if !ok {
		return nil
	}
	return tx
}

func NewImageAnalyzer(sess *session.Session) gin.HandlerFunc {
	analyzer := sdkAWS.NewRekImageAnalyzer(sess)
	return func(c *gin.Context) {
		c.Set(awsImageAnalyzer, analyzer)
		c.Next()
	}
}

func GetImageAnalyzer(c *gin.Context) *sdkAWS.RekImageAnalyzer {
	v := c.Value(awsImageAnalyzer)
	if v == nil {
		return nil
	}
	tx, ok := v.(*sdkAWS.RekImageAnalyzer)
	if !ok {
		return nil
	}
	return tx
}
