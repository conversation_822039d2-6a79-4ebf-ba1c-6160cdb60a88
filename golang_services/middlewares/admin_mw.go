package middlewares

import (
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
)

func AdminCheckerMW(c *gin.Context) {
	authInfo := GetAuthInfo(c)
	if authInfo == nil || authInfo.OrgID.IsZero() {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "This access need admin permission",
		})
		return
	}

	if !funk.Contains(authInfo.UserTypes, models.UserTypeAdmin) {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "This access need admin permission",
		})
		return
	}

	c.Set("admin_auth_info", *authInfo)

	c.Next()
}
