package middlewares

import (
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"github.com/gin-gonic/gin"
)

const (
	consulateKey = "consulate"
)

func ConsulateCheckerMW(c *gin.Context) {

	authInfo := GetAuthInfo(c)
	if authInfo == nil || authInfo.OrgID.IsZero() {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}

	dao, err := GetVisaDao(c)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	consulate, err := dao.GetConsulateByOrgID(authInfo.OrgID.Int64)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}

	if consulate == nil || consulate.ID == "" {
		c.<PERSON>(http.StatusUnauthorized, gin.H{
			"success": false,
		})
		return
	}
	c.Set(consulate<PERSON><PERSON>, consulate)
	c.Next()
}

func GetConsulateInfo(c *gin.Context) *models.Consulate {
	v := c.Value(consulateKey)
	if v == nil {
		return &models.Consulate{}
	}

	consulate, ok := v.(*models.Consulate)

	if !ok {
		return &models.Consulate{}
	}
	return consulate
}
