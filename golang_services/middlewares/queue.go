package middlewares

import (
	"context"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/sdk/queue"
)

const (
	simpleQueuePrefix = "simple_queue_"
)

func GetSimpleQueueSender(c context.Context, queueName string) queue.ISimpleQueueProducer {
	v := c.Value(simpleQueuePrefix + queueName)
	if v == nil {
		return nil
	}
	q, ok := v.(queue.ISimpleQueueProducer)
	if !ok {
		return nil
	}
	return q
}

func SimpleQueueMW(q queue.ISimpleQueueProducer, queueName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(simpleQueuePrefix+queueName, q)
		c.Next()
	}
}
