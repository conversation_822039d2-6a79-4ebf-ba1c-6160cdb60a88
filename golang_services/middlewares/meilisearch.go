package middlewares

import (
	"github.com/gin-gonic/gin"
	"github.com/meilisearch/meilisearch-go"
)

const MEILISEARCH_CLIENT = "meilisearch"

func MeilisearchClientMW(client *meilisearch.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(MEILISEARCH_CLIENT, client)
		c.Next()
	}
}

func GetMeilisearchClient(c *gin.Context) *meilisearch.Client {
	v := c.Value(MEILISEARCH_CLIENT)
	if v == nil {
		return nil
	}
	client, ok := v.(*meilisearch.Client)
	if !ok {
		return nil
	}
	return client
}
