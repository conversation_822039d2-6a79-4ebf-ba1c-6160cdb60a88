package middlewares

import (
	"context"
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/localize"
	"bitbucket.org/persistence17/aria/golang_services/sdk/fedex"
	"github.com/gin-gonic/gin"
	elastic "github.com/olivere/elastic/v7"
)

const (
	PaymentNotifyURLConfigPrefix = "payment_url_"

	AccountServiceConfigKey = "account-service-config-key"
	configKey               = "config-key"
	serviceConfigKey        = "service-config-key"
	elasticKey              = "elastic-key"
	fedexKey                = "fedex-key"
	awsRegionKey            = "aws-region"
	apiTokenConfigKey       = "api-token"
	localizeKey             = "localize"
)

func GetPaymentNotifyURL(c context.Context, pType string) string {
	v := c.Value(serviceConfigKey)
	if v == nil {
		return ""
	}

	m, ok := v.(map[string]any)
	if !ok {
		return ""
	}
	val, ok := m[PaymentNotifyURLConfigPrefix+strings.ToLower(pType)]
	if !ok {
		return ""
	}
	s, ok := val.(string)
	if !ok {
		return ""
	}
	return s
}

func ConfigMW(config map[string]any) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(configKey, config)
		c.Next()
	}
}

func ServiceConfigMapMW(config map[string]any) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(serviceConfigKey, config)
		c.Next()
	}
}

func GetServiceConfigMap(c *gin.Context) map[string]any {
	v := c.Value(serviceConfigKey)
	if v == nil {
		return map[string]any{}
	}
	m, ok := v.(map[string]any)
	if !ok {
		return map[string]any{}
	}
	return m
}

func AccountServiceConfigMW(config map[string]any) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(AccountServiceConfigKey, config)
		c.Next()
	}
}

func NewESMW(es *elastic.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(elasticKey, es)
		c.Next()
	}
}

func GetAccountServiceConfig(c *gin.Context) map[string]any {
	v := c.Value(AccountServiceConfigKey)
	if v == nil {
		return map[string]any{}
	}
	m, ok := v.(map[string]any)
	if !ok {
		return map[string]any{}
	}
	return m
}

func GetESMW(c *gin.Context) *elastic.Client {
	v := c.Value(elasticKey)
	if v == nil {
		return nil
	}

	m, ok := v.(*elastic.Client)
	if !ok {
		return nil
	}
	return m
}

func FedexMW(fdx *fedex.FedexClient) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(fedexKey, fdx)
		c.Next()
	}
}

func GetFedexMW(c *gin.Context) *fedex.FedexClient {
	v := c.Value(fedexKey)
	if v == nil {
		return nil
	}

	m, ok := v.(*fedex.FedexClient)
	if !ok {
		return nil
	}
	return m
}

func AWSRegionMW(awsRegion string) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(awsRegionKey, awsRegion)
		c.Next()
	}
}

func GetAWSRegion(c *gin.Context) string {
	v := c.Value(awsRegionKey)
	if v == nil {
		return ""
	}
	region, ok := v.(string)
	if !ok {
		return ""
	}
	return region
}

func APITokenConfigMW(config map[string]any) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(apiTokenConfigKey, config)
		c.Next()
	}
}

func GetAPITokenConfig(c *gin.Context) map[string]any {
	v := c.Value(apiTokenConfigKey)
	if v == nil {
		return map[string]any{}
	}
	m, ok := v.(map[string]any)
	if !ok {
		return map[string]any{}
	}
	return m
}

func SetLocalize(loc *localize.Localize) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(localizeKey, loc)
		c.Next()
	}
}

func GetLocalize(c *gin.Context) *localize.Localize {
	v := c.Value(localizeKey)
	if v == nil {
		return nil
	}
	m, ok := v.(*localize.Localize)
	if ok {
		return m
	}
	return nil
}
