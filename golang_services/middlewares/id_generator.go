package middlewares

import (
	"context"
	"fmt"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"

	"bitbucket.org/persistence17/aria/golang_services/sdk/idgenerator"
)

const (
	paymentIDGen = "payment_id_generator"
)

var (
	paymentTransactionNamespace = uuid.NewV3(uuid.NamespaceURL, "aria_payment_transaction")
)

func GetTransactionIDGen(c context.Context) (*idgenerator.AriaPaymentTransactionIDGenerator, error) {
	v := c.Value(paymentIDGen)
	if v == nil {
		return nil, fmt.Errorf("missing transation ID generator")
	}

	idg, ok := v.(*idgenerator.AriaPaymentTransactionIDGenerator)
	if !ok {
		return nil, fmt.Errorf("cannot cast to ID generator")
	}
	return idg, nil
}

func IDGeneratorMW() gin.HandlerFunc {
	paymentIDGenerator := idgenerator.NewAriaPaymentTransactionIDGenerator(paymentTransactionNamespace)
	return func(c *gin.Context) {
		c.Set(paymentIDGen, paymentIDGenerator)
		c.Next()
	}
}
