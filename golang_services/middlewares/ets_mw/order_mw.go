package ets_mw

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

const (
	orderKey       = "ets-order"
	orderDetailKey = "ets--detail-order"
)

func EtsOrderAccessControlMW(allowedRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		orderID := c.Param("order-id")
		if orderID == "" {
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
				"success": false,
			})
			return
		}
		_, userID, _ := utils.GetOrgIDAndUserID(c)
		d, err := middlewares.GetVisaDao(c)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":   err,
				"success": false,
			})
			return
		}

		dao := db.NewEts<PERSON>ao(d.DB().Db)

		existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
			ID:           []string{orderID},
			IncludeTasks: true,
			Limit:        1,
		})
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":   err,
				"success": false,
			})
			return
		}
		if len(existing.Data) != 1 || existing.Data[0] == nil {
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
				"success": false,
			})
			return
		}

		// if the user is the creator of the package, it has full access to the order, don't need to check role
		if existing.Data[0].UserID == userID {
			c.Set(orderKey, existing.Data[0])
			c.Next()
			return
		}

		// check if the user has the right role to access the order
		if hasRightRole(middlewares.GetUserRoles(c), allowedRoles) {
			// if yes, set package into context and go to next
			c.Set(orderKey, existing.Data[0])
			c.Next()
			return
		} else {
			// if no, return not found
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
				"success": false,
			})
			return
		}
	}
}

func hasRightRole(roles []string, allowedRoles []string) bool {
	allowed := map[string]bool{}
	for _, r := range allowedRoles {
		allowed[r] = true
	}
	for _, r := range roles {
		if allowed[r] {
			return true
		}
	}
	return false
}

func GetEtsOrderFromCtx(c *gin.Context) *models.ServiceOrderDetail {
	v := c.Value(orderKey)
	if v == nil {
		return nil
	}

	o, ok := v.(*models.ServiceOrderDetail)
	if !ok {
		return nil
	}
	return o
}

func GetEtsOrderDetailFromCtx(c *gin.Context) *models.ServiceOrderDetail {
	v := c.Value(orderDetailKey)
	if v == nil {
		return nil
	}

	o, ok := v.(*models.ServiceOrderDetail)
	if !ok {
		return nil
	}
	return o
}

func EtsOrderBookingAppointmentMW() gin.HandlerFunc {
	return func(c *gin.Context) {
		orderID := c.Param("order-id")
		if orderID == "" {
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
				"success": false,
			})
			return
		}
		d, err := middlewares.GetVisaDao(c)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":   err,
				"success": false,
			})
			return
		}
		dao := db.NewEtsDao(d.DB().Db)
		existing, err := dao.QueryServiceOrders(models.ServiceOrderFilter{
			ID:           []string{orderID},
			IncludeTasks: true,
			Limit:        1,
		})
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":   err,
				"success": false,
			})
			return
		}
		if len(existing.Data) != 1 || existing.Data[0] == nil {
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
				"success": false,
			})
			return
		}

		service := existing.Data[0].Service
		if !(service.Country == "USA" && service.ServiceType == "passport" && service.Tasks[0] == "new") {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"success": false,
			})
			return
		} else {
			c.Set(orderDetailKey, existing.Data[0])
			c.Next()
			return
		}
	}
}
