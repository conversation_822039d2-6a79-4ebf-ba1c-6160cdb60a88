package middlewares

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

const (
	orgIDKey = "org-id"
)

func OrgCheckerMW(c *gin.Context) {
	org := c.Param("org-id")
	if org == "" {
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	orgID, err := strconv.ParseInt(org, 10, 64)
	if err != nil {
		c.AbortWithStatus(http.StatusBadRequest)
		return
	}
	user := GetAuthInfo(c)
	if user == nil || user.OrgID.Int64 != orgID {
		c.AbortWithStatus(http.StatusUnauthorized)
		return
	}
	c.Set(orgIDKey, int(orgID))
	c.Next()
}
