package middlewares

import (
	"context"
	"log"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

func SetRedis(address string) gin.HandlerFunc {
	return func(c *gin.Context) {
		rdb := redis.NewClient(&redis.Options{
			Addr:     "**************:6379",
			Password: "AD@Redis",
			DB:       0,
		})

		// Test Redis connection
		ctx := context.Background()
		_, err := rdb.Ping(ctx).Result()
		if err != nil {
			log.Fatal("Failed to connect to Redis:", err)
		}

		c.Set("redis", rdb)
		c.Next()
	}
}

func GetRedis(c *gin.Context) *redis.Client {
	v := c.Value("redis")
	if v == nil {
		return nil
	}
	dao, ok := v.(*redis.Client)
	if !ok {
		return nil
	}
	return dao
}
