package middlewares

import (
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/sdk/aws"
)

const (
	Presigner = "s3-presigner"
)

func NewS3PresignerMW(sess *session.Session) gin.HandlerFunc {
	presigner := aws.NewS3Svc(sess)
	return func(c *gin.Context) {
		c.Set(Presigner, presigner)
		c.Next()
	}
}

func GetS3Presigner(c *gin.Context) *aws.S3Svc {
	v := c.Value(Presigner)
	if v == nil {
		return nil
	}
	ps, ok := v.(*aws.S3Svc)
	if !ok {
		return nil
	}
	return ps
}
