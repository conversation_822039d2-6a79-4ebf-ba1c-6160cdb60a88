package middlewares

import (
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/textract"
	"github.com/gin-gonic/gin"
)

const (
	textExtract = "aws-textextract"
)

func NewTextExtract(sess *session.Session) gin.HandlerFunc {
	svc := textract.New(sess, aws.NewConfig().WithRegion("us-west-2")) // text extract only support some regions
	return func(c *gin.Context) {
		c.Set(textExtract, svc)
		c.Next()
	}
}

func GetTextExtract(c *gin.Context) *textract.Textract {
	v := c.Value(textExtract)
	if v == nil {
		return nil
	}
	tx, ok := v.(*textract.Textract)
	if !ok {
		return nil
	}
	return tx
}
