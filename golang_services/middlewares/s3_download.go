package middlewares

import (
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/gin-gonic/gin"

	"bitbucket.org/persistence17/aria/golang_services/sdk/aws"
)

const (
	Downloader = "s3-downloader"
)

func NewS3DownloaderMW(sess *session.Session) gin.HandlerFunc {
	presigner := aws.NewDownloader(sess)
	return func(c *gin.Context) {
		c.Set(Downloader, presigner)
		c.Next()
	}
}

func GetS3Downloader(c *gin.Context) *aws.S3Downloader {
	v := c.Value(Downloader)
	if v == nil {
		return nil
	}
	ps, ok := v.(*aws.S3Downloader)
	if !ok {
		return nil
	}
	return ps
}
