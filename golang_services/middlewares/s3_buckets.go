package middlewares

import (
	"github.com/gin-gonic/gin"
)

const (
	s3Buckets = "s3-buckets"
)

func NewS3BucketsMW(buckets map[string]any) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(s3Buckets, buckets)
		c.Next()
	}
}

func GetS3Buckets(c *gin.Context) map[string]any {
	v := c.Value(s3Buckets)
	if v == nil {
		return nil
	}
	m, ok := v.(map[string]any)
	if !ok {
		return nil
	}
	return m
}
