package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"github.com/lib/pq"
)

type UserCorpInfo struct {
	UserID             string         `json:"user_id" db:"user_id"`
	CorporationName    string         `json:"corporation_name" db:"corporation_name"`
	CorporationAddress *CorpAddress   `json:"corporation_address" db:"corporation_address"`
	Phone              string         `json:"phone" db:"phone"`
	ManagerID          *string        `json:"manager_id" db:"manager_id"`
	Manager            *ContactPerson `json:"manager"`
	HRContactID        *string        `json:"hr_contact_id" db:"hr_contact_id"`
	HRContact          *ContactPerson `json:"hr_contact"`
	DepartmentID       *int           `json:"department_id" db:"department_id"`
	DepartmentName     *string        `json:"department_name"`
	CostCenterID       *int           `json:"cost_center_id" db:"cost_center_id"`
	CostCenterName     *string        `json:"cost_center_name" db:"cost_center_name"`
	CorporationCode    string         `json:"corporation_code" db:"corporation_code"`
	ApprovedCountries  pq.StringArray `json:"approved_countries" db:"approved_countries"`
	CreatedAt          time.Time      `json:"created_at" db:"created_at"`
	UpdatedAt          *time.Time     `json:"updated_at" db:"updated_at"`
}

type ContactPerson struct {
	Name        string `json:"name"`
	Email       string `json:"email"`
	PhoneNumber string `json:"phone_number"`
}

func (c ContactPerson) Value() (driver.Value, error) {
	j, err := json.Marshal(c)
	return j, err
}

func (c *ContactPerson) Scan(src any) error {
	v := reflect.ValueOf(src)
	if !v.IsValid() || v.IsNil() {
		return nil
	}

	source, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("Type assertion .([]byte) failed.")
	}

	err := json.Unmarshal(source, c)
	if err != nil {
		return err
	}

	return nil
}

type CorpAddress struct {
	Country string `json:"country"`
	Address string `json:"address"`
	State   string `json:"state"`
	City    string `json:"city"`
	ZipCode string `json:"zip_code"`
}

func (c CorpAddress) Value() (driver.Value, error) {
	j, err := json.Marshal(c)
	return j, err
}

func (c *CorpAddress) Scan(src any) error {
	v := reflect.ValueOf(src)
	if !v.IsValid() || v.IsNil() {
		return nil
	}

	source, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("Type assertion .([]byte) failed.")
	}

	err := json.Unmarshal(source, c)
	if err != nil {
		return err
	}

	return nil
}
