package models

import (
	"time"

	"github.com/lib/pq"
	"gopkg.in/guregu/null.v3"
)

type Consulate struct {
	ID                   string        `json:"id" db:"id"`
	Status               string        `json:"status" db:"status"`
	Name                 string        `json:"name" db:"name"`
	Email                null.String   `json:"email" db:"email"`
	SecondaryEmail       null.String   `json:"secondary_email" db:"secondary_email"`
	Country              string        `json:"country" db:"country"`
	Address              *Address      `json:"address" db:"address"`
	Contact              *Contact      `json:"contact" db:"contact"`
	SecondaryContact     *Contact      `json:"secondary_contact" db:"secondary_contact"`
	TimeZoneName         string        `json:"timezone_name" db:"timezone_name"`
	SupportPickup        null.Bool     `json:"support_pickup" db:"support_pickup"`
	SupportShipping      null.Bool     `json:"support_shipping" db:"support_shipping"`
	SupportedVisaProduct pq.Int64Array `json:"supported_visa_product" db:"supported_visa_product"`
	Website              null.String   `json:"website" db:"website"`
	ServedCountries      null.String   `json:"served_countries" db:"served_countries"`
	ServedArea           null.String   `json:"served_area" db:"served_area"`
	OrgID                int64         `json:"org_id" db:"org_id"`
	CreatedAt            time.Time     `json:"created_at" db:"created_at"`
	UpdatedAt            *time.Time    `json:"updated_at" db:"updated_at"`
}

type ConsulateWithDetails struct {
	*Consulate
	AddressInfo *Address `json:"address_info,omitempty"`
	ContactInfo *Contact `json:"contact_info,omitempty"`
}
