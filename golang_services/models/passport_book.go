package models

import (
	"time"

	"gopkg.in/guregu/null.v3"
)

type PassportBook struct {
	ID                      int         `json:"id" db:"id"`
	UserID                  string      `json:"user_id" db:"user_id"`
	TravelerID              string      `json:"traveler_id" db:"traveler_id"`
	Surname                 string      `json:"surname" db:"surname"`
	GivenName               string      `json:"given_name" db:"given_name"`
	MiddleName              string      `json:"middle_name" db:"middle_name"`
	Gender                  string      `json:"gender" db:"gender"`
	Nationality             string      `json:"nationality" db:"nationality"`
	DateOfBirth             *time.Time  `json:"date_of_birth" db:"date_of_birth"`
	ExpirationDate          *time.Time  `json:"expiration_date" db:"expiration_date"`
	IssueDate               *time.Time  `json:"issue_date" db:"issue_date"`
	CountryOfBirth          string      `json:"country_of_birth" db:"country_of_birth"`
	CityOfBirth             string      `json:"city_of_birth" db:"city_of_birth"`
	CountryIssuingAuthority string      `json:"country_issuing_authority" db:"country_issuing_authority"`
	IsPrimary               bool        `json:"is_primary" db:"is_primary"`
	RemindRenewEnabled      bool        `json:"remind_renew_enabled" db:"remind_renew_enabled"`
	RemindRenewEmail        string      `json:"remind_renew_email" db:"remind_renew_email"`
	LastRemind              *time.Time  `json:"last_remind" db:"last_remind"`
	CreatedAt               *time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt               *time.Time  `json:"updated_at" db:"updated_at"`
	PassportImage           null.String `json:"passport_image" db:"passport_image"`
}
