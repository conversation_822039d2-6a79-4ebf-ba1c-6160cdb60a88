package models

import "time"

const (
	DocumentTypeGenerating = documentType("generating")
	DocumentTypeGenerated  = documentType("generated")
)

type documentType string
type Document struct {
	ID        int          `json:"id" db:"id"`
	Type      string       `json:"type" db:"type"`
	UserID    string       `json:"user_id" db:"user_id"`
	PackageID int          `json:"package_id" db:"package_id"`
	Template  string       `json:"template" db:"template"`
	Input     *PropertyMap `json:"input" db:"input"`
	Status    documentType `json:"status" db:"status"`
	Output    *FileMap     `json:"output" db:"output"`
	CreatedAt *time.Time   `json:"created_at" db:"created_at"`
	UpdatedAt *time.Time   `json:"updated_at" db:"updated_at"`
}
