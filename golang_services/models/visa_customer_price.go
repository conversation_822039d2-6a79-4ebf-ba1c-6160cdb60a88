package models

import (
	"bitbucket.org/persistence17/aria/golang_services/sdk/types"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/tidwall/gjson"
	"gopkg.in/guregu/null.v3"
)

type VisaCustomerPrice struct {
	ID             int              `json:"id" db:"id"`
	Status         string           `json:"status" db:"status"`
	VisaProductID  int              `json:"visa_product_id" db:"visa_product_id"`
	OrganizationID null.Int         `json:"organization_id" db:"organization_id"`
	ConsulateID    string           `json:"consulate_id" db:"consulate_id"`
	Currency       string           `json:"currency" db:"currency"`
	Price          float64          `json:"price" db:"price"`
	AdditionalFee  *PropertyMap     `json:"additional_fee" db:"additional_fee"`
	Shipments      *PropertyMap     `json:"shipments" db:"shipments"`
	Discount       null.Float       `json:"discount" db:"discount"`
	CreatedAt      types.TimeStamp  `json:"created_at" db:"created_at"`
	UpdatedAt      *types.TimeStamp `json:"updated_at" db:"updated_at"`
	Amount         float64          `json:"amount"`
}

func (p VisaCustomerPrice) GetCalculatePrice() *BasePrice {
	basePrice := BasePrice{
		UnitPrice:     p.Price,
		AdditionalFee: p.AdditionalFee,
		Shipments:     p.Shipments,
		Currency:      p.Currency,
	}

	basePrice.SubTotal = basePrice.UnitPrice

	utils.StructToJSON(p.AdditionalFee).ForEach(func(key, value gjson.Result) bool {
		basePrice.SubTotal += value.Float()
		if basePrice.OriginalPrice != nil {
			*basePrice.OriginalPrice += value.Float()
		}
		return true
	})

	return &basePrice
}
