package models

import (
	"github.com/thoas/go-funk"
	"gorm.io/datatypes"
)

const (
	PackageStatusOpen                     = packageStatus("open")
	PackageStatusProcessing               = packageStatus("in_process")
	PackageStatusReady                    = packageStatus("ready")
	PackageStatusPrePaid                  = packageStatus("prepaid")
	PackageStatusPaid                     = packageStatus("paid")
	PackageStatusWaitingShipmentUser      = packageStatus("waiting_shipment_user")
	PackageStatusCreatedShipmentUser      = packageStatus("created_shipment_user")
	PackageStatusSentShipmentUser         = packageStatus("sent_shipment_user")
	PackageStatusReceivedShipmentUser     = packageStatus("received_user_document")
	PackageStatusWaitingShipmentConsulate = packageStatus("waiting_shipment_consulate")
	PackageStatusReadySubmit              = packageStatus("ready_to_submit")
	PackageStatusLock                     = packageStatus("lock")
	PackageStatusSubmitted                = packageStatus("submitted")
	PackageStatusConfirmed                = packageStatus("confirmed")
	PackageStatusProceed                  = packageStatus("proceed")
	PackageStatusPreviewed                = packageStatus("previewed")
	PackageStatusCompleted                = packageStatus("completed")
	PackageStatusWaitingPayment           = packageStatus("manual_payment")
	PackageStatusSentVisaPackage          = packageStatus("sent_visa_package")
	PackageStatusNeedMoreDocument         = packageStatus("need_more_document")
	PackageStatusCancelled                = packageStatus("cancelled")
)

type packageStatus string

func GetPackageStatusesForAdmin() []string {
	return []string{
		string(PackageStatusPrePaid),
		string(PackageStatusPaid),
		string(PackageStatusWaitingShipmentUser),
		string(PackageStatusCreatedShipmentUser),
		string(PackageStatusSentShipmentUser),
		string(PackageStatusReceivedShipmentUser),
		string(PackageStatusWaitingShipmentConsulate),
		string(PackageStatusReadySubmit),
		string(PackageStatusLock),
		string(PackageStatusWaitingPayment),
		string(PackageStatusSubmitted),
		string(PackageStatusConfirmed),
		string(PackageStatusProceed),
		string(PackageStatusPreviewed),
		string(PackageStatusCompleted),
		string(PackageStatusSentVisaPackage),
		string(PackageStatusNeedMoreDocument),
	}
}

func GetPackageStatusesForConsulate() []string {
	return []string{
		string(PackageStatusSubmitted),
		string(PackageStatusConfirmed),
		string(PackageStatusProceed),
		string(PackageStatusPreviewed),
		string(PackageStatusCompleted),
		string(PackageStatusNeedMoreDocument),
	}
}

// PackageListFilterRequest package list filter request
// type PackageListFilterRequest struct {
// 	IDs                         any `form:"-"`
// 	OrgID                       int64       `form:"-"`
// 	UserID                      string      `form:"-"`
// 	IsADAdmin                   bool        `form:"-"`
// 	IncludeVisaProduct          bool        `form:"-"` // For admin
// 	IncludeApplications         bool        `form:"include_apps"`
// 	IncludeCreator              bool        `form:"include_creator"`
// 	HistoryOnly                 bool        `form:"history_only"`
// 	OrderNumber                 string      `form:"order_number"`
// 	Destination                 []string    `form:"destination"` // VNM,CHN,IND,...
// 	Status                      []string    `form:"status"`
// 	VisaType                    []string    `form:"visa_type"`
// 	NumberOfEntries             []string    `form:"number_of_entries"`
// 	Validity                    []string    `form:"validity"`
// 	Purpose                     []string    `form:"purpose"`
// 	ConsulateID                 string      `form:"consulate_id"`
// 	PackageID                   int64       `form:"package_id"`
// 	Limit                       uint64      `form:"limit"`
// 	Offset                      uint64      `form:"offset"`
// 	SortField                   string      `form:"sort_field"`
// 	SortOrder                   string      `form:"sort_order"`
// 	CreatedAtGte                string      `form:"created_at[gte]"`
// 	CreatedAtLte                string      `form:"created_at[lte]"`
// 	UpdatedAtGte                string      `form:"updated_at[gte]"`
// 	UpdatedAtLte                string      `form:"updated_at[lte]"`
// 	OrderTimeGte                string      `form:"order_time[gte]"`
// 	OrderTimeLte                string      `form:"order_time[lte]"`
// 	CompletedTimeGte            string      `form:"completed_time[gte]"`
// 	CompletedTimeLte            string      `form:"completed_time[lte]"`
// 	Query                       string      `form:"query"`
// 	FilterField                 []string    `form:"filter_field"`
// 	FilterValue                 string      `form:"filter_value"`
// 	CountryOrderList            []string
// 	ADAdmin                     bool
// 	SkipPagination              bool
// 	IncludeOutputFileBeforePaid bool
// }

// Format format request input
// func (req *PackageListFilterRequest) Format() {
// 	if req.Limit == 0 {
// 		req.Limit = 10
// 	}

// 	if req.SortField = convertPackageField(req.SortField); req.SortField == "" {
// 		req.SortField = "COALESCE(p.updated_at,p.created_at)"
// 	}

// 	if req.SortField == "p.tracking_times->>'completed'" {
// 		req.SortField = "COALESCE(DATE (p.tracking_times->>'completed'), p.completed_time)"
// 	}

// 	var filter = []string{}
// 	for i := range req.FilterField {
// 		if val := convertPackageField(req.FilterField[i]); val != "" {
// 			filter = append(filter, val)
// 		}
// 	}
// 	req.FilterField = filter

// 	if strings.ToLower(req.SortOrder) != "asc" {
// 		req.SortOrder = "desc"
// 	}
// }

func convertPackageField(field string) string {
	switch field {
	case "order_number", "package_id":
		field = "p.id"
	case "destination":
		field = "p.config->'destination'"
	case "status":
		field = "p.status"
	case "visa_type":
		field = "vp.issue_method"
	case "consulate_id":
		field = "p.consulate_id"
	case "purpose":
		field = "vp.purpose"
	case "number_of_entries":
		field = "vp.attributes->>'number_of_entries'"
	case "validity":
		field = "vp.attributes->>'validity'"
	case "updated_at":
		field = "p.updated_at"
	case "total":
		field = "p.summary->>'total'"
	case "order_time", "received_time":
		field = "p.order_time"
	case "submitted_time":
		field = "p.submitted_time"
	case "approved_time":
		field = "p.tracking_times->>'previewed'"
	case "completed_time":
		field = "p.tracking_times->>'completed'"
	default:
		field = ""
	}
	return field
}

// // PackageListResponse payment list response
// type PackageListResponse struct {
// 	Data       []PackageItemDetail `json:"data"`
// 	Limit      uint64              `json:"limit"`
// 	Offset     uint64              `json:"offset"`
// 	Success    bool                `json:"success"`
// 	TotalCount int                 `json:"total_count"`
// }

// PackageItemDetail package item detail
// type PackageItemDetail struct {
// 	*Package
// 	Shipment          *json.RawMessage `json:"shipment,omitempty" db:"shipment"`
// 	Payment           *json.RawMessage `json:"payment,omitempty" db:"payment"`
// 	Cart              *json.RawMessage `json:"cart,omitempty" db:"cart"`
// 	CurrentCart       *json.RawMessage `json:"current_cart" db:"current_cart"`
// 	Applications      json.RawMessage  `json:"applications,omitempty" db:"applications"`
// 	NumOfApplications int              `json:"num_of_applications,omitempty" db:"num_of_applications"`
// 	ItemIDInCart      *int             `json:"item_id_in_cart" db:"item_id_in_cart"`
// 	VisaPrices        json.RawMessage  `json:"-" db:"visa_prices"`
// 	VisaLowestPrice   *BasePrice       `json:"last_price" db:"-"`
// 	Creator           json.RawMessage  `json:"creator" db:"creator"`
// 	Provider          *json.RawMessage `json:"provider" db:"provider"`
// 	VisaProduct       *json.RawMessage `json:"visa_product" db:"visa_product"`
// 	ActionData        datatypes.JSON   `json:"action_data" db:"action_data"`
// 	UserFeedback      datatypes.JSON   `json:"user_feedback" db:"user_feedback"`
// }

type BasePrice struct {
	UnitPrice          float64        `json:"unit_price"`
	OriginalPrice      *float64       `json:"original_price"`
	AdditionalFee      *PropertyMap   `json:"additional_fee"`
	Shipments          *PropertyMap   `json:"shipments,omitempty"`
	SubTotal           float64        `json:"sub_total"`
	Currency           string         `json:"currency"`
	PriceModifiedRules datatypes.JSON `json:"price_modified_rules"`
	AdditionalServices datatypes.JSON `json:"additional_services"`
}

// func (p *PackageItemDetail) IsEnoughTimeToProcess(nowDate time.Time) (bool, error) {
// 	buff, err := json.Marshal(p)
// 	if err != nil {
// 		return false, err
// 	}

// 	processingTime := gjson.ParseBytes(buff).Get("config.visa_product.attributes.processing_time").String()

// 	entryDate := gjson.ParseBytes(buff).Get("config.entry_date").Time().Truncate(time.Hour * 24).Add(time.Hour*24 - time.Millisecond)
// 	minEntryDate, err := time_util.CalculateNormalDays(nowDate, processingTime)
// 	if err != nil {
// 		return false, err
// 	}
// 	if issueMethod := utils.StructToJSON(p.VisaProduct).Get("issue_method").String(); issueMethod == "voa" {
// 		minEntryDate, err = time_util.CalculateNormalDays(nowDate, processingTime)
// 		if err != nil {
// 			return false, err
// 		}
// 	}

// 	if minEntryDate.After(entryDate) {
// 		return false, nil
// 	}

// 	return true, nil
// }

// func (p *PackageItemDetail) GetAppName() []string {
// 	apps := []string{}
// 	for _, app := range gjson.ParseBytes(p.Applications).Array() {
// 		var givenName, middleName, surName string
// 		for _, pod := range app.Get("input_pods").Array() {
// 			if pod.Get("category").String() == "passport" && pod.Get("sub_category").String() == "core_info" {
// 				if pod.Get("name").String() == "given_name" {
// 					givenName = pod.Get("value.fe").String()
// 				}
// 				if pod.Get("name").String() == "middle_name" {
// 					middleName = pod.Get("value.fe").String()
// 				}
// 				if pod.Get("name").String() == "surname" {
// 					surName = pod.Get("value.fe").String()
// 				}
// 			}

// 		}
// 		names := []string{}
// 		if givenName != "" {
// 			names = append(names, givenName)
// 		}
// 		if middleName != "" {
// 			names = append(names, middleName)
// 		}
// 		if surName != "" {
// 			names = append(names, surName)
// 		}
// 		apps = append(apps, strings.Join(names, " "))
// 	}
// 	return apps
// }

func CheckStatusReturnOutputFile(status packageStatus) bool {
	return !funk.Contains([]packageStatus{PackageStatusOpen, PackageStatusProcessing, PackageStatusReady, PackageStatusWaitingPayment}, status)
}
