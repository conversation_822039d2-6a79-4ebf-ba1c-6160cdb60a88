package models

import (
	"time"
)

const (
	PaymentStatusOpen    = "open"
	PaymentStatusPending = "pending"
	PaymentStatusSuccess = "success"
	PaymentStatusFailed  = "failed"
	PaymentStatusClosed  = "closed"
)

type Payment struct {
	ID                    string       `json:"id" db:"id"`
	Status                string       `json:"status" db:"status"`
	ExternalTransactionID string       `json:"external_transaction_id,omitempty" db:"external_transaction_id"`
	Method                string       `json:"method" db:"method"`
	MethodDescription     string       `json:"method_desc" db:"method_desc"`
	Currency              string       `json:"currency" db:"currency"`
	Count                 int          `json:"count" db:"count"`
	UnitPrice             float64      `json:"unit_price" db:"unit_price"`
	TaxRate               float64      `json:"tax_rate" db:"tax_rate"`
	Discount              float64      `json:"discount" db:"discount"`
	Total                 float64      `json:"total" db:"total"`
	Invoice               string       `json:"invoice,omitempty" db:"invoice"`
	PromotionCode         string       `json:"promotion_code,omitempty" db:"promotion_code"`
	Description           string       `json:"description"`
	Properties            *PropertyMap `json:"properties,omitempty" db:"properties"`
	ReceiptID             string       `json:"receipt_id" db:"receipt_id"`
	ReceiptFile           string       `json:"receipt_file" db:"receipt_file"`
	CreatedAt             time.Time    `json:"created_at" db:"created_at"`
	UpdatedAt             time.Time    `json:"updated_at,omitempty" db:"updated_at"`
}

func (p *Payment) ToMap() map[string]any {
	return map[string]any{
		"method_desc":             p.MethodDescription,
		"currency":                p.Currency,
		"count":                   p.Count,
		"unit_price":              p.UnitPrice,
		"tax_rate":                p.TaxRate,
		"discount":                p.Discount,
		"invoice":                 p.Invoice,
		"promotion_code":          p.PromotionCode,
		"description":             p.Description,
		"id":                      p.ID,
		"status":                  p.Status,
		"external_transaction_id": p.ExternalTransactionID,
		"method":                  p.Method,
		"total":                   p.Total,
		"properties":              p.Properties,
		"created_at":              p.CreatedAt,
		"updated_at":              p.UpdatedAt,
	}
}

// PaymentListRequest payment list request
type PaymentListRequest struct {
	PaymentMethod []string `form:"method"`
	Status        []string `form:"status"`
	FromDate      *time.Time
	ToDate        *time.Time
	GetAllItems   bool   `form:"get_all_items"`
	Limit         uint64 `form:"limit"`
	Offset        uint64 `form:"offset"`
}

// PaymentManual payment manual
type PaymentManual struct {
	ConfirmationCode string  `json:"confirmation_code"`
	Method           string  `json:"method"`
	Amount           float64 `json:"amount" validate:"required"`
	Note             string  `json:"note"`
	Currency         string  `json:"currency"`
	Status           string  `json:"status"`
	ForcePaid        bool    `json:"force_paid"`
}

// RemainAmount remain amount
type RemainAmount struct {
	Total       float64  `json:"total"`
	Paid        float64  `json:"paid"`
	Transaction *Payment `json:"transaction"`
	Remain      float64  `json:"remain"`
}
