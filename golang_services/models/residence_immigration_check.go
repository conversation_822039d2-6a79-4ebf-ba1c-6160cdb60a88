package models

type ResidenceImmigrationCheck struct {
	Nationality       string `json:"nationality" db:"nationality"`
	RegionOfResidence string `json:"region_of_residence" db:"region_of_residence"`
	Destination       string `json:"destination" db:"destination"`
	Document          string `json:"document" db:"document"`
	Status            string `json:"status" db:"status"`
	LengthOfStay      int    `json:"length_of_stay" db:"length_of_stay"`
}
