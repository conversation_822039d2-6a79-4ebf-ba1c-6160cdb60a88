package models

import "time"

type EmailTemplate struct {
	ID                 int        `json:"id" db:"id"`
	Name               string     `json:"name" db:"name"`
	EmailType          string     `json:"email_type" db:"email_type"`
	Language           string     `json:"language" db:"language"`
	From               string     `json:"from" db:"from"`
	Title              string     `json:"title" db:"title"`
	Parameters         []uint8    `json:"parameters" db:"parameters"`
	HTMLBody           string     `json:"htmlbody" db:"htmlbody"`
	TextBody           string     `json:"textbody" db:"textbody"`
	SaveAsNotification bool       `json:"save_as_notification" db:"save_as_notification"`
	Active             bool       `json:"active" db:"active"`
	CreatedAt          *time.Time `json:"created_at" db:"created_at"`
	UpdatedAt          *time.Time `json:"updated_at" db:"updated_at"`
}
