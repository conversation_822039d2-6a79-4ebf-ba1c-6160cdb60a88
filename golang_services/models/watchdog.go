package models

import (
	"encoding/json"
	"time"
)

// RefTable ref table
var RefTable = struct{ Package, ServiceOrder, ServiceTask, Application, Applications, Payment, User, PassportBook, VisaBook string }{
	"package", "service_orders", "service_tasks", "application", "applications", "payment", "users", "passport_book", "visa_book",
}

// WatchdogEvent watch dog event
var WatchdogEvent = struct {
	PaymentPendingTooLong, PaymentSuccessUpdatePod,
	ETSOrderWarningDelete, ETSOrderDeleted, ETSOrderReminderToProvider, ETSOrderFastlaneReminderToProvider3Days, ETSOrderFastlaneReminderToProvider1Day,
	ETSOrderFastlaneReminderUpdateProviderInfo, ETSOrderPassportDelivery,
	ETSOrderUnderReviewXDays, ETSOrderRemindUpdateMissingDocument, ETSOrderSendFeedbackForCompletedOrder,
	ETSOrderSendFastlaneRemind1, ETSOrderSendFastlaneRemind2, ETSOrderSendFastlaneRemindWhatsApp, ETSOrderSendFastlaneJoinWhatsApp,
	ETSOrderSendUrgentEvisaRemind,
	PackageWarningDelete, PackageDeleted, PackageSendNotification, PackageClosedDueEntryDate,
	PackageReminderToConsulate,
	ApplicationUnderReview,
	PassportExpiredWarning, VisaExpiredWarning,
	UserLockWarning, UserAccountLocked string
}{
	"payment_pending_too_long", "payment_success_update_pod",
	"ets_order_warning_delete", "ets_order_deleted", "ets_order_reminder_to_provider", "ets_order_fastlane_reminder_to_provider_3_days", "ets_order_fastlane_reminder_to_provider_1_day",
	"ets_order_fastlane_update_provider_info", "ets_order_passport_delivery",
	"ets_order_under_review_x_days", "ets_order_remind_update_missing_document", "ets_order_send_feedback_for_completed_order",
	"ets_order_send_fastlane_remind_1", "ets_order_send_fastlane_remind_2", "ets_order_send_fastlane_remind_whats_app", "ets_order_send_fastlane_join_whats_app",
	"ets_order_send_urgent_evisa_remind",
	"package_warning_delete", "package_deleted", "package_send_notification", "package_closed_due_entry_date",
	"package_reminder_to_consulate",
	"application_under_review",
	"passport_expired_warning", "visa_expired_warning",
	"user_lock_warning", "user_account_locked",
}

var (
	JobNameDelayBeforeRemoveUserFromWhatsApp = "delay_before_remove_user_from_whatsapp"
	JobNameDelaySendEmail                    = "delay_send_email"
)

type WatchDogJob struct {
	ID          int64           `json:"id" db:"id" header:"id"`
	JobName     string          `json:"job_name" db:"job_name"`
	JobData     json.RawMessage `json:"job_data" db:"job_data"`
	ScheduleAt  time.Time       `json:"schedule_at" db:"schedule_at"`
	StartedAt   *time.Time      `json:"started_at" db:"started_at"`
	CompletedAt *time.Time      `json:"completed_at" db:"completed_at"`
	Status      string          `json:"status" db:"status"`
	Message     *string         `json:"message" db:"message"`
	CreatedAt   time.Time       `json:"created_at" db:"created_at" header:"created_at"`
	UpdatedAt   *time.Time      `json:"updated_at" db:"updated_at"`
}
