package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"github.com/pkg/errors"
)

type SpecialWorkingTime struct {
	Name          string     `json:"name" db:"name"`
	Location      string     `json:"location" db:"location"`
	WorkingHours  MapString  `json:"working_hours" db:"working_hours"`
	SpecialDayOff MapString  `json:"special_day_off" db:"special_day_off"`
	CreatedAt     time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt     *time.Time `json:"updated_at" db:"updated_at"`
}

type MapString map[string][]string

// Scan implement sql.Scan interface
func (l *MapString) Scan(src any) error {
	if src == nil {
		l = nil
		return nil
	}
	switch src := src.(type) {
	case []byte:
		return json.Unmarshal(src, &l)
	case string:
		return json.Unmarshal([]byte(src), &l)
	default:
		return errors.Errorf("Can't scan value type from %T", src)
	}
}

// Value MapString driver.Value interface
func (l MapString) Value() (driver.Value, error) {
	return json.Marshal(l)
}
