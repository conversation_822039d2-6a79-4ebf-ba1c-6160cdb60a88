package models

import (
	"time"

	"gopkg.in/guregu/null.v3"
)

type Agency struct {
	ID               string     `json:"id" db:"id"`
	Status           string     `json:"status" db:"status"`
	Name             string     `json:"name" db:"name"`
	Code             string     `json:"code" db:"code"`
	Country          string     `json:"country" db:"country"`
	Address          *Address   `json:"address" db:"address"`
	Contact          *Contact   `json:"contact" db:"contact"`
	SecondaryContact null.Int   `json:"secondary_contact" db:"secondary_contact"`
	TimeZoneName     string     `json:"timezone_name" db:"timezone_name"`
	CreatedAt        time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt        *time.Time `json:"updated_at" db:"updated_at"`
}
