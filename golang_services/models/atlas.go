package models

import (
	"time"
)

type AtlasTask struct {
	RequestID     string       `json:"request_id" db:"request_id"`
	ServiceType   string       `json:"service_type" db:"service_type"`
	ApplicationID any          `json:"application_id" db:"application_id"`
	RequestTs     *time.Time   `json:"request_ts" db:"request_ts"`
	ResultTs      *time.Time   `json:"result_ts" db:"result_ts"`
	Inputs        *PropertyMap `json:"inputs" db:"inputs"`
	Outputs       *PropertyMap `json:"outputs" db:"outputs"`
}

type AtlasValidation struct {
	RequestID         string       `json:"request_id" db:"request_id"`
	ServiceType       string       `json:"service_type" db:"service_type"`
	ValidationOrderID int          `json:"validation_order_id" db:"validation_order_id"`
	RequestTs         *time.Time   `json:"request_ts" db:"request_ts"`
	ResultTs          *time.Time   `json:"result_ts" db:"result_ts"`
	Inputs            *PropertyMap `json:"inputs" db:"inputs"`
	Outputs           *PropertyMap `json:"outputs" db:"outputs"`
}

type ETSAtlasTask struct {
	RequestID     string       `json:"request_id" db:"request_id"`
	ServiceType   string       `json:"service_type" db:"service_type"`
	ServiceTaskID any          `json:"service_task_id" db:"service_task_id"`
	RequestTs     *time.Time   `json:"request_ts" db:"request_ts"`
	ResultTs      *time.Time   `json:"result_ts" db:"result_ts"`
	Inputs        *PropertyMap `json:"inputs" db:"inputs"`
	Outputs       *PropertyMap `json:"outputs" db:"outputs"`
}
