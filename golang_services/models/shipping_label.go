package models

import (
	"time"

	"gopkg.in/guregu/null.v3"
)

const (
	ShippingLabelStatusCreated   = shippingLabelStatus("created")
	ShippingLabelStatusUsed      = shippingLabelStatus("used")
	ShippingLabelStatusCancelled = shippingLabelStatus("cancelled")
	ShippingLabelStatusDelivered = shippingLabelStatus("delivered")

	ShippingLabelCreatorConsulate = shippingLabelCreator("consulate")
	ShippingLabelCreatorAD        = shippingLabelCreator("AD")

	ShippingLabelNameForUser      = "user_to_consulate"
	ShippingLabelNameForConsulate = "consulate_to_user"
)

type shippingLabelStatus string
type shippingLabelCreator string

type ShippingLabel struct {
	ID             int                  `json:"id" db:"id"`
	VisaShipmentID string               `json:"visa_shipment_id" db:"visa_shipment_id"`
	TrackingNumber string               `json:"tracking_number" db:"tracking_number"`
	Currency       null.String          `json:"currency" db:"currency"`
	Price          null.Float           `json:"price" db:"price"`
	Label          null.String          `json:"label" db:"label"`
	Status         shippingLabelStatus  `json:"status" db:"status"`
	Creator        shippingLabelCreator `json:"creator" db:"creator"`
	ShipDate       *time.Time           `json:"ship_date" db:"ship_date"`
	CreatedAt      *time.Time           `json:"created_at" db:"created_at"`
	UpdatedAt      *time.Time           `json:"updated_at" db:"updated_at"`
}
