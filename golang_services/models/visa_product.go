package models

import (
	"time"

	"gopkg.in/guregu/null.v3"
)

const (
	VisaProductProcessTypeAuto             = "auto"
	VisaProductProcessTypePackageMail      = "package_mail"
	VisaProductProcessTypePackageInPerson  = "package_in_person"
	VisaProductProcessTypePackageInterview = "package_interview"
)

type VisaProductFilter struct {
	Title    string `json:"title"`
	Format   string `json:"format"`
	Optional bool   `json:"optional"`
	ID       string `json:"id"`
	OptionID []any  `json:"option_id,omitempty"`
}

type PriceObj struct {
	Amount        float32      `json:"amount"`
	VisaPrice     float32      `json:"visa_price"`
	AdditionalFee *PropertyMap `json:"additional_fee"`
	Currency      string       `json:"currency"`
}

type VisaProductAttribute struct {
	ID       string   `json:"id"`
	Title    string   `json:"title"`
	Format   string   `json:"format"`
	Optional bool     `json:"optional"`
	OptionID []string `json:"option_id"`
}

type ExtendedService struct {
	ID          int         `json:"id" db:"id"`
	Status      string      `json:"status" db:"status"`
	Name        string      `json:"name" db:"name"`
	Currency    string      `json:"currency" db:"currency"`
	Price       float64     `json:"price" db:"price"`
	Discount    float64     `json:"discount" db:"discount"`
	Description null.String `json:"description" db:"description"`
	CreatedAt   time.Time   `json:"-" db:"created_at"`
	UpdatedAt   *time.Time  `json:"-" db:"updated_at"`
}

type CountCommonProduct struct {
	Count       int                    `json:"count"`
	ServiceType string                 `json:"service_type"`
	Data        any                    `json:"data"`
	ETSProduct  *ExtendedTravelService `json:"ets_product"`
}
