package models

// type VisaOrder struct {
// 	ID                  int          `json:"id" db:"id"`
// 	VisaCountry         string       `json:"visa_country" db:"visa_country"`
// 	EntryDate           time.Time    `json:"entry_date" db:"entry_date"`
// 	ExitDate            *time.Time   `json:"exit_date" db:"exit_date"`
// 	VisaProduct         null.Int     `json:"visa_product" db:"visa_product"`
// 	VisaValidity        string       `json:"visa_validity" db:"visa_validity"`
// 	VisaNumberOfEntries string       `json:"visa_number_of_entries" db:"visa_number_of_entries"`
// 	VisaPurpose         string       `json:"visa_purpose" db:"visa_purpose"`
// 	VisaProcessingTime  string       `json:"visa_processing_time" db:"visa_processing_time"`
// 	Currency            null.String  `json:"currency" db:"currency"`
// 	TotalPrice          null.Float   `json:"total_price" db:"total_price"`
// 	DetailPrice         *PropertyMap `json:"detail_price" db:"detail_price"`
// }

// func VisaOrderToMap(v *VisaOrder) map[string]any {
// 	return map[string]any{
// 		"visa_country":           v.VisaCountry,
// 		"entry_date":             v.EntryDate,
// 		"exit_date":              v.ExitDate,
// 		"visa_product":           v.VisaProduct,
// 		"visa_validity":          v.VisaValidity,
// 		"visa_number_of_entries": v.VisaNumberOfEntries,
// 		"visa_purpose":           v.VisaPurpose,
// 		"visa_processing_time":   v.VisaProcessingTime,
// 	}
// }

// type VisaOrderExt struct {
// 	*VisaOrder
// 	VisaProductDetails *VisaProductResp `json:"visa_product_details,emitempty"`
// }
