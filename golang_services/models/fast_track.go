package models

import (
	"time"
)

type FastTrack struct {
	ID             int        `json:"id" db:"id"`
	UserID         string     `json:"user_id" db:"user_id"`
	Number         string     `json:"number" db:"number"`
	Type           string     `json:"type" db:"type"`
	ExpirationDate *time.Time `json:"expiration_date" db:"expiration_date"`
	CreatedAt      time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt      *time.Time `json:"updated_at" db:"updated_at"`
}
