package models

import (
	"time"

	"gopkg.in/guregu/null.v3"
)

type VisaBook struct {
	ID                 int         `json:"id" db:"id"`
	UserID             string      `json:"user_id" db:"user_id"`
	TravelerID         string      `json:"traveler_id" db:"traveler_id"`
	Country            string      `json:"country" db:"country"`
	IssueDate          *time.Time  `json:"issue_date" db:"issue_date"`
	ExpirationDate     *time.Time  `json:"expiration_date" db:"expiration_date"`
	NumberOfEntries    string      `json:"number_of_entries" db:"number_of_entries"`
	VisaType           string      `json:"visa_type" db:"visa_type"`
	RemindRenewEnabled bool        `json:"remind_renew_enabled" db:"remind_renew_enabled"`
	RemindRenewEmail   string      `json:"remind_renew_email" db:"remind_renew_email"`
	LastRemind         *time.Time  `json:"last_remind" db:"last_remind"`
	CreatedAt          time.Time   `json:"created_at" db:"created_at"`
	UpdatedAt          *time.Time  `json:"updated_at" db:"updated_at"`
	VisaImage          null.String `json:"visa_image" db:"visa_image"`
}
