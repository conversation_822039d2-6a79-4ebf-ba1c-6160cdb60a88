package models

import (
	"fmt"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/sdk/types"
	"gopkg.in/guregu/null.v3"
)

var (
	ProductType    = struct{ Visa, AirPortService string }{"VISA", "AIRPORT_SERVICES"}
	CartStatus     = struct{ Created, Checkout, Lock, Completed string }{"created", "checkout", "lock", "completed"}
	CartItemStatus = struct{ Created, ModifyLock string }{"created", "modify_lock"}
)

type Cart struct {
	ID        int            `json:"id" db:"id"`
	PaymentID null.String    `json:"payment_id" db:"payment_id"`
	IsCurrent bool           `json:"is_current" db:"is_current"`
	Status    string         `json:"status" db:"status"`
	UserID    string         `json:"user_id" db:"user_id"`
	CreatedAt *time.Time     `json:"created_at" db:"created_at"`
	UpdatedAt *time.Time     `json:"updated_at" db:"updated_at"`
	CartItems *types.ExtJSON `json:"-"`
}

type CartItem struct {
	ID            int                 `json:"id" db:"id"`
	CartID        int                 `json:"cart_id" db:"cart_id"`
	ProductType   string              `json:"product_type" db:"product_type"`
	ProductID     string              `json:"product_id" db:"product_id"`
	CreatedAt     *time.Time          `json:"created_at" db:"created_at"`
	UpdatedAt     *time.Time          `json:"updated_at" db:"updated_at"`
	Status        string              `json:"status" db:"status"`
	ServiceDetail *ServiceOrderDetail `json:"service_detail"`
	// VisaData      *PackageItemDetail  `json:"visa_data"`
}

type CartData struct {
	Cart            Cart       `json:"cart"`
	Payment         *Payment   `json:"payment"`
	ServiceData     []CartItem `json:"service_data"`
	CorporationName *string    `json:"corporation_name"`
}

func (c CartData) Total() int {
	return len(c.ServiceData)
}

func (c CartData) AcceptRePayment() (bool, error) {
	if c.Payment == nil {
		return false, fmt.Errorf("No payment info")
	}

	if c.Payment.Status == PaymentStatusPending && c.Cart.Status == CartStatus.Lock {
		return false, fmt.Errorf("User's transaction is pending")
	}

	return true, nil
}

func (c CartData) AcceptCancel() (bool, error) {
	if c.Payment == nil {
		return false, fmt.Errorf("No payment info")
	}

	// Can cancel after 24h without payment success
	if c.Payment.Status == PaymentStatusPending && c.Cart.Status == CartStatus.Lock &&
		c.Payment.CreatedAt.AddDate(0, 0, 1).After(time.Now()) {
		return false, fmt.Errorf("User's transaction is pending")
	}

	return true, nil
}

type CartSelection struct {
	PromotionCode       string     `json:"promotion_code"`
	Method              string     `json:"method"`
	Currency            string     `json:"currency"`
	ItemsSelected       []int      `json:"items_selected"`
	ItemsSkip           []CartItem `json:"-"`
	VisaDataSelected    []CartItem `json:"-"`
	ServiceDataSelected []CartItem `json:"-"`
}

func (c CartSelection) Includes(desc CartItem) bool {
	for _, item := range c.ItemsSelected {
		if item == desc.ID {
			return true
		}
	}
	return false
}

type CartShipment struct {
	CartItem CartItem     `json:"cart_item"`
	Shipment VisaShipment `json:"shipment"`
}

type CartVisaShipment struct {
	ID               int                          `json:"id"`
	ShippingServices []*ShippingServiceWithLabels `json:"shipping_services"`
	NeedShipping     bool                         `json:"need_shipping"`
	PayOnDelivery    bool                         `json:"pay_on_delivery"`
}

type CartFilter struct {
	CartID        int
	UserID        string
	PaymentID     string
	Status        []string
	IsCurrent     *bool
	Pagination    bool
	Query         string   `form:"query"` // Search by card_id, order number, passport number, surname, givenname, contact name, passenger name
	PaymentStatus []string `form:"payment_status"`
	Limit         uint64   `form:"limit"`
	Offset        uint64   `form:"offset"`
	SortField     string   `form:"sort_field"`
	SortOrder     string   `form:"sort_order"`
}

type CartFilterData struct {
	Data    []CartData `json:"data"`
	Success bool       `json:"success"`
	Total   int        `json:"total"`
}

type CartPaymentOptionFilter struct {
	OrgID              int
	Currency           string   `form:"currency"`
	RegionOfResidences []string `form:"region_of_residence"`
}

type CartItemValidationResult struct {
	ID      int    `json:"id"`
	IsValid bool   `json:"is_valid"`
	Message string `json:"message"`
}
