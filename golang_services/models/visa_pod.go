package models

import (
	"crypto/md5"
	"database/sql/driver"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	"gopkg.in/guregu/null.v3"
	"gorm.io/datatypes"
)

const (
	PodTypeInput  = "input"
	PodTypeOutPut = "output"
)

type Pod struct {
	ID               string                 `json:"id" db:"id"`
	IDExt            string                 `json:"_id" db:"_id"`
	Title            string                 `json:"title" db:"title"`
	Name             string                 `json:"name" db:"name"`
	Constraint       bool                   `json:"constraint"`
	ConstraintPods   datatypes.JSON         `json:"constraint_pods,omitempty"`
	Type             string                 `json:"type,omitempty"`
	TypeFE           string                 `json:"type_fe,omitempty"`
	Category         string                 `json:"category" db:"category"`
	SubCategory      string                 `json:"sub_category" db:"sub_category"`
	Default          any                    `json:"default,omitempty"`
	Description      string                 `json:"description,omitempty"`
	Help             string                 `json:"help,omitempty"`
	MinLength        int                    `json:"min_length,omitempty"`
	MaxLength        int                    `json:"max_length,omitempty"`
	Pattern          string                 `json:"pattern,omitempty"`
	RangeFrom        any                    `json:"range_from,omitempty"`
	RangeTo          any                    `json:"range_to,omitempty"`
	OptionList       []any                  `json:"option_list,omitempty"`
	OptionChoice     map[string][]*InputPod `json:"option_choice,omitempty"`
	DocumentType     string                 `json:"document_type,omitempty"`
	UI               string                 `json:"ui,omitempty"`
	Order            int                    `json:"order,omitempty" db:"order"`
	Profile          string                 `json:"profile,omitempty" db:"profile"`
	Task             string                 `json:"task,omitempty"`
	MinValue         any                    `json:"min_value,omitempty"`
	MaxValue         any                    `json:"max_value,omitempty"`
	Checklist        int                    `json:"checklist,omitempty"`
	Object           any                    `json:"object,omitempty"`
	Optional         bool                   `json:"optional"`
	AllowCharacters  string                 `json:"allow_characters"`
	IsUpper          bool                   `json:"is_upper"`
	Display          null.String            `json:"display"`
	Requirement      null.String            `json:"requirement"`
	Tags             null.String            `json:"tags"`
	Validate         any                    `json:"validate,omitempty"`
	TypeDisplay      null.String            `json:"type_display"`
	Scan             json.RawMessage        `json:"scan"`
	OCR              null.String            `json:"ocr"`
	MyProfile        null.String            `json:"my_profile"`
	LimitLength      null.Int               `json:"limit_length"`
	PodType          string                 `json:"pod_type"`
	Editor           []string               `json:"editor"`
	Viewer           []string               `json:"viewer"`
	FileTypes        []string               `json:"file_types,omitempty"`
	AllowChange      bool                   `json:"allow_change,omitempty"`
	SaveToOutputFile bool                   `json:"save_to_output_file,omitempty"`
	Disabled         bool                   `json:"disabled,omitempty"`
	SpecificError    json.RawMessage        `json:"specific_error,omitempty"`
	SourceURL        json.RawMessage        `json:"source_url,omitempty"`
	RelationPodIDs   json.RawMessage        `json:"relation_pod_ids,omitempty"`
	PlaceHolder      null.String            `json:"placeholder,omitempty"`
}

// PodExtSchema pod database schema
type PodExtSchema struct {
	*Pod
	Body      json.RawMessage `json:"body" db:"body"`
	Status    string          `json:"status" db:"status"`
	CreatedAt *time.Time      `json:"created_at" db:"created_at"`
	UpdatedAt *time.Time      `json:"updated_at" db:"updated_at"`
}

type OptionPod struct {
	*Pod
	Value     *InputValue `json:"value,omitempty"`
	InputType string      `json:"input_type,omitempty"`
}

type SchemaPods []*Pod

func (pods *SchemaPods) Scan(src any) error {
	v := reflect.ValueOf(src)
	if !v.IsValid() || v.IsNil() {
		return nil
	}

	source, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("Type assertion .([]byte) failed.")
	}

	var p []*Pod
	err := json.Unmarshal(source, &p)
	if err != nil {
		fmt.Println(string(source))
		return err
	}
	*pods = p
	return nil
}

type VisaProductSchema struct {
	Pods          *SchemaPods `json:"pods"`
	VisaProductID int         `json:"visa_product_id"`
}

// InputValue input value by FE
type InputValue struct {
	FE     any `json:"fe,omitempty"`
	Source any `json:"source,omitempty"`
}

// InputPod input pod
type InputPod struct {
	*Pod
	Value     *InputValue `json:"value,omitempty"`
	InputType string      `json:"input_type,omitempty"`
}

// GetFEValue get FE value
func (pod *InputPod) GetFEValue() any {
	if pod == nil || pod.Value == nil || pod.Value.FE == nil {
		return nil
	}

	// fmt.Println(pod.ID, pod.Category+"_"+pod.SubCategory+"_"+pod.Name)
	return pod.Value.FE
}

// InputPods input pods
type InputPods map[string]*InputPod

// Scan implement for sql.Scan() interface
func (pods *InputPods) Scan(src any) error {
	v := reflect.ValueOf(src)
	if !v.IsValid() || v.IsNil() {
		return nil
	}

	source, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("Type assertion .([]byte) failed. ")
	}

	var p map[string]*InputPod
	err := json.Unmarshal(source, &p)
	if err != nil {
		fmt.Println(string(source))
		return err
	}
	*pods = p
	return nil
}

// Value implement for sql.Value() interface
func (pods InputPods) Value() (driver.Value, error) {
	v, err := json.Marshal(pods)
	return v, err
}

// GetPodByID get visa pod detail by id
func (pods *InputPods) GetPodByID(id string) *InputPod {
	if pods == nil {
		return nil
	}
	if val, ok := map[string]*InputPod(*pods)[id]; ok {
		return val
	}
	return nil
}

// GetPodByName get visa pod detail by name
func (pods *InputPods) GetPodByName(name string) *InputPod {
	if pods == nil {
		return nil
	}

	for _, v := range *pods {
		podName := v.Category + "_" + v.SubCategory + "_" + v.Name
		if podName == name {
			return v
		}
	}

	return nil
}

// ToKeyNameValue to key value
func (pods *InputPods) ToKeyNameValue() map[string]gjson.Result {
	if pods == nil {
		return nil
	}

	result := map[string]gjson.Result{}
	for _, v := range *pods {
		podName := v.ID
		// podName := v.Category + "_" + v.SubCategory + "_" + v.Name
		podValueJ := utils.StructToJSON(v).Get("value.fe")
		if podValueJ.String() != "" {
			result[podName] = podValueJ
		}
	}

	return result
}

// ToMapKeyValues to key value
func (pods *InputPods) ToMapKeyValues() map[string]any {
	if pods == nil {
		return nil
	}

	result := map[string]any{}
	for _, v := range FlatRecursionVisaPod(pods) {
		// podName := v.Category + "_" + v.SubCategory + "_" + v.Name
		podName := v.ID
		if v.Value == nil {
			result[podName] = nil
			continue
		}

		if v.Value.FE != nil {
			result[podName] = nil
			continue
		}

		result[podName] = v.Value.FE
	}

	return result
}

func FlatRecursionVisaPod(pods *InputPods) []*InputPod {
	result := []*InputPod{}
	if pods == nil {
		return result
	}
	for _, pod := range *pods {
		result = append(result, pod)
		if pod.Value == nil {
			continue
		}
		if pod.Value.FE != nil {
			continue
		}

		if oPods, ok := pod.OptionChoice[cast.ToString(pod.Value.FE)]; ok {
			oPodList := InputPods{}
			for _, oPod := range oPods {
				oPodList[cast.ToString(oPod.ID)] = oPod
			}
			result = append(result, FlatRecursionVisaPod(&oPodList)...)
		}
	}
	return result
}

// GetFEValue get fe value
func (pods *InputPods) GetFEValue(id string) any {
	if pods == nil {
		return nil
	}
	pod, ok := map[string]*InputPod(*pods)[id]
	if !ok {
		return nil
	}
	return pod.GetFEValue()
}

// SetFEValue set pod value by id
func (pods *InputPods) SetFEValue(id string, val any) {
	if pods == nil {
		return
	}

	if pod, ok := map[string]*InputPod(*pods)[id]; ok {
		if pod.Value == nil {
			pod.Value = &InputValue{}
		}
		pod.Value.FE = val
		(*pods)[id] = pod
	}
	for _, pod := range *pods {
		if pod.OptionChoice != nil {
			for _, p := range pod.OptionChoice {
				for _, oPod := range p {
					if oPod.ID == id {
						if oPod.Value == nil {
							oPod.Value = &InputValue{}
						}
						oPod.Value.FE = val
					}
				}
			}
		}
	}
}

// MD5 get input pods md5
func (pods *InputPods) MD5() string {
	buff, err := json.Marshal(pods)
	if pods == nil || err != nil {
		return ""
	}

	hasher := md5.New()
	hasher.Write(buff)
	return hex.EncodeToString(hasher.Sum(nil))
}

// Merge merge in the values of b. if there to input pods contain the same k, value in b will overwrite the key
func (pods *InputPods) Merge(b *InputPods) {
	for k, p := range *b {
		(*pods)[k] = p
	}
}

// VisaPodFilter filter request
type VisaPodFilter struct {
	ID                string `form:"id"`
	Title             string `form:"title"`
	Name              string `form:"name"`
	Category          string `form:"category"`
	Country           string `form:"country"`
	RegionOfResidence string `form:"region_of_residence"`
	Destination       string `form:"destination"`
	Nationality       string `form:"nationality"`
	Status            string `form:"status"`
	SortField         string `form:"sort_field"`
	SortOrder         string `form:"sort_order"`
	Limit             uint64 `form:"limit"`
	Offset            uint64 `form:"offset"`
	Tags              string `form:"tags"`
}

// Default default value
func (f *VisaPodFilter) Default() {
	if f == nil {
		return
	}
	if f.SortField == "" {
		f.SortField = "created_at"
		f.SortOrder = "desc"
	}
	if f.Limit == 0 {
		f.Limit = 10
	}
}

// VisaPodFilterResponse data list response
type VisaPodFilterResponse struct {
	Data  []PodExtSchema `json:"data"`
	Total uint64         `json:"total"`
}

type OutputPod struct {
	*Pod
	Value *InputValue `json:"value,omitempty"`
}

type OutputPods map[string]*OutputPod

func (pods *OutputPods) Scan(src any) error {
	v := reflect.ValueOf(src)
	if !v.IsValid() || v.IsNil() {
		return nil
	}

	source, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("Type assertion .([]byte) failed. ")
	}

	var p map[string]*OutputPod
	err := json.Unmarshal(source, &p)
	if err != nil {
		return err
	}
	*pods = p
	return nil
}

func (pods OutputPods) Value() (driver.Value, error) {
	v, err := json.Marshal(pods)
	return v, err
}
