package models

import (
	"time"

	"gopkg.in/guregu/null.v3"
)

type CartGroupItem struct {
	ServiceType             string      `json:"service_type" db:"service_type"`
	ServiceID               int         `json:"service_id" db:"service_id"`
	ServiceTask             null.String `json:"service_task" db:"service_task"`
	Country                 null.String `json:"country" db:"country"`
	LastUpdated             *time.Time  `json:"last_updated" db:"last_updated"`
	SubmittedTime           *time.Time  `json:"submitted_time" db:"submitted_time"`
	OrderTime               *time.Time  `json:"order_time" db:"order_time"`
	CompletedTime           null.String `json:"completed_time" db:"completed_time"`
	ApprovedTime            null.String `json:"approved_time" db:"approved_time"`
	ProcessingTimeExpiredAt *time.Time  `json:"processing_time_expired_at" db:"processing_time_expired_at"`
	FastlaneServiceDate     *string     `json:"fastlane_service_date" db:"fastlane_service_date"`
	DisplayName             string      `json:"display_name" db:"display_name"`
	NumberOfTraveler        int         `json:"number_of_traveler" db:"number_of_traveler"`
	Data                    any         `json:"data" db:"data"`
}

type CartGroupItemRequest struct {
	UserID         string   `form:"user_id"`
	Headers        []string `form:"headers"`
	ProviderID     []string `form:"provider_id"`
	OrgID          int      `form:"org_id"`
	CartItemID     []string `form:"cart_item_id"`
	ConsulateID    string   `form:"consulate_id"`
	IsAdmin        bool
	Name           string     `form:"name"`
	From           *time.Time `form:"from"`
	To             *time.Time `form:"to"`
	ServiceType    []string   `form:"service_type"` // visa, passport,...
	OrderIDs       []int      `form:"order_ids"`    // selected orders
	Types          []string   `form:"type"`         // draft, submit
	Query          string     `form:"query"`
	Limit          uint64     `form:"limit"`
	Offset         uint64     `form:"offset"`
	SortField      string     `form:"sort_field"`
	SortOrder      string     `form:"sort_order"`
	ProviderIDs    []string   `form:"provider_ids"`
	Status         []string   `form:"status"`
	Country        []string   `form:"country"`
	ServiceTask    []string   `form:"service_task"`
	PaymentStatus  []string   `form:"payment_status"`
	StaffID        string     `form:"staff_id"`
	SkipPagination bool
}

type CartGroupItemResponse struct {
	Data       []CartGroupItem `json:"data"`
	Limit      uint64          `json:"limit"`
	Offset     uint64          `json:"offset"`
	Success    bool            `json:"success"`
	TotalCount int             `json:"total_count"`
}

type GroupFilterItemResponse struct {
	Data    []GroupFilter `json:"data"`
	Success bool          `json:"success"`
}

type GroupFilter struct {
	Title string       `json:"filter"`
	Key   string       `json:"param"`
	Value []ItemFilter `json:"options"`
}

type ItemFilter struct {
	Key   string `json:"key"`
	Value string `json:"display"`
}

type GroupOrderItem struct {
	ID           string      `json:"service_id" db:"service_id"`
	ServiceType  string      `json:"service_type" db:"service_type"`
	Status       string      `json:"status" db:"status"`
	UserID       string      `json:"user_id" db:"user_id"`
	ProviderID   null.String `json:"provider_id" db:"provider_id"`
	OrgID        int         `json:"org_id" db:"org_id"`
	ProviderName null.String `json:"provider_name" db:"provider_name"`
	Country      null.String `json:"country"`
	Task         null.String `json:"task" db:"task"`
}
