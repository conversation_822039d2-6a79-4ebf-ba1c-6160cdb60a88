package models

import (
	"time"
)

type ContactBookItem struct {
	ID           int        `json:"id" db:"id"`
	UserID       string     `json:"user_id" db:"user_id"`
	TravelerID   string     `json:"traveler_id" db:"traveler_id"`
	GivenName    string     `json:"given_name" db:"given_name"`
	Surname      string     `json:"surname" db:"surname"`
	MiddleName   string     `json:"middle_name" db:"middle_name"`
	Phone        string     `json:"phone" db:"phone"`
	Email        string     `json:"email" db:"email"`
	Relationship string     `json:"relationship" db:"relationship"`
	RefName      string     `json:"ref_name" db:"ref_name"`
	Status       string     `json:"status" db:"status"`
	CreatedAt    time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt    *time.Time `json:"updated_at" db:"updated_at"`
}
