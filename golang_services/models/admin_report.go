package models

import "time"

type AdminReport struct {
	ID                  int               `json:"id" db:"id"`
	From                time.Time         `json:"report_from" db:"report_from"`
	To                  time.Time         `json:"report_to" db:"report_to"`
	Service             string            `json:"service" db:"service"`
	ReportFile          string            `json:"report_file" db:"report_file"`
	CreatedAt           time.Time         `json:"created_at" db:"created_at"`
	Data                []ReportAdminItem `json:"data" db:"-"` // for export
	FromFormatted       string            `json:"-"`           // for export
	ToFormatted         string            `json:"-"`           // for export
	ReportPassportItems []ReportAdminItem `json:"-"`
	ReportVisaItems     []ReportAdminItem `json:"-"`
	ReportOrtherItems   []ReportAdminItem `json:"-"`
	ReportItems         []ReportAdminItem `json:"-"`
}

type AdminReportReq struct {
	OrgID     int64  `form:"org_id"`
	OrgType   string `form:"org_type"`
	Query     string `form:"query"`
	Offset    uint64 `form:"offset"`
	Limit     uint64 `form:"limit"`
	SortField string `form:"sort_field"`
	SortOrder string `form:"sort_order"`
}

type AdminReportQueryRes struct {
	Data    []AdminReport `json:"data"`
	Success bool          `json:"success"`
	Offset  uint64        `json:"offset"`
	Limit   uint64        `json:"limit"`
	Total   int           `json:"total"`
}

type ReportAdminItem struct {
	ConsulateName     string
	Agency            string
	Country           string
	RegionOfResidence string
	ServiceName       string
	ServiceType       string
	Validity          string
	ProcessingTime    string
	Status            string
	Note              string
	Additional        string
	Tag               string
	OrderNo           string
	ApplicationName   string
	Quantity          int
	ServiceFee        float64
	GovernmentFee     float64
	ShippingFee       float64
	Discount          float64
	CouponCode        string
	Total             float64
	TotalActual       float64
	TotalActualNote   string
	OrderDate         string
	SubmittedDate     string
	ApprovedDate      string
	ServiceDate       string
	CompletedDate     string
	Currency          string
	PaymentMethod     string
	Flight            string
	Description       string
}
