package models

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/tidwall/gjson"
)

type Promotion struct {
	ID          int             `json:"id" db:"id"`
	ServiceType string          `json:"service_type" db:"service_type"`
	ServiceID   int             `json:"service_id" db:"service_id"`
	QueryData   json.RawMessage `json:"query_data" db:"query_data"`
	Name        string          `json:"name" db:"name"`
	Description string          `json:"description" db:"description"`
	StartDate   time.Time       `json:"start_date" db:"start_date"`
	EndDate     time.Time       `json:"end_date" db:"end_date"`
	BannerImage string          `json:"banner_image" db:"banner_image"`
	Active      bool            `json:"active" db:"active"`
	CreatedAt   time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at" db:"updated_at"`
}

type PromotionCoupon struct {
	ID            int             `json:"id" db:"id"`
	PromotionCode string          `json:"promotion_code" db:"promotion_code"`
	PromotionType string          `json:"promotion_type" db:"promotion_type"` // PRODUCT / ORGANIZATION / GENERAL
	DiscountRules json.RawMessage `json:"discount_rules" db:"discount_rules"`
	SelectorRules json.RawMessage `json:"selector_rules" db:"selector_rules"`
	Currency      string          `json:"currency" db:"currency"`
	Quantity      int             `json:"quantity" db:"quantity"`
	Remain        int             `json:"remain" db:"remain"`
	Active        bool            `json:"active" db:"active"`
	StartDate     time.Time       `json:"start_date" db:"start_date"`
	EndDate       time.Time       `json:"end_date" db:"end_date"`
	CreatedAt     time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time       `json:"updated_at" db:"updated_at"`
}

func (p PromotionCoupon) GetDiscountValue(productID int64, user *User) float64 {
	var discount float64 = 0
	discountRulesJ := gjson.ParseBytes(p.DiscountRules)
	if p.PromotionType == "PRODUCT" {
		discount = discountRulesJ.Get(fmt.Sprintf("products.%d", productID)).Float()
	} else if p.PromotionType == "ORGANIZATION" {
		discount = discountRulesJ.Get(fmt.Sprintf("%d", user.OrganizationID.Int64)).Float()
	} else if p.PromotionType == "ORGANIZATION_PRODUCT" {
		discount = discountRulesJ.Get(fmt.Sprintf("%d", user.OrganizationID.Int64)).Get(fmt.Sprintf("%d", productID)).Float()
	} else if p.PromotionType == "GENERAL" {
		discount = discountRulesJ.Get("all").Float()
	}

	if discount > 0 && len(discountRulesJ.Get("user_emails").Array()) > 0 {
		found := false
		for _, email := range discountRulesJ.Get("user_emails").Array() {
			if strings.Contains(strings.ToLower(user.Email), strings.ToLower(email.String())) {
				found = true
				break
			}
		}
		if !found {
			discount = 0
		}
	}
	return discount
}

type PromotionCouponList []PromotionCoupon

func (p PromotionCouponList) ApplyCode(code string, productID int64, user *User) (*PromotionCoupon, float64, bool) {
	if len(p) == 0 {
		return nil, 0, false
	}

	var bestPromotion *PromotionCoupon
	var bestDiscount float64 = 0
	for _, promotion := range p {
		if promotion.PromotionCode == code && promotion.Active && promotion.Remain > 0 && promotion.StartDate.Before(time.Now()) && promotion.EndDate.After(time.Now()) {
			var discount = promotion.GetDiscountValue(productID, user)
			if discount > 0 {
				if bestPromotion == nil {
					bestPromotion = &promotion
					bestDiscount = discount
				} else {
					if discount > bestPromotion.GetDiscountValue(productID, user) {
						bestPromotion = &promotion
						bestDiscount = discount
					}
				}
			}
		}
	}
	return bestPromotion, bestDiscount, bestPromotion != nil
}

type PromotionCouponSimple struct {
	PromotionCode  string          `json:"promotion_code" db:"promotion_code"`
	Currency       string          `json:"currency" db:"currency"`
	Discount       float64         `json:"discount" db:"discount"`
	DiscountOrders map[int]float64 `json:"discount_orders" db:"discount_orders"`
}
