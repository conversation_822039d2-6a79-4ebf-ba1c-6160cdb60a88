package models

import (
	"time"

	"gorm.io/datatypes"
)

type UserFeedback struct {
	ID               int            `json:"id" db:"id"`
	Rate             float64        `json:"rate" db:"rate"`
	ServiceType      string         `json:"service_type" db:"service_type"`
	OrderID          int            `json:"order_id" db:"order_id"`
	Remarks          datatypes.JSON `json:"remarks" db:"remarks"`
	AdditionalRating datatypes.JSON `json:"additional_rating" db:"additional_rating"`
	Comment          string         `json:"comment" db:"comment"`
	CreatedAt        *time.Time     `json:"created_at" db:"created_at"`
	UpdatedAt        *time.Time     `json:"updated_at" db:"updated_at"`
}
