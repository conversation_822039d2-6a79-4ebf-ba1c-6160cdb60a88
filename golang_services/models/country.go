package models

type Country struct {
	Continent string  `json:"continent"`
	Currency  string  `json:"currency"`
	IsoAlpha2 string  `json:"iso_alpha2"`
	IsoAlpha3 string  `json:"iso_alpha3"`
	Language  string  `json:"language"`
	Name      string  `json:"name"`
	National  string  `json:"national"`
	States    []State `json:"states"`
	ZH        string  `json:"zh"`
	VI        string  `json:"vi"`
}

type State struct {
	Abbreviation string `json:"abbreviation"`
	EN           string `json:"en"`
	VI           string `json:"vi"`
	ZH           string `json:"zh"`
}

type StateRegion struct {
	StateRegion string   `json:"state_region"`
	Country     string   `json:"country"`
	State       string   `json:"state"`
	Cities      []string `json:"cities"`
}
