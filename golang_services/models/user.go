package models

import (
	"regexp"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/lib/pq"
	"gopkg.in/guregu/null.v3"
	"gorm.io/datatypes"
)

const (
	UserTypeAdmin       = "AD"
	UserTypeAgency      = "agency"
	UserTypeConsulate   = "consulate"
	UserTypeETSProvider = "ets_provider"
	UserTypeIndividual  = "individual"
	UserTypeCorporation = "corporation"
)

type User struct {
	ID                string         `json:"id" db:"id"`
	Username          string         `json:"username" db:"username"`
	Surname           string         `json:"surname" db:"surname"`
	GivenName         string         `json:"given_name" db:"given_name"`
	SurnameInNative   null.String    `json:"surname_in_native" db:"surname_in_native"`
	GivenNameInNative null.String    `json:"given_name_in_native" db:"given_name_in_native"`
	Avatar            string         `json:"avatar" db:"avatar"`
	Role              string         `json:"role" db:"role"`
	OrganizationID    null.Int       `json:"organization_id" db:"organization_id"`
	Email             string         `json:"email" db:"email"`
	Password          string         `json:"password" db:"password"`
	PasswordInit      string         `json:"password_init" db:"password_init"`
	Status            string         `json:"status" db:"status"`
	EmailVerified     bool           `json:"email_verified" db:"email_verified"`
	AppLanguage       string         `json:"app_language" db:"app_language"`
	StaffContact      datatypes.JSON `json:"staff_contact" db:"staff_contact"`
	AutoAssignedOrder bool           `json:"auto_assigned_order" db:"auto_assigned_order"`
	CreatedAt         *time.Time     `json:"created_at" db:"created_at"`
	UpdatedAt         *time.Time     `json:"updated_at" db:"updated_at"`
}

func (u *User) IsGuest() bool {
	return regexp.MustCompile(`guest_\<EMAIL>`).MatchString(u.Email)
}

type Token struct {
	UserID string `json:"user_id"`
	Role   string `json:"role"`
	*jwt.StandardClaims
}

type UserInfo struct {
	ID                string         `json:"id" db:"id"`
	Username          string         `json:"username" db:"username"`
	Surname           string         `json:"surname" db:"surname"`
	GivenName         string         `json:"given_name" db:"given_name"`
	SurnameInNative   string         `json:"surname_in_native" db:"surname_in_native"`
	GivenNameInNative string         `json:"given_name_in_native" db:"given_name_in_native"`
	Avatar            string         `json:"avatar" db:"avatar"`
	Role              string         `json:"role" db:"role"`
	OrganizationID    null.Int       `json:"organization_id" db:"organization_id"`
	Email             string         `json:"email" db:"email"`
	Password          string         `json:"password" db:"password"`
	Status            string         `json:"status" db:"status"`
	EmailVerified     bool           `json:"email_verified" db:"email_verified"`
	CreatedAt         *time.Time     `json:"created_at" db:"created_at"`
	UpdatedAt         *time.Time     `json:"updated_at" db:"updated_at"`
	UserTypes         pq.StringArray `json:"user_types"`
	AppLanguage       string         `json:"app_language"`
}
