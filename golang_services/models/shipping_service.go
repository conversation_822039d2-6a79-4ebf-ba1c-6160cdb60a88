package models

import (
	"time"
)

type ShippingService struct {
	ID           int        `json:"id" db:"id"`
	Status       string     `json:"status" db:"status"`
	Carrier      string     `json:"carrier" db:"carrier"`
	Service      string     `json:"service" db:"service"`
	Price        float64    `json:"price" db:"price"`
	Currency     string     `json:"currency" db:"currency"`
	ShippingTime int        `json:"shipping_time" db:"shipping_time"`
	CreatedAt    time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt    *time.Time `json:"updated_at" db:"updated_at"`
}

type ShippingServiceWithLabels struct {
	*ShippingService
	Properties *PropertyMap `json:"properties,omitempty"`
}
