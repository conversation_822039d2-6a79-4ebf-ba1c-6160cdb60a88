package models

import (
	"time"
)

type Membership struct {
	ID               int          `json:"id" db:"id"`
	TravelerID       string       `json:"traveler_id" db:"traveler_id"`
	UserID           string       `json:"user_id" db:"user_id"`
	MembershipNumber string       `json:"membership_number" db:"membership_number"`
	Name             string       `json:"name" db:"name"`
	Type             string       `json:"type" db:"type"`
	ExpirationDate   *time.Time   `json:"expiration_date" db:"expiration_date"`
	Status           string       `json:"status" db:"status"`
	Note             string       `json:"note" db:"note"`
	Images           *PropertyMap `json:"images" db:"images"`
	Logo             string       `json:"logo" db:"logo"`
	Description      string       `json:"description" db:"description"`
	CreatedAt        time.Time    `json:"created_at" db:"created_at"`
	UpdatedAt        *time.Time   `json:"updated_at" db:"updated_at"`
}

type MembershipProvider struct {
	Name        string `json:"name" db:"name"`
	Type        string `json:"type" db:"type"`
	Logo        string `json:"logo" db:"logo"`
	Source      string `json:"source" db:"source"`
	Description string `json:"description" db:"description"`
}
