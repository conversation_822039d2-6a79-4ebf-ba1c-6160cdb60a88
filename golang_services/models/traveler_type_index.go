package models

import "time"

const (
	ProfileTypeMyProfile = profileType("my_profile")
)

type profileType string

type TravelerTypeIndex struct {
	ID          int         `json:"id" db:"id"`
	TravelerID  string      `json:"traveler_id" db:"traveler_id"`
	ProfileType profileType `json:"profile_type" db:"profile_type"`
	UserID      string      `json:"user_id" db:"user_id"`
	CreatedAt   time.Time   `json:"created_at" db:"created_at"`
	UpdatedAt   *time.Time  `json:"updated_at" db:"updated_at"`
}
