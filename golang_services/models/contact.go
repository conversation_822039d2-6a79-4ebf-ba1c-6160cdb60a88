package models

import (
	"database/sql/driver"
	"encoding/json"

	"github.com/pkg/errors"
	"gopkg.in/guregu/null.v3"
)

// Contact model
type Contact struct {
	GivenName      string      `json:"given_name" db:"given_name"`
	Surname        string      `json:"surname" db:"surname"`
	Phone          string      `json:"phone" db:"phone"`
	ZaloGroup      string      `json:"zalo_group" db:"zalo_group"`
	Email          string      `json:"email" db:"email"`
	SecondaryPhone null.String `json:"secondary_phone" db:"secondary_phone"`
	SecondaryEmail null.String `json:"secondary_email" db:"secondary_email"`
	WebsiteUrl     null.String `json:"website_url" db:"website_url"`
}

// Scan implement sql.Scan interface
func (c *Contact) Scan(src any) error {
	if src == nil {
		c = nil
		return nil
	}
	switch src := src.(type) {
	case []byte:
		return json.Unmarshal(src, &c)
	case string:
		return json.Unmarshal([]byte(src), &c)
	default:
		return errors.Errorf("Can't scan value type from %T", src)
	}
}

// Value implement driver.Value interface
func (c Contact) Value() (driver.Value, error) {
	return json.<PERSON>(c)
}
