package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

type ShippingRequirement struct {
	CoveringRegionOfResidence string         `json:"covering_region_of_residence"`
	CoveringStateOfResidence  []string       `json:"covering_state_of_residence"`
	CoveringCityOfResidence   []string       `json:"covering_city_of_residence"`
	ShippingContent           string         `json:"shipping_content"`
	From                      map[string]any `json:"from"`
	To                        map[string]any `json:"to"`
}

type RequirementList []*ShippingRequirement

func (rl RequirementList) Value() (driver.Value, error) {
	v, err := json.Marshal(rl)
	return v, err
}

func (rl *RequirementList) Scan(src any) error {
	source, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("Type assertion .([]byte) failed. ")
	}
	return json.Unmarshal(source, rl)
}

type ProductShippingRequirements struct {
	ID           string          `json:"id" db:"id"`
	Requirements RequirementList `json:"requirements" db:"requirements"`
	CreatedAt    time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt    *time.Time      `json:"updated_at" db:"updated_at"`
}
