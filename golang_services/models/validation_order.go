package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"reflect"
	"time"
)

type ValidationOrder struct {
	ID         int        `json:"id" db:"id"`
	UserID     string     `json:"user_id" db:"user_id"`
	OrgID      int        `json:"org_id" db:"org_id"`
	Status     string     `json:"status" db:"status"`
	InputFiles InputFiles `json:"input_files" db:"input_files"`
	CreatedAt  time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt  *time.Time `json:"updated_at" db:"updated_at"`
}

type InputFile struct {
	File string `json:"file"`
	Type string `json:"type"`
}

type InputFiles []*InputFile

func (inputFiles InputFiles) Value() (driver.Value, error) {
	j, err := json.Marshal(inputFiles)
	return j, err
}

func (inputFiles *InputFiles) Scan(src any) error {
	v := reflect.ValueOf(src)
	if !v.IsValid() || v.<PERSON>() {
		return nil
	}

	source, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("Type assertion .([]byte) failed.")
	}

	var i []*InputFile

	err := json.Unmarshal(source, &i)
	if err != nil {
		return err
	}

	if i == nil {
		// There were no properties specified for this record
		return nil
	}

	*inputFiles = i

	return nil
}
