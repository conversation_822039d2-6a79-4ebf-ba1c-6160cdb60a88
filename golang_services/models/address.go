package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"

	"github.com/pariz/gountries"
	"github.com/pkg/errors"
	"gopkg.in/guregu/null.v3"
)

const (
	AddressTypeBusiness    = addressType("business")
	AddressTypeResidential = addressType("residential")
)

type addressType string

// Address model
type Address struct {
	Address         string      `json:"address" db:"address"`
	AddressInNative null.String `json:"address_in_native" db:"address_in_native"`
	City            string      `json:"city" db:"city"`
	ZipCode         null.String `json:"zip_code" db:"zip_code"`
	State           string      `json:"state" db:"state"`
	Country         string      `json:"country" db:"country"`
	Type            addressType `json:"type" db:"type"`
	IsPoBox         null.Bool   `json:"is_po_box" db:"is_po_box"`
	CompanyName     null.String `json:"company_name" db:"company_name"`
}

// Scan implement sql.Scan interface
func (a *Address) Scan(src any) error {
	if src == nil {
		a = nil
		return nil
	}
	switch src := src.(type) {
	case []byte:
		return json.Unmarshal(src, &a)
	case string:
		return json.Unmarshal([]byte(src), &a)
	default:
		return errors.Errorf("Can't scan value type from %T", src)
	}
}

// Value implement driver.Value interface
func (a Address) Value() (driver.Value, error) {
	return json.Marshal(a)
}

func GetFullAddress(address Address) string {
	query := gountries.New()
	country, _ := query.FindCountryByAlpha(address.Country)
	return fmt.Sprintf("%s, %s, %s, %s %s", address.Address, address.City, address.State, address.ZipCode.String, country.Name.Common)
}
