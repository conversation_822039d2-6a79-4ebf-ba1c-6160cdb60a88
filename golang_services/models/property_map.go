package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"reflect"
)

type PropertyMap map[string]any

/*
Value is the interface (driver.Valuer) that transforms our type

	to a database driver compatible type (marshall the map to JSONB)
*/
func (p PropertyMap) Value() (driver.Value, error) {
	j, err := json.Marshal(p)
	return j, err
}

/*
Scan is the second interface (sql.Scanner), which takes the raw data from the database

	and transforms it to our type (unmarshal the JSONB([]byte) to PropertyMap type)
*/
func (p *PropertyMap) Scan(src any) error {
	v := reflect.ValueOf(src)
	if !v.IsValid() || v.IsNil() {
		return nil
	}

	source, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("Type assertion .([]byte) failed.")
	}

	var i any

	err := json.Unmarshal(source, &i)
	if err != nil {
		return err
	}

	if i == nil {
		// There were no properties specified for this record
		return nil
	}

	*p, ok = i.(map[string]any)
	if !ok {
		return fmt.Errorf("Type assertion .(map[string]any) failed.")
	}

	return nil
}

type FileMap map[string]any

/*
Value is the interface (driver.Valuer) that transforms our type

	to a database driver compatible type (marshall the map to JSONB)
*/
func (f FileMap) Value() (driver.Value, error) {
	j, err := json.Marshal(f)
	return j, err
}

/*
Scan is the second interface (sql.Scanner), which takes the raw data from the database

	and transforms it to our type (unmarshal the JSONB([]byte) to FileMap type)
*/
func (f *FileMap) Scan(src any) error {
	v := reflect.ValueOf(src)
	if !v.IsValid() || v.IsNil() {
		return nil
	}

	source, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("Type assertion .([]byte) failed.")
	}

	var i any

	err := json.Unmarshal(source, &i)
	if err != nil {
		return err
	}

	if i == nil {
		// There were no properties specified for this record
		return nil
	}

	*f, ok = i.(map[string]any)
	if !ok {
		return fmt.Errorf("Type assertion .(map[string]any) failed.")
	}

	return nil
}

func (f *FileMap) MarshalJSON() ([]byte, error) {
	m := map[string]any{}
	for k, v := range *f {
		m[k] = map[string]any{
			"file_name": k,
			"file_url":  v,
		}
	}
	return json.Marshal(m)
}

type PropertyArray []any

/*
Value is the interface (driver.Valuer) that transforms our type

	to a database driver compatible type (marshall the map to JSONB)
*/
func (p PropertyArray) Value() (driver.Value, error) {
	j, err := json.Marshal(p)
	return j, err
}

/*
Scan is the second interface (sql.Scanner), which takes the raw data from the database

	and transforms it to our type (unmarshal the JSON([]byte) to PropertyMapArray type)
*/
func (p *PropertyArray) Scan(src any) error {
	v := reflect.ValueOf(src)
	if !v.IsValid() || v.IsNil() {
		return nil
	}

	source, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("Type assertion .([]byte) failed.")
	}

	var i any

	err := json.Unmarshal(source, &i)
	if err != nil {
		return err
	}

	if i == nil {
		// There were no properties specified for this record
		return nil
	}

	*p, ok = i.([]any)
	if !ok {
		return fmt.Errorf("Type assertion .([]any) failed.")
	}

	return nil
}
