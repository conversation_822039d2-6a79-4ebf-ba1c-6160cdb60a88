package models

import (
	"encoding/json"
	"time"

	"github.com/lib/pq"
	"gorm.io/datatypes"
)

var InvoiceStatus = struct{ Created, Sent, Completed string }{"created", "sent", "completed"}

type CorporationInvoice struct {
	ID            int             `json:"id" db:"id"`
	InvoiceNumber string          `json:"invoice_number" db:"invoice_number"`
	From          time.Time       `json:"invoice_from" db:"invoice_from"`
	To            time.Time       `json:"invoice_to" db:"invoice_to"`
	OrgID         int             `json:"org_id" db:"org_id"`
	OrgName       string          `json:"org_name"`
	OrgType       string          `json:"org_type"`
	PONumber      string          `json:"po_number" db:"po_number"`
	Items         json.RawMessage `json:"items" db:"items"`
	SubTotal      float32         `json:"sub_total" db:"sub_total"`
	Total         float32         `json:"total" db:"total"`
	InvoiceFile   string          `json:"invoice_file" db:"invoice_file"`
	ReportFile    string          `json:"report_file" db:"report_file"`
	UserID        string          `json:"user_id" db:"user_id"`
	Status        string          `json:"status" db:"status"`
	CreatedAt     time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt     *time.Time      `json:"updated_at" db:"updated_at"`
	DeletedAt     *time.Time      `json:"deleted_at" db:"deleted_at"`
}

// InvoiceReport invoice report
type InvoiceReport struct {
	InvoiceDate         string `json:"invoice_date"`
	InvoiceNumber       string `json:"invoice_number"`
	From                time.Time
	To                  time.Time
	OrgID               int
	PONumber            string
	CorpName            string `json:"corp_name"`
	CorpCode            string `json:"corp_code"`
	OrgType             string
	Address1            string `json:"address1"`
	Address2            string `json:"address2"`
	Items               []*InvoiceItem
	PassportItems       []*InvoiceItem `json:"passports"`
	VisaItems           []*InvoiceItem `json:"visas"`
	OtherItems          []*InvoiceItem `json:"others"`
	ReportPassportItems []ReportItem
	ReportVisaItems     []ReportItem
	ReportOtherItems    []ReportItem
	ReportItems         []ReportItem
	SubTotal            float32 `json:"sub_total"`
	SubTotalStr         string  `json:"sub_total_str"`
	Tax                 float32
	Total               float32 `json:"total"`
	TotalStr            string  `json:"total_str"`
	InvoiceFile         string
	ReportFile          string
	Status              string
	ID                  string
}

// InvoiceItem
type InvoiceItem struct {
	ID              string  `json:"id"`
	Group           string  `json:"group"`
	Item            string  `json:"item"`
	SubmittedDate   string  `json:"submitted_date"`
	Name            string  `json:"name"`
	Description     string  `json:"description"`
	Qty             string  `json:"qty"`
	Currency        string  `json:"currency"`
	ShippingPrice   float32 `json:"shipping_price"`
	ProcessingTime  string  `json:"processing_time"`
	AdditionalFee   float32 `json:"additional_fee"`
	Price           float32 `json:"price"`
	Total           float32 `json:"total"`
	TotalActual     float32 `json:"total_actual"`
	TotalActualNote string  `json:"total_actual_note"`
	Note            string  `json:"note"`
}

// ReportItem
type ReportItem struct {
	CorpName        string
	CorpCode        string
	OrderNo         string
	OrderStatus     string
	AppName         string
	OrderDate       string
	Country         string
	ServiceType     string
	ServiceName     string
	Description     string
	ProcessingTime  string
	Quantity        int
	UnitPrice       float64
	ShippingPrice   float64
	AdditionalFee   float64
	Total           float64
	TotalActual     float64
	TotalActualNote string
	Currency        string
	PaymentMethod   string
}

type InvoiceQueryReq struct {
	OrgID     int64    `form:"org_id"`
	OrgType   []string `form:"org_type"`
	Status    string   `form:"status"`
	Query     string   `form:"query"`
	Offset    uint64   `form:"offset"`
	Limit     uint64   `form:"limit"`
	SortField string   `form:"sort_field"`
	SortOrder string   `form:"sort_order"`
}

type InvoiceQueryRes struct {
	Data    []CorporationInvoice `json:"data"`
	Success bool                 `json:"success"`
	Offset  uint64               `json:"offset"`
	Limit   uint64               `json:"limit"`
	Total   int                  `json:"total"`
}

type CorporationSelfInvoice struct {
	ID            int            `json:"id" db:"id"`
	Orders        datatypes.JSON `json:"orders" db:"orders"`
	ShippingPrice float64        `json:"shipping_price" db:"shipping_price"`
	Total         float64        `json:"total" db:"total"`
	Files         pq.StringArray `json:"files" db:"files" gorm:"type:text[]"`
	Currency      string         `json:"currency" db:"currency"`
	Status        string         `json:"status" db:"status"`
	UserID        string         `json:"user_id" db:"user_id"`
	CreatedAt     time.Time      `json:"created_at" db:"created_at"`
	UpdatedAt     *time.Time     `json:"updated_at" db:"updated_at"`
	DeletedAt     *time.Time     `json:"deleted_at" db:"deleted_at"`
}

type CorporationSelfInvoiceReq struct {
	Query     string `form:"query"`
	Offset    uint64 `form:"offset"`
	Limit     uint64 `form:"limit"`
	SortField string `form:"sort_field"`
	SortOrder string `form:"sort_order"`
}

type CorporationSelfInvoiceRes struct {
	Data    []CorporationSelfInvoice `json:"data"`
	Success bool                     `json:"success"`
	Offset  uint64                   `json:"offset"`
	Limit   uint64                   `json:"limit"`
	Total   int64                    `json:"total"`
}
