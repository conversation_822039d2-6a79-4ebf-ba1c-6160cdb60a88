package models

type Airline struct {
	FS     string `json:"fs"`
	Name   string `json:"name"`
	Iata   string `json:"iata"`
	Icao   string `json:"icao"`
	Active string `json:"active"`
}

type AirlineFilter struct {
	Query      string   `form:"q"`
	QueryField []string `form:"query_field"`
	Name       []string `form:"name"`
	Iata       []string `form:"iata"`
	Icao       []string `form:"icao"`
	Limit      int      `form:"limit"`
}
