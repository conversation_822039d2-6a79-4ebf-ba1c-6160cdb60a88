package models

import (
	"time"

	"gopkg.in/guregu/null.v3"
)

type AddressBookItem struct {
	ID                int          `json:"id" db:"id"`
	UserID            string       `json:"user_id" db:"user_id"`
	TravelerID        string       `json:"traveler_id" db:"traveler_id"`
	CompanyName       null.String  `json:"company_name" db:"company_name"`
	RecipientFullName string       `json:"recipient_full_name" db:"recipient_full_name"`
	Address           string       `json:"address" db:"address"`
	AddressInNative   string       `json:"address_in_native" db:"address_in_native"`
	City              string       `json:"city" db:"city"`
	ZipCode           string       `json:"zip_code" db:"zip_code"`
	State             string       `json:"state" db:"state"`
	Country           string       `json:"country" db:"country"`
	Type              string       `json:"type" db:"type"`
	RefName           string       `json:"ref_name" db:"ref_name"`
	IsPrimaryAddress  bool         `json:"is_primary_address" db:"is_primary_address"`
	Extra             *PropertyMap `json:"extra" db:"extra"`
	Status            string       `json:"status" db:"status"`
	Validated         bool         `json:"validated" db:"validated"`
	CreatedAt         time.Time    `json:"created_at" db:"created_at"`
	UpdatedAt         *time.Time   `json:"updated_at" db:"updated_at"`
}
