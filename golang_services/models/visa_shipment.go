package models

import (
	"database/sql/driver"
	"encoding/json"

	"github.com/pkg/errors"
)

type VisaShipment struct {
	ID                string       `json:"id" db:"id"`
	RecipientFullName string       `json:"recipient_full_name" db:"recipient_full_name"`
	ShippingContact   *Contact     `json:"shipping_contact" db:"shipping_contact"`
	ShippingAddress   *Address     `json:"shipping_address" db:"shipping_address"`
	PickupAtConsulate bool         `json:"pickup_at_consulate" db:"pickup_at_consulate"`
	ShippingCarrier   string       `json:"shipping_carrier" db:"shipping_carrier"`
	ConsulateCode     string       `json:"consulate_code" db:"consulate_code"`
	Properties        *PropertyMap `json:"properties" db:"properties"`
	Labels            Labels       `json:"labels" db:"labels"`
}
type Label struct {
	Label          string            `json:"label"`
	LabelHistory   map[string]string `json:"label_history"`
	TrackingNumber string            `json:"tracking_number"`
	TrackingURL    string            `json:"tracking_url"`
	Price          float64           `json:"price"`
	Currency       string            `json:"currency"`
}

type Labels map[string]Label

// Scan implement sql.Scan interface
func (l *Labels) Scan(src any) error {
	if src == nil {
		l = nil
		return nil
	}
	switch src := src.(type) {
	case []byte:
		return json.Unmarshal(src, &l)
	case string:
		return json.Unmarshal([]byte(src), &l)
	default:
		return errors.Errorf("Can't scan value type from %T", src)
	}
}

// Value Labels driver.Value interface
func (l Labels) Value() (driver.Value, error) {
	return json.Marshal(l)
}
