package models

import "time"

// EmailSubscribe email subscribe
type EmailSubscribe struct {
	ID                         int        `json:"id" db:"id"`
	Email                      string     `json:"email" db:"email"`
	SubscribeAnnouncement      bool       `json:"subscribe_announcement" db:"subscribe_announcement"`
	SubscribeOrderNotification bool       `json:"subscribe_order_notification" db:"subscribe_order_notification"`
	CreatedAt                  time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt                  *time.Time `json:"update_at" db:"updated_at"`
}
