package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"github.com/pkg/errors"
	"gopkg.in/guregu/null.v3"
)

type Traveler struct {
	ID            string           `json:"id" db:"id"`
	UserID        string           `json:"user_id" db:"user_id"`
	Surname       string           `json:"surname" db:"surname"`
	GivenName     string           `json:"given_name" db:"given_name"`
	Phone         string           `json:"phone" db:"phone"`
	PreferName    null.String      `json:"prefer_name" db:"prefer_name"`
	Residency     null.String      `json:"residency" db:"residency"`
	MaritalStatus null.String      `json:"marital_status" db:"marital_status"`
	WorkEmail     null.String      `json:"work_email" db:"work_email"`
	PersonalEmail null.String      `json:"personal_email" db:"personal_email"`
	WorkPhone     null.String      `json:"work_phone" db:"work_phone"`
	JobTitle      null.String      `json:"job_title" db:"job_title"`
	Occupation    null.String      `json:"occupation" db:"occupation"`
	SocialNetwork *SocialNetwork   `json:"social_network" db:"social_network"`
	HomeAddress   *Address         `json:"home_address" db:"home_address"`
	ResidentInfo  *ResidentInfo    `json:"resident_info" db:"resident_info"`
	TravelHistory *TravelHistories `json:"travel_history" db:"travel_history"`
	CreatedAt     time.Time        `json:"created_at" db:"created_at"`
	UpdatedAt     *time.Time       `json:"updated_at" db:"updated_at"`
}

type SocialNetwork struct {
	Channel  string `json:"channel" db:"channel"`
	SocialID string `json:"social_id" db:"social_id"`
}

// Scan implement sql.Scan interface
func (a *SocialNetwork) Scan(src any) error {
	if src == nil {
		a = nil
		return nil
	}
	switch src := src.(type) {
	case []byte:
		return json.Unmarshal(src, &a)
	case string:
		return json.Unmarshal([]byte(src), &a)
	default:
		return errors.Errorf("Can't scan value type from %T", src)
	}
}

// Value implement driver.Value interface
func (a SocialNetwork) Value() (driver.Value, error) {
	return json.Marshal(a)
}

type ResidentInfo struct {
	Status      string     `json:"status" db:"status"`
	ExpiredDate *time.Time `json:"expired_date" db:"expired_date"`
}

// Scan implement sql.Scan interface
func (a *ResidentInfo) Scan(src any) error {
	if src == nil {
		a = nil
		return nil
	}
	switch src := src.(type) {
	case []byte:
		return json.Unmarshal(src, &a)
	case string:
		return json.Unmarshal([]byte(src), &a)
	default:
		return errors.Errorf("Can't scan value type from %T", src)
	}
}

// Value implement driver.Value interface
func (a ResidentInfo) Value() (driver.Value, error) {
	return json.Marshal(a)
}

type TravelHistories []TravelHistory
type TravelHistory struct {
	Country          string     `json:"country" db:"country"`
	EnterDate        *time.Time `json:"enter_date" db:"enter_date"`
	ExitDate         *time.Time `json:"exit_date" db:"exit_date"`
	Purpose          string     `json:"purpose" db:"purpose"`
	ArrivalAirport   string     `json:"arrival_airport" db:"arrival_airport"`
	DepartureAirport string     `json:"departure_airport" db:"departure_airport"`
}

// Scan implement sql.Scan interface
func (a *TravelHistories) Scan(src any) error {
	if src == nil {
		a = nil
		return nil
	}
	switch src := src.(type) {
	case []byte:
		return json.Unmarshal(src, &a)
	case string:
		return json.Unmarshal([]byte(src), &a)
	default:
		return errors.Errorf("Can't scan value type from %T", src)
	}
}

// Value implement driver.Value interface
func (a TravelHistories) Value() (driver.Value, error) {
	return json.Marshal(a)
}
