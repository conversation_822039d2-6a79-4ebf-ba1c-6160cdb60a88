package models

import (
	"time"
)

type ExchangeRate struct {
	ID           int        `json:"id" db:"id"`
	FromCurrency string     `json:"from_currency" db:"from_currency"`
	ToCurrency   string     `json:"to_currency" db:"to_currency"`
	Rate         float64    `json:"rate" db:"rate"`
	Round        float64    `json:"round" db:"round"`
	Status       string     `json:"status" db:"status"`
	CreatedAt    *time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    *time.Time `json:"updated_at" db:"updated_at"`
}

type ExchangeRateFilter struct {
	FromCurrency string    `json:"from_currency" form:"from_currency"`
	ToCurrency   string    `json:"to_currency" form:"to_currency"`
	FromValues   []float64 `json:"from_values" form:"from_values"`
	ToValues     []float64 `json:"to_values"`
}
