package models

import (
	"database/sql/driver"
	"encoding/json"

	"github.com/pkg/errors"
)

// AppValidateResult app validate result
type AppValidateResult struct {
	Confidence float64 `json:"confidence"`
	Details    any     `json:"details"`
	Reason     string  `json:"reason"`
	Status     string  `json:"status"`
}

// Scan implement sql.Scan interface
func (a *AppValidateResult) Scan(src any) error {
	if src == nil {
		a = nil
		return nil
	}
	switch src := src.(type) {
	case []byte:
		return json.Unmarshal(src, &a)
	case string:
		return json.Unmarshal([]byte(src), &a)
	default:
		return errors.Errorf("Can't scan value type from %T", src)
	}
}

// Value implement driver.Value interface
func (a AppValidateResult) Value() (driver.Value, error) {
	return json.Marshal(a)
}

// PodsValidateResult visa pods validate result
type PodsValidateResult struct {
	Success bool               `json:"success"`
	Version string             `json:"version"`
	Errors  []PodValidateError `json:"errors"`
}

// PodValidateError visa pod validate error
type PodValidateError struct {
	ID              string            `json:"id"`
	Name            string            `json:"name"`
	ErrorCode       string            `json:"error_code"`
	ErrorMessage    string            `json:"error_message"`
	LocalizeMessage map[string]string `json:"localize_message"` // {"vi":"Lỗi", "en": "Error", "cn": "错误"}
}

// Scan implement sql.Scan interface
func (v *PodsValidateResult) Scan(src any) error {
	if src == nil {
		v = nil
		return nil
	}
	switch src := src.(type) {
	case []byte:
		return json.Unmarshal(src, &v)
	case string:
		return json.Unmarshal([]byte(src), &v)
	default:
		return errors.Errorf("Can't scan value type from %T", src)
	}
}

// Value implement driver.Value interface
func (v PodsValidateResult) Value() (driver.Value, error) {
	return json.Marshal(v)
}

// GetFirstError get first error
func (v PodsValidateResult) GetFirstError() *PodValidateError {
	if len(v.Errors) > 0 {
		return &v.Errors[0]
	}
	return nil
}

func (e PodValidateError) Error() string {
	return e.ErrorMessage
}
