package models

import (
	"time"

	"gorm.io/datatypes"
)

type MasterData struct {
	ID        int            `json:"id" db:"id"`
	Category  string         `json:"email" db:"email"`
	Name      string         `json:"name" db:"name"`
	Value     datatypes.JSON `json:"value" db:"value"`
	CreatedAt *time.Time     `json:"created_at" db:"created_at"`
	UpdatedAt *time.Time     `json:"updated_at" db:"updated_at"`
}
