package models

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/sdk/types"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/tidwall/gjson"
)

// VisaPodRequest model
type VisaPodRequest struct {
	ID              int64          `json:"id" db:"id"`
	PodID           int64          `json:"pod_id" db:"pod_id"`
	RequestType     string         `json:"request_type" db:"request_type"`
	Version         string         `json:"version" db:"version"`
	PrePodData      *types.ExtJSON `json:"pre_pod" db:"pre_pod"`
	ExpPodData      *types.ExtJSON `json:"exp_pod" db:"exp_pod"`
	Reason          string         `json:"reason" db:"reason"`
	ReviewerComment string         `json:"reviewer_comment" db:"reviewer_comment"`
	UserID          string         `json:"user_id" db:"user_id"`
	UserDisplayName string         `json:"user_display_name" db:"user_display_name"`
	Status          string         `json:"status" db:"status"`
	CreatedAt       time.Time      `json:"created_at" db:"created_at"`
	UpdatedAt       *time.Time     `json:"updated_at" db:"updated_at"`
}

// Declare enum types
var (
	RequestType    = struct{ Add, Edit, Delete string }{"add", "edit", "delete"}
	RequestStatus  = struct{ Draft, Submitted, Rejected, Approved string }{"draft", "submitted", "rejected", "approved"}
	RequestMetaKey = struct{ VisaProducts, VisaPods string }{"visa_products", "visa_pods"}
)

// WithVersion add version md5
func (p *VisaPodRequest) WithVersion() {
	if p == nil || p.ExpPodData == nil {
		return
	}
	buff, err := json.Marshal(p.ExpPodData)
	if err != nil {
		return
	}

	hasher := md5.New()
	hasher.Write(buff)
	p.Version = hex.EncodeToString(hasher.Sum(nil))
}

// IsDataUpdated check data update or not
func (p *VisaPodRequest) IsDataUpdated() bool {
	if p == nil {
		return true
	}

	if p.RequestType != RequestType.Edit {
		return true
	}

	var (
		sortedSrc  = gjson.ParseBytes(p.PrePodData.Data).Get(`body|@pretty:{"sortKeys":true}`).Raw
		sortedDesc = gjson.ParseBytes(p.ExpPodData.Data).Get(`body|@pretty:{"sortKeys":true}`).Raw
	)

	return sortedSrc != sortedDesc
}

// VisaPodRequestFilter filter request
type VisaPodRequestFilter struct {
	UserID      string
	IsMyRequest bool   `form:"is_my_request"`
	PodID       string `form:"pod_id"`
	SortField   string `form:"sort_field"`
	SortOrder   string `form:"sort_order"`
	Limit       uint64 `form:"limit"`
	Offset      uint64 `form:"offset"`
}

// Default default value
func (f *VisaPodRequestFilter) Default() {
	if f == nil {
		return
	}
	if f.SortField == "" {
		f.SortField = "created_at"
		f.SortOrder = "desc"
	}

	switch f.SortField {
	case "id", "created_at": // Skip
	case "title", "name", "category", "sub_category", "order":
		f.SortField = fmt.Sprintf("pre_pod->>'%s'", f.SortField)
	case "pod_id":
		f.SortField = "pre_pod->>'id'"
	default:
		f.SortField = ""
	}

	f.SortOrder = utils.IfThenElse(f.SortOrder == "desc", "DESC", "ASC")

	if f.Limit == 0 {
		f.Limit = 10
	}
}

// VisaPodRequestFilterResponse data list response
type VisaPodRequestFilterResponse struct {
	Data  []*VisaPodRequest `json:"data"`
	Total uint64            `json:"total"`
}

// VisaPodRequestMeta model
type VisaPodRequestMeta struct {
	ID               int64            `json:"id" db:"id"`
	VisaPodRequestID int64            `json:"visa_pod_request_id" db:"visa_pod_request_id"`
	VisaPodID        int64            `json:"visa_pod_id" db:"visa_pod_id"`
	MetaKey          string           `json:"meta_key" db:"meta_key"`
	Data             *json.RawMessage `json:"data" db:"data"`
	CreatedAt        time.Time        `json:"created_at" db:"created_at"`
	UpdatedAt        *time.Time       `json:"updated_at" db:"updated_at"`
}
