package models

// const (
// 	ApplicationStateSaved              = applicationState("saved")
// 	ApplicationStateCreated            = applicationState("created")
// 	ApplicationStatePreviewed          = applicationState("previewed")
// 	ApplicationStateMissing            = applicationState("missing")
// 	ApplicationStateReady              = applicationState("ready")
// 	ApplicationStateSubmitted          = applicationState("submitted")
// 	ApplicationStateFailed             = applicationState("failed")
// 	ApplicationStateReviewed           = applicationState("reviewed")
// 	ApplicationStateReviewCompleted    = applicationState("review_completed")
// 	ApplicationStateApproved           = applicationState("approved")
// 	ApplicationStateDenied             = applicationState("denied")
// 	ApplicationStateNeedMoreDocument   = applicationState("need_more_document")
// 	ApplicationStateDelivered          = applicationState("delivered")
// 	ApplicationStateLocked             = applicationState("lock")
// 	ApplicationStateCheckedOut         = applicationState("checked_out")
// 	ApplicationStatePacking            = applicationState("packing")
// 	ApplicationStateSubmitToConsulate  = applicationState("submit_to_consulate")
// 	ApplicationUploadedMissingDocument = applicationState("uploaded_missing_document")
// 	ApplicationStateSentVisaPackage    = applicationState("sent_visa_package")
// 	ApplicationStateCancelled          = applicationState("cancelled")
// )

// type applicationState string

// type Application struct {
// 	ID                      int                 `json:"id" db:"id"`
// 	PackageID               int                 `json:"package_id" db:"package_id"`
// 	State                   applicationState    `json:"state" db:"state"`
// 	RegionOfResidence       null.String         `json:"region_of_residence" db:"region_of_residence"`
// 	Surname                 string              `json:"surname" db:"surname"`
// 	GivenName               string              `json:"given_name" db:"given_name"`
// 	Email                   string              `json:"email" db:"email"`
// 	VisaInfo                int                 `json:"-" db:"visa_info"`
// 	VisaInfoObj             *VisaOrderExt       `json:"visa_info,omitempty"`
// 	ReviewJSON              *PropertyMap        `json:"review_json,omitempty" db:"review_json"`
// 	PreviewJSON             *PropertyMap        `json:"preview_json,omitempty" db:"preview_json"`
// 	PackerJSON              *PropertyMap        `json:"packer_json,omitempty" db:"packer_json"`
// 	InputFiles              *FileMap            `json:"input_files,omitempty" db:"input_files"`   // includes S3 link
// 	OutputFiles             *FileMap            `json:"output_files,omitempty" db:"output_files"` // includes S3 link
// 	AdditionalInfo          *PropertyMap        `json:"additional_info" db:"additional_info"`
// 	ExtendedServices        *pq.Int64Array      `json:"extended_services" db:"extended_services"`
// 	ExtendedServicesDetails []*ExtendedService  `json:"extended_services_details,omitempty"`
// 	Remark                  null.String         `json:"remark" db:"remark"`
// 	PodValidateResult       *PodsValidateResult `json:"pod_validation" db:"pod_validation"`
// 	ValidateResult          *AppValidateResult  `json:"-"`
// 	ActivityLog             *PropertyMap        `json:"activity_log" db:"activity_log"`
// 	Note                    json.RawMessage     `json:"note" db:"note"`
// 	CreatedAt               *time.Time          `json:"created_at" db:"created_at"`
// 	UpdatedAt               *time.Time          `json:"updated_at" db:"updated_at"`
// 	InputPods               *InputPods          `json:"-" db:"input_pods"`
// 	InputPodsArr            []*InputPod         `json:"input_pods"`
// 	InputPodValues          any         `json:"input_pod_values" db:"input_pod_values"`
// 	OutputPods              *InputPods          `json:"-" db:"output_pods"`
// 	OutPodValues            any         `json:"output_pod_values" db:"output_pod_values"`
// 	OutputPodsArr           []*InputPod         `json:"output_pods"`
// }

// type applicationWithStatus struct {
// 	ID                     int                 `json:"id"`
// 	PackageID              int                 `json:"package_id"`
// 	State                  applicationState    `json:"state"`
// 	RegionOfResidence      null.String         `json:"region_of_residence"`
// 	Status                 string              `json:"status"`
// 	Surname                string              `json:"surname"`
// 	GivenName              string              `json:"given_name"`
// 	Email                  string              `json:"email"`
// 	VisaInfoObj            *VisaOrderExt       `json:"visa_info"`
// 	ReviewJSON             *PropertyMap        `json:"review_json,omitempty"`
// 	PreviewJSON            *PropertyMap        `json:"preview_json,omitempty"`
// 	PackerJSON             *PropertyMap        `json:"packer_json,omitempty"`
// 	InputFiles             *FileMap            `json:"input_files"`  // includes S3 link
// 	OutputFiles            *FileMap            `json:"output_files"` // includes S3 link
// 	AdditionalInfo         *PropertyMap        `json:"additional_info"`
// 	ExtendedServices       *pq.Int64Array      `json:"extended_services"`
// 	ExtendedServiceDetails []*ExtendedService  `json:"extended_service_details"`
// 	Remark                 null.String         `json:"remark"`
// 	CreatedAt              *time.Time          `json:"created_at"`
// 	UpdatedAt              *time.Time          `json:"updated_at"`
// 	InputPods              []*InputPod         `json:"input_pods"`
// 	OutputPods             []*InputPod         `json:"output_pods"`
// 	InputPodValues         any         `json:"input_pod_values"`
// 	OutPodValues           any         `json:"output_values"`
// 	AIValidateResult       *AppValidateResult  `json:"validate_result"`
// 	PodValidateResult      *PodsValidateResult `json:"pod_validation"`
// 	Note                   json.RawMessage     `json:"note" db:"note"`
// }

// func (a *Application) MarshalJSON() ([]byte, error) {
// 	aa := applicationWithStatus{
// 		ID:                     a.ID,
// 		PackageID:              a.PackageID,
// 		State:                  a.State,
// 		Status:                 stateToStatus(a.State),
// 		Surname:                a.Surname,
// 		GivenName:              a.GivenName,
// 		Email:                  a.Email,
// 		VisaInfoObj:            a.VisaInfoObj,
// 		ReviewJSON:             a.ReviewJSON,
// 		PreviewJSON:            a.PreviewJSON,
// 		PackerJSON:             a.PackerJSON,
// 		InputFiles:             a.InputFiles,
// 		OutputFiles:            a.OutputFiles,
// 		AdditionalInfo:         a.AdditionalInfo,
// 		CreatedAt:              a.CreatedAt,
// 		UpdatedAt:              a.UpdatedAt,
// 		RegionOfResidence:      a.RegionOfResidence,
// 		ExtendedServices:       a.ExtendedServices,
// 		ExtendedServiceDetails: a.ExtendedServicesDetails,
// 		Remark:                 a.Remark,
// 		Note:                   a.Note,
// 		InputPods:              a.InputPodsArr,
// 		OutputPods:             a.OutputPodsArr,
// 		InputPodValues:         a.InputPodValues,
// 		OutPodValues:           a.OutPodValues,
// 		AIValidateResult:       a.ValidateResult,
// 		PodValidateResult:      a.PodValidateResult,
// 	}
// 	return json.Marshal(aa)
// }

// func stateToStatus(state applicationState) string {
// 	switch state {
// 	case ApplicationStateCreated, ApplicationStateSaved:
// 		return "accepted"
// 	case ApplicationStatePreviewed, ApplicationStateReviewed, ApplicationStateMissing, ApplicationStateReady,
// 		ApplicationStateFailed, ApplicationStateCheckedOut, ApplicationStatePacking, ApplicationStateSubmitToConsulate:
// 		return "in_progress"
// 	case ApplicationStateSubmitted, ApplicationStateNeedMoreDocument, ApplicationStateReviewCompleted, ApplicationUploadedMissingDocument, ApplicationStateApproved, ApplicationStateDenied:
// 		return "submitted"
// 	case ApplicationStateDelivered:
// 		return "completed"
// 	}
// 	return ""
// }

// func AppToMap(a *Application) map[string]any {
// 	m := map[string]any{
// 		"state":               a.State,
// 		"surname":             a.Surname,
// 		"given_name":          a.GivenName,
// 		"email":               a.Email,
// 		"region_of_residence": a.RegionOfResidence,
// 	}
// 	if a.AdditionalInfo != nil {
// 		m["additional_info"] = a.AdditionalInfo
// 	}
// 	if a.ExtendedServices != nil {
// 		m["extended_services"] = a.ExtendedServices
// 	}
// 	return m
// }

// type PackerCallbackReq struct {
// 	PackageID         string           `json:"package_id"`
// 	ApplicationID     string           `json:"application_id"`
// 	ApplicationStatus applicationState `json:"application_status"`
// 	S3Bucket          string           `json:"form_upload_s3_bucket"`
// 	S3Key             string           `json:"form_upload_s3_key"`
// 	FormTemplate      string           `json:"form_template"`
// 	VisaProductID     int              `json:"visa_product_id"`
// 	PackerJSON        *PropertyMap     `json:"packer_json"`
// }

// func IsValidApplicationState(state string) bool {
// 	switch applicationState(state) {
// 	case ApplicationStateSaved,
// 		ApplicationStateCreated,
// 		ApplicationStatePreviewed,
// 		ApplicationStateReviewed,
// 		ApplicationStateReviewCompleted,
// 		ApplicationStateMissing,
// 		ApplicationStateReady,
// 		ApplicationStateSubmitted,
// 		ApplicationStateFailed,
// 		ApplicationStateApproved,
// 		ApplicationStateDenied,
// 		ApplicationStateDelivered,
// 		ApplicationStateLocked,
// 		ApplicationStateNeedMoreDocument,
// 		ApplicationStateCheckedOut,
// 		ApplicationStatePacking,
// 		ApplicationStateSubmitToConsulate,
// 		ApplicationUploadedMissingDocument,
// 		ApplicationStateSentVisaPackage,
// 		ApplicationStateCancelled:
// 		return true
// 	default:
// 		return false
// 	}
// }
