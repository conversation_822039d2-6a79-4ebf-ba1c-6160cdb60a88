package models

import (
	"time"
)

type UserLoginHistory struct {
	ID              int        `json:"id" db:"id"`
	UserEmail       string     `json:"email" db:"email"`
	LoginMethod     string     `json:"login_method" db:"login_method"`
	UserAgent       string     `json:"user_agent" db:"user_agent"`
	UserAgentName   string     `json:"-" db:"user_agent_name"`
	UserAgentOS     string     `json:"user_agent_os" db:"user_agent_os"`
	UserAgentDevice string     `json:"user_agent_device" db:"user_agent_device"`
	IPAddress       string     `json:"ip_address" db:"ip_address"`
	IPLocation      string     `json:"ip_location" db:"ip_location"`
	IPCountry       string     `json:"ip_country" db:"ip_country"`
	LogonToken      string     `json:"logon_token" db:"logon_token"`
	LogonAt         *time.Time `json:"logon_at" db:"logon_at"`
	LogoffAt        *time.Time `json:"logoff_at" db:"logoff_at"`
	Status          string     `json:"status" db:"status"`
	CreatedAt       *time.Time `json:"created_at" db:"created_at"`
	UpdatedAt       *time.Time `json:"updated_at" db:"updated_at"`
}

var UserLoginMethod = struct{ Password, Apple, Google, Facebook, Linkedin string }{"PASSWORD", "APPLE", "GOOGLE", "FACEBOOK", "LINKEDIN"}
var UserLoginStatus = struct{ WrongPassword, Login, Logoff, Block string }{"WRONG_PASSWORD", "LOGGED_IN", "LOGGED_OFF", "BLOCKED"}

type UserLogin struct {
	Email      string `json:"email"`
	Password   string `json:"password"`
	UserAgent  string `json:"user_agent"`
	IPAddress  string `json:"ip_address"`
	IPLocation string `json:"ip_location"`
	IPCountry  string `json:"ip_country"`
}
