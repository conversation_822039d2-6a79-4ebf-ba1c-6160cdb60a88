package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/sdk/time_util"
	"bitbucket.org/persistence17/aria/golang_services/sdk/types"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/lib/pq"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
	"gopkg.in/guregu/null.v3"
	"gorm.io/datatypes"
)

const (
	EtsOrderStatusOpen                = "open"
	EtsOrderStatusPendingPayment      = "pending-payment"
	EtsOrderStatusWaitingPayment      = "manual_payment"
	EtsOrderStatusPaid                = "paid" // ets order paid if payment status still pending that mean we not receive callback from vendor (same with lock status in visa order )
	EtsOrderStatusWaitingShipmentUser = "waiting_shipment_user"
	EtsOrderStatusCreatedShipmentUser = "created_shipment_user"
	EtsOrderStatusFormGenerateFailed  = "form_generate_failed"
	// EtsOrderStatusConfirmed           = "confirmed"
	EtsOrderStatusDispatched       = "dispatched"
	EtsOrderStatusSubmitted        = "submitted"
	EtsOrderStatusUnderReview      = "under_review"
	EtsOrderStatusInDelivery       = "in_delivery"
	EtsOrderStatusNeedMoreDocument = "need_more_document"
	EtsOrderStatusCompleted        = "completed"
	EtsOrderStatusCancelled        = "cancelled"

	EtsTaskStatusOpen                    = "open"
	EtsTaskStatusFormCreating            = "form_creating"
	EtsTaskStatusConfirmed               = "confirmed"
	EtsTaskStatusOnGoing                 = "on-going"
	EtsTaskStatusReviewed                = "review_completed"
	EtsTaskStatusApproved                = "approved"
	EtsTaskStatusDenied                  = "denied"
	EtsTaskStatusNeedMoreDocument        = "need_more_document"
	EtsTaskStatusUploadedMissingDocument = "uploaded_missing_document"
	EtsTaskStatusCancelled               = "cancelled"
	EtsTaskStatusFormGenerateFailed      = "form_generate_failed"

	EtsServiceTypeNewVisa           = "new_visa"
	EtsServiceTypeNewVisaUrgent     = "new_visa_urgent"
	EtsServiceTypeFastlane          = "fastlane"
	EtsServiceTypePassport          = "passport"
	EtsServiceTypeIDPhoto           = "id_photo"
	EtsServiceTypeGlobalEntry       = "global_entry"
	EtsServiceTypeTSAPrecheck       = "tsa_precheck"
	EtsServiceTypeSentri            = "sentri"
	EtsServiceTypeFastTruck         = "fast_truck"
	EtsServiceTypeNexus             = "nexus"
	EtsServiceTypeCovidTest         = "covid_test"
	EtsServiceTypeSingleCertificate = "certificate_single_status"
	EtsServiceTypeCountryTourist    = "country_tourist"
	EtsServiceTypeAirportEntry      = "airport_entry"
)

// Config represents the configuration structure
type WorkingTimeConfig struct {
	Location         string              `json:"location"`
	WorkingHours     map[string][]string `json:"working_hours"`
	SpecialOffDays   map[string][]string `json:"special_off_days"`
	QueryTimeSetting string              `json:"query_time_setting"` // current_time, start_of_day, end_of_day
	Buffer           string              `json:"buffer"`
}

type ExtendedTravelService struct {
	ID                int                `json:"id" db:"id"`
	Name              string             `json:"name" db:"name"`
	ServiceType       string             `json:"service_type" db:"service_type"`
	Country           string             `json:"country" db:"country"`
	Airport           *string            `json:"airport" db:"airport"`
	Attributes        *PropertyMap       `json:"attributes" db:"attributes"`
	Currency          string             `json:"currency" db:"currency"`
	Schema            SchemaPods         `json:"schema" db:"schema"`
	OrderSchema       SchemaPods         `json:"order_schema" db:"order_schema"`
	SchemaPods        *pq.StringArray    `json:"schema_pods" db:"schema_pods"`
	Tag               *string            `json:"tag" db:"tag"`
	Status            string             `json:"status" db:"status"`
	Tasks             types.StringArray  `json:"tasks" db:"tasks"`
	WorkingTimes      string             `json:"working_times" db:"working_times"`
	WorkingTimeConfig *WorkingTimeConfig `json:"working_time_config" `
	CreatedAt         types.TimeStamp    `json:"created_at" db:"created_at"`
	UpdatedAt         *types.TimeStamp   `json:"updated_at" db:"updated_at"`
	Price             *BasePrice         `json:"price" db:"-"`
	DebugData         any                `json:"debug_data" `
}

func (s *ExtendedTravelService) Scan(src any) error {
	source, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("Type assertion .([]byte) failed. ")
	}
	j := types.ExtJSON{
		Raw:  source,
		Data: source,
	}
	j.WithISOString()
	err := json.Unmarshal(j.Data, s)
	return err
}

func (s *ExtendedTravelService) IsEnoughTimeToProcess(nowDate, serviceDate time.Time) (bool, error) {
	buff, err := json.Marshal(s)
	if err != nil {
		return false, err
	}

	processingTime := gjson.ParseBytes(buff).Get("attributes.processing_time").String()

	var minServiceDate time.Time
	minServiceDate, err = time_util.CalculateNormalDays(nowDate, processingTime)
	// if s.ServiceType == EtsServiceTypeFastlane {
	// } else {
	// 	minServiceDate, err = time_util.CalculateWorkingDays(nowDate, processingTime, "")
	// }

	if err != nil {
		return false, err
	}

	fmt.Println(processingTime)
	fmt.Println(minServiceDate)
	fmt.Println(serviceDate)
	if minServiceDate.After(serviceDate) {
		return false, nil
	}

	return true, nil
}

func (s *ExtendedTravelService) IsEnoughTransitTime(enterDate, exitDate time.Time) (bool, error) {
	buff, err := json.Marshal(s)
	if err != nil {
		return false, err
	}

	transitProcessingTime := gjson.ParseBytes(buff).Get("attributes.transit_time").String()
	if transitProcessingTime == "" {
		return true, nil
	}
	minTransitDate, err := time_util.CalculateNormalDays(enterDate, transitProcessingTime)
	if err != nil {
		return false, err
	}

	if minTransitDate.After(exitDate) {
		return false, nil
	}

	return true, nil
}

func (s *ExtendedTravelService) IsEnoughValidity(enterDate, exitDate time.Time) (bool, error) {
	buff, err := json.Marshal(s)
	if err != nil {
		return false, err
	}

	processingTime := gjson.ParseBytes(buff).Get("attributes.validity").String()
	if processingTime == "" {
		return true, nil
	}
	maxValidityDate, err := time_util.CalculateNormalDays(enterDate, processingTime)
	if err != nil {
		return false, err
	}

	if maxValidityDate.Truncate(24 * time.Hour).Before(exitDate.Truncate(24 * time.Hour)) {
		return false, nil
	}

	return true, nil
}

type ServiceProvider struct {
	ID          string       `json:"id" db:"id"`
	OrgID       int          `json:"org_id" db:"org_id"`
	ContactInfo *PropertyMap `json:"contact_info" db:"contact_info"`
	MetaInfo    *PropertyMap `json:"meta_info" db:"meta_info"`
	CreatedAt   time.Time    `json:"created_at" db:"created_at"`
	UpdatedAt   *time.Time   `json:"updated_at" db:"updated_at"`
}

type ServiceSummary struct {
	SubTotal            float64                      `json:"sub_total"`
	ShippingPrice       float64                      `json:"shipping_price"`
	UnitPriceWithFees   float64                      `json:"unit_price_with_fees"`    // unit_price + additional_fee
	AdditionAppServices map[int64]map[string]float64 `json:"additional_app_services"` // [Application ID][Service Name][Price]
	UnitPrice           float64                      `json:"unit_price"`
	AdditionalFee       *PropertyMap                 `json:"additional_fee"`
	TransactionFee      float64                      `json:"transaction_fee"`
	Total               float64                      `json:"total"`
	TotalActual         float64                      `json:"total_actual"`
	TotalActualNote     string                       `json:"total_actual_note"`
	Quantity            int                          `json:"quantity"`
	Currency            string                       `json:"currency"`
	PromotionCode       string                       `json:"promotion_code"`
	Discount            float64                      `json:"discount"`
}

func (s *ServiceSummary) Scan(src any) error {
	source, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("Type assertion .([]byte) failed. ")
	}
	return json.Unmarshal(source, s)
}

func (s ServiceSummary) Value() (driver.Value, error) {
	v, err := json.Marshal(s)
	return v, err
}

func (s *ServiceSummary) GetAdditionalFee() float64 {
	var total float64
	utils.StructToJSON(s.AdditionalFee).ForEach(func(key, value gjson.Result) bool {
		total += value.Float()
		return true
	})
	return total
}

type ServiceOrder struct {
	ID                      int                `json:"id" db:"id"`
	Status                  string             `json:"status" db:"status"`
	UserID                  string             `json:"user_id" db:"user_id"`
	ProviderID              string             `json:"provider_id" db:"provider_id"`
	OrgID                   int                `json:"org_id" db:"org_id"`
	ServiceID               int                `json:"service_id" db:"service_id"`
	PaymentID               string             `json:"payment_id" db:"payment_id"`
	QueryPodValues          datatypes.JSON     `json:"query_pod_values" db:"query_pod_values"`
	Summary                 *ServiceSummary    `json:"summary" db:"summary"`
	OutputFiles             *FileMap           `json:"output_files" db:"output_files"`
	FormCallback            *datatypes.JSONMap `json:"form_callback" db:"form_callback"`
	Config                  *PropertyMap       `json:"config" db:"config"`
	ShipmentInfo            null.String        `json:"shipment_info" db:"shipment_info"`
	ShipmentOrder           *PropertyMap       `json:"shipment_order" db:"shipment_order"`
	OrderTime               *time.Time         `json:"order_time" db:"order_time"`
	SubmittedTime           *time.Time         `json:"submitted_time" db:"submitted_time"`
	CompletedTime           *time.Time         `json:"completed_time" db:"completed_time"`
	ProcessingTimeExpiredAt *time.Time         `json:"processing_time_expired_at" db:"processing_time_expired_at"`
	TrackingTimes           *PropertyMap       `json:"tracking_times" db:"tracking_times"`
	CreatedAt               time.Time          `json:"created_at" db:"created_at"`
	UpdatedAt               *time.Time         `json:"updated_at" db:"updated_at"`
	DeletedAt               *time.Time         `json:"deleted_at" db:"deleted_at"`
	AutoGeneratedFormAt     *time.Time         `json:"auto_generated_form_at" db:"auto_generated_form_at"`
	Note                    json.RawMessage    `json:"note" db:"note"`
	IsSendFeedbackToUser    bool               `json:"is_send_feedback_to_user" db:"is_send_feedback_to_user"`
	InputPods               InputPodsArray     `json:"input_pods" db:"input_pods"`
	OutputPods              InputPodsArray     `json:"output_pods" db:"output_pods"`
	InputPodValues          datatypes.JSONMap  `json:"input_pod_values" db:"input_pod_values"`
	OutPodValues            datatypes.JSONMap  `json:"output_pod_values" db:"output_pod_values"`
	ExpectIssueVisaAt       *time.Time         `json:"expect_issue_visa_at" db:"expect_issue_visa_at"`
}

func (o ServiceOrder) IsPaid() bool {
	switch o.Status {
	case EtsOrderStatusOpen,
		EtsOrderStatusPendingPayment,
		EtsOrderStatusWaitingPayment:
		return false
	default:
		return true
	}
}

func (o ServiceOrder) GetOrderStatusByTaskStatuses(taskStatuses []string) string {
	statusCounts := map[string]int{
		EtsTaskStatusApproved:                0,
		EtsTaskStatusDenied:                  0,
		EtsTaskStatusNeedMoreDocument:        0,
		EtsTaskStatusUploadedMissingDocument: 0,
		EtsTaskStatusCancelled:               0,
	}

	for _, status := range taskStatuses {
		statusCounts[status]++
	}

	completeCount := statusCounts[EtsTaskStatusApproved] + statusCounts[EtsTaskStatusDenied]
	cancelCount := statusCounts[EtsTaskStatusCancelled]
	needMoreDocumentCount := statusCounts[EtsTaskStatusNeedMoreDocument] + statusCounts[EtsTaskStatusUploadedMissingDocument]
	totalCount := len(taskStatuses)

	if completeCount > 0 && (completeCount+cancelCount) == totalCount {
		return EtsOrderStatusCompleted
	} else if cancelCount == totalCount {
		return EtsOrderStatusCancelled
	} else if needMoreDocumentCount > 0 {
		return EtsOrderStatusNeedMoreDocument
	}

	return o.Status // No change status
}

// ServiceOrderFilter filter
type ServiceOrderFilter struct {
	IDs                         any      `form:"ids"`
	ID                          []string `form:"id"`
	Status                      []string `form:"status"`
	UserID                      string   `form:"user_id"`
	ProviderOrgID               int64    `form:"provider_org_id"`
	OrgID                       int64    `form:"org_id"`
	ServiceID                   []string `form:"service_id"`
	PaymentID                   []string `form:"payment_id"`
	ServiceType                 []string `form:"service_type"`
	Query                       string   `form:"query"`
	CreatedAtGte                string   `form:"created_at[gte]"`
	CreatedAtLte                string   `form:"created_at[lte]"`
	Limit                       uint64   `form:"limit"`
	Offset                      uint64   `form:"offset"`
	SortField                   string   `form:"sort_field"`
	SortOrder                   string   `form:"sort_order"`
	ADAdmin                     bool
	IncludeService              bool
	IncludePrice                bool
	IncludeTasks                bool `form:"include_tasks"`
	IncludePayment              bool
	IncludeShipment             bool
	SkipPagination              bool
	IncludeOutputFileBeforePaid bool // before paid
	IncludeCreator              bool
	IncludeOutputPodsBeforePaid bool
	IncludeProvider             bool
}

type ServiceOrderDetail struct {
	*ServiceOrder
	TasksRaw      json.RawMessage        `json:"-" db:"tasks"`
	Tasks         []*ServiceTask         `json:"tasks" db:"-"`
	SmallestPrice *BasePrice             `json:"last_price,omitempty" db:"-"`
	PricesRaw     json.RawMessage        `json:"-" db:"prices"`
	PaymentRaw    *json.RawMessage       `json:"payment" db:"payment"`
	Service       *ExtendedTravelService `json:"service" db:"service"`
	CartRaw       *json.RawMessage       `json:"cart" db:"cart"`
	CurrentCart   *json.RawMessage       `json:"current_cart" db:"current_cart"`
	ItemIDInCart  *int                   `json:"item_id_in_cart,omitempty" db:"item_id_in_cart"`
	ShipmentRaw   *json.RawMessage       `json:"shipment" db:"shipment"`
	CreatorRaw    *json.RawMessage       `json:"creator" db:"creator"`
	ProviderRaw   *json.RawMessage       `json:"provider" db:"provider"`
	ActionData    datatypes.JSON         `json:"action_data" db:"action_data"`
	OrderStaffs   datatypes.JSON         `json:"order_staffs" db:"order_staffs"`
	UserFeedback  datatypes.JSON         `json:"user_feedback" db:"user_feedback"`
}

func (s *ServiceOrderDetail) IsEnoughTimeToProcess(nowDate time.Time) (bool, error) {
	var enterTime, exitTime *time.Time

	switch s.Service.ServiceType {
	case EtsServiceTypeFastlane:
		for _, task := range s.Tasks {
			pod := task.InputPods.ToMapKeyValueV2(s.InputPods)

			if task.Type == "arrival" || task.Type == "vip_arrival" {
				// Enter Timestamp
				enterTime = aws.Time(cast.ToTime(pod["travel_enter_flight_enter_timestamp"]))
			}
			if task.Type == "departure" || task.Type == "vip_departure" {
				// Exit Timestamp
				exitTime = aws.Time(cast.ToTime(pod["travel_exit_flight_exit_timestamp"]))
			}
		}

	default:
		// Don't handle proccessing time
		return true, nil
	}

	if enterTime == nil {
		enterTime = exitTime
	}

	if enterTime == nil {
		return false, nil
	}

	if s.Service == nil {
		return false, nil
	}

	return s.Service.IsEnoughTimeToProcess(nowDate, *enterTime)
}

// ServiceOrderResponse response
type ServiceOrderResponse struct {
	Data    []*ServiceOrderDetail `json:"data"`
	Success bool                  `json:"success"`
	Total   uint64                `json:"total"`
}

type ServiceTask struct {
	ID             int64             `json:"id" db:"id"`
	Type           string            `json:"type" db:"type"`
	Status         string            `json:"status" db:"status"`
	OrderID        int               `json:"order_id" db:"order_id"`
	ProviderID     string            `json:"provider_id" db:"provider_id"`
	InputPods      InputPodsArray    `json:"input_pods" db:"input_pods"`
	OutputPods     InputPodsArray    `json:"output_pods" db:"output_pods"`
	OutputFiles    *FileMap          `json:"output_files" db:"output_files"`
	CreatedAt      types.TimeStamp   `json:"created_at" db:"created_at"`
	UpdatedAt      *types.TimeStamp  `json:"updated_at" db:"updated_at"`
	ServiceDate    string            `json:"service_date"`
	Note           json.RawMessage   `json:"note" db:"note"`
	FormCallback   datatypes.JSON    `json:"form_callback" db:"form_callback"`
	InputPodValues datatypes.JSONMap `json:"input_pod_values" db:"input_pod_values"`
	OutPodValues   datatypes.JSONMap `json:"output_pod_values" db:"output_pod_values"`
}

func (s ServiceTask) GetAppName(serviceType string) string {
	name := ""
	pair := s.InputPods.ToMapKeyValue()

	if name == "" {
		names := []string{}

		if val := cast.ToString(pair["passport_core_info_given_name"]); val != "" {
			names = append(names, val)
		}

		if val := cast.ToString(pair["passport_core_info_surname"]); val != "" {
			names = append(names, val)
		}

		name = strings.Join(names, " ")
	}

	if name == "" {
		names := []string{}

		if val := cast.ToString(pair["travel_passenger_info_passenger_name_list"]); val != "" {
			names = append(names, val)
		}

		name = strings.Join(names, " ")
	}

	if name == "" {
		names := []string{}

		if val := cast.ToString(pair["travel_traveler_contact_info_full_name"]); val != "" {
			names = append(names, val)
		} else {
			if val := cast.ToString(pair["travel_traveler_contact_info_given_name"]); val != "" {
				names = append(names, val)
			}

			if val := cast.ToString(pair["travel_traveler_contact_info_surname"]); val != "" {
				names = append(names, val)
			}
		}

		name = strings.Join(names, " ")
	}

	return name
}

type ServiceTaskWithETS struct {
	ID            int64                  `json:"id"`
	Type          string                 `json:"type"`
	Status        string                 `json:"status"`
	OrderID       int                    `json:"order_id"`
	ProviderID    string                 `json:"provider_id"`
	InputPods     InputPodsArray         `json:"input_pods"`
	OutputFiles   *FileMap               `json:"output_files"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     *time.Time             `json:"updated_at"`
	ETS           *ExtendedTravelService `json:"ets"`
	Shipment      *types.ExtJSON         `json:"shipment"`
	OrderStatus   string                 `json:"order_status"`
	SubmittedTime *time.Time             `json:"submitted_time" db:"submitted_time"`
	CompletedTime *time.Time             `json:"completed_time" db:"completed_time"`
}

type ServiceTaskWithETSFilter struct {
	ID          string   `form:"id"`
	ProviderID  string   `form:"provider_id"`
	ServiceType string   `form:"service_type"`
	Status      []string `form:"status"`
	Query       string   `form:"query"`
	Limit       uint64   `form:"limit"`
	Offset      uint64   `form:"offset"`
	SortField   string   `form:"sort_field"`
	SortOrder   string   `form:"sort_order"`
}

type ServiceTaskWithETSData struct {
	Data    []ServiceTaskWithETS `json:"data"`
	Total   uint64               `json:"total"`
	Success bool                 `json:"success"`
}

type InputPodsArray []*InputPod

func (pods *InputPodsArray) Scan(src any) error {
	v := reflect.ValueOf(src)
	if !v.IsValid() || v.IsNil() {
		return nil
	}

	source, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("Type assertion .([]byte) failed. ")
	}

	var p []*InputPod
	err := json.Unmarshal(source, &p)
	if err != nil {
		return err
	}
	*pods = p
	return nil
}

// Value implement for sql.Value() interface
func (pods InputPodsArray) Value() (driver.Value, error) {
	v, err := json.Marshal(pods)
	return v, err
}

// ToMap pod array to map
func (pods InputPodsArray) ToMap() map[string]*InputPod {
	if pods == nil {
		return map[string]*InputPod{}
	}
	return funk.ToMap(pods, "ID").(map[string]*InputPod)
}

// ToMap pod array to map
func (pods InputPodsArray) ToMapByName(name string) map[string]*InputPod {
	if pods == nil {
		return map[string]*InputPod{}
	}
	return funk.ToMap(pods, name).(map[string]*InputPod)
}

func (pods *InputPodsArray) FillByPodValues(podValues map[string]any, skipExistData bool) {
	if pods == nil {
		return
	}
	for _, pod := range *pods {
		if podValue, ok := podValues[pod.ID]; ok {
			if !skipExistData || (cast.ToString(pod.GetFEValue()) == "" && podValue != nil) {
				if len(pod.OptionList) > 0 {
					if pod.Type == "array" {
						values := cast.ToStringSlice(podValue)
						if values == nil {
							values = []string{}
						}
						pod.Value = &InputValue{
							FE: values,
						}
					} else {
						if funk.ContainsString(cast.ToStringSlice(pod.OptionList), cast.ToString(podValue)) {
							pod.Value = &InputValue{
								FE: podValue,
							}
						}
					}

				} else {
					pod.Value = &InputValue{
						FE: podValue,
					}
				}

			}

		}

		if pod.Type == "array" {

			if pod.OptionChoice == nil {
				continue
			}
			for key, oPods := range pod.OptionChoice {
				if podValues[pod.ID] != nil {
					oPodList := InputPodsArray{}
					for _, oPod := range oPods {
						oPodList = append(oPodList, oPod)
					}
					oPodList.FillByPodValues(podValues, skipExistData)
					pod.OptionChoice[key] = oPodList
				}
			}
		} else {
			if choiceValue := podValues[pod.ID]; choiceValue != nil {
				if pod.OptionChoice == nil {
					continue
				}
				if oPods, ok := pod.OptionChoice[cast.ToString(choiceValue)]; ok {
					oPodList := InputPodsArray{}
					for _, oPod := range oPods {
						oPodList = append(oPodList, oPod)
					}
					oPodList.FillByPodValues(podValues, skipExistData)
					pod.OptionChoice[cast.ToString(choiceValue)] = oPodList
				}
			}
		}

	}
}

func FlatRecursionETSPods(pods *InputPodsArray) InputPodsArray {
	result := []*InputPod{}
	if pods == nil {
		return result
	}
	for _, pod := range *pods {
		result = append(result, pod)
		if pod.Value == nil {
			continue
		}
		if pod.Value.FE == nil {
			continue
		}

		if pod.Type == "array" {
			choices := cast.ToStringSlice(pod.Value.FE)
			for _, choice := range choices {
				if pod.OptionChoice == nil {
					continue
				}
				if oPods, ok := pod.OptionChoice[choice]; ok {
					oPodList := InputPodsArray{}
					for _, oPod := range oPods {
						oPodList = append(oPodList, oPod)
					}
					result = append(result, FlatRecursionETSPods(&oPodList)...)
				}
			}
		} else {
			if pod.OptionChoice == nil {
				continue
			}

			if oPods, ok := pod.OptionChoice[cast.ToString(pod.Value.FE)]; ok {
				oPodList := InputPodsArray{}
				for _, oPod := range oPods {
					oPodList = append(oPodList, oPod)
				}
				result = append(result, FlatRecursionETSPods(&oPodList)...)
			}
		}

	}
	return result
}

func (pods InputPodsArray) ToMapKeyValue() datatypes.JSONMap {
	if pods == nil {
		return map[string]any{}
	}

	result := map[string]any{}
	flatPods := FlatRecursionETSPods(&pods)
	for _, v := range flatPods {
		podName := v.ID
		result[podName] = v.GetFEValue()
	}

	return result
}

func (pods InputPodsArray) ToMapKeyValueV2(orderPods InputPodsArray) datatypes.JSONMap {
	if pods == nil {
		return nil
	}

	result := map[string]any{}
	flatPods := FlatRecursionETSPods(&pods)
	for _, v := range flatPods {
		result[v.ID] = v.GetFEValue()
	}

	flatPods2 := FlatRecursionETSPods(&orderPods)
	for _, v := range flatPods2 {
		if _, ok := result[v.ID]; !ok {
			result[v.ID] = v.GetFEValue()
		}
	}

	return result
}

func (pods InputPodsArray) ToMapKeyPod() map[string]*InputPod {
	if pods == nil {
		return nil
	}
	result := map[string]*InputPod{}
	for _, pod := range pods {
		// result[fmt.Sprintf("%s_%s_%s", pod.Category, pod.SubCategory, pod.Name)] = pod
		result[pod.ID] = pod
	}
	return result
}

// SetFEValue set FE value
func (pods InputPodsArray) SetFEValue(podID string, value any) InputPodsArray {
	for i := range pods {
		if pods[i].ID == podID {
			pods[i].Value = &InputValue{
				FE: value,
			}
		}
	}
	return pods
}

type ExtendedServiceTraveler struct {
	FirstName string      `json:"first_name"`
	LastName  string      `json:"last_name"`
	Details   []*InputPod `json:"details"`
}

type EtsTravelers []*ExtendedServiceTraveler

func (t *EtsTravelers) Scan(src any) error {
	switch src := src.(type) {
	case []byte:
		return json.Unmarshal(src, t)
	case string:
		return json.Unmarshal([]byte(src), t)
	case nil:
		*t = nil
		return nil
	}

	return fmt.Errorf("pq: cannot convert %T to EtsTravelers", src)
}

func (t EtsTravelers) Value() (driver.Value, error) {
	byt, err := json.Marshal(t)
	if err != nil {
		return nil, err
	}
	return byt, nil
}

type EtsPrice struct {
	ID                 int            `json:"id" db:"id"`
	Status             string         `json:"status" db:"status"`
	EtsID              int            `json:"ets_id" db:"ets_id"`
	ProviderID         string         `json:"provider_id" db:"provider_id"`
	Price              float64        `json:"price" db:"price"`
	Currency           string         `json:"currency" db:"currency"`
	AdditionalFee      *PropertyMap   `json:"additional_fee" db:"additional_fee"`
	Discount           *float64       `json:"discount" db:"discount"`
	Shipments          *PropertyMap   `json:"shipments" db:"shipments"`
	PriceModifiedRules datatypes.JSON `json:"price_modified_rules" db:"price_modified_rules"`
	AdditionalServices datatypes.JSON `json:"additional_services" db:"additional_services"`
	CreatedAt          time.Time      `json:"-" db:"created_at"`
	UpdatedAt          *time.Time     `json:"-" db:"updated_at"`
}

func (p EtsPrice) GetCalculatePrice() *BasePrice {
	basePrice := BasePrice{
		UnitPrice:          p.Price,
		AdditionalFee:      p.AdditionalFee,
		Shipments:          p.Shipments,
		Currency:           p.Currency,
		PriceModifiedRules: p.PriceModifiedRules,
		AdditionalServices: p.AdditionalServices,
	}

	basePrice.SubTotal = basePrice.UnitPrice

	utils.StructToJSON(p.AdditionalFee).ForEach(func(key, value gjson.Result) bool {
		basePrice.SubTotal += value.Float()
		if basePrice.OriginalPrice != nil {
			*basePrice.OriginalPrice += value.Float()
		}
		return true
	})

	return &basePrice
}

type EtsProvider struct {
	ID               string         `json:"id" db:"id"`
	Status           string         `json:"status" db:"status"`
	Name             string         `json:"name" db:"name"`
	Country          string         `json:"country" db:"country"`
	Address          *Address       `json:"address" db:"address"`
	Contact          *Contact       `json:"contact" db:"contact"`
	SecondaryContact *Contact       `json:"secondary_contact" db:"secondary_contact"`
	TimeZoneName     string         `json:"timezone_name" db:"timezone_name"`
	Website          null.String    `json:"website" db:"website"`
	ServedCountries  null.String    `json:"served_countries" db:"served_countries"`
	ServedArea       null.String    `json:"served_area" db:"served_area"`
	ServedServices   pq.StringArray `json:"served_services" db:"served_services"` // Ex: ["fastlane","passport"]
	OrgID            int64          `json:"org_id" db:"org_id"`
	CreatedAt        time.Time      `json:"created_at" db:"created_at"`
	UpdatedAt        *time.Time     `json:"updated_at" db:"updated_at"`
}

type EtsPod struct {
	ID          string         `json:"id" db:"id"`
	Services    pq.StringArray `json:"services" db:"services"`
	PodType     string         `json:"pod_type" db:"pod_type"`
	Tags        null.String    `json:"tags" db:"tags"`
	Name        string         `json:"name" db:"name"`
	Category    string         `json:"category" db:"category"`
	SubCategory string         `json:"sub_category" db:"sub_category"`
	Body        *PropertyMap   `json:"body" db:"body"`
	Order       int            `json:"order" db:"order"`
	Title       string         `json:"title" db:"title"`
	Status      string         `json:"status" db:"status"`
	CreatedAt   time.Time      `json:"created_at" db:"created_at"`
	UpdatedAt   *time.Time     `json:"updated_at" db:"updated_at"`
}

type EtsQueryPod struct {
	PodID   string       `json:"id" db:"pod_id"`
	Service string       `json:"service" db:"service"`
	Body    *PropertyMap `json:"body" db:"body"`
	Order   int          `json:"order" db:"order"`
}

type BookingAppointmentInfo struct {
	FdbID              string         `json:"fdbId"`
	Date               string         `json:"date"`
	Time               string         `json:"time"`
	FirstName          string         `json:"first_name"`
	LastName           string         `json:"last_name"`
	Phone              string         `json:"phone"`
	Email              string         `json:"email"`
	PostOffice         map[string]any `json:"post-office"`
	ConfirmationNumber string         `json:"confirmation-number"`
	ConfirmationCode   string         `json:"confirmation-code"`
}

func IsOrderPaid(status string) bool {
	switch status {
	case EtsOrderStatusOpen, EtsOrderStatusPendingPayment, EtsOrderStatusWaitingPayment:
		return false
	default:
		return true
	}
}

type ServiceType struct {
	ServiceType string        `json:"service_type"`
	HaveQuery   bool          `json:"have_query"`
	URL         string        `json:"url,omitempty"`
	Order       int           `json:"order"`
	Children    []ServiceType `json:"children,omitempty"`
}

type ServiceOrderStaff struct {
	ID                 int64           `json:"id" db:"id"`
	OrderID            int64           `json:"order_id" db:"order_id"`
	StaffID            string          `json:"staff_id" db:"staff_id"`
	Note               json.RawMessage `json:"note" db:"note"`
	UserID             string          `json:"user_id" db:"user_id"`
	Active             bool            `json:"active" db:"active"`
	StaffConfirmStatus string          `json:"staff_confirm_status" db:"staff_confirm_status"`
	StaffConfirmAt     *time.Time      `json:"staff_confirm_at" db:"staff_confirm_at"`
	StaffConfirmCode   string          `json:"staff_confirm_code" db:"staff_confirm_code"`
	CreatedAt          time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt          *time.Time      `json:"updated_at" db:"updated_at"`
}

type UserSimpleInfo struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

type ServiceCustomerData struct {
	UserID         string            `json:"user_id"`
	PassportNumber string            `json:"passport_number"`
	AppName        string            `json:"app_name"`
	PodValues      datatypes.JSONMap `json:"pod_values"`
}
