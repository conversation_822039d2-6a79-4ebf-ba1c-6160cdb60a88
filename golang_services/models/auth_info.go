package models

import (
	"github.com/thoas/go-funk"
	"gopkg.in/guregu/null.v3"
)

type AuthInfo struct {
	UserID      string
	OrgID       null.Int
	Roles       []string
	Email       string
	UserTypes   []string
	AppLanguage string
}

func (a *AuthInfo) IsADAdmin() bool {
	return funk.ContainsString(a.Roles, "ad_admin")
}

func (a *AuthInfo) IsGuest() bool {
	return funk.ContainsString(a.Roles, "guest")
}

func (a *AuthInfo) IsAdmin() bool {
	return funk.ContainsString(a.Roles, "admin")
}

func (a *AuthInfo) IsUser() bool {
	return funk.ContainsString(a.Roles, "user")
}

func (a *AuthInfo) IsProvider() bool {
	return a.OrgID.Int64 > 0
}

func (a *AuthInfo) IsCanAccessOrg(orgID int64) bool {
	if a.IsADAdmin() {
		return true
	}

	if a.OrgID.Int64 == orgID {
		return true
	}
	return false
}
