package models

type Airport struct {
	City        string `json:"city"`
	FS          string `json:"fs"`
	Name        string `json:"name"`
	Region      string `json:"region"`
	Iata        string `json:"iata" `
	Icao        string `json:"icao"`
	Active      string `json:"active"`
	CountryCode string `json:"country_code"`
	CountryName string `json:"country_name"`
	Timezone    string `json:"timezone"`
	CityCode    string `json:"city_code"`
	ZH          string `json:"zh"`
	VI          string `json:"vi"`
}

type AirportFilter struct {
	Query       string   `form:"q"`
	Iata        []string `form:"iata"`
	CountryCode []string `form:"country_code"`
	CountryName []string `form:"country_name"`
	Limit       int      `form:"limit"`
}
