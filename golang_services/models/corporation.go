package models

import (
	"time"
)

type Corporation struct {
	ID               string     `json:"id" db:"id"`
	Status           string     `json:"status" db:"status"`
	Name             string     `json:"name" db:"name"`
	Domain           string     `json:"domain" db:"domain"`
	Code             string     `json:"code" db:"code"`
	Country          string     `json:"country" db:"country"`
	Address          *Address   `json:"address" db:"address"`
	Contact          *Contact   `json:"contact" db:"contact"`
	SecondaryContact *Contact   `json:"secondary_contact" db:"secondary_contact"`
	TimeZoneName     string     `json:"timezone_name" db:"timezone_name"`
	OrgID            int64      `json:"org_id" db:"org_id"`
	CreatedAt        time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt        *time.Time `json:"updated_at" db:"updated_at"`
}
