package route

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/zalo-mini-app-service/sdk/whatsapp"
	"github.com/gin-gonic/gin"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/proto/waE2E"
	"go.mau.fi/whatsmeow/types"
	"google.golang.org/protobuf/proto"
)

// Response structure for WhatsApp API responses
type WhatsAppResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Error   string      `json:"error,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// CheckWhatsAppHandler checks if a phone number is registered on WhatsApp
func CheckWhatsAppHandler(c *gin.Context) {
	phoneNumber := c.Query("phone")
	if phoneNumber == "" {
		var reqBody struct {
			Phone string `json:"phone"`
		}
		if err := c.ShouldBind<PERSON>N(&reqBody); err != nil || reqBody.Phone == "" {
			c.JSON(http.StatusBadRequest, WhatsAppResponse{
				Success: false,
				Error:   "Phone number is required",
			})
			return
		}
		phoneNumber = reqBody.Phone
	}

	// Ensure phone number starts with +
	if !strings.HasPrefix(phoneNumber, "+") {
		phoneNumber = "+" + phoneNumber
	}

	// Get WhatsApp client
	client := whatsapp.GetInstance()

	// Check if number is on WhatsApp
	info, err := client.IsOnWhatsApp(phoneNumber)
	if err != nil {
		c.JSON(http.StatusInternalServerError, WhatsAppResponse{
			Success: false,
			Error:   "Failed to check WhatsApp: " + err.Error(),
		})
		return
	}

	if info == nil {
		c.JSON(http.StatusOK, WhatsAppResponse{
			Success: true,
			Data: map[string]interface{}{
				"phone_number":   phoneNumber,
				"is_on_whatsapp": false,
			},
		})
		return
	}

	// Get JID from response
	jid := types.NewJID(phoneNumber[1:], types.DefaultUserServer)

	// Get user info
	userInfo, _ := client.GetUserInfo(jid)

	// Get profile picture
	profilePic, _ := client.GetProfilePicture(jid)

	// Prepare response
	response := map[string]interface{}{
		"phone_number":   phoneNumber,
		"is_on_whatsapp": true,
		"jid":            jid.String(),
	}

	// Add user info if available
	if userInfo != nil {
		if userInfo.VerifiedName != nil {
			response["full_name"] = *userInfo.VerifiedName.Details.VerifiedName
		}
		if userInfo.Status != "" {
			response["status"] = userInfo.Status
		}
	}

	// Add profile picture if available
	if profilePic != nil {
		response["picture_url"] = profilePic.URL
		response["picture_id"] = profilePic.ID
	}

	c.JSON(http.StatusOK, WhatsAppResponse{
		Success: true,
		Data:    response,
	})
}

// SendMessageToUserHandler sends a message to a WhatsApp user
func SendMessageToUserHandler(c *gin.Context) {
	phoneNumber := c.PostForm("phone")
	message := c.PostForm("message")

	if phoneNumber == "" || message == "" {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "Phone number and message are required",
		})
		return
	}

	// Ensure phone number starts with +
	if !strings.HasPrefix(phoneNumber, "+") {
		phoneNumber = "+" + phoneNumber
	}

	// Format phone number for WhatsApp
	formattedPhone := phoneNumber
	if formattedPhone[0] == '+' {
		formattedPhone = formattedPhone[1:]
	}

	// Create JID
	jid := types.NewJID(formattedPhone, types.DefaultUserServer)

	// Get WhatsApp client
	client := whatsapp.GetInstance()

	// Send message
	msgID, err := client.SendMessage(jid, message)
	if err != nil {
		c.JSON(http.StatusInternalServerError, WhatsAppResponse{
			Success: false,
			Error:   "Failed to send message: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, WhatsAppResponse{
		Success: true,
		Message: "Message sent successfully",
		Data: map[string]interface{}{
			"message_id": msgID,
		},
	})
}

// SendFileToUserHandler sends a file to a WhatsApp user
func SendFileToUserHandler(c *gin.Context) {
	phoneNumber := c.PostForm("phone")
	caption := c.PostForm("caption")

	if phoneNumber == "" {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "Phone number is required",
		})
		return
	}

	// Get file from form
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "File is required: " + err.Error(),
		})
		return
	}

	// Open the file
	fileReader, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, WhatsAppResponse{
			Success: false,
			Error:   "Failed to open file: " + err.Error(),
		})
		return
	}
	defer fileReader.Close()

	// Read file data
	fileData, err := io.ReadAll(fileReader)
	if err != nil {
		c.JSON(http.StatusInternalServerError, WhatsAppResponse{
			Success: false,
			Error:   "Failed to read file: " + err.Error(),
		})
		return
	}

	// Ensure phone number starts with +
	if !strings.HasPrefix(phoneNumber, "+") {
		phoneNumber = "+" + phoneNumber
	}

	// Format phone number for WhatsApp
	formattedPhone := phoneNumber
	if formattedPhone[0] == '+' {
		formattedPhone = formattedPhone[1:]
	}

	// Create JID
	jid := types.NewJID(formattedPhone, types.DefaultUserServer)

	// Get WhatsApp client
	client := whatsapp.GetInstance()

	// Send file
	msgID, err := client.SendFile(jid, file.Filename, fileData, caption)
	if err != nil {
		c.JSON(http.StatusInternalServerError, WhatsAppResponse{
			Success: false,
			Error:   "Failed to send file: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, WhatsAppResponse{
		Success: true,
		Message: "File sent successfully",
		Data: map[string]interface{}{
			"message_id": msgID,
		},
	})
}

// CreateGroupHandler creates a new WhatsApp group
func CreateGroupHandler(c *gin.Context) {
	var req struct {
		Name         string   `json:"name" binding:"required"`
		Participants []string `json:"participants" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "Invalid request: " + err.Error(),
		})
		return
	}

	// Get WhatsApp client
	client := whatsapp.GetInstance()

	// Create group
	group, err := client.CreateGroup(req.Name, req.Participants)
	if err != nil {
		c.JSON(http.StatusInternalServerError, WhatsAppResponse{
			Success: false,
			Error:   "Failed to create group: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, WhatsAppResponse{
		Success: true,
		Message: "Group created successfully",
		Data: map[string]interface{}{
			"group_id":   group.JID.String(),
			"group_name": group.Name,
		},
	})
}

// SendMessageToGroupHandler sends a message to a WhatsApp group
func SendMessageToGroupHandler(c *gin.Context) {
	var req struct {
		GroupID  string   `json:"group_id" binding:"required"`
		Message  string   `json:"message" binding:"required"`
		Mentions []string `json:"mentions"` // Format like 8408280493
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "Invalid request: " + err.Error(),
		})
		return
	}

	if req.GroupID == "" || req.Message == "" {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "Group ID and message are required",
		})
		return
	}

	// Get WhatsApp client
	client := whatsapp.GetInstance()

	// Send message to group
	msgID, err := client.SendMessageToGroup(req.GroupID, req.Message)
	if err != nil {
		c.JSON(http.StatusInternalServerError, WhatsAppResponse{
			Success: false,
			Error:   "Failed to send message to group: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, WhatsAppResponse{
		Success: true,
		Message: "Message sent to group successfully",
		Data: map[string]interface{}{
			"message_id": msgID,
		},
	})
}

// SendFileToGroupHandler sends a file to a WhatsApp group
func SendFileToGroupHandler(c *gin.Context) {
	groupID := c.PostForm("group_id")
	caption := c.PostForm("caption")

	if groupID == "" {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "Group ID is required",
		})
		return
	}

	// Get file from form
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "File is required: " + err.Error(),
		})
		return
	}

	// Open the file
	fileReader, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, WhatsAppResponse{
			Success: false,
			Error:   "Failed to open file: " + err.Error(),
		})
		return
	}
	defer fileReader.Close()

	// Read file data
	fileData, err := io.ReadAll(fileReader)
	if err != nil {
		c.JSON(http.StatusInternalServerError, WhatsAppResponse{
			Success: false,
			Error:   "Failed to read file: " + err.Error(),
		})
		return
	}

	// Get WhatsApp client
	client := whatsapp.GetInstance()

	// Send file to group
	msgID, err := client.SendFileToGroup(groupID, file.Filename, fileData, caption)
	if err != nil {
		c.JSON(http.StatusInternalServerError, WhatsAppResponse{
			Success: false,
			Error:   "Failed to send file to group: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, WhatsAppResponse{
		Success: true,
		Message: "File sent to group successfully",
		Data: map[string]interface{}{
			"message_id": msgID,
		},
	})
}

// AddUsersToGroupHandler adds users to an existing WhatsApp group
func AddUsersToGroupHandler(c *gin.Context) {
	// Log the raw request body for debugging
	bodyBytes, _ := io.ReadAll(c.Request.Body)
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	fmt.Printf("Raw request body: %s\n", string(bodyBytes))

	var req struct {
		GroupID      string   `json:"group_id" binding:"required"`
		PhoneNumbers []string `json:"phone_numbers" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "Invalid request: " + err.Error(),
		})
		return
	}

	// Log the parsed request for debugging
	fmt.Printf("Parsed request: GroupID=%s, PhoneNumbers=%v\n", req.GroupID, req.PhoneNumbers)

	if len(req.PhoneNumbers) == 0 {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "At least one phone number is required",
		})
		return
	}

	// Format phone numbers to include + prefix if needed
	formattedPhoneNumbers := make([]string, len(req.PhoneNumbers))
	for i, phone := range req.PhoneNumbers {
		// Clean the phone number
		phone = strings.TrimSpace(phone)

		// Ensure phone number starts with +
		if !strings.HasPrefix(phone, "+") {
			phone = "+" + phone
		}

		// Log the phone number for debugging
		fmt.Printf("Formatting phone number for WhatsApp: %s\n", phone)

		formattedPhoneNumbers[i] = phone
	}

	// Get WhatsApp client
	client := whatsapp.GetInstance()

	// Add users to group
	err := client.AddUsersToGroup(req.GroupID, formattedPhoneNumbers)

	// Prepare response data
	responseData := map[string]interface{}{
		"group_id":      req.GroupID,
		"phone_numbers": formattedPhoneNumbers,
	}

	// Check if the error is a GroupAddError which contains an invite link
	var groupAddErr *whatsapp.GroupAddError
	if errors.As(err, &groupAddErr) {
		// We have an invite link available
		responseData["invite_link"] = groupAddErr.InviteLink
		responseData["direct_add_error"] = "Some users could not be added directly due to privacy settings"
		responseData["manual_invite_required"] = true

		// If we have participant results, include them
		if len(groupAddErr.Result) > 0 {
			participantResults := make([]map[string]interface{}, 0, len(groupAddErr.Result))
			for _, participant := range groupAddErr.Result {
				result := map[string]interface{}{
					"phone_number": participant.JID.String(),
					"success":      participant.Error == 0,
				}
				if participant.Error != 0 {
					result["error_code"] = participant.Error
				}
				participantResults = append(participantResults, result)
			}
			responseData["participant_results"] = participantResults
		}

		c.JSON(http.StatusOK, WhatsAppResponse{
			Success: true,
			Message: "Some users could not be added directly. Please share the invite link with them.",
			Data:    responseData,
		})
		return
	}

	// Handle regular error
	if err != nil {
		fmt.Printf("Error adding users to group: %v\n", err)

		// Try to get the invite link as a fallback
		groupJID := req.GroupID
		if !strings.Contains(groupJID, "@") {
			groupJID = groupJID + "@g.us"
		}

		// Parse the group JID
		var groupJIDObj types.JID
		if strings.Contains(groupJID, "@g.us") {
			parts := strings.Split(groupJID, "@")
			if len(parts) == 2 {
				groupJIDObj = types.NewJID(parts[0], types.GroupServer)

				// Try to get the invite link
				inviteLink, linkErr := client.Client.GetGroupInviteLink(groupJIDObj, false)
				if linkErr == nil {
					responseData["invite_link"] = inviteLink
				} else {
					// Try to reset the link if getting it failed
					inviteLink, linkErr = client.Client.GetGroupInviteLink(groupJIDObj, true)
					if linkErr == nil {
						responseData["invite_link"] = inviteLink
					}
				}
			}
		}

		responseData["error"] = err.Error()
		responseData["manual_invite_required"] = true

		message := "Failed to add users to group."
		if responseData["invite_link"] != nil {
			message += " Please share the invite link with them."
		}

		c.JSON(http.StatusOK, WhatsAppResponse{
			Success: false,
			Message: message,
			Data:    responseData,
		})
		return
	}

	// Log success
	fmt.Printf("Successfully added users to group: %s, Phone numbers: %v\n", req.GroupID, formattedPhoneNumbers)

	// Try to get the invite link for convenience
	groupJID := req.GroupID
	if !strings.Contains(groupJID, "@") {
		groupJID = groupJID + "@g.us"
	}

	// Parse the group JID
	var groupJIDObj types.JID
	if strings.Contains(groupJID, "@g.us") {
		parts := strings.Split(groupJID, "@")
		if len(parts) == 2 {
			groupJIDObj = types.NewJID(parts[0], types.GroupServer)

			// Try to get the invite link
			inviteLink, linkErr := client.Client.GetGroupInviteLink(groupJIDObj, false)
			if linkErr == nil {
				responseData["invite_link"] = inviteLink
			}
		}
	}

	c.JSON(http.StatusOK, WhatsAppResponse{
		Success: true,
		Message: "Users added to group successfully",
		Data:    responseData,
	})
}

// RemoveUsersFromGroupHandler removes users from an existing WhatsApp group
func RemoveUsersFromGroupHandler(c *gin.Context) {
	// Log the raw request body for debugging
	bodyBytes, _ := io.ReadAll(c.Request.Body)
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	fmt.Printf("Raw request body for removal: %s\n", string(bodyBytes))

	var req struct {
		GroupID      string   `json:"group_id" binding:"required"`
		PhoneNumbers []string `json:"phone_numbers" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "Invalid request: " + err.Error(),
		})
		return
	}

	// Log the parsed request for debugging
	fmt.Printf("Parsed removal request: GroupID=%s, PhoneNumbers=%v\n", req.GroupID, req.PhoneNumbers)

	if len(req.PhoneNumbers) == 0 {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "At least one phone number is required",
		})
		return
	}

	// Format phone numbers to include + prefix if needed
	formattedPhoneNumbers := make([]string, len(req.PhoneNumbers))
	for i, phone := range req.PhoneNumbers {
		// Clean the phone number
		phone = strings.TrimSpace(phone)

		// Ensure phone number starts with +
		if !strings.HasPrefix(phone, "+") {
			phone = "+" + phone
		}

		// Log the phone number for debugging
		fmt.Printf("Formatting phone number for WhatsApp removal: %s\n", phone)

		formattedPhoneNumbers[i] = phone
	}

	// Get WhatsApp client
	client := whatsapp.GetInstance()

	// Remove users from group
	err := client.RemoveUsersFromGroup(req.GroupID, formattedPhoneNumbers)

	// Get the group JID for getting the invite link
	groupJID := req.GroupID
	if !strings.Contains(groupJID, "@") {
		groupJID = groupJID + "@g.us"
	}

	// Parse the group JID
	var groupJIDObj types.JID
	var inviteLink string
	if strings.Contains(groupJID, "@g.us") {
		parts := strings.Split(groupJID, "@")
		if len(parts) == 2 {
			groupJIDObj = types.NewJID(parts[0], types.GroupServer)

			// Try to get the invite link
			link, linkErr := client.Client.GetGroupInviteLink(groupJIDObj, false)
			if linkErr != nil {
				fmt.Printf("Error getting group invite link: %v\n", linkErr)
			} else {
				inviteLink = link
				fmt.Printf("Got group invite link: %s\n", inviteLink)
			}
		}
	}

	// Prepare response data
	responseData := map[string]interface{}{
		"group_id":      req.GroupID,
		"phone_numbers": formattedPhoneNumbers,
	}

	if inviteLink != "" {
		responseData["invite_link"] = inviteLink
	}

	if err != nil {
		// Log the error for debugging
		fmt.Printf("Error removing users from group: %v\n", err)

		responseData["error"] = err.Error()

		c.JSON(http.StatusOK, WhatsAppResponse{
			Success: true,
			Message: "There was an issue removing users from the group. You may need to remove them manually.",
			Data:    responseData,
		})
		return
	}

	// Log success
	fmt.Printf("Successfully removed users from group: %s, Phone numbers: %v\n", req.GroupID, formattedPhoneNumbers)

	c.JSON(http.StatusOK, WhatsAppResponse{
		Success: true,
		Message: "Users removed from group successfully",
		Data:    responseData,
	})
}

// InviteUsersToGroupHandler invites users to a WhatsApp group (works for users not in contacts)
func InviteUsersToGroupHandler(c *gin.Context) {
	// Log the raw request body for debugging
	bodyBytes, _ := io.ReadAll(c.Request.Body)
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	fmt.Printf("Raw request body for invite: %s\n", string(bodyBytes))

	var req struct {
		GroupID       string   `json:"group_id" binding:"required"`
		PhoneNumbers  []string `json:"phone_numbers" binding:"required"`
		SendInvite    bool     `json:"send_invite"`
		InviteMessage string   `json:"invite_message"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "Invalid request: " + err.Error(),
		})
		return
	}

	// Log the parsed request for debugging
	fmt.Printf("Parsed invite request: GroupID=%s, PhoneNumbers=%v\n", req.GroupID, req.PhoneNumbers)

	if len(req.PhoneNumbers) == 0 {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "At least one phone number is required",
		})
		return
	}

	// Format phone numbers to include + prefix if needed
	formattedPhoneNumbers := make([]string, len(req.PhoneNumbers))
	for i, phone := range req.PhoneNumbers {
		// Clean the phone number
		phone = strings.TrimSpace(phone)

		// Ensure phone number starts with +
		if !strings.HasPrefix(phone, "+") {
			phone = "+" + phone
		}

		// Log the phone number for debugging
		fmt.Printf("Formatting phone number for WhatsApp invite: %s\n", phone)

		formattedPhoneNumbers[i] = phone
	}

	// Get WhatsApp client
	client := whatsapp.GetInstance()

	// Prepare response data
	responseData := map[string]interface{}{
		"group_id":      req.GroupID,
		"phone_numbers": formattedPhoneNumbers,
	}

	// Ensure group ID is in the correct format
	groupJID := req.GroupID
	if !strings.Contains(groupJID, "@") {
		groupJID = groupJID + "@g.us"
	}

	// Parse the group JID
	var groupJIDObj types.JID
	if strings.Contains(groupJID, "@g.us") {
		parts := strings.Split(groupJID, "@")
		if len(parts) == 2 {
			groupJIDObj = types.NewJID(parts[0], types.GroupServer)
		}
	} else {
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "Invalid group ID format",
		})
		return
	}

	// First, verify if the group exists by trying to get group info
	groupInfo, groupInfoErr := client.Client.GetGroupInfo(groupJIDObj)
	if groupInfoErr != nil {
		// Group doesn't exist or user doesn't have access
		c.JSON(http.StatusBadRequest, WhatsAppResponse{
			Success: false,
			Error:   "Group not found or you don't have access to it: " + groupInfoErr.Error(),
		})
		return
	}

	// Group exists, add group name to response
	responseData["group_name"] = groupInfo.Name

	// Try to add users directly first
	directAddResult, directAddErr := client.Client.UpdateGroupParticipants(groupJIDObj, convertToJIDs(formattedPhoneNumbers), whatsmeow.ParticipantChangeAdd)

	// Check for errors in the direct add result
	directAddSuccess := true
	failedParticipants := []map[string]interface{}{}

	for _, participant := range directAddResult {
		if participant.Error != 0 {
			directAddSuccess = false
			errorMessage := getParticipantErrorMessage(participant.Error)
			failedParticipants = append(failedParticipants, map[string]interface{}{
				"phone_number":  participant.JID.String(),
				"error_code":    participant.Error,
				"error_message": errorMessage,
			})

			// If error code is 403 (privacy settings), collect for invite message
			if participant.Error == 403 {
				// Get group info for the invite message
				groupInfo, _ := client.Client.GetGroupInfo(groupJIDObj)

				// Prepare invite message
				inviteMessage := req.InviteMessage
				if inviteMessage == "" {
					inviteMessage = "You're invited to join our WhatsApp group"
				}

				// Track which users received the invite message
				sentInvites := []map[string]interface{}{}
				failedInvites := []map[string]interface{}{}

				// Create group invite message
				groupJidStr := groupJIDObj.String()
				caption := inviteMessage
				// Send invite message
				msg := &waE2E.Message{
					GroupInviteMessage: &waE2E.GroupInviteMessage{
						InviteCode:       proto.String(participant.AddRequest.Code),
						InviteExpiration: proto.Int64(participant.AddRequest.Expiration.Unix()),
						GroupJID:         proto.String(groupJidStr),
						GroupName:        proto.String(groupInfo.Name),
						Caption:          proto.String(caption),
					},
				}

				// Send the message
				resp, err := client.Client.SendMessage(context.Background(), participant.JID, msg)

				if err != nil {
					failedInvites = append(failedInvites, map[string]interface{}{
						"phone_number": participant.JID.String(),
						"error":        err.Error(),
					})
				} else {
					sentInvites = append(sentInvites, map[string]interface{}{
						"phone_number": participant.JID.String(),
						"message_id":   resp.ID,
					})
				}

				// Add invite results to response
				if len(sentInvites) > 0 {
					responseData["sent_invites"] = sentInvites
				}
				if len(failedInvites) > 0 {
					responseData["failed_invites"] = failedInvites
				}
			}
		}
	}

	if directAddErr != nil {
		directAddSuccess = false
		responseData["direct_add_error"] = directAddErr.Error()
	}

	// Always get the invite link (reset=false to use existing link)
	inviteLink, inviteLinkErr := client.Client.GetGroupInviteLink(groupJIDObj, false)
	if inviteLinkErr != nil {
		fmt.Printf("Error getting group invite link: %v\n", inviteLinkErr)
		// Try to reset the link if getting it failed
		inviteLink, inviteLinkErr = client.Client.GetGroupInviteLink(groupJIDObj, true)
		if inviteLinkErr != nil {
			responseData["invite_link_error"] = inviteLinkErr.Error()
		} else {
			responseData["invite_link"] = inviteLink
		}
	} else {
		responseData["invite_link"] = inviteLink
	}

	// Add results to response data
	responseData["direct_add_success"] = directAddSuccess
	if len(failedParticipants) > 0 {
		responseData["failed_participants"] = failedParticipants
	}

	// Determine overall success and message
	success := directAddSuccess || inviteLink != ""
	message := "Users invited to group successfully"

	c.JSON(http.StatusOK, WhatsAppResponse{
		Success: success,
		Message: message,
		Data:    responseData,
	})
}

// Helper function to get human-readable error messages for participant errors
func getParticipantErrorMessage(errorCode int) string {
	switch errorCode {
	case 403:
		return "Forbidden - User's privacy settings prevent direct addition"
	case 404:
		return "Not found - User not found on WhatsApp"
	case 408:
		return "Request timeout"
	case 409:
		return "Conflict - User is already in the group"
	case 500:
		return "Server error"
	default:
		return fmt.Sprintf("Unknown error (code: %d)", errorCode)
	}
}

// Helper function to convert phone numbers to JIDs
func convertToJIDs(phoneNumbers []string) []types.JID {
	jids := make([]types.JID, 0, len(phoneNumbers))
	for _, phone := range phoneNumbers {
		// Remove + prefix if present
		phone = strings.TrimPrefix(phone, "+")
		jid := types.NewJID(phone, types.DefaultUserServer)
		jids = append(jids, jid)
	}
	return jids
}

// GetGroupListHandler returns a list of WhatsApp groups the user is participating in
func GetGroupListHandler(c *gin.Context) {
	// Get WhatsApp client
	client := whatsapp.GetInstance()

	// Get joined groups
	groups, err := client.GetJoinedGroups()
	if err != nil {
		c.JSON(http.StatusInternalServerError, WhatsAppResponse{
			Success: false,
			Error:   "Failed to get group list: " + err.Error(),
		})
		return
	}

	// Format the response
	groupList := make([]map[string]interface{}, 0, len(groups))
	for _, group := range groups {
		// Extract group information
		groupInfo := map[string]interface{}{
			"id":   group.JID.String(),
			"name": group.Name,
		}

		// Add participants information if available
		if group.Participants != nil {
			participants := make([]map[string]interface{}, 0, len(group.Participants))
			for _, participant := range group.Participants {
				participantInfo := map[string]interface{}{
					"jid":   participant.JID.String(),
					"admin": participant.IsAdmin,
				}
				participants = append(participants, participantInfo)
			}
			groupInfo["participants"] = participants
			groupInfo["participants_count"] = len(group.Participants)
		}

		// Add other group metadata if available
		if !group.OwnerJID.IsEmpty() {
			groupInfo["owner"] = group.OwnerJID.String()
		}

		groupList = append(groupList, groupInfo)
	}

	c.JSON(http.StatusOK, WhatsAppResponse{
		Success: true,
		Message: "Group list retrieved successfully",
		Data: map[string]interface{}{
			"groups":       groupList,
			"groups_count": len(groupList),
		},
	})
}
