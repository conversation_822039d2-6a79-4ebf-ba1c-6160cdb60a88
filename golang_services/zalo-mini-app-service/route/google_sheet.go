package route

import (
	"context"
	"io/ioutil"

	"golang.org/x/oauth2/google"
	"gopkg.in/Iwark/spreadsheet.v2"
)

func GetLastEmptyRow(sheet *spreadsheet.Sheet) int {
	lastRow := len(sheet.Rows)
	for i := lastRow - 1; i >= 0; i-- {
		if sheet.Rows[i][0].Value == "" {
			return i
		}
	}
	return lastRow
}

func AppendToGoogleSheet(surveyData []string) error {
	data, err := ioutil.ReadFile("certification.json")
	if err != nil {
		return err
	}

	conf, err := google.JWTConfigFromJSON(data, spreadsheet.Scope)
	if err != nil {
		return err
	}

	client := conf.Client(context.TODO())
	service := spreadsheet.NewServiceWithClient(client)
	spreadsheetID := "1C28auYkBeEYVIl97eojs92Z84OnL2fHptH3kHxu6htM"
	spreadsheet, err := service.FetchSpreadsheet(spreadsheetID)
	if err != nil {
		return err
	}
	sheet, err := spreadsheet.SheetByID(0)
	if err != nil {
		return err
	}

	lastRow := GetLastEmptyRow(sheet)

	for i, val := range surveyData {
		sheet.Update(lastRow, i, val)
	}

	// Make sure to call Synchronize to reflect the changes
	err = sheet.Synchronize()
	if err != nil {
		return err
	}
	return nil
}
