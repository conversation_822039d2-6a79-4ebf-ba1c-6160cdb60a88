package route

import (
	"io/ioutil"
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/zalo-mini-app-service/sdk/zalo"
	"github.com/gin-gonic/gin"
)

func SendMessageToGroup(c *gin.Context) {
	shareLink := c.PostForm("shareLink")
	message := c.PostForm("message")
	zalo.SendMessageToGroup(shareLink, message)
	c.JSON(http.StatusOK, gin.H{"message": "Message sent to group"})
}
func SendMessageToUser(c *gin.Context) {
	phoneNumber := c.PostForm("phone")
	message := c.PostForm("message")
	zalo.SendMessageToUser(phoneNumber, message)
	c.JSON(http.StatusOK, gin.H{"message": "Message sent to user"})
}
func SendFileToUser(c *gin.Context) {
	phoneNumber := c.PostForm("phone")
	file, err := c.FormFile("file")
	if err != nil {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Open the uploaded file
	fileReader, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer fileReader.Close()

	// Read the file
	fileBuff, err := ioutil.ReadAll(fileReader)
	fileName := file.Filename
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	zalo.SendFileToUser(phoneNumber, fileName, fileBuff)
	c.JSON(http.StatusOK, gin.H{"message": "File sent to User"})
}
func SendImageToUser(c *gin.Context) {
	phoneNumber := c.PostForm("phone")
	file, err := c.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Open the uploaded file
	fileReader, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer fileReader.Close()

	// Read the image file
	imageBuff, err := ioutil.ReadAll(fileReader)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	zalo.SendImageToUser(phoneNumber, imageBuff)
	c.JSON(http.StatusOK, gin.H{"message": "Image sent to User"})
}
func SendFileToGroup(c *gin.Context) {
	shareLink := c.PostForm("shareLink")
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Open the uploaded file
	fileReader, err := file.Open()
	fileName := file.Filename
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer fileReader.Close()

	// Read the file
	fileBuff, err := ioutil.ReadAll(fileReader)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	zalo.SendFileToGroup(shareLink, fileName, fileBuff)
	c.JSON(http.StatusOK, gin.H{"message": "Image file to group"})
}

func SendImageToGroup(c *gin.Context) {
	shareLink := c.PostForm("shareLink")
	file, err := c.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Open the uploaded file
	fileReader, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer fileReader.Close()

	// Read the image file
	imageBuff, err := ioutil.ReadAll(fileReader)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	zalo.SendImageToGroup(shareLink, imageBuff)
	c.JSON(http.StatusOK, gin.H{"message": "Image sent to group"})
}
