package route

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type ConvertCodeToUserInfoRequest struct {
	AccessToken string `json:"access_token"`
	Code        string `json:"code"`
}

type CreateUserSurveyRequest struct {
	OAId         string `json:"oa_id"`
	Phone        string `json:"phone"`
	Name         string `json:"name"`
	Year         int    `json:"year"`
	Province     string `json:"province"`
	District     string `json:"district"`
	Ward         string `json:"ward"`
	IsInterested bool   `json:"is_interested"`
	Username     string `json:"username"`
}

func ConvertCodeToUserInfo(c *gin.Context) {
	var req ConvertCodeToUserInfoRequest
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		c.J<PERSON>(http.StatusOK, gin.H{
			"error":   err.<PERSON>rror(),
			"success": false,
		})
		return
	}

	if req.AccessToken == "" || req.Code == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"phone_number": "84900000000",
			},
		})
		return
	}

	client := resty.New()
	resp, err := client.R().
		SetHeaders(map[string]string{
			"access_token": req.AccessToken,
			"code":         req.Code,
			"secret_key":   "R2POMbJ6GNGVVEYFX3EI",
		}).
		Get("https://graph.zalo.me/v2.0/me/info")

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"phone_number": gjson.ParseBytes(resp.Body()).Get("data.number").String(),
		},
	})
}

func CreateUserSurvey(c *gin.Context) {
	var req CreateUserSurveyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	// Get the MongoDB client from the Gin context
	mongoClient, exists := c.Get("mongoClient")
	if !exists {
		c.JSON(http.StatusOK, gin.H{
			"error":   "MongoDB client not found in context",
			"success": false,
		})
		return
	}

	// Save the user info to the database
	collection := mongoClient.(*mongo.Database).Collection("zalo_mini_app_users")
	_, err := collection.InsertOne(context.Background(), bson.M{
		"oa_id":         req.OAId,
		"phone":         req.Phone,
		"name":          req.Name,
		"year":          req.Year,
		"province":      req.Province,
		"district":      req.District,
		"ward":          req.Ward,
		"username":      req.Username,
		"is_interested": req.IsInterested,
		"created_at":    time.Now(),
		"updated_at":    time.Now(),
	})

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"error":   err.Error(),
			"success": false,
		})
		return
	}

	go AppendToGoogleSheet([]string{
		req.OAId,
		req.Phone,
		req.Name,
		cast.ToString(req.Year),
		req.Province,
		req.District,
		req.Ward,
		req.Username,
		cast.ToString(req.IsInterested),
		time.Now().Format("2006-01-02 15:04:05"),
	})

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    req,
	})
}
