package main

import (
	"net/http"
	"os"

	"bitbucket.org/persistence17/aria/golang_services/zalo-mini-app-service/route"
	"bitbucket.org/persistence17/aria/golang_services/zalo-mini-app-service/sdk/whatsapp"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/urfave/cli"
)

func main() {
	app := cli.NewApp()
	app.Name = "Zalo Mini App Service"
	app.Usage = "A service for Zalo Mini App"
	app.Commands = []cli.Command{
		{
			Name:  "start",
			Usage: "Start the Zalo Mini App Service",
			Action: func(c *cli.Context) error {
				startService()
				return nil
			},
		},
	}

	err := app.Run(os.Args)
	if err != nil {
		panic(err)
	}
}

func startService() {
	// Initialize WhatsApp client
	_ = whatsapp.GetInstance()

	router := gin.Default()
	router.Use(cors.Default())

	router.GET("/api/health", func(c *gin.Context) {
		c.<PERSON>(http.StatusOK, gin.H{
			"success": true,
		})
	})

	// Zalo API routes
	router.POST("/api/convert_code_to_user_info", route.ConvertCodeToUserInfo)
	router.POST("/api/user/surveys", route.CreateUserSurvey)
	router.POST("/api/webhooks", route.ZaloWebHook)
	router.POST("/api/zalo_chat/group/send_message", route.SendMessageToGroup)
	router.POST("/api/zalo_chat/user/send_message", route.SendMessageToUser)
	router.POST("/api/zalo_chat/user/send_image", route.SendImageToUser)
	router.POST("/api/zalo_chat/user/send_file", route.SendFileToUser)
	router.POST("/api/zalo_chat/group/send_image", route.SendImageToGroup)
	router.POST("/api/zalo_chat/group/send_file", route.SendFileToGroup)

	router.GET("/api/whatsapp/check", route.CheckWhatsAppHandler)
	router.POST("/api/whatsapp/user/send_message", route.SendMessageToUserHandler)
	router.POST("/api/whatsapp/user/send_file", route.SendFileToUserHandler)
	router.POST("/api/whatsapp/group/create", route.CreateGroupHandler)
	router.POST("/api/whatsapp/group/send_message", route.SendMessageToGroupHandler)
	router.POST("/api/whatsapp/group/send_file", route.SendFileToGroupHandler)
	router.POST("/api/whatsapp/group/add_users", route.AddUsersToGroupHandler)
	router.POST("/api/whatsapp/group/remove_users", route.RemoveUsersFromGroupHandler)
	router.POST("/api/whatsapp/group/invite_users", route.InviteUsersToGroupHandler)

	router.GET("/api/whatsapp/group/list", route.GetGroupListHandler)

	port := os.Getenv("PORT")
	if port == "" {
		port = "3000"
	}
	router.Run(":" + port)
}
