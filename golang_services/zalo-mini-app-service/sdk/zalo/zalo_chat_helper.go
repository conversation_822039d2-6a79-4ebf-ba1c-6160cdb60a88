package zalo

import (
	"encoding/json"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"unicode/utf8"
)

type Style struct {
	Start int    `json:"start"`
	Len   int    `json:"len"`
	St    string `json:"st"`
}

type TextProperties struct {
	Styles []Style `json:"styles"`
	Ver    int     `json:"ver"`
}

// parseHTMLToZaloTextProperties parses an HTML string into a Zalo text string
// Example: Thường <b>In Đậm</b> <i>In Nghiêng</i> <span style="color:#db342e">Màu Đỏ</span><span style="size:20">To</span>

func parseHTMLToZaloTextProperties(input string) (string, string, error) {
	text, styles, err := innterParseHtml(input, 0)
	textProperties := TextProperties{
		Styles: mergeStyles(styles),
		Ver:    0,
	}
	propertiesJSON, _ := json.Marshal(textProperties)
	if err != nil {
		return "", "", err
	}
	return text, string(propertiesJSON), nil
}
func mergeStyles(styles []Style) []Style {
	if len(styles) == 0 {
		return styles
	}

	// Sort styles by start position
	sort.Slice(styles, func(i, j int) bool {
		if styles[i].Start != styles[j].Start {
			return styles[i].Start < styles[j].Start
		}
		return styles[i].Len > styles[j].Len // Larger lengths first
	})

	var mergedStyles []Style
	currentStyle := styles[0]
	for i := 1; i < len(styles); i++ {
		if styles[i].Start == currentStyle.Start && styles[i].Len == currentStyle.Len {
			currentStyle.St += "," + styles[i].St
		} else {
			mergedStyles = append(mergedStyles, currentStyle)
			currentStyle = styles[i]
		}
	}

	mergedStyles = append(mergedStyles, currentStyle)

	return mergedStyles
}

func parseSpanAttributes(attributes string) string {
	var styleBuilder strings.Builder

	// Find color attribute
	colorMatch := regexp.MustCompile(`color:#([0-9a-fA-F]+)`).FindStringSubmatch(attributes)
	if colorMatch != nil {
		styleBuilder.WriteString("c_")
		styleBuilder.WriteString(colorMatch[1])
	}

	// Find size attribute
	sizeMatch := regexp.MustCompile(`size:(\d+)`).FindStringSubmatch(attributes)
	if sizeMatch != nil {
		if styleBuilder.Len() > 0 {
			styleBuilder.WriteString(",")
		}
		styleBuilder.WriteString("f_")
		styleBuilder.WriteString(sizeMatch[1])
	}

	return styleBuilder.String()
}
func innterParseHtml(input string, start int) (string, []Style, error) {
	text := ""
	var styles []Style

	// Define regex patterns for opening and closing tags
	openTagPattern := regexp.MustCompile(`<(\w+)([^>]*)>`)

	// Iterate over the input string
	cursor := 0
	for {
		openTagLoc := openTagPattern.FindStringSubmatchIndex(input[cursor:])
		if openTagLoc == nil {
			text += input[cursor:]
			break
		}

		// Add text up to the next tag to the output
		text += input[cursor : cursor+openTagLoc[0]]

		tag := input[cursor+openTagLoc[2] : cursor+openTagLoc[3]]
		attributes := input[cursor+openTagLoc[4] : cursor+openTagLoc[5]]

		// Move cursor past the current tag
		cursor += openTagLoc[1]

		// Find the corresponding closing tag
		closeTagPattern := regexp.MustCompile(`</` + tag + `>`)
		closeTagLoc := closeTagPattern.FindStringIndex(input[cursor:])
		if closeTagLoc == nil {
			return "", nil, fmt.Errorf("unmatched tag: %s", tag)
		}

		// Parse inner HTML recursively
		innerText, innerStyles, err := innterParseHtml(input[cursor:cursor+closeTagLoc[0]], start+len(text))
		if err != nil {
			return "", nil, err
		}
		text += innerText

		styles = append(styles, innerStyles...)
		// Move cursor past the closing tag
		cursor += closeTagLoc[1]

		// Determine the style
		var style string
		switch tag {
		case "b":
			style = "b"
		case "i":
			style = "i"
		case "s":
			style = "s"
		case "span":
			style = parseSpanAttributes(attributes)
		}
		if style != "" {
			styles = append(styles, Style{
				Start: start + utf8.RuneCountInString(text) - utf8.RuneCountInString(innerText),
				Len:   utf8.RuneCountInString(innerText),
				St:    style,
			})
		}
	}

	return text, styles, nil
}
