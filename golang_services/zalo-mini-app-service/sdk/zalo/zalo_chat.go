package zalo

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/rand"
	"mime/multipart"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

const (
	cookie          = "_zlang=vn; _ga=GA1.2.585358910.1736925040; _gid=GA1.2.1445665861.1736925040; zpsid=vrtS.412316796.4.knXWjScfbEErtGVxnQcKuxpQvjVucwVI_vwjrSSItYMLIKUYo5dBPAwfbEC; zpw_sek=hoku.412316796.a0.-UyxyXLLI0Df8TiRD5L80svtBMWtRsfWGZy0I7eI03HSO7jjNISgL7DaVtjKQ7KXQ5lpITC9gD1KLBbSaZ980m; __zi=3000.SSZzejyD6zOgdh2mtnLQWYQN_RAG01ICFjIXe9fEM8W_c-wbdKjIZtMTuA3NGrU9SvVbe3Wv.1; __zi-legacy=3000.SSZzejyD6zOgdh2mtnLQWYQN_RAG01ICFjIXe9fEM8W_c-wbdKjIZtMTuA3NGrU9SvVbe3Wv.1; ozi=2000.QOBlzDCV2uGerkFzm09Js63GuF350bJSBTJcyuyAKTehtkFpE3a.1; app.event.zalo.me=4669203675082826053"
	imei            = "225c243b-be15-4c9e-ad68-98286df2ac98-a11f5da7336cfe2e2fd950a3d968fdb0" //Should be Unique
	baseURL         = "https://tt-chat2-wpa.chat.zalo.me"
	groupBaseURL    = "https://tt-group-wpa.chat.zalo.me"
	filesBaseURL    = "https://tt-files-wpa.chat.zalo.me"
	userAgent       = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36 Edg/123.0.0.0"
	zpwVer, zpwType = "635", "30"
)

// ZALO AriaDirect: 0906260337 / Ariadirect@123
var secret = "cWjHKoGH7I53nHrbWf17xA=="

func createZcid(zpwType string, uuid string, timestamp string) string {
	//"30,d6062783-7dd0-4d5e-ac57-5cbcff62a264-f1f6b29a6cc1f79a0fea05b885aa33d0,1719898891387"
	combineString := zpwType + "," + uuid + "," + timestamp
	return encodeAESType2("3FC4F0D2AB50057BCE0D90D9187A22B1", combineString, "hex", true)
}
func createZcidExt() string {
	return randomString()
}

// zcid,zcid_ext,enc_ver
func getSignKey(key string, params string, zcid string, zcid_ext string) string {
	return getMD5HashFromString("zsecure" + key + zpwVer + "v2" + params + zpwType + zcid + zcid_ext)
}
func randomString() string {
	length := rand.Intn(7) + 6 // Random length between 6 and 12 (inclusive)

	if length > 12 { // Unnecessary check due to the constraint of the random length
		panic("Invalid length generated. This should never happen.")
	}

	const hexChars = "0123456789abcdef"
	result := make([]byte, length)

	for i := range result {
		result[i] = hexChars[rand.Intn(len(hexChars))]
	}
	return string(result)
}

func encodeAESType2(key string, text string, typ string, uppercase bool) string {
	if text == "" {
		return ""
	}

	// Parse key into bytes
	keyBytes := []byte(key)

	// Parse text into bytes
	textBytes := []byte(text)

	// Create AES cipher block using key
	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		fmt.Printf("Error creating AES cipher: %v\n", err)
		return ""
	}

	// Prepare initialization vector (IV)
	iv := make([]byte, aes.BlockSize)

	// Create AES CBC mode cipher
	mode := cipher.NewCBCEncrypter(block, iv)

	// Pad plaintext to make its length a multiple of the block size
	textBytes = pkcs7Pad(textBytes, aes.BlockSize)

	// Encrypt plaintext
	ciphertext := make([]byte, len(textBytes))
	mode.CryptBlocks(ciphertext, textBytes)

	// Encode ciphertext to specified type
	var encoded string
	switch typ {
	case "hex":
		encoded = hex.EncodeToString(ciphertext)
	case "base64":
		encoded = base64.StdEncoding.EncodeToString(ciphertext)
	default:
		fmt.Printf("Unsupported encoding type: %s\n", typ)
		return ""
	}

	// Convert to uppercase if specified
	if uppercase {
		encoded = uppercaseString(encoded)
	}

	return encoded
}
func decodeAESType2(key, encodedText string) string {
	if encodedText == "" {
		return ""
	}

	// Parse key into bytes
	keyBytes := []byte(key)

	// Decode base64 encoded text
	ciphertext, err := base64.StdEncoding.DecodeString(encodedText)
	if err != nil {
		fmt.Printf("Error decoding base64: %v\n", err)
		return ""
	}

	// Create AES cipher block using key
	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		fmt.Printf("Error creating AES cipher: %v\n", err)
		return ""
	}

	// Prepare initialization vector (IV)
	iv := make([]byte, aes.BlockSize)

	// Create AES CBC mode cipher
	mode := cipher.NewCBCDecrypter(block, iv)

	// Decrypt ciphertext
	plaintext := make([]byte, len(ciphertext))
	mode.CryptBlocks(plaintext, ciphertext)

	// Remove PKCS7 padding
	plaintext = pkcs7Unpad(plaintext, aes.BlockSize)

	return string(plaintext)
}

// uppercaseString converts string to uppercase
func uppercaseString(s string) string {
	return strings.ToUpper(s)
}

func encodeAES(message, secret string) string {
	key, _ := base64.StdEncoding.DecodeString(secret)
	iv, _ := hex.DecodeString("00000000000000000000000000000000")

	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	padded := pkcs7Pad([]byte(message), block.BlockSize())
	ciphertext := make([]byte, len(padded))
	mode.CryptBlocks(ciphertext, padded)

	return base64.StdEncoding.EncodeToString(ciphertext)
}

func decodeAES(ciphertext, secret string) string {
	key, _ := base64.StdEncoding.DecodeString(secret)
	iv, _ := hex.DecodeString("00000000000000000000000000000000")

	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}

	mode := cipher.NewCBCDecrypter(block, iv)
	data, _ := base64.StdEncoding.DecodeString(ciphertext)
	plaintext := make([]byte, len(data))
	mode.CryptBlocks(plaintext, data)

	return string(pkcs7Unpad(plaintext, block.BlockSize()))
}

// processStr processes the string and returns even and odd indexed characters
func processStr(e string) (even []string, odd []string) {
	if e == "" || len(e) == 0 {
		return nil, nil
	}

	for i, ch := range e {
		if i%2 == 0 {
			even = append(even, string(ch))
		} else {
			odd = append(odd, string(ch))
		}
	}

	return even, odd
}
func getEncryptKey(zcidExtMd5, zcid string) string {
	evenE, _ := processStr(zcidExtMd5)
	evenT, oddT := processStr(zcid)

	if evenE == nil || evenT == nil || oddT == nil {
		return ""
	}

	// Slice and concatenate the strings as per the logic
	part1 := strings.Join(evenE, "")
	if len(part1) > 8 {
		part1 = part1[:8]
	}

	part2 := strings.Join(evenT, "")
	if len(part2) > 12 {
		part2 = part2[:12]
	}

	reverseOddT := reverseSlice(oddT)
	part3 := strings.Join(reverseOddT, "")
	if len(part3) > 12 {
		part3 = part3[:12]
	}

	// Concatenate parts to form the final string
	encryptKey := part1 + part2 + part3

	fmt.Println("Encrypted Key:", encryptKey)
	return encryptKey
}

// Helper function to reverse a slice of strings
func reverseSlice(slice []string) []string {
	for i, j := 0, len(slice)-1; i < j; i, j = i+1, j-1 {
		slice[i], slice[j] = slice[j], slice[i]
	}
	return slice
}
func pkcs7Pad(data []byte, blockSize int) []byte {
	padLen := blockSize - (len(data) % blockSize)
	pad := bytes.Repeat([]byte{byte(padLen)}, padLen)
	return append(data, pad...)
}

func pkcs7Unpad(data []byte, blockSize int) []byte {
	padLen := int(data[len(data)-1])
	return data[:len(data)-padLen]
}
func getParamsEncrypted(message any) string {
	jsonData, _ := json.Marshal(message)
	return encodeAES(string(jsonData), getSecrectKey())
}
func getSecrectKey() string {
	if secret == "" {
		// secret is empty get from API
		uuid := imei
		timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
		// timestamp := "1719898891387"
		zcid := createZcid(zpwType, uuid, timestamp)
		zcid_ext := randomString()
		// zcid_ext := "bb8c0d6e9ed1"
		zcidExtMd5 := strings.ToUpper(getMD5HashFromString(zcid_ext))

		jsonData := "{\"imei\":\"" + uuid + "\",\"computer_name\":\"Web\",\"language\":\"vi\",\"ts\":" + strconv.FormatInt(time.Now().UnixMilli(), 10) + "}"
		enk := getEncryptKey(zcidExtMd5, zcid)
		encrypted_data := encodeAESType2(enk, jsonData, "base64", false) //=> Params
		//params : zcid,zcid_ext,enc_ver
		signKey := getSignKey("getlogininfo", encrypted_data, zcid, zcid_ext)
		query := map[string]string{
			"client_version": zpwVer,
			"type":           zpwType,
			"enc_ver":        "v2",
			"nretry":         "0",
			"zcid":           zcid,
			"zcid_ext":       zcid_ext,
			"params":         encrypted_data,
			"signkey":        signKey,
		}
		fmt.Println(query)
		resp, err := resty.New().R().
			SetHeaders(getHeaders()).
			SetQueryParams(query).
			Get("https://wpa.chat.zalo.me/api/login/getLoginInfo")

		if err != nil {
			fmt.Println("Error:", err)
		}
		respDataEncrypted := gjson.Get(resp.String(), "data").String()
		result := decodeAESType2(enk, respDataEncrypted)
		data := cast.ToStringMap(gjson.Parse(result).Get("data").Value())
		// dkey := data["dkey"]
		secret = data["zpw_enk"].(string)
		fmt.Println("New Secrect Key:", secret)
		return secret
	} else {
		return secret
	}
}
func getHeaders() map[string]string {
	return map[string]string{
		"Accept":          "application/json, text/plain, */*",
		"Accept-Language": "en-US,en;q=0.9",
		"Content-Type":    "application/x-www-form-urlencoded",
		"Cookie":          cookie,
		"Origin":          "https://chat.zalo.me",
		"Referer":         "https://chat.zalo.me/",
		"User-Agent":      userAgent,
	}
}

func getFileHeaders(w *multipart.Writer) map[string]string {
	return map[string]string{
		"Accept":          "application/json, text/plain, */*",
		"Accept-Language": "en-US,en;q=0.9",
		"Content-Type":    w.FormDataContentType(),
		"Cookie":          cookie,
		"Origin":          "https://chat.zalo.me",
		"Referer":         "https://chat.zalo.me/",
		"User-Agent":      userAgent,
	}
}

func SendMessageToUser(phoneNumber string, message string) {
	userInfo := GetUserInfo(phoneNumber)
	params := map[string]any{
		"message":  message,
		"clientId": strconv.FormatInt(time.Now().UnixNano(), 10),
		"imei":     imei,
		"ttl":      0,
		"toid":     userInfo["uid"],
	}

	data := map[string]string{
		"params": getParamsEncrypted(params),
	}

	resp, err := resty.New().R().
		SetHeaders(getHeaders()).
		SetFormData(data).
		Post(baseURL + "/api/message/sms?zpw_ver=" + zpwVer + "&zpw_type=" + zpwType + "&nretry=0")

	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	fmt.Println(resp.String())
}

// phone: 84908280493
func GetUserInfo(phone string) map[string]any {
	params := map[string]any{
		"phone":       phone,
		"avatar_size": 240,
		"language":    "vi",
		"imei":        imei,
		"reqSrc":      85,
	}

	resp, err := resty.New().R().
		SetHeaders(getHeaders()).
		SetQueryParams(map[string]string{
			"zpw_ver":  zpwVer,
			"zpw_type": zpwType,
			"params":   getParamsEncrypted(params),
		}).
		Get("https://tt-friend-wpa.chat.zalo.me/api/friend/profile/get")

	if err != nil {
		fmt.Println("Error:", err)
		return nil
	}
	respDataEncrypted := gjson.Get(resp.String(), "data").String()
	respData := decodeAES(respDataEncrypted, getSecrectKey())
	return cast.ToStringMap(gjson.Parse(respData).Get("data").Value())
}

func GetGroupInfo(shareLink string) map[string]any {
	groupName := strings.TrimPrefix(shareLink, "https://zalo.me/g/")
	params := map[string]any{
		"link":               "https://zalo.me/g/" + groupName,
		"avatar_size":        120,
		"member_avatar_size": 120,
		"mpage":              1,
	}

	resp, err := resty.New().R().
		SetHeaders(getHeaders()).
		SetQueryParams(map[string]string{
			"zpw_ver":  zpwVer,
			"zpw_type": zpwType,
			"params":   getParamsEncrypted(params),
		}).
		Get(groupBaseURL + "/api/group/link/ginfo")

	if err != nil {
		fmt.Println("Error:", err)
		return nil
	}
	// fmt.Println(resp.String())
	respDataEncrypted := gjson.Get(resp.String(), "data").String()
	respData := decodeAES(respDataEncrypted, getSecrectKey())
	return cast.ToStringMap(gjson.Parse(respData).Get("data").Value())
}

func SendMessageToGroup(shareLink, message string) {
	groupInfo := GetGroupInfo(shareLink)

	fmt.Println("RAW MESSAGE: ", message)
	textMessage, textProperties, err := parseHTMLToZaloTextProperties(message)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}
	fmt.Println(textMessage)
	fmt.Println(textProperties)
	params := map[string]any{
		"message":    textMessage,
		"clientId":   strconv.FormatInt(time.Now().UnixNano(), 10),
		"imei":       imei,
		"ttl":        0,
		"visibility": 0,
		"grid":       groupInfo["groupId"],
	}
	if textProperties != "" {
		params["textProperties"] = textProperties
	}

	data := map[string]string{
		"params": getParamsEncrypted(params),
	}

	resp, err := resty.New().R().
		SetHeaders(getHeaders()).
		SetFormData(data).
		SetQueryParams(map[string]string{
			"zpw_ver":  zpwVer,
			"zpw_type": zpwType,
		}).
		Post(groupBaseURL + "/api/group/sendmsg")

	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	respDataEncrypted := gjson.Get(resp.String(), "data").String()
	respData := decodeAES(respDataEncrypted, getSecrectKey())
	fmt.Println(cast.ToStringMap(gjson.Parse(respData).Get("data").Value()))
}

func SendImageToGroup(shareLink string, imageBuff []byte) {
	groupInfo := GetGroupInfo(shareLink)

	bodyBuff := &bytes.Buffer{}
	writer := multipart.NewWriter(bodyBuff)
	part, _ := writer.CreateFormFile("chunkContent", "image.jpg")
	part.Write(imageBuff)

	params := map[string]any{
		"totalChunk": 1,
		"fileName":   "image.jpg",
		"clientId":   strconv.FormatInt(time.Now().UnixNano(), 10),
		"totalSize":  len(imageBuff),
		"imei":       imei,
		"grid":       groupInfo["groupId"],
		"isE2EE":     0,
		"chunkId":    1,
	}

	writer.Close()

	resp, err := resty.New().R().
		SetHeaders(getFileHeaders(writer)).
		SetBody(bodyBuff.Bytes()).
		SetQueryParams(map[string]string{
			"zpw_ver":  zpwVer,
			"zpw_type": zpwType,
			"type":     "11",
			"params":   getParamsEncrypted(params),
		}).
		Post(filesBaseURL + "/api/group/photo_original/upload")

	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	respDataEncrypted := gjson.Get(resp.String(), "data").String()
	respData := gjson.Parse(decodeAES(respDataEncrypted, getSecrectKey())).Get("data")
	fmt.Println(respData.String())
	params2 := map[string]any{
		"photoId":  respData.Get("clientFileId").Value(),
		"clientId": strconv.FormatInt(time.Now().UnixNano(), 10),
		"desc":     "",
		"grid":     groupInfo["groupId"],
		"rawUrl":   respData.Get("hdUrl").Value(),
		"thumbUrl": respData.Get("thumbUrl").Value(),
		"oriUrl":   respData.Get("normalUrl").Value(),
		"hdUrl":    respData.Get("hdUrl").Value(),
		"zsource":  -1,
		"jcp":      "{\"sendSource\":1}",
		"ttl":      0,
		"imei":     imei,
	}

	resp2, err := resty.New().R().
		SetHeaders(getHeaders()).
		SetFormData(map[string]string{
			"params": getParamsEncrypted(params2),
		}).
		Post(filesBaseURL + "/api/group/photo_original/send?zpw_ver=" + zpwVer + "&zpw_type=" + zpwType + "&nretry=0")

	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	resp2DataEncrypted := gjson.Get(resp2.String(), "data").String()
	resp2Data := gjson.Parse(decodeAES(resp2DataEncrypted, getSecrectKey())).Get("data")
	fmt.Println(resp2Data.String())
}

func SendImageToUser(phoneNumber string, imageBuff []byte) {
	userInfo := GetUserInfo(phoneNumber)
	bodyBuff := &bytes.Buffer{}
	writer := multipart.NewWriter(bodyBuff)
	part, _ := writer.CreateFormFile("chunkContent", "image.jpg")
	part.Write(imageBuff)
	writer.Close()

	params := map[string]any{
		"totalChunk": 1,
		"fileName":   "image.jpg",
		"clientId":   strconv.FormatInt(time.Now().UnixNano(), 10),
		"totalSize":  len(imageBuff),
		"imei":       imei,
		"toid":       userInfo["uid"],
		"isE2EE":     0,
		"chunkId":    1,
	}
	resp, err := resty.New().R().
		SetHeaders(getFileHeaders(writer)).
		SetBody(bodyBuff.Bytes()).
		SetQueryParams(map[string]string{
			"zpw_ver":  zpwVer,
			"zpw_type": zpwType,
			"type":     "2",
			"params":   getParamsEncrypted(params),
		}).
		Post(filesBaseURL + "/api/message/photo_original/upload")

	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	respDataEncrypted := gjson.Get(resp.String(), "data").String()
	respData := gjson.Parse(decodeAES(respDataEncrypted, getSecrectKey())).Get("data")
	fmt.Println(respData)

	params2 := map[string]any{
		"clientId":  strconv.FormatInt(time.Now().UnixNano(), 10),
		"desc":      "",
		"hdUrl":     respData.Get("hdUrl").Value(),
		"imei":      imei,
		"jcp":       "{\"sendSource\":1}",
		"normalUrl": respData.Get("normalUrl").Value(),
		"photoId":   respData.Get("photoId").Value(),
		"rawUrl":    respData.Get("normalUrl").Value(),
		"thumbSize": "7068",
		"thumbUrl":  respData.Get("thumbUrl").Value(),
		"toid":      userInfo["uid"],
		"ttl":       0,
		"zsource":   -1,
	}

	resp2, err := resty.New().R().
		SetHeaders(getHeaders()).
		SetFormData(map[string]string{
			"params": getParamsEncrypted(params2),
		}).
		Post(filesBaseURL + "/api/message/photo_original/send?zpw_ver=" + zpwVer + "&zpw_type=" + zpwType + "&nretry=0")

	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	fmt.Println(resp2.String())
}
func getMD5Hash(data []byte) string {
	hash := md5.New()
	hash.Write(data)

	return fmt.Sprintf("%x", hash.Sum(nil))
}
func getMD5HashFromString(data string) string {
	hash := md5.New()
	hash.Write([]byte(data))

	return fmt.Sprintf("%x", hash.Sum(nil))
}
func getFileExtension(fileName string) string {
	fileExtension := strings.TrimPrefix(filepath.Ext(fileName), ".")
	return strings.ToUpper(fileExtension)
}

const ChunkSize = 3145728 // 3 MB

func SendFileToGroup(shareLink string, fileName string, fileBuff []byte) {
	groupInfo := GetGroupInfo(shareLink)
	fileExtension := getFileExtension(fileName)
	totalSize := len(fileBuff)
	totalChunks := (totalSize + ChunkSize - 1) / ChunkSize // Calculate total chunks
	client := resty.New()

	var chunkParams map[string]any

	uploadURL := filesBaseURL + "/api/group/asyncfile/upload"
	clientId := strconv.FormatInt(time.Now().UnixMilli(), 10)
	for chunkID := 1; chunkID <= totalChunks; chunkID++ {
		start := (chunkID - 1) * ChunkSize
		end := chunkID * ChunkSize
		if end > totalSize {
			end = totalSize
		}
		chunk := fileBuff[start:end]

		bodyBuff := &bytes.Buffer{}
		writer := multipart.NewWriter(bodyBuff)
		part, err := writer.CreateFormFile("chunkContent", fileName)
		if err != nil {
			fmt.Println("Error creating form file:", err)
			return
		}
		part.Write(chunk)
		writer.Close()
		params := map[string]any{
			"totalChunk": totalChunks,
			"fileName":   fileName,
			"clientId":   clientId,
			"totalSize":  totalSize,
			"imei":       imei,
			"grid":       groupInfo["groupId"],
			"isE2EE":     0,
			"jxl":        0,
			"chunkId":    chunkID,
		}
		encryptedParams := getParamsEncrypted(params)
		headers := getFileHeaders(writer)
		queryParams := map[string]string{
			"zpw_ver":  zpwVer,
			"zpw_type": zpwType,
			"type":     "11",
			"params":   encryptedParams,
		}
		resp, err := resty.New().R().
			SetHeaders(headers).
			SetBody(bodyBuff.Bytes()).
			SetQueryParams(queryParams).
			Post(uploadURL)
		if err != nil {
			fmt.Println("Error during chunk upload:", err)
			return
		}

		respDataEncrypted := gjson.Get(resp.String(), "data").String()
		respData := gjson.Parse(decodeAES(respDataEncrypted, getSecrectKey())).Get("data")
		fmt.Println("Response Data for Chunk", chunkID, ":", respData.String())

		fileId := respData.Get("fileId").String()
		if fileId != "-1" {
			chunkParams = params
		}

	}

	// get file Url
	bodyBuff := &bytes.Buffer{}
	writer := multipart.NewWriter(bodyBuff)
	part, err := writer.CreateFormFile("chunkContent", fileName)
	if err != nil {
		fmt.Println("Error creating form file:", err)
		return
	}
	part.Write(nil)
	writer.Close()

	encryptedParams := getParamsEncrypted(chunkParams)
	headers := getFileHeaders(writer)

	resp, err := client.R().
		SetHeaders(headers).
		SetBody(bodyBuff.Bytes()).
		SetQueryParams(map[string]string{
			"zpw_ver":  zpwVer,
			"zpw_type": zpwType,
			"type":     "11",
			"params":   encryptedParams,
		}).
		Post(uploadURL)
	if err != nil {
		fmt.Println("Error during re-upload of chunk:", err)
		return
	}

	respDataEncrypted := gjson.Get(resp.String(), "data").String()
	respData := gjson.Parse(decodeAES(respDataEncrypted, getSecrectKey())).Get("data")

	fmt.Println("Response Data for re-uploaded chunk:", decodeAES(respDataEncrypted, getSecrectKey()))

	// Send Message
	finalParams := map[string]any{
		"fileId":      respData.Get("fileId").Value(),
		"checksum":    getMD5Hash(fileBuff),
		"checksumSha": "",
		"extension":   fileExtension,
		"totalSize":   totalSize,
		"fileName":    fileName,
		"clientId":    clientId,
		"fType":       1,
		"fdata":       "{}",
		"fileCount":   0,
		"fileUrl":     respData.Get("url").Value(),
		"grid":        groupInfo["groupId"],
		"zsource":     401,
		"ttl":         0,
	}
	resp3, err := client.R().
		SetHeaders(getHeaders()).
		SetFormData(map[string]string{
			"params": getParamsEncrypted(finalParams),
		}).
		Post(filesBaseURL + "/api/group/asyncfile/msg?zpw_ver=" + zpwVer + "&zpw_type=" + zpwType + "&nretry=0")

	if err != nil {
		fmt.Println("Error during finalization:", err)
		return
	}

	resp3DataEncrypted := gjson.Get(resp3.String(), "data").String()
	fmt.Println("Final Response Data:", decodeAES(resp3DataEncrypted, getSecrectKey()))
}
func SendFileToUser(phoneNumber string, fileName string, fileBuff []byte) {
	userInfo := GetUserInfo(phoneNumber)
	fileExtension := getFileExtension(fileName)
	totalSize := len(fileBuff)
	totalChunks := (totalSize + ChunkSize - 1) / ChunkSize // Calculate total chunks
	client := resty.New()

	var chunkParams map[string]any

	uploadURL := filesBaseURL + "/api/message/asyncfile/upload" // Update for user message
	clientId := strconv.FormatInt(time.Now().UnixMilli(), 10)

	for chunkID := 1; chunkID <= totalChunks; chunkID++ {
		start := (chunkID - 1) * ChunkSize
		end := chunkID * ChunkSize
		if end > totalSize {
			end = totalSize
		}
		chunk := fileBuff[start:end]

		bodyBuff := &bytes.Buffer{}
		writer := multipart.NewWriter(bodyBuff)
		part, err := writer.CreateFormFile("chunkContent", fileName)
		if err != nil {
			fmt.Println("Error creating form file:", err)
			return
		}
		part.Write(chunk)
		writer.Close()

		params := map[string]any{
			"totalChunk": totalChunks,
			"fileName":   fileName,
			"clientId":   clientId,
			"totalSize":  totalSize,
			"imei":       imei,
			"toid":       userInfo["uid"],
			"isE2EE":     0,
			"jxl":        0,
			"chunkId":    chunkID,
		}
		encryptedParams := getParamsEncrypted(params)
		headers := getFileHeaders(writer)
		queryParams := map[string]string{
			"zpw_ver":  zpwVer,
			"zpw_type": zpwType,
			"type":     "11",
			"params":   encryptedParams,
		}
		// Send the chunk
		resp, err := client.R().SetHeaders(headers).SetBody(bodyBuff.Bytes()).SetQueryParams(queryParams).Post(uploadURL)
		if err != nil {
			fmt.Println("Error during chunk upload:", err)
			return
		}

		respDataEncrypted := gjson.Get(resp.String(), "data").String()
		respData := gjson.Parse(decodeAES(respDataEncrypted, getSecrectKey())).Get("data")

		fmt.Println("Response Data for Chunk", chunkID, ":", respData.String())

		fileId := respData.Get("fileId").String()
		if fileId != "-1" {
			chunkParams = params
		}
	}

	// Re-upload Chunk to get Url
	bodyBuff := &bytes.Buffer{}
	writer := multipart.NewWriter(bodyBuff)
	part, err := writer.CreateFormFile("chunkContent", fileName)
	if err != nil {
		fmt.Println("Error creating form file:", err)
		return
	}
	part.Write(nil)
	writer.Close()

	encryptedParams := getParamsEncrypted(chunkParams)
	headers := getFileHeaders(writer)

	resp, err := client.R().
		SetHeaders(headers).
		SetBody(bodyBuff.Bytes()).
		SetQueryParams(map[string]string{
			"zpw_ver":  zpwVer,
			"zpw_type": zpwType,
			"type":     "11",
			"params":   encryptedParams,
		}).
		Post(uploadURL)
	if err != nil {
		fmt.Println("Error during re-upload of chunk:", err)
		return
	}

	respDataEncrypted := gjson.Get(resp.String(), "data").String()
	respData := gjson.Parse(decodeAES(respDataEncrypted, getSecrectKey())).Get("data")

	fmt.Println("Response Data for re-uploaded chunk:", decodeAES(respDataEncrypted, getSecrectKey()))

	// Finalize the upload (Send the message)
	finalParams := map[string]any{
		"fileId":      respData.Get("fileId").Value(),
		"checksum":    getMD5Hash(fileBuff),
		"checksumSha": "",
		"extension":   fileExtension,
		"totalSize":   totalSize,
		"fileName":    fileName,
		"clientId":    clientId,
		"fType":       1,
		"fdata":       "{}",
		"fileCount":   0,
		"fileUrl":     respData.Get("url").Value(),
		"toid":        userInfo["uid"],
		"zsource":     403,
		"ttl":         0,
	}

	resp3, err := client.R().
		SetHeaders(getHeaders()).
		SetFormData(map[string]string{
			"params": getParamsEncrypted(finalParams),
		}).
		Post(filesBaseURL + "/api/message/asyncfile/msg?zpw_ver=" + zpwVer + "&zpw_type=" + zpwType + "&nretry=0")

	if err != nil {
		fmt.Println("Error during finalization:", err)
		return
	}

	resp3DataEncrypted := gjson.Get(resp3.String(), "data").String()
	fmt.Println("Final Response Data:", decodeAES(resp3DataEncrypted, getSecrectKey()))
}
