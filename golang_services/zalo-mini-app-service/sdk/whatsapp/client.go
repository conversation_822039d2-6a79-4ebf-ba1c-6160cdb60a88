package whatsapp

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	_ "github.com/mattn/go-sqlite3"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/proto/waE2E"
	"go.mau.fi/whatsmeow/store/sqlstore"
	"go.mau.fi/whatsmeow/types"
	"google.golang.org/protobuf/proto"
	protolib "google.golang.org/protobuf/proto"
)

// GroupAddError is a custom error type that includes the invite link when direct adding fails
type GroupAddError struct {
	OriginalError error
	InviteLink    string
	Result        []types.GroupParticipant
}

// Error implements the error interface
func (e *GroupAddError) Error() string {
	if e.OriginalError != nil {
		return fmt.Sprintf("failed to add participants directly: %v (invite link available)", e.OriginalError)
	}
	return "failed to add some participants directly (invite link available)"
}

// WhatsAppClient represents a WhatsApp client instance
type WhatsAppClient struct {
	Client *whatsmeow.Client
	mutex  sync.Mutex
}

var (
	instance *WhatsAppClient
	once     sync.Once
)

// GetInstance returns the singleton instance of WhatsAppClient
func GetInstance() *WhatsAppClient {
	once.Do(func() {
		instance = newWhatsAppClient()
	})
	return instance
}

// newWhatsAppClient creates a new WhatsApp client
func newWhatsAppClient() *WhatsAppClient {
	dbPath := "whatsmeow.db"

	// Set up database connection
	db, err := sqlstore.New("sqlite3", fmt.Sprintf("file:%s?_foreign_keys=on", dbPath), nil)
	if err != nil {
		log.Fatalf("Failed to initialize DB: %v", err)
	}

	// Get device store
	deviceStore, err := db.GetFirstDevice()
	if err != nil {
		log.Fatalf("Failed to get device: %v", err)
	}

	// Create client
	client := whatsmeow.NewClient(deviceStore, nil)

	// Set up event handler
	client.AddEventHandler(func(evt interface{}) {
		fmt.Println(utils.StructToJSON(evt).Raw)
		// switch v := evt.(type) {
		// case *events.Message:
		// 	fmt.Printf("Received message: %v\n", v)
		// case *events.Connected:
		// 	fmt.Println("Connected to WhatsApp")
		// case *events.Disconnected:
		// 	fmt.Println("Disconnected from WhatsApp")
		// default:
		// 	fmt.Printf("Received event: %v\n", v)
		// }
	})

	// Connect to WhatsApp
	if client.Store.ID == nil {
		// No ID stored, need to pair
		qrChan, _ := client.GetQRChannel(context.Background())
		err = client.Connect()
		if err != nil {
			log.Fatalf("Failed to connect to WhatsApp: %v", err)
		}

		for evt := range qrChan {
			if evt.Event == "code" {
				fmt.Printf("Scan this QR code with WhatsApp: %s\n", evt.Code)
			} else {
				fmt.Printf("Login event: %s\n", evt.Event)
			}
		}
	} else {
		// Already paired, just connect
		err = client.Connect()
		if err != nil {
			log.Fatalf("Failed to connect to WhatsApp: %v", err)
		}
	}

	return &WhatsAppClient{
		Client: client,
	}
}

// IsOnWhatsApp checks if a phone number is registered on WhatsApp
func (wc *WhatsAppClient) IsOnWhatsApp(phoneNumber string) (*types.IsOnWhatsAppResponse, error) {
	wc.mutex.Lock()
	defer wc.mutex.Unlock()

	// Format phone number for WhatsApp
	if phoneNumber[0] == '+' {
		phoneNumber = phoneNumber[1:]
	}

	jid := types.NewJID(phoneNumber, types.DefaultUserServer)
	resp, err := wc.Client.IsOnWhatsApp([]string{jid.String()})
	if err != nil {
		return nil, err
	}

	if len(resp) > 0 && resp[0].IsIn {
		return &resp[0], nil
	}

	return nil, nil
}

// GetUserInfo retrieves information about a WhatsApp user
func (wc *WhatsAppClient) GetUserInfo(jid types.JID) (*types.UserInfo, error) {
	wc.mutex.Lock()
	defer wc.mutex.Unlock()

	userInfo, err := wc.Client.GetUserInfo([]types.JID{jid})
	if err != nil {
		return nil, err
	}

	if info, ok := userInfo[jid]; ok {
		return &info, nil
	}

	return nil, fmt.Errorf("user info not found")
}

// GetProfilePicture retrieves the profile picture of a WhatsApp user
func (wc *WhatsAppClient) GetProfilePicture(jid types.JID) (*types.ProfilePictureInfo, error) {
	wc.mutex.Lock()
	defer wc.mutex.Unlock()

	pic, err := wc.Client.GetProfilePictureInfo(jid, &whatsmeow.GetProfilePictureParams{})
	if err != nil {
		return nil, err
	}

	return pic, nil
}

// SendMessage sends a text message to a WhatsApp user
func (wc *WhatsAppClient) SendMessage(jid types.JID, message string) (string, error) {
	wc.mutex.Lock()
	defer wc.mutex.Unlock()

	msg := &waE2E.Message{
		Conversation: &message,
	}

	resp, err := wc.Client.SendMessage(context.Background(), jid, msg)
	if err != nil {
		return "", err
	}

	return resp.ID, nil
}

// SendFile sends a file to a WhatsApp user
func (wc *WhatsAppClient) SendFile(jid types.JID, fileName string, fileData []byte, caption string) (string, error) {
	wc.mutex.Lock()
	defer wc.mutex.Unlock()

	// Determine message type based on file extension
	ext := filepath.Ext(fileName)
	var uploaded whatsmeow.UploadResponse
	var err error
	var msg *waE2E.Message

	switch ext {
	case ".jpg", ".jpeg", ".png", ".gif":
		uploaded, err = wc.Client.Upload(context.Background(), fileData, whatsmeow.MediaImage)
		if err != nil {
			return "", fmt.Errorf("failed to upload image: %v", err)
		}
		msg = &waE2E.Message{
			ImageMessage: &waE2E.ImageMessage{
				URL:           &uploaded.URL,
				Mimetype:      protolib.String(http.DetectContentType(fileData)),
				Caption:       &caption,
				FileLength:    &uploaded.FileLength,
				FileSHA256:    uploaded.FileSHA256,
				FileEncSHA256: uploaded.FileEncSHA256,
				MediaKey:      uploaded.MediaKey,
			},
		}
	case ".mp4", ".mov", ".avi":
		uploaded, err = wc.Client.Upload(context.Background(), fileData, whatsmeow.MediaVideo)
		if err != nil {
			return "", fmt.Errorf("failed to upload video: %v", err)
		}
		msg = &waE2E.Message{
			VideoMessage: &waE2E.VideoMessage{
				URL:           &uploaded.URL,
				Mimetype:      protolib.String(http.DetectContentType(fileData)),
				Caption:       &caption,
				FileLength:    &uploaded.FileLength,
				FileSHA256:    uploaded.FileSHA256,
				FileEncSHA256: uploaded.FileEncSHA256,
				MediaKey:      uploaded.MediaKey,
			},
		}
	case ".mp3", ".ogg", ".wav":
		uploaded, err = wc.Client.Upload(context.Background(), fileData, whatsmeow.MediaAudio)
		if err != nil {
			return "", fmt.Errorf("failed to upload audio: %v", err)
		}
		msg = &waE2E.Message{
			AudioMessage: &waE2E.AudioMessage{
				URL:           &uploaded.URL,
				Mimetype:      protolib.String(http.DetectContentType(fileData)),
				FileLength:    &uploaded.FileLength,
				FileSHA256:    uploaded.FileSHA256,
				FileEncSHA256: uploaded.FileEncSHA256,
				MediaKey:      uploaded.MediaKey,
			},
		}
	default:
		uploaded, err = wc.Client.Upload(context.Background(), fileData, whatsmeow.MediaDocument)
		if err != nil {
			return "", fmt.Errorf("failed to upload document: %v", err)
		}
		msg = &waE2E.Message{
			DocumentMessage: &waE2E.DocumentMessage{
				URL:           &uploaded.URL,
				Mimetype:      protolib.String(http.DetectContentType(fileData)),
				Title:         &fileName,
				FileName:      &fileName,
				Caption:       &caption,
				FileLength:    &uploaded.FileLength,
				FileSHA256:    uploaded.FileSHA256,
				FileEncSHA256: uploaded.FileEncSHA256,
				MediaKey:      uploaded.MediaKey,
			},
		}
	}

	resp, err := wc.Client.SendMessage(context.Background(), jid, msg)
	if err != nil {
		return "", err
	}

	return resp.ID, nil
}

// CreateGroup creates a new WhatsApp group
func (wc *WhatsAppClient) CreateGroup(name string, participants []string) (*types.GroupInfo, error) {
	wc.mutex.Lock()
	defer wc.mutex.Unlock()

	// Convert phone numbers to JIDs
	var jids []types.JID
	for _, participant := range participants {
		if participant[0] == '+' {
			participant = participant[1:]
		}
		jid := types.NewJID(participant, types.DefaultUserServer)
		jids = append(jids, jid)
	}

	// Create the group
	req := whatsmeow.ReqCreateGroup{
		Name:         name,
		Participants: jids,
	}
	group, err := wc.Client.CreateGroup(req)
	if err != nil {
		return nil, err
	}

	return group, nil
}

// SendMessageToGroup sends a text message to a WhatsApp group
func (wc *WhatsAppClient) SendMessageToGroup(groupID string, message string) (string, error) {
	wc.mutex.Lock()
	defer wc.mutex.Unlock()

	groupJID := groupID
	if !strings.Contains(groupID, "@") {
		groupJID = groupID + "@g.us"
	}

	// Convert group ID to JID
	groupJIDObj := types.NewJID(strings.Split(groupJID, "@")[0], types.GroupServer)

	msg := &waE2E.Message{
		Conversation: proto.String(message),
	}
	mentions := []string{}
	re := regexp.MustCompile(`@(\d+)`)
	matches := re.FindAllStringSubmatch(message, -1)
	for _, match := range matches {
		if len(match) > 1 {
			mentions = append(mentions, match[1])
		}
	}

	mentionParticipants := []string{}
	for _, phone := range mentions {
		if phone == "" {
			continue
		}

		phone = strings.TrimPrefix(phone, "+")

		jid := types.NewJID(phone, types.DefaultUserServer)
		mentionParticipants = append(mentionParticipants, jid.String())
	}

	if len(mentionParticipants) > 0 {
		msg.Conversation = nil
		msg.ExtendedTextMessage = &waE2E.ExtendedTextMessage{
			Text: proto.String(message),
			ContextInfo: &waE2E.ContextInfo{
				MentionedJID: mentionParticipants,
			},
		}
	}

	resp, err := wc.Client.SendMessage(context.Background(), groupJIDObj, msg)
	if err != nil {
		return "", err
	}

	return resp.ID, nil
}

// SendFileToGroup sends a file to a WhatsApp group
func (wc *WhatsAppClient) SendFileToGroup(groupID string, fileName string, fileData []byte, caption string) (string, error) {
	wc.mutex.Lock()
	defer wc.mutex.Unlock()

	jid := types.NewJID(groupID, types.GroupServer)

	// Determine message type based on file extension
	ext := filepath.Ext(fileName)
	var uploaded whatsmeow.UploadResponse
	var err error
	var msg *waE2E.Message

	switch ext {
	case ".jpg", ".jpeg", ".png", ".gif":
		uploaded, err = wc.Client.Upload(context.Background(), fileData, whatsmeow.MediaImage)
		if err != nil {
			return "", fmt.Errorf("failed to upload image: %v", err)
		}
		msg = &waE2E.Message{
			ImageMessage: &waE2E.ImageMessage{
				URL:           &uploaded.URL,
				Mimetype:      protolib.String(http.DetectContentType(fileData)),
				Caption:       &caption,
				FileLength:    &uploaded.FileLength,
				FileSHA256:    uploaded.FileSHA256,
				FileEncSHA256: uploaded.FileEncSHA256,
				MediaKey:      uploaded.MediaKey,
			},
		}
	case ".mp4", ".mov", ".avi":
		uploaded, err = wc.Client.Upload(context.Background(), fileData, whatsmeow.MediaVideo)
		if err != nil {
			return "", fmt.Errorf("failed to upload video: %v", err)
		}
		msg = &waE2E.Message{
			VideoMessage: &waE2E.VideoMessage{
				URL:           &uploaded.URL,
				Mimetype:      protolib.String(http.DetectContentType(fileData)),
				Caption:       &caption,
				FileLength:    &uploaded.FileLength,
				FileSHA256:    uploaded.FileSHA256,
				FileEncSHA256: uploaded.FileEncSHA256,
				MediaKey:      uploaded.MediaKey,
			},
		}
	case ".mp3", ".ogg", ".wav":
		uploaded, err = wc.Client.Upload(context.Background(), fileData, whatsmeow.MediaAudio)
		if err != nil {
			return "", fmt.Errorf("failed to upload audio: %v", err)
		}
		msg = &waE2E.Message{
			AudioMessage: &waE2E.AudioMessage{
				URL:           &uploaded.URL,
				Mimetype:      protolib.String(http.DetectContentType(fileData)),
				FileLength:    &uploaded.FileLength,
				FileSHA256:    uploaded.FileSHA256,
				FileEncSHA256: uploaded.FileEncSHA256,
				MediaKey:      uploaded.MediaKey,
			},
		}
	default:
		uploaded, err = wc.Client.Upload(context.Background(), fileData, whatsmeow.MediaDocument)
		if err != nil {
			return "", fmt.Errorf("failed to upload document: %v", err)
		}
		msg = &waE2E.Message{
			DocumentMessage: &waE2E.DocumentMessage{
				URL:           &uploaded.URL,
				Mimetype:      protolib.String(http.DetectContentType(fileData)),
				Title:         &fileName,
				FileName:      &fileName,
				Caption:       &caption,
				FileLength:    &uploaded.FileLength,
				FileSHA256:    uploaded.FileSHA256,
				FileEncSHA256: uploaded.FileEncSHA256,
				MediaKey:      uploaded.MediaKey,
			},
		}
	}

	resp, err := wc.Client.SendMessage(context.Background(), jid, msg)
	if err != nil {
		return "", err
	}

	return resp.ID, nil
}

// AddUsersToGroup adds users to an existing WhatsApp group
func (wc *WhatsAppClient) AddUsersToGroup(groupID string, phoneNumbers []string) error {
	if groupID == "" {
		return fmt.Errorf("groupID cannot be empty")
	}
	if len(phoneNumbers) == 0 {
		return fmt.Errorf("phoneNumbers cannot be empty")
	}

	wc.mutex.Lock()
	defer wc.mutex.Unlock()

	// Convert phone numbers to JIDs
	participants := make([]types.JID, 0, len(phoneNumbers))
	for _, phone := range phoneNumbers {
		if phone == "" {
			continue // Skip empty phone numbers
		}

		// Normalize phone number - IMPORTANT: WhatsApp JIDs should NOT have the + prefix
		phone = strings.TrimPrefix(phone, "+")

		jid := types.NewJID(phone, types.DefaultUserServer)
		participants = append(participants, jid)
	}

	if len(participants) == 0 {
		return fmt.Errorf("no valid phone numbers provided")
	}

	// Use structured logging instead of fmt.Printf
	log.Printf("Adding %d participants to group %s", len(participants), groupID)

	// Ensure group ID is in the correct format
	var groupJID types.JID
	if strings.Contains(groupID, "@g.us") {
		var err error
		groupJID, err = types.ParseJID(groupID)
		if err != nil {
			return fmt.Errorf("invalid group ID: %w", err)
		}
	} else {
		groupJID = types.NewJID(groupID, types.GroupServer)
	}

	if groupJID.Server != types.GroupServer {
		return fmt.Errorf("invalid group ID server: %s", groupJID.Server)
	}

	// Try to add participants directly
	result, err := wc.Client.UpdateGroupParticipants(groupJID, participants, whatsmeow.ParticipantChangeAdd)

	// Check for errors in the result even if the API call succeeded
	hasErrors := false
	if result != nil {
		for _, participant := range result {
			if participant.Error != 0 {
				hasErrors = true
				log.Printf("Error adding participant %s: Error code %d", participant.JID.String(), participant.Error)
			}
		}
	}

	// If there was an error or errors in the result, try to get the invite link as a fallback
	if err != nil || hasErrors {
		log.Printf("Direct add failed or had errors: %v. Attempting to get invite link as fallback.", err)

		// Try to get the invite link
		inviteLink, inviteErr := wc.Client.GetGroupInviteLink(groupJID, false)
		if inviteErr != nil {
			log.Printf("Failed to get invite link: %v", inviteErr)
			// Return the original error if we couldn't get an invite link
			if err != nil {
				return fmt.Errorf("failed to add participants and couldn't get invite link: %w", err)
			}
		}

		// Return a custom error with the invite link
		if inviteLink != "" {
			return &GroupAddError{
				OriginalError: err,
				InviteLink:    inviteLink,
				Result:        result,
			}
		}
	}

	// Log the result
	log.Printf("Group update result: %+v", result)
	return nil
}

// RemoveUsersFromGroup removes users from an existing WhatsApp group
func (wc *WhatsAppClient) RemoveUsersFromGroup(groupID string, phoneNumbers []string) error {
	wc.mutex.Lock()
	defer wc.mutex.Unlock()

	// Ensure group ID is in the correct format (<EMAIL>)
	groupJID := groupID
	if !strings.Contains(groupID, "@") {
		groupJID = groupID + "@g.us"
	}

	// Convert group ID to JID
	groupJIDObj := types.NewJID(strings.Split(groupJID, "@")[0], types.GroupServer)

	// Convert phone numbers to JIDs
	var participants []types.JID
	for _, phone := range phoneNumbers {
		// Remove + prefix if present
		if phone[0] == '+' {
			phone = phone[1:]
		}
		jid := types.NewJID(phone, types.DefaultUserServer)
		participants = append(participants, jid)
	}

	// Log the operation for debugging
	fmt.Printf("Removing participants from group %s: %v\n", groupJIDObj.String(), participants)

	// Create a context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Remove participants from the group with timeout context
	resultChan := make(chan struct {
		result []types.GroupParticipant
		err    error
	})

	go func() {
		result, err := wc.Client.UpdateGroupParticipants(groupJIDObj, participants, whatsmeow.ParticipantChangeRemove)
		resultChan <- struct {
			result []types.GroupParticipant
			err    error
		}{result, err}
	}()

	// Wait for either the operation to complete or the context to timeout
	select {
	case res := <-resultChan:
		if res.err != nil {
			fmt.Printf("Error removing participants from group: %v\n", res.err)
		} else {
			fmt.Printf("Successfully removed participants from group: %v\n", res.result)
		}
		return res.err
	case <-ctx.Done():
		return fmt.Errorf("operation timed out after 30 seconds: %v", ctx.Err())
	}
}

// GetJoinedGroups returns a list of groups the user is participating in
func (wc *WhatsAppClient) GetJoinedGroups() ([]*types.GroupInfo, error) {
	wc.mutex.Lock()
	defer wc.mutex.Unlock()

	// Call the whatsmeow client's GetJoinedGroups method
	groups, err := wc.Client.GetJoinedGroups()
	if err != nil {
		return nil, fmt.Errorf("failed to get joined groups: %v", err)
	}

	return groups, nil
}

// Disconnect disconnects from WhatsApp
func (wc *WhatsAppClient) Disconnect() {
	wc.mutex.Lock()
	defer wc.mutex.Unlock()

	wc.Client.Disconnect()
}
