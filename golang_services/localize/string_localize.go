package localize

import (
	"io/ioutil"

	"gopkg.in/yaml.v2"
)

const (
	englishLanguage    = "en"
	chineseLanguage    = "zh"
	vietnameseLanguage = "vi"
)

type Localizes struct {
	Localize []LocalizeLanguage `yaml:"localize"`
}

type LocalizeLanguage struct {
	Language       string            `yaml:"language"`
	Purpose        map[string]string `yaml:"purpose"`
	ProcessingTime map[string]string `yaml:"processingTime"`
	Gender         map[string]string `yaml:"gender"`
	Duration       map[string]string `yaml:"duration"`
	VisaType       map[string]string `yaml:"visaType"`
	NumberOfEntry  map[string]string `yaml:"numberOfEntry"`
	MaritalStatus  map[string]string `yaml:"maritalStatus"`
	Country        map[string]string `yaml:"country"`
	OrderStatus    map[string]string `yaml:"orderStatus"`
	State          map[string]string `yaml:"state"`
	ServiceType    map[string]string `yaml:"serviceType"`
	Tasks          map[string]string `yaml:"tasks"`
	Terminal       map[string]string `yaml:"terminal"`
	Airport        map[string]string `yaml:"airport"`
	SubmitMethod   map[string]string `yaml:"submitMethod"`
	PickupMethod   map[string]string `yaml:"pickupMethod"`
	PassportType   map[string]string `yaml:"passportType"`
	DocumentType   map[string]string `yaml:"documentType"`
	Tag            map[string]string `yaml:"tag"`
	Form           map[string]string `yaml:"form"`
	Product        map[string]string `yaml:"product"`
}

type Localize struct {
	EN LocalizeLanguage `yaml:"en"`
	ZH LocalizeLanguage `yaml:"zh"`
	VI LocalizeLanguage `yaml:"vi"`
}

func ReadStringLocalizeMap(location string) (*Localize, error) {
	byt, err := ioutil.ReadFile(location)
	if err != nil {
		return nil, err
	}
	var c Localizes
	if err = yaml.Unmarshal(byt, &c); err != nil {
		return nil, err
	}

	var result Localize
	for _, item := range c.Localize {
		if item.Language == englishLanguage {
			result.EN = item
		}
		if item.Language == chineseLanguage {
			result.ZH = item
		}
		if item.Language == vietnameseLanguage {
			result.VI = item
		}
	}
	return &result, nil
}
