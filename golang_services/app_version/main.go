package main

import (
	"log"
	"os"

	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
)

func main() {
	app := cli.NewApp()
	app.Name = "aria-app-version"
	app.Usage = "App versoiin for Aria"
	app.Commands = []cli.Command{
		appVersionCmd(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}
}

func appVersionCmd() cli.Command {
	return cli.Command{
		Name:      "service",
		ShortName: "svc",
		Usage:     "Start packages service for Aria",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:   "port",
				Usage:  "Port the server listens to",
				EnvVar: "PORT",
				Value:  "3000",
			},
			cli.StringFlag{
				Name:     "version-file-bucket",
				Usage:    "S3 bucket of file for app versions",
				EnvVar:   "VERSION_FILE_BUCKET",
				Required: true,
			},
			cli.StringFlag{
				Name:     "version-file-key",
				Usage:    "s3 key of file for app version",
				EnvVar:   "VERSION_FILE_KEY",
				Required: true,
			},
			cli.StringFlag{
				Name:     "s3-region",
				Usage:    "AWS S3 region of this service",
				EnvVar:   "S3_REGION",
				Required: true,
			},
			flags.LogLevelFlag,
		},
		Action: startAppVersion(),
	}
}
