package main

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
	"os"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
)

const (
	localFile = "/tmp/app-version.json"
)

var (
	appVersions   []map[string]any
	appVersionStr string
	mux           sync.RWMutex
)

func startAppVersion() cli.ActionFunc {
	return func(c *cli.Context) error {
		port := c.String("port")

		e, err := newAppVersionServer(c)
		if err != nil {
			return err
		}

		return e.Run(":" + port)
	}
}

func newAppVersionServer(c *cli.Context) (*gin.Engine, error) {
	logger.SetJsonZeroLoggerWithLevel(c.String(flags.LogLevelFlag.Name))
	e := gin.New()
	e.Use(logger.LoggerMW(), logger.CustomLoggerMW(), gzip.Gzip(gzip.DefaultCompression))

	corsConf := cors.DefaultConfig()
	corsConf.AddAllowHeaders("x-access-token", "Language")
	corsConf.AllowAllOrigins = true
	e.Use(cors.New(corsConf))

	awsConfig := aws.NewConfig().WithRegion(c.String("s3-region")).WithLogLevel(aws.LogOff)
	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return nil, err
	}

	downloader := awslib.NewDownloader(sess)
	bucket, key := c.String("version-file-bucket"), c.String("version-file-key")
	ticker := time.NewTicker(10 * time.Minute)
	if err := downloadAndReadFile(downloader, bucket, key); err != nil {
		return nil, err
	}
	go func() {
		for {
			select {
			case <-ticker.C:
				if err := downloadAndReadFile(downloader, bucket, key); err != nil {
					log.Error().Str("error", err.Error()).Msg("failed to download and read app version file")
				}
			}
		}
	}()

	e.GET("/ad-app-versions", appVersionsHandler)
	e.GET("/v1/ad-app-versions/versions", appVersionsHandler)
	e.GET("/v1/ad-app-versions/status", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "This is Atlas app versions",
		})
	})
	return e, nil
}

func appVersionsHandler(c *gin.Context) {
	mux.RLock()
	defer mux.RUnlock()
	c.JSON(http.StatusOK, appVersions)
}

func downloadAndReadFile(downloader *awslib.S3Downloader, bucket, key string) error {
	defer os.Remove(localFile)
	if err := downloader.DownloadFromS3Bucket(bucket, key, localFile); err != nil {
		return err
	}
	byt, err := ioutil.ReadFile(localFile)
	if err != nil {
		return err
	}
	var newAppVersions []map[string]any
	if err := json.Unmarshal(byt, &newAppVersions); err != nil {
		return err
	}
	data, err := json.Marshal(newAppVersions)
	if err != nil {
		return err
	}
	newAppVersionStr := string(data)
	if newAppVersionStr != appVersionStr {
		mux.Lock()
		appVersionStr = newAppVersionStr
		appVersions = newAppVersions
		mux.Unlock()
	}
	return nil
}
