package main

import (
	"os"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	handler "bitbucket.org/persistence17/aria/golang_services/handlers/album_photo"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func startAlbumDataService() cli.ActionFunc {
	return func(c *cli.Context) error {
		port := c.String("port")

		e, err := newAlbumDataService(c)
		if err != nil {
			return err
		}

		return e.Run(":" + port)
	}
}

func newAlbumDataService(c *cli.Context) (*gin.Engine, error) {
	logger.SetJsonZeroLoggerWithLevel(c.String(flags.LogLevelFlag.Name))
	e := gin.New()
	e.Use(logger.LoggerMW(), logger.CustomLoggerMW(), gzip.Gzip(gzip.DefaultCompression))
	_, conf, err := middlewares.LoadCommonMWAndConf(c, e)
	log.Info().Interface("conf", conf).Msg("config from conf file")
	if err != nil {
		return nil, err
	}

	corsConf := cors.DefaultConfig()
	corsConf.AddAllowHeaders("x-access-token", "Language")
	corsConf.AllowAllOrigins = true
	e.Use(cors.New(corsConf))

	if err := middlewares.InitSecretMap(c.String("ad_secrets")); err != nil {
		return nil, err
	}

	userAccountServiceConfigMap := utils.GetMapEnv(c.String("account-service-config"))
	log.Info().Interface("account-service-config", userAccountServiceConfigMap).Msg("Account Service config from ENV")

	// db
	var conn *db.AuroraDB
	dbConfigMap := utils.GetMapEnv("ad_db")
	if host := dbConfigMap["write_host"]; host != nil {
		log.Info().Interface("write_host", dbConfigMap["write_host"]).Msg("Loading DB config from ENV")
		conn, err = db.NewAuroraDBFromConfigMap(dbConfigMap)
		if err != nil {
			return nil, err
		}
	}

	awsConf := utils.ParseJsonObjStr(os.Getenv("ad_aws"))
	awsConfig := aws.NewConfig().WithRegion(awsConf["region"].(string)).WithLogLevel(aws.LogOff)

	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return nil, err
	}

	bucketMap := utils.GetMapEnv(c.String("s3-bucket-config"))
	log.Info().Interface("bucket_config", bucketMap).Msg("S3 bucket config from ENV")

	if err != nil {
		return nil, err
	}

	e.Use(
		middlewares.AuthorizationMW(),
		middlewares.AccountServiceConfigMW(userAccountServiceConfigMap),
		middlewares.VisaDaoMW(conn),
		middlewares.ORMDaoMW(conn),
		middlewares.NewS3PresignerMW(sess),
		middlewares.NewS3BucketsMW(bucketMap),
	)
	handler.AlbumPhotoHandlers(e)
	return e, nil
}
