package main

import (
	"time"

	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/fedex"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/nlopes/slack"
)

// Mail template
const (
	// Notifications
	ApplicationReviewTemplate    = "send_application_review"
	PackageInprocessNotification = "send_package_inprocess_notification"
	PackageBeDeleteNotification  = "send_package_be_delete_notification"
	PaymentPendingTooLong        = "payment_pending_too_long"

	// Invoices
	InvoiceCorporationReport = "send_corporation_invoice_report"
)

// WatchDogInstant watch dog install
type WatchDogInstant struct {
	dao        *db.Dao
	etsDao     *db.EtsDao
	slack      *slack.Client
	awsSession *session.Session
	config     WatchDogConfig
	notifyURL  string
	fedex      *fedex.FedexClient
}

// WatchDogConfig watch dog config
type WatchDogConfig struct {
	PackageCheckFrequency     string `json:"package_check_frequency"`
	PackageCompleteDays       int    `json:"package_complete_days"`
	PackageInCompleteDays     int    `json:"package_incomplete_days"`
	PackageProcessDays        int    `json:"package_process_days"`
	ApplicationCheckFrequency string `json:"application_check_frequency"`
	ApplicationReviewHours    int    `json:"application_review_hours"`
	SlackChannelID            string `json:"slack_channel_id"`
	RemindPassportURL         string `json:"remind_passport_url"`
	RemindVisaURL             string `json:"remind_visa_url"`
	HostName                  string
	SlackAPIKey               string `json:"slack_api_key"`
	SecretKey                 string
	EmailConfig               map[string]string
	BucketNames               map[string]any
	IsRunning                 bool
}

// WatchDogHistory watch dog history
type WatchDogHistory struct {
	ID        int64      `json:"id" db:"id" header:"id"`
	RefTable  string     `json:"ref_table" db:"ref_table"`
	RefID     string     `json:"ref_id" db:"ref_id"`
	Type      string     `json:"type" db:"type" header:"type"`
	Message   string     `json:"message" db:"message" header:"message"`
	CreatedAt time.Time  `json:"created_at" db:"created_at" header:"created_at"`
	UpdatedAt *time.Time `json:"updated_at" db:"updated_at" `
}
