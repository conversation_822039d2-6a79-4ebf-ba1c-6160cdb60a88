package main

import (
	"fmt"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/dgrijalva/jwt-go"
)

// SentNotificationToUserWhenNoLogin send notification to user when no login
func (wd *WatchDogInstant) SentNotificationToUserWhenNoLogin() error {
	var query = wd.dao.Db.PSQL.Select("id, email").From("users").
		Where("(NOW() - last_logon_at) > (? * interval '1' day)", 5).
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type = ? AND ref_table = ? AND ref_id = users.id::text)",
			models.WatchdogEvent.UserLockWarning, models.RefTable.User,
		)

	sqlStr, args, _ := query.ToSql()

	var users []models.User
	if err := wd.dao.Db.Db.Select(&users, sqlStr, args...); err != nil {
		return err
	}
	for _, user := range users {
		// Sent email to user
		var mailTemplate = map[string]any{
			"template_name": "user_account_lock_warning",
			"to":            user.Email,
			"bcc":           []string{wd.config.EmailConfig["support"]},
			"parameters": map[string]any{
				"Name":              user.GivenName,
				"ExpiredDays":       30,
				"ExpiredDate":       time.Now().AddDate(0, 0, 30).Format("2006-01-02"),
				"UrlChangePassword": wd.config.HostName + "/dashboard/profile",
				"UrlResetPassword":  wd.config.HostName + "/dashboard/profile",
				"UrlFAQ":            wd.config.HostName + "/login",
			},
		}

		// Send email to sqs
		if err := wd.SendEmail(mailTemplate); err != nil {
			return err
		}

		if err := wd.SaveHistory(models.RefTable.User, user.ID, models.WatchdogEvent.UserLockWarning,
			fmt.Sprintf("An warning message have been sent to user: %s", user.Email)); err != nil {
			return err
		}
	}

	return nil
}

// LockUserAccountWhenNoLogin lock user account when no login
func (wd *WatchDogInstant) LockUserAccountWhenNoLogin() error {
	var query = wd.dao.Db.PSQL.Select("id, email").From("users").
		Where("(NOW() - last_logon_at) > (? * interval '1' month)", 6).
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type = ? AND ref_table = ? AND ref_id = users.id::text)",
			models.WatchdogEvent.UserAccountLocked, models.RefTable.User,
		)

	sqlStr, args, _ := query.ToSql()

	var users []models.User
	if err := wd.dao.Db.Db.Select(&users, sqlStr, args...); err != nil {
		return err
	}
	for _, user := range users {
		tk := &models.Token{
			UserID: user.ID,
			Role:   user.Role,
			StandardClaims: &jwt.StandardClaims{
				ExpiresAt: time.Now().Add(time.Hour * 24 * 5).Unix(), // 5 days for reset password
			},
		}

		tokenString, err := jwt.NewWithClaims(jwt.GetSigningMethod("HS256"), tk).SignedString([]byte(wd.config.SecretKey))
		if err != nil {
			return err
		}

		// Sent email to user
		var mailTemplate = map[string]any{
			"template_name": "account_user_locked_long_time_no_login",
			"to":            user.Email,
			"bcc":           []string{wd.config.EmailConfig["support"]},
			"parameters": map[string]any{
				"Name": user.GivenName,
				"Url":  fmt.Sprintf("%sreset/%s/%s", wd.config.HostName, user.ID, tokenString),
			},
		}

		// Send email to sqs
		if err := wd.SendEmail(mailTemplate); err != nil {
			return err
		}

		if err := wd.SaveHistory(models.RefTable.User, user.ID, models.WatchdogEvent.UserAccountLocked,
			fmt.Sprintf("User account: %s had been lock for long time without login", user.Email)); err != nil {
			return err
		}
	}

	return nil
}
