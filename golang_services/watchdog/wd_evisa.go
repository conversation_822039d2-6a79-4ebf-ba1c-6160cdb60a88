package main

import (
	"fmt"
	"os"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
)

// CheckAndGetEVisaForm check and get evisa form
func (wd *WatchDogInstant) CheckAndGetEVisaForm() error {
	var query = wd.dao.Db.PSQL.Select("order_id").From("service_tasks").
		Where(`EXISTS (
			SELECT 1
			FROM jsonb_array_elements(service_tasks.output_pods) AS elem
			WHERE elem->>'id' = 'application_application_info_application_status'and elem->'value'->>'fe' = 'paid'
		) and status != 'cancelled'`)

	sqlStr, args, _ := query.ToSql()

	var tasks []models.ServiceTask
	if err := wd.dao.Db.Db.Select(&tasks, sqlStr, args...); err != nil {
		return err
	}

	etsIDMap := map[int]int{}
	for _, app := range tasks {
		etsIDMap[app.OrderID] = app.OrderID
	}

	baseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("api_base_url").String()
	for _, orderID := range etsIDMap {
		_, err := resty.New().R().Get(fmt.Sprintf("%s/v1/pkg/public/orders/%d/get-evisa-files", baseURL, orderID))
		if err != nil {
			return err
		}
	}
	return nil
}
