package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/whatsapp"
	"github.com/tidwall/gjson"
)

// ProcessJob process job
func (wd *WatchDogInstant) ProcessJob() error {
	var query = wd.dao.Db.PSQL.Select("id, job_name, job_data, schedule_at, started_at, completed_at, status, message, created_at, updated_at").
		From("watchdog_jobs").
		Where("status = ?", "pending").
		Where("schedule_at <= ?", time.Now()).
		OrderBy("schedule_at ASC").
		Limit(1)

	sqlStr, args, _ := query.ToSql()

	var job models.WatchDogJob
	err := wd.dao.Db.Db.QueryRow(sqlStr, args...).Scan(
		&job.ID, &job.JobName, &job.JobData, &job.ScheduleAt, &job.StartedAt, &job.CompletedAt,
		&job.Status, &job.Message, &job.CreatedAt, &job.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil
	}

	if err != nil {
		return err
	}

	startedAt := time.Now()

	if job.JobName == models.JobNameDelayBeforeRemoveUserFromWhatsApp {
		group := gjson.ParseBytes(job.JobData).Get("group").String()
		phone := gjson.ParseBytes(job.JobData).Get("phone").String()
		if err := whatsapp.RemoveUsersFromGroup(group, []string{phone}); err != nil {
			fmt.Println(err)
		}
	}

	if job.JobName == models.JobNameDelaySendEmail {
		var mailTemplate = map[string]any{}
		json.Unmarshal(job.JobData, &mailTemplate)
		if err := wd.SendEmail(mailTemplate); err != nil {
			fmt.Println(err)
		}
	}

	// Update Job status to completed
	if _, err := wd.dao.Db.PSQL.Update("watchdog_jobs").
		Set("status", "completed").
		Set("started_at", startedAt).
		Set("message", "Job completed").
		Set("completed_at", time.Now()).
		Where("id = ?", job.ID).
		RunWith(wd.dao.Db.Db).
		Exec(); err != nil {
		fmt.Println("Error updating job status:", err)
	}

	return nil
}
