package main

import (
	"encoding/json"
	"fmt"

	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/tidwall/gjson"
)

type TrackInfo struct {
	ID            int              `json:"id" db:"id"`
	Service       string           `json:"service" db:"service"`
	ShipmentID    string           `json:"shipment_id" db:"shipment_id"`
	Labels        *json.RawMessage `json:"labels" db:"labels"`
	FedExTracking *json.RawMessage `json:"fedex_tracking" db:"fedex_tracking"`
}

func (wd *WatchDogInstant) TrackDeliveryStatus() error {

	var ts []TrackInfo
	if err := wd.dao.Db.Db.Select(&ts, `
	SELECT  p.id, 'visa' service, vs.id shipment_id, vs.labels, vs.fedex_tracking FROM package p
	LEFT JOIN visa_shipment vs on vs.id = p.shipment_info 
	WHERE ( vs.labels->'consulate_to_user'->>'tracking_number' != '' or  vs.labels->'user_to_consulate'->>'tracking_number' != '') and vs.fedex_tracking->'ACTUAL_DELIVERY' is null
	UNION ALL
	SELECT  so.id, 'ets' servicee, vs.id shipment_id, vs.labels, vs.fedex_tracking  FROM service_orders so 
	LEFT JOIN visa_shipment vs on vs.id = so.shipment_info 
	WHERE ( vs.labels->'consulate_to_user'->>'tracking_number' != '' or  vs.labels->'user_to_consulate'->>'tracking_number' != '') and vs.fedex_tracking->'ACTUAL_DELIVERY' is null`); err != nil {
		return err
	}

	for _, t := range ts {
		trackingNumber := gjson.ParseBytes(*t.Labels).Get("consulate_to_user.tracking_number").String()
		tracking, err := wd.fedex.TrackByNumber(trackingNumber)
		if err != nil {
			fmt.Println(err)
			continue
		}
		fedExMap := map[string]string{}
		for _, trackingTime := range tracking.CompletedTrackDetails.TrackDetails.DatesOrTimes {
			fedExMap[trackingTime.Type] = trackingTime.DateOrTimestamp
		}
		fedExMap["SERVICE_TYPE"] = tracking.CompletedTrackDetails.TrackDetails.Service.Description

		if _, err := wd.dao.Db.Db.Exec(`UPDATE visa_shipment set fedex_tracking = $1 WHERE id = $2`, utils.StructToJSON(fedExMap).Raw, t.ShipmentID); err != nil {
			return err
		}
	}

	// for _, t := range ts {
	// 	status, err := wd.fedex.TrackByNumber(t.TrackingNumber)
	// 	if err != nil {
	// 		log.Error().Msgf("Task return error %v", err)
	// 	} else {
	// 		/** Tracking Status Codes
	// 		- PU: Picked Up
	// 		- IT: In Transit
	// 		- DL: Delivered
	// 		**/
	// 		switch status {
	// 		case "PU", "IT":
	// 			{
	// 				// update shipping label status to used
	// 				if _, err := wd.dao.Db.PSQL.Update("shipping_label").
	// 					Set("status", models.ShippingLabelStatusUsed).
	// 					Set("updated_at", time.Now()).
	// 					Where(squirrel.Eq{"tracking_number": t.TrackingNumber}).
	// 					RunWith(wd.dao.Db.Db).Exec(); err != nil {
	// 					return err
	// 				}
	// 			}
	// 		case "DL":
	// 			{
	// 				// update shipping label status to delivery
	// 				if _, err := wd.dao.Db.PSQL.Update("shipping_label").
	// 					Set("status", models.ShippingLabelStatusDelivered).
	// 					Set("updated_at", time.Now()).
	// 					Where(squirrel.Eq{"tracking_number": t.TrackingNumber}).
	// 					RunWith(wd.dao.Db.Db).Exec(); err != nil {
	// 					return err
	// 				}
	// 				// update order status to completed
	// 				updateStatus := ""
	// 				switch t.OrderType {
	// 				case "package":
	// 					if t.OrderStatus == string(models.PackageStatusProceed) {
	// 						updateStatus = string(models.PackageStatusCompleted)
	// 					}
	// 				case "service_orders":
	// 					if t.OrderStatus == string(models.EtsOrderStatusInDelivery) {
	// 						updateStatus = string(models.EtsOrderStatusCompleted)
	// 					}
	// 				}
	// 				if updateStatus != "" {
	// 					if _, err := wd.dao.Db.PSQL.Update(t.OrderType).
	// 						Set("status", updateStatus).
	// 						Set("updated_at", time.Now()).
	// 						Where(squirrel.Eq{"id": t.ID}).
	// 						RunWith(wd.dao.Db.Db).Exec(); err != nil {
	// 						return err
	// 					}
	// 				}
	// 			}
	// 		}
	// 	}
	// }
	return nil
}
