package main

// func (wd *WatchDogInstant) runSlackBot() error {
// 	bot := slacker.NewClient(wd.config.SlackAPIKey)

// 	botHistory := &slacker.CommandDefinition{
// 		Description: "Show watchdog history logs",
// 		Example:     "wd logs",
// 		Handler: func(request slacker.Request, response slacker.ResponseWriter) {
// 			var logs []WatchDogHistory
// 			wd.dao.Db.Db.Select(&logs, "SELECT * FROM watchdog_history ORDER BY created_at LIMIT 10")

// 			wd.slack.PostMessage(wd.config.SlackChannelID, slack.MsgOptionText(utils.StructToTableString(logs), false), slack.MsgOptionAsUser(true))
// 		},
// 	}

// 	bot.Command("wd logs", botHistory)

// 	botStartStop := &slacker.CommandDefinition{
// 		Description: "Start/Stop watchdog service",
// 		Example:     "wd start/stop",
// 		Handler: func(request slacker.Request, response slacker.ResponseWriter) {
// 			switch request.Param("command") {
// 			case "start":
// 				if wd.config.IsRunning {
// 					wd.slack.PostMessage(wd.config.SlackChannelID, slack.MsgOptionText("Watchdog service already started", false), slack.MsgOptionAsUser(true))
// 					return
// 				}
// 				wd.config.IsRunning = true
// 				wd.slack.PostMessage(wd.config.SlackChannelID, slack.MsgOptionText("Watchdog service started", false), slack.MsgOptionAsUser(true))

// 			case "stop":
// 				if !wd.config.IsRunning {
// 					wd.slack.PostMessage(wd.config.SlackChannelID, slack.MsgOptionText("Watchdog service already stopped", false), slack.MsgOptionAsUser(true))
// 					return
// 				}
// 				wd.config.IsRunning = false
// 				wd.slack.PostMessage(wd.config.SlackChannelID, slack.MsgOptionText("Watchdog service stopped", false), slack.MsgOptionAsUser(true))
// 			}
// 		},
// 	}

// 	bot.Command("wd <command>", botStartStop)

// 	ctx, cancel := context.WithCancel(context.Background())
// 	defer cancel()

// 	return bot.Listen(ctx)
// }
