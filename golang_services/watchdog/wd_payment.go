package main

import (
	"fmt"
	"os"
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/Masterminds/squirrel"
	"github.com/pariz/gountries"
	"github.com/rs/zerolog/log"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

// CheckPaymentPendingTooLong check if payment pending too long
// payment more than 24 hours wil send email notification to admin
func (wd *WatchDogInstant) CheckPaymentPendingTooLong() error {
	var query = wd.dao.Db.PSQL.Select("id").From("payment p").
		Where(squirrel.Eq{"status": models.PaymentStatusPending}).
		Where("(NOW() - updated_at) > (? * interval '1' day)", 1).
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type = ? AND ref_table = ? AND ref_id = p.id)",
			models.WatchdogEvent.PaymentPendingTooLong, models.RefTable.Payment,
		)

	sqlStr, args, _ := query.ToSql()

	var payments []models.Payment
	if err := wd.dao.Db.Db.Select(&payments, sqlStr, args...); err != nil {
		return err
	}

	for _, p := range payments {
		cart, err := wd.dao.GetCartByPaymentID(p.ID)
		if err != nil {
			return err
		}

		if cart == nil {
			continue
		}

		if err := wd.SaveHistory(models.RefTable.Payment, p.ID, models.WatchdogEvent.PaymentPendingTooLong, "Cart payment pending too long"); err != nil {
			log.Error().Err(err)
		}

		pkgServiceJSON := gjson.Parse(os.Getenv("ad_package_service"))
		if _, err := callInternalAPI("POST", fmt.Sprintf("https://%s/cart-internal/%d/cancel", pkgServiceJSON.Get("host_name").String(), cart.ID), nil); err != nil {
			log.Error().Err(err)
		}
	}
	return nil
}

type KeyPair struct {
	Key   any `json:"key" db:"key"`
	Value any `json:"value" db:"value"`
}

func (wd *WatchDogInstant) CheckPaymentAndUpdatePod() error {
	var query = wd.dao.Db.PSQL.Select("id").From("payment p").
		Where(squirrel.Eq{"status": models.PaymentStatusSuccess}).
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type = ? AND ref_table = ? AND ref_id = p.id)",
			models.WatchdogEvent.PaymentSuccessUpdatePod, models.RefTable.Payment,
		).
		Where("EXISTS (SELECT * FROM cart c WHERE c.payment_id  = p.id)").
		OrderBy("created_at DESC")

	sqlStr, args, _ := query.ToSql()

	var payments []models.Payment
	if err := wd.dao.Db.Db.Select(&payments, sqlStr, args...); err != nil {
		return err
	}

	for _, p := range payments {
		cart, err := wd.dao.GetCartByPayment(p.ID)
		if err != nil {
			return err
		}

		if cart == nil {
			continue
		}

		for _, order := range cart.ServiceData {
			var tasks []models.ServiceTask

			// Map {task_id: photo}
			if err := wd.dao.Db.Db.Select(&tasks, `SELECT id, input_pods, output_pods FROM service_tasks WHERE order_id = $1`, order.ServiceDetail.ID); err != nil {
				return err
			}

			for _, task := range tasks {

				for i := range task.InputPods {
					if task.InputPods[i].Name == "copy_of_photo" {
						newValue := strings.ReplaceAll(cast.ToString(task.InputPods[i].GetFEValue()), "-demo", "")
						task.InputPods.SetFEValue(task.InputPods[i].ID, newValue)
					}

					// id_photo
					if order.ServiceDetail.Service.ServiceType == "id_photo" {
						// Update output file
						if _, err = wd.dao.Db.Db.Exec(fmt.Sprintf(`UPDATE service_tasks SET output_files = output_files || '{"copy_of_photo": "%s" }'  WHERE id = $1`, task.InputPods[i].Value.FE), task.ID); err != nil {
							return err
						}

					}
				}
				if _, err = wd.dao.Db.Db.Exec(`UPDATE service_tasks SET input_pods = $1 WHERE id = $2`, utils.StructToJSON(task.InputPods).Raw, task.ID); err != nil {
					return err
				}

			}

			// Send photo
			if order.ServiceDetail.Service.ServiceType == "id_photo" {
				// Get User Info
				var user, err = wd.dao.GetUserByID(order.ServiceDetail.UserID)
				if err != nil || user == nil || user.Email == "" {
					continue
				}

				inputPods := order.ServiceDetail.Tasks[0].InputPods.ToMapKeyValueV2(order.ServiceDetail.InputPods)
				var gocountry = gountries.New()
				country, err := gocountry.FindCountryByAlpha(cast.ToString(inputPods["service_core_info_destination"]))
				if err != nil {
					continue
				}

				attachments := []map[string]any{}
				for _, task := range tasks {
					outputPodMap := task.OutputPods.ToMapKeyValueV2(order.ServiceDetail.InputPods)
					if photoStr := cast.ToString(outputPodMap["application_application_info_copy_of_photo_full"]); photoStr != "" {
						bucket, key, err := utils.UrlToS3BucketAndKey(photoStr)
						if err != nil {
							continue
						}
						attachments = append(attachments, map[string]any{
							"bucket":    bucket,
							"key":       key,
							"file_name": fmt.Sprintf("AD_PHOTO_ORDER_%d_%d_FULL.png", order.ServiceDetail.ID, task.ID),
						})
					}

				}
				// Sent email to user
				var mailTemplate = map[string]any{
					"template_name": "ets_id_photo_send_photo",
					"to":            user.Email,
					"bcc":           []string{wd.config.EmailConfig["support"]},
					"parameters": map[string]any{
						"Country":      country.Name.Common,
						"DocumentType": cast.ToString(inputPods["service_core_info_document_type"]),
						"OrderID":      order.ServiceDetail.ID,
						"Name":         user.GivenName,
					},
					"attachments": attachments,
				}

				// Send email to sqs
				if err := wd.SendEmail(mailTemplate); err != nil {
					return err
				}
			}
		}

		if err := wd.SaveHistory(models.RefTable.Payment, p.ID, models.WatchdogEvent.PaymentSuccessUpdatePod, "Updated pod from config"); err != nil {
			log.Error().Err(err)
		}

	}
	return nil
}
