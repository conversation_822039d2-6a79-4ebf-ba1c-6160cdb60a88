package main

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/notification"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/sdk/zalo"
	"github.com/go-resty/resty/v2"
	"github.com/pariz/gountries"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

var gocountry = gountries.New()

// WarningExpiredPassport send warning when expired passport
func (wd *WatchDogInstant) WarningExpiredPassport() error {
	var query = wd.dao.Db.PSQL.Select("id,given_name,remind_renew_email").From("passport_book pb").
		Where("remind_renew_enabled is true").
		Where("(expiration_date - NOW()) < (? * interval '1' month)", 9).
		Where("(last_remind IS NULL or (NOW() - last_remind) >= (?* interval '1' month))", 1)

	sqlStr, args, _ := query.ToSql()

	var passports []models.PassportBook
	if err := wd.dao.Db.Db.Select(&passports, sqlStr, args...); err != nil {
		return err
	}
	for _, passport := range passports {
		if passport.RemindRenewEmail == "" {
			continue
		}

		// Sent email to user
		var mailTemplate = map[string]any{
			"template_name": "warning_expired_passport",
			"to":            passport.RemindRenewEmail,
			"bcc":           []string{wd.config.EmailConfig["support"]},
			"parameters": map[string]any{
				"Name": passport.GivenName,
				"Url":  wd.config.RemindPassportURL,
			},
		}

		// Send email to sqs
		if err := wd.SendEmail(mailTemplate); err != nil {
			return err
		}

		if _, err := wd.dao.Db.PSQL.Update("passport_book").Set("last_remind", time.Now()).Where("id = ?", passport.ID).RunWith(wd.dao.Db.Db).Exec(); err != nil {
			return err
		}

		if err := wd.SaveHistory(models.RefTable.PassportBook, passport.ID, models.WatchdogEvent.PassportExpiredWarning, "Email sent"); err != nil {
			return err
		}
	}

	return nil
}

// WarningExpiredVisa send warning when expired visa
func (wd *WatchDogInstant) WarningExpiredVisa() error {
	var query = wd.dao.Db.PSQL.Select("vb.id,given_name,country,remind_renew_email").
		From("visa_book vb, users u").
		Where("vb.user_id = u.id").
		Where("remind_renew_enabled is true").
		Where("(expiration_date - NOW()) < (? * interval '1' month)", 3).
		Where("(last_remind IS NULL or (NOW() - last_remind) >= (?* interval '1' month))", 1)

	sqlStr, args, _ := query.ToSql()

	var visas []struct {
		ID        int    `db:"id"`
		GivenName string `db:"given_name"`
		Country   string `db:"country"`
		Email     string `db:"remind_renew_email"`
	}
	if err := wd.dao.Db.Db.Select(&visas, sqlStr, args...); err != nil {
		return err
	}
	for _, visa := range visas {
		if visa.Email == "" {
			continue
		}

		country, err := gocountry.FindCountryByAlpha(visa.Country)
		if err != nil {
			return err
		}

		// Sent email to user
		var mailTemplate = map[string]any{
			"template_name": "warning_expired_visa",
			"to":            visa.Email,
			"bcc":           []string{wd.config.EmailConfig["support"]},
			"parameters": map[string]any{
				"Name":    visa.GivenName,
				"Country": country.Name.Common,
				"Url":     wd.config.RemindVisaURL,
			},
		}

		// Send email to sqs
		if err := wd.SendEmail(mailTemplate); err != nil {
			return err
		}

		if _, err := wd.dao.Db.PSQL.Update("visa_book").Set("last_remind", time.Now()).Where("id = ?", visa.ID).RunWith(wd.dao.Db.Db).Exec(); err != nil {
			return err
		}

		if err := wd.SaveHistory(models.RefTable.VisaBook, visa.ID, models.WatchdogEvent.VisaExpiredWarning, "Email sent"); err != nil {
			return err
		}
	}

	return nil
}

// CheckAndGetUSPassportStatus check and get us passport status
func (wd *WatchDogInstant) CheckAndGetUSPassportStatus() error {
	if os.Getenv("ad_env") != "prod" {
		return nil
	}
	var query = wd.dao.Db.PSQL.Select("st.id, st.order_id, st.input_pods, st.output_pods").From("service_tasks st").
		LeftJoin("service_orders so ON so.id = st.order_id").
		LeftJoin("ets ON ets.id = so.service_id").
		Where(`EXISTS (
			SELECT 1
			FROM jsonb_array_elements(st.output_pods) AS elem
			WHERE elem->>'id' = 'application_application_info_application_status'and elem->'value'->>'fe' IN ('paid','submitted','in_process')
		)`).
		Where("so.status IN ('paid','submitted','under_review')").
		Where("ets.name like '%Renew US Passport%'")

	sqlStr, args, _ := query.ToSql()

	var tasks []models.ServiceTask
	if err := wd.dao.Db.Db.Select(&tasks, sqlStr, args...); err != nil {
		return err
	}

	messages := []string{}
	messages = append(messages, fmt.Sprintf("<b>US PASSPORT, Status Checking at: %s</b>", time.Now().Format("02/01/2006 15:04")))

	for _, task := range tasks {
		order, _ := wd.etsDao.GetServiceOrderByID(int(task.OrderID))
		inputPodMap := task.InputPods.ToMapKeyValue()
		outputPodMap := task.OutputPods.ToMapKeyValue()

		url := fmt.Sprintf("https://form.ariadirectcorp.com/api/us_passport_status/get_us_passport_status_v2?surname=%s&dob=%s&ssn=%s",
			cast.ToString(inputPodMap["passport_core_info_surname"]),
			cast.ToTime(inputPodMap["passport_core_info_date_of_birth"]).Format("01/02/2006"),
			cast.ToString(inputPodMap["personal_core_info_social_security_number"]))
		fmt.Println(url)

		var resp *resty.Response
		var err error
		for retries := 0; retries < 3; retries++ {
			resp, err = resty.New().R().Get(url)
			if err == nil {
				break
			}
			if retries < 2 {
				time.Sleep(10 * time.Second)
				continue
			}
			return err
		}
		fmt.Println(resp.String())
		if gjson.Get(resp.String(), "success").Bool() {
			status := gjson.Get(resp.String(), "data.status").String()
			statusText := gjson.Get(resp.String(), "data.status_text").String()
			fmt.Println(status, statusText)
			note := cast.ToString(outputPodMap["application_application_info_note"])

			lines := strings.Split(note, "\n")
			found := false
			for i, line := range lines {
				if strings.HasPrefix(line, "[USPP] Last check:") {
					oldStatus := strings.Split(line, ", ")[1]
					if oldStatus == statusText {
						lines[i] = "[USPP] Last check:" + time.Now().Format("02/01/2006 15:04") + ", " + statusText
						found = true
						break
					}
				}
			}

			if !found {
				lines = append(lines, "", "[USPP] Last check:"+time.Now().Format("02/01/2006 15:04")+", "+statusText)
			}

			note = strings.Join(lines, "\n")

			task.OutputPods.SetFEValue("application_application_info_note", note)

			update := map[string]any{}
			if status == "approved" {
				task.OutputPods.SetFEValue("application_application_info_application_status", "approved")
				update["status"] = models.EtsTaskStatusApproved
			}

			update["output_pods"] = task.OutputPods

			messages = append(messages, fmt.Sprintf("OrderID: %d, Submitted: %s, %s %s, %s",
				task.OrderID,
				utils.StructToJSON(order.TrackingTimes).Get("submitted").Time().Format("02/01/2006 15:04"),
				cast.ToString(inputPodMap["passport_core_info_given_name"]),
				cast.ToString(inputPodMap["passport_core_info_surname"]),
				statusText))

			if err := wd.etsDao.UpdateServiceTask(update, task.ID); err != nil {
				return err
			}

			{
				orderResp, _ := wd.etsDao.QueryServiceOrders(models.ServiceOrderFilter{ID: []string{strconv.Itoa(int(task.OrderID))}, IncludeTasks: true, Limit: 1})
				order := orderResp.Data[0]

				isAllApproved := true
				for _, task := range order.Tasks {
					if task.Status != models.EtsTaskStatusApproved {
						isAllApproved = false
						break
					}
				}

				if len(order.Tasks) > 0 && isAllApproved && order.Status != models.EtsOrderStatusCompleted {
					if err := wd.etsDao.UpdateServiceOrder(map[string]any{
						"status":         models.EtsOrderStatusCompleted,
						"completed_time": time.Now(),
						"updated_at":     time.Now(),
					}, order.ID); err != nil {
						return err
					}

					if err := notification.NotifyFeedbackToUser(order.ID); err != nil {
						return err
					}
				}
			}

		}
	}

	if len(messages) > 1 {
		zalo.SendZaloSMS(zalo.GetGroupID("INTERNAL_NOTIFICATION"), strings.Join(messages, "\n"))
	}

	return nil
}
