package main

import (
	"encoding/json"
	"fmt"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/go-resty/resty/v2"
)

// SendEmail Send email to sqs
func (wd *WatchDogInstant) SendEmail(emailTemplate map[string]any) error {
	emailMessage, err := json.Marshal(emailTemplate)
	if err != nil {
		return err
	}

	svc := sqs.New(wd.awsSession)
	_, err = svc.SendMessage(&sqs.SendMessageInput{
		MessageBody: aws.String(string(emailMessage)),
		QueueUrl:    &wd.notifyURL,
	})
	return err
}

// SendEmail Send email to sqs
func (wd *WatchDogInstant) SendSMS(phone, message string) error {
	resp, err := resty.New().R().SetHeader("Content-Type", "application/json").SetBody(map[string]any{
		// "number":  phone,
		"number":  "0908427918",
		"message": message,
	}).Post(`https://sms.ariadirectcorp.com/send`)
	if err != nil {
		return err
	}
	fmt.Println(resp.String())
	return nil
}
