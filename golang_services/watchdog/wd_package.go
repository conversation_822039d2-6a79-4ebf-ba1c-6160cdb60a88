package main

import (
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/Masterminds/squirrel"
)

// DeleteCompletedOrder delete compalted order visa or ets
func (wd *WatchDogInstant) DeleteCompletedOrder(days int) error {
	if _, err := wd.dao.Db.PSQL.Update("service_orders").Set("deleted_at", time.Now()).
		Where(squirrel.Eq{"status": []string{
			string(models.EtsOrderStatusCompleted),
		}}).
		Where("(NOW() - updated_at) > (? * interval '1' day)", days).
		RunWith(wd.dao.Db.Db).Exec(); err != nil {
		return err
	}

	return nil
}
