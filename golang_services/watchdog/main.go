package main

import (
	"log"
	"os"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	"github.com/urfave/cli"
)

func main() {
	app := cli.NewApp()
	app.Name = "aria-watchdogs"
	app.Usage = "Watchdog service for Aria"
	app.Commands = []cli.Command{
		watchDogCommand(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}

}

func watchDogCommand() cli.Command {
	return cli.Command{
		Name:      "service",
		ShortName: "svc",
		Usage:     "Start watch dog service for <PERSON>",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:  "db-config",
				Usage: "Env for db config",
			},
			cli.StringFlag{
				Name:     "service-config",
				Usage:    "Env name for service configuration",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad-aws",
				Usage:    "AWS config",
				EnvVar:   "ad_aws",
				Required: true,
			},
			cli.StringFlag{
				Name:     "sqs-config",
				Usage:    "Env name for service configuration",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_website",
				Usage:    "Env for website config",
				EnvVar:   "ad_website",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_email",
				Usage:    "Env for email config",
				EnvVar:   "ad_email",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_secrets",
				Usage:    "secret token",
				EnvVar:   "ad_secrets",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad-s3",
				Usage:    "Env for s3 config",
				EnvVar:   "ad_s3",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_fedex",
				Usage:    "API key for calling fedex service",
				EnvVar:   "ad_fedex",
				Required: true,
			},
			flags.LogLevelFlag,
		},
		Action: watchDogAction(),
	}
}
