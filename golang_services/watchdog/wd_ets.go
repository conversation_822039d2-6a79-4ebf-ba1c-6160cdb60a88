package main

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/flightstats"
	"bitbucket.org/persistence17/aria/golang_services/sdk/notification"
	"bitbucket.org/persistence17/aria/golang_services/sdk/time_util"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/sdk/whatsapp"
	"bitbucket.org/persistence17/aria/golang_services/sdk/zalo"
	"github.com/Masterminds/squirrel"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

// CheckIncompleteETSOrder check incomplete ets order
func (wd *WatchDogInstant) CheckIncompleteETSOrder() error {
	var query = wd.dao.Db.PSQL.Select("id, service_id, user_id,updated_at").From("service_orders so").
		Where(squirrel.Eq{"status": []string{
			string(models.EtsOrderStatusOpen),
		}}).
		// Where("tracking_notification IS TRUE").
		Where("deleted_at IS NULL").
		Where("(NOW() - updated_at) > interval '5' day").
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type = ? AND ref_table = ? AND ref_id = so.id::text)",
			models.WatchdogEvent.ETSOrderWarningDelete, models.RefTable.ServiceOrder,
		)

	sqlStr, args, _ := query.ToSql()

	var services []models.ServiceOrder
	if err := wd.dao.Db.Db.Select(&services, sqlStr, args...); err != nil {
		return err
	}

	for _, service := range services {
		var serviceType string
		if err := wd.dao.Db.Db.Get(&serviceType, "SELECT service_type FROM ets WHERE id = $1 LIMIT 1", service.ServiceID); err != nil {
			return err
		}

		// Get User Info
		var user, err = wd.dao.GetUserByID(service.UserID)
		if err != nil || user == nil || user.Email == "" {
			continue
		}

		// Sent email to user
		var mailTemplate = map[string]any{
			"template_name": "ets_delete_warning",
			"to":            user.Email,
			"bcc":           []string{wd.config.EmailConfig["support"]},
			"parameters": map[string]any{
				"OrderID":        service.ID,
				"FullName":       strings.ToUpper(user.GivenName),
				"InCompleteDays": 5,
				"DeleteDays":     10,
				"OrderURL":       fmt.Sprintf("%s/dashboard/order-services/application?order_id=%d&service=ets", wd.config.HostName, service.ID),
			},
		}

		// Send email to sqs
		if err := wd.SendEmail(mailTemplate); err != nil {
			return err
		}

		if err := wd.SaveHistory(models.RefTable.ServiceOrder, service.ID, models.WatchdogEvent.ETSOrderWarningDelete,
			fmt.Sprintf("ETS Order %d is going to be deleted, An email have been sent to user: %s", service.ID, user.Email)); err != nil {
			return err
		}
	}

	return nil
}

// DeleteIncompleteETSOrder delete incomplete ets order
func (wd *WatchDogInstant) DeleteIncompleteETSOrder() error {
	_, err := wd.dao.Db.PSQL.Update("service_orders").Set("deleted_at", time.Now()).
		Where(squirrel.Eq{"status": []string{
			string(models.EtsOrderStatusOpen),
			string(models.EtsOrderStatusPendingPayment),
			string(models.EtsOrderStatusWaitingPayment),
		}}).
		Where("(NOW() - updated_at) > (? * interval '1' day)", 10).
		RunWith(wd.dao.Db.Db).Exec()
	return err
}

// RemindProcessingETSOrder check and remind processing ets order for ets provider
func (wd *WatchDogInstant) RemindProcessingETSOrder() error {
	var query = wd.dao.Db.PSQL.Select("so.id,so.status,so.user_id,so.updated_at").
		From("service_orders so").
		LeftJoin("ets e ON e.id = so.service_id").
		Where(squirrel.Eq{"so.status": []string{
			string(models.EtsOrderStatusSubmitted),
			string(models.EtsOrderStatusUnderReview),
		}}).
		Where("e.service_type = ?", "passport").
		Where("(NOW() - so.submitted_time) > (3 * interval '1' day)"). // After 5 days from submit https://traversal17.atlassian.net/browse/AD-6011
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type = ? AND ref_table = ? AND ref_id = so.id::text)",
			models.WatchdogEvent.ETSOrderReminderToProvider, models.RefTable.ServiceOrder,
		).
		Where("so.deleted_at IS NULL")

	sqlStr, args, _ := query.ToSql()

	var serviceOrders []models.ServiceOrder
	if err := wd.dao.Db.Db.Select(&serviceOrders, sqlStr, args...); err != nil {
		return err
	}
	for _, serviceOrder := range serviceOrders {
		etsDao := db.NewEtsDao(wd.dao.Db)
		resp, err := etsDao.QueryServiceOrders(models.ServiceOrderFilter{ID: []string{strconv.Itoa(serviceOrder.ID)}, IncludeTasks: true, Limit: 1})
		if err != nil {
			return fmt.Errorf("Error when get order detail %v", err)
		}
		order := resp.Data[0]

		processingTime := utils.StructToJSON(order.Service.Attributes).Get("processing_time").String()
		if duration, err := time_util.ProcessingTimeStrToDuration(processingTime); err == nil {
			// Check duration > 1 month
			if duration > 30*24*time.Hour {
				continue
			}
		}

		etsJ := utils.StructToJSON(order.Service)
		applications := []map[string]any{}
		for _, task := range order.Tasks {
			applications = append(applications, map[string]any{
				"FullName":          task.GetAppName("passport"),
				"RegionOfResidence": utils.StructToJSON(order.QueryPodValues).Get("service_core_info_region_of_residence").String(),
				"ProcessingTime":    etsJ.Get("attributes.processing_time").String(),
				"Task":              strings.Join(order.Service.Tasks, ", "),
			})
		}

		provider, err := etsDao.GetEtsProviderByID(order.ProviderID)
		if err != nil {
			return err
		}

		country, _ := gocountry.FindCountryByAlpha(order.Service.Country)

		// Sent email to provider
		var mailTemplate = map[string]any{
			"template_name": "reminder_vn_passport_consulate",
			"to":            provider.Contact.Email,
			"bcc":           []string{wd.config.EmailConfig["support"]},
			"parameters": map[string]any{
				"OrderID":      order.ID,
				"Name":         provider.Name,
				"Country":      country.Name.Common,
				"Applications": applications,
				"URL":          fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", wd.config.HostName, order.ID),
			},
		}
		// Send email to sqs
		if err := wd.SendEmail(mailTemplate); err != nil {
			return err
		}

		if err := wd.SaveHistory(models.RefTable.ServiceOrder, order.ID, models.WatchdogEvent.ETSOrderReminderToProvider,
			fmt.Sprintf("Package ETSOrderReminderToProvider %d , An email have been sent to ets provider: %s", order.ID, provider.Contact.Email)); err != nil {
			return err
		}
	}

	return nil
}

// RemindProcessingETSOrderFastlane3Days check and remind processing ets order for ets provider fastlane
func (wd *WatchDogInstant) RemindProcessingETSOrderFastlane3Days() error {
	var query = wd.dao.Db.PSQL.Select("so.id,so.status,so.user_id,so.updated_at").
		From("service_orders so").
		LeftJoin("ets e ON e.id = so.service_id").
		Where(squirrel.Eq{"so.status": []string{
			string(models.EtsOrderStatusDispatched),
			string(models.EtsOrderStatusUnderReview),
		}}).
		Where("e.service_type = ?", "fastlane").
		// Where("(processing_time_expired_at - NOW() ) <= interval '2' day").
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type LIKE ? AND ref_table = ? AND ref_id = so.id::text)",
			models.WatchdogEvent.ETSOrderFastlaneReminderToProvider3Days, models.RefTable.ServiceOrder,
		).
		Where("so.deleted_at IS NULL")

	sqlStr, args, _ := query.ToSql()

	var serviceOrders []models.ServiceOrder
	if err := wd.dao.Db.Db.Select(&serviceOrders, sqlStr, args...); err != nil {
		return err
	}
	for _, serviceOrder := range serviceOrders {
		etsDao := db.NewEtsDao(wd.dao.Db)
		resp, err := etsDao.QueryServiceOrders(models.ServiceOrderFilter{ID: []string{strconv.Itoa(serviceOrder.ID)}, IncludeTasks: true, Limit: 1})
		if err != nil {
			return fmt.Errorf("Error when get order detail %v", err)
		}

		order := resp.Data[0]

		etsJ := utils.StructToJSON(order.Service)
		task := order.Tasks[0]

		provider, err := etsDao.GetEtsProviderByID(order.ProviderID)
		if err != nil {
			return err
		}
		inputPods := task.InputPods.ToMapKeyValueV2(order.InputPods)

		var params = map[string]any{
			"OrderID":         order.ID,
			"ProviderName":    provider.Name,
			"Country":         order.Service.Country, //
			"ServiceType":     "Fastlane",
			"Terminal":        strings.Title(etsJ.Get("attributes.terminal").String()),
			"Task":            strings.Title(strings.Join(order.Service.Tasks, " ")),
			"ProcessingTime":  etsJ.Get("attributes.processing_time").String(),
			"NoOfTraveler":    inputPods["travel_passenger_info_no_of_traveler"],
			"AirportName":     inputPods["travel_exit_flight_exit_airport"],
			"FlightNo":        utils.GetStringOrText(inputPods["travel_exit_flight_exit_flight"]),
			"ServiceDateTime": inputPods["travel_exit_flight_exit_timestamp"],
			"URL":             fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", wd.config.HostName, order.ID),
		}

		if task.Type == "arrival" || task.Type == "vip_arrival" {
			params["AirportName"] = inputPods["travel_enter_flight_enter_airport"]
			params["FlightNo"] = utils.GetStringOrText(inputPods["travel_enter_flight_enter_flight"])
			params["ServiceDateTime"] = inputPods["travel_enter_flight_enter_timestamp"]
		}

		serviceDate := utils.StructToJSON(params).Get("ServiceDateTime").Time()

		// Check 3 day before service date send email to provider
		// https://traversal17.atlassian.net/browse/AD-6011
		if (serviceDate.Unix() - time.Now().Unix()) >= 3*24*60*60 {
			continue
		}

		if order.SubmittedTime == nil {
			continue
		}

		// Not remind if over service date
		if time.Now().Unix() > serviceDate.Unix() {
			continue
		}

		// Less than 1 day then skip reminder
		if (time.Now().Unix() - order.SubmittedTime.Unix()) < 24*60*60 {
			continue
		}

		params["ServiceDateTime"] = serviceDate.Format("Mon, 02 Jan 2006 15:04")

		// Sent email to provider
		var mailTemplate = map[string]any{
			"template_name": "reminder_fastlane_consulate",
			"to":            provider.Contact.Email,
			"bcc":           []string{wd.config.EmailConfig["support"]},
			"parameters":    params,
		}

		// Send email to sqs
		if err := wd.SendEmail(mailTemplate); err != nil {
			return err
		}

		if err := wd.SaveHistory(models.RefTable.ServiceOrder, order.ID, models.WatchdogEvent.ETSOrderFastlaneReminderToProvider3Days,
			fmt.Sprintf("Package ETSOrderFastlaneReminderToProvider3Days %d , An email have been sent to ets provider: %s", order.ID, provider.Contact.Email)); err != nil {
			return err
		}
	}

	return nil
}

// RemindProcessingETSOrderFastlane1Day check and remind processing ets order for ets provider fastlane
func (wd *WatchDogInstant) RemindProcessingETSOrderFastlane1Day() error {
	var query = wd.dao.Db.PSQL.Select("so.id,so.status,so.user_id,so.updated_at").
		From("service_orders so").
		LeftJoin("ets e ON e.id = so.service_id").
		Where(squirrel.Eq{"so.status": []string{
			string(models.EtsOrderStatusDispatched),
			string(models.EtsOrderStatusUnderReview),
		}}).
		Where("e.service_type = ?", "fastlane").
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type LIKE ? AND ref_table = ? AND ref_id = so.id::text)",
			models.WatchdogEvent.ETSOrderFastlaneReminderToProvider1Day, models.RefTable.ServiceOrder,
		).
		Where("so.deleted_at IS NULL")

	sqlStr, args, _ := query.ToSql()

	var serviceOrders []models.ServiceOrder
	if err := wd.dao.Db.Db.Select(&serviceOrders, sqlStr, args...); err != nil {
		return err
	}
	for _, serviceOrder := range serviceOrders {
		etsDao := db.NewEtsDao(wd.dao.Db)
		resp, err := etsDao.QueryServiceOrders(models.ServiceOrderFilter{ID: []string{strconv.Itoa(serviceOrder.ID)}, IncludeTasks: true, Limit: 1})
		if err != nil {
			return fmt.Errorf("Error when get order detail %v", err)
		}

		order := resp.Data[0]

		etsJ := utils.StructToJSON(order.Service)
		task := order.Tasks[0]

		provider, err := etsDao.GetEtsProviderByID(order.ProviderID)
		if err != nil {
			return err
		}
		inputPods := task.InputPods.ToMapKeyValueV2(serviceOrder.InputPods)

		var params = map[string]any{
			"OrderID":         order.ID,
			"ProviderName":    provider.Name,
			"Country":         order.Service.Country, //
			"ServiceType":     "Fastlane",
			"Terminal":        strings.Title(etsJ.Get("attributes.terminal").String()),
			"Task":            strings.Title(strings.Join(order.Service.Tasks, " ")),
			"ProcessingTime":  etsJ.Get("attributes.processing_time").String(),
			"NoOfTraveler":    inputPods["travel_passenger_info_no_of_traveler"],
			"AirportName":     inputPods["travel_exit_flight_exit_airport"],
			"FlightNo":        utils.GetStringOrText(inputPods["travel_exit_flight_exit_flight"]),
			"ServiceDateTime": inputPods["travel_exit_flight_exit_timestamp"],
			"URL":             fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", wd.config.HostName, order.ID),
		}

		if task.Type == "arrival" || task.Type == "vip_arrival" {
			params["AirportName"] = inputPods["travel_enter_flight_enter_airline"]
			params["FlightNo"] = utils.GetStringOrText(inputPods["travel_enter_flight_enter_flight"])
			params["ServiceDateTime"] = inputPods["travel_enter_flight_enter_timestamp"]
		}

		serviceDate := utils.StructToJSON(params).Get("ServiceDateTime").Time()

		fmt.Println(serviceDate)
		fmt.Println(time.Now())
		// Check 1 day before service date send email to provider
		// https://traversal17.atlassian.net/browse/AD-6011
		if (serviceDate.Unix() - time.Now().Unix()) >= 24*60*60 {
			continue
		}

		// Less than 2 hours then skip reminder
		if (time.Now().Unix() - order.SubmittedTime.Unix()) < 2*60*60 {
			continue
		}

		// Not remind if over service date
		if time.Now().Unix() > serviceDate.Unix() {
			continue
		}

		params["ServiceDateTime"] = serviceDate.Format("Mon, 02 Jan 2006 15:04")

		// Sent email to provider
		var mailTemplate = map[string]any{
			"template_name": "reminder_fastlane_consulate",
			"to":            provider.Contact.Email,
			"bcc":           []string{wd.config.EmailConfig["support"]},
			"parameters":    params,
		}

		// Send email to sqs
		if err := wd.SendEmail(mailTemplate); err != nil {
			return err
		}

		if err := wd.SaveHistory(models.RefTable.ServiceOrder, order.ID, models.WatchdogEvent.ETSOrderFastlaneReminderToProvider1Day,
			fmt.Sprintf("Package ETSOrderFastlaneReminderToProvider1Day %d , An email have been sent to ets provider: %s", order.ID, provider.Contact.Email)); err != nil {
			return err
		}
	}

	return nil
}

// Remind provider update worker information
// https://traversal17.atlassian.net/browse/AD-6014
func (wd *WatchDogInstant) RemindProviderUpdateOrderForFastlane() error {
	var query = wd.dao.Db.PSQL.Select("so.id,so.status,so.user_id,so.updated_at").
		From("service_orders so").
		LeftJoin("ets e ON e.id = so.service_id").
		Where(squirrel.NotEq{"so.status": []string{
			string(models.EtsOrderStatusOpen),
		}}).
		Where("e.service_type = ?", "fastlane").
		Where("e.tasks::text LIKE ?", "%departure%").
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type = ? AND ref_table = ? AND ref_id = so.id::text)",
			models.WatchdogEvent.ETSOrderFastlaneReminderUpdateProviderInfo, models.RefTable.ServiceOrder,
		).
		Where("so.deleted_at IS NULL")

	sqlStr, args, _ := query.ToSql()

	var serviceOrders []models.ServiceOrder
	if err := wd.dao.Db.Db.Select(&serviceOrders, sqlStr, args...); err != nil {
		return err
	}
	for _, serviceOrder := range serviceOrders {
		etsDao := db.NewEtsDao(wd.dao.Db)
		resp, err := etsDao.QueryServiceOrders(models.ServiceOrderFilter{ID: []string{strconv.Itoa(serviceOrder.ID)}, IncludeTasks: true, Limit: 1})
		if err != nil {
			return fmt.Errorf("Error when get order detail %v", err)
		}

		order := resp.Data[0]

		etsJ := utils.StructToJSON(order.Service)
		task := order.Tasks[0]

		provider, err := etsDao.GetEtsProviderByID(order.ProviderID)
		if err != nil {
			return err
		}
		inputPods := task.InputPods.ToMapKeyValueV2(serviceOrder.InputPods)
		outputPodsJ := task.OutputPods.ToMapKeyPod()

		var params = map[string]any{
			"OrderID":         order.ID,
			"ProviderName":    provider.Name,
			"Country":         order.Service.Country, //
			"ServiceType":     "Fastlane",
			"Terminal":        strings.Title(etsJ.Get("attributes.terminal").String()),
			"Task":            strings.Title(strings.Join(order.Service.Tasks, " ")),
			"ProcessingTime":  etsJ.Get("attributes.processing_time").String(),
			"NoOfTraveler":    inputPods["travel_passenger_info_no_of_traveler"],
			"AirportName":     inputPods["travel_exit_flight_exit_airport"],
			"FlightNo":        utils.GetStringOrText(inputPods["travel_exit_flight_exit_flight"]),
			"ServiceDateTime": inputPods["travel_exit_flight_exit_timestamp"],
			"URL":             fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", wd.config.HostName, order.ID),
		}

		if task.Type == "arrival" || task.Type == "vip_arrival" {
			params["AirportName"] = inputPods["travel_enter_flight_enter_airline"]
			params["FlightNo"] = utils.GetStringOrText(inputPods["travel_enter_flight_enter_flight"])
			params["ServiceDateTime"] = inputPods["travel_enter_flight_enter_timestamp"]
		}

		serviceDate := utils.StructToJSON(params).Get("ServiceDateTime").Time()

		// Check 1 day before service date send email to provider
		if serviceDate.Unix() < time.Now().Unix() && serviceDate.Unix() > (time.Now().Unix()+1*24*60*60) {
			continue
		}

		params["ServiceDateTime"] = serviceDate.Format("Mon, 02 Jan 2006 15:04")

		reasons := []string{}
		// Surname, Given name, Phone Number
		for _, pod := range []string{"worker_staff_service_contact_full_name", "worker_staff_service_contact_given_name", "worker_staff_service_contact_phone"} {
			if cast.ToString(outputPodsJ[pod].GetFEValue()) == "" {
				reasons = append(reasons, outputPodsJ[pod].Title+" is missing")
			}
		}
		params["Reason"] = strings.Join(reasons, ", ")

		// Sent email to provider
		var mailTemplate = map[string]any{
			"template_name": "remind_provider_contact_fastlane",
			"to":            provider.Contact.Email,
			"bcc":           []string{wd.config.EmailConfig["support"]},
			"parameters":    params,
		}

		// Send email to sqs
		if err := wd.SendEmail(mailTemplate); err != nil {
			return err
		}

		if err := wd.SaveHistory(models.RefTable.ServiceOrder, order.ID, models.WatchdogEvent.ETSOrderFastlaneReminderUpdateProviderInfo,
			fmt.Sprintf("Package RemindProviderUpdateOrderForFastlane %d , An email have been sent to ets provider: %s", order.ID, provider.Contact.Email)); err != nil {
			return err
		}
	}

	return nil
}

// VNMPassportOrderDelivery check if VNM passport status is in_delivery
// https://traversal17.atlassian.net/browse/AD-5960
func (wd *WatchDogInstant) VNMPassportOrderDelivery() error {
	var query = wd.dao.Db.PSQL.Select("so.id,so.status,so.user_id,so.updated_at").
		From("service_orders so").
		LeftJoin("ets e ON e.id = so.service_id").
		Where(squirrel.Eq{"so.status": []string{
			string(models.EtsOrderStatusInDelivery),
			string(models.EtsOrderStatusCompleted),
		}}).
		Where("e.service_type = ? AND e.country = ?", "passport", "VNM").
		Where("NOW() > processing_time_expired_at").
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type = ? AND ref_table = ? AND ref_id = so.id::text)",
			models.WatchdogEvent.ETSOrderPassportDelivery, models.RefTable.ServiceOrder,
		).
		Where("so.deleted_at IS NULL")

	sqlStr, args, _ := query.ToSql()

	var serviceOrders []models.ServiceOrder
	if err := wd.dao.Db.Db.Select(&serviceOrders, sqlStr, args...); err != nil {
		return err
	}
	for _, serviceOrder := range serviceOrders {
		user, err := wd.dao.GetUserByID(serviceOrder.UserID)
		if err != nil || user == nil {
			return fmt.Errorf("Error when get user detail %v", err)
		}

		etsDao := db.NewEtsDao(wd.dao.Db)
		resp, err := etsDao.QueryServiceOrders(models.ServiceOrderFilter{ID: []string{strconv.Itoa(serviceOrder.ID)}, IncludeTasks: true, IncludeShipment: true, Limit: 1})
		if err != nil {
			return fmt.Errorf("Error when get order detail %v", err)
		}
		order := resp.Data[0]

		trackingNumber := utils.StructToJSON(order).Get("shipment.labels.consulate_to_user.tracking_number").String()

		if trackingNumber != "" {
			// Sent email to provider
			var mailTemplate = map[string]any{
				"template_name": "notify_approval_passport_to_user",
				"to":            user.Email,
				"bcc":           []string{wd.config.EmailConfig["support"]},
				"parameters": map[string]any{
					"OrderID":         order.ID,
					"FullName":        strings.Join([]string{user.GivenName, user.Surname}, " "),
					"TrackingNumber":  trackingNumber,
					"ShippingService": "FedEx",
				},
			}
			// Send email to sqs
			if err := wd.SendEmail(mailTemplate); err != nil {
				return err
			}
		}

		if err := wd.SaveHistory(models.RefTable.ServiceOrder, order.ID, models.WatchdogEvent.ETSOrderPassportDelivery,
			fmt.Sprintf("Package ETSOrderPassportDelivery %d , An email have been sent to user: %s", order.ID, user.Email)); err != nil {
			return err
		}
	}

	return nil
}

// CheckUnderReviewXDaysETSOrder check under review ets order
// https://traversal17.atlassian.net/browse/AD-6089
func (wd *WatchDogInstant) CheckUnderReviewXDaysETSOrder() error {
	var query = wd.dao.Db.PSQL.Select("id, order_id, created_at, updated_at").From("service_tasks st").
		Where(squirrel.Eq{"status": []string{
			string(models.EtsTaskStatusNeedMoreDocument),
		}}).
		Where("(now() - updated_at) > interval '7' day").
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type = ? AND ref_table = ? AND ref_id = st.id::text)",
			models.WatchdogEvent.ETSOrderRemindUpdateMissingDocument, models.RefTable.ServiceTask,
		)

	sqlStr, args, _ := query.ToSql()

	var tasks []models.ServiceTask
	if err := wd.dao.Db.Db.Select(&tasks, sqlStr, args...); err != nil {
		return err
	}

	for _, task := range tasks {
		if err := wd.etsDao.UpdateServiceTask(map[string]any{
			"status": models.EtsTaskStatusDenied,
		}, task.ID); err != nil {
			return err
		}

		order, err := wd.etsDao.GetServiceOrderByID(task.OrderID)
		if err != nil {
			return err
		}

		if err != nil {
			return err
		}

		conf, _ := sjson.Set(utils.StructToJSON(order.Config).Raw, fmt.Sprintf("reject_message.%d", task.ID), "reject_missing_doc_msg")
		fmt.Println(conf)
		if err := wd.etsDao.UpdateServiceOrder(map[string]any{
			"config": conf,
		}, order.ID); err != nil {
			return err
		}
		// // Sent email to user
		// var mailTemplate = map[string]any{
		// 	"template_name": "ets_delete_warning",
		// 	"to":            user.Email,
		// 	"bcc":           []string{wd.config.EmailConfig["support"]},
		// 	"parameters": map[string]any{
		// 		"OrderID":        order.ID,
		// 		"FullName":       strings.ToUpper(user.GivenName),
		// 		"InCompleteDays": 5,
		// 		"DeleteDays":     10,
		// 		"OrderURL":       fmt.Sprintf("%s/dashboard/order-services/application?order_id=%d&service=ets", wd.config.HostName, order.ID),
		// 	},
		// }

		// // Send email to sqs
		// if err := wd.SendEmail(mailTemplate); err != nil {
		// 	return err
		// }

		// if err := wd.SaveHistory(models.RefTable.ServiceTask, order.ID, models.WatchdogEvent.ETSOrderUnderReviewXDays,
		// 	fmt.Sprintf("ETSOrderUnderReviewXDays  %d to user: %s", order.ID, user.Email)); err != nil {
		// 	return err
		// }
	}

	return nil
}

// ETSRemindUserUpdateMissingDocument check under review ets order
// https://traversal17.atlassian.net/browse/AD-6088
func (wd *WatchDogInstant) ETSRemindUserUpdateMissingDocument() error {
	var query = wd.dao.Db.PSQL.Select("id, order_id, created_at, updated_at").From("service_tasks st").
		Where(squirrel.Eq{"status": []string{
			string(models.EtsTaskStatusNeedMoreDocument),
		}}).
		Where("(now() - updated_at) > interval '7' day")

	sqlStr, args, _ := query.ToSql()

	var tasks []models.ServiceTask
	if err := wd.dao.Db.Db.Select(&tasks, sqlStr, args...); err != nil {
		return err
	}

	for _, task := range tasks {
		if err := wd.etsDao.UpdateServiceTask(map[string]any{
			"status": models.EtsTaskStatusDenied,
		}, task.ID); err != nil {
			return err
		}

		order, err := wd.etsDao.GetServiceOrderByID(task.OrderID)
		if err != nil {
			return err
		}

		if err != nil {
			return err
		}

		conf, _ := sjson.Set(utils.StructToJSON(order.Config).Raw, fmt.Sprintf("reject_message.%d", task.ID), "reject_missing_doc_msg")
		fmt.Println(conf)
		if err := wd.etsDao.UpdateServiceOrder(map[string]any{
			"config": conf,
		}, order.ID); err != nil {
			return err
		}
		// // Sent email to user
		// var mailTemplate = map[string]any{
		// 	"template_name": "ets_delete_warning",
		// 	"to":            user.Email,
		// 	"bcc":           []string{wd.config.EmailConfig["support"]},
		// 	"parameters": map[string]any{
		// 		"OrderID":        order.ID,
		// 		"FullName":       strings.ToUpper(user.GivenName),
		// 		"InCompleteDays": 5,
		// 		"DeleteDays":     10,
		// 		"OrderURL":       fmt.Sprintf("%s/dashboard/order-services/application?order_id=%d&service=ets", wd.config.HostName, order.ID),
		// 	},
		// }

		// // Send email to sqs
		// if err := wd.SendEmail(mailTemplate); err != nil {
		// 	return err
		// }

		// if err := wd.SaveHistory(models.RefTable.ServiceTask, order.ID, models.WatchdogEvent.ETSOrderUnderReviewXDays,
		// 	fmt.Sprintf("ETSOrderUnderReviewXDays  %d to user: %s", order.ID, user.Email)); err != nil {
		// 	return err
		// }
	}

	return nil
}

// SendFeedbackCompleteOrder send feedback for complete order
// https://traversal17.atlassian.net/browse/AD-6093
func (wd *WatchDogInstant) SendFeedbackCompleteOrder() error {
	var query = wd.dao.Db.PSQL.Select("so.id,so.status,so.user_id,so.updated_at").
		From("service_orders so").
		LeftJoin("ets e ON e.id = so.service_id").
		Where(squirrel.Eq{"so.status": []string{
			string(models.EtsOrderStatusUnderReview),
		}}).
		Where("e.service_type = ?", "fastlane").
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type LIKE ? AND ref_table = ? AND ref_id = so.id::text)",
			models.WatchdogEvent.ETSOrderSendFeedbackForCompletedOrder, models.RefTable.ServiceOrder,
		).
		Where("so.deleted_at IS NULL")

	sqlStr, args, _ := query.ToSql()

	var serviceOrders []models.ServiceOrder
	if err := wd.dao.Db.Db.Select(&serviceOrders, sqlStr, args...); err != nil {
		return err
	}
	for _, serviceOrder := range serviceOrders {
		resp, err := wd.etsDao.QueryServiceOrders(models.ServiceOrderFilter{
			ID:              []string{strconv.Itoa(serviceOrder.ID)},
			IncludeShipment: true,
			IncludeTasks:    true,
			Limit:           1,
		})
		if err != nil {
			return err
		}

		order := resp.Data[0]
		task := order.Tasks[0]

		provider, err := wd.etsDao.GetEtsProviderByID(order.ProviderID)
		if err != nil {
			return err
		}
		inputPods := task.InputPods.ToMapKeyValueV2(order.InputPods)

		user, err := wd.dao.GetUserByID(order.UserID)
		if err != nil {
			return err
		}

		var params = map[string]any{
			"OrderID":         order.ID,
			"URL":             fmt.Sprintf("%s/feedback?order_id=%d&service=ets", wd.config.HostName, order.ID),
			"ServiceDateTime": inputPods["travel_exit_flight_exit_timestamp"],
		}

		if task.Type == "arrival" || task.Type == "vip_arrival" {
			params["ServiceDateTime"] = inputPods["travel_enter_flight_enter_timestamp"]
		}

		serviceDate := utils.StructToJSON(params).Get("ServiceDateTime").Time()

		// Check 3 hours after service date send email to user, GMT +7 but stored as UTC ==!> GMT +0
		if (time.Now().Add(time.Hour*7).Unix() - serviceDate.Unix()) <= 3*60*60 {
			continue
		}

		if (time.Now().Unix() - serviceDate.Unix()) > 24*60*60 {
			continue
		}

		if err := wd.etsDao.UpdateServiceOrder(map[string]any{"id": order.ID, "status": models.EtsOrderStatusCompleted}, order.ID); err != nil {
			return err
		}

		cc := []string{}
		shipmentJ := utils.StructToJSON(order).Get("shipment.shipping_contact")
		shipmentEmail := shipmentJ.Get("email").String()
		if shipmentEmail != "" && shipmentEmail != user.Email {
			cc = append(cc, shipmentEmail)
		}

		bcc := []string{wd.config.EmailConfig["support"]}

		fullName := "SIR/MADAM"
		if user.GivenName != "" {
			fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(user.GivenName), strings.TrimSpace(user.Surname)))
		} else if shipmentJ.Get("given_name").String() != "" {
			fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(shipmentJ.Get("given_name").String()), strings.TrimSpace(shipmentJ.Get("surname").String())))
		}
		params["FullName"] = fullName

		message := map[string]any{
			"template_name": "send_feedback_to_user",
			"to":            user.Email,
			"cc":            cc,
			"bcc":           bcc,
			"parameters":    params,
			"attachments":   []map[string]any{},
		}

		// Send email to sqs
		if err := wd.SendEmail(message); err != nil {
			return err
		}

		if err := wd.SaveHistory(models.RefTable.ServiceOrder, order.ID, models.WatchdogEvent.ETSOrderSendFeedbackForCompletedOrder,
			fmt.Sprintf("Package ETSOrderSendFeedbackForCompletedOrder %d , An email have been sent to ets provider: %s", order.ID, provider.Contact.Email)); err != nil {
			return err
		}
	}

	return nil
}

func (wd *WatchDogInstant) SendRemindToFastlaneOrderBeforeXHours() error {
	var query = wd.dao.Db.PSQL.Select("so.id, so.status").
		From("service_orders so").
		LeftJoin("service_tasks st ON st.order_id = so.id").
		LeftJoin("ets ON ets.id = so.service_id").
		Where("so.created_at > ?", time.Now().AddDate(0, -3, 0)).
		Where("so.status NOT IN ('cancelled','completed')").
		Where("st.status != 'cancelled'").
		Where("ets.service_type = 'fastlane'").
		Where(squirrel.Or{
			squirrel.Expr(`(NULLIF(st.input_pod_values->>'travel_enter_flight_enter_timestamp', '')::timestamp with time zone - interval '7 hours')
			  BETWEEN now() AND (now() + interval '2 hours')`),
			squirrel.Expr(`(NULLIF(st.input_pod_values->>'travel_exit_flight_exit_timestamp', '')::timestamp with time zone - interval '7 hours')
			  BETWEEN now() AND (now() + interval '4 hours')`),
		}).
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type LIKE ? AND ref_table = ? AND ref_id = so.id::text)",
			models.WatchdogEvent.ETSOrderSendFastlaneRemind1, models.RefTable.ServiceOrder,
		)

	sqlStr, args, _ := query.ToSql()

	var serviceOrders []models.ServiceOrder
	if err := wd.dao.Db.Db.Select(&serviceOrders, sqlStr, args...); err != nil {
		return err
	}
	for _, serviceOrder := range serviceOrders {
		if !serviceOrder.IsPaid() {
			continue
		}
		resp, err := wd.etsDao.QueryServiceOrders(models.ServiceOrderFilter{
			ID:              []string{strconv.Itoa(serviceOrder.ID)},
			IncludeShipment: true,
			IncludeTasks:    true,
			IncludeService:  true,
			Limit:           1,
		})
		if err != nil {
			return err
		}

		order := resp.Data[0]
		task := order.Tasks[0]

		provider, err := wd.etsDao.GetEtsProviderByID(order.ProviderID)
		if err != nil {
			return err
		}
		inputPods := task.InputPods.ToMapKeyValueV2(order.InputPods)

		// Send SMS
		zaloGroup := provider.Contact.ZaloGroup
		if zaloGroup != "" {
			isDeparture := order.Service.Tasks[0] == "departure"
			serviceType := funk.ShortIf(cast.ToString((*order.Service.Attributes)["terminal"]) == "international", "International", "Domestic")
			serviceDirection := funk.ShortIf(isDeparture, "Departure", "Arrival")

			// Get data based on direction
			airport := cast.ToString(inputPods[lo.If(isDeparture, "travel_exit_flight_exit_airport").Else("travel_enter_flight_enter_airport")])
			flight := utils.GetStringOrText(inputPods[lo.If(isDeparture, "travel_exit_flight_exit_flight").Else("travel_enter_flight_enter_flight")])
			meetupTime := lo.If(isDeparture, cast.ToTime(inputPods["travel_exit_flight_meetup_time"]).Format("Jan 02, 2006 - 15:04")).Else("")
			dateTime := cast.ToTime(inputPods[lo.If(isDeparture, "travel_exit_flight_exit_timestamp").Else("travel_enter_flight_enter_timestamp")])

			oldDateTimeStr := dateTime.Format("Jan 02, 2006 - 15:04")
			if flightData, err := flightstats.GetFlightDataV2(flight, dateTime); err == nil {
				if isDeparture {
					dateTime = cast.ToTime(flightData[lo.If(flightData["actualDepartureUTC"] != "", "actualDepartureUTC").Else("scheduledDepartureUTC")])
				} else {
					dateTime = cast.ToTime(flightData[lo.If(flightData["actualArrivalUTC"] != "", "actualArrivalUTC").Else("scheduledArrivalUTC")])
				}
			}
			newDateTimeStr := dateTime.Format("Jan 02, 2006 - 15:04")

			// Get contact method
			contactMethod := ""
			if method := cast.ToString(inputPods["travel_traveler_contact_info_social_network_channel"]); method != "" {
				contactMethod = method + " - " + utils.OneOf(
					utils.StructToJSON(inputPods["travel_traveler_contact_info_social_network_id"]).Get("phone").String(),
					utils.StructToJSON(inputPods["travel_traveler_contact_info_social_network_id"]).String(),
				)
			}

			// Get driver contact
			driverName := cast.ToString(inputPods["travel_driver_information_full_name"])
			driverPhone := utils.StructToJSON(inputPods["travel_driver_information_phone"]).Get("phone").String()
			driverContact := ""
			if val := cast.ToString(inputPods["travel_driver_information_social_network_id"]); val != "" {
				driverContact = cast.ToString(inputPods["travel_driver_information_social_network_channel"]) + " - " + cast.ToString(inputPods["travel_driver_information_social_network_id"])
			}
			driverLicensePlate := cast.ToString(inputPods["travel_driver_information_license_plate"])

			// Other data
			welcomeName := cast.ToString(inputPods["travel_passenger_info_welcome_name"])
			numTravelers := cast.ToInt(inputPods["travel_passenger_info_no_of_traveler"])
			note := cast.ToString(inputPods["travel_additional_information_note"])
			bookingClass := lo.If(cast.ToString(inputPods["travel_exit_flight_airline_booking_class"]) == "business", "Business").Else("Economy")

			webBaseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("web_base_url").String()

			// Build message
			var parts []string
			parts = append(parts, fmt.Sprintf("[<b><span style=\"color:#f27806\">REMINDER</span></b>] Order# <b>%d</b> - Request for Fastlane <b>%s %s</b> service as below:",
				order.ID, serviceType, serviceDirection))

			// Add fields only if they have values
			addField := func(label string, value interface{}) {
				if v := fmt.Sprintf("%v", value); v != "" && v != "0" && v != "Jan 01, 0001 - 00:00" {
					parts = append(parts, fmt.Sprintf("%s: <b>%v</b>", label, value))
				}
			}

			addField("Airport", airport)
			addField("Flight#", flight)
			addField("Meet Up Time", meetupTime)
			if oldDateTimeStr != newDateTimeStr && newDateTimeStr != "Jan 01, 0001 - 00:00" {
				parts = append(parts, fmt.Sprintf("Date Time: <s>%s</s> -> <b>%s</b>", oldDateTimeStr, newDateTimeStr))
			} else {
				addField("Date Time", oldDateTimeStr)
			}
			addField("Welcome Name", welcomeName)
			addField("Contact", contactMethod)
			addField("Driver Name", driverName)
			addField("Driver Phone", driverPhone)
			addField("Driver Contact", driverContact)
			addField("Driver License Plate", driverLicensePlate)
			addField("Number of travelers", numTravelers)
			addField("Booking Class", bookingClass)
			addField("Note", note)

			parts = append(parts, fmt.Sprintf("To view order details, please click the below button:\n%s/dashboard/orders/detail?order_id=%d",
				webBaseURL, order.ID))

			// Send message
			message := strings.Join(parts, "\n")
			if err := zalo.SendZaloSMS(zaloGroup, message); err != nil {
				fmt.Println(err)
			}
		}

		if err := wd.SaveHistory(models.RefTable.ServiceOrder, order.ID, models.WatchdogEvent.ETSOrderSendFastlaneRemind1,
			fmt.Sprintf("Order ETSOrderSendFastlaneRemind1 %d ", order.ID)); err != nil {
			return err
		}
	}

	return nil
}

func (wd *WatchDogInstant) SendRemindToFastlaneOrderBeforeXHoursInWhatsApp() error {
	var query = wd.dao.Db.PSQL.Select("so.id, so.status").
		From("service_orders so").
		LeftJoin("service_tasks st ON st.order_id = so.id").
		LeftJoin("ets ON ets.id = so.service_id").
		Where("so.created_at > ?", time.Now().AddDate(0, -3, 0)).
		Where("so.status NOT IN ('cancelled','completed')").
		Where("st.status != 'cancelled'").
		Where("ets.service_type = 'fastlane'").
		Where(squirrel.Or{
			squirrel.Expr(`(NULLIF(st.input_pod_values->>'travel_enter_flight_enter_timestamp', '')::timestamp with time zone - interval '7 hours')
			  BETWEEN now() AND (now() + interval '6 hours')`),
			squirrel.Expr(`(NULLIF(st.input_pod_values->>'travel_exit_flight_exit_timestamp', '')::timestamp with time zone - interval '7 hours')
			  BETWEEN now() AND (now() + interval '6 hours')`),
		}).
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type LIKE ? AND ref_table = ? AND ref_id = so.id::text)",
			models.WatchdogEvent.ETSOrderSendFastlaneRemindWhatsApp, models.RefTable.ServiceOrder,
		)

	sqlStr, args, _ := query.ToSql()

	var serviceOrders []models.ServiceOrder
	if err := wd.dao.Db.Db.Select(&serviceOrders, sqlStr, args...); err != nil {
		return err
	}
	for _, serviceOrder := range serviceOrders {
		if !serviceOrder.IsPaid() {
			continue
		}
		resp, err := wd.etsDao.QueryServiceOrders(models.ServiceOrderFilter{
			ID:              []string{strconv.Itoa(serviceOrder.ID)},
			IncludeShipment: true,
			IncludeTasks:    true,
			IncludeService:  true,
			Limit:           1,
		})
		if err != nil {
			return err
		}

		order := resp.Data[0]
		task := order.Tasks[0]

		inputPods := task.InputPods.ToMapKeyValueV2(order.InputPods)

		// Send SMS
		welcomeName := cast.ToString(inputPods["travel_passenger_info_welcome_name"])
		attJ := utils.StructToJSON(order.Service.Attributes)
		whatsAppGroup := attJ.Get("notifications.whatsapp").String()

		if whatsAppGroup != "" && cast.ToString(inputPods["travel_traveler_contact_info_social_network_channel"]) == "whatsapp" {
			message := ""
			if order.Service.Tasks[0] == "arrival" {
				message = fmt.Sprintf(
					strings.Join([]string{
						"Hi %s, this is a group chat to support your arrival Fastlane on %s.",
						"Upon arrival at the airport, proceed towards Immigration.",
						"The staff will be there, holding a card with your welcome name is %s",
					}, "\n"),
					cast.ToString(inputPods["travel_traveler_contact_info_full_name"]),
					cast.ToTime(inputPods["travel_enter_flight_enter_timestamp"]).Format("Jan 02, 2006 - 15:04"),
					welcomeName,
				)
			} else {
				message = fmt.Sprintf(
					strings.Join([]string{
						"Hi %s, this is a group chat to support your departure Fastlane on %s.",
						"Please message us when you are about to head to the airport and when you arrive",
					}, "\n"),
					cast.ToString(inputPods["travel_traveler_contact_info_full_name"]),
					cast.ToTime(inputPods["travel_exit_flight_exit_timestamp"]).Format("Jan 02, 2006 - 15:04"),
				)
			}

			if err := whatsapp.AddUsersToGroup(whatsAppGroup, message, []string{
				strings.ReplaceAll(utils.OneOf(
					utils.StructToJSON(inputPods["travel_traveler_contact_info_social_network_id"]).Get("phone").String(),
					utils.StructToJSON(inputPods["travel_traveler_contact_info_social_network_id"]).String(),
				), "+", ""),
			}); err != nil {
				fmt.Println(err)
			}
		}

		if err := wd.SaveHistory(models.RefTable.ServiceOrder, order.ID, models.WatchdogEvent.ETSOrderSendFastlaneRemindWhatsApp,
			fmt.Sprintf("Order ETSOrderSendFastlaneRemindWhatsApp %d ", order.ID)); err != nil {
			return err
		}
	}

	return nil
}

func (wd *WatchDogInstant) SendRemindToFastlaneOrderJoinWhatsApp() error {
	var query = wd.dao.Db.PSQL.Select("so.id, so.status").
		From("service_orders so").
		LeftJoin("service_tasks st ON st.order_id = so.id").
		LeftJoin("ets ON ets.id = so.service_id").
		Where("so.created_at > ?", time.Now().AddDate(0, -3, 0)).
		Where("so.status NOT IN ('cancelled','completed')").
		Where("st.status != 'cancelled'").
		Where("ets.service_type = 'fastlane'").
		Where(squirrel.Or{
			squirrel.Expr(`(NULLIF(st.input_pod_values->>'travel_enter_flight_enter_timestamp', '')::timestamp with time zone - interval '7 hours')
			  BETWEEN now() AND (now() + interval '6 hours')`),
			squirrel.Expr(`(NULLIF(st.input_pod_values->>'travel_exit_flight_exit_timestamp', '')::timestamp with time zone - interval '7 hours')
			  BETWEEN now() AND (now() + interval '6 hours')`),
		}).
		Where("EXISTS (SELECT ref_id FROM watchdog_history WHERE type LIKE ? AND ref_table = ? AND ref_id = so.id::text)",
			models.WatchdogEvent.ETSOrderSendFastlaneRemindWhatsApp, models.RefTable.ServiceOrder,
		).
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type LIKE ? AND ref_table = ? AND ref_id = so.id::text)",
			models.WatchdogEvent.ETSOrderSendFastlaneJoinWhatsApp, models.RefTable.ServiceOrder,
		)

	sqlStr, args, _ := query.ToSql()

	var serviceOrders []models.ServiceOrder
	if err := wd.dao.Db.Db.Select(&serviceOrders, sqlStr, args...); err != nil {
		return err
	}

	whatsAppGroups, err := whatsapp.GetGroupList()
	if err != nil {
		return err
	}

	for _, serviceOrder := range serviceOrders {
		if !serviceOrder.IsPaid() {
			continue
		}
		resp, err := wd.etsDao.QueryServiceOrders(models.ServiceOrderFilter{
			ID:              []string{strconv.Itoa(serviceOrder.ID)},
			IncludeShipment: true,
			IncludeTasks:    true,
			IncludeService:  true,
			Limit:           1,
		})
		if err != nil {
			return err
		}

		order := resp.Data[0]
		task := order.Tasks[0]

		inputPods := task.InputPods.ToMapKeyValueV2(order.InputPods)

		// Send SMS
		welcomeName := cast.ToString(inputPods["travel_passenger_info_welcome_name"])
		attJ := utils.StructToJSON(order.Service.Attributes)
		whatsAppGroup := attJ.Get("notifications.whatsapp").String()

		if whatsAppGroup != "" && cast.ToString(inputPods["travel_traveler_contact_info_social_network_channel"]) == "whatsapp" {
			whatsAppGroupInfo, ok := lo.Find(whatsAppGroups, func(g whatsapp.Group) bool {
				return g.ID == whatsAppGroup
			})
			if !ok {
				continue
			}

			inputWhatsAppPhone := strings.ReplaceAll(utils.OneOf(
				utils.StructToJSON(inputPods["travel_traveler_contact_info_social_network_id"]).Get("phone").String(),
				utils.StructToJSON(inputPods["travel_traveler_contact_info_social_network_id"]).String(),
			), "+", "")

			whatsAppMember, ok := lo.Find(whatsAppGroupInfo.Participants, func(p whatsapp.Participant) bool {
				return strings.Contains(p.JID, inputWhatsAppPhone)
			})
			if !ok {
				continue
			}
			fmt.Println(whatsAppMember)

			message := ""
			if order.Service.Tasks[0] == "arrival" {
				message = fmt.Sprintf(
					strings.Join([]string{
						"Hi %s, this is a group chat to support your arrival Fastlane on %s for order %d.",
						"Upon arrival at the airport, proceed towards Immigration.",
						"The staff will be there, holding a card with your welcome name is %s",
					}, "\n"),
					// cast.ToString(inputPods["travel_traveler_contact_info_full_name"]),
					"@"+inputWhatsAppPhone,
					cast.ToTime(inputPods["travel_enter_flight_enter_timestamp"]).Format("Jan 02, 2006 - 15:04"),
					order.ID,
					welcomeName,
				)
			} else {
				message = fmt.Sprintf(
					strings.Join([]string{
						"Hi %s, this is a group chat to support your departure Fastlane on %s for order %d.",
						"Please message us when you are about to head to the airport and when you arrive",
					}, "\n"),
					// cast.ToString(inputPods["travel_traveler_contact_info_full_name"]),
					"@"+inputWhatsAppPhone,
					cast.ToTime(inputPods["travel_exit_flight_exit_timestamp"]).Format("Jan 02, 2006 - 15:04"),
					order.ID,
				)
			}

			if err := whatsapp.SendMessageToGroup(whatsAppGroup, message); err != nil {
				fmt.Println(err)
			}
		}

		if err := wd.SaveHistory(models.RefTable.ServiceOrder, order.ID, models.WatchdogEvent.ETSOrderSendFastlaneJoinWhatsApp,
			fmt.Sprintf("Order ETSOrderSendFastlaneJoinWhatsApp %d ", order.ID)); err != nil {
			return err
		}
	}

	return nil
}

func (wd *WatchDogInstant) SendRemindWhenCreateFastlaneOrder() error {
	var query = wd.dao.Db.PSQL.Select("so.id, so.status").
		From("service_orders so").
		// Where("so.created_at > ?", time.Now().AddDate(0, 0, -30)).
		Where("so.created_at > ?", time.Now().AddDate(0, -3, 0)).
		LeftJoin("service_tasks st ON st.order_id = so.id").
		LeftJoin("ets  ON ets.id = so.service_id").
		Where("ets.service_type = 'fastlane'").
		Where(squirrel.NotEq{"so.status": []string{
			models.EtsOrderStatusOpen,
			models.EtsOrderStatusPendingPayment,
			models.EtsOrderStatusWaitingPayment,
			models.EtsOrderStatusCancelled,
		}}).
		Where(squirrel.Or{
			squirrel.Expr(`(((st.input_pod_values->>'travel_enter_flight_enter_timestamp')::timestamp with time zone - interval '7 hours') >  now())`),
			squirrel.Expr(`(((st.input_pod_values->>'travel_exit_flight_exit_timestamp')::timestamp with time zone - interval '7 hours') >  now())`),
		}).
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type LIKE ? AND ref_table = ? AND ref_id = so.id::text)",
			models.WatchdogEvent.ETSOrderSendFastlaneRemind2, models.RefTable.ServiceOrder,
		)

	sqlStr, args, _ := query.ToSql()

	var serviceOrders []models.ServiceOrder
	if err := wd.dao.Db.Db.Select(&serviceOrders, sqlStr, args...); err != nil {
		return err
	}
	for _, serviceOrder := range serviceOrders {
		if !serviceOrder.IsPaid() {
			continue
		}
		resp, err := wd.etsDao.QueryServiceOrders(models.ServiceOrderFilter{
			ID:              []string{strconv.Itoa(serviceOrder.ID)},
			IncludeShipment: true,
			IncludeTasks:    true,
			Limit:           1,
		})
		if err != nil {
			return err
		}

		order := resp.Data[0]
		task := order.Tasks[0]

		provider, err := wd.etsDao.GetEtsProviderByID(order.ProviderID)
		if err != nil {
			return err
		}
		inputPods := task.InputPods.ToMapKeyValueV2(order.InputPods)

		// Send SMS
		zaloGroup := provider.Contact.ZaloGroup
		if zaloGroup != "" {
			isDeparture := order.Service.Tasks[0] == "departure"
			serviceType := funk.ShortIf(cast.ToString((*order.Service.Attributes)["terminal"]) == "international", "International", "Domestic")
			serviceDirection := funk.ShortIf(isDeparture, "Departure", "Arrival")

			// Get data based on direction
			airport := cast.ToString(inputPods[lo.If(isDeparture, "travel_exit_flight_exit_airport").Else("travel_enter_flight_enter_airport")])
			flight := utils.GetStringOrText(inputPods[lo.If(isDeparture, "travel_exit_flight_exit_flight").Else("travel_enter_flight_enter_flight")])
			meetupTime := lo.If(isDeparture, cast.ToTime(inputPods["travel_exit_flight_meetup_time"]).Format("Jan 02, 2006 - 15:04")).Else("")
			dateTime := cast.ToTime(inputPods[lo.If(isDeparture, "travel_exit_flight_exit_timestamp").Else("travel_enter_flight_enter_timestamp")]).Format("Jan 02, 2006 - 15:04")

			// Get contact method
			contactMethod := ""
			if method := cast.ToString(inputPods["travel_traveler_contact_info_social_network_channel"]); method != "" {
				contactMethod = method + " - " + utils.OneOf(
					utils.StructToJSON(inputPods["travel_traveler_contact_info_social_network_id"]).Get("phone").String(),
					utils.StructToJSON(inputPods["travel_traveler_contact_info_social_network_id"]).String(),
				)
			}

			// Get driver contact
			driverName := cast.ToString(inputPods["travel_driver_information_full_name"])
			driverPhone := utils.StructToJSON(inputPods["travel_driver_information_phone"]).Get("phone").String()
			driverContact := ""
			if val := cast.ToString(inputPods["travel_driver_information_social_network_id"]); val != "" {
				driverContact = cast.ToString(inputPods["travel_driver_information_social_network_channel"]) + " - " + cast.ToString(inputPods["travel_driver_information_social_network_id"])
			}
			driverLicensePlate := cast.ToString(inputPods["travel_driver_information_license_plate"])
			// Other data
			welcomeName := cast.ToString(inputPods["travel_passenger_info_welcome_name"])
			numTravelers := cast.ToInt(inputPods["travel_passenger_info_no_of_traveler"])
			note := cast.ToString(inputPods["travel_additional_information_note"])
			bookingClass := lo.If(cast.ToString(inputPods["travel_exit_flight_airline_booking_class"]) == "business", "Business").Else("Economy")

			webBaseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("web_base_url").String()

			// Build message
			var parts []string
			parts = append(parts, fmt.Sprintf("Order# <b>%d</b> - Request for Fastlane <b>%s %s</b> service as below:",
				order.ID, serviceType, serviceDirection))

			// Add fields only if they have values
			addField := func(label string, value interface{}) {
				if v := fmt.Sprintf("%v", value); v != "" && v != "0" {
					parts = append(parts, fmt.Sprintf("%s: <b>%v</b>", label, value))
				}
			}

			addField("Airport", airport)
			addField("Flight#", flight)
			addField("Meet Up Time", meetupTime)
			addField("Date Time", dateTime)
			addField("Welcome Name", welcomeName)
			addField("Contact", contactMethod)
			addField("Driver Name", driverName)
			addField("Driver Phone", driverPhone)
			addField("Driver Contact", driverContact)
			addField("Driver License Plate", driverLicensePlate)
			addField("Number of travelers", numTravelers)
			addField("Booking Class", bookingClass)
			addField("Note", note)

			parts = append(parts, fmt.Sprintf("To view order details, please click the below button:\n%s/dashboard/orders/detail?order_id=%d",
				webBaseURL, order.ID))

			// Send message
			message := strings.Join(parts, "\n")
			if err := zalo.SendZaloSMS(zaloGroup, message); err != nil {
				fmt.Println(err)
			}
		}

		if err := wd.SaveHistory(models.RefTable.ServiceOrder, order.ID, models.WatchdogEvent.ETSOrderSendFastlaneRemind2,
			fmt.Sprintf("Order ETSOrderSendFastlaneRemind2 %d ", order.ID)); err != nil {
			return err
		}
	}

	return nil
}

func (wd *WatchDogInstant) SendRemindToUrgentEVisa() error {
	var query = wd.dao.Db.PSQL.Select("so.id, so.status").
		From("service_orders so").
		Where("so.expect_issue_visa_at < ?", time.Now().In(time.FixedZone("GMT+7", 7*60*60)).Add(-10*time.Minute)).
		LeftJoin("ets  ON ets.id = so.service_id").
		Where(squirrel.NotEq{"so.status": []string{
			models.EtsOrderStatusOpen,
			models.EtsOrderStatusPendingPayment,
			models.EtsOrderStatusWaitingPayment,
			models.EtsOrderStatusCancelled,
			models.EtsOrderStatusCompleted,
		}}).
		Where("NOT EXISTS (SELECT ref_id FROM watchdog_history WHERE type LIKE ? AND ref_table = ? AND ref_id = so.id::text)",
			models.WatchdogEvent.ETSOrderSendUrgentEvisaRemind, models.RefTable.ServiceOrder,
		)

	sqlStr, args, _ := query.ToSql()

	var serviceOrders []models.ServiceOrder
	if err := wd.dao.Db.Db.Select(&serviceOrders, sqlStr, args...); err != nil {
		return err
	}
	for _, serviceOrder := range serviceOrders {
		if !serviceOrder.IsPaid() {
			continue
		}

		if err := notification.NotifyViaZaloForRemindUrgentVisa(wd.etsDao, int64(serviceOrder.ID)); err != nil {
			return err
		}

		if err := wd.SaveHistory(models.RefTable.ServiceOrder, serviceOrder.ID, models.WatchdogEvent.ETSOrderSendUrgentEvisaRemind,
			fmt.Sprintf("Order ETSOrderSendUrgentEvisaRemind %d ", serviceOrder.ID)); err != nil {
			return err
		}
	}

	return nil
}
