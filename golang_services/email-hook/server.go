package main

import (
	"bytes"
	"encoding/json"
	"io"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/emersion/go-imap"
	"github.com/emersion/go-imap/client"
	"github.com/emersion/go-message/mail"
	"github.com/go-resty/resty/v2"
	"github.com/samber/lo"
	"github.com/urfave/cli"
)

var DEV_CONFIG = map[string]string{
	"WEBHOOK_URL":       "https://api.ariadirectcorp.com/v1/pkg/internal/zelle-webhook",
	"EMAIL":             "<EMAIL>",
	"APP_PASSWORD":      "ktfu vvmw llkm hnsi",
	"ANTHROPIC_API_KEY": "************************************************************************************************************",
}

var PROD_CONFIG = map[string]string{
	"WEBHOOK_URL":       "https://api.ariadirect.com/v1/pkg/internal/zelle-webhook",
	"EMAIL":             "<EMAIL>",
	"APP_PASSWORD":      "ncbu dnut pbyi jqfv",
	"ANTHROPIC_API_KEY": "************************************************************************************************************",
}

var CONFIG = lo.If(os.Getenv("ad_env") == "prod", PROD_CONFIG).Else(DEV_CONFIG)

// API handlers
func startService() cli.ActionFunc {
	// Configuration
	config := EmailConfig{
		Email:        CONFIG["EMAIL"],
		AppPassword:  CONFIG["APP_PASSWORD"],
		PollInterval: 30 * time.Second,
	}

	return func(c *cli.Context) error {
		checkEmails(config)
		return nil
	}
}

type EmailConfig struct {
	Email        string
	AppPassword  string
	PollInterval time.Duration
}

func checkEmails(config EmailConfig) {
	log.Printf("Starting email monitoring for %s", config.Email)

	// Get initial highest UID on startup
	startingUID, err := getInitialUID(config)
	if err != nil {
		log.Fatalf("Failed to get initial UID: %v", err)
	}

	// Keep track of logged UIDs
	loggedUIDs := make(map[uint32]bool)

	for {
		// Connect to Gmail IMAP server
		c, err := client.DialTLS("imap.gmail.com:993", nil)
		if err != nil {
			log.Printf("Failed to connect: %v", err)
			time.Sleep(config.PollInterval)
			continue
		}

		// Login
		if err := c.Login(config.Email, config.AppPassword); err != nil {
			log.Printf("Failed to login: %v", err)
			c.Logout()
			time.Sleep(config.PollInterval)
			continue
		}

		// Select INBOX
		mbox, err := c.Select("INBOX", false)
		if err != nil {
			log.Printf("Failed to select INBOX: %v", err)
			c.Logout()
			time.Sleep(config.PollInterval)
			continue
		}

		// Check for new messages
		if mbox.Messages > startingUID {
			seqset := new(imap.SeqSet)
			seqset.AddRange(startingUID+1, mbox.Messages)

			messages := make(chan *imap.Message, 10)
			done := make(chan error, 1)
			go func() {
				done <- c.Fetch(seqset, []imap.FetchItem{
					imap.FetchEnvelope,
					imap.FetchRFC822,
				}, messages)
			}()

			// Process new messages
			for msg := range messages {
				if !loggedUIDs[msg.SeqNum] {
					// Get email body and HTML
					fromEmail := msg.Envelope.From[0].MailboxName + "@" + msg.Envelope.From[0].HostName
					plainText, html, err := getEmailContent(msg)
					if err != nil {
						log.Printf("Error getting email content: %v", err)
						continue
					}

					log.Printf(`New email received:
Subject: %s
From: %s
Date: %v
Plain Text Body: %s
-------------------`,
						msg.Envelope.Subject,
						fromEmail,
						msg.Envelope.Date,
						plainText,
					)

					if os.Getenv("ad_env") == "prod" && fromEmail != "<EMAIL>" {
						log.Printf("Ignoring email from %s", fromEmail)
						continue
					}

					extractData := extractZellePaymentInfoByAI(html)
					log.Printf("Extracted Zelle payment data: %v", extractData)
					if len(extractData) > 0 && extractData["transaction"] != "" {
						resp, err := resty.New().R().
							SetHeader("Content-Type", "application/json").
							SetHeader("x-ad-token", "2hNwPul_SppFM_iUlV1Wng").
							SetBody(extractData).
							Post(CONFIG["WEBHOOK_URL"])

						if err != nil {
							log.Printf("Error sending webhook: %v", err)
						} else {
							log.Printf("Webhook sent successfully. Status: %v, Response: %v",
								resp.StatusCode(), resp.String())
						}
					}

					loggedUIDs[msg.SeqNum] = true

				}
			}

			if err := <-done; err != nil {
				log.Printf("Error fetching messages: %v", err)
			}
		}

		// Cleanup
		c.Logout()

		// Wait before next check
		time.Sleep(config.PollInterval)
	}
}

// getEmailContent extracts plain text and HTML from email message
func getEmailContent(msg *imap.Message) (plainText, html string, err error) {
	// Get the RFC822 body section
	r := msg.GetBody(&imap.BodySectionName{})
	if r == nil {
		return "", "", nil
	}

	// Create a buffer to read the content
	var buf bytes.Buffer
	_, err = io.Copy(&buf, r)
	if err != nil {
		return "", "", err
	}

	// Parse the email message
	mr, err := mail.CreateReader(bytes.NewReader(buf.Bytes()))
	if err != nil {
		return "", "", err
	}

	// Process email parts
	for {
		p, err := mr.NextPart()
		if err == io.EOF {
			break
		}
		if err != nil {
			return "", "", err
		}

		switch h := p.Header.(type) {
		case *mail.InlineHeader:
			contentType, _, _ := h.ContentType()
			b, _ := io.ReadAll(p.Body)

			if contentType == "text/plain" {
				plainText = string(b)
			} else if contentType == "text/html" {
				html = string(b)
			}
		}
	}

	return plainText, html, nil
}

// getInitialUID gets the highest UID at service startup
func getInitialUID(config EmailConfig) (uint32, error) {
	c, err := client.DialTLS("imap.gmail.com:993", nil)
	if err != nil {
		return 0, err
	}
	defer c.Logout()

	if err := c.Login(config.Email, config.AppPassword); err != nil {
		return 0, err
	}

	mbox, err := c.Select("INBOX", false)
	if err != nil {
		return 0, err
	}

	return mbox.Messages, nil
}

func extractZellePaymentInfoByAI(html string) map[string]any {
	client := resty.New()

	// Prepare the request body
	requestBody := map[string]any{
		"model":      "claude-3-sonnet-20240229",
		"max_tokens": 1024,
		"messages": []map[string]any{
			{
				"role": "user",
				"content": []any{
					map[string]string{
						"type": "text",
						"text": `Extract Zelle payment information from the following HTML email content and respond ONLY with a JSON object using this exact format, no other text:

{
  "name": string,
  "amount": float,
  "date": string,
  "transaction": string,
  "memo": string,
  "order_ids": array of strings
}

Extract these fields:
1. name: The sender's name (usually in a headline like "[NAME] sent you money")
2. amount: The payment amount in dollars (e.g., $123.45)
3. date: The date the payment was sent
4. transaction: The transaction number
5. memo: The memo text (if present)
6. order_ids: Array of any numbers found in the memo

If email not from Chase, respond with an empty string for each fields
Here's the HTML content to analyze:
` + html,
					},
				},
			},
		},
	}

	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("x-api-key", CONFIG["ANTHROPIC_API_KEY"]).
		SetHeader("anthropic-version", "2023-06-01").
		SetBody(requestBody).
		Post("https://api.anthropic.com/v1/messages")

	if err != nil {
		log.Printf("Error calling Anthropic API: %v", err)
		return make(map[string]any)
	}

	// Parse the response
	var responseData struct {
		Content []struct {
			Text string `json:"text"`
		} `json:"content"`
	}
	if err := json.Unmarshal(resp.Body(), &responseData); err != nil {
		log.Printf("Error parsing Anthropic response: %v", err)
		return make(map[string]any)
	}

	if len(responseData.Content) == 0 {
		log.Printf("Empty response from Anthropic API")
		return make(map[string]any)
	}

	// Parse the JSON from the response text
	var analysis map[string]any
	if err := json.Unmarshal([]byte(responseData.Content[0].Text), &analysis); err != nil {
		log.Printf("Error parsing JSON from Anthropic response: %v", err)
		return make(map[string]any)
	}

	// Convert amount to float64 if it exists
	if amount, ok := analysis["amount"]; ok {
		switch v := amount.(type) {
		case string:
			if f, err := strconv.ParseFloat(v, 64); err == nil {
				analysis["amount"] = f
			}
		case float64:
			// Already in correct format
		default:
			delete(analysis, "amount")
		}
	}

	return analysis
}
