package main

import (
	"log"
	"os"

	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
)

func main() {
	app := cli.NewApp()
	app.Name = "email-temp"
	app.Usage = "Email Temp Service"
	app.Commands = []cli.Command{
		serviceCmd(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}
}

func serviceCmd() cli.Command {
	return cli.Command{
		Name:      "service",
		ShortName: "svc",
		Usage:     "Start master data service for Aria",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:   "port",
				Usage:  "Port the server listens to",
				EnvVar: "PORT",
				Value:  "3000",
			},
			cli.StringFlag{
				Name:   "bt-panel",
				Value:  "https://209.74.72.129:16112",
				Usage:  "AAPanel URL",
				EnvVar: "BT_PANEL",
			},
			cli.StringFlag{
				Name:   "bt-key",
				Value:  "s8h9Bh8n5lDfG9MFwVB14cPgKxcvhvGD",
				Usage:  "AAPanel API key",
				EnvVar: "BT_KEY",
			},
			flags.LogLevelFlag,
		},
		Action: startService(),
	}
}
