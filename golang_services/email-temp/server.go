package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/urfave/cli"
)

// API handlers
func startService() cli.ActionFunc {
	if err := CleanupCookieFiles(); err != nil {
		fmt.Printf("Warning: Failed to cleanup cookie files: %v\n", err)
	}

	return func(c *cli.Context) error {
		port := c.String("port")
		btPanel := c.String("bt-panel")
		btKey := c.String("bt-key")

		// Initialize BT client
		btClient := NewBTClient(btPanel, btKey)

		// Setup Gin
		r := gin.New()
		r.Use(gin.Logger(), gin.Recovery())

		// API routes
		g := r.Group("/v1/email-temp")
		{
			g.GET("version", func(c *gin.Context) {
				c.JSO<PERSON>(200, gin.H{"success": true, "version": "1.0.0"})
			})

			g.<PERSON>("inboxs", func(c *gin.Context) {
				email := c.Query("email")
				pageStr := c.<PERSON>fault<PERSON>("page", "1")

				if email == "" {
					c.JSON(400, gin.H{"success": false, "message": "Email parameter is required"})
					return
				}

				page, err := strconv.Atoi(pageStr)
				if err != nil {
					page = 1
				}

				userEmail := email

				if strings.Contains(email, "@ariadirectcorp.com") {
					email = "<EMAIL>"
				}
				if strings.Contains(email, "@innovationjourney.org") {
					email = "<EMAIL>"
				}

				emails, err := btClient.GetEmails(email, page)
				if err != nil {
					c.JSON(500, gin.H{"success": false, "message": err.Error()})
					return
				}

				userEmails := []EmailMessage{}
				for _, btEmail := range emails.Message.Data {
					if btEmail.To == "<"+userEmail+">" {
						userEmails = append(userEmails, btEmail)
					}
				}

				// Extract current page and total count from the page HTML string if possible
				currentPage := 1
				totalCount := len(userEmails)

				c.JSON(200, gin.H{
					"success": true,
					"data":    userEmails,
					"page":    currentPage,
					"total":   totalCount,
				})
			})

			g.GET("message", func(c *gin.Context) {
				email := c.Query("email")
				path := c.Query("path")

				if email == "" || path == "" {
					c.JSON(400, gin.H{
						"success": false,
						"message": "Email and path parameters are required",
					})
					return
				}

				content, err := btClient.GetEmailContent(email, path)
				if err != nil {
					c.JSON(500, gin.H{"success": false, "message": err.Error()})
					return
				}

				c.JSON(200, gin.H{
					"success": true,
					"data":    content,
				})
			})
		}

		return r.Run(":" + port)
	}
}

func CleanupCookieFiles() error {
	files, err := filepath.Glob("./*.cookie")
	if err != nil {
		return err
	}

	for _, file := range files {
		if err := os.Remove(file); err != nil {
			return fmt.Errorf("failed to remove cookie file %s: %v", file, err)
		}
		fmt.Printf("Deleted cookie file: %s\n", file)
	}
	return nil
}
