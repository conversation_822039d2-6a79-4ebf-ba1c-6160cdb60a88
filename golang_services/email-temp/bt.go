package main

import (
	"crypto/md5"
	"crypto/tls"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

// BTClient handles communication with AAPanel (BT)
type BTClient struct {
	BTPanel    string
	BTKey      string
	CookieFile string
}

// EmailMessage represents an individual email message from AAPanel
type EmailMessage struct {
	Time        int64        `json:"time"`
	Subject     string       `json:"subject"`
	Body        string       `json:"body"`
	HTML        string       `json:"html"`
	From        string       `json:"from"`
	To          string       `json:"to"`
	Attachments []Attachment `json:"attachments"`
	Path        string       `json:"path"`
}

// Attachment represents an email attachment
type Attachment struct {
	ContentType string `json:"content_type"`
	Size        int    `json:"size"`
	Name        string `json:"name"`
}

// EmailResponse represents the response structure from AAPanel
type EmailResponse struct {
	Status    int   `json:"status"`
	Timestamp int64 `json:"timestamp"`
	Message   struct {
		Status bool           `json:"status"`
		Data   []EmailMessage `json:"data"`
		Page   string         `json:"page"` // Page is actually a string containing HTML
	} `json:"message"`
}

// NewBTClient creates a new AAPanel client
func NewBTClient(btPanel, btKey string) *BTClient {
	hash := md5.Sum([]byte(btPanel))
	cookieFile := filepath.Join(".", hex.EncodeToString(hash[:])+".cookie")

	return &BTClient{
		BTPanel:    btPanel,
		BTKey:      btKey,
		CookieFile: cookieFile,
	}
}

// GetKeyData generates the authentication tokens
func (bt *BTClient) GetKeyData() map[string]string {
	nowTime := strconv.FormatInt(time.Now().Unix(), 10)

	h := md5.New()
	h.Write([]byte(bt.BTKey))
	btKeyMd5 := hex.EncodeToString(h.Sum(nil))

	h = md5.New()
	h.Write([]byte(nowTime + btKeyMd5))
	requestToken := hex.EncodeToString(h.Sum(nil))

	return map[string]string{
		"request_token": requestToken,
		"request_time":  nowTime,
	}
}

// ReadCookieFile reads the cookie file or creates it if it doesn't exist
func (bt *BTClient) ReadCookieFile() (string, error) {
	content, err := os.ReadFile(bt.CookieFile)
	if err != nil {
		// Create an empty file if it doesn't exist
		if os.IsNotExist(err) {
			err = os.WriteFile(bt.CookieFile, []byte(""), 0644)
			if err != nil {
				return "", err
			}
			return "", nil
		}
		return "", err
	}

	return string(content), nil
}

// WriteCookieFile writes cookie data to the file
func (bt *BTClient) WriteCookieFile(cookieString string) error {
	return os.WriteFile(bt.CookieFile, []byte(cookieString), 0644)
}

// HTTPPostCookie sends an HTTP POST request with cookie handling
func (bt *BTClient) HTTPPostCookie(urlPath string, data map[string]string, timeout time.Duration) ([]byte, error) {
	// Read cookie content
	cookieContent, err := bt.ReadCookieFile()
	if err != nil {
		return nil, err
	}

	// Prepare form data
	formData := url.Values{}
	for key, value := range data {
		formData.Add(key, value)
	}

	// Create request
	req, err := http.NewRequest("POST", urlPath, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, err
	}

	// Set headers
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	if cookieContent != "" {
		req.Header.Set("Cookie", cookieContent)
	}

	// Create HTTP client with TLS configuration to skip verification
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Handle cookies
	if cookies := resp.Header.Values("Set-Cookie"); len(cookies) > 0 {
		bt.WriteCookieFile(strings.Join(cookies, "; "))
	}

	// Read response body
	return io.ReadAll(resp.Body)
}

// GetEmails retrieves emails for a specific email address
func (bt *BTClient) GetEmails(email string, page int) (*EmailResponse, error) {
	url := fmt.Sprintf("%s/v2/plugin?action=a&name=mail_sys&s=get_mails", bt.BTPanel)

	// Prepare data
	postData := bt.GetKeyData()
	postData["username"] = email
	postData["p"] = strconv.Itoa(page)

	// Send request
	responseData, err := bt.HTTPPostCookie(url, postData, 60*time.Second)
	if err != nil {
		return nil, err
	}

	// Debug the response (consider logging in production)
	// fmt.Println("Raw response:", string(responseData))

	// Parse response
	var response EmailResponse
	if err := json.Unmarshal(responseData, &response); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v, raw: %s", err, string(responseData))
	}

	return &response, nil
}

// GetEmailContent retrieves the content of a specific email
func (bt *BTClient) GetEmailContent(email string, path string) (map[string]any, error) {
	url := fmt.Sprintf("%s/v2/plugin?action=a&name=mail_sys&s=get_mail_body", bt.BTPanel)

	// Prepare data
	postData := bt.GetKeyData()
	postData["username"] = email
	postData["path"] = path

	// Send request
	responseData, err := bt.HTTPPostCookie(url, postData, 60*time.Second)
	if err != nil {
		return nil, err
	}

	// Parse the response
	var result map[string]any
	if err := json.Unmarshal(responseData, &result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v, raw: %s", err, string(responseData))
	}

	return result, nil
}

// Restart restarts the email service
func (bt *BTClient) Restart(email string, page int) (*EmailResponse, error) {
	url := fmt.Sprintf("%s/v2/system?action=ReWeb", bt.BTPanel)

	// Prepare data
	postData := bt.GetKeyData()
	postData["action"] = "ReWeb"

	// Send request
	responseData, err := bt.HTTPPostCookie(url, postData, 60*time.Second)
	if err != nil {
		return nil, err
	}

	// Parse response
	var response EmailResponse
	if err := json.Unmarshal(responseData, &response); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v, raw: %s", err, string(responseData))
	}

	return &response, nil
}
