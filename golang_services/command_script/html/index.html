<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Import Excel To Database</title>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/bulma/0.8.2/css/bulma.min.css'>
    <link rel='stylesheet' href='https://unpkg.com/bulma-prefers-dark'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.13.0/css/all.min.css'>
    <link rel="stylesheet" href="./style.css">

</head>

<body>
    <!-- partial:index.partial.html -->
    <div class="container">
        <h1 class="title azure">Import Excel To Database</h1>
        <h2 class="subtitle">Upload a file and see logs below:</h2>
        <div class="select ">
            <select>
                <option selected value="VISA">VISA</option>
                <option value="FASTLANE">FASTLANE</option>
                <option value="PASSPORT">PASSPORT</option>
                <option value="GLOBAL_ENTRY">GLOBAL_ENTRY</option>
                <option value="ID_PHOTO">ID_PHOTO</option>
                <option value="TSA_PRECHECK">TSA_PRECHECK</option>
                <option value="SENTRI">SENTRI</option>
                <option value="NEXUS">NEXUS</option>
                <option value="FAST_TRUCK">FAST_TRUCK</option>
                <option value="COVID_TEST">COVID_TEST</option>
                <option value="COUNTRY_TOURIST">COUNTRY_TOURIST</option>
            </select>
        </div>
        <div class="file has-name is-boxed is-medium" style="display: block;">

            <label class="file-label">


                <input class="file-input" type="file" id="file-upload">
                <span class="file-cta">
                    <span class="file-icon"><i class="fas fa-upload"></i></span>
                    <span class="file-label">Choose an file...</span>
                </span>

                <article class="message">
                    <div class="message-header">
                        <p>Script logs:</p>
                        <button class="delete" aria-label="delete"></button>
                    </div>
                    <div class="message-body" style=" white-space: pre;">
                        Wait for some logs
                    </div>
                </article>
            </label>
        </div>


    </div>
    <!-- partial -->
    <script>
        // Select the input element using
        // document.querySelector
        var input = document.querySelector(".file-input");

        // Bind an listener to onChange event of the input
        input.onchange = function () {
            if (input.files.length > 0) {
                console.log(input.files[0])
                var formdata = new FormData();
                formdata.append("file", input.files[0], input.files[0].name);
                // formdata.append("env", "VISA");

                var requestOptions = {
                    method: 'POST',
                    headers: {},
                    body: formdata,
                    redirect: 'follow'
                };

                fetch("https://aria-backend.leoit.xyz/v1/command/python/script/run?env=" + document.querySelector('select').value, requestOptions)
                    .then(response => response.text())
                    .then(result => {
                        document.querySelector('.message-body').textContent = result.replace('\n', '</br>');
                    })
                    .catch(error => console.log('error', error));
            }
        }
    </script>
</body>

</html>