package main

import (
	"fmt"
	"os"
	"os/exec"
	"strings"

	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/tidwall/gjson"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
)

func startService() cli.ActionFunc {
	return func(c *cli.Context) error {
		port := c.String("port")

		e, err := newServiceHandler(c)
		if err != nil {
			return err
		}

		return e.Run(":" + port)
	}
}

func newServiceHandler(c *cli.Context) (*gin.Engine, error) {
	logger.SetJsonZeroLoggerWithLevel(c.String(flags.LogLevelFlag.Name))
	e := gin.New()
	e.Use(logger.LoggerMW(), logger.CustomLoggerMW(), gzip.Gzip(gzip.DefaultCompression))
	middlewares.LoadCommonMWAndConf(c, e)

	e.MaxMultipartMemory = 8 << 20 // 8 MiB
	r := e.Group("/v1/command")
	{
		r.Static("/tool", "./html")

		r.GET("/pod/products/:product/trigger", func(c *gin.Context) {
			pythonFile := "pods.py"

			cmd := exec.Command("python", pythonFile)
			cmd.Env = os.Environ()
			if c.Param("product") != "" {
				cmd.Env = append(cmd.Env, strings.ToUpper(c.Param("product"))+"=TRUE") // VISA=TRUE, FASTLANE=TRUE
			}
			if c.Query("prod") == "true" {
				cmd.Env = append(cmd.Env, "ENV=prod")
			}

			if c.Query("ad_db") != "" {
				cmd.Env = append(cmd.Env, "DB_HOST="+gjson.Get(c.Query("ad_db"), "write_host").String())
				cmd.Env = append(cmd.Env, "DB_PASSWORD="+gjson.Get(c.Query("ad_db"), "password").String())
			}

			out, err := cmd.CombinedOutput()

			fmt.Println(err)

			c.String(200, string(out))
		})

		r.GET("/product/trigger", func(c *gin.Context) {
			pythonFile := "ets_product.py"

			cmd := exec.Command("python", pythonFile)
			cmd.Env = os.Environ()
			if c.Param("product") != "" {
				cmd.Env = append(cmd.Env, strings.ToUpper(c.Param("product"))+"=TRUE") // VISA=TRUE, FASTLANE=TRUE
			}
			if c.Query("prod") == "true" {
				cmd.Env = append(cmd.Env, "ENV=prod")
			}
			out, err := cmd.CombinedOutput()

			fmt.Println(err)

			c.String(200, string(out))
		})

		r.GET("/localization/trigger", func(c *gin.Context) {
			pythonFile := "localization.py"

			cmd := exec.Command("python", pythonFile)
			cmd.Env = os.Environ()
			out, err := cmd.CombinedOutput()

			fmt.Println(err)

			c.String(200, string(out))
		})
	}

	return e, nil
}
