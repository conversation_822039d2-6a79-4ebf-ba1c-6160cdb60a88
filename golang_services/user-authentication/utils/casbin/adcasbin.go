package adcasbin

import (
	//"time"

	"github.com/casbin/casbin"
	"github.com/casbin/gorm-adapter"
	"github.com/jinzhu/gorm"
)

// casbin policy structure
type CasbinModel struct {
	ID         uint   `gorm:"primary_key;AUTO_INCREMENT" json:"id" db:"id"`
	PolicyType string `json:"policy_type" db:"policy_type"`
	RoleName   string `json:"role_name" db:"role_name"`
	Path       string `json:"path" db:"path"`
	Method     string `json:"method" db:"method"`
	//CreatedAt  time.Time  `json:"created_at" db:"created_at"`
	//UpdatedAt  *time.Time `json:"updated_at" db:"updated_at"`
}

func (casbin *CasbinModel) TableName() string {
	return "user_role"
}

// Add role policy
func (c *CasbinModel) AddCasbin(cm CasbinModel, db *gorm.DB) bool {
	e := Casbin(db)
	return e.AddPolicy(cm.RoleName, cm.Path, cm.Method)
}

func Casbin(db *gorm.DB) *casbin.Enforcer {
	m := casbin.NewModel()
	m.AddDef("r", "r", "sub, obj, act")
	m.AddDef("p", "p", "sub, obj, act")
	m.AddDef("e", "e", "some(where (p.eft == allow))")
	m.AddDef("m", "m", "r.sub == p.sub && keyMatch2(r.obj, p.obj) && regexMatch(r.act, p.act)")
	adapter := gormadapter.NewAdapterByDB(db)
	e := casbin.NewEnforcer(m, adapter)
	e.LoadPolicy()
	return e
}
