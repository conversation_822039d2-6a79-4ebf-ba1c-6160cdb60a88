package auth

import (
	"context"
	"encoding/json"
	"net/http"
	"os"
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/user-authentication/controllers"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/utils"
	"github.com/dgrijalva/jwt-go"
	"github.com/gorilla/mux"
	"github.com/jinzhu/gorm"
	"github.com/thoas/go-funk"
)

// Exception struct
type Exception models.Exception

var secret = os.Getenv("ad_secrets")
var secret_json = []byte(secret)
var secret_map = utils.ParseJsonString(secret_json)
var Jwt_secret = secret_map["jwt"].(string)

//var Jwt_secret = "secret"

// JwtVerify Middleware function
func JwtVerify(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

		var header = r.Header.Get("x-access-token") //Grab the token from the header

		header = strings.TrimSpace(header)

		// 1. verify token exists
		if header == "" {
			//Token is missing, returns with error code 403 Unauthorized
			w.WriteHeader(http.StatusForbidden)
			resp := models.Response{
				Error: &models.Error{
					ErrorCode:    "ERR00001",
					ErrorMessage: "Missing auth token",
				},
				Status: false,
			}
			json.NewEncoder(w).Encode(resp)
			return
		}
		tk := &models.Token{}

		// 2. verify token with correct secret
		_, err := jwt.ParseWithClaims(header, tk, func(token *jwt.Token) (any, error) {
			return []byte(Jwt_secret), nil
		})

		if err != nil {
			w.WriteHeader(http.StatusForbidden)
			resp := models.Response{
				Error: &models.Error{
					ErrorCode:    "ERR00001",
					ErrorMessage: err.Error(),
				},
				Status: false,
			}
			json.NewEncoder(w).Encode(resp)
			return
		}

		//3. verify token is not in blacklist
		IsInBlackList, err := IsJwtTokenInBlackList(header)
		if IsInBlackList {
			error_msg := "Token is invalid!"
			if err != nil {
				error_msg = err.Error()
			}
			w.WriteHeader(http.StatusForbidden)
			resp := models.Response{
				Error: &models.Error{
					ErrorCode:    "ERR00001",
					ErrorMessage: error_msg,
				},
				Status: false,
			}
			json.NewEncoder(w).Encode(resp)
			return
		}

		ctx := context.WithValue(r.Context(), "user", tk)
		ctx = context.WithValue(ctx, "id", tk.UserID)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// check if jwt token is in blacklist
func IsJwtTokenInBlackList(token string) (bool, error) {
	err := controllers.Db.Where("token = ?", token).First(&models.TokenBlackList{}).Error
	if err == gorm.ErrRecordNotFound { // token does not exist
		return false, nil
	} else if err != nil {
		return true, err
	} else {
		return true, nil
	}
}

func UserIDVerify(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		v := r.Context().Value("user")
		if v == nil {
			w.WriteHeader(http.StatusUnauthorized)
			return
		}
		userToken, ok := v.(*models.Token)
		if !ok {
			w.WriteHeader(http.StatusBadRequest)
			json.NewEncoder(w).Encode(map[string]any{"message": "missing user token in context"})
			return
		}
		vars := mux.Vars(r)
		id := vars["id"]
		if !funk.Contains([]string{"admin", "ad_admin"}, userToken.Role) && id != "" && userToken.UserID.String() != id {
			w.WriteHeader(http.StatusForbidden)
			resp := models.Response{
				Error: &models.Error{
					ErrorCode:    "ERR00001",
					ErrorMessage: "invalid token",
				},
				Status: false,
			}
			json.NewEncoder(w).Encode(resp)
			return
		}
		ctx := context.WithValue(r.Context(), "id", userToken.UserID)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
