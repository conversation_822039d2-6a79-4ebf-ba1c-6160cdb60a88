package utils

import (
	"fmt"
	"os"

	"github.com/go-redis/redis"
)

func NewRedisClient() *redis.Client {
	//host := "localhost:6379"
	user_service_var := os.Getenv("ad_user_account_service")
	db_json := []byte(user_service_var)
	db_map := ParseJsonString(db_json)

	host := db_map["redis_host"].(string)

	client := redis.NewClient(&redis.Options{
		Addr:     host,
		Password: "", // no password set
		DB:       0,  // use default DB
	})

	pong, err := client.Ping().Result()
	fmt.Println("Redis addr is", client.Options().Addr)
	fmt.Println(pong, err)
	// Output: PONG <nil>

	return client
}
