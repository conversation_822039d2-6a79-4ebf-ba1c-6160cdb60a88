package utils

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/postgres" //Gorm postgres dialect interface

	admodels "bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
)

// ConnectDB function: Make database connection
func ConnectDB() *gorm.DB {
	db_var := os.Getenv("ad_db")
	db_json := []byte(db_var)
	db_map := ParseJsonString(db_json)

	username := db_map["username"]
	password := db_map["password"]
	databaseName := db_map["dbname"]
	databasePort := db_map["port"]
	databaseHost := db_map["write_host"].(string)
	//username := "postgres"
	//password := "19941021"
	//databaseName := "expense"
	//databaseHost := "localhost"

	//Define DB connection string
	dbURI := fmt.Sprintf("host=%s port=%0.0f user=%s dbname=%s password=%s sslmode=disable", databaseHost, databasePort, username, databaseName, password)
	//dbURI := fmt.Sprintf("host=%s user=%s sslmode=disable dbname=%s password=%s", databaseHost, username, databaseName, password)

	//connect to db URI
	db, err := gorm.Open("postgres", dbURI)

	if err != nil {
		fmt.Println("error", err)
		panic(err)
	}
	// close db when not in use
	// defer db.Close()

	// Migrate the schema
	if false {
		db.AutoMigrate(
			&models.User{},
			&models.Organization{},
			&models.Corporation{},
			&models.EtsProvider{},
			&models.Consulate{},
			&models.TokenBlackList{},
			&models.UserInvite{},
			&models.UserNotification{},
			&models.CorporationTeam{},
			&models.CorporationTeamMember{},
			&models.CorporationCostCenter{},
			&models.CorporationDepartment{},
			&models.EmailSubscribe{},
			&admodels.UserLoginHistory{},
		)
	}

	fmt.Println("Successfully connected!", db)
	return db
}

func ParseJsonString(str []byte) map[string]any {
	m := make(map[string]any)
	err := json.Unmarshal(str, &m)
	if err != nil {
		log.Fatal(err)
	}

	return m
}

func SplitString(str string) string {
	split_slash := strings.Split(str, "/")
	split_at := strings.Split(split_slash[2], "@")
	return split_at[1]
}

func NewAuroraDb() *db.AuroraDB {
	dbMap := ParseJsonString([]byte(os.Getenv("ad_db")))
	adb, err := db.NewAuroraDBFromConfigMap(dbMap)
	if err != nil {
		panic(err)
	}
	return adb
}
