package utils

import (
	"fmt"
	"math/rand"
	"unicode"

	"github.com/pariz/gountries"
)

// Requirements: 1. len >= 6
// 2. letter and number
func CheckPasswordRestrict(password string) bool {
	if len(password) < 6 {
		return false
	}
	// only contains letter or only contains digit
	if Is<PERSON><PERSON><PERSON>(password) || IsDigit(password) {
		return false
	}
	return true
}

func IsLetter(s string) bool {
	for _, r := range s {
		if !unicode.IsLetter(r) {
			return false
		}
	}
	return true
}

func IsDigit(s string) bool {
	for _, r := range s {
		if !unicode.IsDigit(r) {
			return false
		}
	}
	return true
}

// check is country code is valid, and return valid iso alpha3
func CheckCountryCode(code string) (error, string) {
	query := gountries.New()
	country, err := query.FindCountryByAlpha(code)
	if err != nil {
		fmt.Println(err)
	}

	return err, country.Alpha3
}

// generate random string for default password
func RandomPassword() string {
	var letters = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")
	b := make([]rune, 8)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

// generate random string for default password
func GenerateOTP(length int) string {
	var letters = []rune("0123456789")
	b := make([]rune, length)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}
