package main

import (
	"bytes"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"

	"github.com/gorilla/handlers"
	"github.com/rs/cors"
	"github.com/rs/zerolog"
	"github.com/thoas/go-funk"

	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/routes"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/utils"
)

func encryptPassword(password string) string {
	h := sha256.New()
	h.Write([]byte(password))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func main() {
	fmt.Println(encryptPassword("victravel20"))
	db_var := os.Getenv("ad_db")
	db_json := []byte(db_var)
	db_map := utils.ParseJsonString(db_json)
	port := "3000"
	//port := "8080"
	if p := os.Getenv("PORT"); p != "" {
		port = p
	}
	fmt.Println("DB: ", db_map["dbname"])

	zerolog.SetGlobalLevel(zerolog.DebugLevel)
	c := cors.New(cors.Options{
		AllowedOrigins: []string{"*"}, // All origins
		AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"Origin", "Accept", "Content-Type", "X-Requested-With",
			"access-control-allow-origin", "access-control-allow-method", "x-access-token"},
	})
	logger.InitGraylogLogger()

	// Handle routes
	r := routes.Handlers()
	http.Handle("/", r)

	h := c.Handler(r)
	h = handlers.CustomLoggingHandler(os.Stdout, h, func(writer io.Writer, params handlers.LogFormatterParams) {
		// Skip some urls
		if funk.ContainsString([]string{
			"/v1/ums/status",
		}, params.Request.URL.RequestURI()) {
			return
		}

		// Create the base log message
		logMessage := fmt.Sprintf("%s - %s [%s] \"%s %s %s\" %d %d",
			params.Request.RemoteAddr,
			"-",
			params.TimeStamp.Format("02/Jan/2006:15:04:05 -0700"),
			params.Request.Method,
			params.Request.URL.RequestURI(),
			params.Request.Proto,
			params.StatusCode,
			params.Size,
		)

		// For POST requests, also log the body
		if params.Request.Method == "POST" {
			// Read the body
			bodyBytes, err := io.ReadAll(params.Request.Body)
			if err != nil {
				log.Printf("Error reading body: %v", err)
				return
			}

			// Restore the body for subsequent reads
			params.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

			// Add body to log message
			logMessage += fmt.Sprintf("\nBody: %s", string(bodyBytes))
		}
		logger.LogToGraylog(6, "USER_MANAGEMENT", logMessage, nil)
	})
	// serve
	log.Printf("Server up on port '%s'", port)
	log.Fatal(http.ListenAndServe(":"+port, h))
}
