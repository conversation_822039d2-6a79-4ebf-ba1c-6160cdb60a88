package models

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/tidwall/gjson"
)

type LinkedinTokenRequest struct {
	Code        string `json:"auth_code"`
	RedirectURI string `json:"redirect_uri"` // for web only
}

type LinkedinTokenResponse struct {
	AccessToken      string `json:"access_token"`
	ExpiresIn        int    `json:"expires_in"`
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
}

type LinkedinClient struct {
	clientID     string
	clientSecret string
	redirectURI  string
	client       *http.Client
}

const (
	oauthURL        = "https://www.linkedin.com/oauth/v2/accessToken"
	basicProfileURL = "https://api.linkedin.com/v2/me"
	emailProfileURL = "https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))"
)

func NewLinkedinClient(redirectURI, clientID, clientSecret string) *LinkedinClient {
	return &LinkedinClient{
		clientID:     clientID,
		clientSecret: clientSecret,
		redirectURI:  redirectURI,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (c *LinkedinClient) VerifyAuthCode(input LinkedinTokenRequest, result any) error {
	data := url.Values{}
	data.Set("client_id", c.clientID)
	data.Set("client_secret", c.clientSecret)
	data.Set("redirect_uri", c.redirectURI)
	data.Set("code", input.Code)
	data.Set("grant_type", "authorization_code")

	req, err := http.NewRequest("POST", oauthURL, strings.NewReader(data.Encode()))
	if err != nil {
		return err
	}

	return c.doRequest(req, &result)
}

func (c *LinkedinClient) GetUserClaims(token string) (map[string]any, error) {
	// Get user profile
	req, err := http.NewRequest("GET", basicProfileURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Add("authorization", "Bearer "+token)

	var profile map[string]any
	if err := c.doRequest(req, &profile); err != nil {
		return nil, err
	}

	result := map[string]any{}

	if val, ok := profile["localizedFirstName"]; ok {
		result["given_name"] = val
	}

	if val, ok := profile["localizedLastName"]; ok {
		result["surname"] = val
	}

	// Get profile email
	if req, err = http.NewRequest("GET", emailProfileURL, nil); err != nil {
		return nil, err
	}
	req.Header.Add("authorization", "Bearer "+token)

	var v map[string]any
	if err := c.doRequest(req, &v); err != nil {
		return nil, err
	}

	buff, err := json.Marshal(v)
	if err != nil {
		return nil, err
	}

	email := gjson.ParseBytes(buff).Get("elements.0.handle~.emailAddress").String()

	if email == "" {
		return nil, fmt.Errorf("Can not get email from linkedin")
	}
	result["email"] = email

	return result, nil
}

func (c *LinkedinClient) doRequest(req *http.Request, result any) error {
	req.Header.Add("content-type", "application/x-www-form-urlencoded")
	req.Header.Add("accept", "application/json")
	req.Header.Add("user-agent", "ad-sign-in-with-linkedin")

	res, err := c.client.Do(req)
	if err != nil {
		return err
	}

	defer res.Body.Close()

	return json.NewDecoder(res.Body).Decode(result)
}
