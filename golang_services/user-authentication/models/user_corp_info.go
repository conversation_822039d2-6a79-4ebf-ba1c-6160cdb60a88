package models

import (
	"encoding/json"
	"time"

	"github.com/lib/pq"
)

type UserCorpInfo struct {
	UserID             string           `json:"user_id" db:"user_id"`
	CorporationName    string           `json:"corporation_name" db:"corporation_name"`
	CorporationAddress *json.RawMessage `json:"corporation_address" db:"corporation_address"`
	Phone              string           `json:"phone" db:"phone"`
	ManagerID          *string          `json:"manager_id" db:"manager_id"`
	ManagerName        string           `json:"manager_name"`
	ManagerEmail       string           `json:"manager_email"`
	ManagerPhoneNumber string           `json:"manager_phone_number"`
	Manager            *json.RawMessage `json:"manager"` // Remove later
	HRContact          *json.RawMessage `json:"hr_contact" db:"hr_contact"`
	DepartmentID       *int             `json:"department_id" db:"department_id"`
	Department         string           `json:"department" db:"department"`
	CostCenterID       *int             `json:"cost_center_id" db:"cost_center_id"`
	CostCenterNumber   string           `json:"cost_center_number"`
	CorporationCode    string           `json:"corporation_code" db:"corporation_code"`
	ApprovedCountries  pq.StringArray   `json:"approved_countries" db:"approved_countries"`
	CreatedAt          time.Time        `json:"created_at" db:"created_at"`
	UpdatedAt          *time.Time       `json:"updated_at" db:"updated_at"`
}

func (u *UserCorpInfo) TableName() string {
	return "user_corp_info"
}
