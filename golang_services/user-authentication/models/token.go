package models

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	jwt "github.com/dgrijalva/jwt-go"
	uuid "github.com/satori/go.uuid"
)

// TokenBlackList token black list table
type TokenBlackList struct {
	ID        int       `json:"id" db:"id"`
	UserID    string    `json:"user_id" db:"user_id"`
	Token     string    `json:"token" db:"token"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// Token struct declaration
type Token struct {
	UserID   uuid.UUID `gorm:"type:uuid;" json:"user_id"`
	Username string    `json:"username"`
	Role     string    `json:"role"`
	*jwt.StandardClaims
}

// Google ID token
type GoogleToken struct {
	IDToken   string `json:"id_token"`
	GivenName string
	Surname   string
}

func (g *GoogleToken) GetUserClaims() (map[string]any, error) {
	parts := strings.Split(g.IDToken, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("invalid token format")
	}

	// Decode base64 to claims
	claimsDecoded, err := base64.RawURLEncoding.DecodeString(parts[1])
	if err != nil {
		return nil, err
	}

	var claims map[string]any
	if err = json.Unmarshal(claimsDecoded, &claims); err != nil {
		return nil, err
	}

	if val, ok := claims["given_name"]; ok {
		g.GivenName = val.(string)
	}

	if val, ok := claims["family_name"]; ok {
		g.Surname = val.(string)
	}

	return claims, nil
}
