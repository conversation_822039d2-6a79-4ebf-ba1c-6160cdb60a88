package models

import (
	"time"
)

type UserNotification struct {
	ID              int        `json:"id" db:"id"`
	Type            string     `json:"type" db:"type"`
	Title           string     `json:"title" db:"title"`
	LanguageCode    string     `json:"language_code" db:"language_code"`
	Content         string     `json:"content" db:"content"`
	ContentTruncate string     `json:"content_truncate" db:"content_truncate"`
	UserID          string     `json:"user_id" db:"user_id"`
	SentAt          *time.Time `json:"sent_at" db:"sent_at"`
	ReadAt          *time.Time `json:"read_at" db:"read_at"`
	CreatedAt       time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt       *time.Time `json:"updated_at" db:"updated_at"`
	DeletedAt       *time.Time `json:"deleted_at" db:"deleted_at"`
}

type UserNotificationFilter struct {
	ID             int  `schema:"id"`
	IncludeContent bool `schema:"include_content"`
	Offset         int  `schema:"offset"`
	Limit          int  `schema:"limit"`
}

type UserNotificationData struct {
	Data        []UserNotification `json:"data"`
	Success     bool               `json:"success"`
	Total       int                `json:"total"`
	TotalRead   int                `json:"total_read"`
	TotalUnread int                `json:"total_unread"`
	Offset      int                `json:"offset"`
	Limit       int                `json:"limit"`
}
