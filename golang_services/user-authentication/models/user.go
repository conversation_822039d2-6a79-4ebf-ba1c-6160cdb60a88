package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/lib/pq"
	uuid "github.com/satori/go.uuid"
	"gorm.io/datatypes"

	"github.com/pkg/errors"
)

// User struct declaration
type User struct {
	//gorm.Model
	ID                uuid.UUID      `gorm:"type:uuid;primary_key;" json:"id" db:"id"`
	Username          string         `json:"username" db:"username"`
	Surname           string         `json:"surname" db:"surname"`
	GivenName         string         `json:"given_name" db:"given_name"`
	Avatar            string         `json:"avatar" db:"avatar"`
	Role              string         `gorm:"default:'user'" json:"role" db:"role"`
	OrganizationID    int64          `gorm:"ForeignKey:id" json:"organization_id" db:"organization_id"`
	Email             string         `gorm:"type:varchar(100);unique_index" json:"email" db:"email"`
	Password          string         `json:"password" db:"password"`
	PasswordInit      string         `json:"-" db:"password_init"`
	Status            string         `json:"status" db:"status"`
	EmailVerified     bool           `json:"email_verified" db:"email_verified"`
	AppLanguage       string         `json:"app_language" db:"app_language"`
	FailedLoginCount  int            `json:"failed_login_count" db:"failed_login_count"`
	FailedLoginTime   *time.Time     `json:"failed_login_time" db:"failed_login_time"`
	LastLogonAt       *time.Time     `json:"last_logon_at" db:"last_logon_at"`
	AutoAssignedOrder bool           `json:"auto_assigned_order" db:"auto_assigned_order"`
	AppleLastToken    string         `json:"apple_last_token" db:"apple_last_token"`
	TableViewConfig   datatypes.JSON `json:"table_view_config" db:"table_view_config"`
	StaffContact      datatypes.JSON `json:"staff_contact" db:"staff_contact"`
	CreatedAt         time.Time      `gorm:"type:timestamp with time zone not null" json:"created_at" db:"created_at"`
	UpdatedAt         *time.Time     `json:"update_at" db:"updated_at"`
}

var UserStatus = struct{ Pending, Active, InActive string }{"pending", "active", "inactive"}

type Profile struct {
	Region_of_residence string `json:"region_of_residence"`
}

func (user *User) BeforeCreate(scope *gorm.Scope) error {
	uuid := uuid.NewV4()
	return scope.SetColumn("ID", uuid)
}

func (p Profile) Value() (driver.Value, error) {
	return json.Marshal(p)
}

func (profile *Profile) Scan(src any) error {
	if src == nil {
		profile = nil
		return nil
	}
	switch src := src.(type) {
	case []byte:
		return json.Unmarshal(src, &profile)
	case string:
		return json.Unmarshal([]byte(src), &profile)
	default:
		return errors.Errorf("Can't scan value type from %T", src)
	}
}

type UserInfo struct {
	//gorm.Model
	ID                         uuid.UUID       `gorm:"type:uuid;primary_key;" json:"id"`
	Username                   string          `json:"username"`
	Surname                    string          `json:"surname"`
	GivenName                  string          `json:"given_name"`
	Avatar                     string          `json:"avatar"`
	Role                       string          `json:"role"`
	UserTypes                  []string        `json:"user_types"`
	OrganizationID             int64           `gorm:"ForeignKey:id" json:"organization_id"`
	Email                      string          `gorm:"type:varchar(100);unique_index" json:"email"`
	Status                     string          `json:"status"`
	EmailVerified              bool            `json:"email_verified"`
	TimezoneName               string          `json:"timezone_name"`
	AppLanguage                string          `json:"app_language"`
	OTPExpired                 bool            `json:"otp_expired"`
	CreatedAt                  time.Time       `gorm:"type:timestamp with time zone not null" json:"created_at"`
	UpdatedAt                  *time.Time      `json:"update_at"`
	PhoneNumber                string          `json:"phone_number"`
	RegionOfResidence          string          `json:"region_of_residence"`
	Department                 string          `json:"department"`
	CostCenterNumber           string          `json:"cost_center_number"`
	ManagerName                string          `json:"manager_name"`
	JobTitle                   string          `json:"job_title"`
	ApprovedCountries          pq.StringArray  `json:"approved_countries"`
	SubscribeAnnouncement      bool            `json:"subscribe_announcement"`
	SubscribeOrderNotification bool            `json:"subscribe_order_notification"`
	StaffOrders                datatypes.JSON  `json:"staff_orders"`
	StaffInSchedule            bool            `json:"staff_in_schedule"`
	StaffContact               datatypes.JSON  `json:"staff_contact"`
	TableViewConfig            json.RawMessage `json:"table_view_config" db:"table_view_config"`
	AutoAssignedOrder          bool            `json:"auto_assigned_order" db:"auto_assigned_order"`
}

type UserInfoWithOrderSummary struct {
	ID             uuid.UUID        `gorm:"type:uuid;primary_key;" json:"id"`
	Username       string           `json:"username"`
	Surname        string           `json:"surname"`
	GivenName      string           `json:"given_name"`
	Fullname       string           `json:"name"`
	Avatar         string           `json:"avatar"`
	Role           string           `json:"role"`
	UserTypes      pq.StringArray   `json:"user_types"`
	OrganizationID int64            `gorm:"ForeignKey:id" json:"organization_id"`
	Email          string           `gorm:"type:varchar(100);unique_index" json:"email"`
	Status         string           `json:"status"`
	EmailVerified  bool             `json:"email_verified"`
	SubmittedOrder int              `json:"submitted_order"`
	OpenOrder      int              `json:"open_order"`
	LastOrderTime  *time.Time       `json:"last_order_time"`
	OrgName        string           `json:"org_name"`
	CorpCode       string           `json:"corp_code"`
	CorpCity       string           `json:"corp_city"`
	CorpCountry    string           `json:"corp_country"`
	CreatedAt      time.Time        `gorm:"type:timestamp with time zone not null" json:"created_at"`
	UpdatedAt      *time.Time       `json:"update_at"`
	Corporation    *json.RawMessage `json:"corporation"`
	Consulate      *json.RawMessage `json:"consulate"`
	ETSProvider    *json.RawMessage `json:"ets_provider"`
}

type UserInfoPagination struct {
	Limit      uint64                     `json:"limit"`
	Offset     uint64                     `json:"offset"`
	Success    bool                       `json:"success"`
	TotalCount int                        `json:"total_count"`
	Data       []UserInfoWithOrderSummary `json:"data"`
}
