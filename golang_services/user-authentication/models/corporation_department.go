package models

import (
	"time"
)

type CorporationDepartment struct {
	ID        int        `json:"id" db:"id"`
	OrgID     int64      `json:"org_id" db:"org_id"`
	Name      string     `json:"name" db:"name"`
	CreatedAt time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt *time.Time `json:"updated_at" db:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at" db:"deleted_at"`
}

type CorporationDepartmentFilter struct {
	Query     string `schema:"query"`
	SortField string `schema:"sort_field"`
	SortOrder string `schema:"sort_order"`
	Offset    int    `schema:"offset"`
	Limit     int    `schema:"limit"`
}

type CorporationDepartmentData struct {
	Data    []CorporationDepartment `json:"data"`
	Success bool                    `json:"success"`
	Total   int                     `json:"total"`
}
