package models

import (
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	jwt "github.com/dgrijalva/jwt-go"
)

type AppleTokenRequest struct {
	Code        string `json:"auth_code"` // valid for 5 minutes only
	GivenName   string `json:"given_name"`
	Surname     string `json:"surname"`
	RedirectURI string `json:"redirect_uri"` // for web only
}

type AppleTokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	IDToken      string `json:"id_token"`
	Error        string `json:"error"`
}

func (a *AppleTokenResponse) GetUserClaims() (map[string]any, error) {
	parts := strings.Split(a.IDToken, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("invalid token format")
	}

	// Decode base64 to claims
	claimsDecoded, err := base64.RawURLEncoding.DecodeString(parts[1])
	if err != nil {
		return nil, err
	}

	var claims map[string]any
	if err = json.Unmarshal(claimsDecoded, &claims); err != nil {
		return nil, err
	}
	return claims, nil
}

type AppleClient struct {
	oauthURL         string
	clientID         string
	clientTeamID     string
	clientKeyID      string
	clientPrivateKey string
	client           *http.Client
}

func NewAppleClient(oauthURL, clientID, clientTeamID, clientKeyID, clientPrivateKey string) *AppleClient {
	return &AppleClient{
		oauthURL:         "https://appleid.apple.com/auth/token",
		clientID:         clientID,
		clientTeamID:     clientTeamID,
		clientKeyID:      clientKeyID,
		clientPrivateKey: clientPrivateKey,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (c *AppleClient) VerifyAuthCode(req AppleTokenRequest, result any) error {
	clientSecret, err := c.GenerateClientSecret()
	if err != nil {
		return err
	}
	data := url.Values{}
	data.Set("client_id", c.clientID)
	data.Set("client_secret", clientSecret)
	data.Set("code", req.Code)
	if req.RedirectURI != "" {
		data.Set("redirect_uri", req.RedirectURI)
	}
	data.Set("grant_type", "authorization_code")

	return doRequest(c.client, &result, c.oauthURL, data)
}

func (c *AppleClient) GenerateClientSecret() (string, error) {
	block, _ := pem.Decode([]byte(c.clientPrivateKey))
	if block == nil {
		return "", errors.New("empty block after decoding")
	}

	privKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return "", err
	}

	// Create the Claims
	now := time.Now()
	claims := &jwt.StandardClaims{
		Issuer:    c.clientTeamID,
		IssuedAt:  now.Unix(),
		ExpiresAt: now.Add(time.Hour*24*180 - time.Second).Unix(), // 180 days
		Audience:  "https://appleid.apple.com",
		Subject:   c.clientID,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodES256, claims)
	token.Header["alg"] = "ES256"
	token.Header["kid"] = c.clientKeyID

	return token.SignedString(privKey)
}

func doRequest(client *http.Client, result any, url string, data url.Values) error {
	req, err := http.NewRequest("POST", url, strings.NewReader(data.Encode()))
	if err != nil {
		return err
	}

	req.Header.Add("content-type", "application/x-www-form-urlencoded")
	req.Header.Add("accept", "application/json")
	req.Header.Add("user-agent", "ad-sign-in-with-apple")

	res, err := client.Do(req)
	if err != nil {
		return err
	}

	defer res.Body.Close()

	return json.NewDecoder(res.Body).Decode(result)
}
