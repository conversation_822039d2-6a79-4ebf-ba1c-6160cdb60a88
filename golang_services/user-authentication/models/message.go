package models

import (
	"database/sql/driver"
	"encoding/json"

	"github.com/pkg/errors"
)

type RegistrationMessage struct {
	Data          any   `json:"data"`
	Error         error `json:"error"`
	Rows_affected int64 `json:"rows_affected"`
	GoogleSSO     bool  `json:"google_sso"`
	AppleSSO      bool  `json:"apple_sso"`
}

type OrgRegistrationMessage struct {
	Data          any   `json:"data"`
	OrgID         uint  `json:"org_id"`
	Error         error `json:"error"`
	Rows_affected int64 `json:"rows_affected"`
}

type Response struct {
	Error  *Error `json:"error"`
	Status bool   `json:"status"`
}

func (err *Response) ErrorMsg() string {
	return err.Error.ErrorMessage
}

type Error struct {
	ErrorCode    string `json:"error_code"`
	ErrorMessage string `json:"error_message"`
	ErrorData    any    `json:"error_data"`
}

func (e Error) Value() (driver.Value, error) {
	return json.Marshal(e)
}

func (e *Error) Scan(src any) error {
	if src == nil {
		e = nil
		return nil
	}
	switch src := src.(type) {
	case []byte:
		return json.Unmarshal(src, &e)
	case string:
		return json.Unmarshal([]byte(src), &e)
	default:
		return errors.Errorf("Can't scan value type from %T", src)
	}
}
