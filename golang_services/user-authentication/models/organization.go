package models

import (
	"encoding/base64"
	"encoding/json"
	"strings"
	"time"

	"github.com/lib/pq"
	"gopkg.in/guregu/null.v3"
	"gorm.io/datatypes"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/jinzhu/gorm"
	uuid "github.com/satori/go.uuid"
)

// Organization struct declaration
type Organization struct {
	ID        uint       `gorm:"primary_key;AUTO_INCREMENT" json:"id" db:"id"`
	CreatedAt time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt *time.Time `json:"updated_at" db:"updated_at"`
}

// OrganizationService
type OrganizationService struct {
	Corporation *json.RawMessage `json:"corporation" db:"corporation"`
	Consulate   *json.RawMessage `json:"consulate" db:"consulate"`
	ETSProvider *json.RawMessage `json:"ets_provider" db:"ets_provider"`
}

func (org *Organization) TableName() string {
	return "organization"
}

// OrganizationDetail detail
type OrganizationDetail struct {
	ID           int64  `json:"id" db:"id"`
	Type         string `json:"type" db:"type"`
	Name         string `json:"name" db:"name"`
	Domain       string `json:"domain" db:"domain"`
	Code         string `json:"code" db:"code"`
	Country      string `json:"country" db:"country"`
	TimezoneName string `json:"timezone_name" db:"timezone_name"`
}

// OrganizationPagination response for organization pagination
type OrganizationPagination struct {
	Limit      uint64             `json:"limit"`
	Offset     uint64             `json:"offset"`
	Success    bool               `json:"success"`
	TotalCount int                `json:"total_count"`
	Data       []ViewOrganization `json:"data"`
}

// Corporation struct
type Corporation struct {
	ID               string          `gorm:"primary_key;" json:"id" db:"id"`
	Status           string          `gorm:"default:'active'" json:"status" db:"status"`
	Name             string          `json:"name" db:"name"`
	Domain           string          `json:"domain" db:"domain"`
	DomainEmailOnly  bool            `json:"domain_email_only" db:"domain_email_only"`
	Code             string          `json:"code" db:"code"`
	Country          string          `json:"country" db:"country"`
	Address          *models.Address `json:"address" db:"address"`
	Contact          *models.Contact `json:"contact" db:"contact"`
	SecondaryContact *models.Contact `json:"secondary_contact" db:"secondary_contact"`
	Offices          datatypes.JSON  `json:"offices" db:"offices"`
	ADContacts       datatypes.JSON  `json:"ad_contacts" db:"ad_contacts"`
	TimezoneName     string          `json:"timezone_name" db:"timezone_name"`
	OrgID            uint            `json:"org_id" db:"org_id"`
	CreatedAt        time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt        *time.Time      `json:"updated_at" db:"updated_at"`
}

func (corp *Corporation) TableName() string {
	return "corporation"
}

func (corp *Corporation) BeforeCreate(scope *gorm.Scope) error {
	uuid := uuid.NewV4()
	escaper := strings.NewReplacer("9", "99", "-", "90", "_", "91")
	suffix_id := escaper.Replace(base64.RawURLEncoding.EncodeToString(uuid.Bytes()))
	id := "corp-" + suffix_id
	return scope.SetColumn("ID", id)
}

type CorporationDetail struct {
	Status           string          `gorm:"default:'active'" json:"status"`
	Name             string          `json:"name"`
	Domain           string          `json:"domain"`
	DomainEmailOnly  bool            `json:"domain_email_only"`
	Code             string          `json:"code"`
	Country          string          `json:"country"`
	Address          *models.Address `json:"address"`
	Contact          *models.Contact `json:"contact"`
	SecondaryContact *models.Contact `json:"secondary_contact"`
	TimezoneName     string          `json:"timezone_name"`
}

// Consulate struct
type Consulate struct {
	ID                   string          `gorm:"primary_key;" json:"id" db:"id"`
	Status               string          `gorm:"default:'active'" json:"status" db:"status"`
	Name                 string          `json:"name" db:"name"`
	Email                string          `json:"email" db:"email"`
	SecondaryEmail       string          `json:"secondary_email" db:"secondary_email"`
	Country              string          `json:"country" db:"country"`
	Address              *models.Address `json:"address" db:"address"`
	Contact              *models.Contact `json:"contact" db:"contact"`
	SecondaryContact     *models.Contact `json:"secondary_contact" db:"secondary_contact"`
	TimezoneName         string          `json:"timezone_name" db:"timezone_name"`
	SupportPickup        bool            `json:"support_pickup" db:"support_pickup"`
	SupportShipping      bool            `json:"support_shipping" db:"support_shipping"`
	SupportedVisaProduct pq.Int64Array   `gorm:"type:int[]" json:"supported_visa_product" db:"supported_visa_product"`
	ADContacts           json.RawMessage `json:"ad_contacts" db:"ad_contacts"`
	OrgID                uint            `json:"org_id" db:"org_id"`
	CreatedAt            time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt            *time.Time      `json:"updated_at" db:"updated_at"`
}

func (cons *Consulate) TableName() string {
	return "consulate"
}

func (cons *Consulate) BeforeCreate(scope *gorm.Scope) error {
	uuid := uuid.NewV4()
	escaper := strings.NewReplacer("9", "99", "-", "90", "_", "91")
	suffix_id := escaper.Replace(base64.RawURLEncoding.EncodeToString(uuid.Bytes()))
	id := "cons-" + suffix_id
	return scope.SetColumn("ID", id)
}

type ConsulateDetail struct {
	Status               string          `gorm:"default:'active'" json:"status"`
	Name                 string          `json:"name"`
	Email                string          `json:"email"`
	SecondaryEmail       string          `json:"secondary_email"`
	Country              string          `json:"country"`
	Address              *models.Address `json:"address"`
	Contact              *models.Contact `json:"contact"`
	SecondaryContact     *models.Contact `json:"secondary_contact"`
	TimezoneName         string          `json:"timezone_name"`
	SupportPickup        bool            `json:"support_pickup"`
	SupportShipping      bool            `json:"support_shipping"`
	SupportedVisaProduct pq.Int64Array   `json:"supported_visa_product"`
}

type ConsulateInfoWithOrderInfo struct {
	ID                   string          `gorm:"primary_key;" json:"id"`
	Status               string          `gorm:"default:'active'" json:"status"`
	Name                 string          `json:"name"`
	Email                string          `json:"email"`
	SecondaryEmail       string          `json:"secondary_email"`
	Country              string          `json:"country"`
	Address              *models.Address `json:"address"`
	Contact              *models.Contact `json:"contact"`
	SecondaryContact     *models.Contact `json:"secondary_contact"`
	TimezoneName         string          `json:"timezone_name"`
	SupportPickup        bool            `json:"support_pickup"`
	SupportShipping      bool            `json:"support_shipping"`
	SupportedVisaProduct []int           `gorm:"type:int[]" json:"supported_visa_product"`
	ProcessingOrder      int             `json:"processing_order"`
	ProcessedOrder       int             `json:"processed_order"`
	CreatedAt            time.Time       `json:"created_at"`
	UpdatedAt            *time.Time      `json:"updated_at"`
}

type ConsulateInfoPagination struct {
	Limit      int                          `json:"limit"`
	Offset     int                          `json:"offset"`
	Success    bool                         `json:"success"`
	TotalCount int                          `json:"total_count"`
	Data       []ConsulateInfoWithOrderInfo `json:"data"`
}

// EtsProvider struct
type EtsProvider struct {
	ID               string          `gorm:"primary_key;" json:"id" db:"id"`
	Status           string          `gorm:"default:'active'" json:"status" db:"status"`
	Name             string          `json:"name" db:"name"`
	Country          string          `json:"country" db:"country"`
	TimezoneName     string          `json:"timezone_name" db:"timezone_name"`
	Address          *models.Address `json:"address" db:"address"`
	Contact          *models.Contact `json:"contact" db:"contact"`
	SecondaryContact *models.Contact `json:"secondary_contact" db:"secondary_contact"`
	Website          null.String     `json:"website" db:"website"`
	ServedCountries  string          `json:"served_countries" db:"served_countries"`
	ServedArea       string          `json:"served_area" db:"served_area"`
	ServedServices   pq.StringArray  `json:"served_services" db:"served_services"`
	ADContacts       json.RawMessage `json:"ad_contacts" db:"ad_contacts"`
	OrgID            uint            `json:"org_id" db:"org_id"`
	CreatedAt        time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt        *time.Time      `json:"updated_at" db:"updated_at"`
}

func (etsp *EtsProvider) TableName() string {
	return "ets_provider"
}

func (etsp *EtsProvider) BeforeCreate(scope *gorm.Scope) error {
	uuid := uuid.NewV4()
	escaper := strings.NewReplacer("9", "99", "-", "90", "_", "91")
	suffix_id := escaper.Replace(base64.RawURLEncoding.EncodeToString(uuid.Bytes()))
	id := "etsp-" + suffix_id
	return scope.SetColumn("ID", id)
}

type EtsProviderDetail struct {
	Status           string          `gorm:"default:'active'" json:"status"`
	Name             string          `json:"name"`
	Email            string          `json:"email"`
	SecondaryEmail   string          `json:"secondary_email"`
	Country          string          `json:"country"`
	Address          *models.Address `json:"address"`
	Contact          *models.Contact `json:"contact"`
	SecondaryContact *models.Contact `json:"secondary_contact"`
	Website          null.String     `json:"website"`
	ServedCountries  string          `json:"served_countries"`
	ServedArea       string          `json:"served_area"`
	ServedServices   pq.StringArray  `json:"served_services"`
	TimezoneName     string          `json:"timezone_name"`
}

type ViewOrganization struct {
	ID           uint            `json:"id" db:"id"`
	Profile      string          `json:"profile" db:"profile"`
	Type         string          `json:"type" db:"type"`
	Name         string          `json:"name" db:"name"`
	Domain       string          `json:"domain" db:"domain"`
	Code         string          `json:"code" db:"code"`
	Country      string          `json:"country" db:"country"`
	TimezoneName string          `json:"timezone_name" db:"timezone_name"`
	ADContacts   json.RawMessage `json:"ad_contacts" db:"ad_contacts"`
	CreatedAt    time.Time       `json:"created_at" db:"created_at"`
}

type CorporationOffice struct {
	ID           string     `json:"id"`
	Name         string     `json:"name"`
	Address      string     `json:"address"`
	ContactName  string     `json:"contact_name"`
	ContactEmail string     `json:"contact_email"`
	ContactPhone string     `json:"contact_phone"`
	CreatedAt    time.Time  `json:"created_at"`
	DeletedAt    *time.Time `json:"deleted_at"`
}

type ADContact struct {
	ID           string    `json:"id"`
	ContactName  string    `json:"contact_name"`
	ContactEmail string    `json:"contact_email"`
	CreatedAt    time.Time `json:"created_at"`
}
