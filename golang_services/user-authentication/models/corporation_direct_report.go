package models

type ClassifyMemberForDirectReport struct {
	UsersCanAddToManager        []MemberUserInfo `json:"users_can_add_to_manager"`
	UsersAlreadyAddToManager    []MemberUserInfo `json:"users_already_add_to_manager"`
	UsersBelongToAnotherCorp    []MemberUserInfo `json:"users_belong_to_another_corp"`
	UsersBelongToAnotherManager []MemberUserInfo `json:"users_belong_to_another_manager"`
	UsersNotInSystem            []MemberUserInfo `json:"users_not_in_system"`
}
