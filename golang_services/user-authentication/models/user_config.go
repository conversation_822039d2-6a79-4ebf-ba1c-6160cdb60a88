package models

import (
	"encoding/json"
	"time"
)

type UserConfig struct {
	ID          int             `json:"id" db:"id"`
	UserID      string          `json:"user_id" db:"user_id"`
	ConfigKey   string          `json:"config_key" db:"config_key"`
	ConfigValue json.RawMessage `json:"config_value" db:"config_value"`
	CreatedAt   time.Time       `json:"created_at" db:"created_at"`
}

func (uc *UserConfig) TableName() string {
	return "user_configs"
}
