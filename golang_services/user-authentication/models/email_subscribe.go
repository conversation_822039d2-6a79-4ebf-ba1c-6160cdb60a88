package models

import (
	"time"
)

// EmailSubscribe email subscribe
type EmailSubscribe struct {
	ID                         int        `gorm:"primary_key;" json:"id" db:"id"`
	Email                      string     `json:"email" db:"email"`
	SubscribeAnnouncement      bool       `json:"subscribe_announcement" db:"subscribe_announcement"`
	SubscribeOrderNotification bool       `json:"subscribe_order_notification" db:"subscribe_order_notification"`
	CreatedAt                  time.Time  `gorm:"type:timestamp with time zone not null" json:"created_at" db:"created_at"`
	UpdatedAt                  *time.Time `json:"update_at" db:"updated_at"`
}
