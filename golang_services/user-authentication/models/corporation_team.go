package models

import (
	"encoding/json"
	"time"
)

type CorporationTeam struct {
	ID          int        `json:"id" db:"id"`
	OrgID       int64      `json:"org_id" db:"org_id"`
	Name        string     `json:"name" db:"name"`
	Description string     `json:"description" db:"description"`
	CreatedAt   time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at" db:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at" db:"deleted_at"`
}

type CorporationTeamDetail struct {
	*CorporationTeam
	TotalMember  int              `json:"total_member"`
	TotalManager int              `json:"total_manager"`
	Members      []MemberUserInfo `json:"members,omitempty"`
}

type CorporationTeamFilter struct {
	Query     string `schema:"query"`
	SortField string `schema:"sort_field"`
	SortOrder string `schema:"sort_order"`
	Offset    int    `schema:"offset"`
	Limit     int    `schema:"limit"`
}

type CorporationTeamData struct {
	Data    []CorporationTeamDetail `json:"data"`
	Success bool                    `json:"success"`
	Total   int                     `json:"total"`
}

var MemberRole = struct{ Manager, Member string }{"manager", "member"}
var MemberStatus = struct{ Pending, Active string }{"pending", "active"}

type CorporationTeamMember struct {
	ID              int              `json:"id" db:"id"`
	TeamID          int              `json:"team_id" db:"team_id"`
	UserID          string           `json:"user_id" db:"user_id"`
	Role            string           `json:"role" db:"role"`
	RequireApproval *bool            `json:"require_approval" db:"require_approval"`
	Approvers       *json.RawMessage `json:"approvers" db:"approvers"`
	Status          string           `json:"status" db:"status"`
	CreatedAt       time.Time        `json:"created_at" db:"created_at"`
	UpdatedAt       *time.Time       `json:"updated_at" db:"updated_at"`
}

type CorporationTeamMemberDetail struct {
	*CorporationTeamMember
	UserSurname   string `json:"user_surname"`
	UserGivenName string `json:"user_given_name"`
	UserEmail     string `json:"user_email"`
}

type CorporationTeamMemberFilter struct {
	Query     string `schema:"query"`
	Role      string `schema:"role"`
	SortField string `schema:"sort_field"`
	SortOrder string `schema:"sort_order"`
	Offset    int    `schema:"offset"`
	Limit     int    `schema:"limit"`
	TeamID    any
}

type CorporationTeamMemberData struct {
	Data    []CorporationTeamMemberDetail `json:"data"`
	Success bool                          `json:"success"`
	Total   int                           `json:"total"`
}

type AvailableUserForTeamFilter struct {
	Query  string `schema:"query"`
	Offset int    `schema:"offset"`
	Limit  int    `schema:"limit"`
}

type AvailableUserForTeamData struct {
	Data    []User `json:"data"`
	Success bool   `json:"success"`
	Total   int    `json:"total"`
}

type MemberUserInfo struct {
	Email     string `json:"email"`
	Surname   string `json:"surname"`
	GivenName string `json:"given_name"`
	Role      string `json:"role,omitempty"`
}

type ClassifyMember struct {
	UsersCanAddToTeam        []MemberUserInfo `json:"users_can_add_to_team"`
	UsersAlreadyAddedToTeam  []MemberUserInfo `json:"users_already_add_to_team"`
	UsersBelongToAnotherCorp []MemberUserInfo `json:"users_belong_to_another_corp"`
	UsersBelongToAnotherTeam []MemberUserInfo `json:"users_belong_to_another_team"`
	UsersNotInSystem         []MemberUserInfo `json:"users_not_in_system"`
}
