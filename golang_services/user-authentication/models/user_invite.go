package models

import (
	"time"
)

// UserInvite user invite
type UserInvite struct {
	ID                  int       `json:"id" db:"id"`
	UserID              string    `json:"user_id" db:"user_id"`
	Email               string    `json:"email" db:"email"`
	OTP                 string    `json:"token" db:"token"`
	IsUsed              bool      `json:"is_used" db:"is_used"`
	InvitedBy           string    `json:"invited_by" db:"invited_by"`
	OrgID               int64     `json:"org_id" db:"org_id"`
	ExpiredNotification bool      `json:"expired_notification" db:"expired_notification"`
	ExpiredAt           time.Time `json:"expired_at" db:"expired_at"`
	CreatedAt           time.Time `json:"created_at" db:"created_at"`
}
