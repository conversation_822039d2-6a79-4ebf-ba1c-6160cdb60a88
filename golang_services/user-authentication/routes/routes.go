package routes

import (
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/user-authentication/controllers"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/middleware/role"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/utils/auth"

	"github.com/gorilla/mux"
)

func Handlers() *mux.Router {

	e := mux.NewRouter().StrictSlash(true)
	e.Use(CommonMiddleware)

	r := e.PathPrefix("/v1/ums").Subrouter()
	{
		r.HandleFunc("/", controllers.TestAPI).Methods("GET")
		r.Handle<PERSON>unc("/status", controllers.TestAPI).Methods("GET")
		r.Handle<PERSON>unc("/register", controllers.CreateUser).Methods("POST")
		r.HandleFunc("/login", controllers.Login).Methods("POST")
		r.HandleFunc("/guest/login", controllers.GuestLogin).Methods("POST")

		r.<PERSON>le<PERSON>unc("/activate", controllers.ActivateUser).Methods("POST")
		r.HandleFunc("/email/verify", controllers.SendEmailUserVerification).Methods("POST")
		r.HandleFunc("/email/reset-password", controllers.SendEmailResetPassword).Methods("POST")
		r.HandleFunc("/email/subscribe", controllers.GetEmailSubscribe).Methods("GET")
		r.HandleFunc("/email/subscribe", controllers.UpdateEmailSubscribe).Methods("POST")
		r.HandleFunc("/otp-validate", controllers.ValidateOTP).Methods("POST")
		// Add Casbin policy, advanced internal user only, may break thing easily
		//r.HandleFunc("/addrole", controllers.AddCasbin).Methods("POST")
		// sso
		r.HandleFunc("/google/login", controllers.GoogleLogin).Methods("POST")
		r.HandleFunc("/apple/login", controllers.AppleLogin).Methods("POST")
		r.HandleFunc("/linkedin/login", controllers.LinkedinLogin).Methods("POST")
		r.HandleFunc("/okta/login", controllers.OktaLogin).Methods("GET")
		r.HandleFunc("/okta/authorization-code/callback", controllers.AuthCodeCallbackHandler).Methods("GET")
		r.HandleFunc("/trace/user/login", controllers.TraceUserLogin).Methods("POST")

		// Auth route allow user can get another info
		j := r.PathPrefix("/auth").Subrouter()
		{
			j.Use(auth.JwtVerify)
			j.HandleFunc("/user/{id}/user-list", controllers.ListUsers).Methods("GET")
			j.HandleFunc("/user/{id}/members/{member_id}", controllers.GetUser).Methods("GET")
			j.HandleFunc("/user/{id}/corp-info", controllers.GetUserCorpInfo).Methods("GET")
		}

		// Auth route
		s := r.PathPrefix("/auth").Subrouter()
		{
			s.Use(auth.JwtVerify, auth.UserIDVerify)
			// user API
			s.HandleFunc("/user/{id}/token", controllers.ExchangeToken).Methods("GET")
			s.HandleFunc("/user", controllers.GetUser).Methods("GET")

			// User config
			s.HandleFunc("/user/configs", controllers.GetUserConfigList).Methods("GET")
			s.HandleFunc("/user/configs", controllers.UpdateUserConfig).Methods("POST")
			s.HandleFunc("/user/configs/{config_key}", controllers.DeleteUserConfig).Methods("DELETE")

			s.HandleFunc("/user/{id}", controllers.GetUser).Methods("GET")
			s.HandleFunc("/user/{id}", controllers.UpdateUser).Methods("PUT")
			s.HandleFunc("/user/password/{id}", controllers.ResetPassword).Methods("POST")
			s.HandleFunc("/user/change-password/{id}", controllers.ChangePassword).Methods("POST")
			s.HandleFunc("/user/logout/{id}", controllers.Logout).Methods("POST")
			s.HandleFunc("/user/revoke/{id}", controllers.RevokeAccount).Methods("POST")
			s.HandleFunc("/user/{id}", controllers.DeleteUser).Methods("DELETE")
			s.HandleFunc("/user/{id}/organization", controllers.GetOrganization).Methods("GET")
			s.HandleFunc("/user/invited/{id}", controllers.UpdateInvitedMember).Methods("POST")
			//user role check
			s.HandleFunc("/user/{id}/enforce-safe", controllers.CasbinEnforceSafe).Methods("GET")
			// user corp info
			s.HandleFunc("/user/{id}/corp-info", controllers.CreateUserCorpInfo).Methods("POST")
			s.HandleFunc("/user/{id}/corp-info", controllers.UpdateUserCorpInfo).Methods("PUT")
			s.HandleFunc("/user/{id}/fetch-logo", controllers.FetchCorpLogo).Methods("GET")

			// user notifications
			s.HandleFunc("/user/{id}/notifications", controllers.GetUserNotificationList).Methods("GET")
			s.HandleFunc("/user/{id}/notifications", controllers.DeleteAllUserNotification).Methods("DELETE")
			s.HandleFunc("/user/{id}/notifications/{notification_id}", controllers.GetUserNotification).Methods("GET")
			s.HandleFunc("/user/{id}/notifications/{notification_id}/read", controllers.ReadUserNotification).Methods("POST")
			s.HandleFunc("/user/{id}/notifications/{notification_id}/unread", controllers.UnreadUserNotification).Methods("POST")
			s.HandleFunc("/user/{id}/notifications/{notification_id}", controllers.DeleteUserNotification).Methods("DELETE")

			// team access by manager
			s.HandleFunc("/user/{id}/team/members", controllers.OrgGetCorporationTeamMemberList).Methods("GET")
			s.HandleFunc("/user/{id}/teams/{team_id}", controllers.OrgGetCorporationTeamByID).Methods("GET")
			s.HandleFunc("/user/{id}/teams/{team_id}", controllers.OrgUpdateCorporationTeam).Methods("PUT")
			s.HandleFunc("/user/{id}/teams/{team_id}", controllers.OrgDeleteCorporationTeam).Methods("DELETE")
			s.HandleFunc("/user/{id}/teams/{team_id}/available-members", controllers.GetAvailableMemberToAddToTeam).Methods("GET")
			s.HandleFunc("/user/{id}/teams/{team_id}/members", controllers.OrgGetCorporationTeamMemberList).Methods("GET")
			s.HandleFunc("/user/{id}/teams/{team_id}/members", controllers.OrgAddMemberToCorporationTeam).Methods("POST")
			s.HandleFunc("/user/{id}/teams/{team_id}/members", controllers.OrgUpdateMembersInCorporationTeam).Methods("PUT")
			s.HandleFunc("/user/{id}/teams/{team_id}/members/{member_id}", controllers.OrgUpdateMemberInCorporationTeam).Methods("PUT")
			s.HandleFunc("/user/{id}/teams/{team_id}/members", controllers.OrgDeleteCorporationMultiTeamMember).Methods("DELETE")
			s.HandleFunc("/user/{id}/teams/{team_id}/member/classifies", controllers.ClassifyMembersIntoUserGroups).Methods("POST")

			// Corporation Cost Center
			s.HandleFunc("/user/{id}/corporation/centers", controllers.GetCorporationCostCenterList).Methods("GET")
			s.HandleFunc("/user/{id}/corporation/centers", controllers.AddCorporationCostCenter).Methods("POST")
			s.HandleFunc("/user/{id}/corporation/center/imports", controllers.ImportCorporationCostCenter).Methods("POST")
			s.HandleFunc("/user/{id}/corporation/centers", controllers.DeleteCorporationMultiCostCenter).Methods("DELETE")

			// Corporation Department
			s.HandleFunc("/user/{id}/corporation/departments", controllers.GetCorporationDepartmentList).Methods("GET")
			s.HandleFunc("/user/{id}/corporation/departments", controllers.AddCorporationDepartment).Methods("POST")
			s.HandleFunc("/user/{id}/corporation/department/imports", controllers.ImportCorporationDepartment).Methods("POST")
			s.HandleFunc("/user/{id}/corporation/departments", controllers.DeleteCorporationMultiDepartment).Methods("DELETE")

			// Corporation Direct Report
			s.HandleFunc("/user/{id}/corporation/managers/{manager_id}/classifies", controllers.ClassifyMembersForDirectReport).Methods("POST")
			s.HandleFunc("/user/{id}/corporation/managers/{manager_id}/direct-reports", controllers.GetMembersForDirectReport).Methods("GET")
			s.HandleFunc("/user/{id}/corporation/managers/{manager_id}/direct-reports", controllers.AddMembersForDirectReport).Methods("POST")
			s.HandleFunc("/user/{id}/corporation/managers/{manager_id}/direct-reports", controllers.RemoveDirectReportsManager).Methods("DELETE")

			s.HandleFunc("/user/{id}/corporation/approve-countries", controllers.UpsertApproveCountry).Methods("POST")
			s.HandleFunc("/user/{id}/corporation/approve-countries", controllers.DeleteApproveCountry).Methods("DELETE")

		}

		// admin user API
		a := r.PathPrefix("/admin").Subrouter()
		{
			a.Use(auth.JwtVerify)
			a.Use(role.AuthCheckRole)
			a.HandleFunc("/org/{org_id}/provider/{provider_id}", controllers.GetOrganizationDetail).Methods("GET")
			a.HandleFunc("/org/{org_id}/user-list", controllers.ListUsersUnderOrg).Methods("GET")
			a.HandleFunc("/org/{org_id}/user/{id}", controllers.GetUser).Methods("GET")
			a.HandleFunc("/org/{org_id}/user/{id}", controllers.UpdateUser).Methods("PUT")
			a.HandleFunc("/org/{org_id}/invite/{user_id}", controllers.InviteMembers).Methods("POST")
			a.HandleFunc("/org/{org_id}/cancel-invite", controllers.CancelInvitation).Methods("POST")
			a.HandleFunc("/org/{org_id}/update-user", controllers.UpdateUserUnderOrg).Methods("PUT")
			a.HandleFunc("/org/{org_id}/import-users", controllers.ImportUserFromCSV).Methods("POST")
			a.HandleFunc("/org/{org_id}/upload-logo", controllers.UpdateCorpLogo).Methods("POST")
			a.HandleFunc("/org/{org_id}/export-users", controllers.ExportUsersToCSV).Methods("POST")
			a.HandleFunc("/org/{org_id}/activate-users", controllers.ActivateUsers).Methods("POST")
			a.HandleFunc("/org/{org_id}/deactivate-users", controllers.DeactivateUsers).Methods("POST")
			a.HandleFunc("/org/{org_id}/remove-user/{user_id}", controllers.RemoveUserFromOrg).Methods("POST")
			// Corporation
			a.HandleFunc("/org/{org_id}/restrict-domain-email", controllers.EnableDomainOnlyInvite).Methods("POST")
			a.HandleFunc("/org/{org_id}/no-email-restriction", controllers.DisableDomainOnlyInvite).Methods("POST")
			a.HandleFunc("/org/{org_id}/get-corp", controllers.RestrictDomainEmailStatus).Methods("GET")

			// Corporation offices
			a.HandleFunc("/org/{org_id}/offices", controllers.CreateOffice).Methods("POST")
			a.HandleFunc("/org/{org_id}/offices", controllers.GetOfficeList).Methods("GET")
			a.HandleFunc("/org/{org_id}/offices/{office_id}", controllers.UpdateOffice).Methods("PUT")
			a.HandleFunc("/org/{org_id}/offices/{office_id}", controllers.DeleteOffice).Methods("DELETE")

			// Corporation contacts
			a.HandleFunc("/org/{org_id}/contacts", controllers.GetADContactList).Methods("GET")

			// team access by corporation admin
			a.HandleFunc("/org/{org_id}/teams", controllers.OrgGetCorporationTeamList).Methods("GET")
			a.HandleFunc("/org/{org_id}/teams", controllers.OrgCreateCorporationTeamWithMember).Methods("POST")
			a.HandleFunc("/org/{org_id}/teams", controllers.OrgDeleteCorporationMultiTeam).Methods("DELETE")
			a.HandleFunc("/org/{org_id}/teams/{team_id}", controllers.OrgGetCorporationTeamByID).Methods("GET")
			a.HandleFunc("/org/{org_id}/teams/{team_id}", controllers.OrgUpdateCorporationTeam).Methods("PUT")
			a.HandleFunc("/org/{org_id}/teams/{team_id}", controllers.OrgDeleteCorporationTeam).Methods("DELETE")

			// team members by corporation admin
			a.HandleFunc("/org/{org_id}/teams/{team_id}/available-members", controllers.GetAvailableMemberToAddToTeam).Methods("GET")
			a.HandleFunc("/org/{org_id}/teams/{team_id}/members", controllers.OrgGetCorporationTeamMemberList).Methods("GET")
			a.HandleFunc("/org/{org_id}/teams/{team_id}/members", controllers.OrgAddMemberToCorporationTeam).Methods("POST")
			a.HandleFunc("/org/{org_id}/teams/{team_id}/members", controllers.OrgUpdateMembersInCorporationTeam).Methods("PUT")
			a.HandleFunc("/org/{org_id}/teams/{team_id}/members/{member_id}", controllers.OrgUpdateMemberInCorporationTeam).Methods("PUT")
			a.HandleFunc("/org/{org_id}/teams/{team_id}/members", controllers.OrgDeleteCorporationMultiTeamMember).Methods("DELETE")
			a.HandleFunc("/org/{org_id}/teams/{team_id}/member/classifies", controllers.ClassifyMembersIntoUserGroups).Methods("POST")
		}

		// AD admin API
		ad := r.PathPrefix("/ad-admin").Subrouter()
		{
			ad.Use(auth.JwtVerify)
			ad.Use(role.AuthCheckRole)
			ad.HandleFunc("/get-users", controllers.ListAllUsersV2).Methods("GET")
			ad.HandleFunc("/get-consulates", controllers.ListAllConsulates).Methods("GET")
			ad.HandleFunc("/get-ets-providers", controllers.ListAllProviders).Methods("GET")

			ad.HandleFunc("/export-users", controllers.ExportUsersToCSVByADAdmin).Methods("POST")

			// organization api
			ad.HandleFunc("/org/create", controllers.CreateOrg).Methods("POST")
			ad.HandleFunc("/corporation/create", controllers.CreateCorporation).Methods("POST")
			ad.HandleFunc("/consulate/create", controllers.CreateConsulate).Methods("POST")
			ad.HandleFunc("/etsprovider/create", controllers.CreateEtsProvider).Methods("POST")
			ad.HandleFunc("/get-organizations", controllers.FindOrgList).Methods("GET")
			ad.HandleFunc("/organizations/{org_id}", controllers.GetOrgByID).Methods("GET")

			// user api
			ad.HandleFunc("/user-update/{user_id}", controllers.AdminUpdateUser).Methods("PUT")

			// Add Casbin policy, advanced internal user only, may break thing easily
			ad.HandleFunc("/addrole", controllers.AddCasbin).Methods("POST")

			// Corporation ad contacts
			ad.HandleFunc("/org/{org_id}/contacts", controllers.CreateADContact).Methods("POST")
			ad.HandleFunc("/org/{org_id}/contacts", controllers.GetADContactList).Methods("GET")
			ad.HandleFunc("/org/{org_id}/contacts/{contact_id}", controllers.UpdateADContact).Methods("PUT")
			ad.HandleFunc("/org/{org_id}/contacts/{contact_id}", controllers.DeleteADContact).Methods("DELETE")
		}
	}
	return e
}

// CommonMiddleware --Set content-type
func CommonMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Add("Content-Type", "application/json")
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "POST,GET,OPTIONS,PUT,DELETE")
		w.Header().Set("Access-Control-Allow-Headers", "Accept,Content-Type,Content-Length,Accept-Encoding,X-CSRF-Token,Authorization,Access-Control-Request-Headers,Access-Control-Request-Method,Connection,Host,Origin,User-Agent,Referer,Cache-Control,X-header")
		next.ServeHTTP(w, r)
	})
}
