package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	adUtil "bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"github.com/gorilla/schema"
	uuid "github.com/satori/go.uuid"
	"github.com/thoas/go-funk"
)

func GetCorporationCostCenterList(w http.ResponseWriter, r *http.Request) {
	currentUserID := r.Context().Value("id").(uuid.UUID)
	var currentUser models.User
	if err := Db.Where("id = ?", currentUserID.String()).First(&currentUser).Error; err != nil {
		handleError(w, err)
		return
	}

	if err := r.ParseForm(); err != nil {
		return
	}

	filter := new(models.CorporationCostCenterFilter)
	filter.Limit = 10
	if err := schema.NewDecoder().Decode(filter, r.Form); err != nil {
		handleError(w, err)
		return
	}

	resp := models.CorporationCostCenterData{
		Success: true,
		Data:    []models.CorporationCostCenter{},
	}

	query := Db.Table("corporation_cost_centers").
		Where("org_id = ?", currentUser.OrganizationID).
		Where("deleted_at IS NULL").
		Limit(filter.Limit).Offset(filter.Offset)

	if filter.Query != "" {
		query = query.Where("name ILIKE ?", "%"+filter.Query+"%")
	}

	if strings.ToLower(filter.SortOrder) == "desc" {
		filter.SortOrder = "DESC"
	} else {
		filter.SortOrder = "ASC"
	}

	if funk.Contains([]string{"name"}, filter.SortField) {
		query = query.Order(fmt.Sprintf("%s %s", filter.SortField, filter.SortOrder))
	} else {
		query = query.Order("name ASC") // Default sort
	}

	// Get pagination data and total
	query.Limit(filter.Limit).Offset(filter.Offset).Find(&resp.Data)
	query.Limit(1).Offset(0).Count(&resp.Total)

	json.NewEncoder(w).Encode(resp)
}

func AddCorporationCostCenter(w http.ResponseWriter, r *http.Request) {
	currentUserID := r.Context().Value("id").(uuid.UUID)
	var currentUser models.User
	if err := Db.Where("id = ?", currentUserID.String()).First(&currentUser).Error; err != nil {
		handleError(w, err)
		return
	}

	costCenter := models.CorporationCostCenter{}
	if err := json.NewDecoder(r.Body).Decode(&costCenter); err != nil {
		handleError(w, err)
		return
	}

	if err := addCorpCostCenter(currentUser.OrganizationID, costCenter.Name); err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
	})
}

// ImportCorporationCostCenter import corporation cost center from csv, xls, xlsx file
func ImportCorporationCostCenter(w http.ResponseWriter, r *http.Request) {
	currentUserID := r.Context().Value("id").(uuid.UUID)
	var currentUser models.User
	if err := Db.Where("id = ?", currentUserID.String()).First(&currentUser).Error; err != nil {
		handleError(w, err)
		return
	}

	file, header, _ := r.FormFile("file")
	records, err := adUtil.ReadSheetRecords(file, header)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "message": err})
		return
	}
	total, success, importErr := importCorporationCostCenter(currentUser.OrganizationID, records)
	json.NewEncoder(w).Encode(map[string]any{
		"success":       true,
		"total":         total,
		"success_count": success,
		"import_err":    importErr,
	})
}

func importCorporationCostCenter(orgID int64, records [][]string) (total int, success int, importErr map[int]string) {
	if len(records) <= 1 {
		return
	}

	total = len(records) - 1
	importErr = map[int]string{}

	// Scan each records
	for i, record := range records {
		if i == 0 {
			continue // Skip header row
		}

		rowNumber := i + 1
		if len(record) < 1 {
			importErr[rowNumber] = "invalid data" // map row_number and error
			continue
		}

		if err := addCorpCostCenter(orgID, record[0]); err != nil {
			importErr[rowNumber] = err.Error() // map row_number and error
			continue
		}
		success++
	}
	return
}

func addCorpCostCenter(orgID int64, name string) error {
	if err := Db.Where("org_id = ? AND name = ?", orgID, name).First(&models.CorporationCostCenter{}).Error; err == nil {
		return errors.AlreadyExistErr
	}

	if err := Db.Create(&models.CorporationCostCenter{
		Name:  name,
		OrgID: orgID,
	}).Error; err != nil {
		return err
	}

	return nil
}

func DeleteCorporationMultiCostCenter(w http.ResponseWriter, r *http.Request) {
	req := []int{}
	json.NewDecoder(r.Body).Decode(&req)

	if err := Db.Exec("UPDATE corporation_cost_centers SET deleted_at = NOW() WHERE id IN (?)", req).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
	})
}
