package controllers

import (
	"encoding/json"
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"github.com/gorilla/mux"
	"github.com/thoas/go-funk"
)

// UpsertApproveCountry add or remove approve country
func UpsertApproveCountry(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var (
		newCountries []string
		corpInfo     models.UserCorpInfo
	)

	json.NewDecoder(r.Body).Decode(&newCountries)

	if err := Db.Table("user_corp_info").Where("user_id = ?", params["id"]).First(&corpInfo).Error; err != nil {
		handleError(w, err)
		return
	}

	if corpInfo.ApprovedCountries == nil {
		corpInfo.ApprovedCountries = []string{}
	}

	for _, country := range newCountries {
		if index := funk.IndexOfString(corpInfo.ApprovedCountries, country); index == -1 {
			corpInfo.ApprovedCountries = append(corpInfo.ApprovedCountries, country) // Append if not exist
		}
	}

	if err := Db.Exec("UPDATE user_corp_info SET approved_countries = ? WHERE user_id = ?",
		corpInfo.ApprovedCountries, corpInfo.UserID).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    corpInfo.ApprovedCountries,
	})
}

// DeleteApproveCountry delete approve country
func DeleteApproveCountry(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var (
		deleteCountries []string
		corpInfo        models.UserCorpInfo
	)

	json.NewDecoder(r.Body).Decode(&deleteCountries)

	if err := Db.Table("user_corp_info").Where("user_id = ?", params["id"]).First(&corpInfo).Error; err != nil {
		handleError(w, err)
		return
	}

	for _, country := range deleteCountries {
		if index := funk.IndexOfString(corpInfo.ApprovedCountries, country); index > -1 {
			corpInfo.ApprovedCountries = append(corpInfo.ApprovedCountries[:index], corpInfo.ApprovedCountries[index+1:]...) // Delete if exist
		}
	}

	if err := Db.Exec("UPDATE user_corp_info SET approved_countries = ? WHERE user_id = ?",
		corpInfo.ApprovedCountries, corpInfo.UserID).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    corpInfo.ApprovedCountries,
	})
}
