package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/utils"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/gorilla/mux"
	"github.com/gorilla/schema"
	"github.com/jinzhu/gorm"
	uuid "github.com/satori/go.uuid"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
)

func OrgGetCorporationTeamMemberList(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	if err := r.ParseForm(); err != nil {
		return
	}

	filter := new(models.CorporationTeamMemberFilter)
	filter.Limit = 10
	schema.NewDecoder().Decode(filter, r.Form)

	resp := models.CorporationTeamMemberData{
		Success: true,
		Data:    []models.CorporationTeamMemberDetail{},
	}

	if val, ok := params["team_id"]; ok {
		filter.TeamID = val
	}

	if val, ok := params["id"]; ok {
		var member models.CorporationTeamMember
		Db.Where("user_id = ? AND role = ?", val, models.MemberRole.Manager).First(&member)
		filter.TeamID = member.TeamID
	}

	query := Db.Table("corporation_team_members cm").
		Joins("LEFT JOIN users u ON u.id = cm.user_id").
		Select(`cm.id, cm.team_id, cm.user_id, cm."role", cm.require_approval, cm.status, cm.created_at, cm.updated_at,
			u.surname user_surname, u.given_name user_given_name, CONCAT_WS(' ', u.surname, u.given_name) user_full_name, u.email user_email`).
		Where("team_id = ?", filter.TeamID)

	if filter.Query != "" {
		query = query.Where("CONCAT_WS(' ', u.surname, u.given_name) ILIKE ? OR u.email ILIKE ?", "%"+filter.Query+"%", "%"+filter.Query+"%")
	}

	if filter.Role != "" {
		query = query.Where("cm.role = ?", filter.Role)
	}

	if strings.ToLower(filter.SortOrder) == "desc" {
		filter.SortOrder = "DESC"
	} else {
		filter.SortOrder = "ASC"
	}

	if funk.Contains([]string{"user_full_name", "role", "require_approval", "status"}, filter.SortField) {
		query = query.Order(fmt.Sprintf("%s %s", filter.SortField, filter.SortOrder))
	} else {
		query = query.Order("user_full_name ASC") // Default sort
	}

	// Get pagination data and total
	query.Limit(filter.Limit).Offset(filter.Offset).Find(&resp.Data)
	query.Limit(1).Offset(0).Count(&resp.Total)

	json.NewEncoder(w).Encode(resp)
}

func ClassifyMembersIntoUserGroups(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	currentUserID := r.Context().Value("id").(uuid.UUID)
	var user models.User
	if err := Db.Where("id = ?", currentUserID.String()).First(&user).Error; err != nil {
		handleError(w, err)
		return
	}

	teamID, _ := strconv.Atoi(params["team_id"])
	members := []models.MemberUserInfo{}
	json.NewDecoder(r.Body).Decode(&members)

	classifies, err := classifyMembersIntoUserGroups(user.OrganizationID, teamID, members)
	if err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    classifies,
	})
}

func classifyMembersIntoUserGroups(orgID int64, teamID int, members []models.MemberUserInfo) (models.ClassifyMember, error) {
	result := models.ClassifyMember{
		UsersCanAddToTeam:        []models.MemberUserInfo{},
		UsersBelongToAnotherCorp: []models.MemberUserInfo{},
		UsersBelongToAnotherTeam: []models.MemberUserInfo{},
		UsersNotInSystem:         []models.MemberUserInfo{},
	}

	// Map to remove duplicate email
	membersMap := funk.ToMap(members, "Email").(map[string]models.MemberUserInfo)

	for _, member := range membersMap {
		var user models.User
		if err := Db.Where("email = ?", member.Email).First(&user).Error; err != nil && err != gorm.ErrRecordNotFound {
			return result, err
		}
		if member.GivenName == "" {
			member.GivenName = user.GivenName
		}

		if member.Surname == "" {
			member.Surname = user.Surname
		}

		if user.ID == uuid.FromBytesOrNil(nil) {
			result.UsersNotInSystem = append(result.UsersNotInSystem, member)
			continue
		}

		if user.OrganizationID != orgID {
			result.UsersBelongToAnotherCorp = append(result.UsersBelongToAnotherCorp, member)
			continue
		}

		var teamMember models.CorporationTeamMember

		if err := Db.Where("user_id = ?", user.ID).First(&teamMember).Error; err != nil && err != gorm.ErrRecordNotFound {
			return result, err
		}
		if teamMember.ID > 0 {
			if teamMember.ID == teamID {
				result.UsersAlreadyAddedToTeam = append(result.UsersAlreadyAddedToTeam, member)
			} else {
				result.UsersBelongToAnotherTeam = append(result.UsersBelongToAnotherTeam, member)
			}
			continue
		}

		result.UsersCanAddToTeam = append(result.UsersCanAddToTeam, member)
	}
	return result, nil
}

func OrgAddMemberToCorporationTeam(w http.ResponseWriter, r *http.Request) {
	currentUserID := r.Context().Value("id").(uuid.UUID)
	var currentUser models.User
	if err := Db.Where("id = ?", currentUserID.String()).First(&currentUser).Error; err != nil {
		handleError(w, err)
		return
	}

	params := mux.Vars(r)
	teamID, _ := strconv.Atoi(params["team_id"])

	members := []models.MemberUserInfo{}
	json.NewDecoder(r.Body).Decode(&members)

	class, err := classifyMembersIntoUserGroups(currentUser.OrganizationID, teamID, members)
	if err != nil {
		handleError(w, err)
		return
	}

	// Directly add members to team
	for _, member := range class.UsersCanAddToTeam {
		var user models.User
		if err := Db.Where("email = ?", member.Email).First(&user).Error; err != nil {
			handleError(w, err)
			return
		}
		if err := Db.Create(&models.CorporationTeamMember{
			TeamID:          teamID,
			UserID:          user.ID.String(),
			Role:            funk.ShortIf(member.Role == models.MemberRole.Manager, models.MemberRole.Manager, models.MemberRole.Member).(string),
			RequireApproval: aws.Bool(true),
			Status:          models.MemberStatus.Active,
		}).Error; err != nil {
			handleError(w, err)
			return
		}
	}

	// Add member to team and send email pending
	for _, member := range class.UsersNotInSystem {
		user := models.User{
			Email:         member.Email,
			GivenName:     member.GivenName,
			Surname:       member.Surname,
			EmailVerified: true,
			Status:        models.UserStatus.Pending,
			// OrganizationID: null.IntFrom(0),
			Password: utils.RandomPassword(),
		}
		user.OrganizationID = currentUser.OrganizationID
		user.PasswordInit = user.Password
		if err := Db.Create(&user).Error; err != nil {
			handleError(w, err)
			return
		}

		if err := Db.Create(&models.CorporationTeamMember{
			TeamID:          teamID,
			UserID:          user.ID.String(),
			Role:            funk.ShortIf(member.Role == models.MemberRole.Manager, models.MemberRole.Manager, models.MemberRole.Member).(string),
			RequireApproval: aws.Bool(true),
			Status:          models.MemberStatus.Pending,
		}).Error; err != nil {
			handleError(w, err)
			return
		}

		go SendEmailInvitedMember(&currentUser, &user, false)
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
	})
}

func OrgUpdateMemberInCorporationTeam(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)

	member := models.CorporationTeamMember{}
	json.NewDecoder(r.Body).Decode(&member)

	existMember := models.CorporationTeamMember{}
	if err := Db.Where("id = ?", params["member_id"]).First(&existMember).Error; err != nil {
		handleError(w, err)
		return
	}

	if existMember.ID <= 0 {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{
			"success": false,
			"error":   errors.NotFoundErr,
		})
		return
	}

	if funk.Contains([]string{models.MemberRole.Manager, models.MemberRole.Member}, member.Role) {
		existMember.Role = member.Role
	}

	if member.RequireApproval != nil {
		existMember.RequireApproval = member.RequireApproval
	}

	if member.Approvers != nil {
		for _, approverID := range gjson.ParseBytes(*member.Approvers).Array() {
			if approverID.String() == existMember.UserID {
				w.WriteHeader(http.StatusBadRequest)
				json.NewEncoder(w).Encode(map[string]any{
					"success": false,
					"error":   errors.CanNotSelfAssign,
				})
				return
			}
		}
		existMember.Approvers = member.Approvers
	}

	if err := Db.Save(&existMember).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    existMember,
	})
}

func OrgUpdateMembersInCorporationTeam(w http.ResponseWriter, r *http.Request) {
	members := []models.CorporationTeamMember{}
	json.NewDecoder(r.Body).Decode(&members)

	for _, member := range members {
		existMember := models.CorporationTeamMember{}
		if err := Db.Where("id = ?", member.ID).First(&existMember).Error; err != nil {
			handleError(w, err)
			return
		}
		if existMember.ID <= 0 {
			w.WriteHeader(http.StatusBadRequest)
			json.NewEncoder(w).Encode(map[string]any{
				"success": false,
				"error":   errors.NotFoundErr,
			})
			return
		}

		if funk.Contains([]string{models.MemberRole.Manager, models.MemberRole.Member}, member.Role) {
			existMember.Role = member.Role
		}

		if member.RequireApproval != nil {
			existMember.RequireApproval = member.RequireApproval
		}
		if err := Db.Save(&existMember).Error; err != nil {
			handleError(w, err)
			return
		}
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
	})
}

func OrgDeleteCorporationMultiTeamMember(w http.ResponseWriter, r *http.Request) {
	req := []int{}
	json.NewDecoder(r.Body).Decode(&req)

	if err := Db.Exec("DELETE FROM corporation_team_members WHERE id IN (?)", req).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
	})
}

func GetAvailableMemberToAddToTeam(w http.ResponseWriter, r *http.Request) {
	filter := models.AvailableUserForTeamFilter{}
	filter.Limit = 10
	if err := r.ParseForm(); err != nil {
		return
	}
	schema.NewDecoder().Decode(&filter, r.Form)

	currentUserID := r.Context().Value("id").(uuid.UUID)
	var user models.User
	if err := Db.Where("id = ?", currentUserID.String()).First(&user).Error; err != nil {
		handleError(w, err)
		return
	}

	result := models.AvailableUserForTeamData{
		Success: true,
		Data:    []models.User{},
	}
	query := Db.Where("organization_id = ?", user.OrganizationID).
		Where("id NOT IN (SELECT user_id FROM corporation_team_members)")

	if filter.Query != "" {
		query = query.Where("CONCAT_WS(' ',surname,given_name) ILIKE ? OR email ILIKE ?", "%"+filter.Query+"%", "%"+filter.Query+"%")
	}

	if err := query.Limit(filter.Limit).Offset(filter.Offset).Find(&result.Data).Error; err != nil {
		handleError(w, err)
		return
	}

	if err := query.Table("users").Count(&result.Total).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(result)
}
