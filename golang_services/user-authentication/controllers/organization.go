package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/gorilla/mux"
	"github.com/thoas/go-funk"

	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"github.com/ulule/deepcopier"
)

func CreateOrg(w http.ResponseWriter, r *http.Request) {
	org := &models.Organization{}
	err := json.NewDecoder(r.Body).Decode(org)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	createdOrg := Db.Create(org)
	var errMessage = createdOrg.Error

	if createdOrg.Error != nil {
		fmt.Println(errMessage)
	}
	message := &models.RegistrationMessage{
		Data:          createdOrg.Value,
		Error:         createdOrg.Error,
		Rows_affected: createdOrg.RowsAffected,
	}
	json.NewEncoder(w).Encode(message)
}

func CreateCorporation(w http.ResponseWriter, r *http.Request) {
	corp := &models.CorporationDetail{}
	err := json.NewDecoder(r.Body).Decode(corp)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}
	if CheckCorpCodeExists(corp.Code) {
		resp := &models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00016",
				ErrorMessage: "Corporation code already exists",
			},
			Status: false,
		}
		json.NewEncoder(w).Encode(resp)
		return
	}

	// 1.create org
	org := &models.Organization{}
	Db.Create(org)

	// 2. save corporation
	newCorp := &models.Corporation{}
	deepcopier.Copy(corp).To(newCorp)
	newCorp.Address = corp.Address
	newCorp.Contact = corp.Contact
	newCorp.SecondaryContact = corp.SecondaryContact
	newCorp.OrgID = org.ID
	createdCorp := Db.Create(newCorp)
	var errMessage = createdCorp.Error

	if createdCorp.Error != nil {
		resp := &models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00014",
				ErrorMessage: errMessage.Error(),
			},
			Status: false,
		}
		json.NewEncoder(w).Encode(resp)
		return
	}

	message := &models.OrgRegistrationMessage{
		Data:          createdCorp.Value,
		OrgID:         org.ID,
		Error:         createdCorp.Error,
		Rows_affected: createdCorp.RowsAffected,
	}
	json.NewEncoder(w).Encode(message)
}

func CreateConsulate(w http.ResponseWriter, r *http.Request) {
	cons := &models.ConsulateDetail{}
	err := json.NewDecoder(r.Body).Decode(cons)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	// 1.create org
	org := &models.Organization{}

	// 2. save consulate
	newCons := &models.Consulate{}
	deepcopier.Copy(cons).To(newCons)
	newCons.Address = cons.Address
	newCons.Contact = cons.Contact
	newCons.SecondaryContact = cons.SecondaryContact
	newCons.OrgID = org.ID
	createdCons := Db.Create(newCons)
	fmt.Println(newCons.SupportedVisaProduct)
	var errMessage = createdCons.Error

	if createdCons.Error != nil {
		resp := &models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00014",
				ErrorMessage: errMessage.Error(),
			},
			Status: false,
		}
		json.NewEncoder(w).Encode(resp)
		return
	}

	message := &models.OrgRegistrationMessage{
		Data:          createdCons.Value,
		OrgID:         org.ID,
		Error:         createdCons.Error,
		Rows_affected: createdCons.RowsAffected,
	}
	json.NewEncoder(w).Encode(message)
}

func CreateEtsProvider(w http.ResponseWriter, r *http.Request) {
	EtsProvider := &models.EtsProvider{}
	err := json.NewDecoder(r.Body).Decode(EtsProvider)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	// 1.create org
	org := &models.Organization{}

	// 2. save ets-provider
	newEtsp := &models.EtsProvider{}
	deepcopier.Copy(EtsProvider).To(newEtsp)
	newEtsp.Address = EtsProvider.Address
	newEtsp.Contact = EtsProvider.Contact
	newEtsp.SecondaryContact = EtsProvider.SecondaryContact
	newEtsp.OrgID = org.ID
	createdEtsp := Db.Create(newEtsp)
	var errMessage = createdEtsp.Error

	if createdEtsp.Error != nil {
		resp := &models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00014",
				ErrorMessage: errMessage.Error(),
			},
			Status: false,
		}
		json.NewEncoder(w).Encode(resp)
		return
	}

	message := &models.OrgRegistrationMessage{
		Data:          createdEtsp.Value,
		OrgID:         org.ID,
		Error:         createdEtsp.Error,
		Rows_affected: createdEtsp.RowsAffected,
	}
	json.NewEncoder(w).Encode(message)
}

// check if corp code exists
func CheckCorpCodeExists(code string) bool {
	checkCorp := &models.Corporation{}
	Db.Table("corporation").Where("code = ?", code).First(&checkCorp)
	if checkCorp.Code == code {
		return true
	}
	return false
}

// find organization time zone name by id/profile
func FindOrgTimeZoneByID(id int64) (string, error) {
	timeZone := ""
	rows, err := Db.DB().Query("SELECT timezone_name FROM v_organizations WHERE id = $1", id)
	if err != nil {
		return "", err
	}
	defer rows.Close()
	if rows.Next() {
		if err := rows.Scan(&timeZone); err != nil {
			return "", err
		}
	}

	// for individual and other user, return empty string
	return timeZone, nil
}

// FindOrgList find all organization list
func FindOrgList(w http.ResponseWriter, r *http.Request) {
	var res = models.OrganizationPagination{
		Limit: 10,
		Data:  []models.ViewOrganization{},
	}

	var query = Db.Table("v_organizations")

	var params = r.URL.Query()
	if val := params["name"]; len(val) > 0 && val[0] != "" {
		query = query.Where("name ILIKE ?", "%"+val[0]+"%")
	}

	if val := params["type"]; len(val) > 0 && val[0] != "" {
		query = query.Where("type LIKE ?", "%"+val[0]+"%")
	}

	if val := params["limit"]; len(val) > 0 && val[0] != "" {
		if limit, err := strconv.ParseUint(val[0], 10, 64); err == nil {
			res.Limit = limit
		}
	}
	if val := params["offset"]; len(val) > 0 && val[0] != "" {
		if offset, err := strconv.ParseUint(val[0], 10, 64); err == nil {
			res.Offset = offset
		}
	}

	var sortField, sortOrder = "created_at", "desc" // Default
	if val := params["sort_field"]; len(val) > 0 {
		if utils.Contain(val[0], []string{"type", "name", "created_at", "domain", "code", "country", "timezone_name"}) {
			sortField = val[0]
		}
	}

	if val := params["sort_order"]; len(val) > 0 {
		if val[0] == "desc" {
			sortOrder = "desc"
		} else {
			sortOrder = "asc"
		}
	}

	if sortField != "" {
		query = query.Order(fmt.Sprintf("%s %s", sortField, sortOrder))
	}

	query.Count(&res.TotalCount)
	query = query.Limit(res.Limit).Offset(res.Offset)
	query.Find(&res.Data)

	res.Success = true
	json.NewEncoder(w).Encode(res)
}

// GetOrgByID get org by id
func GetOrgByID(w http.ResponseWriter, r *http.Request) {
	result := models.ViewOrganization{}
	params := mux.Vars(r)
	var query = Db.Table("v_organizations").Where("id = ?", params["org_id"])

	query.First(&result)

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    result,
	})
}

// determine if this user is from AD
func IsADUser(orgID int64) bool {
	corp := &models.Corporation{}
	if err := Db.Table("corporation").Where("org_id = ?", orgID).First(corp).Error; err != nil {
		fmt.Println("Corporation not found!, OrgID:", orgID)
		return false
	}

	if funk.ContainsString([]string{"ariadirect.com"}, corp.Domain) {
		return true
	}
	return false
}

// fetch corporation logo
func FetchCorpLogo(w http.ResponseWriter, r *http.Request) {
	awsConfig := aws.NewConfig().WithRegion(region).WithLogLevel(aws.LogOff)
	sess, err := session.NewSession(awsConfig)
	if err != nil {
		var resp = map[string]any{"success": false, "message": "Invalid request"}
		json.NewEncoder(w).Encode(resp)
		return
	}

	bucket, key := s3_map["ariadirect_prod_corporation_logo"].(string), r.URL.Query().Get("corp_id")+"/logo.jpg"
	req, _ := s3.New(sess).GetObjectRequest(&s3.GetObjectInput{Bucket: aws.String(bucket), Key: aws.String(key)})
	url, err := req.Presign(24 * time.Hour)
	if err != nil {
		var resp = map[string]any{"success": false, "message": "Invalid request"}
		json.NewEncoder(w).Encode(resp)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"url":     url,
	})
}

func EnableDomainOnlyInvite(w http.ResponseWriter, r *http.Request) {
	corp := models.Corporation{}
	err := json.NewDecoder(r.Body).Decode(&corp)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request"}
		json.NewEncoder(w).Encode(resp)
		return
	}

	if err := Db.Where("id = ?", corp.ID).First(&corp).Error; err != nil {
		resp := map[string]any{"status": false, "message": "Corporation does not exist."}
		json.NewEncoder(w).Encode(resp)
		return
	}

	corp.DomainEmailOnly = true
	Db.Save(&corp)
	json.NewEncoder(w).Encode(&corp)
}

func DisableDomainOnlyInvite(w http.ResponseWriter, r *http.Request) {
	corp := models.Corporation{}
	err := json.NewDecoder(r.Body).Decode(&corp)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request"}
		json.NewEncoder(w).Encode(resp)
		return
	}

	if err := Db.Where("id = ?", corp.ID).First(&corp).Error; err != nil {
		resp := map[string]any{"status": false, "message": "Corporation does not exist."}
		json.NewEncoder(w).Encode(resp)
		return
	}

	corp.DomainEmailOnly = false
	Db.Save(&corp)
	json.NewEncoder(w).Encode(&corp)
}
