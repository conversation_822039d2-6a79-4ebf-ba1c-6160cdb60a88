package controllers

import (
	"encoding/json"
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"

	"github.com/samber/lo"
	uuid "github.com/satori/go.uuid"
)

func GetUserConfigList(w http.ResponseWriter, r *http.Request) {
	currentUserID := r.Context().Value("id").(uuid.UUID).String()
	var configs []models.UserConfig
	if err := Db.Where("user_id = ?", currentUserID).Find(&configs).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data": lo.Map(configs, func(c models.UserConfig, _ int) map[string]any {
			return map[string]any{
				"config_key":   c.Config<PERSON>ey,
				"config_value": c.ConfigValue,
			}
		}),
	})
}

func UpdateUserConfig(w http.ResponseWriter, r *http.Request) {
	currentUserID := r.Context().Value("id").(uuid.UUID).String()

	var config models.UserConfig
	if err := json.NewDecoder(r.Body).Decode(&config); err != nil {
		handleError(w, err)
		return
	}

	config.UserID = currentUserID
	if err := Db.Where("user_id = ? AND config_key = ?", currentUserID, config.ConfigKey).FirstOrInit(&config).Error; err != nil {
		handleError(w, err)
		return
	}
	if err := Db.Save(&config).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{"success": true})
}

func DeleteUserConfig(w http.ResponseWriter, r *http.Request) {
	currentUserID := r.Context().Value("id").(uuid.UUID).String()

	var config models.UserConfig
	if err := json.NewDecoder(r.Body).Decode(&config); err != nil {
		handleError(w, err)
		return
	}

	if err := Db.Where("user_id = ? AND config_key = ?", currentUserID, config.ConfigKey).Delete(&config).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{"success": true})
}
