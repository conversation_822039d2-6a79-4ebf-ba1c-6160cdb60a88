package controllers

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"google.golang.org/api/oauth2/v2"
)

func verifyIdToken(idToken string) (*oauth2.Tokeninfo, error) {
	// Verify token
	oauth2Service, err := oauth2.New(httpClient)
	tokenInfoCall := oauth2Service.Tokeninfo()
	tokenInfoCall.IdToken(idToken)
	tokenInfo, err := tokenInfoCall.Do()
	if err != nil {
		return nil, err
	}

	// Verify client id
	if CLIENT_ID != tokenInfo.Audience && SECOND_CLIENT_ID != tokenInfo.Audience && IOS_CLIENT_ID != tokenInfo.Audience {
		fmt.Println("Client ID not match!")
		return nil, errors.New("Client ID not match!")
	}
	return tokenInfo, nil
}

func GoogleLogin(w http.ResponseWriter, r *http.Request) {
	req := &models.GoogleToken{}
	err := json.NewDecoder(r.Body).Decode(req)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	tokenInfo, err := verifyIdToken(req.IDToken)

	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid ID token"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	if _, err := req.GetUserClaims(); err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid ID token"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	resp := FindOrCreate(tokenInfo.Email, req.GivenName, req.Surname, "")

	if !resp["status"].(bool) {
		w.WriteHeader(http.StatusUnauthorized)
		json.NewEncoder(w).Encode(models.Response{
			Error: &models.Error{
				ErrorCode:    resp["error_code"].(string),
				ErrorMessage: resp["error_message"].(string),
			},
			Status: false,
		})
		return
	}

	json.NewEncoder(w).Encode(resp)
}

func AppleLogin(w http.ResponseWriter, r *http.Request) {
	req := models.AppleTokenRequest{}
	err := json.NewDecoder(r.Body).Decode(&req)
	if err != nil {
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(map[string]any{"status": false, "error_message": "Invalid body request"})
		return
	}

	tokenResp := models.AppleTokenResponse{}
	if err := appleClient.VerifyAuthCode(req, &tokenResp); err != nil || tokenResp.Error != "" {
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(map[string]any{"status": false, "error_message": "Authorize code is invalid or expired"})
		return
	}

	fmt.Println("Apple Response")
	fmt.Println(utils.StructToJSON(tokenResp))
	fmt.Println("Apple Response - End")

	claims, err := tokenResp.GetUserClaims()
	if err != nil {
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(map[string]any{"status": false, "error_message": "The OAuth token is invalid or expired"})
		return
	}

	userResp := FindOrCreate(claims["email"].(string), req.GivenName, req.Surname, tokenResp.AccessToken)

	if !userResp["status"].(bool) {
		w.WriteHeader(http.StatusUnauthorized)
		json.NewEncoder(w).Encode(models.Response{
			Error: &models.Error{
				ErrorCode:    userResp["error_code"].(string),
				ErrorMessage: userResp["error_message"].(string),
			},
			Status: false,
		})
		return
	}

	json.NewEncoder(w).Encode(userResp)
}

func LinkedinLogin(w http.ResponseWriter, r *http.Request) {
	req := models.LinkedinTokenRequest{}
	err := json.NewDecoder(r.Body).Decode(&req)
	if err != nil {
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(map[string]any{"status": false, "error_message": "Invalid body request"})
		return
	}

	tokenResp := models.LinkedinTokenResponse{}
	if err := linkedinClient.VerifyAuthCode(req, &tokenResp); err != nil || tokenResp.Error != "" {
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(map[string]any{"status": false, "error_message": "Authorize code is invalid or expired"})
		return
	}

	claims, err := linkedinClient.GetUserClaims(tokenResp.AccessToken)
	if err != nil {
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(map[string]any{"status": false, "error_message": "The OAuth token is invalid or expired"})
		return
	}

	userResp := FindOrCreate(claims["email"].(string), claims["given_name"].(string), claims["surname"].(string), "")

	if !userResp["status"].(bool) {
		w.WriteHeader(http.StatusUnauthorized)
		json.NewEncoder(w).Encode(models.Response{
			Error: &models.Error{
				ErrorCode:    userResp["error_code"].(string),
				ErrorMessage: userResp["error_message"].(string),
			},
			Status: false,
		})
		return
	}

	json.NewEncoder(w).Encode(userResp)
}

func OktaLogin(w http.ResponseWriter, r *http.Request) {
	w.Header().Add("Cache-Control", "no-cache")
	nonce, _ = GenerateNonce()
	var redirectPath string

	q := r.URL.Query()
	q.Add("client_id", OKTA_CLIENT_ID)
	q.Add("response_type", "code")
	q.Add("response_mode", "query")
	q.Add("scope", "openid profile email")
	q.Add("redirect_uri", OKTA_REDIRECT_URI)
	q.Add("state", state)
	q.Add("nonce", nonce)
	redirectPath = OKTA_URL + "/v1/authorize?" + q.Encode()

	json.NewEncoder(w).Encode(redirectPath)
}

func ParseJsonString(str []byte) map[string]any {
	m := make(map[string]any)
	err := json.Unmarshal(str, &m)
	if err != nil {
		log.Fatal(err)
	}

	return m
}
