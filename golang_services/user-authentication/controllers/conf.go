package controllers

import (
	"net/http"
	"os"
	"sync"

	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/utils"
	"github.com/jinzhu/gorm"
	"golang.org/x/oauth2"
)

var (
	Db                                         *gorm.DB
	secret, jwt_secret                         string
	user_service_var                           string
	secret_json                                []byte
	secret_map, db_map, s3_map, apple_map      map[string]any
	db_json                                    []byte
	CLIENT_ID, SECOND_CLIENT_ID, IOS_CLIENT_ID string
	OKTA_CLIENT_ID, OKTA_CLIENT_SECRET         string
	OKTA_URL, OKTA_REDIRECT_URI                string
	httpClient                                 = &http.Client{}
	appleClient                                *models.AppleClient
	linkedinClient                             *models.LinkedinClient

	// Scopes: OAuth 2.0 scopes provide a way to limit the amount of access that is granted to an access token.
	googleOauthConfig *oauth2.Config
)

var (
	adb  *db.AuroraDB
	once sync.Once
)

func init() {
	once.Do(func() {
		adb = utils.NewAuroraDb()
		Db = utils.ConnectDB()
		secret = os.Getenv("ad_secrets")
		secret_json = []byte(secret)
		secret_map = utils.ParseJsonString(secret_json)
		jwt_secret = secret_map["jwt"].(string)
		user_service_var = os.Getenv("ad_user_account_service")

		db_json = []byte(user_service_var)
		db_map = ParseJsonString(db_json)
		s3_map = ParseJsonString([]byte(os.Getenv("ad_s3")))
		apple_map = ParseJsonString([]byte(os.Getenv("ad_apple")))

		CLIENT_ID, SECOND_CLIENT_ID, IOS_CLIENT_ID =
			db_map["googlecid"].(string),
			db_map["second_clientid"].(string),
			db_map["ios_clientid"].(string)

		OKTA_CLIENT_ID = db_map["okta_cid"].(string)
		OKTA_CLIENT_SECRET = db_map["okta_csecret"].(string)
		OKTA_URL = db_map["okta_app_url"].(string)
		OKTA_REDIRECT_URI = "https://" + db_map["host_name"].(string) + "/okta/authorization-code/callback"

		httpClient = &http.Client{}
		appleClient = models.NewAppleClient(
			apple_map["apple_oauth_url"].(string),
			apple_map["apple_client_id"].(string),
			apple_map["apple_team_id"].(string),
			apple_map["apple_key_id"].(string),
			apple_map["apple_private_key"].(string),
		)
		linkedinClient = models.NewLinkedinClient(
			db_map["linkedin_request_uri"].(string),
			db_map["linkedin_client_id"].(string),
			db_map["linkedin_client_secret"].(string),
		)
	})
}
