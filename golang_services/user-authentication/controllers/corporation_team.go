package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/errors"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/utils"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/gorilla/mux"
	"github.com/gorilla/schema"
	uuid "github.com/satori/go.uuid"
	"github.com/thoas/go-funk"
)

func OrgGetCorporationTeamList(w http.ResponseWriter, r *http.Request) {
	currentUserID := r.Context().Value("id").(uuid.UUID)
	var currentUser models.User
	if err := Db.Where("id = ?", currentUserID.String()).First(&currentUser).Error; err != nil {
		handleError(w, err)
		return
	}

	if err := r.ParseForm(); err != nil {
		handleError(w, err)
		return
	}

	filter := new(models.CorporationTeamFilter)
	filter.Limit = 10
	schema.NewDecoder().Decode(filter, r.Form)

	resp := models.CorporationTeamData{
		Success: true,
		Data:    []models.CorporationTeamDetail{},
	}

	query := Db.Table("corporation_teams").
		Select(`*,
		(SELECT COUNT(*) FROM corporation_team_members WHERE team_id = corporation_teams.id AND role = ?) total_member,
		(SELECT COUNT(*) FROM corporation_team_members WHERE team_id = corporation_teams.id AND role = ?) total_manager`,
			models.MemberRole.Member, models.MemberRole.Manager).
		Where("deleted_at IS NULL").
		Where("org_id = ?", currentUser.OrganizationID)

	if filter.Query != "" {
		query = query.Where("name ILIKE ?", "%"+filter.Query+"%")
	}
	if strings.ToLower(filter.SortOrder) == "desc" {
		filter.SortOrder = "DESC"
	} else {
		filter.SortOrder = "ASC"
	}

	if funk.Contains([]string{"name", "total_member", "total_manager"}, filter.SortField) {
		query = query.Order(fmt.Sprintf("%s %s", filter.SortField, filter.SortOrder))
	} else {
		query = query.Order("name ASC") // Default sort
	}

	// Get pagination data and total
	if err := query.Limit(filter.Limit).Offset(filter.Offset).Find(&resp.Data).Error; err != nil {
		handleError(w, err)
		return
	}
	if err := query.Limit(1).Offset(0).Count(&resp.Total).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(resp)
}

func OrgCreateCorporationTeamWithMember(w http.ResponseWriter, r *http.Request) {
	var currentUser models.User
	currentUserID := r.Context().Value("id").(uuid.UUID)

	if err := Db.Where("id = ?", currentUserID.String()).First(&currentUser).Error; err != nil {
		handleError(w, err)
		return
	}

	team := new(models.CorporationTeamDetail)
	json.NewDecoder(r.Body).Decode(team)
	team.CorporationTeam.OrgID = currentUser.OrganizationID

	if err := Db.Create(&team.CorporationTeam).Error; err != nil {
		handleError(w, err)
		return
	}

	class, err := classifyMembersIntoUserGroups(currentUser.OrganizationID, team.CorporationTeam.ID, team.Members)
	if err != nil {
		handleError(w, err)
		return
	}
	// Directly add members to team
	for _, member := range class.UsersCanAddToTeam {
		var user models.User
		if err := Db.Where("email = ?", member.Email).First(&user).Error; err != nil {
			handleError(w, err)
			return
		}
		if err := Db.Create(&models.CorporationTeamMember{
			TeamID:          team.ID,
			UserID:          user.ID.String(),
			Role:            models.MemberRole.Member,
			RequireApproval: aws.Bool(true),
			Status:          models.MemberStatus.Active,
		}).Error; err != nil {
			handleError(w, err)
			return
		}
	}

	// Add member to team and send email pending
	for _, member := range class.UsersNotInSystem {
		user := models.User{
			Email:         member.Email,
			GivenName:     "",
			Surname:       "",
			EmailVerified: true,
			Status:        models.UserStatus.Pending,
			// OrganizationID: null.IntFrom(0),
			Password: utils.RandomPassword(),
		}
		user.OrganizationID = currentUser.OrganizationID
		user.PasswordInit = user.Password
		if err := Db.Create(&user).Error; err != nil {
			handleError(w, err)
			return
		}

		if err := Db.Create(&models.CorporationTeamMember{
			TeamID:          team.ID,
			UserID:          user.ID.String(),
			Role:            models.MemberRole.Member,
			RequireApproval: aws.Bool(true),
			Status:          models.MemberStatus.Pending,
		}).Error; err != nil {
			handleError(w, err)
			return
		}

		go SendEmailInvitedMember(&currentUser, &user, false)
	}

	team.TotalMember = len(class.UsersCanAddToTeam) + len(class.UsersNotInSystem)

	json.NewEncoder(w).Encode(team)
}

func OrgGetCorporationTeamByID(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)

	currentUserID := r.Context().Value("id").(uuid.UUID)
	var currentUser models.User
	if err := Db.Where("id = ?", currentUserID.String()).First(&currentUser).Error; err != nil {
		handleError(w, err)
		return
	}

	team := new(models.CorporationTeam)
	if err := Db.Where("id = ? AND org_id = ?", params["team_id"], currentUser.OrganizationID).First(&team).Error; err != nil {
		handleError(w, err)
		return
	}

	if team.ID <= 0 {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{
			"success": false,
			"error":   errors.NotFoundErr,
		})
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    team,
	})
}

func OrgUpdateCorporationTeam(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)

	team := new(models.CorporationTeam)
	if err := Db.Where("id = ?", params["team_id"]).First(&team).Error; err != nil {
		handleError(w, err)
		return
	}

	req := new(models.CorporationTeam)
	json.NewDecoder(r.Body).Decode(req)
	if req.Name != "" {
		team.Name = req.Name
	}
	if req.Description != "" {
		team.Description = req.Description
	}

	if err := Db.Save(&team).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    team,
	})
}

func OrgDeleteCorporationTeam(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)

	team := new(models.CorporationTeam)
	if err := Db.Where("id = ?", params["team_id"]).First(&team).Error; err != nil {
		handleError(w, err)
		return
	}

	team.DeletedAt = aws.Time(time.Now())

	if err := Db.Save(&team).Error; err != nil {
		handleError(w, err)
		return
	}

	if err := Db.Exec("DELETE FROM corporation_team_members WHERE team_id = ?", team.ID).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    team,
	})
}

func OrgDeleteCorporationMultiTeam(w http.ResponseWriter, r *http.Request) {
	req := []int{}
	json.NewDecoder(r.Body).Decode(&req)

	if err := Db.Exec("UPDATE corporation_teams SET deleted_at = NOW() WHERE id IN (?)", req).Error; err != nil {
		handleError(w, err)
		return
	}
	if err := Db.Exec("DELETE FROM corporation_team_members WHERE team_id IN (?)", req).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
	})
}

func handleError(w http.ResponseWriter, err error) {
	w.WriteHeader(http.StatusBadRequest)
	json.NewEncoder(w).Encode(map[string]any{
		"status": false,
		"error":  err.Error(),
	})
}
