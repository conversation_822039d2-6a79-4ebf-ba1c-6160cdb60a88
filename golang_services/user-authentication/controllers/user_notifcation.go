package controllers

import (
	"encoding/json"
	"net/http"
	"regexp"
	"strings"
	"time"

	adAWS "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/gorilla/mux"
	"github.com/gorilla/schema"
)

func GetUserNotificationList(w http.ResponseWriter, r *http.Request) {
	if err := r.ParseForm(); err != nil {
		return
	}

	filter := new(models.UserNotificationFilter)
	filter.Limit = 10
	schema.NewDecoder().Decode(filter, r.Form)

	resp := models.UserNotificationData{
		Success: true,
		Data:    []models.UserNotification{},
		Offset:  filter.Offset,
		Limit:   filter.Limit,
	}

	query := Db.Model(&models.UserNotification{}).Where("user_id = ?", mux.Vars(r)["id"]).
		Order("sent_at DESC")

	columns := []string{"id", "type", "title", "language_code", "content_truncate", "sent_at", "read_at", "user_id", "created_at", "updated_at", "deleted_at"}
	if filter.IncludeContent {
		columns = append(columns, "content")
	}

	query = query.Select(columns)

	// Get pagination data and total
	query.Limit(filter.Limit).Offset(filter.Offset).Find(&resp.Data)
	query.Limit(1).Offset(0).Count(&resp.Total)

	// Get read and unread count
	query.Where("read_at IS NULL").Limit(1).Count(&resp.TotalUnread)
	resp.TotalRead = resp.Total - resp.TotalUnread

	json.NewEncoder(w).Encode(resp)
}

func GetUserNotification(w http.ResponseWriter, r *http.Request) {
	notification := models.UserNotification{}

	query := Db.Model(&models.UserNotification{}).
		Where("user_id = ?", mux.Vars(r)["id"]).
		Where("id = ?", mux.Vars(r)["notification_id"])

	query.First(&notification)

	urlRegex := regexp.MustCompile(`href=[\'"]?([^\'" >]+)`)
	urls := urlRegex.FindAllStringSubmatch(notification.Content, -1)

	sess, err := session.NewSession(aws.NewConfig().WithRegion(region))
	if err != nil {
		json.NewEncoder(w).Encode(map[string]any{
			"success": false,
			"error":   err,
		})
		return
	}
	presigner := adAWS.NewS3Svc(sess)

	for _, url := range urls {
		if bucket, key, err := utils.UrlToS3BucketAndKey(url[1]); err == nil {
			if presigned, err := presigner.PresignUrl(bucket, key, 24*time.Hour); err == nil {
				notification.Content = strings.ReplaceAll(notification.Content, url[1], presigned)
			}
		}
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    notification,
	})
}

func ReadUserNotification(w http.ResponseWriter, r *http.Request) {
	notification := models.UserNotification{}

	Db.Model(&models.UserNotification{}).
		Where("user_id = ?", mux.Vars(r)["id"]).
		Where("id = ?", mux.Vars(r)["notification_id"]).First(&notification)

	notification.ReadAt = aws.Time(time.Now())
	notification.UpdatedAt = aws.Time(time.Now())

	Db.Save(&notification)
	Db.First(&notification, "id = ?", notification.ID)

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    notification,
	})
}

func UnreadUserNotification(w http.ResponseWriter, r *http.Request) {
	notification := models.UserNotification{}

	Db.Model(&models.UserNotification{}).
		Where("user_id = ?", mux.Vars(r)["id"]).
		Where("id = ?", mux.Vars(r)["notification_id"]).First(&notification)

	notification.ReadAt = nil
	notification.UpdatedAt = aws.Time(time.Now())

	Db.Save(&notification)
	Db.First(&notification, "id = ?", notification.ID)

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    notification,
	})
}

func DeleteUserNotification(w http.ResponseWriter, r *http.Request) {
	notification := models.UserNotification{}

	Db.Model(&models.UserNotification{}).
		Where("user_id = ?", mux.Vars(r)["id"]).
		Where("id = ?", mux.Vars(r)["notification_id"]).First(&notification)

	notification.DeletedAt = aws.Time(time.Now())
	notification.UpdatedAt = aws.Time(time.Now())

	Db.Save(&notification)
	Db.First(&notification, "id = ?", notification.ID)

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    notification,
	})
}

func DeleteAllUserNotification(w http.ResponseWriter, r *http.Request) {
	if err := Db.Exec(`UPDATE user_notifications SET deleted_at = ?, updated_at = ? WHERE user_id = ?`, time.Now(), time.Now(), mux.Vars(r)["id"]).Error; err != nil {
		json.NewEncoder(w).Encode(map[string]any{
			"success": false,
			"error":   err,
		})
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
	})
}
