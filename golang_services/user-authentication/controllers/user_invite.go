package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"github.com/dgrijalva/jwt-go"
)

func ValidateOTP(w http.ResponseWriter, r *http.Request) {
	var req struct {
		Email string `json:"email"`
		Code  string `json:"code"`
	}

	err := json.NewDecoder(r.Body).Decode(&req)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "message": "Invalid request"})
		return
	}

	req.Email = strings.ToLower(req.Email)
	invite := models.UserInvite{}
	Db.Where("otp = ? AND email = ?", req.Code, req.Email).First(&invite)
	user := &models.User{}
	Db.Where("email = ?", req.Email).First(user)

	if invite.IsUsed {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "error_code": "ERR00020", "message": "otp is invalid or expired"})
		return
	}

	if invite.ExpiredAt.Before(time.Now()) {
		if !invite.ExpiredNotification {
			var (
				inviter models.User
				org     models.ViewOrganization
			)
			Db.Where("id = ?", invite.InvitedBy).First(&inviter)
			Db.Table("v_organizations").Where("id = ?", invite.OrgID).First(&org)
			SendMessageToSQS("send_expired_notification_to_corp_admin", inviter.Email, map[string]any{
				"AdminName": inviter.GivenName,
				"CorpName":  org.Name,
				"Email":     user.Email,
				"Name":      user.GivenName + " " + user.Surname,
			})
			invite.ExpiredNotification = true
			Db.Save(&invite)
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "error_code": "ERR00021", "message": "otp is not used and expired"})
		return
	}

	expiresAt := time.Now().Add(TOKEN_TIMELIFE).Unix()
	tk := &models.Token{
		UserID: user.ID,
		Role:   user.Role,
		StandardClaims: &jwt.StandardClaims{
			ExpiresAt: expiresAt,
		},
	}

	token := jwt.NewWithClaims(jwt.GetSigningMethod("HS256"), tk)

	tokenString, error := token.SignedString([]byte(jwt_secret))
	if error != nil {
		fmt.Println(error)
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"token":   tokenString,
	})
}
