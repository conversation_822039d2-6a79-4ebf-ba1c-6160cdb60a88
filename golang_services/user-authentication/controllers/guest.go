package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/dgrijalva/jwt-go"
	"github.com/jinzhu/gorm"
)

func GuestLogin(w http.ResponseWriter, r *http.Request) {
	user := &models.User{}
	err := json.NewDecoder(r.Body).Decode(user)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}
	if user.Email == "" {
		user.Email = fmt.Sprintf("<EMAIL>", time.Now().Unix())
	}
	resp := FindOrCreateGuestAccount(user.Email)
	if resp["status"] == false {
		w.WriteHeader(401)
		response := models.Response{
			Error: &models.Error{
				ErrorCode:    resp["error_code"].(string),
				ErrorMessage: resp["error_message"].(string),
				ErrorData:    resp["error_data"],
			},
			Status: false,
		}
		json.NewEncoder(w).Encode(response)
		return
	}
	json.NewEncoder(w).Encode(resp)
}

func FindOrCreateGuestAccount(email string) map[string]any {
	user := &models.User{}

	if err := Db.Find(user, "lower(email) = ?", strings.ToLower(email)).Error; err == gorm.ErrRecordNotFound {
		user = &models.User{
			Username:         email,
			Email:            email,
			EmailVerified:    false,
			OrganizationID:   0,
			Password:         encryptPassword("AriaDirect " + secret),
			Status:           models.UserStatus.InActive,
			Role:             GUEST,
			LastLogonAt:      aws.Time(time.Now()),
			FailedLoginCount: 0,
		}

		if err := Db.Create(user).Error; err != nil {
			return map[string]any{"error_message": err.Error, "status": false}
		}
	} else if err != nil {
		return map[string]any{"error_message": err.Error, "status": false}
	}

	if err := Db.Save(user).Error; err != nil {
		return map[string]any{"error_message": err.Error, "status": false}
	}

	tk := &models.Token{
		UserID: user.ID,
		Role:   user.Role,
		StandardClaims: &jwt.StandardClaims{
			Subject:   "token",
			ExpiresAt: time.Now().Add(TOKEN_TIMELIFE).Unix(),
		},
	}

	token := jwt.NewWithClaims(jwt.GetSigningMethod("HS256"), tk)

	tokenString, err := token.SignedString([]byte(jwt_secret))

	if err != nil {
		return map[string]any{"error_message": err.Error, "status": false}
	}

	refreshToken := &models.Token{
		UserID: user.ID,
		Role:   user.Role,
		StandardClaims: &jwt.StandardClaims{
			Subject:   "refresh_token",
			ExpiresAt: time.Now().Add(REFRESH_TOKEN_TIMELIFE).Unix(),
		},
	}

	refreshTokenString, err := jwt.NewWithClaims(jwt.GetSigningMethod("HS256"), refreshToken).SignedString([]byte(jwt_secret))
	if err != nil {
		return map[string]any{"error_message": err.Error, "status": false}
	}
	var resp = map[string]any{"status": true, "message": "logged in"}
	resp["token"] = tokenString //Store the token in the response
	resp["token_expired_at"] = time.Now().Add(TOKEN_TIMELIFE)
	resp["refresh_token"] = refreshTokenString
	resp["id"] = user.ID
	resp["role"] = user.Role
	resp["organization_id"] = user.OrganizationID

	return resp
}
