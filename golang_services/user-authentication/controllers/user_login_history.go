package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/aws/aws-sdk-go/aws"
	ua "github.com/mileusna/useragent"
	"github.com/thoas/go-funk"
)

func TraceUserLogin(w http.ResponseWriter, r *http.Request) {
	history := &models.UserLoginHistory{}
	err := json.NewDecoder(r.Body).Decode(history)
	if err != nil {
		handleError(w, err)
		return
	}

	var user models.User
	if err := Db.Where("email = ?", history.UserEmail).First(&user).Error; err != nil {
		handleError(w, err)
		return
	}

	if history.UserAgent != "" {
		ua := ua.Parse(history.UserAgent)
		history.UserAgentName = ua.Name
		history.UserAgentDevice = ua.Device
		history.UserAgentOS = ua.OS
	}

	if !funk.ContainsString([]string{
		models.UserLoginStatus.Login,
		models.UserLoginStatus.Logoff,
		models.UserLoginStatus.WrongPassword,
		models.UserLoginStatus.Block,
	}, history.Status) {
		handleError(w, fmt.Errorf("Wrong status"))
		return
	}

	if history.Status == models.UserLoginStatus.Login {
		history.LogonAt = aws.Time(time.Now())
	}
	if history.Status == models.UserLoginStatus.Logoff {
		history.LogoffAt = aws.Time(time.Now())
	}

	if !funk.ContainsString([]string{
		models.UserLoginMethod.Password,
		models.UserLoginMethod.Google,
		models.UserLoginMethod.Apple,
		models.UserLoginMethod.Linkedin,
	}, history.LoginMethod) {
		handleError(w, fmt.Errorf("Wrong method"))
		return
	}
	Db.Save(&history)

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    history,
	})
}
