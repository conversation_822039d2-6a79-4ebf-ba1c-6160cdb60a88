package controllers

import (
	"bytes"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"

	generalmodel "bitbucket.org/persistence17/aria/golang_services/models"

	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"github.com/asaskevich/govalidator"
	"github.com/gorilla/mux"
	"github.com/gorilla/schema"
	"github.com/jinzhu/gorm"
	uuid "github.com/satori/go.uuid"
	"github.com/ulule/deepcopier"
)

const (
	// user role type
	ADMIN    = "admin"
	AD_ADMIN = "ad_admin"
	USER     = "user"
	GUEST    = "guest"
)

// ad-admin: list all consulates
func ListAllConsulates(w http.ResponseWriter, r *http.Request) {
	var consulates []models.Consulate
	Db.Order("name ASC").Find(&consulates)

	var consulatesWithInfo []models.ConsulateInfoWithOrderInfo
	for i := 0; i < len(consulates); i++ {
		consulateWithInfo := &models.ConsulateInfoWithOrderInfo{}
		deepcopier.Copy(consulates[i]).To(consulateWithInfo)
		consulatesWithInfo = append(consulatesWithInfo, *consulateWithInfo)
	}

	var resp models.ConsulateInfoPagination
	resp.Data = consulatesWithInfo
	resp.TotalCount = len(consulates)
	resp.Success = true
	json.NewEncoder(w).Encode(resp)
}

// ad-admin: list all providers
func ListAllProviders(w http.ResponseWriter, r *http.Request) {
	var providers []models.EtsProvider
	Db.Order("name ASC").Find(&providers)

	json.NewEncoder(w).Encode(map[string]any{
		"data":        providers,
		"total_count": len(providers),
		"success":     true,
	})
}

// List all users for a corporation
func ListUsers(w http.ResponseWriter, r *http.Request) {
	currentUserID := r.Context().Value("id").(uuid.UUID)
	var currentUser models.User
	if err := Db.Where("id = ?", currentUserID.String()).First(&currentUser).Error; err != nil {
		handleError(w, err)
		return
	}

	result := ListUsersByOrgID(currentUser.OrganizationID, r.URL.Query())
	json.NewEncoder(w).Encode(result)
}

// List all users under org
func ListUsersUnderOrg(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var org_id = params["org_id"]
	org, _ := strconv.ParseInt(org_id, 10, 64)

	uid, ok := r.Context().Value("id").(uuid.UUID)
	if !ok {
		fmt.Println("Error transform user id")
	}

	// check if this user's request org id is same as user's org id
	var user models.User
	Db.Where("id = ?", uid.String()).First(&user)
	if user.OrganizationID != int64(org) && user.Role != AD_ADMIN {
		var resp = map[string]any{"status": false, "message": "User is not belong to request organization"}
		w.WriteHeader(http.StatusUnauthorized)
		json.NewEncoder(w).Encode(resp)
		return
	}

	result := ListUsersByOrgID(org, r.URL.Query())

	json.NewEncoder(w).Encode(result)
}

type UserListResponse struct {
	Data    []models.UserInfo `json:"data"`
	Total   int               `json:"total"`
	Limit   int               `json:"limit"`
	Offset  int               `json:"offset"`
	Success bool              `json:"success"`
}

// List users by org id
func ListUsersByOrgID(org_id int64, q map[string][]string) *UserListResponse {
	query := Db.Select(`u.*,COALESCE(t.phone,'') phone_number,
	cd.name department,cc.name cost_center_number,CONCAT_WS(' ',u_m.given_name,u_m.surname) manager_name,t.job_title,
	t.residency region_of_residence, uc.approved_countries, 
	(SELECT jsonb_object_agg(sos.order_id, jsonb_build_object('service_date', vst.service_date, 'service_date_interval',jsonb_build_object('from',to_char(vst.service_date::timestamp - INTERVAL '1 hour', 'YYYY-MM-DD"T"HH24:MI:SSZ'), 'to', to_char(vst.service_date::timestamp + INTERVAL '1 hour', 'YYYY-MM-DD"T"HH24:MI:SSZ'))))
		FROM service_order_staffs sos 
		LEFT JOIN v_service_tasks vst ON vst.order_id = sos.order_id 
		WHERE sos.staff_id = u.id AND staff_confirm_status != 'rejected'
	) staff_orders`,
	).Table("users u").
		Joins("LEFT JOIN traveler_type_index ti ON ti.user_id = u.id AND ti.profile_type = ?", "my_profile").
		Joins("LEFT JOIN traveler t ON t.id = ti.traveler_id").
		Joins("LEFT JOIN user_corp_info uc ON uc.user_id = u.id").
		Joins("LEFT JOIN corporation_departments cd ON cd.id = uc.department_id").
		Joins("LEFT JOIN corporation_cost_centers cc ON cc.id = uc.cost_center_id").
		Joins("LEFT JOIN users u_m ON u_m.id = uc.manager_id").
		Where("u.organization_id = ?", org_id)

	sortOrder := "DESC"
	if val, ok := q["sort_order"]; ok && strings.ToUpper(val[0]) == "ASC" {
		sortOrder = "ASC"
	}

	sortField := "created_at"
	if val, ok := q["sort_field"]; ok {
		switch val[0] {
		case "name":
			sortField = "CONCAT_WS(' ',u.given_name,u.surname)"
		case "role":
			sortField = "role"
		case "region_of_residence":
			sortField = "region_of_residence"
		case "phone_number":
			sortField = "phone_number"
		case "department":
			sortField = "cd.name"
		case "cost_center_number":
			sortField = "cc.name"
		case "manager_name":
			sortField = "CONCAT_WS(' ',u_m.given_name,u_m.surname)"
		case "job_title":
			sortField = "job_title"
		}
	}

	query = query.Order(fmt.Sprintf("%s %s NULLS LAST", sortField, sortOrder))

	if val, ok := q["status"]; ok {
		query = query.Where("u.status IN (?)", val)
	}

	if val, ok := q["role"]; ok {
		query = query.Where("u.role IN (?)", val)
	}

	if val, ok := q["manager_id"]; ok {
		query = query.Where("uc.manager_id IN (?)", val)
	}

	// Query equal, not equal, like and not like by email
	if values, ok := q["email[eq]"]; ok {
		for _, val := range values {
			query = query.Where("u.email = ?", val)
		}
	}
	if values, ok := q["email[neq]"]; ok {
		for _, val := range values {
			query = query.Where("u.email != ?", val)
		}
	}

	if values, ok := q["email[like]"]; ok {
		for _, val := range values {
			query = query.Where("u.email LIKE ?", "%"+val+"%")
		}
	}
	if values, ok := q["email[nlike]"]; ok {
		for _, val := range values {
			query = query.Where("u.email NOT LIKE ?", "%"+val+"%")
		}
	}

	// Query equal, not equal, like and not like by name
	if values, ok := q["name[eq]"]; ok {
		for _, val := range values {
			query = query.Where("LOWER(CONCAT_WS(' ',u.given_name,u.surname)) = ?", strings.ToLower(val))
		}
	}
	if values, ok := q["name[neq]"]; ok {
		for _, val := range values {
			query = query.Where("LOWER(CONCAT_WS(' ',u.given_name,u.surname)) != ?", strings.ToLower(val))
		}
	}
	if values, ok := q["name[like]"]; ok {
		for _, val := range values {
			query = query.Where("LOWER(CONCAT_WS(' ',u.given_name,u.surname)) ILIKE ?", strings.ToLower("%"+val+"%"))
		}
	}
	if values, ok := q["name[nlike]"]; ok {
		for _, val := range values {
			query = query.Where("LOWER(CONCAT_WS(' ',u.given_name,u.surname)) NOT ILIKE ?", strings.ToLower("%"+val+"%"))
		}
	}

	// General query
	if values, ok := q["query"]; ok && len(values) > 0 {
		val := strings.ToLower("%" + values[0] + "%")
		query = query.Where(`
		(
			LOWER(CONCAT_WS(' ',u.given_name,u.surname)) ILIKE ? 
			OR u.email ILIKE ?
			OR cd.name ILIKE ?
			OR cc.name ILIKE ?
			OR CONCAT_WS(' ',u_m.given_name,u_m.surname) ILIKE ?
			OR t.job_title ILIKE ?
		)`, val, val, val, val, val, val)
	}

	result := &UserListResponse{
		Data:    []models.UserInfo{},
		Success: true,
	}

	query.Count(&result.Total)

	// default to limit 10, offset 0
	if val, ok := q["limit"]; ok {
		result.Limit, _ = strconv.Atoi(val[0])
	}
	query = query.Limit(result.Limit)

	if val, ok := q["offset"]; ok {
		result.Offset, _ = strconv.Atoi(val[0])
	}

	query = query.Offset(result.Offset)
	query.Find(&result.Data)

	var invites []models.UserInvite
	Db.Where("is_used = false AND expired_at < NOW()").Find(&invites) // Get expired invites

	userTypes, _ := GetUserTypeByOrgID(org_id)
	for i := range result.Data {
		if funk.Some(funk.Map(invites, func(u models.UserInvite) string {
			return u.UserID
		}), result.Data[i].ID.String()) {
			result.Data[i].OTPExpired = true
		}

		if val, ok := q["service_date"]; ok && len(val) > 0 {
			serviceDate, _ := time.Parse(val[0], time.RFC3339)
			gjson.ParseBytes(result.Data[i].StaffOrders).ForEach(func(key, value gjson.Result) bool {
				if value.Get("service_date_interval.from").Time().Unix() >= serviceDate.Unix() && serviceDate.Unix() <= value.Get("service_date_interval.to").Time().Unix() {
					result.Data[i].StaffInSchedule = true
				}
				return true
			})
		}
		result.Data[i].UserTypes = userTypes

	}
	return result
}

type getUserRequest struct {
	Status    string `schema:"status"`
	OrgID     int64  `schema:"org_id"`
	UserType  string `schema:"user_type"`
	SortField string `schema:"sort_field"`
	SortOrder string `schema:"sort_order"`
	Search    string
	Offset    uint64
	Limit     uint64
}

// List all users - only for ad_admin
func ListAllUsersV2(w http.ResponseWriter, r *http.Request) {
	if err := r.ParseForm(); err != nil {
		handleError(w, err)
		return
	}

	filter := new(getUserRequest)
	schema.NewDecoder().Decode(filter, r.Form)

	// Set default
	if filter.Limit <= 0 {
		filter.Limit = 10
	}

	if strings.ToUpper(filter.SortOrder) != "ASC" {
		filter.SortOrder = "DESC"
	}

	adminStatus := generalmodel.GetPackageStatusesForAdmin()

	query := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar).Select(`
		u.id, u.username, u.surname, u.given_name, CONCAT_WS(' ',u.given_name,u.surname) fullname, u.avatar, u."role", u.organization_id, 
		u.email, u.status, u.email_verified, u.created_at,u.updated_at,
		org_user_types(u.organization_id) user_types`).
		Column(fmt.Sprintf(`(SELECT COUNT(*) FROM package WHERE user_id = u.id AND deleted_at IS NULL AND status IN (%s)) submitted_order`,
			strings.Join(funk.Map(adminStatus, func(s string) string {
				return "'" + s + "'"
			}).([]string), ",")),
		).
		Column("(SELECT COUNT(*) FROM package WHERE user_id = u.id AND status = ? AND deleted_at IS NULL)", "open").
		Column("(SELECT MAX(created_at) FROM package WHERE user_id = u.id AND deleted_at IS NULL) last_order_time").
		Column(`
		CASE 
			WHEN corp.id IS NOT NULL THEN corp.code
			ELSE ''
		END code`).
		Column(`
		CASE 
			WHEN corp.id IS NOT NULL THEN corp.name
			WHEN con.id IS NOT NULL THEN con.name
			ELSE ''
		END org_name`).
		Column(`
		CASE 
			WHEN corp.id IS NOT NULL THEN COALESCE(corp.address->>'city','')
			WHEN con.id IS NOT NULL THEN COALESCE(con.address->>'city','')
			ELSE ''
		END corp_city`).
		Column(`
		CASE 
			WHEN corp.id IS NOT NULL THEN COALESCE(corp.address->>'country','')
			WHEN con.id IS NOT NULL THEN COALESCE(con.address->>'country','')
			ELSE ''
		END corp_country`).
		Column("to_jsonb(corp) corporation").
		Column("to_jsonb(con) consulate").
		Column("to_jsonb(etsp) ets_provider").
		From("users u").
		LeftJoin("organization o ON o.id = u.organization_id").
		LeftJoin("corporation corp ON corp.org_id = o.id").
		LeftJoin("consulate con ON con.org_id = o.id").
		LeftJoin("ets_provider etsp ON etsp.org_id = o.id")

	query = query.Where("u.role != ?", "guest") // Skip guest account

	if filter.Status != "" {
		query = query.Where("u.status = ?", filter.Status)
	}

	if filter.OrgID > 0 {
		query = query.Where("u.organization_id = ?", filter.OrgID)
	}

	if filter.UserType != "" {
		query = query.Where(`org_user_types(u.organization_id) @> ARRAY[?]`, filter.UserType)
	}
	if filter.Search != "" {
		textSearch := "%" + filter.Search + "%"
		query = query.Where("u.email ILIKE ? OR u.username ILIKE ? OR u.surname ILIKE ? OR u.given_name ILIKE ?",
			textSearch, textSearch, textSearch, textSearch)
	}

	if funk.Contains([]string{"user_type", "fullname", "email", "org_name", "corp_country", "submitted_order", "last_order_time"}, filter.SortField) {
		query = query.OrderBy(fmt.Sprintf("%s %s", filter.SortField, filter.SortOrder))
	} else {
		query = query.OrderBy("u.given_name DESC")
	}

	query = query.Limit(filter.Limit).Offset(filter.Offset)

	resp := models.UserInfoPagination{
		Data:   []models.UserInfoWithOrderSummary{},
		Limit:  filter.Limit,
		Offset: filter.Offset,
	}

	rows, err := query.RunWith(Db.DB()).Query()
	if err != nil {
		handleError(w, err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var item models.UserInfoWithOrderSummary
		if err := rows.Scan(
			&item.ID, &item.Username, &item.Surname, &item.GivenName, &item.Fullname, &item.Avatar, &item.Role, &item.OrganizationID,
			&item.Email, &item.Status, &item.EmailVerified, &item.CreatedAt, &item.UpdatedAt, &item.UserTypes,
			&item.SubmittedOrder, &item.OpenOrder, &item.LastOrderTime, &item.CorpCode, &item.OrgName,
			&item.CorpCity, &item.CorpCountry, &item.Corporation, &item.Consulate, &item.ETSProvider,
		); err != nil {
			handleError(w, err)
			return
		}

		resp.Data = append(resp.Data, item)
	}
	sqlStr, args, _ := query.ToSql()
	sqlCount := utils.QueryCountItemV2(sqlStr)

	if err := Db.DB().QueryRow(sqlCount, args...).Scan(&resp.TotalCount); err != nil {
		handleError(w, err)
		return
	}
	resp.Success = true
	json.NewEncoder(w).Encode(resp)
}

type inviteMemberRes struct {
	EmailInvited     []string         `json:"email_invited"`
	EmailNotInvited  []string         `json:"email_not_invited"`
	NotInvitedReason []map[string]any `json:"not_invited_reason"`
}

// invite member to org - org admin
func InviteMembers(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var org_id = params["org_id"]
	org, _ := strconv.Atoi(org_id)
	var requester_user_id = params["user_id"]

	users := []models.User{}
	err := json.NewDecoder(r.Body).Decode(&users)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"status": false, "message": "Invalid request"})
		return
	}

	result := inviteMemberRes{
		EmailInvited:     []string{},
		EmailNotInvited:  []string{},
		NotInvitedReason: []map[string]any{},
	}

	for i := 0; i < len(users); i++ {
		users[i].Email = strings.ToLower(users[i].Email)
		resp := InviteMember(&users[i], int64(org), requester_user_id)
		if resp["status"].(bool) {
			result.EmailInvited = append(result.EmailInvited, users[i].Email)
		} else {
			result.EmailNotInvited = append(result.EmailNotInvited, users[i].Email)
			result.NotInvitedReason = append(result.NotInvitedReason, resp)
		}
	}
	json.NewEncoder(w).Encode(result)
}

func InviteMember(user *models.User, org int64, requester_user_id string) map[string]any {
	// find if email exists
	invite_user := &models.User{}
	if err := Db.Where("Email = ?", user.Email).First(invite_user).Error; err == nil {
		// Resend email
		var resp map[string]any
		if invite_user.OrganizationID == org || invite_user.OrganizationID == 0 {
			requester := &models.User{}
			if err := Db.Where("Id = ?", requester_user_id).First(requester).Error; err != nil {
				var error = map[string]any{"status": false, "error_message": "Failed to send email"}
				return error
			}
			resp = SendEmailInvitedMember(requester, invite_user, true)
		} else {
			resp = map[string]any{
				"error_code":    "ERR00023",
				"error_message": fmt.Sprintf("This email belong to another org_id: %d", invite_user.OrganizationID),
				"status":        false,
			}
		}
		return resp
	}

	// check if organization has domain email striction
	organization := models.Organization{}
	Db.Where("Id = ?", org).First(&organization)
	domain := strings.Split(user.Email, "@")[1]
	corp := models.Corporation{}
	Db.Where("org_id = ?", organization.ID).First(&corp)
	if corp.DomainEmailOnly && domain != corp.Domain {
		resp := map[string]any{
			"error_code":    "ERR00019",
			"error_message": "Invited user does not have domain email.",
			"status":        false,
		}
		return resp
	}

	// create user
	user.OrganizationID = org
	if len(user.Role) == 0 {
		user.Role = USER
	}

	if user.Status == "" {
		user.Status = models.UserStatus.Pending
	}

	if user.StaffContact == nil {
		user.StaffContact = []byte("{}")
	}

	if user.TableViewConfig == nil {
		user.TableViewConfig = []byte("{}")
	}

	createdUser := Db.Create(user)
	var errMessage = createdUser.Error

	if createdUser.Error != nil {
		fmt.Println(errMessage)
		var resp = map[string]any{"status": false, "error_message": "Failed to create user"}
		return resp
	}

	//send email to invited user
	requester := &models.User{}
	if err := Db.Where("Id = ?", requester_user_id).First(requester).Error; err != nil {
		var resp = map[string]any{"status": false, "error_message": "Failed to send email"}
		return resp
	}
	resp := SendEmailInvitedMember(requester, user, false)
	if resp["status"] == false {
		var resp = map[string]any{"status": false, "error_message": "Failed to send email"}
		return resp
	}

	message := map[string]any{
		"data":   createdUser.Value,
		"status": true,
	}
	return message
}

func RestrictDomainEmailStatus(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var org_id = params["org_id"]

	corp := &models.Corporation{}
	Db.Where("org_id = ?", org_id).First(&corp)
	json.NewEncoder(w).Encode(&corp)
}

// Cancel invitation
func CancelInvitation(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var org_id = params["org_id"]
	org, _ := strconv.Atoi(org_id)

	users := []models.User{}
	err := json.NewDecoder(r.Body).Decode(&users)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	var result []map[string]any
	for i := 0; i < len(users); i++ {
		resp := map[string]any{"success": false, "message": ""}
		cancel_user := &models.User{}
		Db.Where("email = ?", users[i].Email).First(&cancel_user)

		if cancel_user.EmailVerified {
			resp["message"] = "User has accepted the invitation, can not cancel invitation."
		} else if cancel_user.OrganizationID != int64(org) {
			resp["message"] = "User does not belong to organization."
		} else {
			Db.Delete(&cancel_user)
			resp["success"] = true
			resp["message"] = "Cancelled invitation"
		}
		result = append(result, resp)
	}
	json.NewEncoder(w).Encode(result)
}

// UpdateUserUnderOrg update user under org
func UpdateUserUnderOrg(w http.ResponseWriter, r *http.Request) {
	if err := updateUserUnderOrg(w, r); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "message": err.Error()})
		return
	}

	json.NewEncoder(w).Encode(map[string]any{"success": true, "message": "success"})
}

func updateUserUnderOrg(w http.ResponseWriter, r *http.Request) error {
	params := mux.Vars(r)
	var org_id = params["org_id"]
	org, _ := strconv.Atoi(org_id)

	uid, ok := r.Context().Value("id").(uuid.UUID)
	if !ok {
		return fmt.Errorf("Error transform user id")
	}

	// check if this user's request org id is same as user's org id
	var (
		admin, user models.User
	)

	Db.Where("id = ?", uid.String()).First(&admin)
	if admin.OrganizationID != int64(org) && admin.Role != AD_ADMIN {
		return fmt.Errorf("User is not belong to request organization")
	}

	if err := json.NewDecoder(r.Body).Decode(&user); err != nil {
		return fmt.Errorf("Invalid request")
	}

	if user.ID.String() == "" || user.Role == "" {
		return fmt.Errorf("Invalid input")
	}

	update := map[string]any{}
	if user.Role != "" {
		if !utils.Contain(user.Role, []string{"admin", "user"}) {
			return fmt.Errorf("Invalid role name: %s", user.Role)
		}
		update["role"] = user.Role
	}

	if user.Status != "" {
		if !utils.Contain(user.Status, []string{"active", "inactive"}) {
			return fmt.Errorf("Invalid status: %s", user.Status)
		}
		update["status"] = user.Status
	}

	if len(update) > 0 {
		Db.Model(&user).Update(update)
	}

	return nil
}

// ImportUserFromCSV import users from csv file
func ImportUserFromCSV(w http.ResponseWriter, r *http.Request) {
	file, header, _ := r.FormFile("file")
	records, err := utils.ReadSheetRecords(file, header)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "message": err})
		return
	}

	adminUUID := r.Context().Value("id").(uuid.UUID)
	var admin models.User
	if err := Db.Where("id = ?", adminUUID.String()).First(&admin).Error; err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "message": err})
		return
	}

	orgID, _ := strconv.ParseInt(mux.Vars(r)["org_id"], 10, 64)

	total, success, importErr := importUserFromCSV(admin, orgID, records)
	json.NewEncoder(w).Encode(map[string]any{
		"success":       true,
		"total":         total,
		"success_count": success,
		"import_err":    importErr,
	})
}

func importUserFromCSV(admin models.User, orgID int64, records [][]string) (total int, success int, importErr map[int]string) {
	if len(records) <= 1 {
		return
	}

	total = len(records) - 1
	importErr = map[int]string{}
	// Scan each records
	for i, record := range records {
		if i == 0 {
			continue // Skip header row
		}

		rowNumber := i + 1
		if len(record) < 5 {
			importErr[rowNumber] = "invalid data" // map row_number and error
			continue
		}

		// TODO: add more data
		user := models.UserInfo{
			GivenName:        record[0],
			Surname:          record[1],
			Email:            record[2],
			Department:       record[3],
			CostCenterNumber: record[4],
			Role:             USER,
		}

		if err := addCorpUser(admin, orgID, user); err != nil {
			importErr[rowNumber] = err.Error() // map row_number and error
			continue
		}
		success++
	}
	return
}

// UpdateCorpLogoFromImage import and update corporation logo
func UpdateCorpLogo(w http.ResponseWriter, r *http.Request) {
	file, _, err := r.FormFile("file")
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "message": err})
		return
	}

	url, err := updateLogoFromImageToS3(r.FormValue("corp_id"), file)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "message": err})
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"url":     url,
	})
}

func updateLogoFromImageToS3(corpID string, file multipart.File) (string, error) {
	awsConfig := aws.NewConfig().WithRegion(region).WithLogLevel(aws.LogOff)
	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return "", err
	}

	// Upload file to s3 from multipart file
	var buf bytes.Buffer
	if _, err := io.Copy(&buf, file); err != nil {
		return "", err
	}
	bucket, key := s3_map["ariadirect_prod_corporation_logo"].(string), corpID+"/logo.jpg"
	if err := awslib.UploadFile(sess, bucket, key, buf.Bytes()); err != nil {
		return "", err
	}

	// Make a url presign
	req, _ := s3.New(sess).GetObjectRequest(&s3.GetObjectInput{Bucket: aws.String(bucket), Key: aws.String(key)})
	url, err := req.Presign(24 * time.Hour)
	if err != nil {
		return "", err
	}
	return url, nil
}

// ExportUsersToCSV export users to csv
func ExportUsersToCSV(w http.ResponseWriter, r *http.Request) {
	orgID, _ := strconv.ParseInt(mux.Vars(r)["org_id"], 10, 64)

	var users []models.UserInfo
	if err := Db.Select("users.*, cd.name department,cc.name cost_center_number").Table("users").
		Joins("LEFT JOIN user_corp_info uc ON uc.user_id = users.id ").
		Joins("LEFT JOIN corporation_departments cd ON cd.id = uc.department_id").
		Joins("LEFT JOIN corporation_cost_centers cc ON cc.id = uc.cost_center_id").
		Where("organization_id = ? AND role IN (?)", orgID, []string{"user", "admin"}).
		Find(&users).Error; err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "message": err})
		return
	}

	var headers []string = []string{"#", "Given Name", "Surname", "Email", "Department Name", "Cost Center Number"}

	writer := csv.NewWriter(w)
	defer writer.Flush()

	// Write headers
	if err := writer.Write(headers); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "message": err})
		return
	}

	// Write data
	for i, user := range users {
		row := []string{fmt.Sprintf("%d", i+1), user.GivenName, user.Surname, user.Email, user.Department, user.CostCenterNumber}
		if err := writer.Write(row); err != nil {
			w.WriteHeader(http.StatusBadRequest)
			json.NewEncoder(w).Encode(map[string]any{"success": false, "message": err})
			return
		}
	}

	// Write response as csv
	w.Header().Set("Content-Type", "text/csv;charset=UTF-8")
}

type exportUserReq struct {
	From time.Time `json:"from"`
	To   time.Time `json:"to"`
}
type exportUserItem struct {
	*models.UserInfo
	LastLoginCountry string
	LastLoginDevice  string
	RegisterDay      *time.Time
	LastOrder        *int64
}

// ExportUsersToCSVByADAdmin export users to csv
func ExportUsersToCSVByADAdmin(w http.ResponseWriter, r *http.Request) {
	var req exportUserReq
	var users []exportUserItem
	json.NewDecoder(r.Body).Decode(&req)

	if err := Db.Select(`u.*,
	(SELECT CONCAT_WS(' - ', ip_country,ip_location) FROM user_login_histories WHERE user_email = u.email ORDER BY created_at DESC LIMIT 1) last_login_country,
	(SELECT CONCAT_WS(' - ', user_agent_os,user_agent_device) FROM user_login_histories WHERE user_email = u.email ORDER BY created_at DESC LIMIT 1) last_login_device,
	(SELECT id from (SELECT id,user_id,created_at from package union all SELECT id,user_id,created_at from service_orders so) v where v.user_id = u.id order by created_at desc limit 1 ) last_order
	`).Table("users u").
		Where("created_at > ?", req.From).
		Where("created_at < ?", req.To).
		Find(&users).Error; err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "message": err})
		return
	}

	writer := csv.NewWriter(w)
	defer writer.Flush()

	// Write headers
	var headers []string = []string{"#", "Given name", "Last name", "Email", "Last login country", "Last login device", "Register date", "Last order"}
	if err := writer.Write(headers); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "message": err})
		return
	}

	// Write data
	for i, user := range users {
		lastOrder := ""
		if user.LastOrder != nil {
			lastOrder = "Order: #" + strconv.FormatInt(*user.LastOrder, 10)
		}
		row := []string{fmt.Sprintf("%d", i+1), user.GivenName, user.Surname, user.Email, user.LastLoginCountry, user.LastLoginDevice, user.CreatedAt.Format(time.RFC3339), lastOrder}
		if err := writer.Write(row); err != nil {
			w.WriteHeader(http.StatusBadRequest)
			json.NewEncoder(w).Encode(map[string]any{"success": false, "message": err})
			return
		}
	}

	// Write response as csv
	w.Header().Set("Content-Type", "text/csv;charset=UTF-8")
}

func addCorpUser(admin models.User, orgID int64, userInfo models.UserInfo) error {
	if !govalidator.IsEmail(userInfo.Email) {
		return fmt.Errorf("Email: %s is invalid format", userInfo.Email)
	}

	var userByEmail models.User
	err := Db.Where("email = ?", userInfo.Email).First(&userByEmail).Error
	if err == nil {
		return fmt.Errorf("Email: %s already exist", userInfo.Email)
	}

	if !gorm.IsRecordNotFoundError(err) {
		return err
	}

	user := models.User{
		GivenName: userInfo.GivenName,
		Surname:   userInfo.Surname,
		Email:     userInfo.Email,
		Role:      userInfo.Role,
		Password:  encryptPassword(userInfo.Email),
		Status:    models.UserStatus.Pending,
	}
	user.OrganizationID = orgID

	if err := Db.Create(&user).Error; err != nil {
		return err
	}
	var (
		department models.CorporationDepartment
		costCenter models.CorporationCostCenter
	)

	if err := Db.FirstOrCreate(&department, map[string]any{
		"name": userInfo.Department,
	}).Error; err != nil {
		return err
	}

	if err := Db.FirstOrCreate(&costCenter, map[string]any{
		"name": userInfo.CostCenterNumber,
	}).Error; err != nil {
		return err
	}

	if err := Db.Exec("INSERT INTO user_corp_info(user_id,department_id,cost_center_id) VALUES(?,?,?)",
		user.ID, department.ID, costCenter.ID).Error; err != nil {
		return err
	}

	go SendEmailInvitedMember(&admin, &user, false)

	return nil
}

func ActivateUsers(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var org_id = params["org_id"]
	org, _ := strconv.Atoi(org_id)

	users := []models.User{}
	err := json.NewDecoder(r.Body).Decode(&users)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	number_users := len(users)
	var result []map[string]any
	for i := 0; i < number_users; i++ {
		resp := ChangeUserStatus(&users[i], int64(org), "active")
		result = append(result, resp)
	}
	json.NewEncoder(w).Encode(result)
}

func DeactivateUsers(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var org_id = params["org_id"]
	org, _ := strconv.Atoi(org_id)

	users := []models.User{}
	err := json.NewDecoder(r.Body).Decode(&users)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid request"}
		w.WriteHeader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	number_users := len(users)
	var result []map[string]any
	for i := 0; i < number_users; i++ {
		resp := ChangeUserStatus(&users[i], int64(org), "inactive")
		result = append(result, resp)
	}
	json.NewEncoder(w).Encode(result)
}

func ChangeUserStatus(user *models.User, org int64, new_status string) map[string]any {
	current_user := &models.User{}
	if err := Db.Where("Email = ?", user.Email).First(current_user).Error; err != nil {
		resp := map[string]any{
			"error_code":    "ERR00005",
			"error_message": "User doesn't exist",
			"status":        false,
		}
		return resp
	}

	if current_user.OrganizationID != org {
		resp := map[string]any{
			"error_code":    "ERR00005",
			"error_message": "User is not in request organization",
			"status":        false,
		}
		return resp
	}
	current_status := current_user.Status
	if current_status == "active" || current_status == "inactive" {
		current_user.Status = new_status
	}

	// Released direct reports when user disabled or remove from corp
	if current_user.Status == "inactive" {
		if err := Db.Exec("UPDATE user_corp_info SET manager_id = NULL WHERE manager_id = ?", current_user.ID).Error; err != nil {
			resp := map[string]any{
				"error_code":    "ERR00005",
				"error_message": err.Error(),
				"status":        false,
			}
			return resp
		}
	}

	if err := Db.Save(current_user).Error; err != nil {
		resp := map[string]any{
			"error_code":    "ERR00005",
			"error_message": err.Error(),
			"status":        false,
		}
		return resp
	}

	message := map[string]any{
		"data":   current_user,
		"status": true,
	}
	return message
}

func RemoveUserFromOrg(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var id = params["user_id"]
	var user models.User
	if err := Db.Where("id = ?", id).First(&user).Error; err != nil {
		handleError(w, err)
		return
	}

	user.OrganizationID = 0
	if err := Db.Save(&user).Error; err != nil {
		handleError(w, err)
		return
	}

	// Released direct reports when user disabled or remove from corp
	if err := Db.Exec("UPDATE user_corp_info SET manager_id = NULL WHERE manager_id = ?", user.ID).Error; err != nil {
		handleError(w, err)
		return
	}

	// Release staff orders
	if err := Db.Exec("DELETE FROM service_order_staffs where staff_id  = ?", user.ID).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(&user)
}

func GetOrganizationDetail(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var providerInfo = &json.RawMessage{}

	if strings.HasPrefix(params["provider_id"], "cons-") {
		if err := Db.DB().QueryRow(`SELECT to_jsonb(consulate) FROM consulate WHERE id = $1 LIMIT 1`, params["provider_id"]).
			Scan(&providerInfo); err != nil {
			handleError(w, err)
			return
		}
	}

	if strings.HasPrefix(params["provider_id"], "etsp-") {
		if err := Db.DB().QueryRow(`SELECT to_jsonb(ets_provider) FROM ets_provider WHERE id = $1 LIMIT 1`, params["provider_id"]).
			Scan(&providerInfo); err != nil {
			handleError(w, err)
			return
		}
	}

	if strings.HasPrefix(params["provider_id"], "corp-") {
		if err := Db.DB().QueryRow(`SELECT to_jsonb(corporation) FROM corporation WHERE id = $1 LIMIT 1`, params["provider_id"]).
			Scan(&providerInfo); err != nil {
			handleError(w, err)
			return
		}
	}

	json.NewEncoder(w).Encode(providerInfo)
}
