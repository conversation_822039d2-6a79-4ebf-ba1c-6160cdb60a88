package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	sdkUtils "bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/utils"
	"github.com/dgrijalva/jwt-go"
	uuid "github.com/satori/go.uuid"

	//go get -u github.com/aws/aws-sdk-go
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sqs"
)

type VerifyMessageData struct {
	Given_name string
	User_id    uuid.UUID
}

type ResetPasswordMessageData struct {
	User_id uuid.UUID
	Token   string
}

var emailConfigMap = sdkUtils.GetMapEnv("ad_email")
var infoEmail = emailConfigMap["info"].(string)
var bccEmailList = []string{infoEmail}

var sqs_var = os.Getenv("ad_sqs")
var sqs_json = []byte(sqs_var)
var sqs_map = utils.ParseJsonString(sqs_json)
var qURL = sqs_map["url_prefix"].(string) + "/" + sqs_map["notification_sqs_name"].(string)
var aws_var = os.Getenv("ad_aws")
var aws_json = []byte(aws_var)
var aws_map = utils.ParseJsonString(aws_json)
var region = aws_map["region"].(string)
var user_var = os.Getenv("ad_user_account_service")
var user_json = []byte(user_var)
var user_map = utils.ParseJsonString(user_json)
var baseurl = user_map["url"].(string)

//var region = "ap-northeast-1"
//var qURL = "queue"
//var baseurl = "http://d2idwwgwi5gjcc.cloudfront.net/"

//const (
//	Sender = "<EMAIL>"
//)
//var infoEmail = Sender
//var bccEmailList = []string{infoEmail}
//var bccEmail = infoEmail //sns

func SendEmailUserVerification(w http.ResponseWriter, r *http.Request) {
	user := &models.User{}
	json.NewDecoder(r.Body).Decode(user)
	user.Email = strings.ToLower(user.Email)

	var Recipient = user.Email
	//var Template = "user-email-verification-template" sns
	var Template = "register_user"
	var VerificationURL = baseurl + "verify/"

	if err := Db.Where("lower(Email) = ?", user.Email).First(&user).Error; err != nil {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00004",
				ErrorMessage: "Email address not found, please create account first!",
			},
			Status: false,
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(resp)
		return
	}

	// Add metadata for user
	Db.Where("lower(Email) = ?", user.Email).First(user)
	given_name := strings.Title(strings.ToLower(user.GivenName))
	id := user.ID
	url := VerificationURL + id.String()
	parameter := make(map[string]any)
	parameter["Name"] = given_name
	parameter["Url"] = url

	// Already verified, no need to send email
	if user.EmailVerified {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00010",
				ErrorMessage: "Email address already verified, no need to verify again!",
			},
			Status: false,
		}
		w.WriteHeader(http.StatusTooManyRequests) //too many requests
		json.NewEncoder(w).Encode(resp)
		return
	}

	//resp := SendEmail(Recipient, Template, MetaData)
	//if resp["status"] == false {
	//	w.WriteHeader(http.StatusBadRequest)
	//}
	// get email config
	// bccEmail := emailConfigMap["supervisor"].(string)

	resp := SendMessageToSQS(Template, Recipient, parameter)
	json.NewEncoder(w).Encode(resp)
}

func SendEmailResetPassword(w http.ResponseWriter, r *http.Request) {
	user := &models.User{}
	json.NewDecoder(r.Body).Decode(user)
	user.Email = strings.ToLower(user.Email)

	var Recipient = user.Email
	//var Template = "user-reset-password-template"
	var Template = "reset_password"
	var ResetPwdURL = baseurl + "reset/"

	if err := Db.Where("lower(Email) = ?", strings.ToLower(user.Email)).First(&user).Error; err != nil {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00004",
				ErrorMessage: "Email address not found, please create account first!",
			},
			Status: false,
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(resp)
		return
	}

	// Add metadata for user, reset password link expired in 5hr
	Db.Where("Email = ?", user.Email).First(user)
	expiresAt := time.Now().Add(time.Minute * 300).Unix()
	tk := &models.Token{
		UserID: user.ID,
		Role:   user.Role,
		StandardClaims: &jwt.StandardClaims{
			ExpiresAt: expiresAt,
		},
	}
	token := jwt.NewWithClaims(jwt.GetSigningMethod("HS256"), tk)
	tokenString, error := token.SignedString([]byte(jwt_secret))
	if error != nil {
		fmt.Println(error)
	}

	given_name := strings.Title(strings.ToLower(user.GivenName))
	id := user.ID
	url := ResetPwdURL + id.String() + "/" + tokenString
	parameter := make(map[string]any)
	parameter["Name"] = given_name
	parameter["Url"] = url

	resp := SendMessageToSQS(Template, Recipient, parameter)
	json.NewEncoder(w).Encode(resp)
}

func SendEmailInvitedMember(requester, user *models.User, exist bool) map[string]any {
	var Template = "invite_member"

	otp := utils.GenerateOTP(12)

	Db.Create(&models.UserInvite{
		UserID:    user.ID.String(),
		Email:     user.Email,
		OTP:       otp,
		IsUsed:    false,
		InvitedBy: requester.ID.String(),
		OrgID:     requester.OrganizationID,
		ExpiredAt: time.Now().AddDate(0, 0, 3), // Expired in 3 days
		CreatedAt: time.Now(),
	})

	var InviteURL string
	if exist {
		InviteURL = baseurl + "invite-corp/"
	} else {
		InviteURL = baseurl + "invite/"
	}

	url := fmt.Sprintf("%s%s?code=%s&email=%s&org_id=%d", InviteURL, user.ID.String(), otp, user.Email, requester.OrganizationID)

	parameter := map[string]any{
		"FullName": requester.GivenName + " " + requester.Surname,
		"Url":      url,
	}

	resp := SendMessageToSQS(Template, user.Email, parameter)
	return resp
}

type MessageBody struct {
	TemplateName string         `json:"template_name"`
	To           string         `json:"to"`
	Cc           []string       `json:"cc"`
	BCC          []string       `json:"bcc"`
	Parameters   map[string]any `json:"parameters"`
}

func SendMessageToSQS(EmailTemplate string, Recipient string, Parameters map[string]any, BCCEmailList ...string) map[string]any {
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(region)},
	)
	svc := sqs.New(sess)

	bccEmails := bccEmailList
	if len(BCCEmailList) > 0 {
		for _, item := range BCCEmailList {
			bccEmails = append(bccEmails, item)
		}
	}

	message := &MessageBody{
		TemplateName: EmailTemplate,
		To:           Recipient,
		BCC:          bccEmails,
		Parameters:   Parameters,
	}
	messageData, _ := json.Marshal(message)
	messageStr := string(messageData)

	result, err := svc.SendMessage(&sqs.SendMessageInput{
		MessageBody: &messageStr,
		QueueUrl:    &qURL,
	})
	data, _ := json.Marshal(result)
	resultStr := string(data)

	resp := map[string]any{"status": true, "message": resultStr}
	if err != nil {
		resp["status"] = false
		resp["message"] = err.Error()
	}
	return resp
}
