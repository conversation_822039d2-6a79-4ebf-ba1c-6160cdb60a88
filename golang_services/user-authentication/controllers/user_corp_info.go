package controllers

import (
	"encoding/json"
	"net/http"

	"github.com/gorilla/mux"
	"github.com/jinzhu/gorm"
	"github.com/rs/zerolog/log"

	models2 "bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
)

func getUserCorpInfoDao() db.UserCorpInfoDao {
	return &db.PGUserCorpInfoDao{Db: adb}
}

func GetUserCorpInfo(w http.ResponseWriter, r *http.Request) {
	user, err := getUserInfo(r)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{
			"success": false,
		})
		log.Error().Err(err).Msg("failed to get user info")
		return
	}
	if user == nil {
		w.WriteHeader(http.StatusNotFound)
		return
	}
	corpInfo, err := getUserCorpInfo(getUserCorpInfoDao(), user.ID.String())
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{
			"success": false,
		})
		log.Error().Err(err).Msg("failed to get user corp info")
		return
	}
	json.NewEncoder(w).Encode(corpInfo)
	return
}

func getUserCorpInfo(dao db.UserCorpInfoDao, userID string) (*models2.UserCorpInfo, error) {
	corpInfo, err := dao.GetUserCorpInfo([]string{userID})
	if err != nil {
		return nil, err
	}
	if len(corpInfo) > 0 {
		return corpInfo[0], nil
	}
	return nil, nil
}

func CreateUserCorpInfo(w http.ResponseWriter, r *http.Request) {
	user, err := getUserInfo(r)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{
			"success": false,
		})
		log.Error().Err(err).Msg("failed to get user info")
		return
	}
	if user == nil {
		w.WriteHeader(http.StatusNotFound)
		return
	}
	var info models2.UserCorpInfo
	if err := json.NewDecoder(r.Body).Decode(&info); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		log.Error().Err(err).Msg("failed to decode")
		return
	}
	info.UserID = user.ID.String()
	dao := getUserCorpInfoDao()
	if err := createOrUpdateUserCorpInfo(dao, &info); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{
			"success": false,
		})
		log.Error().Err(err).Msg("failed to create or update user corp info")
		return
	}
	updated, err := getUserCorpInfo(dao, info.UserID)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{
			"success": false,
		})
		log.Error().Err(err).Msg("failed to get user corp info")
		return
	}
	json.NewEncoder(w).Encode(updated)
	return
}

func createOrUpdateUserCorpInfo(dao db.UserCorpInfoDao, corpInfo *models2.UserCorpInfo) error {
	return dao.CreateOrUpdateUserCorpInfo(corpInfo)
}

func UpdateUserCorpInfo(w http.ResponseWriter, r *http.Request) {
	user, err := getUserInfo(r)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{
			"success": false,
		})
		log.Error().Err(err).Msg("failed to get user info")
		return
	}
	if user == nil {
		w.WriteHeader(http.StatusNotFound)
		return
	}

	var info models2.UserCorpInfo
	if err := json.NewDecoder(r.Body).Decode(&info); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		log.Error().Err(err).Msg("failed to decode")
		return
	}
	info.UserID = user.ID.String()
	dao := getUserCorpInfoDao()
	if err := dao.UpdateUserCorpInfo(&info); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{
			"success": false,
		})
		log.Error().Err(err).Msg("failed to update user corp info")
		return
	}
	updated, err := getUserCorpInfo(dao, info.UserID)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{
			"success": false,
		})
		log.Error().Err(err).Msg("failed to get user corp info")
		return
	}
	json.NewEncoder(w).Encode(updated)
	return
}

func getUserInfo(r *http.Request) (*models.User, error) {
	params := mux.Vars(r)
	userID := params["id"]
	if userID == "" {
		return nil, nil
	}
	var user models.User
	db := Db.Where("id = ?", userID).First(&user)
	if db.Error != nil {
		if db.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, db.Error
	}
	return &user, nil
}
