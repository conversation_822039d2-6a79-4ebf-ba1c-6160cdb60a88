package controllers

import (
	"encoding/json"
	"net/http"

	"github.com/gorilla/mux"
	"github.com/ulule/deepcopier"

	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	adcasbin "bitbucket.org/persistence17/aria/golang_services/user-authentication/utils/casbin"
)

var casbins = adcasbin.CasbinModel{}

func AddCasbin(w http.ResponseWriter, r *http.Request) {
	casbin := &adcasbin.CasbinModel{}
	err := json.NewDecoder(r.Body).Decode(casbin)
	if err != nil {
		var resp = map[string]any{"status": false, "message": "Invalid Casbin policy request"}
		w.Write<PERSON>eader(401)
		json.NewEncoder(w).Encode(resp)
		return
	}

	// Casbin definition: p -> policy definition
	casbin.PolicyType = "p"
	ok := casbins.AddCasbin(*casbin, Db)
	if ok {
		var resp = map[string]any{"status": true, "message": "Add role policy successfuly!"}
		json.NewEncoder(w).Encode(resp)
		return
	} else {
		var resp = map[string]any{"status": false, "message": "Failed to add role policy!"}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(resp)
		return
	}
}

func CasbinEnforceSafe(w http.ResponseWriter, r *http.Request) {
	query := r.URL.Query()
	path, method := query.Get("path"), query.Get("method")
	v := r.Context().Value("user")
	if v == nil {
		w.WriteHeader(http.StatusUnauthorized)
		return
	}
	userToken, ok := v.(*models.Token)
	if !ok {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"message": "missing user token in context"})
		return
	}
	role := userToken.Role

	e := adcasbin.Casbin(Db)
	// TODO: Check role from token matches DB
	// check auth
	res, err := e.EnforceSafe(role, path, method)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]any{"message": err.Error()})
		return
	}
	if res {
		params := mux.Vars(r)
		var id = params["id"]
		var user models.User
		Db.Where("id = ?", id).First(&user)

		// add user type to response
		userInfo := &models.UserInfo{}
		deepcopier.Copy(user).To(userInfo)
		if userInfo.TimezoneName, err = FindOrgTimeZoneByID(user.OrganizationID); err != nil {
			handleError(w, err)
			return
		}

		if userInfo.UserTypes, err = GetUserTypeByOrgID(user.OrganizationID); err != nil {
			handleError(w, err)
			return
		}

		json.NewEncoder(w).Encode(&userInfo)
	} else {
		resp := models.Response{
			Error: &models.Error{
				ErrorCode:    "ERR00009",
				ErrorMessage: "Sorry, you don't have permission to take action!",
			},
			Status: true,
		}
		w.WriteHeader(http.StatusUnauthorized)
		json.NewEncoder(w).Encode(resp)
		return
	}
}
