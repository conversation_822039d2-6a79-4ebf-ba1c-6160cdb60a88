package controllers

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"github.com/gorilla/mux"
	uuid "github.com/satori/go.uuid"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

// GetOfficeList get offices in corporation
func GetOfficeList(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var (
		corp models.Corporation
	)

	Db.Where("org_id=?", params["org_id"]).First(&corp)

	if err := r.ParseForm(); err != nil {
		return
	}

	if corp.Offices == nil {
		corp.Offices = []byte("[]") // Init array
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    corp.Offices,
	})
}

// CreateOffice add office to corporation
func CreateOffice(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var (
		corp   models.Corporation
		office models.CorporationOffice
	)

	Db.Where("org_id=?", params["org_id"]).First(&corp)

	if err := r.ParseForm(); err != nil {
		return
	}

	json.NewDecoder(r.Body).Decode(&office)
	office.ID = uuid.NewV4().String()
	office.CreatedAt = time.Now()

	if corp.Offices == nil {
		corp.Offices = []byte("[]") // Init array
	}

	corp.Offices, _ = sjson.SetBytes(corp.Offices, "-1", office) // Append
	Db.Save(&corp)

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    corp.Offices,
	})
}

// UpdateOffice update office in corporation
func UpdateOffice(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var (
		corp   *models.Corporation
		office models.CorporationOffice
	)

	Db.Where("org_id=?", params["org_id"]).First(&corp)
	if corp == nil {
		w.WriteHeader(http.StatusNotFound)
		json.NewEncoder(w).Encode(map[string]any{"status": false, "message": "not found corporation"})
		return
	}

	officeIndex := -1
	for i, item := range gjson.ParseBytes(corp.Offices).Array() {
		if item.Get("id").String() == params["office_id"] {
			officeIndex = i
			break
		}
	}
	if officeIndex < 0 {
		w.WriteHeader(http.StatusNotFound)
		json.NewEncoder(w).Encode(map[string]any{"status": false, "message": "Office not found"})
		return
	}

	json.NewDecoder(r.Body).Decode(&office)

	corp.Offices, _ = sjson.SetBytes(corp.Offices, strconv.Itoa(officeIndex), office) // Update
	Db.Save(&corp)

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    corp.Offices,
	})
}

// DeleteOffice delete office in corporation
func DeleteOffice(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var (
		corp   models.Corporation
		office models.CorporationOffice
	)

	Db.Where("org_id=?", params["org_id"]).First(&corp)

	officeIndex := -1
	for i, item := range gjson.ParseBytes(corp.Offices).Array() {
		if item.Get("id").String() == params["office_id"] {
			officeIndex = i
			break
		}
	}
	if officeIndex < 0 {
		w.WriteHeader(http.StatusNotFound)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "message": "Office not found"})
		return
	}

	json.NewDecoder(r.Body).Decode(&office)

	corp.Offices, _ = sjson.DeleteBytes(corp.Offices, strconv.Itoa(officeIndex)) // Delete
	Db.Save(&corp)

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    corp.Offices,
	})
}

// GetADContactList get contacts for corporation
func GetADContactList(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var (
		org models.ViewOrganization
	)

	Db.Table("v_organizations").Where("id = ?", params["org_id"]).First(&org)

	if org.ADContacts == nil {
		org.ADContacts = []byte("[]") // Init array
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    org.ADContacts,
	})
}

// CreateADContact add contact to corporation
func CreateADContact(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var (
		org     models.ViewOrganization
		contact models.ADContact
	)

	Db.Table("v_organizations").Where("id=?", params["org_id"]).First(&org)

	if err := r.ParseForm(); err != nil {
		return
	}

	json.NewDecoder(r.Body).Decode(&contact)
	contact.ID = uuid.NewV4().String()
	contact.CreatedAt = time.Now()

	if org.ADContacts == nil {
		org.ADContacts = []byte("[]") // Init array
	}

	org.ADContacts, _ = sjson.SetBytes(org.ADContacts, "-1", contact) // Append

	Db.Table(org.Type).Where("id = ?", org.Profile).Update(map[string]any{
		"ad_contacts": org.ADContacts,
	})

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    org.ADContacts,
	})
}

// UpdateADContact update contact in corporation
func UpdateADContact(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var (
		org     models.ViewOrganization
		contact models.ADContact
	)

	Db.Table("v_organizations").Where("id=?", params["org_id"]).First(&org)

	contactIndex := -1
	for i, item := range gjson.ParseBytes(org.ADContacts).Array() {
		if item.Get("id").String() == params["contact_id"] {
			contactIndex = i
			break
		}
	}
	if contactIndex < 0 {
		w.WriteHeader(http.StatusNotFound)
		json.NewEncoder(w).Encode(map[string]any{"status": false, "message": "Contact not found"})
		return
	}

	json.NewDecoder(r.Body).Decode(&contact)

	org.ADContacts, _ = sjson.SetBytes(org.ADContacts, strconv.Itoa(contactIndex), contact) // Update

	Db.Table(org.Type).Where("id = ?", org.Profile).Update(map[string]any{
		"ad_contacts": org.ADContacts,
	})

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    org.ADContacts,
	})
}

// DeleteADContact delete contact in corporation
func DeleteADContact(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	var (
		org     models.ViewOrganization
		contact models.ADContact
	)

	Db.Table("v_organizations").Where("id=?", params["org_id"]).First(&org)

	contactIndex := -1
	for i, item := range gjson.ParseBytes(org.ADContacts).Array() {
		if item.Get("id").String() == params["contact_id"] {
			contactIndex = i
			break
		}
	}
	if contactIndex < 0 {
		w.WriteHeader(http.StatusNotFound)
		json.NewEncoder(w).Encode(map[string]any{"success": false, "message": "contact not found"})
		return
	}

	json.NewDecoder(r.Body).Decode(&contact)

	org.ADContacts, _ = sjson.DeleteBytes(org.ADContacts, strconv.Itoa(contactIndex)) // Delete

	Db.Table(org.Type).Where("id = ?", org.Profile).Update(map[string]any{
		"ad_contacts": org.ADContacts,
	})

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    org.ADContacts,
	})
}
