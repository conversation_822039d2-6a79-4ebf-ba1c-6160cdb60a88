package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	//go get -u github.com/aws/aws-sdk-go
)

type emailSubscribeReq struct {
	Email                      string `json:"email"`
	Code                       string `json:"code"`
	SubscribeAnnouncement      bool   `json:"subscribe_announcement"`
	SubscribeOrderNotification bool   `json:"subscribe_order_notification"`
}

func GetEmailSubscribe(w http.ResponseWriter, r *http.Request) {
	emails := r.URL.Query()["email"]
	if len(emails) == 0 {
		handleError(w, fmt.Errorf("invalid email"))
		return
	}

	email := models.EmailSubscribe{}
	if err := Db.FirstOrCreate(&email, map[string]any{
		"email": emails[0],
	}).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(email)
}

func UpdateEmailSubscribe(w http.ResponseWriter, r *http.Request) {
	req := emailSubscribeReq{}
	json.NewDecoder(r.Body).Decode(&req)
	if encryptPassword(req.Email) != req.Code {
		handleError(w, fmt.Errorf("Invalid request code"))
		return
	}

	email := models.EmailSubscribe{}
	if err := Db.FirstOrCreate(&email, map[string]any{
		"email": req.Email,
	}).Error; err != nil {
		handleError(w, err)
		return
	}

	email.SubscribeAnnouncement = req.SubscribeAnnouncement
	email.SubscribeOrderNotification = req.SubscribeOrderNotification

	Db.Save(&email)

	json.NewEncoder(w).Encode(email)
}
