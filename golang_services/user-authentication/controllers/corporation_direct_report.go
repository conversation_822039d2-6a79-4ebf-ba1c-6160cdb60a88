package controllers

import (
	"encoding/json"
	"net/http"

	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/utils"
	"github.com/gorilla/mux"
	"github.com/jinzhu/gorm"
	uuid "github.com/satori/go.uuid"
	"github.com/thoas/go-funk"
)

func ClassifyMembersForDirectReport(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	currentUserID := r.Context().Value("id").(uuid.UUID)
	var user models.User
	if err := Db.Where("id = ?", currentUserID.String()).First(&user).Error; err != nil {
		handleError(w, err)
		return
	}

	members := []models.MemberUserInfo{}
	json.NewDecoder(r.Body).Decode(&members)

	classifies, err := classifyMembersForDirectReport(user.OrganizationID, params["manager_id"], members)
	if err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    classifies,
	})
}

func classifyMembersForDirectReport(orgID int64, managerID string, members []models.MemberUserInfo) (models.ClassifyMemberForDirectReport, error) {
	result := models.ClassifyMemberForDirectReport{
		UsersCanAddToManager:        []models.MemberUserInfo{},
		UsersBelongToAnotherCorp:    []models.MemberUserInfo{},
		UsersBelongToAnotherManager: []models.MemberUserInfo{},
		UsersNotInSystem:            []models.MemberUserInfo{},
		UsersAlreadyAddToManager:    []models.MemberUserInfo{},
	}

	// Map to remove duplicate email
	membersMap := funk.ToMap(members, "Email").(map[string]models.MemberUserInfo)

	for _, member := range membersMap {
		var user models.User
		if err := Db.Where("email = ?", member.Email).First(&user).Error; err != nil && err != gorm.ErrRecordNotFound {
			return result, err
		}
		if member.GivenName == "" {
			member.GivenName = user.GivenName
		}

		if member.Surname == "" {
			member.Surname = user.Surname
		}

		if user.ID == uuid.FromBytesOrNil(nil) {
			result.UsersNotInSystem = append(result.UsersNotInSystem, member)
			continue
		}

		if user.OrganizationID != orgID {
			result.UsersBelongToAnotherCorp = append(result.UsersBelongToAnotherCorp, member)
			continue
		}

		var userCorpInfo models.UserCorpInfo

		if err := Db.Where("user_id = ?", user.ID).First(&userCorpInfo).Error; err != nil && err != gorm.ErrRecordNotFound {
			return result, err
		}

		if userCorpInfo.ManagerID == nil {
			result.UsersCanAddToManager = append(result.UsersCanAddToManager, member)
			continue
		}

		if *userCorpInfo.ManagerID == managerID {
			result.UsersAlreadyAddToManager = append(result.UsersAlreadyAddToManager, member)
		} else {
			result.UsersBelongToAnotherManager = append(result.UsersBelongToAnotherManager, member)
		}

	}
	return result, nil
}

func GetMembersForDirectReport(w http.ResponseWriter, r *http.Request) {
	currentUserID := r.Context().Value("id").(uuid.UUID)
	var currentUser models.User
	if err := Db.Where("id = ?", currentUserID.String()).First(&currentUser).Error; err != nil {
		handleError(w, err)
		return
	}

	params := mux.Vars(r)
	query := r.URL.Query()
	query.Add("manager_id", params["manager_id"])

	result := ListUsersByOrgID(currentUser.OrganizationID, query)
	json.NewEncoder(w).Encode(result)
}

func AddMembersForDirectReport(w http.ResponseWriter, r *http.Request) {
	currentUserID := r.Context().Value("id").(uuid.UUID)
	var currentUser models.User
	if err := Db.Where("id = ?", currentUserID.String()).First(&currentUser).Error; err != nil {
		handleError(w, err)
		return
	}

	params := mux.Vars(r)
	members := []models.MemberUserInfo{}
	json.NewDecoder(r.Body).Decode(&members)

	class, err := classifyMembersForDirectReport(currentUser.OrganizationID, params["manager_id"], members)
	if err != nil {
		handleError(w, err)
		return
	}
	if len(class.UsersCanAddToManager)+len(class.UsersNotInSystem) != len(members) {
		json.NewEncoder(w).Encode(map[string]any{
			"success": false,
			"data":    class,
		})
		return
	}

	// Directly add members to manager
	for _, member := range class.UsersCanAddToManager {
		var user models.User
		if err := Db.Where("email = ?", member.Email).First(&user).Error; err != nil {
			handleError(w, err)
			return
		}

		if err := Db.Exec(`INSERT INTO user_corp_info(user_id,manager_id) 
			VALUES (?,?) ON CONFLICT(user_id) DO UPDATE
			SET manager_id = EXCLUDED.manager_id`, user.ID, params["manager_id"]).Error; err != nil {
			handleError(w, err)
			return
		}
	}

	// Add member to manager and send email pending
	for _, member := range class.UsersNotInSystem {
		user := models.User{
			Email:         member.Email,
			GivenName:     member.GivenName,
			Surname:       member.Surname,
			EmailVerified: true,
			Status:        models.UserStatus.Pending,
			// OrganizationID: null.IntFrom(0),
			Password: utils.RandomPassword(),
		}
		user.OrganizationID = currentUser.OrganizationID
		user.PasswordInit = user.Password
		if err := Db.Create(&user).Error; err != nil {
			handleError(w, err)
			return
		}

		if err := Db.Exec(`INSERT INTO user_corp_info(user_id,manager_id) 
			VALUES (?,?) ON CONFLICT(user_id) DO UPDATE
			SET manager_id = EXCLUDED.manager_id`, user.ID, params["manager_id"]).Error; err != nil {
			handleError(w, err)
			return
		}

		go SendEmailInvitedMember(&currentUser, &user, false)
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
	})
}

func RemoveDirectReportsManager(w http.ResponseWriter, r *http.Request) {
	req := []string{}
	json.NewDecoder(r.Body).Decode(&req)

	if err := Db.Exec("UPDATE user_corp_info SET manager_id = NULL WHERE user_id IN (?)", req).Error; err != nil {
		handleError(w, err)
		return
	}

	json.NewEncoder(w).Encode(map[string]any{
		"success": true,
	})
}
