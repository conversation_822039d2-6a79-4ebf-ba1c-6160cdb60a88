package role

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/user-authentication/controllers"
	"github.com/dgrijalva/jwt-go"

	"bitbucket.org/persistence17/aria/golang_services/user-authentication/models"
	"bitbucket.org/persistence17/aria/golang_services/user-authentication/utils/auth"
	adcasbin "bitbucket.org/persistence17/aria/golang_services/user-authentication/utils/casbin"
)

// Middleware check user auth role
func AuthCheckRole(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// get role from claims
		//claims := c.MustGet("claims").(*models.Token)
		var header = r.Header.Get("x-access-token") //Grab the token from the header
		header = strings.TrimSpace(header)

		token, err := jwt.ParseWithClaims(header, &models.Token{}, func(token *jwt.Token) (any, error) {
			return []byte(auth.Jwt_secret), nil
		})
		if err != nil {
			w.WriteHeader(http.StatusForbidden)
			resp := models.Response{
				Error: &models.Error{
					ErrorCode:    "ERR00001",
					ErrorMessage: err.Error(),
				},
				Status: false,
			}
			json.NewEncoder(w).Encode(resp)
			return
		}

		claims, ok := token.Claims.(*models.Token)
		// error get claims
		if !ok {
			w.WriteHeader(http.StatusForbidden)
			json.NewEncoder(w).Encode(auth.Exception{Message: "Error get claims payload"})
			return
		}

		role := claims.Role
		e := adcasbin.Casbin(controllers.Db)
		// TODO: Check role from token matches DB
		// check auth
		res, err := e.EnforceSafe(role, r.URL.Path, r.Method)
		if err != nil {
			w.WriteHeader(http.StatusBadRequest)
			json.NewEncoder(w).Encode(auth.Exception{Message: err.Error()})
			return
		}
		if res {
			ctx := context.WithValue(r.Context(), "id", claims.UserID)
			next.ServeHTTP(w, r.WithContext(ctx))
		} else {
			resp := models.Response{
				Error: &models.Error{
					ErrorCode:    "ERR00009",
					ErrorMessage: "Sorry, you don't have permission to take action!",
				},
				Status: true,
			}
			json.NewEncoder(w).Encode(resp)
			return
		}
	})
}
