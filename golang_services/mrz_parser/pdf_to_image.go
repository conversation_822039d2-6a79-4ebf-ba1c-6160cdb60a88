package main

import (
	"net/http"
	"path/filepath"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"github.com/spf13/cast"
)

func PDFToImage(c *gin.Context) {
	downloader := middlewares.GetS3Downloader(c)

	var req parseDriverLicenseReq
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11001",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}

	bucket, key, err := utils.UrlToS3BucketAndKey(req.Image)
	if err != nil {
		if err != nil {
			c.JSO<PERSON>(http.StatusOK, gin.H{
				"success": false,
				"error":   err,
			})
			return
		}
	}
	if filepath.Ext(key) != ".pdf" {
		c.<PERSON>(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11002",
			"error_message": "Invalid file format. Only PDF files are supported.",
		})
		return
	}

	buff, err := downloader.DownloadFromS3BucketToBuffer(bucket, key)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   err,
		})
		return
	}

	newBuff, err := ConvertPDFToImage(buff)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   err,
		})
		return
	}

	bucketMap := utils.GetMapEnv("ad_s3")
	imageURL, err := UploadBufferToS3(newBuff, cast.ToString(bucketMap["ariadirect_prod_passport_images"]), uuid.NewV4().String()+".jpg")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    imageURL,
	})
}
