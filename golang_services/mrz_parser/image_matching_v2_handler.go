package main

import (
	"fmt"
	"net/http"
	"sort"
	"sync"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/spf13/cast"
)

func MatchPassportURLsWithBatchIDV2(c *gin.Context) {
	ps := middlewares.GetS3Presigner(c)
	downloader := middlewares.GetS3Downloader(c)
	imageAnalyzer := middlewares.GetImageAnalyzer(c)
	bucketMap := utils.GetMapEnv("ad_s3")
	bucket := cast.ToString(bucketMap["ariadirect_prod_applications"])

	images, err := ps.ListObjects(bucket, fmt.Sprintf("batches/%s", c.<PERSON>("batch-id")))
	if err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	resp, err := batchMatchPassportV2(downloader, bucket, images, ps, imageAnalyzer)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data":    resp,
		"success": true,
	})
}

func batchMatchPassportV2(downloader *aws.S3Downloader, bucket string, keys []string, presigner *aws.S3Svc, analyzer *aws.RekImageAnalyzer) (*BatchMatchPassportResponse, error) {
	newKeys, err := checkPDFAndConvertToImages(downloader, bucket, keys)
	if err != nil {
		return nil, err
	}

	categorized, err := analyzer.CategorizeImages(bucket, newKeys)
	if err != nil {
		return nil, err
	}

	matched, unmatched, err := matchPassportsV2(bucket, categorized.Passports, categorized.PersonalPhotos, analyzer)
	if err != nil {
		return nil, err
	}

	if err := presignMatchedPassports(bucket, matched, presigner); err != nil {
		return nil, err
	}
	unmatched, err = presignFiles(bucket, unmatched, presigner)
	if err != nil {
		return nil, err
	}
	others, err := presignFiles(bucket, categorized.Others, presigner)
	if err != nil {
		return nil, err
	}
	return &BatchMatchPassportResponse{
		MatchedPassports: matched,
		UnmatchedImages:  append(unmatched, others...),
	}, nil
}

func matchPassportsV2(bucket string, passports, personalPhotos []string, analyzer *aws.RekImageAnalyzer) ([]*MatchedPassport, []string, error) {
	var wg sync.WaitGroup
	resCh := make(chan *matchingResult, len(passports)*len(personalPhotos))
	matchedFiles := make(map[string]bool)
	mu := sync.Mutex{}
	sem := make(chan struct{}, 5) // limit to 5 concurrent goroutines

	for _, passport := range passports {
		wg.Add(1)
		go func(pp string) {
			defer wg.Done()

			sem <- struct{}{}
			defer func() { <-sem }()

			mu.Lock()
			if matchedFiles[pp] {
				mu.Unlock()
				return
			}
			mu.Unlock()

			for _, photo := range personalPhotos {
				mu.Lock()
				if matchedFiles[photo] {
					mu.Unlock()
					continue
				}
				mu.Unlock()

				confidence, err := analyzer.GetFaceSimilarity(bucket, pp, photo)
				if err != nil {
					resCh <- &matchingResult{err: err, img1: pp, img2: photo}
					continue
				}
				if confidence > 0 {
					mu.Lock()
					matchedFiles[pp] = true
					matchedFiles[photo] = true
					mu.Unlock()
					resCh <- &matchingResult{
						matched:    true,
						img1:       pp,
						img2:       photo,
						confidence: confidence,
					}
					return
				}
			}
		}(passport)
	}

	go func() {
		wg.Wait()
		close(resCh)
	}()

	var matchingResults []*matchingResult
	for m := range resCh {
		if m.err != nil {
			log.Error().Str("passport", m.img1).Str("photo", m.img2).Err(m.err).Msg("failed to match pair")
			continue
		}
		if m.matched {
			matchingResults = append(matchingResults, m)
		}
	}

	sort.SliceStable(matchingResults, func(i, j int) bool {
		return matchingResults[i].confidence > matchingResults[j].confidence
	})

	var matchedPassports []*MatchedPassport
	for _, match := range matchingResults {
		matchedPassports = append(matchedPassports, &MatchedPassport{
			PassportImage: match.img1,
			PersonalImage: match.img2,
			Confidence:    match.confidence,
		})
	}

	var unMatchedFiles []string
	for _, k := range passports {
		if !matchedFiles[k] {
			unMatchedFiles = append(unMatchedFiles, k)
		}
	}

	for _, k := range personalPhotos {
		if !matchedFiles[k] {
			unMatchedFiles = append(unMatchedFiles, k)
		}
	}

	return matchedPassports, unMatchedFiles, nil
}
