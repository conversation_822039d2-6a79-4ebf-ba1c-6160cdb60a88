package main

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"image"
	"image/draw"
	"image/jpeg"
	"image/png"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode"

	adaws "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/mrz"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/aws/aws-sdk-go/service/textract"
	"github.com/disintegration/gift"
	"github.com/go-resty/resty/v2"
	"github.com/karmdip-mi/go-fitz"
	"github.com/muesli/smartcrop"
	"github.com/muesli/smartcrop/nfnt"
	uuid "github.com/satori/go.uuid"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
	"golang.org/x/text/transform"
	"golang.org/x/text/unicode/norm"
)

func UploadBufferToS3(buffer []byte, bucketName, objectKey string) (string, error) {
	var awsJSON = gjson.ParseBytes([]byte(os.Getenv("ad_aws")))
	region := aws.String(awsJSON.Get("region").String())

	sess, err := session.NewSession(&aws.Config{
		Region: region,
	})
	if err != nil {
		return "", err
	}

	uploader := s3manager.NewUploader(sess)

	_, err = uploader.Upload(&s3manager.UploadInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(objectKey),
		Body:   bytes.NewReader(buffer),
		ACL:    aws.String("public-read"), // Set ACL to public-read
	})
	if err != nil {
		return "", err
	}
	// Get the public URL of the uploaded object
	publicURL := fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", bucketName, awsJSON.Get("region").String(), objectKey)

	// Return the public URL
	return publicURL, nil
}

func ConvertPDFToImages(pdfBuffer []byte) ([][]byte, error) {
	doc, err := fitz.NewFromReader(bytes.NewBuffer(pdfBuffer))
	if err != nil {
		return nil, err
	}
	defer doc.Close()

	numPages := doc.NumPage()
	result := make([][]byte, numPages)
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, numPages)

	for i := 0; i < numPages; i++ {
		wg.Add(1)
		go func(pageNum int) {
			defer wg.Done()

			img, err := doc.Image(pageNum)
			if err != nil {
				errors[pageNum] = fmt.Errorf("error processing page %d: %v", pageNum, err)
				return
			}

			var imageBuffer bytes.Buffer
			err = png.Encode(&imageBuffer, img)
			if err != nil {
				errors[pageNum] = fmt.Errorf("error encoding page %d: %v", pageNum, err)
				return
			}

			mu.Lock()
			result[pageNum] = imageBuffer.Bytes()
			mu.Unlock()
		}(i)
	}

	wg.Wait()

	// Check for errors
	for _, err := range errors {
		if err != nil {
			return nil, err
		}
	}

	return result, nil
}

func ConvertPDFToImage(pdfBuffer []byte) ([]byte, error) {
	doc, err := fitz.NewFromReader(bytes.NewBuffer(pdfBuffer))
	if err != nil {
		return nil, err
	}
	defer doc.Close()

	var pageImages = []image.Image{}
	for i := 0; i < doc.NumPage(); i++ {
		img, err := doc.Image(i)
		if err != nil {
			return nil, err
		}
		pageImages = append(pageImages, img)
	}

	combinedImage := image.NewRGBA(image.Rect(0, 0, pageImages[0].Bounds().Dx(), pageImages[0].Bounds().Dy()*len(pageImages)))

	for i, img := range pageImages {
		yPos := img.Bounds().Dy() * i
		rect := image.Rect(0, yPos, img.Bounds().Dx(), yPos+img.Bounds().Dy())

		draw.Draw(combinedImage, rect, img, image.Point{0, 0}, draw.Src)
	}

	var imageBuffer bytes.Buffer
	err = png.Encode(&imageBuffer, combinedImage)
	if err != nil {
		return nil, err
	}

	return imageBuffer.Bytes(), nil
}

func downloadImage(downloader *adaws.S3Downloader, url string) ([]byte, error) {
	bucket, key, err := utils.UrlToS3BucketAndKey(url)
	if err != nil {
		return nil, err
	}
	buff, err := downloader.DownloadFromS3BucketToBuffer(bucket, key)
	if err != nil {
		return nil, err
	}

	return buff, nil
}

func loadImage(downloader *adaws.S3Downloader, url string) (image.Image, error) {
	bucket, key, err := utils.UrlToS3BucketAndKey(url)
	if err != nil {
		return nil, err
	}
	buff, err := downloader.DownloadFromS3BucketToBuffer(bucket, key)
	if err != nil {
		return nil, err
	}

	if strings.HasSuffix(key, ".pdf") {
		// Convert PDF to image
		imgBytes, err := ConvertPDFToImage(buff)
		if err != nil {
			return nil, err
		}
		img, _, err := image.Decode(bytes.NewReader(imgBytes))
		return img, err
	}
	// Load image directly
	img, _, err := image.Decode(bytes.NewReader(buff))
	return img, err
}

func loadImageOrPDFPages(downloader *adaws.S3Downloader, url string) ([][]byte, error) {
	bucket, key, err := utils.UrlToS3BucketAndKey(url)
	if err != nil {
		return nil, err
	}
	buff, err := downloader.DownloadFromS3BucketToBuffer(bucket, key)
	if err != nil {
		return nil, err
	}

	var result [][]byte
	if strings.HasSuffix(key, ".pdf") {
		// Convert PDF to image
		imgBytes, err := ConvertPDFToImages(buff)
		if err != nil {
			return nil, err
		}
		result = imgBytes
	} else {
		result = append(result, buff)
	}
	return result, nil
}

func loadImageFromURL(url string) (image.Image, error) {
	response, e := http.Get(url)
	if e != nil {
		log.Fatal(e)
	}
	defer response.Body.Close()
	img, _, err := image.Decode(response.Body)
	return img, err
}

func adjustImage(src image.Image) ([]byte, error) {
	// Create a new filter list and add some filters.
	g := gift.New(
		// gift.UnsharpMask(1, 1, 0.4),
		// gift.Grayscale(),
		gift.Resize(500, 0, gift.BoxResampling),
		// gift.CropToSize(1200, 600, gift.BottomLeftAnchor),
	)

	// Create a new image of the corresponding size.
	dst := image.NewRGBA(g.Bounds(src.Bounds()))
	g.Draw(dst, src)

	analyzer := smartcrop.NewAnalyzer(nfnt.NewDefaultResizer())
	topCrop, _ := analyzer.FindBestCrop(dst, dst.Bounds().Dx(), 150)

	croppedimg := dst.SubImage(topCrop)

	finePixelImage := image.NewRGBA(g.Bounds(croppedimg.Bounds()))
	gift.New(
		gift.ColorspaceSRGBToLinear(),
		gift.Brightness(30),
		gift.Contrast(30),
		// gift.Threshold(50),
		// gift.Threshold(50),
		gift.Median(3, true),
		gift.Median(3, true),
		gift.Median(3, true),
		// gift.CropToSize(1200-20, 150-10, gift.CenterAnchor),
	).Draw(finePixelImage, croppedimg)

	// image to buffer
	buf := new(bytes.Buffer)

	// STEP: 1
	// if err := jpeg.Encode(buf, finePixelImage, nil); err != nil {
	// 	return nil, err
	// }

	// STEP: 2
	if err := jpeg.Encode(buf, dst, nil); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

func resizeImage(buff []byte, width int) ([]byte, error) {
	img, _, err := image.Decode(bytes.NewReader(buff))
	if err != nil {
		return nil, err
	}
	g := gift.New(
		gift.Resize(width, 0, gift.LanczosResampling),
	)
	dst := image.NewRGBA(g.Bounds(img.Bounds()))
	g.Draw(dst, img)

	buf := new(bytes.Buffer)
	if err := jpeg.Encode(buf, dst, nil); err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}
func adjustImage2(src image.Image) ([]byte, error) {
	g := gift.New(
		gift.Grayscale(),
		gift.ResizeToFill(0, 500, gift.LanczosResampling, gift.CenterAnchor),
		gift.Median(3, true),
	)
	dst := image.NewRGBA(g.Bounds(src.Bounds()))
	g.Draw(dst, src)

	buf := new(bytes.Buffer)

	if err := jpeg.Encode(buf, dst, nil); err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

func imageToTextWithGoogleVision(buff []byte) ([]string, error) {
	sEnc := base64.StdEncoding.EncodeToString(buff)
	client := &http.Client{}
	url := "https://vision.googleapis.com/v1/images:annotate?key=AIzaSyB26MFJHe7SMNyRBBUoKRgx1uo8onuQ9TU"
	payload := map[string]any{
		"requests": []map[string]any{
			{
				"features": []map[string]any{
					{
						"maxResults": 10,
						"type":       "TEXT_DETECTION",
					},
				},
				"image": map[string]any{
					"content": sEnc,
				},
				"imageContext": map[string]any{
					"languageHints": "en",
				},
			},
		},
	}
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequest("POST", url, bytes.NewReader(payloadBytes))
	if err != nil {
		return nil, err
	}

	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	fullText := gjson.ParseBytes(body).Get("responses.0.fullTextAnnotation.text").String()
	lines := strings.Split(fullText, "\n")

	return lines, err
}

func imageToTextWithTextExtract(textExtract *textract.Textract, buff []byte) ([]string, string, error) {
	txData, err := textExtract.AnalyzeID(&textract.AnalyzeIDInput{
		DocumentPages: []*textract.Document{
			{
				Bytes: buff,
			},
		},
	})
	if err != nil {
		return nil, "", fmt.Errorf("failed to analyze document: %w", err)
	}

	if len(txData.IdentityDocuments) == 0 {
		return nil, "", fmt.Errorf("no identity documents found in the image")
	}

	var lines []string
	var issueDate string

	for _, field := range txData.IdentityDocuments[0].IdentityDocumentFields {
		fieldJ := utils.StructToJSON(field)
		fieldType := fieldJ.Get("Type.Text").String()

		if fieldType == "MRZ_CODE" {
			if val := fieldJ.Get("ValueDetection.Text").String(); val != "" {
				// Split MRZ into lines and validate
				mrzLines := strings.Split(val, "\n")
				for _, line := range mrzLines {
					if len(strings.TrimSpace(line)) > 0 {
						lines = append(lines, strings.TrimSpace(line))
					}
				}
			}
		}

		if fieldType == "DATE_OF_ISSUE" {
			if normalizedDate := fieldJ.Get("ValueDetection.NormalizedValue.Value").String(); normalizedDate != "" {
				issueDate = normalizedDate + "Z"
			}
		}
	}
	if len(lines) == 2 && len(lines[0]) == 44 && len(lines[1]) == 44 {
		return lines, issueDate, nil
	}

	lines, err = imageToTextWithGoogleVision(buff)
	if err != nil {
		return nil, "", fmt.Errorf("fallback to Google Vision failed: %w", err)
	}

	return lines, "", nil
}

func imageToTextOnlyWithTextExtract(textExtract *textract.Textract, buff []byte) ([]string, string, error) {
	txData, err := textExtract.AnalyzeID(&textract.AnalyzeIDInput{
		DocumentPages: []*textract.Document{
			{
				Bytes: buff,
			},
		},
	})
	if err != nil {
		return nil, "", err
	}

	var lines []string
	fmt.Println(utils.StructToJSON(txData.IdentityDocuments[0].IdentityDocumentFields).Raw)
	for _, field := range txData.IdentityDocuments[0].IdentityDocumentFields {
		fieldJ := utils.StructToJSON(field)
		if val := fieldJ.Get("ValueDetection.Text").String(); val != "" {
			lines = append(lines, strings.Split(val, "\n")...)
		}
	}

	if len(lines) == 0 {
		lines, err = imageToTextWithGoogleVision(buff)
		if err != nil {
			return nil, "", err
		}
		return lines, "", nil
	}
	return lines, "", nil

}

type Document struct {
	Image          string         `json:"image,omitempty"`
	Format         string         `json:"format"`
	Fields         map[string]any `json:"fields"`
	InternalFields map[string]any `json:"internal_fields"`
	Valid          bool           `json:"valid"`
}

func textToMRZ(lines []string) (*Document, string, string, error) {
	var IsLetter = regexp.MustCompile(`[a-zA-Z]`).MatchString
	// Regex contain special charactor
	var IsSpecial = regexp.MustCompile(`[<く]`).MatchString

	var IsMRZStr = regexp.MustCompile(`^[A-Z0-9]{40,44}$`).MatchString

	// Filter string's length at least 20 && len(s) < 50
	newLines := funk.FilterString(lines, func(s string) bool {
		return (len(s) > 15 && IsLetter(s) && IsSpecial(s))
	})

	if len(newLines) == 1 {
		newLines = funk.FilterString(lines, func(s string) bool {
			return (len(s) > 15 && IsLetter(s) && IsSpecial(s)) || IsMRZStr(s)
		})
	}

	if len(newLines) < 2 {
		return nil, "", "", fmt.Errorf("can not detect mrz")
	}
	if !strings.Contains(newLines[len(newLines)-2], "<") && !strings.Contains(newLines[len(newLines)-1], "<") {
		return nil, "", "", fmt.Errorf("can not detect mrz")
	}

	// AI OCR correction
	for i := 0; i < len(newLines); i++ {
		newLines[i] = strings.ReplaceAll(newLines[i], " ", "")
		newLines[i] = strings.ReplaceAll(newLines[i], "く", "<")
		newLines[i] = strings.ReplaceAll(newLines[i], "¢", "")
		ccc := regexp.MustCompile(`C{2,}`).FindStringSubmatch(newLines[i]) // Some time C is more than 2 with wrong detect C instead of <
		if len(ccc) > 0 {
			newLines[i] = strings.ReplaceAll(newLines[i], ccc[0], strings.Repeat("<", len(ccc[0])))
		}

		// Remove Vietnamese characters
		newLines[i] = utils.ParseVietnameseText(newLines[i])
	}

	text1 := newLines[len(newLines)-2] + "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<"
	text2 := newLines[len(newLines)-1] + "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<"

	text1 = text1[0:44]
	// Replace F => P
	runes := []rune(text1)
	if runes[0] == 'F' {
		runes[0] = 'P'
	}
	if runes[0] == '<' {
		runes = append([]rune{'P'}, runes...)
	}

	text1 = string(runes)

	text1 = strings.ReplaceAll(text1, "0", "O")

	text1 = text1[0:44]
	text2 = text2[0:44]

	doc1, err := mrz.Parse(strings.Join([]string{text1, text2}, "\n"))
	if err != nil {
		return nil, "", "", err
	}
	// Upside down check
	doc2, err := mrz.Parse(strings.Join([]string{text2, text1}, "\n"))
	if err != nil {
		return nil, "", "", err
	}
	var doc *mrz.Document
	if doc2.ValidCount() > doc1.ValidCount() {
		doc = doc2
		text1, text2 = text2, text1
	} else {
		doc = doc1
	}

	newDoc := &Document{
		Format: doc.Format,
		Fields: map[string]any{},
		Valid:  doc.Valid,
	}
	for k, v := range doc.Fields {
		switch k {
		case "documentNumber":
			newDoc.Fields["passport_number"] = v
		case "lastName":
			newDoc.Fields["surname"] = v
		case "firstName":
			newDoc.Fields["given_name"] = v
		case "birthDate":
			t, _ := time.Parse("060102", v)
			if t.Year() > time.Now().Year() {
				t = t.AddDate(-100, 0, 0)
			}
			newDoc.Fields["date_of_birth"] = t.Format("2006-01-02T00:00:00Z")
		case "sex":
			newDoc.Fields["gender"] = v
		case "expirationDate":
			t, _ := time.Parse("060102", v)
			newDoc.Fields["expiration_date"] = t.Format("2006-01-02T00:00:00Z")
		case "issuingState":
			newDoc.Fields["issuing_authority"] = v
		default:
			newDoc.Fields[utils.Underscore(k)] = v
		}

	}

	return newDoc, text1, text2, err
}

func saveImage(filename string, img image.Image) {
	f, err := os.Create(filename)
	if err != nil {
		log.Fatalf("os.Create failed: %v", err)
	}
	defer f.Close()
	err = jpeg.Encode(f, img, nil)
	if err != nil {
		log.Fatalf("png.Encode failed: %v", err)
	}
}

type Rule struct {
	Active          string          `json:"active"`
	Country         string          `json:"country"`
	IssueDateFormat string          `json:"issue_date_format"`
	IssueDateRules  []IssueDateRule `json:"issue_date_rule"`
}

type IssueDateRule struct {
	Operation string `json:"operation"`
	Value     string `json:"value"`
	Unit      string `json:"unit"`
	Condition string `json:"condition"`
}

func getIssueDateByRule(rules []*Rule, nationality, expireDateStr, birthDateStr string) (string, error) {
	// validate input
	if nationality == "" || expireDateStr == "" || birthDateStr == "" {
		return "", nil
	}
	expireDate, err := time.Parse(time.RFC3339, expireDateStr)
	if err != nil {
		return "", err
	}
	birthDate, err := time.Parse(time.RFC3339, birthDateStr)
	if birthDate.Unix() > time.Now().Unix() {
		birthDate = birthDate.AddDate(-100, 0, 0)
	}
	if err != nil {
		return "", err
	}

	if err != nil {
		return "", err
	}

	// find rule of by nationality
	var activeRules []*Rule
	for _, rule := range rules {
		if rule.Country == nationality && rule.Active == "true" {
			activeRules = append(activeRules, rule)
		}
	}
	if activeRules == nil {
		return "", nil
	}

	issueDate := expireDate
	// expired  {operation}{value}{unit} - dob  {condition} == true , So => issue =  expired {operation} {value} {unit}
	for _, rule := range activeRules {
		for _, item := range rule.IssueDateRules {
			expiredOperationValueUnit := findDateByCondition(expireDate, item.Operation, item.Unit, item.Value)
			expiredOperationValueUnitMinusDOB := expiredOperationValueUnit.Sub(birthDate)
			fmt.Println(expiredOperationValueUnit)
			fmt.Println(birthDate)
			fmt.Println(expiredOperationValueUnitMinusDOB)
			if item.Condition == "" {
				issueDate = findDateByCondition(issueDate, item.Operation, item.Unit, item.Value)
			} else {
				matches := regexp.MustCompile(`([><=]{1,2})([\d]+)`).FindAllStringSubmatch(item.Condition, -1) // validate condition

				if len(matches) == 1 && len(matches[0]) == 3 {
					conditionMark := matches[0][1]
					conditionValue, _ := strconv.Atoi(matches[0][2])

					isValid := false
					switch conditionMark {
					case ">":
						isValid = expiredOperationValueUnitMinusDOB > time.Duration(conditionValue)*365*24*time.Hour
					case ">=":
						isValid = expiredOperationValueUnitMinusDOB >= time.Duration(conditionValue)*365*24*time.Hour
					case "<=":
						isValid = expiredOperationValueUnitMinusDOB <= time.Duration(conditionValue)*365*24*time.Hour
					case "<":
						isValid = expiredOperationValueUnitMinusDOB < time.Duration(conditionValue)*365*24*time.Hour
					case "=":
						isValid = expiredOperationValueUnitMinusDOB == time.Duration(conditionValue)*365*24*time.Hour
					}
					if isValid {
						issueDate = expiredOperationValueUnit
					}
				}

			}
		}

	}

	if issueDate == expireDate {
		return "", nil
	}

	return issueDate.Format(dateFormat), nil
}

func findDateByCondition(date time.Time, operation, unit, value string) time.Time {
	num, _ := strconv.Atoi(operation + value)
	switch unit {
	case "d":
		date = date.AddDate(0, 0, num)
	case "M":
		date = date.AddDate(0, num, 0)
	case "y":
		date = date.AddDate(num, 0, 0)
	}
	return date
}

func normalizeVietnamese(s string) string {
	// Manually replace Vietnamese-specific characters
	replacements := map[string]string{
		"đ": "d", "Đ": "D",
	}
	for old, new := range replacements {
		s = strings.ReplaceAll(s, old, new)
	}

	// Remove diacritics
	t := transform.Chain(norm.NFD, transform.RemoveFunc(func(r rune) bool {
		return unicode.Is(unicode.Mn, r) // Mn: Mark, nonspacing
	}), norm.NFC)
	result, _, _ := transform.String(t, s)
	return result
}

func imageBufferToText(buff []byte) string {
	lines, err := imageToTextWithGoogleVision(buff)
	if err != nil {
		return err.Error()
	}
	return strings.Join(lines, "\n")
}

// func getPassportByAIChat(buff []byte, ext string) map[string]any {
// 	url := "https://api.blackbox.ai/api/upload"
// 	url2 := "https://api.blackbox.ai/api/chat"

// 	messageID := uuid.NewV4().String()
// 	userID := uuid.NewV4().String()

// 	resp, err := resty.New().R().
// 		SetFileReader("image", uuid.NewV4().String()+ext, bytes.NewReader(buff)).
// 		SetFormData(map[string]string{
// 			"fileName": uuid.NewV4().String() + ext,
// 			"userId":   userID,
// 		}).
// 		Post(url)
// 	if err != nil {
// 		log.Fatal(err)
// 	}
// 	fmt.Println(string(resp.Body()))

// 	prompt := fmt.Sprintf(`FILE:BB
// $#$
// %s
// $#$. Base on file. Extract the following fields:
// 	- issue_date (YYYY-MM-DD)
// 	- mrz_line1  (make sure it correct passport mrz format with 44 chars)
// 	- mrz_line2  (make sure it correct passport mrz format with 44 chars)
// 	Please provide the extracted data as a JSON object, do not include any additional explanations in the response.
// 	Only an json`, gjson.ParseBytes(resp.Body()).Get("response").String())
// 	resp2, err := resty.New().R().
// 		SetBody(map[string]any{
// 			"messages": []map[string]any{
// 				{
// 					"id":      messageID,
// 					"content": prompt,
// 					"role":    "user",
// 				},
// 			},
// 			"id":                messageID,
// 			"previewToken":      nil,
// 			"userId":            userID,
// 			"codeModelMode":     true,
// 			"agentMode":         map[string]any{},
// 			"trendingAgentMode": map[string]any{},
// 			"isMicMode":         false,
// 			"isChromeExt":       false,
// 			"githubToken":       nil,
// 			"clickedAnswer2":    false,
// 			"clickedAnswer3":    false,
// 			"visitFromURL":      nil,
// 		}).
// 		Post(url2)
// 	if err != nil {
// 		log.Fatal(err)
// 	}
// 	fmt.Println(string(resp2.Body()))
// 	re := regexp.MustCompile(`\{([^{}]+)\}`)
// 	// Find all matches
// 	matches := re.FindAllStringSubmatch(string(resp2.Body()), -1)

//		var result map[string]any
//		rawJSON := utils.ParseVietnameseText(matches[0][0])
//		json.Unmarshal([]byte(rawJSON), &result)
//		for _, key := range []string{"issue_date"} {
//			if val := result[key]; val != nil {
//				result[key] = cast.ToString(val) + "T00:00:00Z"
//			}
//		}
//		return result
//	}
func retry[T any](operation func() (T, error), maxAttempts int) (T, error) {
	var result T
	var err error

	for attempt := 1; attempt <= maxAttempts; attempt++ {
		result, err = operation()
		if err == nil {
			return result, nil
		}

		if attempt == maxAttempts {
			break
		}
		time.Sleep(time.Second * time.Duration(attempt))
	}
	return result, fmt.Errorf("failed after %d attempts: %v", maxAttempts, err)
}

func getDataByImageFromAIChat(buff []byte, promptStr string) map[string]any {
	ext := detectFileExtension(buff)

	buffText := imageBufferToText(buff)
	fmt.Println(buffText)
	text, err := callClaudeAPI(fmt.Sprintf(`%s. %s`, buffText, promptStr), "", getMimeType(ext))
	if err != nil {
		log.Printf("Error calling Claude API: %v", err)
		return nil
	}

	fmt.Println(text)
	var result map[string]any
	matches := regexp.MustCompile(`\{([^{}]+)\}`).FindAllStringSubmatch(text, -1)
	if len(matches) == 0 {
		return nil
	}
	if len(matches[0]) == 0 {
		return nil
	}
	json.Unmarshal([]byte(utils.ParseVietnameseText(matches[0][0])), &result)
	result["registration_code"] = extractRegistrationCode(buffText)

	return result
}

func getEvisaDataByAIChat(imageBuff []byte) map[string]any {
	result := getDataByImageFromAIChat(imageBuff, `.Based on the above data and your knowledge, give me only a JSON object with the following fields:
		registration_code,
		full_name,
		date_of_birth (YYYY-MM-DD) (in file it has format DD/MM/YYYY),
		entry_date (YYYY-MM-DD) (in file it has format DD/MM/YYYY),
		exit_date (YYYY-MM-DD) (in file it has format DD/MM/YYYY),
		passport_number,
		passport_expired_date (YYYY-MM-DD) (in file name Date of expiry/THỜI HẠN ĐẾN it has format DD/MM/YYYY),
		nationality (country ISO3 format),
		`)

	for _, key := range []string{"date_of_birth", "entry_date", "exit_date", "passport_expired_date"} {
		if val := cast.ToString(result[key]); val != "" {
			result[key] = val + "T00:00:00Z"
		}
	}
	return result
}

func getPassportMRZByAIChat(imageBuff []byte) map[string]any {
	result := getDataByImageFromAIChat(imageBuff, `.Based on the above data of passport image front, follow rules:
Give me only a JSON object with the following fields:
		mrz_line1 (at botton of passport, 44 chars, no space only A-Z 0-9 and <):,
		mrz_line2 (at botton of passport, lower line, 44 chars, no space only A-Z 0-9 and <):,
		issue_date (YYYY-MM-DD),
		`)

	for _, key := range []string{"issue_date"} {
		if val := cast.ToString(result[key]); val != "" {
			result[key] = val + "T00:00:00Z"
		}
	}
	return result
}

func getPassportInfoByAIChat(text string) map[string]any {
	resp2, err := callClaudeAPI(fmt.Sprintf(`%s. %s`, text, `Extract passport data and verify against MRZ information for accuracy.
	Provide a JSON object with the following fields:
	- date_of_birth (format: "YYYY-MM-DD")
	- expiration_date (format: "YYYY-MM-DD")
	- gender ("M" or "F")
	- given_name (base on MRZ first then PP, if it not match, re thinking to give correct)
	- surname (base on PP first then MRZ)
	- issue_date
	- issuing_authority (ISO3 country code)
	- nationality (ISO3 country code)
	- passport_number
	Return only the JSON object without additional explanations.`), "", "")
	if err != nil {
		log.Printf("Error calling Claude API: %v", err)
		return nil
	}
	re := regexp.MustCompile(`\{([^{}]+)\}`)
	matches := re.FindAllStringSubmatch(string(resp2), -1)

	var result map[string]any
	if len(matches) == 0 {
		return nil
	}
	rawJSON := utils.ParseVietnameseText(matches[0][0])
	json.Unmarshal([]byte(rawJSON), &result)

	for _, key := range []string{"issue_date", "date_of_birth", "expiration_date"} {
		if val := cast.ToString(result[key]); val != "" {
			result[key] = val + "T00:00:00Z"
		}
	}
	for _, key := range []string{"given_name", "surname"} {
		result[key] = strings.ReplaceAll(cast.ToString(result[key]), "-", " ")
	}
	return result
}

func getFlightTicketImageByAIChat(dao db.IDao, country string, imageBuff []byte) map[string]any {
	fileContent := imageBufferToText(imageBuff)

	prompt := fmt.Sprintf(`%s, Based on the provided flight information, generate a JSON object representing a flight from airport A to airport B (where B is located in %s country). Disregard any transit or connection airports. Include only the initial departure and final arrival details.

Provide a JSON object with the following fields:

{
  "departure_airport": "XYZ",  // 3-letter IATA code for departure airport
  "departure_airline": "Airline Name",
  "departure_datetime": "YYYY-MM-DD HH:mm",
  "arrival_airport": "ABC",  // 3-letter IATA code for arrival airport in %s country
  "arrival_airline": "Airline Name",
  "arrival_flight_number": "AB-123",
  "estimated_arrival_datetime": "YYYY-MM-DD HH:mm"
}

Ensure all fields are strings and datetime fields follow the specified format.`, fileContent, country, country)
	resp2, err := callClaudeAPI(prompt, "", "")
	if err != nil {
		log.Printf("Error calling Claude API: %v", err)
		return nil
	}

	re := regexp.MustCompile(`\{([^{}]+)\}`)
	matches := re.FindAllStringSubmatch(string(resp2), -1)

	var result map[string]any
	if len(matches) == 0 {
		return nil
	}
	rawJSON := utils.ParseVietnameseText(matches[0][0])
	json.Unmarshal([]byte(rawJSON), &result)

	for key, val := range result {
		if strings.Contains(key, "schedule_time") {
			time, err := time.Parse("2006-01-02 15:04", cast.ToString(val))
			if err == nil {
				result[key] = time.Format(("2006-01-02T15:04:05Z"))
			}
		}
	}

	var wg2 sync.WaitGroup
	for key, val := range result {
		if strings.Contains(key, "airline") {
			wg2.Add(1)
			go func(key string, val any) {
				defer wg2.Done()
				hits := getMeilisearchData("airline", cast.ToString(val))
				if len(hits) > 0 {
					result[key] = hits[0].Get("name").String()
					if key == "arrival_airline" {
						rawFlightNumber := strings.ReplaceAll(cast.ToString(result["arrival_flight_number"]), hits[0].Get("iata").String(), "")
						result["arrival_flight_number"] = hits[0].Get("iata").String() + "-" + strings.Trim(rawFlightNumber, " ")
					}
				}
			}(key, val)
		} else if strings.Contains(key, "airport") {
			wg2.Add(1)
			go func(key string, val any) {
				defer wg2.Done()
				hits := getMeilisearchData("airport", cast.ToString(val))
				for _, hit := range hits {
					if hit.Get("iata").String() == cast.ToString(val) {
						result[key] = hit.Get("name").String()
						return
					}
				}
			}(key, val)
		}
	}

	wg2.Wait()
	return result
}

func getFlightTicketPDFByAIChat(dao db.IDao, country string, imageBuff []byte) map[string]any {
	url := "https://api.blackbox.ai/api/upload"
	url2 := "https://api.blackbox.ai/api/chat"

	messageID := uuid.NewV4().String()
	userID := uuid.NewV4().String()

	var aiMessages []map[string]any
	var wg sync.WaitGroup
	extension := detectFileExtension(imageBuff)
	base64Data := base64.StdEncoding.EncodeToString(imageBuff)

	uploadResp, err := resty.New().R().
		SetFileReader("image", uuid.NewV4().String()+extension, bytes.NewReader(imageBuff)).
		SetFormData(map[string]string{
			"fileName": uuid.NewV4().String() + extension,
			"userId":   userID,
		}).
		Post(url)
	if err != nil {
		log.Printf("Error uploading image: %v", err)
		return nil
	}

	fileText := gjson.ParseBytes(uploadResp.Body()).Get("response").String()
	print(fileText)
	message := map[string]any{
		"id": messageID,
		"content": fmt.Sprintf(`FILE:BB
				$#$
				%s
				$#$`, fileText),
		"role": "user",
		"data": map[string]any{
			"fileText":    fileText,
			"imageBase64": fmt.Sprintf("data:%s;base64,%s", getMimeType(extension), base64Data),
			"title":       fmt.Sprintf(`FilePDF%s`, extension),
		},
	}

	aiMessages = append(aiMessages, message)

	wg.Wait()

	prompt := fmt.Sprintf(`Based on the provided flight information, generate a JSON object representing a flight from airport A to airport B (where B is located in %s country). Disregard any transit or connection airports. Include only the initial departure and final arrival details.

Provide a JSON object with the following fields:
{
  "departure_airport": "XYZ",  // 3-letter IATA code for departure airport
  "departure_airline": "Airline Name",
  "departure_datetime": "YYYY-MM-DD HH:mm",
  "arrival_airport": "ABC",  // 3-letter IATA code for arrival airport in %s country
  "arrival_airline": "Airline Name",
  "arrival_flight_number": "AB-123",
  "estimated_arrival_datetime": "YYYY-MM-DD HH:mm"
}

Ensure all fields are strings and datetime fields follow the specified format.`, country, country)

	aiMessages = append(aiMessages, map[string]any{
		"id":      messageID,
		"content": prompt,
		"role":    "user",
		"data":    map[string]any{},
	})

	resp2, err := resty.New().R().
		SetBody(map[string]any{
			"messages":              aiMessages,
			"id":                    messageID,
			"previewToken":          nil,
			"userId":                nil,
			"codeModelMode":         true,
			"agentMode":             map[string]any{},
			"trendingAgentMode":     map[string]any{},
			"isMicMode":             false,
			"isChromeExt":           false,
			"githubToken":           nil,
			"clickedAnswer2":        false,
			"clickedAnswer3":        false,
			"clickedForceWebSearch": false,
			"maxTokens":             1024,
			"mobileClient":          false,
			"playgroundTemperature": 0.5,
			"playgroundTopP":        0.9,
			"userSelectedModel":     nil,
			"userSystemPrompt":      nil,
			"visitFromDelta":        false,
		}).
		SetHeaders(map[string]string{
			"content-type": "application/json",
			"cookie":       "sessionId=2a4367c9-31e5-463f-a2f2-6a59c0325385; intercom-id-jlmqxicb=6f9030cc-4f47-40fd-8e7f-5ffba66fd38e; intercom-device-id-jlmqxicb=0d142e44-191c-4b0b-9d96-b8a78a1d8060; __Host-authjs.csrf-token=2bcf130d452a9f8b2b519afb502791d76ed6e221fbb6f8acfde4d69ddf4699d6%7Cdaa24b1b1e7f486ac1f51f80d4b1fc6e1cb707327cf08c4304f2e308951a132f; __Secure-authjs.callback-url=https%3A%2F%2Fapi.blackbox.ai; __Secure-authjs.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..Jr7cosG2weUbqJKj.--j9UCbBPh_9uf56piate1Iz8g3mmrLMqDLbg5GIkGmZ87YiLNXm-TbDGWXCy2cUqRQWZn5URRPbgPC5p9hFrI06kUP8yRlC7J-v4PFqDhzwyoKnJBBoofEg6nxo1h36OlVlBdZrekU2KJoQgKTLwdNF4KvuIGg1iJkgN3RlvVtZUJVt0p2dVB0ieZh1jUTGkMPMdJ6owuxnu5CLhwS5h9KgYw_3rCKBw_BR_r5SZX9F3W3mbwrohu-wyzvaDZgEKh2qgM-vXrwcXX9NSY6Zay-tjRSY35q3A-Du_ZjEzXnub0JpnGZ6Qp9hBErzH_4gU_P71DzAPBOGYpmbRuyTj7ZV9YeAFqnzOw_GPZ4qTrre-zHcrSNNd_ICZmfa-Nyie9k43gAnEIPQaXuaYmgPaxuYli1QaVIDYzpaNNzuZxRPm3jQ1gVBxDmbuip0aTlgKpDQ5I1EwdMo9AiUk16QUZ7ON1wtkvyHGtBFzdUpSVrDj57UDQOaM1GxqdcZnC8J.eia66xcZhSC860Tpq-zbMw; intercom-session-jlmqxicb=KzNhV2JlcmFjYURxMW9HbXpTd2g1Szd1VDZyOXlsUG53MCt1NXpsN3ZHVEFoaGIrdWlKeVlDVFBHZHMzUEhobS0tNEVpTUlxVlVacXFPRWVibmFjbitaQT09--1f630c51ec06af71989a46eccd4e4639553a29bf; sessionId=41b29286-732e-4d9e-b173-edd8ea503f17",
			"origin":       "https://api.blackbox.ai",
			"priority":     "u=1, i",
			"referer":      "https://api.blackbox.ai/?model=claude-sonnet-3.5",
			"user-agent":   "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		}).
		Post(url2)
	if err != nil {
		log.Fatal(err)
	}

	test2 := string(resp2.Body())
	fmt.Println(test2)

	re := regexp.MustCompile(`\{([^{}]+)\}`)
	matches := re.FindAllStringSubmatch(string(resp2.Body()), -1)
	if len(matches) == 0 {
		return nil
	}
	var result map[string]any
	rawJSON := utils.ParseVietnameseText(matches[0][0])
	json.Unmarshal([]byte(rawJSON), &result)

	for key, val := range result {
		if strings.Contains(key, "schedule_time") {
			time, err := time.Parse("2006-01-02 15:04", cast.ToString(val))
			if err == nil {
				result[key] = time.Format(("2006-01-02T15:04:05Z"))
			}
		}
	}

	var wg2 sync.WaitGroup
	for key, val := range result {
		if strings.Contains(key, "airline") {
			wg2.Add(1)
			go func(key string, val any) {
				defer wg2.Done()
				hits := getMeilisearchData("airline", cast.ToString(val))
				if len(hits) > 0 {
					result[key] = hits[0].Get("name").String()
					if key == "arrival_airline" {
						rawFlightNumber := strings.ReplaceAll(cast.ToString(result["arrival_flight_number"]), hits[0].Get("iata").String(), "")
						result["arrival_flight_number"] = hits[0].Get("iata").String() + "-" + strings.Trim(rawFlightNumber, " ")
					}
				}
			}(key, val)
		}
		if strings.Contains(key, "airport") {
			wg2.Add(1)
			go func(key string, val any) {
				defer wg2.Done()
				hits := getMeilisearchData("airport", cast.ToString(val))
				for _, hit := range hits {
					if hit.Get("iata").String() == cast.ToString(val) {
						result[key] = hit.Get("name").String()
						return
					}
				}
			}(key, val)
		}
	}

	wg2.Wait()
	return result
}
func detectFileExtension(buff []byte) string {
	// Detect file type based on magic numbers
	fileType := http.DetectContentType(buff)

	switch fileType {
	case "image/jpeg":
		return ".jpg"
	case "image/png":
		return ".png"
	case "application/pdf":
		return ".pdf"
	default:
		return ".unknown"
	}
}

func getMimeType(extension string) string {
	switch extension {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".pdf":
		return "application/pdf"
	default:
		return "application/octet-stream"
	}
}

func getMeilisearchData(index, name string) []gjson.Result {
	// url := gjson.Parse(os.Getenv("ad_meilisearch")).Get("host").String()
	url := "https://meilisearch.ariadirectcorp.com/"

	resp, err := resty.New().R().
		SetBody(map[string]any{
			"q":                     name,
			"attributesToHighlight": []string{"*"},
			"limit":                 1,
		}).
		Post(fmt.Sprintf("%s/indexes/%s/search", url, index))
	if err != nil {
		log.Fatal(err)
		return nil
	}
	hits := gjson.ParseBytes(resp.Body()).Get("hits").Array()

	return hits
}

func getDriverLicenseByAIChat(buff []byte) map[string]any {
	text := imageBufferToText(buff)

	prompt := `This is data from the driver license after OCR: ` + text + `. Extract the following fields:
	- address
	- city
	- country (alpha3 format -  3 chars)
	- date_of_birth (ISO date string)
	- date_of_issue (ISO date string)
	- document_number
	- endorsements
	- expiration_date (ISO date string)
	- eye_color
	- given_name
	- hair_color
	- middle_name
	- restrictions
	- sex (M or F)
	- state
	- surname
	- height
	- weight
	- zip_code
	Base on data you known and above data.
	ISO date string format like "2024-01-01T00:00:00Z".Answer me as json result only.
	Do not give any additional explanations in the response.`
	respText, _ := callClaudeAPI(prompt, "", "text/plain")

	pattern := `\{(?:[^{}])*\}`
	re := regexp.MustCompile(pattern)

	// Find the JSON string in the input text
	match := re.FindString(strings.ReplaceAll(respText, `\_`, `_`))
	fmt.Println(match)
	var result map[string]any
	json.Unmarshal([]byte(match), &result)
	return result
}

func getIDCardByAIChat(buff []byte) map[string]any {
	text := imageBufferToText(buff)

	prompt := `This is data from the id card (căn cước công dân) after OCR: ` + text + `. Extract the following fields:
	- full_name (Họ và tên)
	- date_of_birth (ngày sinh as ISO date string)
	- sex (M or F)
	- birth_place (Nơi sinh)
	- address (Địa chỉ thường trú)
	Please provide the extracted data as a JSON object with above keys i had definited. Return ISO date string format like "2024-01-01T00:00:00Z". Do not include any additional explanations in the response.`

	respText, _ := callClaudeAPI(prompt, "", "text/plain")

	pattern := `\{(?:[^{}])*\}`
	re := regexp.MustCompile(pattern)

	// Find the JSON string in the input text
	match := re.FindString(strings.ReplaceAll(respText, `\_`, `_`))
	fmt.Println(match)
	var result map[string]any
	json.Unmarshal([]byte(match), &result)
	return result
}

func getFlightTicketImageByClaudeAIChat(dao db.IDao, country string, imageBuff []byte) map[string]any {
	extension := detectFileExtension(imageBuff)
	base64Data := base64.StdEncoding.EncodeToString(imageBuff)

	prompt := fmt.Sprintf(`Based on the provided flight information in the image, generate a JSON object representing a flight from airport A to airport B (where B is located in %s country). Disregard any transit or connection airports. Include only the initial departure and final arrival details.

Provide a JSON object with the following fields:

{
  "departure_airport": "XYZ",  // 3-letter IATA code for departure airport
  "departure_airline": "Airline Name",
  "departure_datetime": "YYYY-MM-DD HH:mm",
  "arrival_airport": "ABC",  // 3-letter IATA code for arrival airport in %s country
  "arrival_airline": "Airline Name",
  "arrival_flight_number": "AB123",
  "estimated_arrival_datetime": "YYYY-MM-DD HH:mm"
}

Ensure all fields are strings and datetime fields follow the specified format.`, country, country)

	// Call Claude API
	claudeResponse, err := callClaudeAPI(prompt, base64Data, getMimeType(extension))
	if err != nil {
		log.Printf("Error calling Claude API: %v", err)
		return nil
	}

	// Parse Claude response
	var result map[string]any
	err = json.Unmarshal([]byte(claudeResponse), &result)
	if err != nil {
		log.Printf("Error parsing Claude response: %v", err)
		return nil
	}

	// Process datetime fields
	for key, val := range result {
		if strings.Contains(key, "datetime") {
			time, err := time.Parse("2006-01-02 15:04", cast.ToString(val))
			if err == nil {
				result[key] = time.Format("2006-01-02T15:04:05Z")
			}
		}
	}

	// Process airline and airport data
	var wg sync.WaitGroup
	for key, val := range result {
		if strings.Contains(key, "airline") {
			wg.Add(1)
			go func(key string, val any) {
				defer wg.Done()
				hits := getMeilisearchData("airline", cast.ToString(val))
				if len(hits) > 0 {
					result[key] = hits[0].Get("name").String()
					if key == "arrival_airline" {
						rawFlightNumber := strings.ReplaceAll(cast.ToString(result["arrival_flight_number"]), hits[0].Get("iata").String(), "")
						result["arrival_flight_number"] = hits[0].Get("iata").String() + "-" + strings.Trim(rawFlightNumber, " ")
					}
				}
			}(key, val)
		} else if strings.Contains(key, "airport") {
			wg.Add(1)
			go func(key string, val any) {
				defer wg.Done()
				hits := getMeilisearchData("airport", cast.ToString(val))
				for _, hit := range hits {
					if hit.Get("iata").String() == cast.ToString(val) {
						result[key] = hit.Get("name").String()
						return
					}
				}
			}(key, val)
		}
	}

	wg.Wait()
	return result
}

func callClaudeAPI(prompt, base64Data, mimeType string) (string, error) {
	// Replace this with the actual Claude API endpoint and authentication
	claudeAPIURL := "https://api.anthropic.com/v1/messages"
	claudeAPIKey := os.Getenv("CLAUDE_API_KEY")
	if claudeAPIKey == "" {
		claudeAPIKey = "************************************************************************************************************"
	}

	messages := []map[string]any{
		{
			"role": "user",
			"content": []map[string]any{
				{
					"type": "text",
					"text": prompt,
				},
			},
		},
	}
	if base64Data != "" {
		messages = []map[string]any{
			{
				"role": "user",
				"content": []map[string]any{
					{
						"type": "text",
						"text": prompt,
					},
					{
						"type": "image",
						"source": map[string]string{
							"type":       "base64",
							"media_type": mimeType,
							"data":       base64Data,
						},
					},
				},
			},
		}
	}

	requestBody := map[string]any{
		"model":      "claude-3-7-sonnet-20250219",
		"messages":   messages,
		"max_tokens": 1024,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return "", err
	}

	req, err := http.NewRequest("POST", claudeAPIURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-api-key", claudeAPIKey)
	req.Header.Set("anthropic-version", "2023-06-01")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	var responseData map[string]any
	err = json.Unmarshal(body, &responseData)
	if err != nil {
		return "", err
	}

	fmt.Println(string(body))
	content, ok := responseData["content"].([]any)
	if !ok || len(content) == 0 {
		return "", fmt.Errorf("unexpected response format")
	}

	firstContent, ok := content[0].(map[string]any)
	if !ok {
		return "", fmt.Errorf("unexpected content format")
	}

	text, ok := firstContent["text"].(string)
	if !ok {
		return "", fmt.Errorf("text not found in content")
	}

	return text, nil
}
