package main

import (
	"context"
	"net/http"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"

	"github.com/gin-gonic/gin"
)

type parseFlightTicketReq struct {
	Country string   `json:"country"`
	Images  []string `json:"images"`
}

func ParseFlightTicket(c *gin.Context) {
	// Create a context with a 1-minute timeout
	ctx, cancel := context.WithTimeout(c.Request.Context(), 1*time.Minute)
	defer cancel()

	// Create a channel to signal completion
	done := make(chan bool)

	go func() {
		downloader := middlewares.GetS3Downloader(c)
		dao, err := middlewares.GetVisaDao(c)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			done <- true
			return
		}

		var req parseFlightTicketReq
		if err := c.BindJ<PERSON>N(&req); err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success":       false,
				"error_code":    "err11001",
				"error_message": "Mrz parser error, can not process the image: " + err.<PERSON>rror(),
			})
			done <- true
			return
		}
		if req.Country == "" {
			req.Country = "VNM"
		}

		buff, err := downloadImage(downloader, req.Images[0])
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success":       false,
				"error_code":    "err11002",
				"error_message": "Mrz parser error, can not process the image: " + err.Error(),
			})
			done <- true
			return
		}
		result := map[string]any{}
		if detectFileExtension(buff) == ".pdf" {
			result = getFlightTicketPDFByAIChat(dao, req.Country, buff)
		} else {
			result = getFlightTicketImageByAIChat(dao, req.Country, buff)
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": Document{
				Format: "airline_ticket",
				Fields: result,
				Valid:  true,
			},
		})

		done <- true
	}()

	select {
	case <-done:
		// Function completed normally
		return
	case <-ctx.Done():
		// Timeout occurred
		c.JSON(http.StatusRequestTimeout, gin.H{
			"success":       false,
			"error_code":    "err11003",
			"error_message": "Request timed out after 1 minute",
		})
		return
	}
}
