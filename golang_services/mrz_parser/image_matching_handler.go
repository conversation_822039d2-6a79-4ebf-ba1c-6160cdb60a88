package main

import (
	"fmt"
	"net/http"
	"sort"
	"strings"
	"sync"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
)

var (
	expirationForFiles = 24 * time.Hour
)

type BatchMatchPassportReq struct {
	Bucket    string   `json:"bucket"`
	ImageKeys []string `json:"image_keys"`
	BatchID   string   `json:"batch_id"`
	ProductID int64    `json:"product_id"`
}

type MatchedPassport struct {
	PassportImage string  `json:"passport_image"`
	PersonalImage string  `json:"personal_image"`
	Confidence    float64 `json:"confidence"`
}

type BatchMatchPassportResponse struct {
	MatchedPassports []*MatchedPassport `json:"matched_passports"`
	UnmatchedImages  []string           `json:"unmatched_images"`
}

func MatchPassportURLsWithBatchID(c *gin.Context) {
	ps := middlewares.GetS3Presigner(c)
	downloader := middlewares.GetS3Downloader(c)
	imageAnalyzer := middlewares.GetImageAnalyzer(c)
	bucketMap := utils.GetMapEnv("ad_s3")
	bucket := cast.ToString(bucketMap["ariadirect_prod_applications"])

	images, err := ps.ListObjects(bucket, fmt.Sprintf("batches/%s", c.Param("batch-id")))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	resp, err := batchMatchPassport(downloader, bucket, images, ps, imageAnalyzer)
	if err != nil {
		response.HandleErrResponseWithCode(c, http.StatusBadRequest, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data":    resp,
		"success": true,
	})
}

func batchMatchPassport(downloader *aws.S3Downloader, bucket string, keys []string, presigner *aws.S3Svc, analyzer *aws.RekImageAnalyzer) (*BatchMatchPassportResponse, error) {
	newKeys, err := checkPDFAndConvertToImages(downloader, bucket, keys)
	if err != nil {
		return nil, err
	}

	categorized, err := analyzer.CategorizeImages(bucket, newKeys)
	if err != nil {
		return nil, err
	}

	matched, unmatched, err := matchPassports(bucket, categorized.Passports, categorized.PersonalPhotos, analyzer)
	if err != nil {
		return nil, err
	}

	if err := presignMatchedPassports(bucket, matched, presigner); err != nil {
		return nil, err
	}
	unmatched, err = presignFiles(bucket, unmatched, presigner)
	if err != nil {
		return nil, err
	}
	others, err := presignFiles(bucket, categorized.Others, presigner)
	if err != nil {
		return nil, err
	}
	return &BatchMatchPassportResponse{
		MatchedPassports: matched,
		UnmatchedImages:  append(unmatched, others...),
	}, nil
}

func presignFiles(bucket string, keys []string, presigner *aws.S3Svc) ([]string, error) {
	var wg sync.WaitGroup
	mu := sync.Mutex{}
	res := make([]string, 0, len(keys))
	errCh := make(chan error, len(keys))

	for _, k := range keys {
		wg.Add(1)
		go func(key string) {
			defer wg.Done()
			url, err := presigner.PresignUrl(bucket, key, expirationForFiles)
			if err != nil {
				errCh <- err
				return
			}
			mu.Lock()
			res = append(res, url)
			mu.Unlock()
		}(k)
	}

	wg.Wait()
	close(errCh)

	for err := range errCh {
		if err != nil {
			return nil, err
		}
	}

	return res, nil
}

type matchingResult struct {
	matched    bool
	img1       string
	img2       string
	confidence float64
	err        error
}

func matchPassports(bucket string, passports, personalPhotos []string, analyzer *aws.RekImageAnalyzer) ([]*MatchedPassport, []string, error) {
	var wg sync.WaitGroup
	resCh := make(chan *matchingResult, len(passports)*len(personalPhotos))
	matchedFiles := make(map[string]bool)
	mu := sync.Mutex{}
	sem := make(chan struct{}, 5) // limit to 5 concurrent goroutines

	for _, passport := range passports {
		wg.Add(1)
		go func(pp string) {
			defer wg.Done()

			sem <- struct{}{}
			defer func() { <-sem }()

			mu.Lock()
			if matchedFiles[pp] {
				mu.Unlock()
				return
			}
			mu.Unlock()

			for _, photo := range personalPhotos {
				mu.Lock()
				if matchedFiles[photo] {
					mu.Unlock()
					continue
				}
				mu.Unlock()

				confidence, err := analyzer.GetFaceSimilarity(bucket, pp, photo)
				if err != nil {
					resCh <- &matchingResult{err: err, img1: pp, img2: photo}
					continue
				}
				if confidence > 0 {
					mu.Lock()
					matchedFiles[pp] = true
					matchedFiles[photo] = true
					mu.Unlock()
					resCh <- &matchingResult{
						matched:    true,
						img1:       pp,
						img2:       photo,
						confidence: confidence,
					}
					return
				}
			}
		}(passport)
	}

	go func() {
		wg.Wait()
		close(resCh)
	}()

	var matchingResults []*matchingResult
	for m := range resCh {
		if m.err != nil {
			log.Error().Str("passport", m.img1).Str("photo", m.img2).Err(m.err).Msg("failed to match pair")
			continue
		}
		if m.matched {
			matchingResults = append(matchingResults, m)
		}
	}

	sort.SliceStable(matchingResults, func(i, j int) bool {
		return matchingResults[i].confidence > matchingResults[j].confidence
	})

	var matchedPassports []*MatchedPassport
	var solvedImages []string
	for _, match := range matchingResults {
		if funk.Contains(solvedImages, match.img1) || funk.Contains(solvedImages, match.img2) {
			continue
		}
		matchedPassports = append(matchedPassports, &MatchedPassport{
			PassportImage: match.img1,
			PersonalImage: match.img2,
			Confidence:    match.confidence,
		})
		solvedImages = append(solvedImages, match.img1, match.img2)
	}
	var unMatchedFiles []string

	for _, k := range append(passports, personalPhotos...) {
		if !funk.Contains(solvedImages, k) {
			unMatchedFiles = append(unMatchedFiles, k)
		}
	}

	return matchedPassports, unMatchedFiles, nil
}

func presignMatchedPassports(bucket string, matchedPassports []*MatchedPassport, presigner *aws.S3Svc) error {
	for _, m := range matchedPassports {
		passportUrl, err := presigner.PresignUrl(bucket, m.PassportImage, expirationForFiles)
		if err != nil {
			return err
		}
		photoUrl, err := presigner.PresignUrl(bucket, m.PersonalImage, expirationForFiles)
		if err != nil {
			return err
		}
		m.PassportImage, m.PersonalImage = passportUrl, photoUrl
	}
	return nil
}

func checkPDFAndConvertToImages(downloader *aws.S3Downloader, bucket string, keys []string) ([]string, error) {
	var wg sync.WaitGroup
	mu := sync.Mutex{}
	newKeys := make([]string, 0, len(keys))
	errCh := make(chan error, len(keys))

	for _, key := range keys {
		wg.Add(1)
		go func(key string) {
			defer wg.Done()
			if strings.HasSuffix(key, ".pdf") {
				buff, err := downloader.DownloadFromS3BucketToBuffer(bucket, key)
				if err != nil {
					errCh <- err
					return
				}
				buff, err = ConvertPDFToImage(buff)
				if err != nil {
					errCh <- err
					return
				}
				newKey := strings.ReplaceAll(key, ".pdf", ".jpg")
				if _, err = UploadBufferToS3(buff, bucket, newKey); err != nil {
					errCh <- err
					return
				}
				mu.Lock()
				newKeys = append(newKeys, newKey)
				mu.Unlock()
			} else {
				mu.Lock()
				newKeys = append(newKeys, key)
				mu.Unlock()
			}
		}(key)
	}

	wg.Wait()
	close(errCh)

	// Check if any errors were encountered during processing
	for err := range errCh {
		if err != nil {
			return nil, err
		}
	}

	return newKeys, nil
}
