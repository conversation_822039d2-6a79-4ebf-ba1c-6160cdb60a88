package main

import (
	"bytes"
	"fmt"
	"image/jpeg"
	"io"
	"log"
	"math"
	"net/http"
	"strings"
	"sync"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"

	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/sdk/version"
	"github.com/aws/aws-sdk-go/service/textract"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"github.com/spf13/cast"
)

const (
	dateFormat = "060102"
)

func VersionHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "OK",
		"message": "This is Aria MRZ-Parser service",
		"version": version.Version,
	})
}

type parsePassportReq struct {
	PassportImage string `json:"passport_image"`
}

func ParsePassport(c *gin.Context) {
	downloader := middlewares.GetS3Downloader(c)

	var req parsePassportReq
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11001",
			"error_message": "Mrz parser error, cannot process the image: " + err.Error(),
		})
		return
	}

	start := time.Now()

	src, err := loadImage(downloader, req.PassportImage)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11001",
			"error_message": "Mrz parser error, cannot process the image: " + err.Error(),
		})
		return
	}
	log.Printf("loadImage took %s", time.Since(start))

	buf := new(bytes.Buffer)
	if err := jpeg.Encode(buf, src, nil); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11001",
			"error_message": "Error encoding image: " + err.Error(),
		})
		return
	}
	buff := buf.Bytes()

	result, err := parsePassportFromBufferV2(c, buff)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11001",
			"error_message": "Mrz parser error, cannot process the image: " + err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, result)
}

func parsePassportFromBufferV2(c *gin.Context, buff []byte) (map[string]any, error) {
	bucketMap := utils.GetMapEnv("ad_s3")
	var wg sync.WaitGroup
	var imageURL string
	var uploadErr error

	wg.Add(1)
	go func() {
		defer wg.Done()
		imageURL, uploadErr = UploadBufferToS3(buff, cast.ToString(bucketMap["ariadirect_prod_passport_images"]), uuid.NewV4().String()+".jpg")
	}()

	var result *Document
	var text1, text2 string

	wg.Add(1)
	go func() {
		defer wg.Done()
		textExtract := middlewares.GetTextExtract(c)
		ocrText, err := textExtract.DetectDocumentText(&textract.DetectDocumentTextInput{
			Document: &textract.Document{
				Bytes: buff,
			},
		})
		if err != nil {
			log.Printf("Error detecting document text: %v", err)
			return
		}

		var textValues []string
		for _, block := range ocrText.Blocks {
			if *block.BlockType == "LINE" {
				textValues = append(textValues, *block.Text)
			}
		}
		ocrTextString := strings.Join(textValues, " ")

		fields := getPassportInfoByAIChat(ocrTextString)
		result = &Document{
			Fields:         fields,
			InternalFields: fields,
			Valid:          true,
		}
	}()

	fmt.Println("Waiting for upload and process to finish")
	wg.Wait()
	fmt.Println("Upload and process finished")

	if uploadErr != nil {
		return nil, uploadErr
	}

	return map[string]any{
		"success": true,
		"data":    result,
		"image":   imageURL,
		"text1":   text1,
		"text2":   text2,
	}, nil
}

func parsePassportFromBuffer(c *gin.Context, buff []byte) (map[string]any, error) {
	bucketMap := utils.GetMapEnv("ad_s3")
	var wg sync.WaitGroup
	var imageURL string
	var uploadErr error

	wg.Add(1)
	go func() {
		defer wg.Done()
		imageURL, uploadErr = UploadBufferToS3(buff, cast.ToString(bucketMap["ariadirect_prod_passport_images"]), uuid.NewV4().String()+".jpg")
	}()

	var result *Document
	var text1, text2 string
	var processErr error

	wg.Add(1)
	go func() {
		defer wg.Done()

		textExtract := middlewares.GetTextExtract(c)
		lines, issueDate, err := imageToTextWithTextExtract(textExtract, buff)

		if err != nil {
			processErr = fmt.Errorf("Mrz parser error, cannot process the image: %w", err)
			return
		}
		rules, ok := c.Get("issue-rule")
		if !ok {
			processErr = fmt.Errorf("missing issue rule")
			return
		}

		result, text1, text2, err = textToMRZ(lines)
		if err != nil {
			processErr = fmt.Errorf("Mrz parser error, cannot process the image: %w", err)
			return
		}

		if issueDate != "" {
			expirationDate := cast.ToString(result.Fields["expiration_date"])

			layout := "2006-01-02T15:04:05Z"
			expDate, _ := time.Parse(layout, expirationDate)

			date01, _ := time.Parse(layout, issueDate)
			date02, _ := time.Parse(layout, switchDayMonth(issueDate))

			dayDiff := func(date1, date2 time.Time) int {
				date1 = time.Date(2000, date1.Month(), date1.Day(), 0, 0, 0, 0, time.UTC)
				date2 = time.Date(2000, date2.Month(), date2.Day(), 0, 0, 0, 0, time.UTC)
				return int(math.Abs(date1.Sub(date2).Hours() / 24))
			}

			diff01 := dayDiff(expDate, date01)
			diff02 := dayDiff(expDate, date02)

			if diff01 < diff02 {
				result.Fields["issue_date"] = date01.Format("2006-01-02T00:00:00Z")
			} else {
				result.Fields["issue_date"] = date02.Format("2006-01-02T00:00:00Z")
			}
		} else {
			fieldJ := utils.StructToJSON(result.Fields)
			if fieldJ.Get("nationality").String() != "" && fieldJ.Get("expiration_date").String() != "" && fieldJ.Get("date_of_birth").String() != "" {
				issueDateRaw, err := getIssueDateByRule(rules.([]*Rule), fieldJ.Get("nationality").String(), fieldJ.Get("expiration_date").String(), fieldJ.Get("date_of_birth").String())
				if err != nil {
					log.Println("Error getting issue date:", err)
				}
				if issueDateRaw != "" {
					t, _ := time.Parse("060102", issueDateRaw)
					result.Fields["issue_date"] = t.Format("2006-01-02T00:00:00Z")
				}
			}
		}

	}()

	fmt.Println("Waiting for upload and process to finish")
	wg.Wait()
	fmt.Println("Upload and process finished")

	if uploadErr != nil {
		return nil, uploadErr
	}

	if processErr != nil {
		return nil, processErr
	}

	return map[string]any{
		"success": true,
		"data":    result,
		"image":   imageURL,
		"text1":   text1,
		"text2":   text2,
	}, nil
}

func switchDayMonth(s string) string {
	t, _ := time.Parse("2006-01-02T15:04:05Z", s)
	return time.Date(t.Year(), time.Month(t.Day()), int(t.Month()), t.Hour(), t.Minute(), t.Second(), t.Nanosecond(), t.Location()).Format("2006-01-02T15:04:05Z")
}

func ParsePassportWithBinary(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   err,
		})
		return
	}

	fileR, err := file.Open()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   err,
		})
		return
	}
	defer fileR.Close()

	buff, err := io.ReadAll(fileR)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   err,
		})
		return
	}

	result, err := parsePassportFromBufferV2(c, buff)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11001",
			"error_message": "Mrz parser error, cannot process the image: " + err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, result)
}
