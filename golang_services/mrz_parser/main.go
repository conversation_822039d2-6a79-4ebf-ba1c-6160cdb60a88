package main

import (
	"log"
	"os"

	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
)

func main() {
	app := cli.NewApp()
	app.Name = "mrz-parser"
	app.Usage = "mrz-parser service for Aria"
	app.Flags = []cli.Flag{
		cli.StringFlag{
			Name:   "port",
			Usage:  "Port the server listens to",
			EnvVar: "PORT",
			Value:  "3000",
		},
		cli.StringFlag{
			Name:     "db-config",
			Usage:    "Env for db config",
			Required: true,
		},
		cli.StringFlag{
			Name:     "ad_endpoint",
			Usage:    "Env for endpoint",
			EnvVar:   "ad_endpoint",
			Required: true,
		},
		flags.LogLevelFlag,
	}
	app.Action = startMRZService()

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}

}
