package main

import (
	"bytes"
	"fmt"
	"image/jpeg"
	"io/ioutil"
	"net/http"
	"regexp"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/openai"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"

	"github.com/aws/aws-sdk-go/service/textract"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

type parseDriverLicenseReq struct {
	Image string `json:"image"`
}

func ParseDriverLicense(c *gin.Context) {
	downloader := middlewares.GetS3Downloader(c)

	var req parseDriverLicenseReq
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11004",
			"error_message": "Mrz parser error, can not process the image" + err.<PERSON>rror(),
		})
		return
	}

	src, err := loadImage(downloader, req.Image)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11004",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}
	buff := new(bytes.Buffer)
	if err := jpeg.Encode(buff, src, nil); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11004",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}

	data := map[string]any{}
	lines, err := imageToTextWithGoogleVision(buff.Bytes())
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11004",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
	}

	prompt := fmt.Sprintf(`This is data from driver license after read ocr: %s . Help me extract data like example of field: address,city,country,date_of_birth ,date_of_issue,document_number,endorsements,expiration_date,eye_color,given_name,hair_color,middle_name,restrictions,sex,state,surname,height, weight,zip_code as key:value. Answer me only as key:value. Dont talk anything else`, strings.Join(lines, "\n"))
	aiResp := openai.GetGPTChatResponseV2(prompt)
	if aiResp != "" {
		aiResp = strings.TrimPrefix(aiResp, "{\"status\":200,\"message\":\"")
		aiResp = strings.TrimSuffix(aiResp, "\"}")
		lines := strings.Split(aiResp, "\\n")
		for _, line := range lines {
			parts := strings.Split(line, ":")
			if len(parts) == 2 {
				key := strings.TrimSpace(parts[0])
				value := strings.TrimSpace(parts[1])
				data[key] = value
			}
		}

		for k, v := range data {
			switch k {
			case "height":
				matches := regexp.MustCompile(`\d+`).FindAllStringSubmatch(cast.ToString(v), 2)
				if len(matches) == 2 {
					data["height"] = matches[0][0] + "ft." + matches[1][0] + "in."
				}
			case "weight":
				matches := regexp.MustCompile(`\d+`).FindStringSubmatch(cast.ToString(v))
				if len(matches) == 1 {
					data["weight"] = matches[0] + "lb"
				}
			case "date_of_birth", "date_of_issue", "expiration_date":
				parsedDate, err := time.Parse("01/02/2006", cast.ToString(v))
				if err == nil {
					data[k] = parsedDate.Format("2006-01-02T15:04:05Z")
				}
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}

func ParseDriverLicenseV2(c *gin.Context) {
	downloader := middlewares.GetS3Downloader(c)

	var req parseDriverLicenseReq
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11004",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}

	src, err := loadImage(downloader, req.Image)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11004",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}
	buff := new(bytes.Buffer)
	if err := jpeg.Encode(buff, src, nil); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11004",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}

	result := getDriverLicenseByAIChat(buff.Bytes())
	if val := cast.ToString(result["height"]); val != "" {
		matches := regexp.MustCompile(`\d+`).FindAllStringSubmatch(val, 2)
		if len(matches) == 2 {
			result["height"] = matches[0][0] + "ft." + matches[1][0] + "in."
		}
	}
	if val := cast.ToString(result["weight"]); val != "" {
		matches := regexp.MustCompile(`\d+`).FindAllStringSubmatch(val, 2)
		if len(matches) == 1 {
			result["weight"] = matches[0][0] + "lb"
		}
	}
	if val := cast.ToString(result["document_number"]); val != "" {
		matches := strings.Split(val, " ")
		if len(matches) == 2 {
			result["document_number"] = matches[1]
		}
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func ParseIDCardV2(c *gin.Context) {
	downloader := middlewares.GetS3Downloader(c)

	var req parseDriverLicenseReq
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11004",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}

	src, err := loadImage(downloader, req.Image)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11004",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}
	buff := new(bytes.Buffer)
	if err := jpeg.Encode(buff, src, nil); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11004",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}

	result := getIDCardByAIChat(buff.Bytes())

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

var mapDriverLicense = map[string]string{
	"ADDRESS":             "address",
	"CITY_IN_ADDRESS":     "city",
	"CLASS":               "class",
	"COUNTY":              "country",
	"DATE_OF_BIRTH":       "date_of_birth",
	"DATE_OF_ISSUE":       "date_of_issue",
	"DOCUMENT_NUMBER":     "document_number",
	"ENDORSEMENTS":        "endorsements",
	"EXPIRATION_DATE":     "expiration_date",
	"FIRST_NAME":          "given_name",
	"LAST_NAME":           "surname",
	"MIDDLE_NAME":         "middle_name",
	"RESTRICTIONS":        "restrictions",
	"STATE_IN_ADDRESS":    "state",
	"SUFFIX":              "suffix",
	"ZIP_CODE_IN_ADDRESS": "zip_code",
	// "VETERAN":             "veteran",
	// "ID_TYPE":             "id_type",
	// "PLACE_OF_BIRTH":      "place_of_birth",
	// "STATE_NAME":          "state_name",
}

func getDriverLicenseFromImage(textExtract *textract.Textract, buff []byte) (map[string]any, error) {
	txData, err := textExtract.AnalyzeID(&textract.AnalyzeIDInput{
		DocumentPages: []*textract.Document{
			{
				Bytes: buff,
			},
		},
	})
	if err != nil {
		return nil, err
	}
	result := map[string]any{}
	for _, field := range txData.IdentityDocuments[0].IdentityDocumentFields {
		fieldJ := utils.StructToJSON(field)
		if name := fieldJ.Get("Type.Text").String(); name != "" {
			if key, ok := mapDriverLicense[name]; ok {
				result[key] = fieldJ.Get("ValueDetection.Text").String()
				if valueType := fieldJ.Get("ValueDetection.NormalizedValue.ValueType").String(); valueType == "Date" {
					result[key] = fieldJ.Get("ValueDetection.NormalizedValue.Value").String() + "Z"
				}
			}

		}
	}

	return result, nil
}

func getDataFromImageByAI(image string) (map[string]string, error) {
	req, _ := http.NewRequest(
		"POST",
		"https://api.ariadirectcorp.com/v1/id-reader/driver_licenses",
		strings.NewReader(`{
			"image": "`+image+`"
		}`))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	result := map[string]string{}
	gjson.ParseBytes(body).Get("data").ForEach(func(key, value gjson.Result) bool {
		result[key.String()] = value.String()
		return true
	})

	return result, nil
}
