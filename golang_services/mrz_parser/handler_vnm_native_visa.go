package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pariz/gountries"
	"github.com/tidwall/gjson"
)

type visaCheckVisaReq struct {
	Image    string            `json:"image"`
	Expected map[string]string `json:"expected"`
	Type     string            `json:"type"`
}

type visaCheckVisaResp struct {
	Expected map[string]string `json:"expected"`
	Result   map[string]string `json:"result"`
	Raw      map[string]string `json:"raw"`
	Matched  map[string]bool   `json:"matched"`
}

var gocountry = gountries.New()

func VisaCheckHandler(c *gin.Context) {
	var req visaCheckVisaReq
	if err := c.BindJ<PERSON>(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11001",
			"error_message": "Can not process the image" + err.Error(),
		})
		return
	}
	var (
		data map[string]string
		err  error
	)

	if req.Type == "vnm_native_visa" || req.Type == "vnm_regular_visa" {
		if data, err = getVNMFromAI(req.Image, req.Type); err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success":       false,
				"error_code":    "err11001",
				"error_message": "getDataFromImageByAI error, can not process the image" + err.Error(),
			})
			return
		}
	}

	resp := visaCheckVisaResp{
		Expected: req.Expected,
		Matched:  map[string]bool{},
	}

	for k, v := range data {
		if val, ok := req.Expected[k]; ok {
			resp.Matched[k] = val == v
		}
	}
	resp.Result = data

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    resp,
	})
}

func getVNMFromAI(image string, documentType string) (map[string]string, error) {
	baseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("api_base_url").String()

	req, _ := http.NewRequest(
		"POST",
		fmt.Sprintf("%s/v1/id-reader/%s", baseURL, documentType),
		strings.NewReader(fmt.Sprintf(`{ "image": "%s" }`, image)))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	result := map[string]string{}
	gjson.ParseBytes(body).Get("data").ForEach(func(key, value gjson.Result) bool {
		result[key.String()] = value.String()
		return true
	})

	return result, nil
}

type parseVisaReq struct {
	Image string `json:"image"`
}

func ParseVisaHandler(c *gin.Context) {
	var req parseVisaReq
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11002",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}
	start := time.Now()

	src, err := loadImageFromURL(req.Image)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11002",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}
	log.Printf("loadImage took %s", time.Since(start))
	start = time.Now()
	buff, err := adjustImage(src)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11002",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}
	log.Printf("adjustImage took %s", time.Since(start))
	if c.Query("debug") == "true" {
		c.Writer.Header().Set("Content-Type", "image/jpeg")
		c.Writer.Header().Set("Content-Length", strconv.Itoa(len(buff)))
		if _, err := c.Writer.Write(buff); err != nil {
			log.Println("unable to write image.")
		}
		return
	}

	start = time.Now()

	lines, err := imageToTextWithGoogleVision(buff)

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11002",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}
	log.Printf("imageToTextWithGoogleVision took %s", time.Since(start))
	start = time.Now()

	result, text1, text2, err := textToMRZ(lines)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11002",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}
	result.Fields["issue_date"] = "" // Wrong issue date

	log.Printf("textToMRZ took %s", time.Since(start))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		// "lines":   lines,
		"text1": text1,
		"text2": text2,
	})
}
