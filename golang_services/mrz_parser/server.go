package main

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"os"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/tidwall/gjson"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func startMRZService() cli.ActionFunc {
	return func(c *cli.Context) error {
		port := c.String("port")

		e, err := newMRZService(c)
		if err != nil {
			return err
		}

		return e.Run(":" + port)
	}
}

func newMRZService(c *cli.Context) (*gin.Engine, error) {
	logger.SetJsonZeroLoggerWithLevel(c.String(flags.LogLevelFlag.Name))
	e := gin.New()
	e.Use(logger.LoggerMW(), logger.CustomLoggerMW(), gzip.Gzip(gzip.DefaultCompression))

	logger.InitGraylogLogger()
	e.Use(logger.GraylogLoggerMiddleware(logger.DefaultRequestLoggerConfig()))

	// cors
	corsConf := cors.DefaultConfig()
	corsConf.AddAllowHeaders("x-access-token", "language")
	corsConf.AllowAllOrigins = true

	e.Use(
		cors.New(corsConf),
	)

	var (
		conn *db.AuroraDB
		err  error
	)

	dbConfigMap := utils.GetMapEnv(c.String("db-config"))
	if host := dbConfigMap["write_host"]; host != nil {
		log.Info().Interface("write_host", host).Msg("Loading DB config from ENV")
		conn, err = db.NewAuroraDBFromConfigMap(dbConfigMap)
	}

	if err != nil {
		return nil, err
	}

	var awsJSON = gjson.ParseBytes([]byte(os.Getenv("ad_aws")))

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(awsJSON.Get("region").String()),
	})

	if err != nil {
		return nil, err
	}

	// googleJSON := gjson.ParseBytes([]byte(os.Getenv("ad_google")))

	e.Use(
		logger.LoggerMW(),
		gin.Recovery(),
		middlewares.VisaDaoMW(conn),
		middlewares.NewS3DownloaderMW(sess),
		middlewares.NewTextExtract(sess),
		middlewares.NewRekognition(sess),
		middlewares.NewS3PresignerMW(sess),
		middlewares.NewImageAnalyzer(sess),
	)

	// e.Use(func(c *gin.Context) {
	// 	c.Set("google-vision-key", googleJSON.Get("vision_key"))
	// })

	rules, err := getIssueDateRules()
	if err != nil {
		return nil, err
	}

	e.Use(func(c *gin.Context) {
		c.Set("issue-rule", rules)
	})

	// load handlers
	LoadHandlers(e)

	return e, nil
}

func getIssueDateRules() ([]*Rule, error) {
	// import the issue date rule file
	url := "https://ad-app-version.s3.us-west-2.amazonaws.com/app-passport-config/passport_rules.json"
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	var rules []*Rule
	err = json.Unmarshal(bytes.TrimPrefix(body, []byte("\xef\xbb\xbf")), &rules)
	return rules, err
}
