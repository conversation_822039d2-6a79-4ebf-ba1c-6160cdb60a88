package main

import (
	"github.com/gin-gonic/gin"
)

func LoadHandlers(r gin.IRouter) gin.IRouter {
	g := r.Group("/v1/mrz-parser")
	{
		g.GET("version", VersionHandler)
		g.GET("status", VersionHandler)
		g.POST("pdf-to-images", PDFToImage)
		g.POST("parse", ParsePassport)
		g.POST("parse-with-binary", ParsePassportWithBinary)
		g.POST("parse-driver-license-front", ParseDriverLicenseV2)
		g.POST("parse-id-card-front", ParseIDCardV2)
		g.POST("parse-green-card-front", ParseGreenCardFront)
		g.POST("parse-green-card-back", ParseGreenCardMRZ)
		g.POST("parse-visa", ParseVisaHandler)
		g.POST("visa-check", VisaCheckHandler)
		g.POST("capcha-solver", CapchaSolverHandler)
		g.POST("capcha-solver-by-binary", CapchaSolverByBinaryHandler)
		g.POST("parse-flight-ticket", ParseFlightTicket)
		g.POST("batches/:batch-id/matches", MatchPassportURLsWithBatchID)
		g.POST("vnm-evisa", ParseVNMEVisa)
		g.POST("vnm-evisa-binnary", ParseVNMEVisaWithBinary)
		g.POST("vnm-evisa-details", ParseVNMEVisaV2)
		g.POST("vnm-evisa-passport-compares", ParseVNMEVisaV3)
	}
	return r
}
