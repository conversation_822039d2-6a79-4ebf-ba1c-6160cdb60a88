package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"os"
	"path"
	"regexp"
	"strings"
	"sync"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"

	"github.com/agnivade/levenshtein"
	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
	"github.com/vmihailenco/msgpack/v5"
)

type parseImageInput struct {
	Image string `json:"image"`
}

func ParseVNMEVisa(c *gin.Context) {
	downloader := middlewares.GetS3Downloader(c)

	var req parseImageInput
	if err := c.BindJSON(&req); err != nil {
		handleError(c, "err11001", "Mrz parser error, can not process the image"+err.Error())
		return
	}

	buff, err := downloadImage(downloader, req.Image)
	if err != nil {
		handleError(c, "err11002", "Failed to download image: "+err.Error())
		return
	}

	processParsedImage(c, buff, path.Ext(strings.Split(req.Image, "?")[0]))
}

func ParseVNMEVisaWithBinary(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		handleError(c, "err11003", "Failed to get form file: "+err.Error())
		return
	}

	fileR, err := file.Open()
	if err != nil {
		handleError(c, "err11004", "Failed to open file: "+err.Error())
		return
	}
	defer fileR.Close()

	buff, err := io.ReadAll(fileR)
	if err != nil {
		handleError(c, "err11005", "Failed to read file: "+err.Error())
		return
	}

	processParsedImage(c, buff, path.Ext(file.Filename))
}

func processParsedImage(c *gin.Context, buff []byte, ext string) {
	if strings.ToUpper(ext) == ".PDF" {
		var err error
		buff, err = ConvertPDFToImage(buff)
		if err != nil {
			handleError(c, "err11006", "Failed to convert PDF to image: "+err.Error())
			return
		}
		ext = ".jpg"
	}

	rawText := imageBufferToText(buff)
	registrationCode := extractRegistrationCode(rawText)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": Document{
			Fields: map[string]any{"registration_code": registrationCode},
			Valid:  true,
		},
	})
}

func extractRegistrationCode(rawText string) string {
	matches := regexp.MustCompile(`E\d{6}[A-Z]{3}[A-Z0-9]{9,12}`).FindStringSubmatch(rawText)
	if len(matches) > 0 {
		return matches[0]
	}
	return ""
}

func handleError(c *gin.Context, errorCode, errorMessage string) {
	c.JSON(http.StatusOK, gin.H{
		"success":       false,
		"error_code":    errorCode,
		"error_message": errorMessage,
	})
}

func ParseVNMEVisaV2(c *gin.Context) {
	downloader := middlewares.GetS3Downloader(c)

	var req parseImageInput
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11001",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}

	buff, _ := downloadImage(downloader, req.Image)
	result := getEvisaDataByAIChat(buff)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": Document{
			Fields: result,
			Valid:  true,
		},
	})
}

type parseImageListInput struct {
	Images []string `json:"images"`
}

type compareEVisaPassport struct {
	RefName         string     `json:"ref_name"`
	RefID           string     `json:"ref_id"`
	MatchedFields   []string   `json:"matched_fields"`
	UnMatchedFields []string   `json:"unmatched_fields"`
	Documents       []Document `json:"documents"`
}

func ParseVNMEVisaV3(c *gin.Context) {
	downloader := middlewares.GetS3Downloader(c)
	// textExtract := middlewares.GetTextExtract(c)
	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11001",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
	}

	var req parseImageListInput
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11001",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}

	maxWorkers := 6
	documentsChan := make(chan Document, len(req.Images))
	semaphore := make(chan struct{}, maxWorkers)

	var wg sync.WaitGroup

	for _, image := range req.Images {
		wg.Add(1)
		go func(img string) {
			defer wg.Done()
			semaphore <- struct{}{}        // Acquire semaphore
			defer func() { <-semaphore }() // Release semaphore

			buff, err := downloadImage(downloader, img)
			if err != nil {
				documentsChan <- Document{
					Image:  img,
					Format: "unknown",
					Valid:  false,
					Fields: map[string]any{},
				}
				return
			}

			if strings.Contains(img, ".pdf") {
				buff, err = ConvertPDFToImage(buff)
				if err != nil {
					documentsChan <- Document{
						Image:  img,
						Format: "unknown",
						Valid:  false,
						Fields: map[string]any{},
					}
					return
				}
			}

			if len(buff) > 1.5*1024*1024 { // Check if buff is larger than 1.5MB
				resizedBuff, err := resizeImage(buff, 1024) // Resize to 1024px width
				if err != nil {
					documentsChan <- Document{
						Image:  img,
						Format: "unknown",
						Valid:  false,
						Fields: map[string]any{},
					}
					return
				}
				buff = resizedBuff
			}

			lines, _ := imageToTextWithGoogleVision(buff)
			text := strings.Join(lines, " ")
			fmt.Println(text)
			if strings.Contains(strings.ToUpper(text), "ELECTRONIC") || strings.Contains(strings.ToUpper(text), "VISA") {
				fields := getEvisaDataByAIChat(buff)
				fixVisaURL := ""
				entryDate := cast.ToString(fields["entry_date"])
				if entryDate != "" {
					serviceType, err := checkVNMEvisaProduct(entryDate)
					if err == nil {
						webBase := gjson.Parse(os.Getenv("ad_endpoint")).Get("web_base_url").String()
						if serviceType == "new_visa" {
							fixVisaURL = fmt.Sprintf(`%s/dashboard/order-services/list-products?service_core_info_country=VNM&service_type=new_visa&service_core_info_nationality=%s&service_core_info_entry_date=%s`, webBase, cast.ToString(fields["nationality"]), entryDate)
						} else {
							fixVisaURL = fmt.Sprintf(`%s/dashboard/order-services/list-products?service_core_info_country=VNM&service_type=new_visa_urgent&tag=evisa,fix_visa_error&service_core_info_nationality=%s&service_core_info_entry_date=%s`, webBase, cast.ToString(fields["nationality"]), entryDate)
						}
					}
				}
				documentsChan <- Document{
					Image: img,
					Fields: map[string]any{
						"full_name": cast.ToString(fields["full_name"]),
						// "gender":          cast.ToString(fields["gender"]),
						"date_of_birth":   cast.ToString(fields["date_of_birth"]),
						"passport_number": cast.ToString(fields["passport_number"]),
						"expiration_date": cast.ToString(fields["passport_expired_date"]),
						"nationality":     cast.ToString(fields["nationality"]),
						"entry_date":      cast.ToString(fields["entry_date"]),
						"suggest_url":     fixVisaURL,
					},
					InternalFields: fields,
					Valid:          true,
					Format:         "evisa",
				}
			} else if strings.Contains(strings.ToUpper(text), "PASS") || strings.Contains(text, "<") {
				fields, _ := parsePassportFromBufferV2(c, buff)
				var mrzDocument *Document
				json.Unmarshal([]byte(utils.StructToJSON(fields).Get("data").Raw), &mrzDocument)
				if mrzDocument == nil {
					documentsChan <- Document{
						Image:  img,
						Format: "unknown",
						Valid:  false,
						Fields: map[string]any{},
					}
					return
				}
				documentsChan <- Document{
					Image: img,
					Fields: map[string]any{
						"full_name":       cast.ToString(mrzDocument.Fields["surname"]) + " " + cast.ToString(mrzDocument.Fields["given_name"]),
						"gender":          cast.ToString(mrzDocument.Fields["gender"]),
						"date_of_birth":   cast.ToString(mrzDocument.Fields["date_of_birth"]),
						"passport_number": cast.ToString(mrzDocument.Fields["passport_number"]),
						"expiration_date": cast.ToString(mrzDocument.Fields["expiration_date"]),
						"nationality":     cast.ToString(mrzDocument.Fields["nationality"]),
					},
					InternalFields: mrzDocument.Fields,
					Valid:          true,
					Format:         "passport",
				}
			} else {
				documentsChan <- Document{
					Image:  img,
					Format: "unknown",
					Valid:  false,
					Fields: map[string]any{},
				}
			}
		}(image)
	}

	go func() {
		wg.Wait()
		close(documentsChan)
	}()

	var documents []Document
	for doc := range documentsChan {
		documents = append(documents, doc)
	}

	result := classifyDocuments(dao, documents)

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"data":      result,
		"documents": documents,
	})
}

func classifyDocuments(dao db.IDao, documents []Document) []compareEVisaPassport {
	evisas := funk.Filter(documents, func(doc Document) bool {
		return doc.Format == "evisa"
	}).([]Document)

	passports := funk.Filter(documents, func(doc Document) bool {
		return doc.Format == "passport"
	}).([]Document)

	unknowns := funk.Filter(documents, func(doc Document) bool {
		return doc.Format == "unknown"
	}).([]Document)

	result := []compareEVisaPassport{}
	for _, evisa := range evisas {
		matchingPassports := funk.Filter(passports, func(passport Document) bool {
			return compareDocuments(evisa, passport)
		}).([]Document)

		var matchedFields, unmatchedFields []string
		if len(matchingPassports) > 0 {
			if cast.ToString(evisa.Fields["gender"]) == "" {
				delete(evisa.Fields, "gender")
				delete(matchingPassports[0].Fields, "gender")
			}
			matchedFields, unmatchedFields = getMatchedFields(evisa, matchingPassports[0])
			item := compareEVisaPassport{
				MatchedFields:   matchedFields,
				UnMatchedFields: unmatchedFields,
				Documents:       append([]Document{evisa}, matchingPassports...),
			}

			packedMsg, _ := msgpack.Marshal(utils.StructToJSON(item).Raw)

			item.RefName = "evisa_check"
			item.RefID = base64.StdEncoding.EncodeToString(packedMsg)
			result = append(result, item)
		}
	}

	if len(unknowns) > 0 {
		result = append(result, compareEVisaPassport{
			MatchedFields:   []string{},
			UnMatchedFields: []string{},
			Documents:       unknowns,
		})
	}

	return result
}

func compareDocuments(doc1, doc2 Document) bool {
	weights := map[string]float64{
		"passport_number": 0.4,
		"date_of_birth":   0.3,
		"full_name":       0.2,
		"expiration_date": 0.1,
	}

	score := 0.0
	for field, weight := range weights {
		val1 := sanitize(doc1.Fields[field])
		val2 := sanitize(doc2.Fields[field])

		tokens1 := strings.Fields(val1)
		tokens2 := strings.Fields(val2)

		if len(tokens1) == 0 || len(tokens2) == 0 {
			continue
		}

		matchCount := 0
		for _, t1 := range tokens1 {
			for _, t2 := range tokens2 {
				if similarity(t1, t2) > 0.5 {
					matchCount++
					break
				}
			}
		}

		similarity := float64(matchCount) / math.Max(float64(len(tokens1)), float64(len(tokens2)))
		score += similarity * weight
	}

	return score >= 0.5
}

func sanitize(v any) string {
	return strings.ToLower(strings.TrimSpace(cast.ToString(v)))
}

func similarity(s1, s2 string) float64 {
	maxLen := math.Max(float64(len(s1)), float64(len(s2)))
	dist := levenshtein.ComputeDistance(s1, s2)
	return 1 - float64(dist)/maxLen
}

func getMatchedFields(doc1, doc2 Document) ([]string, []string) {
	fields := []string{"passport_number", "date_of_birth", "full_name", "expiration_date", "nationality"}
	matched := funk.Filter(fields, func(field string) bool {
		fmt.Println(cast.ToString(doc1.Fields[field]), cast.ToString(doc2.Fields[field]))
		fmt.Println(cast.ToString(doc1.Fields[field]) == cast.ToString(doc2.Fields[field]))
		return cast.ToString(doc1.Fields[field]) == cast.ToString(doc2.Fields[field])
	}).([]string)
	unmatched := funk.Subtract(fields, matched).([]string)
	return matched, unmatched
}

func checkVNMEvisaProduct(entryDate string) (string, error) {
	apiBase := gjson.Parse(os.Getenv("ad_endpoint")).Get("api_base_url").String()
	if apiBase == "" {
		return "", fmt.Errorf("invalid API base URL")
	}

	resp, err := resty.New().R().
		SetQueryParams(map[string]string{
			"service_core_info_country":    "VNM",
			"service_type":                 "new_visa",
			"service_core_info_entry_date": entryDate,
		}).
		SetHeader("x-ad-token", gjson.Parse(os.Getenv("ad_api_token")).Get("token").String()).
		Get(apiBase + "/v1/pkg/ets")

	if err != nil {
		return "", fmt.Errorf("API request failed: %w", err)
	}

	result := gjson.Parse(string(resp.Body()))
	data := result.Get("data")

	if !data.Exists() {
		return "", fmt.Errorf("invalid response format")
	}

	if data.IsArray() && len(data.Array()) > 0 {
		return "new_visa", nil
	}

	return "urgent_visa", nil
}
