package main

import (
	"bytes"
	"fmt"
	"image/jpeg"
	"io/ioutil"
	"log"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"

	"bitbucket.org/persistence17/aria/golang_services/sdk/mrz"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/aws/aws-sdk-go/service/textract"
	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
)

type parseGreenCardReq struct {
	Image string `json:"image"`
}

func ParseGreenCardFront(c *gin.Context) {
	downloader := middlewares.GetS3Downloader(c)

	var req parseDriverLicenseReq
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11003",
			"error_message": "Can not process the photo," + err.Error(),
		})
		return
	}

	src, err := loadImage(downloader, req.Image)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11003",
			"error_message": "Can not process the photo," + err.Error(),
		})
		return
	}
	buff := new(bytes.Buffer)
	if err := jpeg.Encode(buff, src, nil); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11003",
			"error_message": "Can not process the photo," + err.Error(),
		})
		return
	}

	textExtract := middlewares.GetTextExtract(c)
	data, err := getGreenCardFromImage(textExtract, buff.Bytes())

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11003",
			"error_message": "Can not process the photo," + err.Error(),
		})
		return
	}

	data["issue_date"] = "" // Amazon parse wrong issue date, get it from AI

	dataAppend, err := getGreenCardDataFromImageByAI(req.Image)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11003",
			"error_message": "getDataFromImageByAI error, can not process the image" + err.Error(),
		})
		return
	}
	for k, v := range dataAppend {
		data[k] = v
		if k == "issue_date" && v != "" {
			if v, err := time.Parse("01/02/06", v); err == nil {
				data[k] = v.Format("2006-01-02T00:00:00Z")
			}
		}
	}
	for k, v := range data {
		if v == "" {
			delete(data, k)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}

var mapGreenCard = map[string]string{
	"COUNTY":          "country",
	"DATE_OF_BIRTH":   "date_of_birth",
	"DATE_OF_ISSUE":   "issue_date",
	"EXPIRATION_DATE": "expiration_date",
	"FIRST_NAME":      "given_name",
	"LAST_NAME":       "surname",
	"MIDDLE_NAME":     "middle_name",
	"SUFFIX":          "suffix",
	"DOCUMENT_NUMBER": "document_number",
	// "STATE_IN_ADDRESS": "state",
	// "ADDRESS":          "address",
	// "CITY_IN_ADDRESS":  "city",
	// "CLASS":            "class",
	// "ENDORSEMENTS":     "endorsements",
	// "RESTRICTIONS":     "restrictions",
	// "ZIP_CODE_IN_ADDRESS": "zip_code",
	// "VETERAN":             "veteran",
	// "ID_TYPE":             "id_type",
	// "PLACE_OF_BIRTH":      "place_of_birth",
	// "STATE_NAME":          "state_name",
}

func getGreenCardFromImage(textExtract *textract.Textract, buff []byte) (map[string]string, error) {
	txData, err := textExtract.AnalyzeID(&textract.AnalyzeIDInput{
		DocumentPages: []*textract.Document{
			{
				Bytes: buff,
			},
		},
	})
	if err != nil {
		return nil, err
	}
	result := map[string]string{}
	for _, field := range txData.IdentityDocuments[0].IdentityDocumentFields {
		fieldJ := utils.StructToJSON(field)
		if name := fieldJ.Get("Type.Text").String(); name != "" {
			if key, ok := mapGreenCard[name]; ok {
				result[key] = fieldJ.Get("ValueDetection.Text").String()
				if valueType := fieldJ.Get("ValueDetection.NormalizedValue.ValueType").String(); valueType == "Date" {
					result[key] = fieldJ.Get("ValueDetection.NormalizedValue.Value").String() + "Z"
				}
			}

		}
	}
	return result, nil
}

func getGreenCardDataFromImageByAI(image string) (map[string]string, error) {
	req, _ := http.NewRequest(
		"POST",
		"https://api.ariadirectcorp.com/v1/id-reader/green_card_front",
		strings.NewReader(`{
			"image": "`+image+`"
		}`))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	result := map[string]string{}
	gjson.ParseBytes(body).Get("data").ForEach(func(key, value gjson.Result) bool {
		result[key.String()] = value.String()
		return true
	})

	return result, nil
}

func ParseGreenCardMRZ(c *gin.Context) {
	downloader := middlewares.GetS3Downloader(c)

	var req parseGreenCardReq
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11003",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}
	start := time.Now()

	src, err := loadImage(downloader, req.Image)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11003",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}
	log.Printf("loadImage took %s", time.Since(start))
	start = time.Now()
	buff, err := adjustImage(src)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11003",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}
	log.Printf("adjustImage took %s", time.Since(start))
	if c.Query("debug") == "true" {
		c.Writer.Header().Set("Content-Type", "image/jpeg")
		c.Writer.Header().Set("Content-Length", strconv.Itoa(len(buff)))
		if _, err := c.Writer.Write(buff); err != nil {
			log.Println("unable to write image.")
		}
		return
	}

	start = time.Now()

	lines, err := imageToTextWithGoogleVision(buff)

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11003",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}
	log.Printf("imageToTextWithGoogleVision took %s", time.Since(start))
	start = time.Now()

	rules, ok := c.Get("issue-rule")
	if !ok {
		c.JSON(http.StatusOK, gin.H{
			"success":    false,
			"error_code": "err11002",
			"error":      "missing issue rule",
		})
		return
	}
	result, text1, text2, text3, err := textToGreenCardMRZ(rules.([]*Rule), lines)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11003",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}
	log.Printf("textToMRZ took %s", time.Since(start))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		// "lines":   lines,
		"text1": text1,
		"text2": text2,
		"text3": text3,
	})
}

func textToGreenCardMRZ(rules []*Rule, lines []string) (*Document, string, string, string, error) {
	var IsLetter = regexp.MustCompile(`[a-zA-Z]`).MatchString
	// Regex contain special charactor
	var IsSpecial = regexp.MustCompile(`[<く]`).MatchString

	// Filter string's length at least 10
	lines = funk.FilterString(lines, func(s string) bool {
		return len(s) > 10 && IsLetter(s) && IsSpecial(s)
	})

	if len(lines) < 3 {
		return nil, "", "", "", fmt.Errorf("can not detect mrz")
	}

	// Remove spaces
	for i := 0; i < len(lines); i++ {
		lines[i] = strings.ReplaceAll(lines[i], " ", "")
		lines[i] = strings.ReplaceAll(lines[i], "く", "<")
	}

	if !strings.Contains(lines[len(lines)-2], "<") && !strings.Contains(lines[len(lines)-1], "<") {
		return nil, "", "", "", fmt.Errorf("can not detect mrz")
	}

	text1 := lines[len(lines)-3] + "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<"
	text2 := lines[len(lines)-2] + "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<"
	text3 := lines[len(lines)-1] + "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<"

	text1 = text1[0:30]
	text2 = text2[0:30]
	text3 = text3[0:30]

	text1 = text1[:15] + predictFix("text", text1[15:18]) + predictFix("number", text1[18:])
	text3 = strings.ReplaceAll(text3, "0", "O")

	doc, err := mrz.Parse(strings.Join([]string{text1, text2, text3}, "\n"))
	if err != nil {
		return nil, "", "", "", err
	}

	newDoc := &Document{
		Format: doc.Format,
		Fields: map[string]any{},
		Valid:  doc.Valid,
	}
	for k, v := range doc.Fields {
		switch k {
		case "documentNumber":
			newDoc.Fields["digital_number"] = v
		case "optional1":
			newDoc.Fields["document_number"] = v
		case "lastName":
			newDoc.Fields["surname"] = v
		case "firstName":
			newDoc.Fields["given_name"] = v
		case "birthDate":
			t, _ := time.Parse("060102", v)
			if t.Year() > time.Now().Year() {
				t = t.AddDate(-100, 0, 0)
			}
			newDoc.Fields["date_of_birth"] = t.Format("2006-01-02T00:00:00Z")
		case "sex":
			newDoc.Fields["gender"] = v
		case "issueDate":
			t, _ := time.Parse("060102", v)
			newDoc.Fields["issue_date"] = t.Format("2006-01-02T00:00:00Z")
		case "expirationDate":
			t, _ := time.Parse("060102", v)
			newDoc.Fields["expiration_date"] = t.Format("2006-01-02T00:00:00Z")
		default:
			newDoc.Fields[utils.Underscore(k)] = v
		}

	}

	return newDoc, text1, text2, text3, err
}

func predictFix(dataType, value string) string {
	if dataType == "text" {
		value = strings.ReplaceAll(value, "0", "O")
		value = strings.ReplaceAll(value, "1", "I")
		value = strings.ReplaceAll(value, "5", "S")
	}
	if dataType == "number" {
		value = strings.ReplaceAll(value, "O", "0")
		value = strings.ReplaceAll(value, "I", "1")
		value = strings.ReplaceAll(value, "S", "5")
	}
	return value
}
