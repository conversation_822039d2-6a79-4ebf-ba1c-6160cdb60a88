package main

import (
	"bytes"
	"fmt"
	"image"
	"image/jpeg"
	"log"
	"net/http"
	"strconv"
	"strings"

	"github.com/disintegration/gift"
	"github.com/gin-gonic/gin"
)

type capchaSolverReq struct {
	Image string `json:"image"`
	Type  string `json:"type"`
}

func CapchaSolverHandler(c *gin.Context) {
	var req capchaSolverReq
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11001",
			"error_message": "Can not process the image" + err.Error(),
		})
		return
	}

	src, err := loadImageFromURL(req.Image)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11003",
			"error_message": "Mrz parser error, can not process the image" + err.<PERSON>rror(),
		})
		return
	}
	var buff []byte
	if req.Type == "vnm_visa" {
		// Example https://visa.mofa.gov.vn/_layouts/registration/LoadCaptcha.aspx?p=326
		buff, err = adjustImageCapchaForVNMVisa(src)
	} else if req.Type == "evisa" {
		// Example https://evisa.xuatnhapcanh.gov.vn/en_US/web/guest/khai-thi-thuc-dien-tu/cap-thi-thuc-dien-tu?p_p_id=khaithithucdientu_WAR_eVisaportlet&p_p_lifecycle=2&p_p_state=normal&p_p_mode=view&p_p_cacheability=cacheLevelPage&p_p_col_id=column-2&p_p_col_count=1&_khaithithucdientu_WAR_eVisaportlet_view=insert
		buff, err = adjustImageCapchaForEVisa(src)
	} else {
		buff, err = adjustImageCapcha(src)
	}
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11003",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}

	lines, err := imageToTextWithGoogleVision(buff)
	result := ""
	if len(lines) > 0 {
		fmt.Println(strings.ReplaceAll(lines[0], " ", ""))
		result = strings.ReplaceAll(lines[0], " ", "")
	}
	if c.Query("debug") == "true" {

		c.Writer.Header().Set("Content-Type", "image/jpeg")
		c.Writer.Header().Set("Content-Length", strconv.Itoa(len(buff)))
		if _, err := c.Writer.Write(buff); err != nil {
			log.Println("unable to write image.")
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    strings.ToLower(result),
	})
}

func CapchaSolverByBinaryHandler(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   err,
		})
		return
	}
	fileR, err := file.Open()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   err,
		})
		return
	}

	src, _, err := image.Decode(fileR)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   err,
		})
		return
	}

	buff, err := adjustImageCapcha(src)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":       false,
			"error_code":    "err11003",
			"error_message": "Mrz parser error, can not process the image" + err.Error(),
		})
		return
	}

	lines, err := imageToTextWithGoogleVision(buff)
	result := ""
	if len(lines) > 0 {
		fmt.Println(strings.ReplaceAll(lines[0], " ", ""))
		result = strings.ReplaceAll(lines[0], " ", "")
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    strings.ToLower(result),
	})
}

func adjustImageCapcha(src image.Image) ([]byte, error) {
	// Create a new filter list and add some filters.
	g := gift.New(
		gift.Invert(),
		gift.ColorFunc(
			func(r0, g0, b0, a0 float32) (r, g, b, a float32) {
				// Keep white color, another color to black
				// Values range 0-1
				if r0 > 0.8 && g0 > 0.8 && b0 > 0.8 {
					return r0, g0, b0, a0
				} else {
					return 0, 0, 0, a0
				}
			},
		),
	)

	// Create a new image of the corresponding size.
	dst := image.NewRGBA(g.Bounds(src.Bounds()))
	g.Draw(dst, src)

	// image to buffer
	buf := new(bytes.Buffer)

	if err := jpeg.Encode(buf, dst, nil); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

func adjustImageCapchaForVNMVisa(src image.Image) ([]byte, error) {
	// Create a new filter list and add some filters.
	g := gift.New(
		gift.ColorFunc(
			func(r0, g0, b0, a0 float32) (r, g, b, a float32) {
				// Keep white color, another color to black
				// Values range 0-1
				if r0 > 0.7 && g0 < 0.5 && b0 < 0.5 {
					return 1, 1, 1, a0
				} else {
					return 0, 0, 0, a0
				}
			},
		),
		gift.Sigmoid(2, 10),
		gift.Minimum(2, false),

		gift.Invert(),
	)

	// Create a new image of the corresponding size.
	dst := image.NewRGBA(g.Bounds(src.Bounds()))
	g.Draw(dst, src)

	// image to buffer
	buf := new(bytes.Buffer)

	if err := jpeg.Encode(buf, dst, nil); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

func adjustImageCapchaForEVisa(src image.Image) ([]byte, error) {
	// Create a new filter list and add some filters.
	g := gift.New(
		gift.ColorFunc(
			func(r0, g0, b0, a0 float32) (r, g, b, a float32) {
				// Keep white color, another color to black
				// Values range 0-1
				if r0 < 0.2 && g0 < 0.2 && b0 < 0.2 {
					return 1, 1, 1, a0
				} else {
					return 0, 0, 0, a0
				}
			},
		),
		gift.Invert(),
	)

	// Create a new image of the corresponding size.
	dst := image.NewRGBA(g.Bounds(src.Bounds()))
	g.Draw(dst, src)

	// image to buffer
	buf := new(bytes.Buffer)

	if err := jpeg.Encode(buf, dst, nil); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}
