package main

import (
	"fmt"
	"os"
	"strconv"
	"strings"

	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/pariz/gountries"
	uuid "github.com/satori/go.uuid"
	"github.com/tidwall/gjson"
)

func AutoAssignedOrderStaffToEtsOrder(orderID int64, dao db.IEtsDao) error {
	order, err := dao.GetServiceOrderByID(int(orderID))
	if err != nil {
		return err
	}
	provider, err := dao.GetEtsProviderByID(order.ProviderID)
	if err != nil {
		return err
	}
	users, err := dao.VisaDB().GetUserByUnderOrganizationID(provider.OrgID)
	if err != nil {
		return err
	}

	activeStaffIDs := []string{}
	for _, user := range users {
		if user.AutoAssignedOrder {
			activeStaffIDs = append(activeStaffIDs, user.ID)
		}
	}

	staffFreeSort, err := dao.GetCountServiceOrderByStaffs(activeStaffIDs)
	if err != nil {
		return err
	}

	if len(staffFreeSort) > 0 {
		activeStaffIDs = staffFreeSort
	}

	// if len(activeStaffIDs) == 0 && len(users) > 0 {
	// 	activeStaffIDs = []string{users[0].ID}
	// }

	for _, staffID := range activeStaffIDs {
		existStaffs, err := dao.GetServiceOrderStaffs(orderID, staffID)
		if err != nil {
			return err
		}
		if len(existStaffs) > 0 {
			continue
		}

		staff, err := dao.VisaDB().GetUserByID(staffID)
		if err != nil {
			return err
		}

		randomCode := uuid.NewV4().String()
		if err := dao.UpdateServiceOrderStaff(orderID, staffID, randomCode, order.UserID, []byte(`{}`)); err != nil {
			return err
		}

		// Prepare for email sending
		orderData, err := dao.QueryServiceOrders(models.ServiceOrderFilter{ID: []string{strconv.FormatInt(orderID, 10)}, Limit: 1})
		if err != nil {
			return err
		}

		order := orderData.Data[0]

		tasks, _, err := dao.QueryServiceTasks(map[string]any{"order_id": order.ID}, 0, 10)
		if err != nil {
			return err
		}

		inputPods := tasks[0].InputPods.ToMapKeyValueV2(order.InputPods)

		etsJ := utils.StructToJSON(order.Service)

		webBaseURL := gjson.Parse(os.Getenv("ad_endpoint")).Get("web_base_url").String()

		var params = map[string]any{
			"OrderID":         order.ID,
			"FullName":        staff.GivenName + " " + staff.Surname,
			"Country":         order.Service.Country, //
			"ServiceType":     "Fastlane",
			"Terminal":        strings.Title(etsJ.Get("attributes.terminal").String()),
			"Task":            strings.Title(strings.Join(order.Service.Tasks, " ")),
			"ProcessingTime":  etsJ.Get("attributes.processing_time").String(),
			"NoOfTraveler":    inputPods["travel_passenger_info_no_of_traveler"],
			"AirportName":     inputPods["travel_exit_flight_exit_airport"],
			"AirlineName":     inputPods["travel_exit_flight_exit_airline"],
			"WelcomeName":     inputPods["travel_passenger_info_welcome_name"],
			"FlightNo":        utils.GetStringOrText(inputPods["travel_exit_flight_exit_flight"]),
			"ServiceDateTime": inputPods["travel_exit_flight_exit_timestamp"],
			"URL":             fmt.Sprintf("%s/fastlane/pickup-confirmed?order_id=%d&task_id=%d&service=ets&code=%s", webBaseURL, order.ID, tasks[0].ID, randomCode),
		}

		var gocountry = gountries.New()
		country, _ := gocountry.FindCountryByAlpha(order.Service.Country)
		params["Country"] = country.Name.Common

		if tasks[0].Type == "arrival" || tasks[0].Type == "vip_arrival" {
			params["AirportName"] = inputPods["travel_enter_flight_enter_airport"]
			params["AirlineName"] = inputPods["travel_enter_flight_enter_airline"]
			params["FlightNo"] = utils.GetStringOrText(inputPods["travel_enter_flight_enter_flight"])
			params["ServiceDateTime"] = inputPods["travel_enter_flight_enter_timestamp"]
		}

		params["ServiceDateTime"] = utils.StructToJSON(params).Get("ServiceDateTime").Time().Format("Mon, 02 Jan 2006 15:04")

		emailConfigMap := utils.GetMapEnv("ad_email")
		supportEmail := utils.StructToJSON(emailConfigMap).Get("support").String()
		var message = map[string]any{
			"template_name": "fastlane_request_to_staff",
			"to":            staff.Email,
			"bcc":           []string{supportEmail},
			"parameters":    params,
		}

		// Send email to sqs
		if err := sendEmail(utils.StructToJSON(message).Raw); err != nil {
			return err
		}

	}
	return nil
}

func sendEmail(data string) error {
	var awsJSON = gjson.ParseBytes([]byte(os.Getenv("ad_aws")))

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(awsJSON.Get("region").String()),
	})
	if err != nil {
		return err
	}
	svc := sqs.New(sess)
	sqsConf := gjson.Parse(os.Getenv("ad_sqs"))
	qURL := sqsConf.Get("url_prefix").String() + "/" + sqsConf.Get("notification_sqs_name").String()

	if _, err := svc.SendMessage(&sqs.SendMessageInput{
		MessageBody: aws.String(data),
		QueueUrl:    &qURL,
	}); err != nil {
		return err
	}
	return nil
}
