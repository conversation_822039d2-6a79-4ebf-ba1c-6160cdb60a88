package main

import (
	"fmt"
	"log"
	"os"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/localize"
	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func main() {
	app := cli.NewApp()
	app.Name = "aria-submit-dispatcher"
	app.Usage = "Submit dispatcher service for Aria"
	app.Commands = []cli.Command{
		submitDispatcherCmd(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("Submit dispatcher failed with error %v", err)
	}
}

func submitDispatcherCmd() cli.Command {
	return cli.Command{
		Name:      "submit-dispatcher",
		ShortName: "s",
		Usage:     "Start submit dispatcher single thread",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:     "ad_sqs",
				Usage:    "Env for sqs config",
				EnvVar:   "ad_sqs",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_aws",
				Usage:    "Env for aws config",
				EnvVar:   "ad_aws",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_db",
				Usage:    "Env for aws config",
				EnvVar:   "ad_db",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_package_service",
				Usage:    "Env for package service config",
				EnvVar:   "ad_package_service",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_s3",
				Usage:    "Env for s3 config",
				EnvVar:   "ad_s3",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_email",
				Usage:    "Env for email config",
				EnvVar:   "ad_email",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_website",
				Usage:    "Env for website config",
				EnvVar:   "ad_website",
				Required: true,
			},
			cli.StringFlag{
				Name:     "localize-file",
				Usage:    "Localize file for submit dispatcher",
				Required: true,
				Value:    "conf/localize.yaml",
			},
		},
		Action: runSubmitDispatcher(),
	}
}

func runSubmitDispatcher() cli.ActionFunc {
	return func(c *cli.Context) error {
		localizeFile := c.String("localize-file")
		localize, err := localize.ReadStringLocalizeMap(localizeFile)
		if err != nil {
			return err
		}

		sqsConfigMap := utils.GetMapEnv("ad_sqs")
		if sqsConfigMap["url_prefix"].(string) == "" {
			return fmt.Errorf("missing url_prefix in ad_sqs")
		}
		if sqsConfigMap["submit_sqs_name"].(string) == "" {
			return fmt.Errorf("missing submit_sqs_name in ad_sqs")
		}
		if sqsConfigMap["notification_sqs_name"].(string) == "" {
			return fmt.Errorf("missing notification_sqs_name in ad_sqs")
		}
		if sqsConfigMap["shipment_sqs_name"].(string) == "" {
			return fmt.Errorf("missing shipment_sqs_name in ad_sqs")
		}

		s3ConfigMap := utils.GetMapEnv("ad_s3")
		if s3ConfigMap["ariadirect_prod_applications"].(string) == "" {
			return fmt.Errorf("missing ariadirect_prod_applications in ad_s3")
		}

		sqsQueueURL := sqsConfigMap["url_prefix"].(string) + "/" + sqsConfigMap["submit_sqs_name"].(string)
		sqsNotificationURL := sqsConfigMap["url_prefix"].(string) + "/" + sqsConfigMap["notification_sqs_name"].(string)
		sqsShipURL := sqsConfigMap["url_prefix"].(string) + "/" + sqsConfigMap["shipment_sqs_name"].(string)

		awsUSConfig := utils.GetMapEnv("ad_aws")
		if awsUSConfig["region"].(string) == "" {
			return fmt.Errorf("missing region in ad_aws")
		}

		awsConfig := aws.NewConfig().WithRegion(awsUSConfig["region"].(string)).WithLogLevel(aws.LogOff)

		sess, err := session.NewSession(awsConfig)
		if err != nil {
			return err
		}

		s3Presigner := awslib.NewS3Svc(sess)
		s3Downloader := awslib.NewDownloader(sess)

		dbConfigMap := utils.GetMapEnv("ad_db")
		if dbConfigMap["write_host"].(string) == "" {
			log.Fatal(fmt.Errorf("missing write_host in ad_db"))
		}
		if dbConfigMap["dbname"].(string) == "" {
			log.Fatal(fmt.Errorf("missing dbname in ad_db"))
		}
		if dbConfigMap["username"].(string) == "" {
			log.Fatal(fmt.Errorf("missing username in ad_db"))
		}
		if dbConfigMap["password"].(string) == "" {
			log.Fatal(fmt.Errorf("missing password in ad_db"))
		}
		conn, err := db.NewAuroraDBFromConfigMap(dbConfigMap)
		if err != nil {
			return err
		}

		packageServiceConfigMap := utils.GetMapEnv("ad_package_service")
		if packageServiceConfigMap["host_name"].(string) == "" {
			return fmt.Errorf("missing host_name in ad_package_service")
		}

		websiteConfigMap := utils.GetMapEnv("ad_website")
		if websiteConfigMap["host_name"].(string) == "" {
			return fmt.Errorf("missing host_name in ad_website")
		}

		emailConfigMap := utils.GetMapEnv("ad_email")
		if emailConfigMap["support"].(string) == "" {
			return fmt.Errorf("missing support in ad_email")
		}

		config := map[string]any{
			"package_host_name": packageServiceConfigMap["host_name"].(string),
			"website_host_name": websiteConfigMap["host_name"].(string),
			"support_email":     emailConfigMap["support"].(string),
			"visas_email":       emailConfigMap["visas"].(string),
			"finance_email":     emailConfigMap["finance"].(string),
			"admin_email":       emailConfigMap["admin"].(string),
			"services_email":    emailConfigMap["services"].(string),
			"env":               emailConfigMap["env"].(string),
		}

		sqsSubmit := awslib.NewSQSClient(sess, sqsQueueURL)
		sqsNotification := awslib.NewSQSClient(sess, sqsNotificationURL)
		sqsShip := awslib.NewSQSClient(sess, sqsShipURL)

		submit := NewSubmitDispatcher(conn, sqsSubmit, sqsShip, config, s3Presigner, s3Downloader, s3ConfigMap, localize, sqsNotification)
		err = submit.WatchSubmitDispatcher()
		if err != nil {
			return err
		}
		return nil
	}
}
