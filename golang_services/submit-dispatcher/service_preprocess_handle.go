package main

import (
	"crypto/tls"
	"fmt"
	"os"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/go-resty/resty/v2"
	uuid "github.com/satori/go.uuid"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

func (s *SubmitDispatcher) ServicePreProcessing(order *models.ServiceOrder) error {
	orderResp, err := s.etsDao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:                          []string{cast.ToString(order.ID)},
		IncludeTasks:                true,
		IncludeService:              true,
		IncludePrice:                true,
		IncludePayment:              true,
		IncludeShipment:             true,
		IncludeOutputFileBeforePaid: true,
		IncludeProvider:             true,
		Limit:                       1,
	})
	if err != nil {
		return err
	}
	nOrder := orderResp.Data[0]

	presigner := s.s3Presigner
	bucketMap := utils.GetMapEnv("ad_s3")
	bucket := cast.ToString(bucketMap["ariadirect_prod_applications"])
	API_BASE := gjson.Parse(os.Getenv("ad_endpoint")).Get("api_base_url").String()

	restyClient := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})

	for _, task := range nOrder.Tasks {
		podKV := task.InputPods.ToMapKeyValueV2(nOrder.InputPods)
		passportURL := cast.ToString(podKV["document_copy_of_passport_copy_of_passport_main_page"])

		if passportURL == "" {
			registrationCode := cast.ToString(podKV["travel_visa_info_registration_code"])
			codeResp, err := restyClient.R().Get("https://api.evisa.gov.vn/client-service/public/hstt/ma-ho-so?maHoSo=" + registrationCode)
			if err != nil {
				continue
			}
			passportRawURL := gjson.Parse(codeResp.String()).Get("data.anhHoChieu").String()
			if passportRawURL == "" || passportRawURL[0] == '?' {
				continue
			}
			passportResp, err := restyClient.R().Get("https://api.evisa.gov.vn" + passportRawURL)
			if err != nil {
				continue
			}
			passportBuff := passportResp.Body()
			passportURL, _ = presigner.UploadFileBuffer(bucket, fmt.Sprintf("passport_images/%s.jpg", uuid.NewV4().String()), passportBuff, "image/jpeg")

			task.InputPods.SetFEValue("document_copy_of_passport_copy_of_passport_main_page", passportURL)
		}

		kValues := task.InputPods.ToMapKeyValueV2(nOrder.InputPods)
		if kValues["passport_core_info_given_name"] != "" && kValues["passport_core_info_surname"] != "" {
			parseResp, err := restyClient.R().SetBody(map[string]any{
				"passport_image": passportURL,
			}).Post(API_BASE + "/v1/mrz-parser/parse")
			if err != nil {
				continue
			}
			mrzFields := gjson.Parse(parseResp.String()).Get("data.fields")
			if mrzFields.Get("passport_number").String() != "" {
				task.InputPods.FillByPodValues(map[string]any{
					"passport_core_info_passport_number": mrzFields.Get("passport_number").String(),
					"passport_core_info_surname":         mrzFields.Get("surname").String(),
					"passport_core_info_given_name":      mrzFields.Get("given_name").String(),
					"passport_core_info_gender":          mrzFields.Get("gender").String(),
					"passport_core_info_date_of_birth":   mrzFields.Get("date_of_birth").String(),
					"passport_core_info_nationality":     mrzFields.Get("nationality").String(),
					"passport_core_info_issue_date":      mrzFields.Get("issue_date").String(),
					"passport_core_info_expiration_date": mrzFields.Get("expiration_date").String(),
				}, true)
			}
			if err := s.etsDao.UpdateServiceTask(map[string]any{
				"input_pods":       task.InputPods,
				"input_pod_values": task.InputPods.ToMapKeyValueV2(nOrder.InputPods),
			}, task.ID); err != nil {
				continue
			}
		}

	}

	return nil
}
