package main

import (
	"bytes"
	"fmt"
	"html/template"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/file"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/SebastiaanKlippert/go-wkhtmltopdf"
	"github.com/thoas/go-funk"
)

const (
	ContentTypePDF = "application/pdf"
)

type Group struct {
	Currency       string
	Details        []Detail
	SubTotal       float64
	TransactionFee float64
	Discount       float64
	SubTotalText   string
}
type Detail struct {
	PackageID      string
	Description    string
	Quantity       string
	Currency       string
	LineTotal      string
	ShipmentPrice  string
	TransactionFee float64
	Total          float64
	Discount       float64
	TotalText      string
	Shipping       string
}

type EmailWithName struct {
	Email string
	Name  string
}

func (s *SubmitDispatcher) SendReceipt(payment *models.Payment) error {
	cart, err := s.dao.GetCartByPaymentID(payment.ID)
	if err != nil {
		return err
	}
	if cart == nil {
		return nil
	}
	user, err := s.dao.GetUserByID(cart.UserID)
	if err != nil {
		return nil
	}

	fmt.Println("Generate receipt file for payment: " + payment.ID)

	yyyy, mm, dd := time.Now().Date()
	receiptID := fmt.Sprintf("AD-%d%d%d-%d", yyyy, mm, dd, cart.ID)

	// create receipt
	cartItems, err := s.dao.GetCartItemByCardID(cart.ID)
	if err != nil {
		return err
	}
	var details []Detail
	var backupEmails []EmailWithName
	var total float64
	var totalDiscount float64
	for _, cartItem := range cartItems {
		orID, err := strconv.Atoi(cartItem.ProductID)
		if err != nil {
			return err
		}
		order, err := s.etsDao.GetServiceOrderByID(orID)
		if err != nil {
			return err
		}
		shipment, err := s.dao.GetVisaShipment(order.ShipmentInfo.String)
		if err != nil {
			return err
		}
		if shipment == nil {
			return fmt.Errorf("Empty shipment in order # %d", orID)
		}

		svcs, err := s.etsDao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
		if err != nil {
			return err
		}
		svc := svcs[0]
		if order.Summary == nil {
			order.Summary = &models.ServiceSummary{}
		}
		total += order.Summary.Total
		totalDiscount += order.Summary.Discount
		// if transactionFeeRate, ok := payments.TransactionFee[payment.Method]; ok && order.Summary.TransactionFee == 0 {
		// 	order.Summary.TransactionFee = math.Ceil(order.Summary.SubTotal * transactionFeeRate)
		// }
		detail := Detail{
			PackageID:      fmt.Sprintf("%d", order.ID),
			Quantity:       fmt.Sprintf("%d", order.Summary.Quantity),
			Currency:       order.Summary.Currency,
			LineTotal:      utils.ParseFloatToCurrency(order.Summary.UnitPriceWithFees),
			ShipmentPrice:  fmt.Sprintf("%0.2f", order.Summary.ShippingPrice),
			TransactionFee: order.Summary.TransactionFee,
			Discount:       order.Summary.Discount,
			Total:          order.Summary.Total,
			TotalText:      utils.ParseFloatToCurrency(order.Summary.Total),
		}
		switch svc.ServiceType {
		case models.EtsServiceTypeFastlane:
			detail.Description = fmt.Sprintf("%s - %s - %s - %s - %s - %s", s.localize.EN.Country[svc.Country], s.localize.EN.ServiceType[svc.ServiceType], s.localize.EN.Terminal[(*svc.Attributes)["terminal"].(string)], s.localize.EN.Tasks[strings.Join(svc.Tasks, ",")], s.localize.EN.ProcessingTime[(*svc.Attributes)["processing_time"].(string)], s.localize.EN.Airport[*svc.Airport])
		case models.EtsServiceTypePassport:
			detail.Description = fmt.Sprintf("%s - %s - %s - %s", s.localize.EN.Country[svc.Country], s.localize.EN.Tasks[strings.Join(svc.Tasks, ",")], s.localize.EN.ServiceType[svc.ServiceType], s.localize.EN.ProcessingTime[(*svc.Attributes)["processing_time"].(string)])
		case models.EtsServiceTypeGlobalEntry:
			detail.Description = fmt.Sprintf("%s - %s - %s", s.localize.EN.Tasks[strings.Join(svc.Tasks, ",")], s.localize.EN.DocumentType[(*svc.Attributes)["document_type"].(string)], s.localize.EN.ProcessingTime[(*svc.Attributes)["processing_time"].(string)])
		case models.EtsServiceTypeTSAPrecheck, models.EtsServiceTypeNexus, models.EtsServiceTypeSentri, models.EtsServiceTypeIDPhoto:
			detail.Description = fmt.Sprintf("%s", s.localize.EN.ServiceType[svc.ServiceType])
		case models.EtsServiceTypeFastTruck:
			detail.Description = fmt.Sprintf("%s", s.localize.EN.DocumentType[(*svc.Attributes)["document_type"].(string)])
		case models.EtsServiceTypeCovidTest:
			detail.Description = s.localize.EN.Country[svc.Country] + " Covid Test"
		case models.EtsServiceTypeNewVisa:
			detail.Description = s.localize.EN.Country[svc.Country] + " " + s.localize.EN.Tasks[svc.Tasks[0]] + " Visa"
		default:
			detail.Description = s.localize.EN.Product[svc.Name]
		}

		if shipment.ShippingContact != nil {
			backupEmails = append(backupEmails, EmailWithName{shipment.ShippingContact.Email, shipment.ShippingContact.GivenName + " " + shipment.ShippingContact.Surname})
		}
		details = append(details, detail)
	}

	var groups []Group
	for _, item := range details {
		found := false
		foundIndex := 0
		for index, group := range groups {
			if group.Currency == item.Currency {
				found = true
				foundIndex = index
			}
		}
		if !found {
			group := Group{
				Currency:       item.Currency,
				Details:        []Detail{item},
				SubTotal:       item.Total,
				Discount:       item.Discount,
				TransactionFee: item.TransactionFee,
				SubTotalText:   item.TotalText,
			}
			groups = append(groups, group)
		} else {
			groups[foundIndex].Details = append(groups[foundIndex].Details, item)
			groups[foundIndex].TransactionFee = groups[foundIndex].TransactionFee + item.TransactionFee
			groups[foundIndex].SubTotal = groups[foundIndex].SubTotal + item.Total
			groups[foundIndex].Discount = groups[foundIndex].Discount + item.Discount
			groups[foundIndex].SubTotalText = utils.ParseFloatToCurrency(groups[foundIndex].SubTotal)
		}
	}

	emails := []EmailWithName{{user.Email, user.GivenName + " " + user.Surname}}
	if regexp.MustCompile(`guest_\<EMAIL>`).MatchString(user.Email) {
		emails = backupEmails
	}
	emails = funk.Filter(emails, func(s EmailWithName) bool {
		return s.Email != ""
	}).([]EmailWithName)

	emails = funk.Uniq(emails).([]EmailWithName)
	if len(emails) == 0 {
		emails = []EmailWithName{{s.config["support_email"].(string), "AriaDirect Support"}}
	}

	receiptParameters := map[string]any{
		"Date":           payment.CreatedAt.Format(DateFormat),
		"ReceiptID":      receiptID,
		"Fullname":       emails[0].Name,
		"Method":         payment.Method,
		"Invoice":        payment.ID,
		"Currency":       payment.Currency,
		"Discount":       utils.ParseFloatToCurrency(totalDiscount),
		"TransactionFee": utils.ParseFloatToCurrency(utils.StructToJSON(payment.Properties).Get("summary").Get("transaction_fee").Float()),
		"Tax":            utils.ParseFloatToCurrency(payment.TaxRate),
		"SubTotal":       utils.ParseFloatToCurrency(utils.StructToJSON(payment.Properties).Get("summary").Get("sub_total").Float()),
		"GrandTotal":     utils.ParseFloatToCurrency(utils.StructToJSON(payment.Properties).Get("summary").Get("total").Float()),
		"Groups":         groups,
	}

	fileName := fmt.Sprintf("AriaDirect-%s-Receipt-Number-%s.pdf", receiptParameters["Fullname"].(string), receiptParameters["ReceiptID"].(string))
	localPath, err := filepath.Abs(filepath.Dir(os.Args[0]))
	if err != nil {
		return err
	}
	fileDir := localPath + "/" + fileName
	if err = s.CreateNewPDFByHTML("sale_receipt_group", fileDir, receiptParameters); err != nil {
		return err
	}

	// Remove file
	defer utils.RemoveFile(fileDir)
	// add to s3
	bucket := s.s3Config["ariadirect_prod_applications"].(string)
	key := file.BuildPaymentOutputS3Key(user.ID, payment.ID, fileName)
	err = s.s3Presigner.UploadFile(bucket, key, fileDir, ContentTypePDF)
	if err != nil {
		return err
	}
	// update payment
	up := models.Payment{
		ID:          payment.ID,
		ReceiptID:   receiptID,
		ReceiptFile: path.Join(bucket, key),
	}
	err = s.paymentDao.UpdatePayment(&up)
	if err != nil {
		return err
	}

	//prepare data
	for _, email := range emails {
		message := map[string]any{
			"template_name": "send_receipt_group",
			"to":            email.Email,
			"bcc":           []string{s.config["finance_email"].(string)},
			"parameters": map[string]any{
				"FullName": strings.ToUpper(email.Name),
				"Orders": strings.Join(funk.Map(details, func(g Detail) string {
					return g.PackageID
				}).([]string), ", "),
			},
			"attachments": []map[string]any{
				{
					"bucket":    bucket,
					"key":       key,
					"file_name": fileName,
				},
			},
		}

		// send message to notification queue
		err = s.sqsNotification.Send(message)
		if err != nil {
			return err
		}

	}
	return nil
}

func (s *SubmitDispatcher) CreateNewPDFByHTML(templateName string, fileName string, parameters map[string]any) error {
	// Get HTML template
	temp, err := s.dao.GetEmailTemplateByName(templateName, "EN")
	if err != nil {
		return err
	}
	if temp == nil {
		return fmt.Errorf("Empty template in database")
	}
	// Fill parameter to template
	tmpl, err := template.New("").Parse(temp.HTMLBody)
	if err != nil {
		return err
	}
	var htmlStr bytes.Buffer
	err = tmpl.Execute(&htmlStr, parameters)
	if err != nil {
		return err
	}
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		return err
	}
	// DisableSmartShrinking
	page := wkhtmltopdf.NewPageReader(strings.NewReader(htmlStr.String()))
	page.DisableSmartShrinking.Set(true)

	pdfg.AddPage(page)
	pdfg.PageSize.Set(wkhtmltopdf.PageSizeA4)
	// Create PDF document in internal buffer
	err = pdfg.Create()
	if err != nil {
		return err
	}
	// Your PDF Name
	err = pdfg.WriteFile(fileName)
	if err != nil {
		return err
	}

	return nil
}
