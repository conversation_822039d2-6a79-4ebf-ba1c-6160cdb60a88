package errors

//ERR07001	The photo could not be saved into the system. Please try again!
//ERR07002	This visa type is not supported.
//ERR07003	A visa for this country is not supported.
//ERR007017 Your email is not on the approval list. Please contact your admininstration department.
//ERR07031	This code is not a corporate code. Please contact us via <NAME_EMAIL>
//ERR07036	This promotion code has expired.

var (
	// backend cannot tell if photo upload is successful or not
	PhotoUploadErr = &adError{
		errorCode:    "ERR07001",
		errorMessage: "The photo could not be saved into the system. Please try again!",
	}
	VisaTypeNotSupportedErr = &adError{
		errorCode:    "ERR07002",
		errorMessage: "This visa type is not supported.",
	}
	NationalityNotSupportedErr = &adError{
		errorCode:    "ERR07003",
		errorMessage: "Visa Product not found due to nationality is in blacklist",
	}
	NoAvailableVisaProductErr = &adError{
		errorCode:    "ERR07004",
		errorMessage: "There is no supported visa based on the provided destination, residence, purpose.",
	}
	NoAvailableVisaProductEntryDateErr = &adError{
		errorCode:    "ERR07005",
		errorMessage: "Entry date too close, no available visa product.",
	}
	EmailNotOnArrrovalListErr = &adError{
		errorCode:    "ERR007017",
		errorMessage: "Your email is not on the approval list. Please contact your admininstration department.",
	}
	InvalidCorpCodeErr = &adError{
		errorCode:    "ERR07031",
		errorMessage: "This code is not a corporate code. Please contact us via <NAME_EMAIL>",
	}
	ExpiredPromoCode = &adError{
		errorCode:    "ERR07036",
		errorMessage: "This promotion code has expired.",
	}
	CannotUpdateNonOpenPackage = &adError{
		errorMessage: "cannot update non-open package",
		errorCode:    "ERR08004",
	}
	CannotGeneratePaymentsErr = &adError{
		errorCode:    "ERR08005",
		errorMessage: "cannot generate order for paid package",
	}
	CannotReopenErr = &adError{
		errorMessage: "only package with ready status can be re-opened",
		errorCode:    "ERR08006",
	}
	CannotGeneratePaymentsNonReadyErr = &adError{
		errorCode:    "ERR08007",
		errorMessage: "cannot generate order for non-ready package",
	}
	PaymentAmountNotMatchErr = &adError{
		errorCode:    "ERR08008",
		errorMessage: "payment amount is not match",
	}
)
