package errors

var (
	CannotCheckoutReadyPackageErr = &adError{
		errorCode:    "ERR08003",
		errorMessage: "Your order has been checked out. Please reopen and check out again.",
	}
	CannotCheckoutLockedCartErr = &adError{
		errorCode:    "ERR08009",
		errorMessage: "Your order has been lock for checked out. Please complete modify and check out again.",
	}
	CannotAddLabelNonProceedErr = &adError{
		errorCode:    "ERR09000",
		errorMessage: "Cannot add shipping label for non-proceed package",
	}
	AlreadyExistsLabelErr = &adError{
		errorCode:    "ERR08005",
		errorMessage: "This order have already shipping label. Please use it.",
	}
)
