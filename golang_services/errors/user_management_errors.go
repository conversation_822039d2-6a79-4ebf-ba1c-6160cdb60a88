package errors

//
//ERR00001	This token has expired or is invalid.
//ERR00002	Password should be at least 6 characters and contains both digit and letter!
//ERR00003	This email exists in our system.
//ERR00004	This user does not exist in our system.
//ERR00006	Your account has not been verified. Please verify your account first.
//ERR00007	The password does not match with the old password
//ERR00008	Your new password must be different from your previous password.

var (
	InvalidTokenErr = &adError{errorCode: "ERR00001", errorMessage: "This token has expired or is invalid."}
	BadPassword     = &adError{
		errorCode:    "ERR00002",
		errorMessage: "Password should be at least 6 characters and contains both digit and letter!",
	}
	DuplicateEmailErr = &adError{
		errorCode:    "ERR00003",
		errorMessage: "This email exists in our system.",
	}
	InvalidEmailOrPasswordErr = &adError{
		errorCode:    "ERR00004",
		errorMessage: "Invalid email/password",
	}
	NotVerifiedAccountErrErr = &adError{
		errorCode:    "ERR00006",
		errorMessage: "Your account has not been verified. Please verify your account first.",
	}
	NotMatchWithOldPasswordErr = &adError{
		errorCode:    "ERR00007",
		errorMessage: "he password does not match with the old password",
	}
	SamePasswordErr = &adError{
		errorCode:    "ERR00008",
		errorMessage: "Your new password must be different from your previous password.",
	}
	CanNotSelfAssign = &adError{
		errorCode:    "ERR00012",
		errorMessage: "Can not assign yourself",
	}
	InvalidEtsIDErr = &adError{
		errorCode:    "",
		errorMessage: "Invalid extended travel service ID",
	}
)
