package errors

import (
	"encoding/json"
	"fmt"
)

const (
	ValidationCategoryVisaProduct = 1
	ValidationCategoryPassport    = 2
	ValidationCategoryTravelInfo  = 3
	ValidationCategoryPersonInfo  = 4
	ValidationCategoryFamilyInfo  = 5
)

const (
	PersonalInfoCitizenshipNumErr = iota + 1
	PersonalInfoOccupationErr
	PersonalInfoCompanyInfoErr
	PersonalInfoMaritalStatusErr
	PersonalInfoHomeAddErr
)

const (
	TravelInfoExitDateTimeErr = iota + 1
	TravelInfoExitFlightNumErr
	TravelInfoEnterDateTimeErr
	TravelInfoEnterFlightNumErr
	TravelInfoEntryDateLaterThanPPExpErr
	TravelInfoEntryDateLaterThanExitErr
	TravelInfoExitDateLaterThanPPExpErr
	TravelInfoNotEnoughProcessTimeErr
)

const (
	FamilyInfoSpouseInfoErr = iota + 1
)

type ADValidationError struct {
	category    int
	countryCode string
	errorCode   int
	message     string
	podID       string
}

func (err *ADValidationError) Error() string {
	return err.message
}

func (err *ADValidationError) ErrCode() string {
	if err.countryCode != "" {
		return fmt.Sprintf("VLD%d%s%03d", err.category, err.countryCode, err.errorCode)
	} else {
		return fmt.Sprintf("VLD%d000%03d", err.category, err.errorCode)
	}
}

func (err *ADValidationError) MarshalJSON() ([]byte, error) {
	m := map[string]any{
		"error_code":    err.ErrCode(),
		"error_message": err.message,
	}
	if err.podID != "" {
		m["pod_id"] = err.podID
	}
	return json.Marshal(m)
}

// GetError get error values
func (err *ADValidationError) GetError() (podID string, errorCode string, message string) {
	if err == nil {
		return
	}
	podID = err.podID
	errorCode = err.ErrCode()
	message = err.message
	return
}

var (
	PassportNumberFmtErr = &ADValidationError{
		category:    ValidationCategoryPassport,
		countryCode: "",
		errorCode:   1,
		message:     "Passport number should length between 7 and 9 characters and numbers",
		podID:       "passport_core_info_passport_number",
	}
	PassportExpErr = &ADValidationError{
		category:    ValidationCategoryPassport,
		countryCode: "",
		errorCode:   2,
		message:     "Exp date must be valid for at least 6 months",
		podID:       "passport_core_info_expiration_date",
	}
	EnterTimeErr = &ADValidationError{
		category:    ValidationCategoryTravelInfo,
		countryCode: "",
		errorCode:   3,
		message:     "Enter time must be equal to entry date",
		podID:       "travel_enter_flight_enter_timestamp",
	}
	ExitFlightNumberFmtErr = &ADValidationError{
		category:    ValidationCategoryTravelInfo,
		countryCode: "",
		errorCode:   11,
		message:     "Flight number has min length of 3",
		podID:       "travel_exit_flight_exit_flight",
	}
	ExitTimeErr = &ADValidationError{
		category:    ValidationCategoryTravelInfo,
		countryCode: "",
		errorCode:   10,
		message:     "Exit time must be equal to entry date",
		podID:       "travel_exit_flight_exit_timestamp",
	}
	EnterFlightNumberFmtErr = &ADValidationError{
		category:    ValidationCategoryTravelInfo,
		countryCode: "",
		errorCode:   4,
		message:     "Flight number has min length of 3",
		podID:       "travel_enter_flight_enter_flight",
	}
	ProcessingTimeEntryDateErr = &ADValidationError{
		category:    ValidationCategoryTravelInfo,
		countryCode: "",
		errorCode:   9,
		message:     "Cannot process visa request before entry date",
	}
)

func NewValidationError(category, errorCode int, podID string, countryCode, message string) *ADValidationError {
	return &ADValidationError{
		category:    category,
		countryCode: countryCode,
		errorCode:   errorCode,
		message:     message,
		podID:       podID,
	}
}

func NewEntryDateErr() *ADValidationError {
	return NewValidationError(ValidationCategoryTravelInfo, 5, "service_core_info_entry_date", "",
		"Entry date must be equal to or greater than the current date and earlier than the passport Exp date")
}

func NewEntryDateLaterThanExitDateErr() *ADValidationError {
	return NewValidationError(ValidationCategoryTravelInfo, 6, "service_core_info_entry_date", "",
		"Entry date must be equal to or earlier than exit date")
}

func NewExitDateErr() *ADValidationError {
	return NewValidationError(ValidationCategoryTravelInfo, 7, "service_core_info_exit_date", "",
		"Exit date must be equal to or greater than the current date and earlier than the passport Exp date")
}

// In some SPECIAL case Exp date must be valid for at least 1 year. SPECIAL: 5 years Vietnam Visa
func NewOneYearPassportExpErr(country string) *ADValidationError {
	return NewValidationError(ValidationCategoryPassport, 2, "service_core_info_exit_date", country,
		"Passport should not expire for at least 1 year")
}

func NewValidationErrorWithCategoryAndFieldStr(category, field, msg, countryCode string, podID string) *ADValidationError {
	var categoryCode, errorCode int
	switch category {
	case "personal":
		categoryCode = ValidationCategoryPersonInfo
		errorCode = getPersonalInfoErrorCode(field)
	case "travel_info", "travel":
		errorCode = getTravelInfoErrorCode(field)
		categoryCode = ValidationCategoryTravelInfo
	case "family_info", "family":
		errorCode = getFamilyInfoErrorCode(field)
		categoryCode = ValidationCategoryFamilyInfo
	case "passport":
		categoryCode = ValidationCategoryPassport
	default:
		return &ADValidationError{
			category:    0,
			countryCode: "",
			errorCode:   0,
			message:     msg,
			podID:       podID,
		}
	}
	return NewValidationError(categoryCode, errorCode, podID, countryCode, msg)
}

func getPersonalInfoErrorCode(field string) int {
	switch field {
	case "citizenship_number":
		return PersonalInfoCitizenshipNumErr
	case "occupation":
		return PersonalInfoOccupationErr
	case "marital_status":
		return PersonalInfoMaritalStatusErr
	case "home_address", "state", "city", "street_number_name":
		return PersonalInfoHomeAddErr
	default:
		return 0
	}
}

func getTravelInfoErrorCode(field string) int {
	switch field {
	case "exit_timestamp":
		return TravelInfoExitDateTimeErr
	case "exit_flight":
		return TravelInfoExitFlightNumErr
	case "enter_timestamp":
		return TravelInfoEnterDateTimeErr
	case "enter_flight":
		return TravelInfoEnterFlightNumErr
	default:
		return 0
	}
}

func getFamilyInfoErrorCode(field string) int {
	switch field {
	case "spouse_info":
		return FamilyInfoSpouseInfoErr
	default:
		return 0
	}
}
