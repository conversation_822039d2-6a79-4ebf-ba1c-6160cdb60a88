package errors

import (
	"encoding/json"
	"fmt"
)

type ADError interface {
	error
	ErrCode() string
}

type adError struct {
	errorCode    string
	errorMessage string
}

func (err *adError) Error() string {
	return err.errorMessage
}

func (err *adError) ErrCode() string {
	return err.errorCode
}

func (err *adError) MarshalJSON() ([]byte, error) {
	m := map[string]any{
		"error_code":    err.errorCode,
		"error_message": err.errorMessage,
	}
	return json.Marshal(m)
}

var (
	NotFoundErr = &adError{
		errorCode:    "ERR02000",
		errorMessage: "Not found",
	}
	CannotSubmitStateErr  = fmt.Errorf("cannot submit application with non-ready state")
	AppUpdatedByOthersErr = fmt.Errorf("applicaiton has be updated by other request")
	AlreadyExistErr       = &adError{
		errorCode:    "ERR03001",
		errorMessage: "Already exist",
	}
	SystemBusyErr = &adError{
		errorCode:    "ERR99003",
		errorMessage: "The system is currently busy at the moment. Please try again later!",
	}
)
