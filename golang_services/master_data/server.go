package main

import (
	"encoding/json"
	"fmt"
	"os"

	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"github.com/meilisearch/meilisearch-go"
	"github.com/rs/zerolog/log"
	"github.com/tidwall/gjson"
	"github.com/urfave/cli"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	handler "bitbucket.org/persistence17/aria/golang_services/handlers/master_data"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func startMasterDataService() cli.ActionFunc {
	return func(c *cli.Context) error {
		port := c.String("port")

		e, err := newMasterDataService(c)
		if err != nil {
			return err
		}

		return e.Run(":" + port)
	}
}

func newMasterDataService(c *cli.Context) (*gin.Engine, error) {
	logger.SetJsonZeroLoggerWithLevel(c.String(flags.LogLevelFlag.Name))
	e := gin.New()
	e.Use(logger.LoggerMW(), logger.CustomLoggerMW(), gzip.Gzip(gzip.DefaultCompression))
	_, conf, err := middlewares.LoadCommonMWAndConf(c, e)
	log.Info().Interface("conf", conf).Msg("config from conf file")
	if err != nil {
		return nil, err
	}
	if c.Bool("bypass-auth") {
		e.Use(middlewares.DummyAuthMW())
	}

	if err := middlewares.InitSecretMap(c.String("ad_secrets")); err != nil {
		return nil, err
	}

	userAccountServiceConfigMap := utils.GetMapEnv(c.String("account-service-config"))
	log.Info().Interface("account-service-config", userAccountServiceConfigMap).Msg("Account Service config from ENV")

	// db
	var conn *db.AuroraDB
	dbConfigMap := utils.GetMapEnv("ad_db")
	if host := dbConfigMap["write_host"]; host != nil {
		log.Info().Interface("write_host", dbConfigMap["write_host"]).Msg("Loading DB config from ENV")
		conn, err = db.NewAuroraDBFromConfigMap(dbConfigMap)
		if err != nil {
			return nil, err
		}
	}
	meilisearchConf := gjson.Parse(os.Getenv("ad_meilisearch"))
	meilisearchHost := meilisearchConf.Get("host").String()
	client := meilisearch.NewClient(meilisearch.ClientConfig{
		Host: meilisearchHost,
	})

	e.Use(
		middlewares.AuthorizationMW(),
		middlewares.AccountServiceConfigMW(userAccountServiceConfigMap),
		middlewares.VisaDaoMW(conn),
		middlewares.ORMDaoMW(conn),
		middlewares.MeilisearchClientMW(client),
	)
	go meilisearchMigration(conn, client, meilisearchHost)
	handler.LoadV1MasterDataHandlers(e)
	return e, nil
}

func meilisearchMigration(conn *db.AuroraDB, client *meilisearch.Client, host string) error {
	db, err := gorm.Open(postgres.New(postgres.Config{Conn: conn.Db}))
	if err != nil {
		fmt.Println(err)
	}

	var indexes []models.MasterData
	db.Table("master_data").Select("name, value").Where("category = ?", "meilisearch").Find(&indexes)
	for _, index := range indexes {
		var documents []any
		for _, item := range gjson.ParseBytes(index.Value).Array() {
			var document map[string]any
			json.Unmarshal([]byte(item.Raw), &document)
			documents = append(documents, document)
		}

		if _, err := client.Index(index.Name).AddDocuments(documents); err != nil {
			fmt.Println("AddDocuments error:", err)

		}

		if index.Name == "airport" {
			// filterableAttributes := []string{"iata", "country_code"}
			// if _, err := client.Index(index.Name).UpdateFilterableAttributes(&filterableAttributes); err != nil {
			// 	fmt.Println("UpdateFilterableAttributes error:", err)

			// }
			if _, err := resty.New().NewRequest().SetBody([]string{"iata", "country_code", "name"}).Put(host + "/indexes/airport/settings/filterable-attributes"); err != nil {
				fmt.Println("UpdateFilterableAttributes error:", err)
			}
		}
	}
	return nil
}
