module bitbucket.org/persistence17/aria/golang_services

go 1.23.0

toolchain go1.23.3

require (
	github.com/360EntSecGroup-Skylar/excelize v1.4.1
	github.com/Jeffail/gabs v1.4.0
	github.com/Masterminds/squirrel v1.5.2
	github.com/Sebas<PERSON>an<PERSON><PERSON>pert/go-wkhtmltopdf v1.5.0
	github.com/asaskevich/govalidator v0.0.0-20200907205600-7a23bdc65eef
	github.com/aws/aws-sdk-go v1.42.21
	github.com/bluele/gcache v0.0.0-20190518031135-bc40bd653833
	github.com/casbin/casbin v1.9.1
	github.com/casbin/gorm-adapter v1.0.0
	github.com/davegardnerisme/phonegeocode v0.0.0-20160120101024-a49b977f8889
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/disintegration/gift v1.2.1
	github.com/dlclark/regexp2 v1.2.0
	github.com/extrame/xls v0.0.1
	github.com/gin-contrib/cors v1.3.0
	github.com/gin-contrib/gzip v0.0.3
	github.com/gin-contrib/logger v0.0.2
	github.com/gin-gonic/gin v1.6.3
	github.com/go-playground/tz v0.0.0-20160623151202-753dd2620dd9
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/gorilla/handlers v1.4.2
	github.com/gorilla/mux v1.7.3
	github.com/gorilla/schema v1.2.0
	github.com/gorilla/sessions v1.2.1
	github.com/iancoleman/strcase v0.3.0
	github.com/jinzhu/gorm v1.9.11
	github.com/jmoiron/sqlx v1.2.0
	github.com/k3a/html2text v1.0.7
	github.com/landoop/tableprinter v0.0.0-20200104100433-ae9249991eb1
	github.com/lib/pq v1.10.2
	github.com/mileusna/useragent v1.0.2
	github.com/muesli/smartcrop v0.3.0
	github.com/nlopes/slack v0.6.0
	github.com/okta/okta-jwt-verifier-golang v1.1.1
	github.com/olivere/elastic v6.2.26+incompatible
	github.com/olivere/elastic/v7 v7.0.9
	github.com/pariz/gountries v0.0.0-20191029140926-233bc78cf5b5
	github.com/pkg/errors v0.9.1
	github.com/rickar/cal/v2 v2.0.0-beta.2
	github.com/robfig/cron v1.2.0
	github.com/rs/cors v1.7.0
	github.com/rs/xid v1.6.0
	github.com/rs/zerolog v1.34.0
	github.com/satori/go.uuid v1.2.0
	github.com/skip2/go-qrcode v0.0.0-20191027152451-9434209cb086
	github.com/smartwalle/wxpay v0.0.0-20190806084730-d7d0789179ad
	github.com/spf13/cast v1.5.1
	github.com/stripe/stripe-go v70.15.0+incompatible
	github.com/tealeg/xlsx/v3 v3.2.3
	github.com/thoas/go-funk v0.7.1-0.20201128100912-5035611e402b
	github.com/tidwall/gjson v1.6.0
	github.com/tidwall/sjson v1.1.1
	github.com/ulule/deepcopier v0.0.0-20171107155558-ca99b135e50f
	github.com/urfave/cli v1.22.4
	golang.org/x/oauth2 v0.22.0
	golang.org/x/sync v0.13.0 // indirect
	golang.org/x/text v0.24.0
	google.golang.org/api v0.155.0
	gopkg.in/gomail.v2 v2.0.0-20160411212932-81ebce5c23df
	gopkg.in/guregu/null.v3 v3.4.0
	gopkg.in/yaml.v2 v2.4.0
	gorm.io/datatypes v1.0.4
	gorm.io/driver/postgres v1.2.3
	gorm.io/gorm v1.22.4
)

require (
	github.com/PullRequestInc/go-gpt3 v1.1.15
	github.com/boombuler/barcode v1.0.2
	github.com/emersion/go-imap v1.2.1
	github.com/emersion/go-message v0.18.2
	github.com/go-resty/resty/v2 v2.7.0
	github.com/ivahaev/go-xlsx-templater v0.0.0-20200217104802-1394ee35aab8
	github.com/karmdip-mi/go-fitz v0.0.0-20210702102225-a530a79566e9
	github.com/mattn/go-sqlite3 v1.14.24
	github.com/meilisearch/meilisearch-go v0.19.0
	github.com/metal3d/go-slugify v0.0.0-20160607203414-7ac2014b2f23
	github.com/signintech/gopdf v0.15.0
	go.mau.fi/whatsmeow v0.0.0-20250417131650-164ddf482526
	go.mongodb.org/mongo-driver v1.14.0
	gopkg.in/Iwark/spreadsheet.v2 v2.0.0-20230915040305-7677e8164883
)

require (
	cloud.google.com/go/compute/metadata v0.3.0 // indirect
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/emersion/go-sasl v0.0.0-20200509203442-7bfe0ed36a21 // indirect
	github.com/felixge/httpsnoop v1.0.4 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/google/s2a-go v0.1.7 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/googleapis/enterprise-certificate-proxy v0.3.2 // indirect
	github.com/mattn/go-colorable v0.1.14 // indirect
	github.com/petermattis/goid v0.0.0-20250319124200-ccd6737f222a // indirect
	github.com/vmihailenco/tagparser/v2 v2.0.0 // indirect
	go.mau.fi/libsignal v0.1.2 // indirect
	go.mau.fi/util v0.8.6 // indirect
	go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.46.1 // indirect
	go.opentelemetry.io/otel v1.30.0 // indirect
	go.opentelemetry.io/otel/metric v1.30.0 // indirect
	go.opentelemetry.io/otel/trace v1.30.0 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20240903143218-8af14fe29dc1 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240903143218-8af14fe29dc1 // indirect
	gopkg.in/check.v1 v1.0.0-20201130134442-10cb98267c6c // indirect
)

require (
	github.com/Knetic/govaluate v3.0.1-0.20171022003610-9aa49832a739+incompatible // indirect
	github.com/agnivade/levenshtein v1.2.0
	github.com/andybalholm/brotli v1.0.4 // indirect
	github.com/aymerick/raymond v2.0.2+incompatible // indirect
	github.com/cpuguy83/go-md2man/v2 v2.0.0-20190314233015-f79a8a8ca69d // indirect
	github.com/dustin/go-humanize v1.0.0 // indirect
	github.com/extrame/ole2 v0.0.0-20160812065207-d69429661ad7 // indirect
	github.com/frankban/quicktest v1.14.4 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/go-playground/locales v0.13.0 // indirect
	github.com/go-playground/universal-translator v0.17.0 // indirect
	github.com/go-playground/validator/v10 v10.2.0 // indirect
	github.com/go-redis/redis/v8 v8.11.5
	github.com/go-sql-driver/mysql v1.6.0 // indirect
	github.com/goccy/go-json v0.3.5 // indirect
	github.com/golang-jwt/jwt v3.2.2+incompatible // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/btree v1.0.0 // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/googleapis/gax-go/v2 v2.12.0 // indirect
	github.com/gorilla/securecookie v1.1.1 // indirect
	github.com/gorilla/websocket v1.5.0 // indirect
	github.com/guregu/null v3.4.0+incompatible // indirect
	github.com/jackc/chunkreader/v2 v2.0.1 // indirect
	github.com/jackc/pgconn v1.10.1 // indirect
	github.com/jackc/pgio v1.0.0 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgproto3/v2 v2.2.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20200714003250-2b9c44734f2b // indirect
	github.com/jackc/pgtype v1.9.1 // indirect
	github.com/jackc/pgx/v4 v4.14.1 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.3 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.9 // indirect
	github.com/kataras/tablewriter v0.0.0-20180708051242-e063d29b7c23 // indirect
	github.com/klauspost/compress v1.15.0 // indirect
	github.com/kr/pretty v0.3.1 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/lann/builder v0.0.0-20180802200727-47ae307949d0 // indirect
	github.com/lann/ps v0.0.0-20150810152359-62de8c46ede0 // indirect
	github.com/leodido/go-urn v1.2.0 // indirect
	github.com/lestrrat-go/backoff/v2 v2.0.7 // indirect
	github.com/lestrrat-go/httpcc v1.0.0 // indirect
	github.com/lestrrat-go/iter v1.0.0 // indirect
	github.com/lestrrat-go/jwx v1.1.1 // indirect
	github.com/lestrrat-go/option v1.0.0 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.9 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.1 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/montanaflynn/stats v0.0.0-20171201202039-1bf9dbcd8cbe // indirect
	github.com/nfnt/resize v0.0.0-20180221191011-83c6a9932646 // indirect
	github.com/patrickmn/go-cache v0.0.0-20180815053127-5633e0862627 // indirect
	github.com/peterbourgon/diskv v2.0.1+incompatible // indirect
	github.com/phpdave11/gofpdi v1.0.11 // indirect
	github.com/rogpeppe/fastuuid v1.2.0 // indirect
	github.com/rogpeppe/go-internal v1.12.0 // indirect
	github.com/russross/blackfriday/v2 v2.0.1 // indirect
	github.com/samber/lo v1.49.1
	github.com/shabbyrobe/xmlwriter v0.0.0-20200208144257-9fca06d00ffa // indirect
	github.com/shurcooL/sanitized_anchor_name v1.0.0 // indirect
	github.com/smartystreets/goconvey v1.6.4 // indirect
	github.com/tchap/go-patricia v2.3.0+incompatible // indirect
	github.com/tealeg/xlsx v1.0.5 // indirect
	github.com/tidwall/match v1.0.1 // indirect
	github.com/tidwall/pretty v1.0.1 // indirect
	github.com/ugorji/go/codec v1.1.7 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.34.0 // indirect
	github.com/vmihailenco/msgpack/v5 v5.4.1
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/youmark/pkcs8 v0.0.0-20181117223130-1be2e3e5546d // indirect
	go.opencensus.io v0.24.0 // indirect
	golang.org/x/crypto v0.37.0 // indirect
	golang.org/x/exp v0.0.0-20250408133849-7e4ce0ab07d0
	golang.org/x/image v0.14.0
	golang.org/x/net v0.39.0 // indirect
	golang.org/x/sys v0.32.0 // indirect
	google.golang.org/grpc v1.66.1 // indirect
	google.golang.org/protobuf v1.36.6
	gopkg.in/alexcesaro/quotedprintable.v3 v3.0.0-20150716171945-2caba252f4dc // indirect
	gorm.io/driver/mysql v1.2.1 // indirect
)
