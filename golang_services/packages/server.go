package main

import (
	"errors"
	"fmt"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	elasticaws "github.com/olivere/elastic/aws/v4"
	"github.com/olivere/elastic/v7"
	"github.com/rs/zerolog/log"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	"bitbucket.org/persistence17/aria/golang_services/handlers/packages"
	packages_v1 "bitbucket.org/persistence17/aria/golang_services/handlers/packages/v1"
	"bitbucket.org/persistence17/aria/golang_services/localize"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func startPackagesService() cli.ActionFunc {
	return func(c *cli.Context) error {
		port := c.String("port")

		e, err := newPackagesServer(c)
		if err != nil {
			return err
		}

		return e.Run(":" + port)
	}
}

func newPackagesServer(c *cli.Context) (*gin.Engine, error) {
	logger.SetJsonZeroLoggerWithLevel(c.String(flags.LogLevelFlag.Name))
	e := gin.New()
	e.Use(logger.LoggerMW(), logger.CustomLoggerMW(), gzip.Gzip(gzip.DefaultCompression))
	logger.InitGraylogLogger()
	e.Use(logger.GraylogLoggerMiddleware(logger.DefaultRequestLoggerConfig()))

	_, conf, err := middlewares.LoadCommonMWAndConf(c, e)
	log.Info().Interface("conf", conf).Msg("config from conf file")
	if err != nil {
		return nil, err
	}

	localizeFile := c.String("localize-file")
	localize, err := localize.ReadStringLocalizeMap(localizeFile)
	if err != nil {
		return nil, err
	}

	e.Use(middlewares.SetLocalize(localize))

	if c.Bool("bypass-auth") {
		e.Use(middlewares.DummyAuthMW())
	}
	// aws related
	awsConf := utils.ParseJsonObjStr(c.String("ad-aws"))
	awsConfig := aws.NewConfig().WithRegion(awsConf["region"].(string)).WithLogLevel(aws.LogOff)

	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return nil, err
	}
	packages_v1.InitImageAnalyzer(sess)

	// db
	var conn *db.AuroraDB
	dbConfigMap := utils.GetMapEnv("ad_db")
	if host := dbConfigMap["write_host"]; host != nil {
		log.Info().Interface("write_host", dbConfigMap["write_host"]).Msg("Loading DB config from ENV")
		conn, err = db.NewAuroraDBFromConfigMap(dbConfigMap)
		if err != nil {
			return nil, err
		}
	}

	if err := middlewares.InitSecretMap(c.String("ad_secrets")); err != nil {
		return nil, err
	}

	bucketMap := utils.GetMapEnv(c.String("s3-bucket-config"))
	log.Info().Interface("bucket_config", bucketMap).Msg("S3 bucket config from ENV")

	svcConfigMap := utils.GetMapEnv(c.String("service-config"))
	log.Info().Interface("service_config", svcConfigMap).Msg("Service config from ENV")

	packerServiceConfigMap := utils.GetMapEnv(c.String("ad_packer_service"))
	log.Info().Interface("ad_packer_service", svcConfigMap).Msg("Packer service config from ENV")
	svcConfigMap["packer_service_host_name"] = packerServiceConfigMap["host_name"]

	emailConfigMap := utils.GetMapEnv("ad_email")
	if emailConfigMap["support"].(string) == "" {
		return nil, fmt.Errorf("missing support in ad_email")
	}

	endpointConfigMap := utils.GetMapEnv("ad_endpoint")
	if endpointConfigMap["web_base_url"].(string) == "" {
		return nil, fmt.Errorf("missing support in ad_endpoint")
	}

	svcConfigMap["support_email"] = emailConfigMap["support"].(string)
	svcConfigMap["supervisor_email"] = emailConfigMap["supervisor"].(string)
	svcConfigMap["admin_email"] = emailConfigMap["admin"].(string)
	svcConfigMap["env"] = emailConfigMap["env"].(string)
	svcConfigMap["web_base_url"] = endpointConfigMap["web_base_url"].(string)

	userAccountServiceConfigMap := utils.GetMapEnv(c.String("account-service-config"))
	log.Info().Interface("account-service-config", userAccountServiceConfigMap).Msg("Account Service config from ENV")

	apiTokenMap := utils.GetMapEnv("ad_api_token")

	awsESConfig := utils.GetMapEnv(c.String("ad_es"))
	endpoint := awsESConfig["endpoint"].(string)
	if endpoint == "" {
		return nil, fmt.Errorf("missing endpoint in ad_es")
	}
	regionES := awsESConfig["region"].(string)
	if regionES == "" {
		return nil, fmt.Errorf("missing region in ad_aws")
	}

	es, err := newElastic(sess, regionES, endpoint)
	if err != nil {
		return nil, err
	}

	onepayConf := utils.GetMapEnv(c.String("ad_onepay"))
	log.Info().Interface("ad_onepay", onepayConf).Msg("Load onepay config")

	zellepayConf := utils.GetMapEnv(c.String("ad_zellepay"))
	log.Info().Interface("ad_zellepay", zellepayConf).Msg("Load zellepay config")

	paymentConfig := map[string]any{
		"onepay_enabled":         onepayConf["onepay_enabled"],
		"zellepay_contact_email": zellepayConf["zellepay_contact_email"],
		"zellepay_contact_phone": zellepayConf["zellepay_contact_phone"],
		"bank_1_name":            zellepayConf["bank_1_name"],
		"bank_1_owner":           zellepayConf["bank_1_owner"],
		"bank_1_number":          zellepayConf["bank_1_number"],
	}

	e.Use(
		middlewares.AuthorizationMW(),
		middlewares.VisaDaoMW(conn),
		middlewares.ORMDaoMW(conn),
		middlewares.PaymentDaoMW(conn),
		middlewares.NewS3PresignerMW(sess),
		middlewares.NewS3DownloaderMW(sess),
		middlewares.NewS3BucketsMW(bucketMap),
		middlewares.ServiceConfigMapMW(svcConfigMap),
		middlewares.AccountServiceConfigMW(userAccountServiceConfigMap),
		middlewares.AWSRegionMW(awsConf["region"].(string)),
		middlewares.APITokenConfigMW(apiTokenMap),
		middlewares.NewESMW(es),
		middlewares.PaymentConfigMW(paymentConfig),
	)

	// sqs
	_, err = loadPackageRelatedQueues(utils.GetMapEnv(c.String("sqs-config")), e, sess)
	if err != nil {
		return nil, err
	}

	packages.LoadV1PackagesHandlers(e)

	return e, nil
}

func newPackagesDb(conf map[string]any, creds *credentials.Credentials) (*db.AuroraDB, error) {
	paymentDb, ok := conf["packages-db"]
	if !ok {
		return nil, fmt.Errorf("missing packages-db in config file")
	}
	region, ok := conf["db-region"]
	if !ok {
		return nil, fmt.Errorf("missing db-region in config file")
	}
	user, ok := conf["db-user"]
	if !ok {
		return nil, fmt.Errorf("missing db-user in config file")
	}
	database, ok := conf["db-database"]
	if !ok {
		return nil, fmt.Errorf("missing db-database in config file")
	}

	return db.NewAuroraDBWithAwsCred(paymentDb.(string), region.(string), user.(string), database.(string), creds)
}

func newElastic(sess *session.Session, region string, url string) (*elastic.Client, error) {
	signingClient := elasticaws.NewV4SigningClient(sess.Config.Credentials, region)
	return elastic.NewClient(
		elastic.SetURL("https://"+url),
		elastic.SetSniff(false),
		elastic.SetHealthcheck(false),
		elastic.SetHttpClient(signingClient),
	)
}

func loadPackageRelatedQueues(conf map[string]any, r gin.IRouter, sess *session.Session) (gin.IRouter, error) {
	log.Info().Interface("sqs_config", conf).Msg("SQS config from env")
	prefix := conf["url_prefix"].(string)
	//previewQueue := prefix + "/" + conf["review_sqs_name"].(string)
	//if previewQueue == "" {
	//	return nil, errors.New("missing passport-image-preview-queue")
	//}
	// sync queue for preview to
	//previewQ := awslib.NewSQSClient(sess, previewQueue)

	// packerQueue := prefix + "/" + conf["packer_sqs_name"].(string)
	// if packerQueue == "" {
	// 	return nil, errors.New("missing packer_sqs_name")
	// }
	// use async queue for packer
	// packerQ := awslib.NewAsyncSQSProducer(sess, packerQueue)
	// go packerQ.Run()
	//submitQueue := conf["package-submit-queue"]
	//if submitQueue == "" {
	//	return nil, errors.New("missing package-submit-queue")
	//}
	//submitQ := awslib.NewAsyncSQSProducer(sess, submitQueue)
	packerOnlineQueue := prefix + "/" + conf["packer_online_sqs_name"].(string)
	if packerOnlineQueue == "" {
		return nil, errors.New("missing packer_online_sqs_name")
	}
	// use async queue for packer online
	packerOnlineQ := awslib.NewAsyncSQSProducer(sess, packerOnlineQueue)
	go packerOnlineQ.Run()

	notiUserQueue := prefix + "/" + conf["notification_user_sqs_name"].(string)
	if notiUserQueue == "" {
		return nil, errors.New("missing notification_user_sqs_name")
	}
	notiUserQ := awslib.NewAsyncSQSProducer(sess, notiUserQueue)
	go notiUserQ.Run()

	notiQueue := prefix + "/" + conf["notification_sqs_name"].(string)
	if notiQueue == "" {
		return nil, errors.New("missing notification_sqs_name")
	}
	notiQ := awslib.NewAsyncSQSProducer(sess, notiQueue)
	go notiQ.Run()

	r.Use(
		//middlewares.SimpleQueueMW(previewQ, packages_v1.UploadPreviewQueue),
		// middlewares.SimpleQueueMW(packerQ, packages_v1.PackerQueue),
		middlewares.SimpleQueueMW(packerOnlineQ, packages_v1.PackerOnlineQueue),
		middlewares.SimpleQueueMW(notiUserQ, packages_v1.NotiUserQueue),
		middlewares.SimpleQueueMW(notiQ, packages_v1.NotiQueue),
	)

	return r, nil
}
