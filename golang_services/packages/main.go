package main

import (
	"log"
	"os"

	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
)

func main() {
	app := cli.NewApp()
	app.Name = "aria-visa-packages"
	app.Usage = "Packages service for Aria"
	app.Commands = []cli.Command{
		serviceCmd(),
		serviceInternalCmd(),
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}
}

func serviceCmd() cli.Command {
	return cli.Command{
		Name:      "service",
		ShortName: "svc",
		Usage:     "Start packages service for Aria",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:   "port",
				Usage:  "Port the server listens to",
				EnvVar: "PORT",
				Value:  "3000",
			},
			cli.StringFlag{
				Name:     "conf-file",
				Usage:    "Configuration file for payment service",
				EnvVar:   "CONF_FILE",
				Required: true,
				Value:    "conf/packages.yaml",
			},
			cli.StringFlag{
				Name:     "localize-file",
				Usage:    "Localize file for submit dispatcher",
				Required: true,
				Value:    "conf/localize.yaml",
			},
			cli.StringFlag{
				Name:     "client-secret",
				Usage:    "Client secret key for call internal service",
				EnvVar:   "CLIENT_SECRET",
				Required: true,
			},
			cli.StringFlag{
				Name:     "internal-auth-service-host",
				Usage:    "Internal authenticate service host",
				EnvVar:   "INTERNAL_AUTH_SERVICE_HOST",
				Required: true,
			},
			cli.StringFlag{
				Name: "pg-conn-string",
				Usage: "Postgres connection string, including username password. If not provided, server will try to " +
					"get connection string aws instance role",
				EnvVar: "PG_CONN_STR",
			},
			cli.StringFlag{
				Name:  "db-config",
				Usage: "Env for db config",
			},
			cli.StringFlag{
				Name:  "s3-bucket-config",
				Usage: "S3 bucket config for uploading package/application related files",
				Value: "s3_bucket",
			},
			cli.StringFlag{
				Name:     "ad-aws",
				Usage:    "AWS config",
				EnvVar:   "ad_aws",
				Required: true,
			},
			cli.BoolFlag{
				Name:   "bypass-auth",
				Usage:  "TEST ONLY!!! Bypass auth",
				Hidden: false,
			},
			cli.StringFlag{
				Name:     "sqs-config",
				Usage:    "Env name for sqs config",
				Required: true,
			},
			cli.StringFlag{
				Name:     "service-config",
				Usage:    "Env name for service configuration",
				Required: true,
			},
			cli.StringFlag{
				Name:     "account-service-config",
				Usage:    "Env for user account configuration",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_secrets",
				Usage:    "Env for secret configuration",
				EnvVar:   "ad_secrets",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_api_token",
				Usage:    "Env for api token configuration",
				EnvVar:   "ad_api_token",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_es",
				Usage:    "Env for Amazon Elasticsearch Service config",
				EnvVar:   "ad_es",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_onepay",
				Usage:    "Env for onepay config",
				EnvVar:   "ad_onepay",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_zellepay",
				Usage:    "Env for zellepay config",
				EnvVar:   "ad_zellepay",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_packer_service",
				Usage:    "Env for Packer service config",
				EnvVar:   "ad_packer_service",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_email",
				Usage:    "Env for email config",
				EnvVar:   "ad_email",
				Required: true,
			},
			cli.StringFlag{
				Name:     "ad_endpoint",
				Usage:    "Env for endpoint",
				EnvVar:   "ad_endpoint",
				Required: true,
			},
			flags.LogLevelFlag,
		},
		Action: startPackagesService(),
	}
}

func serviceInternalCmd() cli.Command {
	return cli.Command{
		Name:      "internal-service",
		ShortName: "int-svc",
		Usage:     "Start packages service for Aria",
		Flags: []cli.Flag{
			cli.StringFlag{
				Name:   "port",
				Usage:  "Port the server listens to",
				EnvVar: "PORT",
				Value:  "3000",
			},
			cli.StringFlag{
				Name:     "conf-file",
				Usage:    "Configuration file for payment service",
				EnvVar:   "CONF_FILE",
				Required: true,
				Value:    "conf/packages.yaml",
			},
			cli.StringFlag{
				Name:     "client-secret",
				Usage:    "Client secret key for call internal service",
				EnvVar:   "CLIENT_SECRET",
				Required: true,
			},
			cli.StringFlag{
				Name:     "internal-auth-service-host",
				Usage:    "Internal authenticate service host",
				EnvVar:   "INTERNAL_AUTH_SERVICE_HOST",
				Required: true,
			},
			cli.StringFlag{
				Name: "pg-conn-string",
				Usage: "Postgres connection string, including username password. If not provided, server will try to " +
					"get connection string aws instance role",
				EnvVar: "PG_CONN_STR",
			},
			cli.StringFlag{
				Name:  "db-config",
				Usage: "Env for db config",
			},
			cli.StringFlag{
				Name:  "s3-bucket-config",
				Usage: "S3 bucket config for uploading package/application related files",
				Value: "s3_bucket",
			},
			cli.StringFlag{
				Name:     "ad-aws",
				Usage:    "AWS config",
				EnvVar:   "ad_aws",
				Required: true,
			},
			cli.BoolFlag{
				Name:   "bypass-auth",
				Usage:  "TEST ONLY!!! Bypass auth",
				Hidden: false,
			},
			cli.StringFlag{
				Name:     "sqs-config",
				Usage:    "Env name for sqs config",
				Required: true,
			},
			cli.StringFlag{
				Name:     "service-config",
				Usage:    "Env name for service configuration",
				Required: true,
			},
			flags.LogLevelFlag,
		},
		Action: startPackagesInternalService(),
	}
}
