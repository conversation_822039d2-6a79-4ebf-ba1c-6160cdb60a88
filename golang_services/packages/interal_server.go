package main

import (
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/flags"
	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/logger"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

func startPackagesInternalService() cli.ActionFunc {
	return func(c *cli.Context) error {
		port := c.String("port")

		e, err := newPackagesInteralServer(c)
		if err != nil {
			return err
		}

		return e.Run(":" + port)
	}
}

func newPackagesInteralServer(c *cli.Context) (*gin.Engine, error) {
	logger.SetJsonZeroLoggerWithLevel(c.String(flags.LogLevelFlag.Name))
	e := gin.New()
	// aws related
	e.Use(logger.LoggerMW(), logger.CustomLoggerMW(), gzip.Gzip(gzip.DefaultCompression))
	logger.InitGraylogLogger()
	e.Use(logger.GraylogLoggerMiddleware(logger.DefaultRequestLoggerConfig()))

	awsConf := utils.ParseJsonObjStr(c.String("ad-aws"))
	awsConfig := aws.NewConfig().WithRegion(awsConf["region"].(string)).WithLogLevel(aws.LogOff)

	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return nil, err
	}

	// db
	var conn *db.AuroraDB
	dbConfigMap := utils.GetMapEnv("ad_db")
	if host := dbConfigMap["write_host"]; host != nil {
		log.Info().Interface("write_host", dbConfigMap["write_host"]).Msg("Loading DB config from ENV")
		conn, err = db.NewAuroraDBFromConfigMap(dbConfigMap)
	}
	if err != nil {
		return nil, err
	}

	bucketMap := utils.GetMapEnv(c.String("s3-bucket-config"))
	log.Info().Interface("bucket_config", bucketMap).Msg("S3 bucket config from ENV")

	svcConfigMap := utils.GetMapEnv(c.String("service-config"))
	log.Info().Interface("service_config", svcConfigMap).Msg("Service config from ENV")

	e.Use(
		middlewares.VisaDaoMW(conn),
		middlewares.NewS3PresignerMW(sess),
		middlewares.NewS3BucketsMW(bucketMap),
		middlewares.ServiceConfigMapMW(svcConfigMap),
		middlewares.AWSRegionMW(awsConf["region"].(string)),
	)

	return e, nil
}
