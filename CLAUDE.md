# CLAUDE.md - Guidelines for Aria-Backend

## Commands
- Build: `make build-%-image` (e.g., `make build-package-image`)
- Format Go code: `cd golang_services && make fmt`
- Lint Go code: `cd golang_services && make vet`
- Run Go test: `cd golang_services && go test ./path/to/package -v`
- Run single Go test: `cd golang_services && go test ./path/to/package -run TestName`

## Code Style
- **Go**: Follow standard Go conventions (gofmt)
- **Node.js**: Use camelCase for variables and functions
- **Error Handling**: Always check errors in Go code with appropriate logging
- **Imports**: Group imports (standard lib, third-party, project)
- **Types**: Use strong typing, avoid interface{} when possible
- **Naming**: Use descriptive names for functions and variables
- **Project Structure**: Microservices architecture with golang_services, nodejs_services, and python_services
- **Documentation**: Comment public functions and complex logic
- **Testing**: Write table-driven tests for Go functions

Remember to run `make fmt` and `make vet` before committing Go code.