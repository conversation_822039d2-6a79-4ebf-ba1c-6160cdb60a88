apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-service
  labels:
    app: notification-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: notification-service
  template:
    metadata:
      labels:
        app: notification-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: notification-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/notification-service:38e2535b8Z250331T112004
          command: ["./notification"]
          args:
            [
              "n",
              "--ad_sqs",
              "ad_sqs",
              "--ad_aws",
              "ad_aws",
              "--ad_db",
              "ad_db",
              "--ad_s3",
              "ad_s3",
              "--ad_ses",
              "ad_ses",
              "--ad_website",
              "ad_website",
              "--ad_endpoint",
              "ad_endpoint",
              "--ad_api_token",
              "ad_api_token",
            ]
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: app-environment
