apiVersion: apps/v1
kind: Deployment
metadata:
  name: atlas-service
  labels:
    app: atlas-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: atlas-service
  template:
    metadata:
      labels:
        app: atlas-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: atlas-service
          image: ************.dkr.ecr.us-west-2.amazonaws.com/atlas-service:6789891cf
          command: ["./atlas"]
          args:
            [
              "--account-service-config",
              "ad_user_account_service",
              "--ad_secrets",
              "ad_secrets",
              "--callback-host",
              "api.ariadirectcorp.com",
            ]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: atlas-service
  labels:
    app: atlas-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: atlas-service
