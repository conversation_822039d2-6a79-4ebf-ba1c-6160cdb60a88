apiVersion: apps/v1
kind: Deployment
metadata:
  name: master-data-service
  labels:
    app: master-data-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: master-data-service
  template:
    metadata:
      labels:
        app: master-data-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: master-data-service
          image: ************.dkr.ecr.us-west-2.amazonaws.com/master-data-service:0f8701bdfZ241223T115741
          command: ["./master_data"]
          args:
            [
              "svc",
              "--db-config",
              "ad_db",
              "--account-service-config",
              "ad_user_account_service",
              "--ad_secrets",
              "ad_secrets",
            ]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: master-data-service
  labels:
    app: master-data-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: master-data-service
