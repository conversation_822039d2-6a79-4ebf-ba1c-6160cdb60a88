apiVersion: apps/v1
kind: Deployment
metadata:
  name: submit-dispatcher-service
  labels:
    app: submit-dispatcher-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: submit-dispatcher-service
  template:
    metadata:
      labels:
        app: submit-dispatcher-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: submit-dispatcher-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/submit-dispatcher-service:56a837948Z250528T105539
          command: ["./submit-dispatcher"]
          args:
            [
              "s",
              "--ad_db",
              "ad_db",
              "--ad_sqs",
              "ad_sqs",
              "--ad_aws",
              "ad_aws",
              "--ad_package_service",
              "ad_package_service",
              "--ad_s3",
              "ad_s3",
              "--ad_email",
              "ad_email",
              "--ad_website",
              "ad_website",
              "--localize-file",
              "conf/localize.yaml",
            ]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
