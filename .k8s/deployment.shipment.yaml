apiVersion: apps/v1
kind: Deployment
metadata:
  name: shipment-service
  labels:
    app: shipment-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: shipment-service
  template:
    metadata:
      labels:
        app: shipment-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: shipment-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/shipment-service:17d5e302b
          command: ["./shipments"]
          args: ["service", "--db-config", "ad_db"]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: shipment-service
  labels:
    app: shipment-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: shipment-service
