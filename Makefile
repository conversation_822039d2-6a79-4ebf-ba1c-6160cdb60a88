pre-build:
	$(MAKE) -C golang_services fmt
	$(MAKE) -C golang_services vet

# Build seperate service image
SHELL :=/bin/bash
GIT_SHA=$(shell git rev-parse --short=9 HEAD)Z$(shell date +%y%m%dT%H%M%S)

# build-versions-image \
# build-id-reader-image \


build-all-images: \
	build-atlas-image \
	build-package-image \
	build-packer-image \
	build-payment-image \
	build-search-image \
	build-user-management-image \
	build-master-data-image \
	build-shipment-image \
	build-mrz-parser-image \
	build-passport-photo-image \
	build-helper-image
	build-devices-image \
	build-album-image \
	build-atlas-worker-image \
	build-carrier-image \
	build-packer-offline-image \
	build-packer-online-image \
	build-packer-online-captcha-image \
	build-submit-email-worker-image \
	build-notification-image \
	build-notification-user-image \
	build-watchdog-image \
	build-submit-dispatcher-image
	
ecr-login:
	@aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 853431205376.dkr.ecr.us-west-2.amazonaws.com

# Example: make build-package-image
# build-%-image: ecr-login
# 	@SERVIVE=$(subst build-,,$(subst -image,,$@)); \
# 	echo Building service: $$SERVIVE ; \
# 	docker build -t 853431205376.dkr.ecr.us-west-2.amazonaws.com/$$SERVIVE-service:$(GIT_SHA) --no-cache -f dockerfiles/$$SERVIVE.dockerfile . ; \
# 	docker push 853431205376.dkr.ecr.us-west-2.amazonaws.com/$$SERVIVE-service:$(GIT_SHA); \
# 	cd .k8s && sed -i "/image:/s/:[0-9a-z_].*/:${GIT_SHA}/g" deployment.$$SERVIVE.yaml ;

build-%-image: ecr-login
	@SERVIVE=$(subst build-,,$(subst -image,,$@)); \
	echo Building service: $$SERVIVE ; \
	docker build -t 853431205376.dkr.ecr.us-west-2.amazonaws.com/$$SERVIVE-service:$(GIT_SHA)  -f dockerfiles/$$SERVIVE.dockerfile . ; \
	docker push 853431205376.dkr.ecr.us-west-2.amazonaws.com/$$SERVIVE-service:$(GIT_SHA); \
	cd .k8s && sed -i "/image:/s/:[0-9a-z_].*/:${GIT_SHA}/g" deployment.$$SERVIVE.yaml ; 
	rsync -av  ./.k8s/* ad-stag-vps:~/aria/.k8s
	ssh ad-stag-vps "cd ~/aria/.k8s/scripts && sh deployment_local.sh";

# Example: make deploy-package
deploy-jp-%:
	@SERVICE=$(subst deploy-jp-,,$@) ; \
	echo Deploying service: $$SERVICE ; \
	make build-$$SERVICE-image; \
	SERVICE_LIST=$$SERVICE IMAGE_TAG=$(GIT_SHA) ansible-playbook -i infrastructure/deployment/ansible/inventory/prodjp/prodjp infrastructure/deployment/ansible/prodjp_service.yml; \

deploy-us-%:
	@SERVICE=$(subst deploy-us-,,$@) ; \
	echo Deploying service: $$SERVICE ; \
	make build-$$SERVICE-image; \
	SERVICE_LIST=$$SERVICE IMAGE_TAG=$(GIT_SHA) ansible-playbook -i infrastructure/deployment/ansible/inventory/produs/produs infrastructure/deployment/ansible/produs_service.yml; \


# Deploy by git message
deploy-dev:
	rsync -av  ./.k8s/* ad-stag-vps:~/aria/.k8s
	ssh ad-stag-vps "cd /home/<USER>/aria/.k8s/scripts && sh deployment_local.sh";

deploy-us:
	@for service in $(shell git show -s --format=%B | cut -d'|' -f 1); do \
		echo Deploying service: $$service ; \
		make build-$$service-image; \
    	echo Deploying service: $$service ; \
		SERVICE_LIST=$$service IMAGE_TAG=$(GIT_SHA) ansible-playbook -i -vvv infrastructure/deployment/ansible/inventory/produs/produs infrastructure/deployment/ansible/produs_service.yml; \
	done