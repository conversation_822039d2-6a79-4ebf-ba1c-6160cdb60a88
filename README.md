# Aria Visa service

This project uses `Go` to build services. Please install `go1.12` or higher.

## CI/CD Dev status: [![CircleCI](https://dl.circleci.com/status-badge/img/bb/persistence17/aria/tree/dev.svg?style=svg&circle-token=****************************************)](https://dl.circleci.com/status-badge/redirect/bb/persistence17/aria/tree/dev)

## CI/CD Production status: [![CircleCI](https://dl.circleci.com/status-badge/img/bb/persistence17/aria/tree/master.svg?style=svg&circle-token=****************************************)](https://dl.circleci.com/status-badge/redirect/bb/persistence17/aria/tree/master)

## Dependency management

We use `go mod`, aka `go modules`, to manage dependencies. Please **DO NOT** modify the `go.mod` and `go.sum` files directly. To add a dependency, please run `go get <dependency>` command. If you need a specific version of a dependency, please check out the go official website for more details about go modules.

## Frameworks

[Gin](https://github.com/gin-gonic/gin): http server framework

[cli](https://github.com/urfave/cli): for building command line tools

## Structure

```markdown
$ tree -d -L 1
.
├── dockerfiles  
├── golang_services  
├── infrastructure  
├── nodejs_services
├── python_services
└── tools
```

## Some commands

- `pre-build`: to against none generated code
- `build-service-images`: build all service images
- `build-golang-service-images`: build golang service images
- `build-nodejs-service-images`: build nodejs service images
- `build-python-service-images`: build python service images

Note: You need setup aws credential to do the push image

- `push-service-images`: push all images to ECR
- `push-golang-service-images`: push golang images to ECR
- `push-nodejs-service-images`: push nodejs images to ECR
- `push-python-service-images`: push python images to ECR

## Useful tools

[gvm](https://github.com/moovweb/gvm): a go version management tool

## Auto deployment

Git message will control deployment for services.
Examples:

- `package` => will deploy package service
- `package|update order` => will deploy package service
- `package payment|update callback` => will deploy package and payment service
- `update something new but don't need release` => will not deploy any service

## Services

```
service:
  with_path:
    - atlas
    - internals
    - package
    - packer
    - payment
    - search
    - user-management
    - versions
    - master-data
    - shipment
    - mrz-parser
    - passport-photo
    - helper
    - id-reader
    - device-tokens
    - album
    - third-party-service
  without_path:
    - atlas-worker
    - carrier
    - packer-offline
    - packer-online
    - packer-online-captcha
    - submit-email-worker
    - notification
    - notification-user
    - watchdog
    - submit-dispatcher
  schedule:
    - checker
    - payment-worker
```

# Deploy #5

minikube addons enable ingress
kubectl delete regcred; kubectl create secret docker-registry regcred --docker-server=853431205376.dkr.ecr.us-west-2.amazonaws.com --docker-username=AWS --docker-password=$(aws ecr get-login-password --region us-west-2)

For big docker image file
minikube ssh
docker login 853431205376.dkr.ecr.us-west-2.amazonaws.com -u AWS
aws ecr get-login-password --region us-west-2

minikube addons configure registry-creds
minikube addons enable registry-creds

eval $(minikube docker-env)

helm install k8s-helm .k8s-helm/
helm upgrade k8s-helm .k8s-helm/
